from fastapi import APIRouter, Depends, HTTPException, status # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from pydantic import BaseModel

from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies, AllocationStatus


router = APIRouter(prefix="/projects", tags=["Projects"])


class AssignAllocationStrategyRequest(BaseModel):
    strategy_id: int


@router.post("/{project_id}/allocation-strategy", status_code=status.HTTP_200_OK)
async def assign_allocation_strategy(
    project_id: int,
    payload: AssignAllocationStrategyRequest,
    db: AsyncSession = Depends(get_master_db_session),
):
    # Validate project exists
    project = await db.get(ProjectsRegistry, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Validate strategy exists and is active
    strategy = await db.get(AllocationStrategies, payload.strategy_id)
    if not strategy:
        raise HTTPException(status_code=404, detail="Allocation strategy not found")
    if getattr(strategy, "allocation_status", AllocationStatus.ACTIVE) != AllocationStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="Allocation strategy is not active")

    # Update project configuration
    project.allocation_strategy_id = strategy.id
    await db.commit()
    await db.refresh(project)

    return {
        "success": True,
        "message": "Allocation strategy assigned to project",
        "data": {
            "project": {
                "id": project.id,
                "project_code": project.project_code,
                "project_name": project.project_name,
                "allocation_strategy_id": project.allocation_strategy_id,
            },
            "strategy": {
                "id": strategy.id,
                "strategy_name": strategy.strategy_name,
                "strategy_type": str(strategy.strategy_type),
                "allocation_status": str(getattr(strategy, "allocation_status", AllocationStatus.ACTIVE)),
            },
        },
    }


@router.post("/by-code/{project_code}/allocation-strategy", status_code=status.HTTP_200_OK)
async def assign_allocation_strategy_by_code(
    project_code: str,
    payload: AssignAllocationStrategyRequest,
    db: AsyncSession = Depends(get_master_db_session),
):
    """Assign allocation strategy to project using project_code instead of project_id"""
    
    # Optimize: Use a single query with join to validate both project and strategy
    from sqlalchemy.orm import selectinload
    
    # First, validate strategy exists and is active
    strategy_result = await db.execute(
        select(AllocationStrategies).where(
            AllocationStrategies.id == payload.strategy_id,
            AllocationStrategies.allocation_status == AllocationStatus.ACTIVE
        )
    )
    strategy = strategy_result.scalar_one_or_none()
    if not strategy:
        raise HTTPException(status_code=404, detail="Active allocation strategy not found")

    # Then validate and update project in one operation
    project_result = await db.execute(
        select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
    )
    project = project_result.scalar_one_or_none()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Update project configuration
    project.allocation_strategy_id = strategy.id
    await db.commit()
    # Skip refresh since we already have the data we need

    return {
        "success": True,
        "message": "Allocation strategy assigned to project",
        "data": {
            "project": {
                "id": project.id,
                "project_code": project.project_code,
                "project_name": project.project_name,
                "allocation_strategy_id": project.allocation_strategy_id,
            },
            "strategy": {
                "id": strategy.id,
                "strategy_name": strategy.strategy_name,
                "strategy_type": str(strategy.strategy_type),
                "allocation_status": str(getattr(strategy, "allocation_status", AllocationStatus.ACTIVE)),
            },
        },
    }

