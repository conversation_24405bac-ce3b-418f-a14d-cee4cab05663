// hooks/useClientData.tsx
import { useState, useEffect } from 'react';
import { Client, ClientFormData } from '../types';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { validateClientForm } from '../utils/helpers';
import { API_BASE_URL } from "@/lib/api";

export const useClientData = (onNext?: () => void) => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [existingClients, setExistingClients] = useState<Client[]>([]);
  const [clientForm, setClientForm] = useState<ClientFormData>({
    username: "",
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    
    // Project details
    projectName: "",
    projectType: "",
    projectDescription: "",
  });
  const [clientFormErrors, setClientFormErrors] = useState<Record<string, string>>({});
  const [registering, setRegistering] = useState(false);

  useEffect(() => {
    fetchExistingClients();
  }, []);

  const fetchExistingClients = async () => {
    try {
      const res = await authFetch(`${API_BASE_URL}/admin/clients`);
      const data = await res.json();
      if (data.success && data.data?.clients) {
        // Transform the client data to match the expected Client interface
        const clients = data.data.clients.map((clientData: any) => ({
          id: clientData.id,
          username: clientData.username,
          full_name: clientData.name,
          email: clientData.email,
          role: 'client', // Add role for compatibility
          is_active: true, // Assume active since they're in the clients table
          created_at: clientData.created_at || new Date().toISOString()
        }));
        setExistingClients(clients as Client[]);
      } else {
        setExistingClients([]);
      }
    } catch (error) {
      console.error("Failed to fetch clients:", error);
      setExistingClients([]);
    }
  };

  const handleClientRegistration = async () => {
    const errors = validateClientForm(clientForm);
    if (Object.keys(errors).length > 0) {
      setClientFormErrors(errors);
      return { success: false };
    }

    setRegistering(true);
    try {
      // Single API call to register client and project together
      const projectRes = await authFetch(`${API_BASE_URL}/admin/register-client`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          client_name: clientForm.fullName, // Use fullName as client name
          username: clientForm.username,
          email: clientForm.email,
          project_name: clientForm.projectName,
          project_type: clientForm.projectType,
          project_description: clientForm.projectDescription || ""
        }),
      });

      if (!projectRes.ok) {
        const errorData = await projectRes.json().catch(() => ({}));
        throw new Error(errorData.detail || errorData.message || "Failed to register client and project");
      }

      const projectData = await projectRes.json();
      console.log("Client and project registered:", projectData);

      // Store project data in localStorage for use in later steps
      if (projectData.success && projectData.data?.project) {
        localStorage.setItem('currentProject', JSON.stringify(projectData.data.project));
      }

      showToast.success("Client and project registered successfully!");

      // Set the selected client to the newly created client entry
      const newClient = projectData.data?.client;
      const projectCode = projectData.success && projectData.data?.project?.project_code;

      setSelectedClient({
        id: newClient.id,
        username: clientForm.username,
        full_name: newClient.name,
        email: clientForm.email,
        role: 'client',
        is_active: true,
        created_at: new Date().toISOString(),
        project_code: projectCode // Store the project code for the new client
      });
      fetchExistingClients();

      // Reset form
      setClientForm({
        username: "",
        fullName: "",
        email: "",
        password: "",
        confirmPassword: "",
        projectName: "",
        projectType: "",
        projectDescription: "",
      });
      setClientFormErrors({});

      return {
        success: true,
        client: {
          id: newClient.id,
          username: clientForm.username,
          full_name: newClient.name,
          email: clientForm.email,
          role: 'client',
          is_active: true,
          created_at: new Date().toISOString()
        }
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        showToast.error(`Registration failed: ${error.message}`);
      } else {
        showToast.error("Registration failed");
      }
      return { success: false };
    } finally {
      setRegistering(false);
    }
  };

  const handleSelectExistingClient = async (client: Client) => {
    try {
      console.log("Client selected from clients table:", client);

      // Fetch client's projects from the API
      const res = await authFetch(`${API_BASE_URL}/admin/clients/${client.id}`);
      const data = await res.json();

      if (data.success && data.data?.client) {
        const clientData = data.data.client;
        console.log("Client projects fetched:", clientData.projects);

        // Check if client has existing projects
        if (clientData.projects && clientData.projects.length > 0) {
          // Use the first active project or let user choose later
          const activeProject = clientData.projects.find((p: any) => p.project_status === 'active') || clientData.projects[0];

          // Store project data in localStorage
          const projectData = {
            id: activeProject.id,
            project_code: activeProject.project_code,
            project_name: activeProject.project_name,
            project_type: activeProject.project_type,
            folder_path: activeProject.folder_path,
            batch_size: activeProject.batch_size,
            total_files: activeProject.total_files,
            total_batches: activeProject.total_batches,
            completed_files: activeProject.completed_files,
            credentials: activeProject.credentials,
            connection_type: activeProject.connection_type
          };
          localStorage.setItem('currentProject', JSON.stringify(projectData));

          console.log("Existing project selected and stored:", projectData);
          showToast.success(`Client "${client.full_name}" selected with project "${activeProject.project_name}"!`);
        } else {
          // No projects found - user will need to create one in the next steps
          console.log("No existing projects found for client, will create new one");
          localStorage.removeItem('currentProject'); // Clear any existing project data
          showToast.info(`Client "${client.full_name}" selected. You'll need to create a project in the next steps.`);
        }
      } else {
        // If we can't fetch projects, still proceed but show warning
        console.warn("Could not fetch client projects, proceeding without project data");
        localStorage.removeItem('currentProject');
        showToast.warning(`Client "${client.full_name}" selected but could not load projects.`);
      }

      // Update the client with the project_code from localStorage
      const projectData = localStorage.getItem('currentProject');
      let projectCode = null;
      if (projectData) {
        try {
          const project = JSON.parse(projectData);
          projectCode = project.project_code;
        } catch (error) {
          console.error('Error parsing project data:', error);
        }
      }

      setSelectedClient({
        ...client,
        project_code: projectCode
      });

      // Navigate to next step
      if (onNext) {
        onNext();
      }
      return { success: true, client };
    } catch (error) {
      console.error("Error selecting existing client:", error);
      showToast.error("Failed to select client. Please try again.");
      return { success: false, client };
    }
  };

  return {
    selectedClient,
    setSelectedClient,
    existingClients,
    clientForm,
    setClientForm,
    clientFormErrors,
    setClientFormErrors,
    registering,
    handleClientRegistration,
    handleSelectExistingClient,
    fetchExistingClients,
  };
};