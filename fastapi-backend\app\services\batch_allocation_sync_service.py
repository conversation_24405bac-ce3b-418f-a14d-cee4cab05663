import logging
from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, update
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from repositories.project_db_repository import ProjectDBRepository
from core.session_manager import get_project_db_session
logger = logging.getLogger(__name__)

class BatchAllocationSyncService:
    """
    Service to refresh batch allocation data and sync project progress.
    Reads existing completion counts without updating them, but updates project progress in master DB.
    """
    
    def __init__(self):
        self.project_repo = ProjectDBRepository()
    
    async def sync_batch_allocations(self, project_id: int, master_session: AsyncSession) -> Dict[str, Any]:
        """
        Refresh batch allocation data for a project by reading from database.
        
        Args:
            project_id: The project ID from master database
            master_session: Master database session
            
        Returns:
            Dict containing refresh results
        """
        try:
            # Get project details from master database
            project_result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.id == project_id)
            )
            project = project_result.scalar_one_or_none()
            
            if not project:
                return {
                    "success": False,
                    "error": f"Project with ID {project_id} not found",
                    "batches_synced": 0
                }
            
            # Get project database session to count batches
            
            async with get_project_db_session(project.project_code) as session:
                # Just count the batches - no actual syncing needed since data is already correct
                batch_result = await session.execute(
                    text("SELECT COUNT(*) FROM allocation_batches")
                )
                batch_count = batch_result.scalar() or 0
                
            return {
                "success": True,
                "message": f"Successfully refreshed batch allocation data for project {project.project_code}",
                "batches_synced": batch_count
            }
                
        except Exception as e:
            logger.error(f"Error refreshing batch allocations for project {project_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "batches_synced": 0
            }
    
    async def sync_single_batch_allocation(
        self, 
        project_id: int, 
        batch_id: int, 
        master_session: AsyncSession
    ) -> Dict[str, Any]:
        """
        Refresh a single batch allocation by reading from database.
        
        Args:
            project_id: The project ID from master database
            batch_id: The batch ID to refresh
            master_session: Master database session
            
        Returns:
            Dict containing refresh results for the batch
        """
        try:
            # Get project details from master database
            project_result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.id == project_id)
            )
            project = project_result.scalar_one_or_none()
            
            if not project:
                return {
                    "success": False,
                    "error": f"Project with ID {project_id} not found"
                }
            
            # Get project database session
            
            async with get_project_db_session(project.project_code) as session:
                # Just verify the batch exists
                batch_result = await session.execute(
                    text("SELECT batch_identifier FROM allocation_batches WHERE id = :batch_id"),
                    {"batch_id": batch_id}
                )
                batch = batch_result.fetchone()
                
                if not batch:
                    return {
                        "success": False,
                        "error": f"Batch with ID {batch_id} not found"
                    }
                
                return {
                    "success": True,
                    "message": f"Successfully refreshed batch {batch.batch_identifier}",
                    "batch_id": batch_id,
                    "batch_identifier": batch.batch_identifier
                }
                
        except Exception as e:
            logger.error(f"Error refreshing batch allocation for batch {batch_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def sync_project_progress(self, project_id: int, master_session: AsyncSession) -> Dict[str, Any]:
        """
        Update project.completed_files in master database based on actual completion data.
        
        Args:
            project_id: The project ID from master database
            master_session: Master database session
            
        Returns:
            Dict containing sync results
        """
        try:
            # Get project details from master database
            project_result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.id == project_id)
            )
            project = project_result.scalar_one_or_none()
            
            if not project:
                return {
                    "success": False,
                    "error": f"Project with ID {project_id} not found"
                }
            
            # Get strategy details to understand completion requirements
            strategy_details = None
            if project.allocation_strategy_id:
                strategy_result = await master_session.execute(
                    select(AllocationStrategies).where(
                        AllocationStrategies.id == project.allocation_strategy_id
                    )
                )
                strategy_details = strategy_result.scalar_one_or_none()
            
            # Get project database session
            
            async with get_project_db_session(project.project_code) as session:
                # Calculate total completed files based on project requirements
                if strategy_details and strategy_details.requires_verification:
                    # For projects requiring verification, count files from fully verified batches
                    # A batch is fully verified when ALL files in the batch have verifier_review
                    total_completed_result = await session.execute(
                        text("""
                            SELECT COALESCE(SUM(ab.total_files), 0)
                            FROM allocation_batches ab
                            WHERE ab.total_files > 0 
                            AND (
                                SELECT COUNT(*) 
                                FROM file_allocations fa 
                                WHERE fa.batch_id = ab.id 
                                AND fa.verifier_review IS NOT NULL
                            ) = ab.total_files
                        """)
                    )
                else:
                    # For projects not requiring verification, use completion_count from batches
                    total_completed_result = await session.execute(
                        text("SELECT COALESCE(SUM(completion_count), 0) FROM allocation_batches")
                    )
                total_completed_files = total_completed_result.scalar() or 0
                
                # Get total files from all batches
                total_files_result = await session.execute(
                    text("SELECT COALESCE(SUM(total_files), 0) FROM allocation_batches")
                )
                total_files = total_files_result.scalar() or 0
                
                # Update the project in master database
                await master_session.execute(
                    update(ProjectsRegistry)
                    .where(ProjectsRegistry.id == project_id)
                    .values(
                        completed_files=total_completed_files,
                        total_files=total_files  # Also update total_files in case it changed
                    )
                )
                await master_session.commit()
                
                logger.info(
                    f"Updated project {project.project_code}: "
                    f"completed_files: {project.completed_files} -> {total_completed_files}, "
                    f"total_files: {project.total_files} -> {total_files} "
                    f"(verification required: {strategy_details.requires_verification if strategy_details else False})"
                )
                
                return {
                    "success": True,
                    "project_id": project_id,
                    "project_code": project.project_code,
                    "previous_completed_files": project.completed_files,
                    "new_completed_files": total_completed_files,
                    "previous_total_files": project.total_files,
                    "new_total_files": total_files,
                    "progress_percentage": round((total_completed_files / total_files) * 100) if total_files > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Error syncing project progress for project {project_id}: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def sync_all_projects_progress(self, master_session: AsyncSession) -> Dict[str, Any]:
        """
        Update progress for all active projects.
        
        Args:
            master_session: Master database session
            
        Returns:
            Dict containing sync results for all projects
        """
        try:
            # Get all active projects
            projects_result = await master_session.execute(
                select(ProjectsRegistry).where(
                    ProjectsRegistry.project_status.in_(['active', 'annotating', 'verifying'])
                )
            )
            projects = projects_result.scalars().all()
            
            sync_results = []
            successful_syncs = 0
            
            for project in projects:
                result = await self.sync_project_progress(project.id, master_session)
                sync_results.append(result)
                if result.get("success"):
                    successful_syncs += 1
            
            return {
                "success": True,
                "message": f"Successfully synced {successful_syncs} out of {len(projects)} projects",
                "total_projects": len(projects),
                "successful_syncs": successful_syncs,
                "sync_results": sync_results
            }
            
        except Exception as e:
            logger.error(f"Error syncing all projects progress: {e}")
            return {
                "success": False,
                "error": str(e)
            }