// main.tsx - Entry point for the refactored ClientOnboarding
export { default as ClientOnboarding } from './ClientOnboarding';

// Re-export types
export * from './types';

// Re-export components
export { StepIndicator } from './components/StepIndicator';
export { DatasetInfoCard } from './components/DatasetInfoCard';
export { NasBrowserModal } from './components/NasBrowserModal';
export { ImageBrowser } from './components/ImageBrowser';

// Re-export steps
export { ClientRegistration } from './steps/ClientRegistration';
export { DataConnectorSetup } from './steps/DataConnectorSetup';
export { DatasetSelection } from './steps/DatasetSelection';
export { EditInstructions } from './steps/EditInstructions';
export { AllocationStrategy } from './steps/AllocationStrategy';

// Re-export hooks
export { useClientData } from './hooks/useClientData';
export { useConnectorStatus } from './hooks/useConnectorStatus';
export { useDatasetData } from './hooks/useDatasetData';
export { useNasBrowser } from './hooks/useNasBrowser';
export { useInstructions } from './hooks/useInstructions';
export { useDatasetCreation } from './hooks/useDatasetCreation';
export { useOnboardingSteps } from './hooks/useOnboardingSteps';

// Re-export utilities
export { validateClientForm, getCompletionStatus, generateBreadcrumbs } from './utils/helpers';
export { API_BASE_URL } from '@/lib/api';