"""
Test Data Factories for DADP Integration Tests
Provides dynamic, non-hardcoded test data generation.
"""

import time
import tempfile
import uuid
from typing import Dict, Any, Optional, List
from faker import Faker

# Import using the proper test import paths
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "app")))

from core.config import get_settings
from post_db.master_models.users import users, UserRole
from post_db.master_models.clients import Clients
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from post_db.allocation_models.project_users import ProjectUsers
from post_db.allocation_models.files_registry import FilesRegistry, FileType
from schemas.UserSchemas import UserRegisterRequest


class TestConfig:
    """Centralized test configuration management."""
    
    def __init__(self):
        self.settings = get_settings()
        self.api_prefix = self.settings.api_prefix
        self.temp_dir = tempfile.mkdtemp(prefix="dadp_test_")
    
    def get_endpoint(self, path: str) -> str:
        """Get full API endpoint URL."""
        # Ensure path starts with /
        if not path.startswith('/'):
            path = f"/{path}"
        return f"{self.api_prefix}{path}"
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get test database configuration."""
        return {
            "database_host": "localhost",  # Keep localhost for test isolation
            "database_port": 5432,
            "database_name": f"test_db_{int(time.time())}"
        }


class UserTestFactory:
    """Factory for creating test user data."""
    
    def __init__(self):
        self.fake = Faker()
        self.unique_counter = int(time.time() * 1000)
        # Use consistent test password for all test users
        self.test_password = "TestPassword123!"
    
    def create_user_data(self, role: str = "annotator", **overrides) -> Dict[str, Any]:
        """Create user registration data."""
        self.unique_counter += 1
        
        base_data = {
            "username": f"test_user_{self.unique_counter}",
            "email": f"test_{self.unique_counter}@testdomain.com",
            "password": self.test_password,
            "confirm_password": self.test_password,
            "full_name": self.fake.name(),
            "role": role
        }
        
        # Apply any overrides
        base_data.update(overrides)
        return base_data
    
    def create_user_register_request(self, role: str = "annotator", **overrides) -> UserRegisterRequest:
        """Create UserRegisterRequest object."""
        data = self.create_user_data(role, **overrides)
        return UserRegisterRequest(**data)
    
    def create_project_user(self, role: str = "annotator_1", **overrides) -> ProjectUsers:
        """Create ProjectUsers model instance."""
        self.unique_counter += 1
        
        base_data = {
            "user_id": self.unique_counter,
            "username": f"project_user_{self.unique_counter}",
            "role": role,
            "current_batch": None
        }
        
        base_data.update(overrides)
        return ProjectUsers(**base_data)
    
    def create_multiple_users(self, count: int, role: str = "annotator") -> List[Dict[str, Any]]:
        """Create multiple user data objects."""
        return [self.create_user_data(role) for _ in range(count)]


class ProjectTestFactory:
    """Factory for creating test project data."""
    
    def __init__(self):
        self.fake = Faker()
        self.unique_counter = int(time.time() * 1000)
        self.test_config = TestConfig()
    
    def create_client(self, **overrides) -> Clients:
        """Create test client."""
        self.unique_counter += 1
        
        base_data = {
            "name": f"Test Client {self.unique_counter}",
            "username": f"test_client_{self.unique_counter}",
            "email": f"client_{self.unique_counter}@testdomain.com"
        }
        
        base_data.update(overrides)
        return Clients(**base_data)
    
    def create_allocation_strategy(self, **overrides) -> AllocationStrategies:
        """Create test allocation strategy."""
        self.unique_counter += 1
        
        base_data = {
            "strategy_name": f"Test Strategy {self.unique_counter}",
            "strategy_type": StrategyType.SEQUENTIAL,
            "num_annotators": 1,
            "requires_verification": False,
            "requires_ai_preprocessing": False,
            "requires_audit": False,
            "allocation_status": "active",
            "quality_requirements": None,
            "configuration": None
        }
        
        base_data.update(overrides)
        return AllocationStrategies(**base_data)
    
    def create_project(self, client_id: int, strategy_id: int, **overrides) -> ProjectsRegistry:
        """Create test project."""
        self.unique_counter += 1
        db_config = self.test_config.get_database_config()
        
        base_data = {
            "project_code": f"TEST_PROJECT_{self.unique_counter}",
            "project_name": f"Test Project {self.unique_counter}",
            "project_type": "image",
            "client_id": client_id,
            "database_name": db_config["database_name"],
            "database_host": db_config["database_host"],
            "database_port": db_config["database_port"],
            "allocation_strategy_id": strategy_id,
            "project_status": "active",
            "priority_level": 1
        }
        
        base_data.update(overrides)
        return ProjectsRegistry(**base_data)


class BatchTestFactory:
    """Factory for creating test batch data."""
    
    def __init__(self):
        self.unique_counter = int(time.time() * 1000)
    
    def create_allocation_batch(self, **overrides) -> AllocationBatches:
        """Create test allocation batch."""
        self.unique_counter += 1
        
        base_data = {
            "batch_identifier": f"TEST_BATCH_{self.unique_counter}",
            "total_files": 10,
            "annotation_count": 1,
            "assignment_count": 0,
            "completion_count": 0,
            "batch_status": BatchStatus.CREATED,
            "file_list": [f"test_file_{i}.jpg" for i in range(1, 11)]
        }
        
        base_data.update(overrides)
        return AllocationBatches(**base_data)
    
    def create_batch_with_files(self, file_count: int = 5, **overrides) -> AllocationBatches:
        """Create batch with specified number of files."""
        batch_data = {
            "total_files": file_count,
            "file_list": [f"test_file_{i}.jpg" for i in range(1, file_count + 1)]
        }
        batch_data.update(overrides)
        return self.create_allocation_batch(**batch_data)


class FileTestFactory:
    """Factory for creating test file data."""
    
    def __init__(self):
        self.fake = Faker()
        self.unique_counter = int(time.time() * 1000)
        self.test_config = TestConfig()
    
    def create_test_folder_path(self, project_code: str) -> str:
        """Create test folder path."""
        return f"{self.test_config.temp_dir}/{project_code}/test_data"
    
    def create_storage_location(self, filename: str, storage_type: str = "filesystem") -> Dict[str, Any]:
        """Create storage location configuration."""
        return {
            "type": storage_type,
            "path": f"{self.test_config.temp_dir}/{filename}"
        }
    
    def create_file_identifier(self, folder: str, filename: str) -> str:
        """Create file identifier."""
        return f"{folder}/{filename}"
    
    def create_files_registry(self, batch_id: int, **overrides) -> FilesRegistry:
        """Create test file registry entry."""
        self.unique_counter += 1
        
        base_data = {
            "batch_id": batch_id,
            "file_identifier": f"test_file_{self.unique_counter}.jpg",
            "original_filename": f"test_image_{self.unique_counter}.jpg",
            "file_type": FileType.IMAGE,
            "file_size_bytes": 1024000,  # 1MB
            "storage_location": self.create_storage_location(f"test_file_{self.unique_counter}.jpg")
        }
        
        base_data.update(overrides)
        return FilesRegistry(**base_data)
    
    def create_multiple_files(self, batch_id: int, count: int) -> List[FilesRegistry]:
        """Create multiple file registry entries."""
        return [self.create_files_registry(batch_id) for _ in range(count)]


class APITestFactory:
    """Factory for creating API test data."""
    
    def __init__(self):
        self.test_config = TestConfig()
        self.fake = Faker()
        self.unique_counter = int(time.time() * 1000)
    
    def create_client_registration_data(self, **overrides) -> Dict[str, Any]:
        """Create client registration API data."""
        self.unique_counter += 1
        
        base_data = {
            "client_name": f"API Test Client {self.unique_counter}",
            "username": f"api_client_{self.unique_counter}",
            "email": f"api_client_{self.unique_counter}@testdomain.com",
            "project_name": f"API Test Project {self.unique_counter}",
            "project_type": "image",
            "project_description": "Test project created via API"
        }
        
        base_data.update(overrides)
        return base_data
    
    def create_batch_creation_data(self, **overrides) -> Dict[str, Any]:
        """Create batch creation API data."""
        folder_path = self.test_config.temp_dir + "/api_test_folder"
        
        base_data = {
            "folder_path": folder_path,
            "files_per_batch": 10,
            "content_type": "image"
        }
        
        base_data.update(overrides)
        return base_data


class TestDataFactory:
    """Main factory that combines all other factories."""
    
    def __init__(self):
        self.config = TestConfig()
        self.users = UserTestFactory()
        self.projects = ProjectTestFactory()
        self.batches = BatchTestFactory()
        self.files = FileTestFactory()
        self.api = APITestFactory()
    
    async def create_complete_test_environment(self, test_db, master_db):
        """Create a complete test environment with all necessary data."""
        # Create client
        client = self.projects.create_client()
        master_db.add(client)
        await master_db.commit()
        await master_db.refresh(client)
        
        # Create allocation strategy
        strategy = self.projects.create_allocation_strategy()
        master_db.add(strategy)
        await master_db.commit()
        await master_db.refresh(strategy)
        
        # Create project
        project = self.projects.create_project(client.id, strategy.id)
        master_db.add(project)
        await master_db.commit()
        await master_db.refresh(project)
        
        # Create batch
        batch = self.batches.create_allocation_batch()
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Create files
        files = self.files.create_multiple_files(batch.id, 5)
        for file_entry in files:
            test_db.add(file_entry)
        await test_db.commit()
        
        return {
            "client": client,
            "strategy": strategy,
            "project": project,
            "batch": batch,
            "files": files
        }


# Global factory instance for easy access
test_factory = TestDataFactory()
