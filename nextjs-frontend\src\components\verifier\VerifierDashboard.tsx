"use client";

import { useState, useEffect } from "react";
import { FaCheckCircle, FaProjectDiagram, FaSpinner, FaArrowRight, FaArrowLeft, FaCheck, FaTimes, FaSave } from "react-icons/fa";
import { API_BASE_URL } from "@/lib/api";

interface VerifierDashboardProps {
  username?: string;
  fullName?: string;
  projectCode?: string | null;
  projectName?: string | null;
}

export default function VerifierDashboard({
  username = "User",
  fullName = "",
  projectCode = null,
  projectName = null,
}: VerifierDashboardProps) {
  const [typedText, setTypedText] = useState("");
  const [phraseIdx, setPhraseIdx] = useState(0);
  const [activeTab, setActiveTab] = useState<"keyboard">("keyboard");
  
  // Verification states
  const [isStarting, setIsStarting] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [batchStatus, setBatchStatus] = useState<{
    hasActiveBatch?: boolean;
    canStartVerifying?: boolean;
    currentBatch?: any;
  } | null>(null);

  const displayName = fullName || username;
  const firstInitial = displayName.charAt(0).toUpperCase();

  // Typing animation effect
  useEffect(() => {
    const phrases = [
      "Ready to verify annotations?",
      "Quality control in progress...",
      "Review and validate data...",
    ];

    const currentPhrase = phrases[phraseIdx];
    let charIndex = 0;

    const typeInterval = setInterval(() => {
      if (charIndex <= currentPhrase.length) {
        setTypedText(currentPhrase.slice(0, charIndex));
        charIndex++;
      } else {
        clearInterval(typeInterval);
        setTimeout(() => {
          setPhraseIdx((prev) => (prev + 1) % phrases.length);
        }, 2000);
      }
    }, 100);

    return () => clearInterval(typeInterval);
  }, [phraseIdx]);

  // API base URL
  const API_BASE = API_BASE_URL;

  // Fetch batch status (same pattern as annotator dashboard)
  const fetchBatchStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/verifier/current-batch`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Verifier batch status:', result);
        setBatchStatus({
          hasActiveBatch: result.success && result.batch,
          canStartVerifying: true, // Always allow clicking the button
          currentBatch: result.batch
        });
      } else {
        console.log('No active batch found');
        setBatchStatus({
          hasActiveBatch: false,
          canStartVerifying: true,
          currentBatch: null
        });
      }
    } catch (error) {
      console.error('Error fetching batch status:', error);
      setBatchStatus({
        hasActiveBatch: false,
        canStartVerifying: true,
        currentBatch: null
      });
    }
  };

  // Check for existing batch on component mount and when returning to dashboard
  useEffect(() => {
    if (projectCode) {
      fetchBatchStatus();
    }
  }, [projectCode]);

  // Refresh batch status when component becomes visible (user returns from verification)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && projectCode) {
        console.log('Dashboard became visible, refreshing batch status...');
        fetchBatchStatus();
      }
    };

    const handleFocus = () => {
      if (projectCode) {
        console.log('Window focused, refreshing batch status...');
        fetchBatchStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [projectCode]);

  // Handle start verification
  const handleStartVerification = async () => {
    if (!projectCode) {
      setStatusMessage("No active project assigned");
      return;
    }

    setIsStarting(true);
    setStatusMessage(null);
    
    try {
      const response = await fetch(`${API_BASE}/verifier/start-verification`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.success) {
          if (result.continue_verification) {
            setVerificationStatus("continue");
            setStatusMessage(`Continue verifying batch: ${result.batch?.batch_identifier}`);
            // Navigate to verification interface
            setTimeout(() => {
              window.location.href = '/verifier/verify';
            }, 2000);
          } else if (result.already_assigned) {
            setVerificationStatus("assigned");
            setStatusMessage(`You are already assigned to batch: ${result.batch?.batch_identifier}`);
            // Navigate to verification interface
            setTimeout(() => {
              window.location.href = '/verifier/verify';
            }, 2000);
          } else {
            // Newly assigned batch
            setVerificationStatus("assigned");
            setStatusMessage(`Successfully assigned to batch: ${result.batch?.batch_identifier} for verification`);
            // Navigate to verification interface
            setTimeout(() => {
              window.location.href = '/verifier/verify';
            }, 2000);
          }
        }
      } else {
        const error = await response.json();
        
        // Handle different error cases
        if (response.status === 404 && error.detail?.includes('No batches have been completed yet')) {
          setVerificationStatus("no_completed");
          setStatusMessage("No batches have been completed yet. Please wait for annotations to be finished.");
        } else if (response.status === 409) {
          setVerificationStatus("all_assigned");
          setStatusMessage("All batches have been assigned. Wait for admin to assign another project.");
        } else if (response.status === 404 && error.detail?.includes('No available batches')) {
          setVerificationStatus("no_available");
          setStatusMessage("No batches are available for verification at this time.");
        } else if (response.status === 400 && error.detail?.includes('No active project')) {
          setVerificationStatus("no_project");
          setStatusMessage("You are not assigned to any active project. Please contact your administrator.");
        } else {
          setVerificationStatus("error");
          setStatusMessage(`Failed to start verification: ${error.detail || 'Unknown error'}`);
        }
      }
    } catch (error) {
      setVerificationStatus("error");
      setStatusMessage('Network error occurred. Please check your connection and try again.');
    } finally {
      setIsStarting(false);
    }
  };

  return (
    <div className="px-4 py-4 max-w-7xl mx-auto min-h-screen">
      {/* Welcome Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl py-4 px-6 mb-6 shadow-lg border border-blue-200">
        <div className="flex items-center">
          <div className="flex-shrink-0 mr-6">
            <div className="w-[80px] h-[80px] bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl font-bold text-white">
                {firstInitial}
              </span>
            </div>
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <h2 className="text-2xl font-bold text-gray-800">
                  Welcome back, {displayName}{" "}
                  <span className="text-2xl">👋</span>
                </h2>
                <div className="mt-2 min-h-[48px] min-w-[240px] overflow-hidden">
                  <span className="text-lg text-blue-600 font-medium whitespace-nowrap">
                    {typedText}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <span className="bg-gradient-to-br from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                  Verification Mode
                </span>
                {projectCode && (
                  <span className="bg-white text-blue-600 px-3 py-1 rounded-full text-xs font-medium border border-blue-200">
                    {projectCode}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-nowrap items-stretch gap-4">
        {/* Project Information Panel - Only Keyboard Shortcuts */}
        <div className="w-full md:w-5/12">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                <FaProjectDiagram className="mr-2 text-blue-500" />
                Project Information
              </h3>
            </div>
            <div className="p-0">
              <ul className="flex border-b border-gray-200">
                <li role="presentation" className="flex-1">
                  <button
                    type="button"
                    onClick={() => setActiveTab("keyboard")}
                    className={`w-full px-6 py-3 font-medium transition-all duration-200 ${
                      activeTab === "keyboard"
                        ? "text-blue-600 border-b-3 border-blue-600 bg-blue-50"
                        : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    Keyboard Shortcuts
                  </button>
                </li>
              </ul>
              <div className="min-h-[280px]">
                {activeTab === "keyboard" && (
                  <div className="p-6">
                    <div className="space-y-4">
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-800 mb-3 flex items-center">
                          <FaProjectDiagram className="mr-2" />
                          Verification Shortcuts
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-blue-200">
                            <div className="flex items-center text-gray-700">
                              <FaCheck className="text-green-600 mr-2" />
                              <span className="font-medium">Accept annotation</span>
                            </div>
                            <kbd className="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm font-mono text-gray-800 shadow-sm">A</kbd>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-blue-200">
                            <div className="flex items-center text-gray-700">
                              <FaTimes className="text-red-600 mr-2" />
                              <span className="font-medium">Reject annotation</span>
                            </div>
                            <kbd className="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm font-mono text-gray-800 shadow-sm">R</kbd>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-blue-200">
                            <div className="flex items-center text-gray-700">
                              <FaArrowRight className="text-blue-600 mr-2" />
                              <span className="font-medium">Next item</span>
                            </div>
                            <kbd className="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm font-mono text-gray-800 shadow-sm">→</kbd>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-blue-200">
                            <div className="flex items-center text-gray-700">
                              <FaArrowLeft className="text-blue-600 mr-2" />
                              <span className="font-medium">Previous item</span>
                            </div>
                            <kbd className="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm font-mono text-gray-800 shadow-sm">←</kbd>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-blue-200">
                            <div className="flex items-center text-gray-700">
                              <FaSave className="text-purple-600 mr-2" />
                              <span className="font-medium">Save progress</span>
                            </div>
                            <kbd className="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm font-mono text-gray-800 shadow-sm">Ctrl+S</kbd>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Action Panel */}
        <div className="w-full md:w-7/12">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b">
              <h3 className="text-lg font-semibold text-gray-800">Ready to Start Verifying?</h3>
              <p className="text-gray-600 text-sm mt-1">Begin your verification work with the tools below</p>
            </div>
            <div className="p-4">
              {/* Main Action Card */}
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white shadow-lg mb-4">
                <div className="flex flex-col items-center text-center">
                  <h4 className="text-2xl font-bold mb-2">
                    {batchStatus?.hasActiveBatch ? "Continue Verification" : "Start Verification"}
                  </h4>
                  <p className="text-blue-100 mb-4 max-w-md">
                    {batchStatus?.hasActiveBatch 
                      ? "Continue working on your current batch of assigned verification tasks."
                      : "Review and validate annotations for quality assurance and accuracy."
                    }
                  </p>
                  <button
                    onClick={batchStatus?.hasActiveBatch ? () => window.location.href = '/verifier/verify' : handleStartVerification}
                    disabled={!projectCode || isStarting || !batchStatus?.canStartVerifying}
                    className={`font-bold py-3 px-8 rounded-lg flex items-center space-x-2 transition-all duration-200 ${
                      !projectCode || isStarting || !batchStatus?.canStartVerifying
                        ? "bg-white bg-opacity-50 text-blue-300 cursor-not-allowed opacity-70"
                        : batchStatus?.hasActiveBatch
                        ? "bg-white text-green-600 hover:bg-green-50 active:bg-green-100 cursor-pointer shadow-lg hover:shadow-xl"
                        : "bg-white text-blue-600 hover:bg-blue-50 active:bg-blue-100 cursor-pointer shadow-lg hover:shadow-xl"
                    }`}
                  >
                    {isStarting ? (
                      <FaSpinner className="animate-spin" />
                    ) : batchStatus?.hasActiveBatch ? (
                      <FaArrowRight />
                    ) : (
                      <FaCheckCircle />
                    )}
                    <span>
                      {isStarting ? "Starting..." : 
                       batchStatus?.hasActiveBatch ? "Continue Verifying →" : 
                       "Start Verifying →"}
                    </span>
                  </button>
                </div>
              </div>

              {/* Status Message */}
              {statusMessage && (
                <div className={`mt-4 p-4 rounded-lg border ${
                  verificationStatus === "error" || verificationStatus === "no_project" 
                    ? "bg-red-50 border-red-200 text-red-800"
                    : verificationStatus === "all_assigned" || verificationStatus === "no_available" || verificationStatus === "no_completed"
                    ? "bg-yellow-50 border-yellow-200 text-yellow-800"
                    : "bg-blue-50 border-blue-200 text-blue-800"
                }`}>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">{statusMessage}</span>
                  </div>
                </div>
              )}
              
              {/* Project Status */}
              {projectCode ? (
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center text-green-800">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Project Active: {projectCode}</span>
                  </div>
                  {projectName && (
                    <p className="text-green-700 text-sm mt-1">{projectName}</p>
                  )}
                </div>
              ) : (
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center text-yellow-800">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">No active project assigned</span>
                  </div>
                  <p className="text-yellow-700 text-sm mt-1">Please contact your administrator to get assigned to a project.</p>
                </div>
              )}

              {/* Batch Status (same pattern as annotator dashboard) */}
              {batchStatus?.hasActiveBatch && batchStatus.currentBatch && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center text-blue-800 mb-2">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Current Batch: {batchStatus.currentBatch.batch_identifier}</span>
                  </div>
                  <p className="text-blue-700 text-sm">
                    Continue verifying your assigned batch with {batchStatus.currentBatch.total_files} files.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
