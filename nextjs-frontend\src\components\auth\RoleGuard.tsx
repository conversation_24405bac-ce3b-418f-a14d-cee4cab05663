// "use client";

// import { useEffect, useState, useRef } from "react";
// import { useRouter } from "next/navigation";
// import { useAuth } from "@/contexts/AuthContext";

// interface RoleGuardProps {
//   allowedRoles: string[];
//   children: React.ReactNode;
//   fallbackRoute?: string;
// }

// export default function RoleGuard({
//   allowedRoles,
//   children,
//   fallbackRoute = "/",
// }: RoleGuardProps) {
//   const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
//   const router = useRouter();
//   const [authChecked, setAuthChecked] = useState(false);
//   const timeoutRef = useRef<NodeJS.Timeout | null>(null);

//   // Only check auth once on mount
//   useEffect(() => {
//     let cancelled = false;
//     if (!authChecked && !isLoading) {
//       checkAuth().finally(() => {
//         if (!cancelled) setAuthChecked(true);
//       });
//     }
//     return () => {
//       cancelled = true;
//     };
//   }, [authChecked, isLoading, checkAuth]);

//   useEffect(() => {
//     if (!authChecked || isLoading) return;

//     // Not authenticated: redirect to home immediately
//     if (!isAuthenticated) {
//       router.replace("/");
//       return;
//     }

//     // Authenticated but role is not allowed
//     if (user && !allowedRoles.includes(user.role)) {
//       // Find dashboard for user or fallback
//       const roleRoutes: Record<string, string> = {
//         admin: "/admin",
//         annotator: "/annotator",
//         auditor: "/auditor",
//         client: "/client",
//       };
//       const userRoute = roleRoutes[user.role] || fallbackRoute;

//       // Delay redirect for a short message, but cleanup if component unmounts
//       timeoutRef.current = setTimeout(() => {
//         router.replace(userRoute);
//       }, 1500);
//     }

//     return () => {
//       if (timeoutRef.current) clearTimeout(timeoutRef.current);
//     };
//   }, [
//     authChecked,
//     isLoading,
//     isAuthenticated,
//     user,
//     allowedRoles,
//     router,
//     fallbackRoute,
//   ]);

//   // Loading
//   if (!authChecked || isLoading) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
//       </div>
//     );
//   }

//   // Not authenticated
//   if (!isAuthenticated) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-600">Redirecting to login...</p>
//         </div>
//       </div>
//     );
//   }

//   // Authenticated, but role not allowed
//   if (user && !allowedRoles.includes(user.role)) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-600">
//             Access denied. Redirecting to your dashboard...
//           </p>
//         </div>
//       </div>
//     );
//   }

//   // Authenticated and authorized
//   return <>{children}</>;
// }

// "use client";

// import { useEffect, useState } from 'react';
// import { useRouter } from 'next/navigation';
// import { useAuth } from '@/contexts/AuthContext';

// interface RoleGuardProps {
//   allowedRoles: string[];
//   children: React.ReactNode;
//   fallbackRoute?: string;
// }

// export default function RoleGuard({ allowedRoles, children, fallbackRoute = '/' }: RoleGuardProps) {
//   const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
//   const router = useRouter();
//   const [redirecting, setRedirecting] = useState(false);
//   const [authChecked, setAuthChecked] = useState(false);

//   // Trigger auth check when RoleGuard mounts
//   useEffect(() => {
//     if (!authChecked && !isLoading) {
//       checkAuth().finally(() => setAuthChecked(true));
//     }
//   }, [checkAuth, authChecked, isLoading]);

//   useEffect(() => {
//     if (authChecked && !isLoading) {
//       if (!isAuthenticated) {
//         // Not authenticated, redirect to home
//         setRedirecting(true);
//         router.push('/');
//         return;
//       }

//       if (user && !allowedRoles.includes(user.role)) {
//         // Authenticated but wrong role, redirect to appropriate dashboard
//         setRedirecting(true);
//         const roleRoutes: Record<string, string> = {
//           'admin': '/admin',
//           'annotator': '/annotator',
//           'auditor': '/auditor',
//           'client': '/client'
//         };

//         const userRoute = roleRoutes[user.role] || fallbackRoute;

//         // Show a brief message before redirecting
//         setTimeout(() => {
//           router.push(userRoute);
//         }, 1500);
//         return;
//       }
//     }
//   }, [authChecked, isLoading, isAuthenticated, user, allowedRoles, router, fallbackRoute]);

//   // Show loading spinner while checking authentication and authorization
//   if (!authChecked || isLoading) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
//       </div>
//     );
//   }

//   // Don't render children if not authenticated
//   if (!isAuthenticated) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-600">Redirecting to login...</p>
//         </div>
//       </div>
//     );
//   }

//   // Show unauthorized message if wrong role
//   if (user && !allowedRoles.includes(user.role)) {
//     return (
//       <div className="flex items-center justify-center min-h-screen">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-600">Access denied. Redirecting to your dashboard...</p>
//         </div>
//       </div>
//     );
//   }

//   // Render children if authenticated and authorized
//   return <>{children}</>;
// }

"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

interface RoleGuardProps {
  allowedRoles: string[];
  children: React.ReactNode;
  fallbackRoute?: string;
}

export default function RoleGuard({
  allowedRoles,
  children,
  fallbackRoute = "/",
}: RoleGuardProps) {
  const { isAuthenticated, user, isLoading, checkAuth } = useAuth();
  const router = useRouter();
  const [authChecked, setAuthChecked] = useState(false);

  // Trigger auth check when RoleGuard mounts
  useEffect(() => {
    if (!authChecked && !isLoading) {
      checkAuth().finally(() => setAuthChecked(true));
    }
  }, [checkAuth, authChecked, isLoading]);

  useEffect(() => {
    if (!(authChecked && !isLoading)) return;

    if (!isAuthenticated) {
      // Not authenticated, redirect to home
      router.push("/");
      return;
    }

    if (user && !allowedRoles.includes(user.role)) {
      // Authenticated but wrong role, redirect to appropriate dashboard
      const roleRoutes: Record<string, string> = {
        admin: "/admin",
        annotator: "/annotator",
        auditor: "/auditor",
        client: "/client",
      };
      const userRoute = roleRoutes[user.role] || fallbackRoute;
      // Show a brief message before redirecting
      setTimeout(() => {
        router.push(userRoute);
      }, 1500);
    }
  }, [
    authChecked,
    isLoading,
    isAuthenticated,
    user,
    allowedRoles,
    router,
    fallbackRoute,
  ]);

  // Show loading spinner while checking authentication and authorization
  if (!authChecked || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Authenticated but unauthorized role
  if (user && !allowedRoles.includes(user.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">
            Access denied. Redirecting to your dashboard...
          </p>
        </div>
      </div>
    );
  }

  // Authenticated and authorized
  return <>{children}</>;
}
