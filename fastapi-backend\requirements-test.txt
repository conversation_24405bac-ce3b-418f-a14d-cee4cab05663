# Testing Dependencies
pytest==7.4.4
pytest-asyncio==0.23.2
pytest-cov==4.1.0
httpx==0.27.0               # Compatible with pytest-httpx
pytest-mock==3.12.0
factory-boy==3.3.0
faker==22.0.0

# Database Testing Dependencies
asyncpg==0.30.0  # PostgreSQL async driver
psycopg2-binary==2.9.10  # PostgreSQL sync driver for setup scripts
aiosqlite==0.19.0  # SQLite async driver for fallback testing
# pytest-postgresql removed - causes psycopg version conflicts, using custom DB setup instead

# Additional Testing Tools
pytest-xdist==3.5.0  # For parallel test execution
pytest-html==4.1.1   # For HTML test reports
coverage==7.4.0

# Development Tools
sqlalchemy-utils==0.41.1  # For database utilities in tests

# Services Testing Dependencies
responses==0.24.1          # HTTP request mocking for external API testing
pytest-httpx==0.30.0       # HTTPX testing support for async HTTP testing (updated for compatibility)
freezegun==1.4.0            # DateTime mocking for time-dependent tests
pytest-benchmark==4.0.0     # Performance benchmarking for services
aiofiles==23.2.1           # Async file operations testing
redis==5.0.1               # Redis client for cache testing
minio==7.2.0               # MinIO client for storage testing
