"""
Image-related caching functionality.
"""

import hashlib
from datetime import datetime
from cache.redis_connector import cache_set, cache_get, cache_delete, cache_exists
from core.config import settings

# Cache key prefixes
BATCH_PREFIX = "batch:"
IMAGE_PREFIX = "image:"
USER_BATCH_PREFIX = "user_batch:"
FILE_REVIEW_PREFIX = "file_review:"

def normalize_image_path(path: str) -> str:
    """
    Normalize image path for consistent cache key generation
    
    Args:
        path: Image path to normalize
        
    Returns:
        Normalized path
    """
    if not path:
        return path
    # Convert backslashes to forward slashes and strip whitespace
    normalized = path.replace('\\', '/').strip()
    return normalized

def generate_image_key(image_path):
    """
    Generate a cache key for an image

    Args:
        image_path: Path to the image

    Returns:
        str: Cache key
    """
    # Normalize path for consistent keys
    normalized_path = normalize_image_path(image_path)
    if normalized_path.startswith('/'):
        normalized_path = normalized_path[1:]

    # Create a hash of the path to avoid special characters in Redis keys
    path_hash = hashlib.md5(normalized_path.encode()).hexdigest()
    cache_key = f"{IMAGE_PREFIX}{path_hash}"

    return cache_key

def generate_batch_key(batch_id):
    """
    Generate a cache key for a batch

    Args:
        batch_id: Batch ID

    Returns:
        str: Cache key
    """
    return f"{BATCH_PREFIX}{batch_id}"

def generate_user_batch_key(username, mode="annotation"):
    """
    Generate a cache key for a user's current batch

    Args:
        username: Username
        mode: 'annotation' or 'verification'

    Returns:
        str: Cache key
    """
    return f"{USER_BATCH_PREFIX}{username}:{mode}"

def generate_file_review_key(file_id, batch_id):
    """
    Generate a cache key for a file's review data

    Args:
        file_id: File ID
        batch_id: Batch ID

    Returns:
        str: Cache key
    """
    return f"{FILE_REVIEW_PREFIX}{batch_id}:{file_id}"

async def cache_image(image_path, image_data):
    """
    Cache an image in Redis

    Args:
        image_path: Path to the image
        image_data: Binary image data

    Returns:
        bool: Success status
    """
    key = generate_image_key(image_path)
    ttl = settings.redis_settings.image_cache_ttl

    return await cache_set(key, image_data, ttl)

async def get_cached_image(image_path):
    """
    Get an image from Redis cache

    Args:
        image_path: Path to the image

    Returns:
        bytes: Image data or None if not found
    """
    key = generate_image_key(image_path)
    return await cache_get(key)

async def is_image_cached(image_path):
    """
    Check if an image is in Redis cache

    Args:
        image_path: Path to the image

    Returns:
        bool: True if image is cached, False otherwise
    """
    key = generate_image_key(image_path)
    return await cache_exists(key)

async def cache_batch(batch_id, batch_data):
    """
    Cache a batch in Redis

    Args:
        batch_id: Batch ID
        batch_data: Batch data dictionary

    Returns:
        bool: Success status
    """
    key = generate_batch_key(batch_id)
    ttl = settings.redis_settings.batch_cache_ttl

    # Add timestamp for cache freshness tracking
    batch_data['cached_at'] = datetime.now().isoformat()

    return await cache_set(key, batch_data, ttl)

async def get_cached_batch(batch_id):
    """
    Get a batch from Redis cache

    Args:
        batch_id: Batch ID

    Returns:
        dict: Batch data or None if not found
    """
    key = generate_batch_key(batch_id)
    return await cache_get(key, json_decode=True)

async def cache_user_batch(username, batch_id, mode="annotation"):
    """
    Cache a user's current batch ID

    Args:
        username: Username
        batch_id: Batch ID
        mode: 'annotation' or 'verification'

    Returns:
        bool: Success status
    """
    key = generate_user_batch_key(username, mode)
    ttl = settings.redis_settings.batch_cache_ttl

    return await cache_set(key, batch_id, ttl)

async def get_cached_user_batch_id(username, mode="annotation"):
    """
    Get a user's current batch ID from Redis cache

    Args:
        username: Username
        mode: 'annotation' or 'verification'

    Returns:
        str: Batch ID or None if not found
    """
    key = generate_user_batch_key(username, mode)
    batch_id = await cache_get(key)

    if batch_id and isinstance(batch_id, bytes):
        batch_id = batch_id.decode('utf-8')

    return batch_id

async def delete_user_batch_cache(username, mode="annotation"):
    """
    Delete a user's current batch ID from Redis cache

    Args:
        username: Username
        mode: 'annotation' or 'verification'

    Returns:
        bool: Success status
    """
    key = generate_user_batch_key(username, mode)
    return await cache_delete(key)

async def prefetch_batch_images(batch_data):
    """
    Prefetch and cache all images in a batch

    Args:
        batch_data: Batch data dictionary with 'images' list

    Returns:
        int: Number of images successfully cached
    """
    if not batch_data or 'images' not in batch_data:
        return 0

    try:
        from core.nas_connector import get_ftp_connector

        # Get FTP connector
        ftp_connector = await get_ftp_connector()

        if not ftp_connector:
            return 0

        success_count = 0
        failed_images = []

        for image_path in batch_data['images']:
            try:
                # Normalize path using our internal function
                normalized_path = normalize_image_path(image_path)
                # Skip if already cached
                if await is_image_cached(normalized_path):
                    success_count += 1
                    continue

                # Try to fetch the image content using original path for FTP
                content = None

                # Get content from FTP
                try:
                    content = await ftp_connector.get_file_content(image_path)
                except Exception as e:
                    pass

                # Cache the image if we got content (using normalized path for cache key)
                if content:
                    if await cache_image(normalized_path, content):
                        success_count += 1
                    else:
                        failed_images.append(image_path)
                else:
                    failed_images.append(image_path)
            except Exception as e:
                failed_images.append(image_path)

        return success_count
    except Exception as e:
        return 0

async def cache_file_review_data(file_id, batch_id, review_data):
    """
    Cache review data for a specific file

    Args:
        file_id: File ID
        batch_id: Batch ID
        review_data: Review data dictionary

    Returns:
        bool: Success status
    """
    key = generate_file_review_key(file_id, batch_id)
    ttl = settings.redis_settings.batch_cache_ttl
    
    # Add timestamp for cache freshness tracking
    review_data['cached_at'] = datetime.now().isoformat()
    
    return await cache_set(key, review_data, ttl)

async def get_cached_file_review_data(file_id, batch_id):
    """
    Get cached review data for a specific file

    Args:
        file_id: File ID
        batch_id: Batch ID

    Returns:
        dict: Review data or None if not found
    """
    key = generate_file_review_key(file_id, batch_id)
    return await cache_get(key, json_decode=True)

async def delete_cached_file_review_data(file_id, batch_id):
    """
    Delete cached review data for a specific file

    Args:
        file_id: File ID
        batch_id: Batch ID

    Returns:
        bool: Success status
    """
    key = generate_file_review_key(file_id, batch_id)
    return await cache_delete(key)

def prefetch_batch_images_async(batch_data):
    """
    Prefetch and cache all images in a batch asynchronously

    Args:
        batch_data: Batch data dictionary with 'images' list

    Returns:
        bool: Success status (indicates the async task was scheduled)
    """
    import asyncio

    try:
        # Schedule prefetch in the current event loop
        loop = asyncio.get_running_loop()
    except RuntimeError:
        # Fallback to default loop if no running loop
        loop = asyncio.get_event_loop()
    loop.create_task(prefetch_batch_images(batch_data))
    return True