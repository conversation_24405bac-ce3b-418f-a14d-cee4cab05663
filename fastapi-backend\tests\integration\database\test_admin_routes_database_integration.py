"""
Integration tests for Admin Routes Database operations with REAL database operations.
Tests end-to-end admin workflows through API endpoints with database persistence.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual admin route endpoints from routes/admin_route_modules/
- Database operations managed by service layer through API calls
- User authentication and authorization tested with real JWT tokens
- Multi-database coordination (master + project databases) tested
"""
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
import json
import time
from datetime import datetime

from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.admin_settings import AdminSettings
from app.post_db.allocation_models.allocation_batches import AllocationBatches
from app.services.auth_service import AuthService
from app.schemas.UserSchemas import UserRegisterRequest

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def admin_user_with_token(test_master_db: AsyncSession):
    """Create admin user and generate JWT token for authentication."""
    # Create admin user using factory
    admin_user_data = test_factory.users.create_user_register_request(role="admin")
    
    success, admin_user = await AuthService.register_user(test_master_db, admin_user_data)
    assert success
    
    # Generate JWT token
    from app.core.security import create_access_token
    token_data = {
        "sub": admin_user.username,
        "user_id": admin_user.id,
        "role": admin_user.role.value if hasattr(admin_user.role, 'value') else str(admin_user.role),
        "email": admin_user.email
    }
    access_token = create_access_token(data=token_data)
    
    return {
        "user": admin_user,
        "token": access_token,
        "password": "testpass123"  # From factory
    }


@pytest_asyncio.fixture
async def authenticated_admin_client(client: AsyncClient, admin_user_with_token):
    """Create authenticated admin client with proper headers."""
    token = admin_user_with_token["token"]
    client.headers.update({"Authorization": f"Bearer {token}"})
    return client


@pytest_asyncio.fixture
async def admin_test_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up complete admin test environment with client, strategy, and project."""
    # Use factory to create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Add additional client for multi-client testing
    additional_client = test_factory.projects.create_client()
    test_master_db.add(additional_client)
    await test_master_db.commit()
    await test_master_db.refresh(additional_client)
    
    environment["additional_client"] = additional_client
    return environment


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.smoke           # Suite marker - Critical path
@pytest.mark.critical        # Priority marker - P0
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminDashboardOperations:
    """SMOKE TEST SUITE: Critical admin dashboard operations."""
    
    @pytest.mark.asyncio
    async def test_admin_dashboard_data_retrieval_real_database(
        self, 
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin dashboard data retrieval with REAL database operations."""
        # Test dashboard endpoint
        response = await authenticated_admin_client.get("/api/admin/dashboard")
        
        # Should succeed or return meaningful config data
        assert response.status_code in [200, 500]  # 500 acceptable if NAS/Drive not configured in tests
        
        if response.status_code == 200:
            result = response.json()
            assert result["success"] is True
            assert "data" in result
            assert "config" in result["data"]
            
            # Verify configuration structure
            config = result["data"]["config"]
            assert "nas_connected" in config
            assert "drive_connected" in config
            assert isinstance(config["nas_connected"], bool)
            assert isinstance(config["drive_connected"], bool)
    
    @pytest.mark.asyncio
    async def test_admin_nas_directory_browsing_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test NAS directory browsing with REAL database operations."""
        # Test browsing root directory
        response = await authenticated_admin_client.get("/api/admin/browse-nas-directory?path=/")
        
        # Expected to fail in test environment due to missing NAS credentials
        # But should fail gracefully with proper error handling
        assert response.status_code in [200, 400, 500]
        
        if response.status_code == 400:
            result = response.json()
            assert "No project with NAS credentials found" in result["detail"] or \
                   "No NAS credentials found" in result["detail"]
        elif response.status_code == 200:
            # If configured, verify response structure
            result = response.json()
            assert result["success"] is True
            assert "data" in result
            assert "path" in result["data"]
            assert "items" in result["data"]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Comprehensive testing
@pytest.mark.high            # Priority marker - P1
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminUserManagementOperations:
    """REGRESSION TEST SUITE: Comprehensive admin user management operations."""
    
    @pytest.mark.asyncio
    async def test_admin_user_creation_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin user creation with REAL database operations."""
        # Create user data using factory
        user_data = test_factory.users.create_user_data(role="annotator")
        user_data.update({
            "password": "testpass123",
            "is_active": True,
            "annotation_mode": "annotation"
        })
        
        # Test user creation through admin endpoint
        response = await authenticated_admin_client.post("/api/admin/add-user", json=user_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "data" in result
        assert "user" in result["data"]
        
        created_user = result["data"]["user"]
        assert created_user["username"] == user_data["username"]
        assert created_user["email"] == user_data["email"]
        assert created_user["role"] == user_data["role"]
        assert created_user["is_active"] is True
        
        # Verify user exists in database
        stmt = select(users).where(users.username == user_data["username"])
        db_result = await test_master_db.execute(stmt)
        db_user = db_result.scalar_one_or_none()
        
        assert db_user is not None
        assert db_user.username == user_data["username"]
        assert db_user.email == user_data["email"]
        assert db_user.role == UserRole.ANNOTATOR
        assert db_user.is_active is True
    
    @pytest.mark.asyncio
    async def test_admin_user_listing_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin user listing with REAL database operations."""
        # Create test users in database
        test_users = []
        for i in range(3):
            user_data = test_factory.users.create_user_register_request(role="annotator")
            success, user = await AuthService.register_user(test_master_db, user_data)
            assert success
            test_users.append(user)
        
        # Test user listing
        response = await authenticated_admin_client.get("/api/admin/users")
        assert response.status_code == 200
        
        result = response.json()
        assert isinstance(result, list)
        
        # Should contain at least our test users (plus admin user from fixture)
        assert len(result) >= 3
        
        # Verify user data structure
        for user_response in result:
            assert "id" in user_response
            assert "username" in user_response
            assert "email" in user_response
            assert "role" in user_response
            assert "is_active" in user_response
    
    @pytest.mark.asyncio
    async def test_admin_user_suspension_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin user suspension with REAL database operations."""
        # Create test user
        user_data = test_factory.users.create_user_register_request(role="annotator")
        success, test_user = await AuthService.register_user(test_master_db, user_data)
        assert success
        
        # Test user suspension
        suspend_data = {"action": "suspend"}
        response = await authenticated_admin_client.post(
            f"/api/admin/users/{test_user.username}/suspend",
            json=suspend_data
        )
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "suspended" in result["message"]
        
        # Verify user is suspended in database
        stmt = select(users).where(users.username == test_user.username)
        db_result = await test_master_db.execute(stmt)
        updated_user = db_result.scalar_one_or_none()
        
        assert updated_user is not None
        assert updated_user.is_active is False
        
        # Test user unsuspension
        unsuspend_data = {"action": "unsuspend"}
        response = await authenticated_admin_client.post(
            f"/api/admin/users/{test_user.username}/suspend",
            json=unsuspend_data
        )
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "unsuspended" in result["message"]
        
        # Verify user is unsuspended in database
        await test_master_db.refresh(updated_user)
        assert updated_user.is_active is True
    
    @pytest.mark.asyncio
    async def test_admin_client_creation_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin client creation with REAL database operations."""
        # Create client data
        client_data = {
            "client_name": f"Test Client Corp {int(time.time())}",
            "contact_info": {
                "email": "<EMAIL>",
                "phone": "************"
            }
        }
        
        # Test client creation
        response = await authenticated_admin_client.post("/api/admin/add-client", json=client_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "data" in result
        assert "client" in result["data"]
        
        created_client = result["data"]["client"]
        assert created_client["name"] == client_data["client_name"]
        assert created_client["contact_info"] == client_data["contact_info"]
        assert "id" in created_client
        assert isinstance(created_client["id"], int)
        
        # Verify client exists in database
        stmt = select(Clients).where(Clients.id == created_client["id"])
        db_result = await test_master_db.execute(stmt)
        db_client = db_result.scalar_one_or_none()
        
        assert db_client is not None
        assert db_client.name == client_data["client_name"]
        assert db_client.contact_info == client_data["contact_info"]
    
    @pytest.mark.asyncio
    async def test_admin_client_listing_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin client listing with REAL database operations."""
        # Test client listing
        response = await authenticated_admin_client.get("/api/admin/clients")
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "data" in result
        assert "clients" in result["data"]
        
        clients = result["data"]["clients"]
        assert isinstance(clients, list)
        # Should contain at least the clients from test environment
        assert len(clients) >= 1
        
        # Verify client data structure
        for client in clients:
            assert "id" in client
            assert "name" in client
            assert "username" in client
            assert "email" in client
            assert isinstance(client["id"], int)


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Comprehensive testing
@pytest.mark.high            # Priority marker - P1
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminProjectManagementOperations:
    """REGRESSION TEST SUITE: Comprehensive admin project management operations."""
    
    @pytest.mark.asyncio
    async def test_admin_project_listing_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin project listing with REAL database operations."""
        # Test project listing without filters
        response = await authenticated_admin_client.get("/api/admin/projects")
        assert response.status_code == 200
        
        result = response.json()
        assert "projects" in result
        assert "total" in result
        assert "page" in result
        assert "page_size" in result
        
        projects = result["projects"]
        assert isinstance(projects, list)
        # Should contain at least the project from test environment
        assert len(projects) >= 1
        
        # Verify project data structure
        for project in projects:
            assert "id" in project
            assert "project_code" in project
            assert "project_name" in project
            assert "project_type" in project
            assert "project_status" in project
            assert "client" in project
            
            # Verify client information
            client = project["client"]
            assert "id" in client
            assert "name" in client
    
    @pytest.mark.asyncio
    async def test_admin_project_filtering_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin project filtering with REAL database operations."""
        project = admin_test_environment["project"]
        
        # Test filtering by project type
        response = await authenticated_admin_client.get(
            f"/api/admin/projects?project_type={project.project_type}"
        )
        assert response.status_code == 200
        
        result = response.json()
        projects = result["projects"]
        assert len(projects) >= 1
        
        # All projects should match the filter
        for proj in projects:
            assert proj["project_type"] == project.project_type
        
        # Test filtering by project status
        response = await authenticated_admin_client.get(
            f"/api/admin/projects?project_status={project.project_status}"
        )
        assert response.status_code == 200
        
        result = response.json()
        projects = result["projects"]
        assert len(projects) >= 1
        
        # All projects should match the filter
        for proj in projects:
            assert proj["project_status"] == project.project_status
    
    @pytest.mark.asyncio
    async def test_admin_project_search_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin project search with REAL database operations."""
        project = admin_test_environment["project"]
        
        # Test search by project name
        search_term = project.project_name[:5]  # Use first 5 characters
        response = await authenticated_admin_client.get(
            f"/api/admin/projects?search={search_term}"
        )
        assert response.status_code == 200
        
        result = response.json()
        projects = result["projects"]
        
        # Should find our project
        found_project = any(p["project_code"] == project.project_code for p in projects)
        assert found_project
        
        # Test search by project code
        search_term = project.project_code[:8]  # Use first 8 characters
        response = await authenticated_admin_client.get(
            f"/api/admin/projects?search={search_term}"
        )
        assert response.status_code == 200
        
        result = response.json()
        projects = result["projects"]
        
        # Should find our project
        found_project = any(p["project_code"] == project.project_code for p in projects)
        assert found_project


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Comprehensive testing
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminDatasetOperations:
    """REGRESSION TEST SUITE: Admin dataset management operations."""
    
    @pytest.mark.asyncio
    async def test_admin_dataset_listing_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        setup_test_database
    ):
        """Test admin dataset listing with REAL database operations."""
        # Test dataset listing
        response = await authenticated_admin_client.get("/api/admin/get-datasets")
        
        # Expected to succeed or fail gracefully due to user-client relationship requirements
        assert response.status_code in [200, 404, 422]
        
        if response.status_code == 200:
            result = response.json()
            assert result["success"] is True
            assert "data" in result
            assert "datasets" in result["data"]
            assert "count" in result["data"]
            
            datasets = result["data"]["datasets"]
            assert isinstance(datasets, list)
        
        # Test dataset listing with specific project code
        project = admin_test_environment["project"]
        response = await authenticated_admin_client.get(
            f"/api/admin/get-datasets?project_code={project.project_code}"
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 422]
    
    @pytest.mark.asyncio
    async def test_admin_dataset_selection_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin dataset selection with REAL database operations."""
        project = admin_test_environment["project"]
        
        # Update project to active status for testing
        project.project_status = "active"
        test_master_db.add(project)
        await test_master_db.commit()
        
        # Test dataset selection for annotation
        selection_data = {
            "dataset_id": project.project_code,
            "mode": "annotation"
        }
        
        response = await authenticated_admin_client.post("/api/admin/select-dataset", json=selection_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "annotation project" in result["message"]
        assert project.project_code in result["message"]
        
        # Verify admin settings were updated in database
        stmt = select(AdminSettings).limit(1)
        db_result = await test_master_db.execute(stmt)
        admin_settings = db_result.scalar_one_or_none()
        
        if admin_settings:
            assert admin_settings.active_annotation_project_code == project.project_code
        
        # Test dataset selection for verification
        selection_data = {
            "dataset_id": project.project_code,
            "mode": "verification"
        }
        
        response = await authenticated_admin_client.post("/api/admin/select-dataset", json=selection_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "verification project" in result["message"]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Comprehensive testing
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminProjectInstructionsOperations:
    """REGRESSION TEST SUITE: Admin project instructions operations."""
    
    @pytest.mark.asyncio
    async def test_admin_project_instructions_update_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        admin_test_environment,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test project instructions update with REAL database operations."""
        project = admin_test_environment["project"]
        
        # Test instructions update
        new_instructions = f"Updated instructions for testing at {datetime.now().isoformat()}"
        instructions_data = {
            "project_code": project.project_code,
            "instructions": new_instructions
        }
        
        response = await authenticated_admin_client.post(
            "/api/admin/edit-project-instructions",
            json=instructions_data
        )
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert project.project_code in result["message"]
        assert "updated successfully" in result["message"]
        
        # Verify instructions were updated in database
        stmt = select(ProjectsRegistry).where(ProjectsRegistry.project_code == project.project_code)
        db_result = await test_master_db.execute(stmt)
        updated_project = db_result.scalar_one_or_none()
        
        assert updated_project is not None
        assert updated_project.instructions == new_instructions
    
    @pytest.mark.asyncio
    async def test_admin_project_instructions_update_nonexistent_project_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        setup_test_database
    ):
        """Test project instructions update for non-existent project."""
        # Test with non-existent project
        instructions_data = {
            "project_code": "NONEXISTENT_PROJECT_CODE",
            "instructions": "These instructions should fail"
        }
        
        response = await authenticated_admin_client.post(
            "/api/admin/edit-project-instructions",
            json=instructions_data
        )
        assert response.status_code == 404
        
        result = response.json()
        assert "Project with code NONEXISTENT_PROJECT_CODE not found" in result["detail"]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Error scenarios
@pytest.mark.high            # Priority marker - P1 (error handling is critical)
@pytest.mark.stable          # Stability marker - Reliable
class TestAdminErrorHandling:
    """REGRESSION TEST SUITE: Admin error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_admin_routes_without_authentication(
        self,
        client: AsyncClient,
        setup_test_database
    ):
        """Test admin routes without authentication should fail."""
        # Test various admin endpoints without authentication
        endpoints = [
            "/api/admin/dashboard",
            "/api/admin/users",
            "/api/admin/projects",
            "/api/admin/clients",
            "/api/admin/get-datasets"
        ]
        
        for endpoint in endpoints:
            response = await client.get(endpoint)
            assert response.status_code == 401, f"Endpoint {endpoint} should require authentication"
    
    @pytest.mark.asyncio
    async def test_admin_routes_with_non_admin_user(
        self,
        client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test admin routes with non-admin user should fail."""
        # Create non-admin user
        user_data = test_factory.users.create_user_register_request(role="annotator")
        success, user = await AuthService.register_user(test_master_db, user_data)
        assert success
        
        # Generate token for non-admin user
        from app.core.security import create_access_token
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role)
        }
        access_token = create_access_token(data=token_data)
        
        # Test admin endpoints with non-admin token
        client.headers.update({"Authorization": f"Bearer {access_token}"})
        
        admin_endpoints = [
            "/api/admin/users",
            "/api/admin/add-user",
            "/api/admin/projects",
            "/api/admin/add-client"
        ]
        
        for endpoint in admin_endpoints:
            if endpoint == "/api/admin/add-user":
                response = await client.post(endpoint, json={})
            else:
                response = await client.get(endpoint)
            
            # Should fail with 403 Forbidden due to admin requirement
            assert response.status_code in [403, 422], f"Endpoint {endpoint} should require admin role"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.admin           # Feature marker
@pytest.mark.regression      # Suite marker - Database operations
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
@pytest.mark.slow            # Execution marker - Database flush operations take time
class TestAdminDatabaseFlushOperations:
    """REGRESSION TEST SUITE: Admin database flush and cleanup operations."""
    
    @pytest.mark.asyncio
    async def test_admin_database_flush_real_database(
        self,
        authenticated_admin_client: AsyncClient,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test admin database flush with REAL database operations."""
        # Insert some test data that will be flushed
        try:
            # Try to create tables that might exist (ignore errors if they don't)
            await test_db.execute(text("CREATE TABLE IF NOT EXISTS image_annotation (id SERIAL PRIMARY KEY)"))
            await test_db.execute(text("INSERT INTO image_annotation DEFAULT VALUES"))
            await test_db.commit()
        except Exception:
            # Table might not exist or operation might fail - that's OK for this test
            await test_db.rollback()
        
        # Test database flush
        response = await authenticated_admin_client.post("/api/admin/flush-db")
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert "flushed successfully" in result["message"]
        
        # The flush operation should complete without errors
        # Note: Actual tables flushed depend on the database schema
