from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, TIMESTAMP, Boolean, func, Foreign<PERSON>ey, UniqueConstraint, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum


class WorkflowPhase(str, PyEnum):
    """Workflow phase enumeration."""
    ANNOTATION = "annotation"
    REVIEW = "review"
    VERIFICATION = "verification"

class AllocationStatus(str, PyEnum):
    """Allocation status enumeration."""
    ALLOCATED = "allocated"
    ACTIVE = "active"
    SUBMITTED = "submitted"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class ProcessingStatus(str, PyEnum):
    """File processing status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    READY = "ready"
    FAILED = "failed"

class FileAllocations(ProjectBase):
    """
    Manages allocation of individual files to specific users. 
    Handles parallel and blind allocation workflows where multiple users work on the same file.
    """
    __tablename__ = "file_allocations"
    
    __table_args__ = (
        UniqueConstraint('file_id', 'allocation_sequence', name='_file_allocation_seq_uc'),
    )

    # Primary Identity & Core Relationships
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique file allocation identifier")
    file_id = Column(Integer, ForeignKey("files_registry.id", ondelete="CASCADE"), nullable=False, index=True,
                    comment="Reference to file being allocated")
    batch_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"), nullable=False, index=True,
                      comment="Reference to allocation batch")
    
    # Allocation Context & Positioning
    allocation_sequence = Column(Integer, default=1, nullable=False,
                                comment="Sequence number for parallel allocations (1st, 2nd, 3rd annotator for same file)")
    workflow_phase = Column(CaseInsensitiveEnum(WorkflowPhase), default=WorkflowPhase.ANNOTATION, nullable=False,
                           comment="Workflow phase")
    
    # File Processing & Preparation (moved from FilesRegistry)
    processing_status = Column(CaseInsensitiveEnum(ProcessingStatus), default=ProcessingStatus.PENDING, nullable=False,
                              comment="Current processing state")
    processed_metadata = Column(JSONB, nullable=True,
                               comment="Extracted file metadata (dimensions, duration, format details) for annotation tools")
    preprocessing_results = Column(JSONB, nullable=True,
                                  comment="Results of preprocessing steps (resizing, format conversion, thumbnails)")
    
    # Timeline & Deadlines
    allocated_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                         comment="When file was allocated to this user")
    activation_deadline = Column(TIMESTAMP, nullable=True,
                                comment="Deadline for user to start working")
    completion_deadline = Column(TIMESTAMP, nullable=True,
                                comment="Deadline for completing this allocation")
    
    # Allocation Rules & Constraints
    allocation_rules = Column(JSONB, nullable=True,
                             comment="Custom rules for this specific allocation")
    
    # Assignment & Completion Tracking
    assignment_count = Column(Integer, default=0, nullable=False,
                            comment="Number of times this file has been assigned")
    completion_count = Column(Integer, default=0, nullable=False,
                            comment="Number of times this file has been completed")
    
    # Dynamic columns for annotators will be added at runtime
    # Example: annotator_1 = Column(Integer, nullable=True, comment='References user_id in project_users table')
    # Example: annotator_1_review = Column(JSONB, nullable=True, comment='Review data for annotator 1')

    # Dynamic columns for verifier will be added at runtime for strategies that require verification
    # Example: verifier = Column(Integer, nullable=True, comment='References user_id in project_users table')
    # Example: verifier_review = Column(JSONB, nullable=True, comment='Review data for verifier')

    # Relationships
    file = relationship("FilesRegistry", back_populates="file_allocations")
    batch = relationship("AllocationBatches", back_populates="file_allocations")

    def __repr__(self):
        return f"<FileAllocations(id={self.id}, file_id={self.file_id}, allocation_sequence={self.allocation_sequence}, workflow_phase={self.workflow_phase})>"