"""
Integration tests for ProjectUsersService with real external systems.
Tests user management with real database operations and cross-service coordination.

REAL SYSTEM INTEGRATION:
- Real PostgreSQL master and project database connections
- Real dynamic role assignment based on allocation strategies
- Real user project assignment workflows
- Real cross-database operations and transaction management
- Real user eligibility validation with database constraints
"""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta
import time
import asyncio

from app.services.project_users_service import ProjectUsersService
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

class TestProjectUsersServiceIntegration:
    """Integration tests for ProjectUsersService with real dependencies."""
    
    @pytest.fixture
    async def real_master_db_session(self):
        """Real master database session."""
        from app.post_db.master_db import MasterSessionLocal
        async with MasterSessionLocal() as session:
            yield session
    
    @pytest.fixture
    async def real_test_project_data(self):
        """Real test project data."""
        timestamp = int(time.time())
        return {
            'project_code': f'INTEGRATION_USER_TEST_{timestamp}',
            'project_name': 'Integration User Test Project',
            'database_name': f'integration_user_test_{timestamp}_db',
            'allocation_strategies': {
                'single_annotator': {
                    'strategy_type': 'single_annotator_no_verification',
                    'annotators_per_batch': 1,
                    'verifiers_per_batch': 0,
                    'verification_required': False
                },
                'dual_annotator': {
                    'strategy_type': 'dual_annotator_verification',
                    'annotators_per_batch': 2,
                    'verifiers_per_batch': 1,
                    'verification_required': True
                },
                'three_annotator': {
                    'strategy_type': 'three_annotator_verification',
                    'annotators_per_batch': 3,
                    'verifiers_per_batch': 1,
                    'verification_required': True,
                    'cross_verification': True
                }
            }
        }
    
    @pytest.fixture
    async def real_test_users(self):
        """Real test users for integration testing."""
        timestamp = int(time.time())
        return [
            {
                'user_id': 7001,
                'username': f'integration_annotator_1_{timestamp}',
                'email': f'ann1_{timestamp}@integration.test',
                'role': 'annotator',
                'is_active': True
            },
            {
                'user_id': 7002,
                'username': f'integration_annotator_2_{timestamp}',
                'email': f'ann2_{timestamp}@integration.test',
                'role': 'annotator',
                'is_active': True
            },
            {
                'user_id': 7003,
                'username': f'integration_annotator_3_{timestamp}',
                'email': f'ann3_{timestamp}@integration.test',
                'role': 'annotator',
                'is_active': True
            },
            {
                'user_id': 7101,
                'username': f'integration_verifier_1_{timestamp}',
                'email': f'ver1_{timestamp}@integration.test',
                'role': 'verifier',
                'is_active': True
            },
            {
                'user_id': 7201,
                'username': f'integration_admin_1_{timestamp}',
                'email': f'admin1_{timestamp}@integration.test',
                'role': 'admin',
                'is_active': True
            }
        ]

    # ==================================================================
    # REAL PROJECT STRATEGY INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_project_strategy_retrieval_from_database(self, real_master_db_session, real_test_project_data):
        """Test retrieving project allocation strategy from real master database."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        
        try:
            result = await service.get_project_strategy(project_code)
            
            # Should return None for non-existent project
            assert result is None
            
        except Exception as e:
            # Database connection issues are acceptable
            assert any(keyword in str(e).lower() for keyword in ["connection", "database", "timeout"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_allocation_strategy_compliance_validation(self, real_test_project_data):
        """Test allocation strategy compliance with real database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        strategies = real_test_project_data['allocation_strategies']
        
        for strategy_name, strategy_config in strategies.items():
            try:
                # Test strategy validation with real database
                compliance_result = await service.validate_strategy_compliance(
                    project_code,
                    strategy_config
                )
                
                # Should return compliance status
                assert isinstance(compliance_result, dict)
                assert 'compliant' in compliance_result or 'error' in compliance_result
                
            except Exception as e:
                # Expected for non-existent projects
                assert any(keyword in str(e).lower() for keyword in ["project", "database", "not found"])

    # ==================================================================
    # REAL USER PROJECT ASSIGNMENT INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_user_project_assignment_workflow(self, real_test_project_data, user_factory):
        """Test complete user project assignment workflow with real database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        annotator = real_test_users[0]  # First annotator
        
        try:
            # Test adding user to project (would create real database entries)
            result = await service.add_user_to_project(
                project_code,
                annotator['user_id'],
                'annotator'
            )
            
            # Should handle non-existent project gracefully
            assert isinstance(result, dict)
            assert 'success' in result or 'error' in result
            
            if result.get('success'):
                # Test retrieving user from project
                user_projects = await service.get_user_projects(annotator['user_id'])
                assert isinstance(user_projects, list)
                
                # Test removing user from project
                removal_result = await service.remove_user_from_project(
                    project_code,
                    annotator['user_id']
                )
                assert isinstance(removal_result, dict)
            
        except Exception as e:
            # Database operations expected to fail for non-existent project
            assert any(keyword in str(e).lower() for keyword in ["database", "project", "not found", "constraint"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_bulk_user_assignment_performance(self, real_test_project_data, user_factory):
        """Test bulk user assignment performance with real database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        users = real_test_users[:3]  # Use first 3 users
        
        start_time = time.time()
        
        try:
            # Test bulk assignment
            assignment_tasks = [
                service.add_user_to_project(project_code, user['user_id'], user['role'])
                for user in users
            ]
            
            results = await asyncio.gather(*assignment_tasks, return_exceptions=True)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Performance validation
            assert processing_time < 5.0  # Should complete bulk operations in under 5 seconds
            assert len(results) == len(users)
            
            # Verify results
            for result in results:
                if isinstance(result, dict):
                    assert 'success' in result or 'error' in result
                elif isinstance(result, Exception):
                    # Acceptable for non-existent project
                    assert any(keyword in str(result).lower() for keyword in ["database", "project", "not found"])
            
        except Exception as e:
            # Bulk operations may fail due to missing project
            assert any(keyword in str(e).lower() for keyword in ["database", "project", "connection"])

    # ==================================================================
    # REAL DYNAMIC ROLE MANAGEMENT INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_dynamic_role_assignment_with_strategy_changes(self, real_test_project_data, user_factory):
        """Test dynamic role assignment when project strategy changes with real database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        user = real_test_users[0]
        strategies = real_test_project_data['allocation_strategies']
        
        try:
            # Test role assignment with different strategies
            for strategy_name, strategy_config in strategies.items():
                role_update_result = await service.update_user_role_for_strategy(
                    project_code,
                    user['user_id'],
                    strategy_config
                )
                
                # Should handle gracefully
                assert isinstance(role_update_result, dict)
                assert 'success' in role_update_result or 'error' in role_update_result
                
        except Exception as e:
            # Expected for non-existent project
            assert any(keyword in str(e).lower() for keyword in ["project", "database", "not found"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_user_eligibility_validation_with_database_constraints(self, real_test_project_data, user_factory):
        """Test user eligibility validation with real database constraints."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        
        # Test different user roles
        test_cases = [
            {'user': real_test_users[0], 'role': 'annotator'},  # Annotator requesting annotator role
            {'user': real_test_users[3], 'role': 'verifier'},   # Verifier requesting verifier role
            {'user': real_test_users[0], 'role': 'verifier'},   # Annotator requesting verifier role
        ]
        
        for case in test_cases:
            try:
                eligibility_result = await service.validate_user_eligibility(
                    project_code,
                    case['user']['user_id'],
                    case['role']
                )
                
                # Should return eligibility status
                assert isinstance(eligibility_result, (bool, dict))
                
                if isinstance(eligibility_result, dict):
                    assert 'eligible' in eligibility_result or 'error' in eligibility_result
                
            except Exception as e:
                # Database validation may fail for non-existent entities
                assert any(keyword in str(e).lower() for keyword in ["user", "project", "database", "not found"])

    # ==================================================================
    # REAL CROSS-DATABASE OPERATIONS INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_master_project_database_synchronization(self, real_master_db_session, real_test_project_data, user_factory):
        """Test synchronization between master database and project database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        user = real_test_users[0]
        
        try:
            # Test operations that span both databases
            # 1. Get user data from master DB
            master_user_data = await service.get_master_user_data(user['user_id'])
            
            # 2. Sync with project DB
            if master_user_data:
                sync_result = await service.sync_user_data(
                    project_code,
                    user['user_id']
                )
                
                assert isinstance(sync_result, dict)
                assert 'synced' in sync_result or 'error' in sync_result
            else:
                # User doesn't exist in master DB - expected for test data
                assert master_user_data is None
                
        except Exception as e:
            # Cross-database operations may fail
            assert any(keyword in str(e).lower() for keyword in ["database", "sync", "connection", "not found"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_transaction_consistency_across_databases(self, real_test_project_data, user_factory):
        """Test transaction consistency across master and project databases."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        user = real_test_users[0]
        
        try:
            # Test transaction that affects both databases
            result = await service.assign_user_with_role_validation(
                project_code,
                user['user_id'],
                'annotator'
            )
            
            # Should maintain consistency or rollback properly
            assert isinstance(result, dict)
            
            if result.get('success'):
                # Verify consistency
                user_status = await service.get_user_project_status(
                    project_code,
                    user['user_id']
                )
                assert isinstance(user_status, dict)
            else:
                # Transaction should fail cleanly
                assert 'error' in result
                
        except Exception as e:
            # Transaction failures are acceptable for non-existent entities
            assert any(keyword in str(e).lower() for keyword in ["transaction", "rollback", "database", "constraint"])

    # ==================================================================
    # REAL CONCURRENT OPERATIONS INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_concurrent_user_assignments_race_conditions(self, real_test_project_data, user_factory):
        """Test concurrent user assignments with real database race conditions."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        users = real_test_users[:3]  # First 3 users
        
        # Concurrent assignment attempts
        async def assign_user_concurrently(user_data):
            try:
                result = await service.add_user_to_project(
                    project_code,
                    user_data['user_id'],
                    user_data['role']
                )
                return {
                    'user_id': user_data['user_id'],
                    'success': result.get('success', False),
                    'error': result.get('error')
                }
            except Exception as e:
                return {
                    'user_id': user_data['user_id'],
                    'success': False,
                    'error': str(e)
                }
        
        # Execute concurrent assignments
        tasks = [assign_user_concurrently(user) for user in users]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify race condition handling
        for result in results:
            if isinstance(result, dict):
                assert 'user_id' in result
                assert 'success' in result
                if not result['success']:
                    assert 'error' in result
            elif isinstance(result, Exception):
                # Concurrent operations may cause exceptions
                assert any(keyword in str(result).lower() for keyword in ["constraint", "race", "duplicate", "database"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_service_load_testing_with_database(self, real_test_project_data, user_factory):
        """Test service performance under load with real database operations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        
        # Create load testing scenarios
        async def load_operation(operation_id):
            try:
                if operation_id % 3 == 0:
                    # Get project strategy
                    return await service.get_project_strategy(f'{project_code}_{operation_id}')
                elif operation_id % 3 == 1:
                    # Get user projects
                    return await service.get_user_projects(6000 + operation_id)
                else:
                    # Validate user eligibility
                    return await service.validate_user_eligibility(
                        f'{project_code}_{operation_id}',
                        6000 + operation_id,
                        'annotator'
                    )
            except Exception as e:
                return {'error': str(e)}
        
        # Execute 30 concurrent operations
        start_time = time.time()
        tasks = [load_operation(i) for i in range(30)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Performance validation
        assert processing_time < 15.0  # Should handle load in under 15 seconds
        assert len(results) == 30
        
        # Verify service resilience
        for result in results:
            # Service should handle all operations without crashing
            assert result is not None

    # ==================================================================
    # REAL ERROR HANDLING AND RESILIENCE INTEGRATION TESTS
    # ==================================================================

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_database_constraint_violation_handling(self, real_test_project_data, user_factory):
        """Test handling of real database constraint violations."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        user = real_test_users[0]
        
        try:
            # Test constraint violation (duplicate assignment)
            result1 = await service.add_user_to_project(project_code, user['user_id'], 'annotator')
            result2 = await service.add_user_to_project(project_code, user['user_id'], 'annotator')
            
            # First might succeed, second should fail with constraint violation
            if result1.get('success') and result2.get('success'):
                # Both succeeded - might be due to non-existent project handling
                pass
            else:
                # At least one should fail due to constraint or project non-existence
                assert not (result1.get('success') and result2.get('success'))
                
        except Exception as e:
            # Constraint violations are acceptable
            assert any(keyword in str(e).lower() for keyword in ["constraint", "duplicate", "violation", "project"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_network_timeout_and_recovery(self, real_test_project_data):
        """Test service behavior during network timeouts and recovery."""
        
        service = ProjectUsersService()
        project_code = real_test_project_data['project_code']
        
        # Test operations that might timeout
        timeout_operations = [
            service.get_project_strategy(project_code),
            service.get_all_project_users(project_code),
            service.validate_strategy_compliance(project_code, {'annotators_per_batch': 1})
        ]
        
        try:
            # Execute with timeout
            results = await asyncio.wait_for(
                asyncio.gather(*timeout_operations, return_exceptions=True),
                timeout=10.0  # 10 second timeout
            )
            
            # Service should handle timeouts gracefully
            for result in results:
                if isinstance(result, Exception):
                    assert any(keyword in str(result).lower() for keyword in ["timeout", "connection", "database"])
                else:
                    # Successful operations should return expected types
                    assert result is None or isinstance(result, (list, dict))
                    
        except asyncio.TimeoutError:
            # Timeout is acceptable for integration testing
            pass
