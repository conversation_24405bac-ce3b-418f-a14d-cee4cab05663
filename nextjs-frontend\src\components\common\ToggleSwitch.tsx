'use client';


interface ToggleSwitchProps {
  leftOption: string;
  rightOption: string;
  isRightActive: boolean;
  onChange: (isRightActive: boolean) => void;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const ToggleSwitch = ({
  leftOption,
  rightOption,
  isRightActive,
  onChange,
  leftIcon,
  rightIcon
}: ToggleSwitchProps) => {
  return (
    <div className="flex flex-col items-center space-y-2">
      <div className="flex items-center justify-center bg-gray-100 p-1 rounded-full w-full max-w-md">
        <button
          onClick={() => onChange(false)}
          className={`flex items-center justify-center py-2 px-4 rounded-full w-1/2 transition-all duration-300 ${
            !isRightActive 
              ? 'bg-primary text-white shadow-md' 
              : 'bg-transparent text-gray-600 hover:bg-gray-200'
          }`}
        >
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {leftOption}
        </button>
        <button
          onClick={() => onChange(true)}
          className={`flex items-center justify-center py-2 px-4 rounded-full w-1/2 transition-all duration-300 ${
            isRightActive 
              ? 'bg-primary text-white shadow-md' 
              : 'bg-transparent text-gray-600 hover:bg-gray-200'
          }`}
        >
          {rightIcon && <span className="mr-2">{rightIcon}</span>}
          {rightOption}
        </button>
      </div>
    </div>
  );
};

export default ToggleSwitch; 