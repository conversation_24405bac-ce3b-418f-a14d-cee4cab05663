import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from utils.csv_batch_processor import CSVBatchProcessor
from core.session_manager import get_project_db_session
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select

logger = logging.getLogger(__name__)

class CSVBatchService:
    """
    Service for creating database batches from CSV data.
    """
    
    def __init__(self):
        self.csv_processor = CSVBatchProcessor()
        
    async def create_csv_batches_in_project_db(
        self,
        project_code: str,
        master_db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Create batches in the project database from CSV data.
        
        Args:
            project_code: The project code
            master_db: Master database session
            
        Returns:
            Dict with creation results
        """
        try:
            # Get project allocation strategy information
            project_info = await self._get_project_info(project_code, master_db)
            annotation_count = project_info.get('num_annotators', 1)
            
            # Debug logging to ensure we're getting the right value
            logger.info(f"CSV Batch Creation - Project: {project_code}, Annotation Count: {annotation_count}")
            logger.info(f"CSV Batch Creation - Project Info: {project_info}")
            
            # Process CSV and get batches
            processing_result = await self.csv_processor.process_csv_for_project(project_code, master_db)
            
            if not processing_result["success"]:
                return processing_result
            
            batches = processing_result["batches"]
            statistics = processing_result["statistics"]
            
            # Create batches in the project database using unified session manager
            async with get_project_db_session(project_code) as project_db:
                created_batch_ids = []
                created_file_count = 0
                
                # Create batches in the project database
                for batch_data in batches:
                    try:
                        # Add annotation_count to batch_data
                        batch_data["annotation_count"] = annotation_count
                        
                        # Debug logging for batch creation
                        logger.info(f"Creating batch {batch_data.get('batch_identifier')} with annotation_count: {annotation_count}")
                        
                        batch_id = await self._create_allocation_batch(project_db, batch_data)
                        if batch_id:
                            created_batch_ids.append(batch_id)
                            
                            # Create files for this batch
                            files_created = await self._create_files_for_batch(
                                project_db, batch_id, batch_data["files"]
                            )
                            created_file_count += files_created
                            
                            # Create file allocations
                            await self._create_file_allocations_for_batch(
                                project_db, batch_id, batch_data["files"]
                            )
                        else:
                            logger.error(f"Failed to create batch: {batch_data['batch_identifier']}")
                    except Exception as batch_error:
                        logger.error(f"Error processing batch {batch_data['batch_identifier']}: {str(batch_error)}")
                        # Rollback this batch and continue with others
                        await project_db.rollback()
                        continue
                
                # Commit all changes
                await project_db.commit()
                
                return {
                    "success": True,
                    "created_batches": len(created_batch_ids),
                    "created_files": created_file_count,
                    "batch_ids": created_batch_ids,
                    "statistics": statistics,
                    "message": f"Successfully created {len(created_batch_ids)} batches with {created_file_count} files"
                }
                
        except Exception as e:
            logger.error(f"Error in create_csv_batches_in_project_db: {str(e)}")
            return {
                "success": False,
                "error": f"Service error: {str(e)}"
            }
    
    async def _create_allocation_batch(
        self, 
        project_db: AsyncSession, 
        batch_data: Dict[str, Any]
    ) -> Optional[int]:
        """
        Create an allocation_batch record.
        
        Args:
            project_db: Project database session
            batch_data: Batch data dictionary
            
        Returns:
            Created batch ID or None if failed
        """
        try:
            query = """
            INSERT INTO allocation_batches (
                batch_identifier, 
                batch_status, 
                total_files, 
                file_list, 
                skill_requirements,
                allocation_criteria,
                is_priority,
                created_at,
                annotation_count,
                assignment_count,
                completion_count,
                custom_batch_config
            ) VALUES (
                :batch_identifier,
                :batch_status,
                :total_files,
                :file_list,
                :skill_requirements,
                :allocation_criteria,
                :is_priority,
                :created_at,
                :annotation_count,
                :assignment_count,
                :completion_count,
                :custom_batch_config
            ) RETURNING id
            """
            
            # Prepare file list for JSON storage
            file_list = []
            for f in batch_data["files"]:
                # Handle long cell values for file_identifier (same logic as in files_registry)
                cell_value = str(f["cell_value"])
                if len(cell_value) > 250:  # Leave some buffer
                    # For long values, use truncated version with row info
                    file_identifier = f"Row_{f['row_index']}_{cell_value[:200]}..."
                    if len(file_identifier) > 255:
                        file_identifier = f"Row_{f['row_index']}_LongText"
                else:
                    file_identifier = cell_value
                
                file_list.append({
                    "file_identifier": file_identifier,  # Truncated if needed
                    "cell_value": f["cell_value"],  # Keep full cell value here
                    "row_index": f["row_index"],
                    "column_name": f["column_name"]
                })
            
            annotation_count_value = batch_data.get("annotation_count", 0)
            logger.info(f"SQL Insert - Batch: {batch_data['batch_identifier']}, annotation_count: {annotation_count_value}")
            
            result = await project_db.execute(text(query), {
                "batch_identifier": batch_data["batch_identifier"],
                "batch_status": batch_data["batch_status"],
                "total_files": batch_data["total_files"],
                "file_list": json.dumps(file_list),
                "skill_requirements": json.dumps({"csv_annotation": True}),
                "allocation_criteria": json.dumps({"batch_type": "csv_cells"}),
                "is_priority": False,
                "created_at": datetime.now(),
                "annotation_count": annotation_count_value,
                "assignment_count": 0,
                "completion_count": 0,
                "custom_batch_config": json.dumps(batch_data["batch_metadata"])
            })
            
            batch_id = result.scalar()
            logger.info(f"Created allocation_batch with ID: {batch_id}")
            return batch_id
            
        except Exception as e:
            logger.error(f"Error creating allocation_batch: {str(e)}")
            return None
    
    async def _create_files_for_batch(
        self,
        project_db: AsyncSession,
        batch_id: int,
        files_data: List[Dict[str, Any]]
    ) -> int:
        """
        Create files_registry records for a batch.
        
        Args:
            project_db: Project database session
            batch_id: The batch ID
            files_data: List of file data dictionaries
            
        Returns:
            Number of files created
        """
        try:
            query = """
            INSERT INTO files_registry (
                batch_id,
                file_identifier,
                original_filename,
                file_type,
                file_extension,
                storage_location,
                file_size_bytes,
                file_hash,
                sequence_order,
                uploaded_at
            ) VALUES (
                :batch_id,
                :file_identifier,
                :original_filename,
                :file_type,
                :file_extension,
                :storage_location,
                :file_size_bytes,
                :file_hash,
                :sequence_order,
                :uploaded_at
            ) RETURNING id
            """
            
            created_count = 0
            
            for file_data in files_data:
                # Storage location contains just the row index
                storage_location = file_data["row_index"]
                
                # Handle long cell values for file_identifier (max 255 chars)
                cell_value = str(file_data["cell_value"])
                if len(cell_value) > 250:  # Leave some buffer
                    # For long values, use truncated version with row info
                    file_identifier = f"Row_{file_data['row_index']}_{cell_value[:200]}..."
                    if len(file_identifier) > 255:
                        file_identifier = f"Row_{file_data['row_index']}_LongText"
                else:
                    file_identifier = cell_value
                
                result = await project_db.execute(text(query), {
                    "batch_id": batch_id,
                    "file_identifier": file_identifier,  # Truncated if needed
                    "original_filename": None,  # Null
                    "file_type": "csv",  # Set to 'csv' for CSV projects
                    "file_extension": "csv",  # Set to 'csv' for CSV projects
                    "storage_location": json.dumps(storage_location),  # Just row index
                    "file_size_bytes": len(str(file_data["cell_value"]).encode('utf-8')),
                    "file_hash": None,  # Not applicable for CSV cells
                    "sequence_order": file_data["sequence_order"],
                    "uploaded_at": datetime.now()
                })
                
                if result.scalar():
                    created_count += 1
            
            logger.info(f"Created {created_count} files_registry records for batch {batch_id}")
            return created_count
            
        except Exception as e:
            logger.error(f"Error creating files_registry records: {str(e)}")
            return 0
    
    async def _create_file_allocations_for_batch(
        self,
        project_db: AsyncSession,
        batch_id: int,
        files_data: List[Dict[str, Any]]
    ) -> int:
        """
        Create file_allocations records for a batch.
        
        Args:
            project_db: Project database session
            batch_id: The batch ID
            files_data: List of file data dictionaries
            
        Returns:
            Number of allocations created
        """
        try:
            # First, get the file IDs that were just created
            file_ids_query = """
            SELECT id, file_identifier 
            FROM files_registry 
            WHERE batch_id = :batch_id
            ORDER BY sequence_order
            """
            
            result = await project_db.execute(text(file_ids_query), {"batch_id": batch_id})
            file_records = result.fetchall()
            
            if not file_records:
                logger.warning(f"No file records found for batch {batch_id}")
                return 0
            
            # Create file_allocations for each file
            allocation_query = """
            INSERT INTO file_allocations (
                file_id,
                batch_id,
                allocation_sequence,
                workflow_phase,
                processing_status,
                processed_metadata,
                preprocessing_results,
                allocated_at,
                assignment_count,
                completion_count,
                allocation_rules
            ) VALUES (
                :file_id,
                :batch_id,
                :allocation_sequence,
                :workflow_phase,
                :processing_status,
                :processed_metadata,
                :preprocessing_results,
                :allocated_at,
                :assignment_count,
                :completion_count,
                :allocation_rules
            )
            """
            
            created_count = 0
            
            for file_record in file_records:
                file_id, file_identifier = file_record
                
                # Find corresponding file data by matching the file_identifier logic
                file_data = None
                for f in files_data:
                    # Recreate the same file_identifier logic used when creating the record
                    cell_value = str(f["cell_value"])
                    if len(cell_value) > 250:
                        expected_identifier = f"Row_{f['row_index']}_{cell_value[:200]}..."
                        if len(expected_identifier) > 255:
                            expected_identifier = f"Row_{f['row_index']}_LongText"
                    else:
                        expected_identifier = cell_value
                    
                    if expected_identifier == file_identifier:
                        file_data = f
                        break
                
                if not file_data:
                    logger.warning(f"Could not find file_data for file_identifier: {file_identifier}")
                    continue
                
                allocation_rules = {
                    "allocation_type": "csv_cell",
                    "requires_annotation": True,
                    "cell_metadata": {
                        "column_name": file_data["column_name"],
                        "row_index": file_data["row_index"],
                        "cell_value": file_data["cell_value"]
                    }
                }
                
                await project_db.execute(text(allocation_query), {
                    "file_id": file_id,
                    "batch_id": batch_id,
                    "allocation_sequence": 1,
                    "workflow_phase": "annotation",
                    "processing_status": "pending",
                    "processed_metadata": json.dumps({"csv_cell": True}),
                    "preprocessing_results": None,
                    "allocated_at": datetime.now(),
                    "assignment_count": 0,
                    "completion_count": 0,
                    "allocation_rules": json.dumps(allocation_rules)
                })
                
                created_count += 1
            
            logger.info(f"Created {created_count} file_allocations records for batch {batch_id}")
            return created_count
            
        except Exception as e:
            logger.error(f"Error creating file_allocations records: {str(e)}")
            return 0
    
    async def _get_project_info(self, project_code: str, master_db: AsyncSession) -> Dict[str, Any]:
        """
        Get project information including allocation strategy.
        
        Args:
            project_code: The project code
            master_db: Master database session
            
        Returns:
            Dict: Project information with num_annotators
        """
        try:
            result = await master_db.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project = result.scalar_one_or_none()
            
            if not project:
                raise ValueError(f"Project with code {project_code} not found in master DB")
            
            # Get allocation strategy info if available
            num_annotators = 1  # Default value
            
            if project.allocation_strategy_id:
                from post_db.master_models.allocation_strategies import AllocationStrategies
                strategy_result = await master_db.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                allocation_strategy = strategy_result.scalar_one_or_none()
                if allocation_strategy:
                    num_annotators = allocation_strategy.num_annotators
            
            return {
                "project_code": project.project_code,
                "project_name": project.project_name,
                "project_type": project.project_type,
                "num_annotators": num_annotators,
                "batch_size": project.batch_size or 10
            }
            
        except Exception as e:
            logger.error(f"Error getting project info for {project_code}: {str(e)}")
            return {
                "project_code": project_code,
                "num_annotators": 1,  # Default fallback
                "batch_size": 10
            }
