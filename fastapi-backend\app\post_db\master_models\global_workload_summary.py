from sqlalchemy import <PERSON>um<PERSON>, <PERSON>te<PERSON>, String, Boolean, DateTime, Numeric, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..base import Base


class GlobalWorkloadSummary(Base):
    """
    Real-time aggregated view of each user's workload across all projects.
    Used for intelligent task assignment, capacity planning, and preventing user overload.
    """
    __tablename__ = 'global_workload_summary'

    # Primary Identity
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, 
                    comment='Reference to user record for relationship integrity')
    username = Column(String(255), nullable=False, 
                     comment='Username for quick reference and display (denormalized for performance)')
    
    # Current Cross-Project Workload Status
    total_active_projects = Column(Integer, default=0, 
                                 comment='Number of projects user is currently working on (for project assignment limits)')
    total_active_batches = Column(Integer, default=0, 
                                comment='Number of annotation batches currently assigned across all projects')
    total_pending_files = Column(Integer, default=0, 
                               comment='Total files waiting for annotation across all user\'s assignments')
    
    # Capacity Management & Resource Allocation
    max_concurrent_projects = Column(Integer, default=3, 
                                   comment='Maximum projects this user can handle simultaneously (copied from users table for performance)')
    max_concurrent_batches = Column(Integer, default=5, 
                                  comment='Maximum batches this user can work on at once (copied from users table)')
    current_capacity_percentage = Column(Numeric(5, 2), default=0, 
                                       comment='Current workload as percentage of maximum capacity (0-100%)')
    
    # User Availability & Status
    is_available = Column(Boolean, default=True, 
                         comment='Whether user is available for new assignments')
    availability_reason = Column(String(255), 
                               comment='Reason for unavailability ("On vacation", "Training", "Medical leave", etc.)')
    timezone = Column(String(50), default='UTC', 
                     comment='User\'s timezone for scheduling and deadline management')
    
    # Data Freshness
    last_updated = Column(DateTime, default=func.current_timestamp(), 
                         comment='When this summary was last recalculated (for cache invalidation)')

    # Relationships
    user = relationship("users", backref="workload_summary", foreign_keys=[user_id])

    # Constraints
    __table_args__ = (
        UniqueConstraint('username', name='unique_username_workload'),
    )

    def __repr__(self):
        return f"<GlobalWorkloadSummary(user_id={self.user_id}, username='{self.username}', active_projects={self.total_active_projects})>" 