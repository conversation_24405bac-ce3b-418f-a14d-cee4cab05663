{"tests/integration/database/test_ai_processing_operations.py": true, "tests/integration/database/test_concurrent_operations.py": true, "tests/integration/database/test_connection_management.py": true, "tests/integration/database/test_cross_project_operations.py": true, "tests/integration/database/test_dependency_injection_database.py": true, "tests/integration/database/test_dynamic_schema_generation.py": true, "tests/integration/database/test_dynamic_schema_integration.py": true, "tests/integration/database/test_large_dataset_operations.py": true, "tests/integration/database/test_schema_migration_operations.py": true, "tests/integration/database/test_verifier_assignment_operations.py": true, "tests/services/integration/test_auth_service_integration.py": true, "tests/services/integration/test_project_batch_service_dynamic_integration.py": true, "tests/services/performance/test_csv_batch_service_performance.py": true, "tests/services/performance/test_storage_performance_comparison.py": true, "tests/services/unit/test_annotator_batch_assignment_service_unit.py": true, "tests/services/unit/test_hybrid_storage_scenarios_unit.py": true, "tests/services/unit/test_project_users_service_unit.py": true, "tests/services/unit/test_verifier_batch_assignment_service_unit.py": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_user_success": true, "tests/services/unit/test_csv_batch_service_unit.py::TestCSVBatchServiceUnit::test_validate_csv_structure_valid": true, "tests/integration/database/test_master_db_operations.py::TestMasterDatabaseOperations::test_client_creation_and_retrieval": true, "tests/integration/database/test_validation_rules.py::TestPasswordValidation::test_password_length_validation": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_user_username_exists": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_user_email_exists": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_user_weak_password": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_login_user_success": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_login_user_not_found": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_login_user_wrong_password": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_login_user_inactive_account": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_change_password_success": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_change_password_wrong_old_password": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_change_password_same_as_old": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_sql_injection_prevention_login": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_password_hash_never_returned": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_timing_attack_resistance": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_invalid_email_format": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_special_characters_in_username": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_handle_database_error_gracefully": true, "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_token_generation_failure_handling": true}