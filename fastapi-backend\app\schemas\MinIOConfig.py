# schemas.py
from pydantic import BaseModel, <PERSON>
from typing import Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class MinIOConfig(BaseModel):
    """Pydantic model for MinIO configuration"""
    endpoint: str = Field(default=os.environ.get('MINIO_ENDPOINT', 'localhost:9000'))  # MinIO server endpoint
    access_key: str = Field(default=os.environ.get('MINIO_ACCESS_KEY', 'minioadmin'))  # MinIO access key
    secret_key: str = Field(default=os.environ.get('MINIO_SECRET_KEY', 'minioadmin'))  # MinIO secret key
    bucket_name: str = Field(default=os.environ.get('MINIO_BUCKET_NAME', 'default-bucket'))  # Default bucket name
    secure: bool = Field(default=os.environ.get('MINIO_SECURE', 'false').lower())  # Use HTTPS (True) or HTTP (False)
    region: Optional[str] = Field(default=os.environ.get('MINIO_REGION', None))  # AWS region (optional)
    timeout: int = Field(default=int(os.environ.get('MINIO_TIMEOUT', '30')))  # Connection timeout in seconds
    max_retries: int = Field(default=int(os.environ.get('MINIO_MAX_RETRIES', '3')))  # Maximum number of retries
    retry_delay: int = Field(default=int(os.environ.get('MINIO_RETRY_DELAY', '1')))  # Delay between retries in seconds
    
    @classmethod
    def from_env(cls) -> 'MinIOConfig':
        """Create MinIOConfig from environment variables"""
        return cls()
    
    @classmethod
    def from_settings(cls, settings) -> 'MinIOConfig':
        """Create MinIOConfig from settings object"""
        return cls(
            endpoint=settings.minio_settings.endpoint,
            access_key=settings.minio_settings.access_key,
            secret_key=settings.minio_settings.secret_key,
            bucket_name=settings.minio_settings.bucket_name,
            secure=settings.minio_settings.secure,
            region=settings.minio_settings.region,
            timeout=settings.minio_settings.timeout,
            max_retries=settings.minio_settings.max_retries,
            retry_delay=settings.minio_settings.retry_delay
        )
    
    @classmethod
    def from_centralized_config(cls) -> 'MinIOConfig':
        """Create MinIOConfig from centralized config settings"""
        from core.config import get_settings
        settings = get_settings()
        return cls.from_settings(settings)