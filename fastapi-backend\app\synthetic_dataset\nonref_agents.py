"""
Synthetic Dataset Generator using Google's Gemini model with Agno Agent.
"""
import os
import logging
import json
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from agno.agent import Agent
from agno.models.google import Gemini

logger = logging.getLogger('nonref_agents')

class DatasetType(str, Enum):
    """Types of synthetic datasets that can be generated"""
    QA = "qa"
    ARTICLES = "articles"
    CONVERSATION = "conversation"
    CODE_SNIPPETS = "code_snippets"

class SyntheticDataRequest(BaseModel):
    """Request model for synthetic data generation"""
    query: str
    dataset_type: DatasetType
    num_samples: int = 5
    model: str = "gemini-2.0-flash"
    reference_text: Optional[str] = None

class SyntheticDataResponse(BaseModel):
    """Response model for synthetic data generation"""
    success: bool
    data: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

class GeminiGenerator:
    """Class to generate synthetic datasets using Google's Gemini model with Agno agent"""
    
    def __init__(self):
        """Initialize the Gemini generator with API key"""
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            logger.error("GEMINI_API_KEY not found in environment variables")
            raise ValueError("GEMINI_API_KEY is required")
        
        # Set the API key for the environment
        os.environ["GOOGLE_API_KEY"] = api_key
    
    def _get_agent(self, model_id: str):
        """Create and return an Agno agent with the specified model"""
        return Agent(model=Gemini(id=model_id), markdown=True)
    
    def _build_qa_prompt(self, query: str, num_samples: int) -> str:
        """Build a prompt for QA dataset generation"""
        return f"""
        Create {num_samples} high-quality question-answer pairs about the following topic: {query}
        
        For each question-answer pair:
        1. Make questions diverse, specific, and interesting
        2. Include factual questions, analytical questions, and thought-provoking questions
        3. Provide comprehensive, accurate answers with enough detail to be valuable
        4. Ensure answers are factually correct and educational
        
        Format your response as a valid JSON array, where each item is an object with "question" and "answer" fields.
        
        Example format:
        ```json
        [
            {{
                "question": "What is the main function of mitochondria in cells?",
                "answer": "Mitochondria are often referred to as the powerhouse of the cell because their primary function is to generate energy in the form of ATP (adenosine triphosphate) through cellular respiration. They convert glucose and oxygen into energy that the cell can use for various processes."
            }},
            ...
        ]
        ```
        
        Only respond with the JSON array and nothing else.
        """
    
    def _build_articles_prompt(self, query: str, num_samples: int) -> str:
        """Build a prompt for article dataset generation"""
        return f"""
        Create {num_samples} informative article snippets about the following topic: {query}
        
        For each article:
        1. Include a compelling title that accurately reflects the content
        2. Write a well-structured article of at least 300 words
        3. Use proper paragraph breaks and organization
        4. Include 5-7 relevant keywords for the article
        5. Use different writing styles and approaches across the articles
        
        Format your response as a valid JSON array, where each item is an object with "title", "content", and "keywords" fields.
        
        Example format:
        ```json
        [
            {{
                "title": "The Fascinating World of Marine Biology",
                "content": "Marine biology stands as one of the most diverse and dynamic fields in modern science. Spanning from microscopic plankton to massive blue whales, the study encompasses all organisms that live in saltwater environments...[rest of article content]",
                "keywords": ["marine biology", "ocean science", "marine ecosystems", "marine conservation", "oceanography", "marine species", "biodiversity"]
            }},
            ...
        ]
        ```
        
        Only respond with the JSON array and nothing else.
        """
    
    def _build_conversation_prompt(self, query: str, num_samples: int) -> str:
        """Build a prompt for conversation dataset generation"""
        return f"""
        Create {num_samples} realistic conversation examples about the following topic: {query}
        
        For each conversation:
        1. Include 2-3 participants with distinct speaking styles and perspectives
        2. Create a natural flow with at least 6-10 exchanges
        3. Include a scenario or context for the conversation
        4. Ensure the conversation feels authentic and shows personality
        
        Format your response as a valid JSON array, where each item is an object with "scenario", "participants", and "exchanges" fields.
        
        Example format:
        ```json
        [
            {{
                "scenario": "Two friends discussing plans for a weekend camping trip",
                "participants": ["Alex", "Jordan"],
                "exchanges": [
                    {{"speaker": "Alex", "text": "So, are we still on for camping this weekend?"}},
                    {{"speaker": "Jordan", "text": "Absolutely! I've been looking forward to getting away from the city."}},
                    ...
                ]
            }},
            ...
        ]
        ```
        
        Only respond with the JSON array and nothing else.
        """
    
    def _build_code_snippets_prompt(self, query: str, num_samples: int) -> str:
        """Build a prompt for code snippets dataset generation"""
        return f"""
        Create {num_samples} educational code snippets related to: {query}
        
        For each code snippet:
        1. Include a descriptive title of what the code accomplishes
        2. Specify the programming language
        3. Provide well-commented, clean code that demonstrates good practices
        4. Include a brief explanation of how the code works
        5. Add 3-5 key concepts demonstrated in the snippet
        
        Use various programming languages across the examples if appropriate (Python, JavaScript, Java, etc.).
        
        Format your response as a valid JSON array, where each item is an object with the fields "title", "language", "code", "explanation", and "concepts".
        
        Example format:
        ```json
        [
            {{
                "title": "Implementing Binary Search in Python",
                "language": "Python",
                "code": "def binary_search(arr, target):\\n    left = 0\\n    right = len(arr) - 1\\n    \\n    while left <= right:\\n        mid = (left + right) // 2\\n        \\n        # Check if target is present at mid\\n        if arr[mid] == target:\\n            return mid\\n        \\n        # If target is greater, ignore left half\\n        elif arr[mid] < target:\\n            left = mid + 1\\n        \\n        # If target is smaller, ignore right half\\n        else:\\n            right = mid - 1\\n    \\n    # Element not present\\n    return -1",
                "explanation": "This function implements the binary search algorithm to efficiently find an element in a sorted array. It repeatedly divides the search space in half, making it much faster than linear search for large datasets.",
                "concepts": ["binary search", "algorithms", "time complexity", "divide and conquer", "searching"]
            }},
            ...
        ]
        ```
        
        Only respond with the JSON array and nothing else.
        """
    
    def _get_prompt_for_dataset_type(self, request: SyntheticDataRequest) -> str:
        """Get the appropriate prompt based on dataset type"""
        if request.dataset_type == DatasetType.QA:
            return self._build_qa_prompt(request.query, request.num_samples)
        elif request.dataset_type == DatasetType.ARTICLES:
            return self._build_articles_prompt(request.query, request.num_samples)
        elif request.dataset_type == DatasetType.CONVERSATION:
            return self._build_conversation_prompt(request.query, request.num_samples)
        elif request.dataset_type == DatasetType.CODE_SNIPPETS:
            return self._build_code_snippets_prompt(request.query, request.num_samples)
        else:
            raise ValueError(f"Unsupported dataset type: {request.dataset_type}")
    
    async def generate_dataset(self, request: SyntheticDataRequest) -> SyntheticDataResponse:
        """Generate synthetic dataset based on the request using Agno agent"""
        try:
            # Map the model name to the corresponding Gemini model ID
            model_map = {
                "gemini-2.0-flash": "gemini-2.0-flash",
                "gemini-2.0-pro": "gemini-2.0-pro"
            }
            
            model_id = model_map.get(request.model, "gemini-2.0-flash")
            
            # Create an Agno agent with the specified model
            agent = self._get_agent(model_id)
            
            # Get the appropriate prompt for the dataset type
            prompt = self._get_prompt_for_dataset_type(request)
            
            # Run the agent to generate the dataset
            run_response = agent.run(prompt)
            
            # Parse the JSON response
            try:
                # Extract the JSON content from the response
                content = run_response.content
                
                # If the content contains markdown code blocks, extract the JSON from it
                if "```json" in content:
                    content = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].split("```")[0].strip()
                
                # Parse the JSON
                dataset = json.loads(content)
                
                return SyntheticDataResponse(success=True, data=dataset)
            except Exception as e:
                logger.error(f"Error parsing Agno agent response: {str(e)}")
                logger.error(f"Raw response: {run_response.content}")
                return SyntheticDataResponse(
                    success=False, 
                    error=f"Failed to parse model response: {str(e)}"
                )
                
        except Exception as e:
            logger.error(f"Error in generate_dataset: {str(e)}")
            return SyntheticDataResponse(
                success=False,
                error=f"Failed to generate dataset: {str(e)}"
            ) 