import { NextRequest, NextResponse } from "next/server";

import { API_BASE_URL } from "@/lib/api";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectCode: string }> }
) {
  try {
    const { projectCode } = await params;
    const body = await request.json();

    const response = await fetch(
      `${API_BASE_URL}/projects/by-code/${projectCode}/allocation-strategy`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.detail || "Failed to assign allocation strategy" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error assigning allocation strategy:", error);
    return NextResponse.json(
      { error: "Failed to assign allocation strategy" },
      { status: 500 }
    );
  }
}
