import { useState } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { StrategyDetails } from '../types';
import { API_BASE_URL } from "../../../../lib/api";
export const useStrategy = () => {
  const [strategyDetails, setStrategyDetails] = useState<StrategyDetails | null>(null);
  const [loadingStrategy, setLoadingStrategy] = useState(false);

  // Fetch strategy details for a project
  const fetchStrategyDetails = async (projectId: number) => {
    try {
      setLoadingStrategy(true);
      const response = await authFetch(`${API_BASE_URL}/projects/${projectId}/strategy-details`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Strategy details response:', data);
      
      if (data.success && data.has_strategy) {
        setStrategyDetails(data.strategy);
        console.log('Strategy details set:', data.strategy);
        return data.strategy;
      } else {
        setStrategyDetails(null);
        console.log('No strategy found:', data.message);
        showToast.warning(data.message || "Project does not have an allocation strategy");
        return null;
      }
    } catch (error) {
      console.error("Error fetching strategy details:", error);
      showToast.error("Failed to load strategy details");
      setStrategyDetails(null);
      return null;
    } finally {
      setLoadingStrategy(false);
    }
  };

  // Clear strategy details
  const clearStrategyDetails = () => {
    setStrategyDetails(null);
  };

  return {
    strategyDetails,
    loadingStrategy,
    fetchStrategyDetails,
    clearStrategyDetails,
  };
};
