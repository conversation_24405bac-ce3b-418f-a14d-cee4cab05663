"""
Routes for removing users from projects.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, delete
from typing import Dict, Any
import logging

from core.session_manager import get_project_db_session, get_master_db_session, get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users as Users
from post_db.master_models.user_project_access import UserProjectAccess
from dependencies.auth import get_current_active_user, require_admin

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/projects",
    tags=["User Removal"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)]
)

async def get_project_or_404(project_id: int, db: AsyncSession) -> ProjectsRegistry:
    """Helper to fetch project or raise 404 if not found."""
    project = await db.get(ProjectsRegistry, project_id)
    if not project:
        logger.warning(f"Project not found with ID: {project_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Project with ID {project_id} not found")
    return project

async def get_user_or_404(user_id: int, db: AsyncSession) -> Users:
    """Helper to fetch user or raise 404 if not found."""
    user = await db.get(Users, user_id)
    if not user:
        logger.warning(f"User not found with ID: {user_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"User with ID {user_id} not found")
    return user

async def remove_from_project_database(project: ProjectsRegistry, user_id: int) -> bool:
    """
    Remove user from project-specific database.
    
    Args:
        project: Project registry record
        user_id: User ID to remove
        
    Returns:
        bool: Success status
    """
    try:
        project_code = project.project_code
        
        # Connect to project database
        async with get_project_db_session(project_code) as session:
            from post_db.allocation_models.project_users import ProjectUsers
            from sqlalchemy import delete
            
            # Delete user from project database
            delete_query = delete(ProjectUsers).where(
                ProjectUsers.user_id == user_id
            )
            await session.execute(delete_query)
            
            # Commit the changes
            await session.commit()
            
            logger.info(f"Successfully removed user {user_id} from project database {project.database_name}")
            return True
    
    except Exception as e:
        logger.error(f"Error removing user from project database: {str(e)}")
        return False

@router.delete("/{project_id}/{role}/{user_id}", response_model=Dict[str, Any])
async def remove_user_from_project(
    project_id: int = Path(..., description="Project ID"),
    role: str = Path(..., description="User role (annotators, verifiers)"),
    user_id: int = Path(..., description="User ID to remove"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Remove a user from a project.
    
    Args:
        project_id: Project ID
        role: User role (annotators, verifiers)
        user_id: User ID to remove
        
    Returns:
        Dict: Result of user removal
    """
    try:
        # Validate role
        if role not in ["annotators", "verifiers"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid role: {role}. Must be one of: annotators, verifiers"
            )
        
        # Get project and user
        project = await get_project_or_404(project_id, db)
        user = await get_user_or_404(user_id, db)
        
        # Check if user is assigned to this project
        access_query = select(UserProjectAccess).where(
            and_(
                UserProjectAccess.user_id == user_id,
                UserProjectAccess.project_id == project_id
            )
        )
        access_result = await db.execute(access_query)
        access = access_result.scalar_one_or_none()
        
        if not access:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"User {user_id} is not assigned to project {project_id}"
            )
        
        # Delete user access record
        await db.delete(access)
        
        # Clear active_project field in user record
        user.active_project = None
        
        # Commit changes to master database
        await db.commit()
        
        # Remove from project database
        await remove_from_project_database(project, user_id)
        
        return {
            "success": True,
            "message": f"Successfully removed user {user.username} from project {project.project_name}",
            "user_id": user_id,
            "project_id": project_id,
            "role": role.rstrip("s")  # Convert "annotators" to "annotator", etc.
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing user from project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error removing user from project: {str(e)}"
        )
