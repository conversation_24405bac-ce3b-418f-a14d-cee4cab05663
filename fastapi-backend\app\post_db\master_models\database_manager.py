"""
Database Management System for Master Database Architecture
Provides utilities for creating project databases and managing cross-project queries.
"""

import logging
from typing import Dict, List, Optional, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from core.session_manager import get_master_db_context, get_session_manager

logger = logging.getLogger(__name__)


class ProjectDatabaseManager:
    """
    Service for dynamic schema creation and project database management.
    Automates the creation and configuration of project-specific databases 
    with customized schemas.
    """
    
    def __init__(self, template_db_name: str = "template_project_db"):
        """
        Initialize the database manager.
        Uses the unified session manager for all connections.
        
        Args:
            template_db_name: Name of the template database containing base schema
        """
        self.template_db_name = template_db_name
        self.session_manager = get_session_manager()
    
    def create_project_database(self, project_config: Dict[str, Any]) -> str:
        """
        Creates a new database for a project with custom schema tailored to client requirements.
        
        Args:
            project_config: Dictionary containing project specifications, client requirements, 
                          annotation schema, and workflow configuration
        
        Returns:
            str: Database name of the newly created project database
        """
        try:
            # 1. Generate unique database name following naming convention
            db_name = f"proj_{project_config['client_id']}_{project_config['project_code']}"
            
            # 2. Create empty database instance
            self._create_database(db_name)
            
            # 3. Apply standardized base schema (common tables for all projects)
            self._apply_base_schema(db_name)
            
            # 4. Create project-specific annotation tables based on media type and requirements
            self._create_custom_annotation_tables(db_name, project_config)
            
            # 5. Optimize database for project-specific workload patterns
            self._create_project_indexes(db_name, project_config)
            
            # 6. Register new project database in master coordination system
            self._register_project_in_master(project_config, db_name)
            
            logger.info(f"Successfully created project database: {db_name}")
            return db_name
            
        except Exception as e:
            logger.error(f"Failed to create project database: {str(e)}")
            raise
    
    async def _create_database(self, db_name: str) -> None:
        """
        Creates an empty database instance.
        
        Args:
            db_name: Name of the database to create
        """
        async with get_master_db_context() as session:
            # Note: Database creation requires admin privileges and direct connection
            # This is a limitation where we need direct access for DDL operations
            await session.execute(text("COMMIT"))
            
            # Create database
            create_sql = f"""
            CREATE DATABASE {db_name}
                WITH OWNER = postgres
                ENCODING = 'UTF8'
                LC_COLLATE = 'en_US.UTF-8'
                LC_CTYPE = 'en_US.UTF-8'
                TEMPLATE = {self.template_db_name};
            """
            await session.execute(text(create_sql))
            await session.execute(text("COMMIT"))
    
    async def _apply_base_schema(self, db_name: str, project_code: str) -> None:
        """
        Deploy project_metadata, files_registry, batches, assignments tables.
        
        Args:
            db_name: Target project database name
            project_code: Project code to get session for the database
        """
        # Use the project session for the newly created database
        async with self.session_manager.get_project_session(project_code) as session:
            # This would contain the base schema SQL for common project tables
            base_schema_sql = """
            -- Base schema for project databases
            -- This would include tables like project_metadata, files_registry, batches, assignments
            -- Implementation would depend on the specific base schema design
            """
            
            await session.execute(text(base_schema_sql))
            await session.commit()
    
    def _create_custom_annotation_tables(self, db_name: str, project_config: Dict[str, Any]) -> None:
        """
        Creates annotation tables customized for specific project requirements and media types.
        
        Args:
            db_name: Target project database name
            project_config: Project configuration including annotation schema and label definitions
        """
        annotation_schema = project_config.get('annotation_schema', {})
        project_type = project_config.get('project_type', 'image')
        
        if project_type == 'image':
            self._create_image_annotation_table(db_name, annotation_schema)
        elif project_type == 'text':
            self._create_text_annotation_table(db_name, annotation_schema)
        elif project_type == 'pdf':
            self._create_pdf_annotation_table(db_name, annotation_schema)
        # Additional media types: video, audio, etc.
    
    async def _create_image_annotation_table(self, project_code: str, annotation_schema: Dict[str, Any]) -> None:
        """
        Creates image-specific annotation tables.
        
        Args:
            project_code: Project code to get session for
            annotation_schema: Image annotation schema definition
        """
        async with self.session_manager.get_project_session(project_code) as session:
            # Create image annotation table based on schema
            image_annotation_sql = f"""
            CREATE TABLE annotations_image (
                id SERIAL PRIMARY KEY,
                file_id INTEGER NOT NULL,
                annotator_id INTEGER NOT NULL,
                annotation_data JSONB NOT NULL,
                confidence_score DECIMAL(5,2),
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            await session.execute(text(image_annotation_sql))
            await session.commit()
    
    async def _create_text_annotation_table(self, project_code: str, annotation_schema: Dict[str, Any]) -> None:
        """
        Creates text-specific annotation tables.
        
        Args:
            project_code: Project code to get session for
            annotation_schema: Text annotation schema definition
        """
        async with self.session_manager.get_project_session(project_code) as session:
            # Create text annotation table based on schema
            text_annotation_sql = f"""
            CREATE TABLE annotations_text (
                id SERIAL PRIMARY KEY,
                file_id INTEGER NOT NULL,
                annotator_id INTEGER NOT NULL,
                annotation_data JSONB NOT NULL,
                confidence_score DECIMAL(5,2),
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            await session.execute(text(text_annotation_sql))
            await session.commit()
    
    async def _create_pdf_annotation_table(self, project_code: str, annotation_schema: Dict[str, Any]) -> None:
        """
        Creates PDF-specific annotation tables.
        
        Args:
            project_code: Project code to get session for
            annotation_schema: PDF annotation schema definition
        """
        async with self.session_manager.get_project_session(project_code) as session:
            # Create PDF annotation table based on schema
            pdf_annotation_sql = f"""
            CREATE TABLE annotations_pdf (
                id SERIAL PRIMARY KEY,
                file_id INTEGER NOT NULL,
                page_number INTEGER NOT NULL,
                annotator_id INTEGER NOT NULL,
                annotation_data JSONB NOT NULL,
                confidence_score DECIMAL(5,2),
                status VARCHAR(50) DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            await session.execute(text(pdf_annotation_sql))
            await session.commit()
    
    async def _create_project_indexes(self, project_code: str, project_config: Dict[str, Any]) -> None:
        """
        Creates indexes for expected query patterns.
        
        Args:
            project_code: Project code to get session for
            project_config: Project configuration for workload optimization
        """
        async with self.session_manager.get_project_session(project_code) as session:
            # Create common indexes for performance
            indexes_sql = """
            -- Common indexes for project databases
            CREATE INDEX idx_files_status ON files_registry(status);
            CREATE INDEX idx_annotations_annotator ON annotations_image(annotator_id);
            CREATE INDEX idx_annotations_status ON annotations_image(status);
            CREATE INDEX idx_batches_status ON batches(status);
            """
            
            await session.execute(text(indexes_sql))
            await session.commit()
    
    def _register_project_in_master(self, project_config: Dict[str, Any], db_name: str) -> None:
        """
        Register new project database in master coordination system.
        
        Args:
            project_config: Project configuration
            db_name: Database name to register
        """
        # This would update the projects_registry table in the master database
        # Implementation would depend on the specific registration requirements
        pass


class CrossProjectQueryService:
    """
    Service for executing queries across multiple project databases and aggregating results.
    Manages connection pools to all project databases and provides unified data access.
    """
    
    def __init__(self):
        """
        Initialize the cross-project query service.
        Uses the unified session manager for all connections.
        """
        self.session_manager = get_session_manager()
    
    async def get_user_workload_across_projects(self, username: str) -> Dict[str, int]:
        """
        Aggregates user's workload across all projects they have access to.
        
        Args:
            username: Username to get workload summary for
            
        Returns:
            dict: Aggregated workload metrics across all projects
        """
        try:
            # Get list of projects user has access to
            projects = await self._get_user_projects(username)
            
            total_workload = {
                'active_batches': 0,
                'pending_files': 0,
                'in_progress_files': 0
            }
            
            for project in projects:
                workload = await self._get_user_workload_in_project(project['project_code'], username)
                
                # Aggregate workload metrics across all projects
                total_workload['active_batches'] += workload.get('active_batches', 0)
                total_workload['pending_files'] += workload.get('pending_files', 0)
                total_workload['in_progress_files'] += workload.get('in_progress_files', 0)
            
            return total_workload
            
        except Exception as e:
            logger.error(f"Failed to get user workload across projects: {str(e)}")
            raise
    
    async def _get_user_projects(self, username: str) -> List[Dict[str, Any]]:
        """
        Get list of projects user has access to from master database.
        
        Args:
            username: Username to get projects for
            
        Returns:
            List of project dictionaries
        """
        async with get_master_db_context() as session:
            query = """
            SELECT p.database_name, p.project_name, p.project_code, upa.project_role
            FROM projects_registry p
            JOIN user_project_access upa ON p.id = upa.project_id
            JOIN users u ON upa.user_id = u.id
            WHERE u.username = :username AND upa.is_active = true
            """
            result = await session.execute(text(query), {"username": username})
            return [dict(row._mapping) for row in result]
    
    # No longer needed - using session manager directly
    
    async def _get_user_workload_in_project(self, project_code: str, username: str) -> Dict[str, int]:
        """
        Get user's workload in a specific project.
        
        Args:
            project_code: Project code to get session for
            username: Username to get workload for
            
        Returns:
            Dictionary with workload metrics
        """
        async with self.session_manager.get_project_session(project_code) as session:
            query = """
            SELECT 
                COUNT(DISTINCT b.id) as active_batches,
                COUNT(f.id) as pending_files,
                COUNT(CASE WHEN f.processing_status = 'in_progress' THEN 1 END) as in_progress_files
            FROM allocation_batches b
            LEFT JOIN files_registry f ON b.id = f.batch_id
            WHERE b.batch_status = 'active'
            """
            result = await session.execute(text(query), {"username": username})
            row = result.first()
            
            return {
                'active_batches': row.active_batches if row else 0,
                'pending_files': row.pending_files if row else 0,
                'in_progress_files': row.in_progress_files if row else 0
            } 