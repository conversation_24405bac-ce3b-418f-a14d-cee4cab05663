from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
from datetime import datetime
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from post_db.master_models.projects_registry import ProjectsRegistry
from core.session_manager import get_master_db_session
from ..model_endpoints.audio_transcription import (
    get_audio_transcription_service,
    AudioTranscriptionService
)
from services.ai_processing_service import get_ai_processing_service, AIProcessingService

router = APIRouter()
# Create router instance

@router.post("/batch/project", response_model=Dict[str, Any])
async def batch_transcribe_project_folder(
    project_code: str = Form(...),
    model_name: str = Form(...),
    custom_prompt: Optional[str] = Form(None),
    ai_service: AIProcessingService = Depends(get_ai_processing_service),
    service: AudioTranscriptionService = Depends(get_audio_transcription_service),
    db: AsyncSession = Depends(get_master_db_session)
):
    try:
        stmt = select(ProjectsRegistry).where(
            ProjectsRegistry.project_code == project_code,
            ProjectsRegistry.folder_path.isnot(None),
            ProjectsRegistry.project_status == 'active'
        )
        result = await db.execute(stmt)
        project = result.scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project '{project_code}' not found or no folder path available"
            )

        file_identifiers = []

        if model_name and model_name.startswith("AI-Processing/"):
            model_name = model_name[len("AI-Processing/"):]

        ai_result = await ai_service.process_files_for_project(
            project_code=project_code,
            file_identifiers=file_identifiers,
            model_name=model_name,
            processing_type="transcription",
            user_prompt=custom_prompt,
            system_prompt="Transcribe the audio file accurately."
        )

        ai_result["project_name"] = project.project_name
        ai_result["folder_path"] = project.folder_path

        return ai_result

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error processing project folder: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing project folder: {str(e)}"
        )