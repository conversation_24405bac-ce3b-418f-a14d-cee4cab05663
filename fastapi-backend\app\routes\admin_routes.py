from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, File, Form, UploadFile #type:ignore
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func, update
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.clients import Clients
from dependencies.auth import get_current_active_user, require_admin
from core.session_manager import get_master_db_session
from post_db.master_models.admin_settings import AdminSettings
from typing import Optional
from schemas.UserSchemas import SuccessResponse, ErrorResponse
from schemas.AdminDatasetSchemas import RegisterDatasetRequest
from core.config import get_settings, ITEMS_PER_PAGE
from schemas.AdminSettingsSchemas import (
    SelectAnnotationFolderRequest,
    SelectVerificationFoldersRequest,
    SelectDatasetRequest,
    MergeDatasetRequest
)
from core.session_manager import get_project_db_session, get_master_db_session
from services.project_batch_service import ProjectBatchService
from utils.project_provisioning import provision_project_database  
from core.session_manager import get_master_db_context
from core.project_context import ProjectContext
from core.nas_connector import get_ftp_connector_from_credentials, count_files_in_directory
import os
import json
import logging
import math
from cache.admin_cache import invalidate_stats_cache
from urllib.parse import unquote
from googleapiclient.http import MediaIoBaseUpload #type:ignore
from io import BytesIO
from services.project_batch_service import ProjectBatchService

logger = logging.getLogger('admin_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}})


#................................. Dashboard .................................
@router.get("/dashboard", response_model=SuccessResponse)
async def dashboard(request: Request):
    """
    Main admin dashboard endpoint.
    Returns configuration and status data for the admin dashboard.
    """
    try:
        settings = get_settings()
        nas_status = settings.nas_settings.is_connected
        return SuccessResponse(
            success=True,
            message="Dashboard data retrieved successfully",
        )

    except Exception as e:
        logger.error(f"Error in dashboard route: {str(e)}")
        logger.exception("Detailed error:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dashboard data"
        )


@router.get("/browse-nas-directory", response_model=SuccessResponse)
async def browse_nas_directory(
    path: str = Query("/", description="Directory path to browse"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Browse a directory on the NAS and return its contents.
    """
    # Get current project for NAS credentials using ProjectContext
    from core.project_context import ProjectContext
    from core.nas_connector import get_ftp_connector_from_credentials

    # Get current project (most recent active project as fallback)
    current_project = await ProjectContext.get_current_project(db)
    
    if not current_project:
        logger.warning("No project found for NAS browsing")
        # Try to find any project with NAS credentials as fallback
        from post_db.master_models.projects_registry import ProjectsRegistry
        from sqlalchemy import select, desc

        result = await db.execute(
            select(ProjectsRegistry)
            .where(ProjectsRegistry.credentials.isnot(None))
            .order_by(desc(ProjectsRegistry.created_at))
            .limit(1)
        )
        current_project = result.scalar_one_or_none()

        if not current_project:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No project with NAS credentials found. Please connect to NAS first."
            )

    # Get NAS credentials for this project
    project_credentials = await ProjectContext.get_project_credentials(db, current_project)
    
    if not project_credentials:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No NAS credentials found for current project. Please connect to NAS first."
        )

    # Create connector with project-specific credentials
    connector = await get_ftp_connector_from_credentials(project_credentials)
    if not connector:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to connect to NAS with stored credentials."
        )
    items = await connector.list_directory(path)
    if not items:
        return SuccessResponse(success=True, message="Directory is empty", data={"path": path, "items": [], "count": 0})
    if isinstance(items, dict) and "data" in items and "files" in items["data"]:
        items_list = items["data"]["files"]
    elif isinstance(items, list):
        items_list = items
    else:
        items_list = []
        logger.warning(f"Unexpected format received from list_directory for {path}: {items}")
    parent_path = os.path.dirname(path) if path != "/" else "/"
    return SuccessResponse(success=True, message="Directory contents", data={"path": path, 
     "parent_path": parent_path, "items": items_list, "count": len(items_list)})

@router.post("/select-annotation-folder", response_model=SuccessResponse)
async def select_annotation_folder(req: SelectAnnotationFolderRequest, db: AsyncSession = Depends(get_master_db_session)):
    """
    Select a folder for annotation and update the existing project with its path, batch size, and file count.
    The project must already exist - this endpoint only updates existing projects.
    """
    folder_path = req.folder_path
    project_code = req.project_code

    if not folder_path:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No folder path provided")
    if not project_code:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No project code provided")

    # Get the specific project to update
  

    # Get the project by project_code
    current_project = await ProjectContext.get_current_project(db, project_code=project_code)
    if not current_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with code '{project_code}' not found. Please create the project first."
        )
    
    # Check if we need to update the client_id based on the request
    # If req has client_id, update the project to use the correct client
    if hasattr(req, 'client_id') and req.client_id and current_project.client_id != req.client_id:
        logger.info(f"Updating project {project_code} from client_id {current_project.client_id} to {req.client_id}")
        current_project.client_id = req.client_id
        
        # Also update the project code to reflect the new client_id
        old_project_code = project_code
        project_name_part = project_code.split('_')[1]  # Extract project name part
        new_project_code = f"PROJ_{project_name_part}_{req.client_id:04d}"
        
        # Update the project code
        current_project.project_code = new_project_code
        project_code = new_project_code  # Update the variable for the rest of the function
        
        logger.info(f"Updated project code from {old_project_code} to {new_project_code}")
        
        # Update database name to match new client
        new_database_name = f"proj_{project_name_part.lower()}_db"
        current_project.database_name = new_database_name
        logger.info(f"Updated database name to: {new_database_name}")
        
        # Commit the client_id and project_code changes
        await db.commit()

    # Get NAS credentials and count files in the selected folder
    logger.info(f"Project found: {current_project.project_code} - counting files in folder: {folder_path}")

    # Use existing project type or default to 'image'
    content_type = current_project.project_type or 'image'

    # Count files in the selected folder
    total_files = 0
    if current_project.credentials:
        try:
            if current_project.connection_type == "MinIO":
                from core.minio_utils import get_project_minio_connector
                connector = await get_project_minio_connector(db, current_project)
                if connector:
                    total_files = await connector.count_files_in_directory(folder_path)
                    logger.info(f"MinIO: counted {total_files} files in {folder_path}")
            else:
                # Default to NAS-FTP
                connector = await get_ftp_connector_from_credentials(current_project.credentials)
                if connector:
                    total_files = await count_files_in_directory(connector, folder_path)
                    logger.info(f"NAS: counted {total_files} files in {folder_path}")
        except Exception as e:
            logger.error(f"Error counting files: {e}")
            total_files = 0

    # Calculate total batches based on files and batch size
    total_batches = 0
    if req.files_per_batch and req.files_per_batch > 0 and total_files > 0:
        total_batches = (total_files + req.files_per_batch - 1) // req.files_per_batch  # Ceiling division
        logger.info(f"Calculated {total_batches} batches for {total_files} files with batch size {req.files_per_batch}")

    # Update the existing project
    try:
        async with get_master_db_context() as master_session:
            logger.info(f"Updating existing project {project_code}:")
            logger.info(f"  - old folder_path: {current_project.folder_path}")
            logger.info(f"  - new folder_path: {folder_path}")
            logger.info(f"  - old total_files: {current_project.total_files}")
            logger.info(f"  - new total_files: {total_files}")
            logger.info(f"  - old batch_size: {current_project.batch_size}")
            logger.info(f"  - new batch_size: {req.files_per_batch}")
            logger.info(f"  - old total_batches: {current_project.total_batches}")
            logger.info(f"  - new total_batches: {total_batches}")

            # Update existing project with new folder path, batch size, total files, and total batches
            await master_session.execute(
                update(ProjectsRegistry)
                .where(ProjectsRegistry.project_code == project_code)
                .values(
                    folder_path=folder_path,
                    batch_size=req.files_per_batch,
                    total_files=total_files,
                    total_batches=total_batches
                )
            )
            await master_session.commit()

            # Verify the update
            updated_project_result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            updated_project = updated_project_result.scalar_one_or_none()

            if updated_project:
                logger.info(f"Verified updated project data: folder_path={updated_project.folder_path}, total_files={updated_project.total_files}, batch_size={updated_project.batch_size}, total_batches={updated_project.total_batches}")
            else:
                logger.error("Failed to verify updated project data!")

    except Exception as e:
        logger.error(f"Error updating project {project_code}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update project: {str(e)}"
        )

    return SuccessResponse(
        success=True,
        message=f"{content_type} annotation folder set to: {folder_path}. Project {project_code} updated successfully.",
        data={
            "folder_path": folder_path,
            "folder_name": os.path.basename(folder_path),
            "content_type": content_type,
            "total_files": total_files,
            "batch_size": req.files_per_batch,
            "total_batches": total_batches,
            "project_code": project_code
        }
    )

@router.post("/select-verification-folders", response_model=SuccessResponse)
async def select_verification_folders(req: SelectVerificationFoldersRequest, db: AsyncSession = Depends(get_master_db_session)):
    """
    Select folders for verification mode via browser.
    """
    image_folder = req.image_folder.replace("\\", "/").replace("//", "/")
    if not image_folder.startswith("/"):
        image_folder = "/" + image_folder
    label_file = req.label_file.replace("\\", "/").replace("//", "/")
    if not label_file.startswith("/"):
        label_file = "/" + label_file

    # Get current project for NAS credentials using ProjectContext
    from core.project_context import ProjectContext
    from core.nas_connector import get_ftp_connector_from_credentials

    # Get current project (most recent active project as fallback)
    current_project = await ProjectContext.get_current_project(db)
    if not current_project:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No active project found. Please create or select a project first.")

    # Get NAS credentials for this project
    project_credentials = await ProjectContext.get_project_credentials(db, current_project)
    if not project_credentials:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No NAS credentials found for current project.")

    connector = await get_ftp_connector_from_credentials(project_credentials)
    if not connector:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to connect to FTP storage with stored credentials")
    try:
        items = await connector.list_directory(image_folder)
        if not items:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Image folder not found or empty: {image_folder}")
        content = await connector.get_file_content(label_file)
        if not content:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Label file not found or empty: {label_file}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating files: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error validating files: {str(e)}")
    
    if isinstance(content, bytes):
        encodings = ["utf-8", "utf-8-sig", "latin1"]
        decoded = None
        last_error = None
        for encoding in encodings:
            try:
                decoded = content.decode(encoding)
                logger.info(f"Successfully decoded file using {encoding} encoding")
                break
            except UnicodeDecodeError as e:
                last_error = e
        if decoded is None:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Failed to decode with any encoding: {str(last_error)}")
        content = decoded
    if not content.strip():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Label file is empty: {label_file}")
    try:
        parsed_json = json.loads(content)
    except json.JSONDecodeError as e:
        logger.error(f"JSON parse error for {label_file}: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid JSON format: {str(e)}")
    if not isinstance(parsed_json, dict):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="JSON file must contain a dictionary mapping filenames to labels")
    if not parsed_json:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="JSON file contains an empty dictionary")
    logger.info(f"Successfully validated JSON file: {label_file} via FTP ({len(parsed_json)} entries)")

    batch_manager = ProjectBatchService()
    # Use current project's client_id
    client_id_int = current_project.client_id
    # Convert integer client_id to string format for batch_manager
    client_id_str = str(client_id_int)

    success, message, batch_count = await batch_manager.create_batches_from_folder(image_folder, mode="verification", label_file=label_file, client_id=client_id_str)
    if not success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Folders set but failed to create batches: {message}")

    # Get total files count for projects_registry
    from utils.media_utils import get_media_from_folder, detect_folder_content_type
    content_type = await detect_folder_content_type(image_folder)
    all_media, _ = await get_media_from_folder(
        image_folder, content_type=content_type, page=1, items_per_page=100000, recursive=True)
    total_files = len(all_media) if all_media else 0

    # Note: Project creation is now handled through admin_client_routes.py
    logger.info(f"Verification folders set successfully. Project creation should be done through client registration.")

    return SuccessResponse(success=True, message=f"Verification folders set successfully. {message}",
                            data={"image_folder": image_folder, "image_folder_name": os.path.basename(image_folder),
                                   "label_file": label_file, "label_file_name": os.path.basename(label_file),
                                   "batch_count": batch_count, "projects_registry_created": False})

@router.post("/select-dataset", response_model=SuccessResponse)
async def select_dataset(req: SelectDatasetRequest, db: AsyncSession = Depends(get_master_db_session)):
    """
    Select an allocation_batches project for annotation or verification.
    Only handles new allocation_batches workflow.
    """
    if req.mode not in ["annotation", "verification"]:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid mode. Must be 'annotation' or 'verification'")
    
    project_code = req.dataset_id
    
    # Verify project exists in projects_registry
    try:
        async with get_master_db_context() as master_session:
            result = await master_session.execute(
                select(ProjectsRegistry).where(
                    ProjectsRegistry.project_code == project_code,
                    ProjectsRegistry.project_status == 'active'
                )
            )
            project = result.scalar_one_or_none()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, 
                    detail=f"Active project not found with code: {project_code}"
                )
    except Exception as e:
        logger.error(f"Error verifying project {project_code}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to verify project: {str(e)}"
        )
    
    # Get or create AdminSettings record
    result = await db.execute(select(AdminSettings).limit(1))
    settings_obj = result.scalar_one_or_none()
    if not settings_obj:
        settings_obj = AdminSettings()
        db.add(settings_obj)
        await db.flush()
    
    # Update active project fields
    if req.mode == "annotation":
        settings_obj.active_annotation_project_code = project_code
        settings_obj.annotation_dataset_name = project.project_name  # Keep for compatibility
    else:
        settings_obj.active_verification_project_code = project_code
        settings_obj.verification_dataset_name = project.project_name  # Keep for compatibility
    
    await db.commit()
    await invalidate_stats_cache("ocr_directory")
    
    return SuccessResponse(
        success=True, 
        message=f"Successfully selected {req.mode} project: {project.project_name} ({project_code})"
    )


@router.get("/get-datasets", response_model=SuccessResponse)
async def get_datasets(
    project_code: Optional[str] = Query(None, description="Specific project code to fetch"),
    client_id: Optional[str] = Query(None, description="4-digit client filter"),
    current_user: dict = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get available projects from projects_registry filtered by current user's client.
    """
    try:
        datasets_list = []

        # Get current user's client
        from core.project_context import ProjectContext
        user_client = await ProjectContext.get_user_client(db, current_user.get("sub"))

        if not user_client:
            return SuccessResponse(
                success=True,
                message="No client associated with current user",
                data={"datasets": [], "count": 0}
            )

        # Get all active projects from projects_registry for the current user's client
        async with get_master_db_context() as master_session:
            # Load projects with their client information using a join
            stmt = select(
                ProjectsRegistry, Clients
            ).join(
                Clients, ProjectsRegistry.client_id == Clients.id
            ).where(
                ProjectsRegistry.project_status == 'active',
                ProjectsRegistry.client_id == user_client.id  # Filter by current user's client
            )

            # If project_code is provided, filter by it
            if project_code:
                stmt = stmt.where(ProjectsRegistry.project_code == project_code)

            # If client_id is provided, filter by it (for admin purposes)
            if client_id:
                # client_id should be an integer matching Clients.id
                if client_id.isdigit():
                    stmt = stmt.where(ProjectsRegistry.client_id == int(client_id))

            # Order by creation date (newest first) to prioritize recently created projects
            stmt = stmt.order_by(ProjectsRegistry.created_at.desc())
            result = await master_session.execute(stmt)
            projects_with_clients = result.tuples().all()

            for project, client in projects_with_clients:
                # Skip temporary projects and default projects
                if (project.project_status == 'temporary' or
                    'DEFAULT' in project.project_name.upper() or
                    'TEMP' in project.project_name.upper()):
                    continue

                # Display as "project_name (project_code)"
                display_name = f"{project.project_name} ({project.project_code})"
                entry = {
                    "id": project.project_code,  # Use project_code as string for mapping
                    "name": display_name,
                    "project_name": project.project_name,
                    "project_code": project.project_code,
                    "project_type": project.project_type,
                    "folder_path": project.folder_path,
                    "client_id": client.id,
                    "client_name": client.name,
                    "total_files": project.total_files,
                    "total_batches": project.total_batches,
                    "completed_files": project.completed_files,
                    "batch_size": project.batch_size or 10,
                    "progress_percentage": round((project.completed_files / project.total_files) * 100) if project.total_files > 0 else 0,
                    "instructions": project.instructions or "",
                }
                datasets_list.append(entry)
                
        # Return different messages based on filter criteria
        if project_code:
            message = f"Project details for {project_code}"
            if not datasets_list:
                message = f"No project found with code {project_code}"
        elif client_id:
            message = f"Found {len(datasets_list)} projects for client {client_id}"
        else:
            message = f"Found {len(datasets_list)} projects"
            
        return SuccessResponse(
            success=True,
            message=message,
            data={
                "datasets": datasets_list,
                "count": len(datasets_list),
                "project_code": project_code if project_code else None
            }
        )
    except Exception as e:
        logger.error(f"Error getting datasets from projects_registry: {e}")
        raise HTTPException(status_code=500, detail="Failed to get datasets")

@router.get("/browser/{folder:path}", response_model=SuccessResponse)
async def browser(
    folder: str,
    page: int = Query(1, ge=1, description="Page number"),
):
    """
    Admin file browser API endpoint.
    Automatically detects and lists either images or videos (not both).
    """
    selected_folder = unquote(folder)

    from utils.media_utils import detect_folder_content_type, get_media_from_folder
    content_type = await detect_folder_content_type(selected_folder)
    
    if content_type == "empty":
        return SuccessResponse(
            success=True, 
            message="Directory is empty", 
            data={
                "images": [],
                "folder": selected_folder,
                "page": page,
                "total_pages": 0,
                "total_images": 0,
                "parent_folder": os.path.dirname(selected_folder.rstrip("/")) or "",
                "media_type": "empty"
            }
        )
    elif content_type == "unknown":
        raise HTTPException(status_code=400, detail="No supported media files found in folder")
    elif content_type == "mixed":
        raise HTTPException(status_code=400, detail="Folder contains multiple media types (images, videos, audio, text, PDF). Please organize them into separate folders.")

    # Get media files using the generic function
    media, total = await get_media_from_folder(selected_folder, content_type=content_type, page=page)

    total_pages = math.ceil(total / ITEMS_PER_PAGE) if ITEMS_PER_PAGE > 0 else 0

    parent_folder = os.path.dirname(selected_folder.rstrip("/"))
    if parent_folder == selected_folder or not parent_folder:
        parent_folder = ""

    data = {
        "images": media,  # Use 'images' key for both images and videos for frontend compatibility
        "folder": selected_folder,
        "page": page,
        "total_pages": total_pages,
        "total_images": total,  # Use 'total_images' for both for frontend compatibility
        "parent_folder": parent_folder,
        "media_type": content_type  # Add media type for frontend to handle appropriately
    }
    return SuccessResponse(success=True, message="Directory listing", data=data)

@router.post("/projects/{project_code}/start-processing", response_model=SuccessResponse)
async def start_processing(project_code: str):
    """Create project DB if needed and enqueue dataset batching."""
    
    try:
        # Ensure DB exists (idempotent)
        await provision_project_database(project_code)
        # Kick off batching (could be Celery task)
        batch_manager = ProjectBatchService()
        await batch_manager.enqueue_initial_batches()
        return SuccessResponse(success=True, message=f"Project {project_code} processing started")
    except Exception as e:
        logger.error(f"Failed to start processing for {project_code}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/projects/register-dataset", response_model=SuccessResponse)
async def register_dataset(req: RegisterDatasetRequest):
    """Dataset registration is now handled through admin_client_routes.py - this endpoint is deprecated"""
    logger.warning("Dataset registration should be done through admin_client_routes.py register_client endpoint")
    return SuccessResponse(
        success=False,
        message="Dataset registration is now handled through client registration. Please use the client onboarding flow.",
        data={"deprecated": True}
    )

@router.post("/edit-project-instructions", response_model=SuccessResponse)
async def edit_project_instructions(req: dict, db: AsyncSession = Depends(get_master_db_session)):
    """
    Update instructions for a project in the projects_registry.
    """
    if 'project_code' not in req or 'instructions' not in req:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, 
                            detail="Missing required fields: project_code, instructions")
    
    project_code = req['project_code']
    instructions = req['instructions']
    
    try:
        async with get_master_db_context() as master_session:
            # Check if project exists
            project_result = await master_session.execute(
                select(ProjectsRegistry).where(
                    ProjectsRegistry.project_code == project_code
                )
            )
            project = project_result.scalar_one_or_none()
            
            if not project:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Project with code {project_code} not found"
                )
                
            # Update instructions
            await master_session.execute(
                update(ProjectsRegistry)
                .where(ProjectsRegistry.project_code == project_code)
                .values(instructions=instructions)
            )
            await master_session.commit()
            
            return SuccessResponse(
                success=True,
                message=f"Instructions for project {project_code} updated successfully",
                data={"project_code": project_code}
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating project instructions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project instructions"
        )
