"""
Comprehensive tests for hybrid storage scenarios - projects using BOTH MinIO AND NAS storage.
Tests storage routing, failover, migration, and load balancing between storage types.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List, Optional
import asyncio
import time
from datetime import datetime

from app.services.media_streaming_service import MediaStreamingService
from app.services.project_batch_service import ProjectBatchService
from app.services.ai_processing_service import AIProcessingService

class TestHybridStorageScenarios:
    """Tests for projects using both MinIO and NAS storage simultaneously."""
    
    @pytest.fixture
    def hybrid_project_config(self):
        """Mock hybrid project configuration."""
        return {
            'project_code': 'HYBRID_001',
            'primary_storage': 'MinIO',
            'fallback_storage': 'NAS-FTP',
            'storage_routing': {
                'video': 'MinIO',      # Large files to MinIO
                'audio': 'MinIO',      # Large files to MinIO  
                'image': 'NAS-FTP',    # Small files to NAS
                'document': 'NAS-FTP', # Small files to NAS
                'archive': 'NAS-FTP'   # Archives to NAS
            }
            'file_size_threshold': 50 * 1024 * 1024,  # 50MB threshold
            'cost_optimization': True,
            'redundancy_enabled': True
        }
        
        test_files = [
            {'path': '/audio/music.wav', 'type': 'audio', 'expected_storage': 'MinIO'},
            {'path': '/images/photo.jpg', 'type': 'image', 'expected_storage': 'NAS-FTP'},
            {'path': '/docs/report.pdf', 'type': 'document', 'expected_storage': 'NAS-FTP'},
        ]
        
        for file_info in test_files:
            with patch.object(service, '_determine_optimal_storage') as mock_determine:
                mock_determine.return_value = file_info['expected_storage']
                
                with patch.object(service, '_get_connector_for_storage') as mock_get_connector:
                    expected_connector = mock_dual_connectors['minio'] if file_info['expected_storage'] == 'MinIO' else mock_dual_connectors['nas']
                    mock_get_connector.return_value = expected_connector
                    
                    # Test storage routing
                    storage_type = service._determine_optimal_storage(
                        file_info['path'], 
                        file_info['type'],
                        hybrid_project_config
                    )
                    
                    assert storage_type == file_info['expected_storage']
                    
                    # Test connector retrieval
                    connector = service._get_connector_for_storage(
                        storage_type, 
                        hybrid_project_config['project_code']
                    )
                    
                    assert connector.connection_type == file_info['expected_storage']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_failover_minio_to_nas(self, hybrid_project_config, mock_dual_connectors):
        """Test failover from MinIO to NAS when MinIO is unavailable."""
        service = MediaStreamingService()
        
        # Simulate MinIO failure
        mock_dual_connectors['minio'].is_available.return_value = False
        mock_dual_connectors['minio'].get_presigned_url.side_effect = Exception("MinIO service unavailable")
        
        video_path = '/videos/important.mp4'
        project_code = hybrid_project_config['project_code']
        
        with patch.object(service, '_get_project_storage_config') as mock_get_config:
            mock_get_config.return_value = hybrid_project_config
            
            with patch.object(service, '_attempt_minio_streaming') as mock_minio:
                mock_minio.side_effect = Exception("MinIO unavailable")
                
                with patch.object(service, '_fallback_to_nas_streaming') as mock_nas_fallback:
                    mock_nas_fallback.return_value = {
                        'streaming_url': 'https://nas.test.com/videos/important.mp4',
                        'storage_type': 'NAS-FTP',
                        'fallback_used': True,
                        'original_storage': 'MinIO'
                    }
                    
                    result = await service.get_streaming_url(video_path, 'video', project_code)
                    
                    assert result['fallback_used'] is True
                    assert result['storage_type'] == 'NAS-FTP'
                    assert result['original_storage'] == 'MinIO'
                    assert mock_nas_fallback.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_load_balancing(self, hybrid_project_config, mock_dual_connectors):
        """Test load balancing between MinIO and NAS storage."""
        service = ProjectBatchService()
        
        # Mock multiple concurrent requests
        concurrent_requests = [
            {'file': f'/mixed/file_{i}.jpg', 'size': (i % 2 + 1) * 25 * 1024 * 1024}  # 25MB or 50MB
            for i in range(20)  # 20 concurrent file requests
        ]
        
        with patch.object(service, '_get_storage_load_metrics') as mock_load_metrics:
            mock_load_metrics.return_value = {
                'minio': {'current_connections': 15, 'avg_response_time': 50}
                'nas': {'current_connections': 5, 'avg_response_time': 200}
            }
            
            with patch.object(service, '_distribute_load_optimally') as mock_distribute:
                mock_distribute.return_value = {
                    'minio_assignments': 8,  # Fewer due to current load
                    'nas_assignments': 12,   # More available capacity
                    'load_balancing_used': True
                }
                
                # Test load distribution
                distribution = service._distribute_load_optimally(
                    concurrent_requests, hybrid_project_config
                )
                
                assert distribution['load_balancing_used'] is True
                assert distribution['minio_assignments'] + distribution['nas_assignments'] == len(concurrent_requests)
                # Should assign more to less loaded storage
                assert distribution['nas_assignments'] > distribution['minio_assignments']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_cross_storage_file_migration(self, hybrid_project_config, mock_dual_connectors):
        """Test migrating files between MinIO and NAS storage."""
        service = ProjectBatchService()
        
        migration_plan = {
            'files_to_migrate': [
                {'path': '/archive/old_video.mp4', 'from': 'MinIO', 'to': 'NAS-FTP', 'reason': 'archival'}
                {'path': '/active/new_image.jpg', 'from': 'NAS-FTP', 'to': 'MinIO', 'reason': 'frequent_access'}
            ],
            'migration_strategy': 'background',
            'verify_integrity': True,
            'cleanup_source': False  # Keep source until verification complete
        }
        
        with patch.object(service, '_execute_file_migration') as mock_migrate:
            mock_migrate.return_value = {
                'migration_id': 'MIG_001',
                'files_migrated': 2,
                'files_failed': 0,
                'total_size_migrated': 102 * 1024 * 1024,  # 102MB
                'migration_time': 45.5,  # seconds
                'integrity_verified': True
            }
            
            result = await service.migrate_files_between_storages(
                hybrid_project_config['project_code'], 
                migration_plan
            )
            
            assert result['files_migrated'] == 2
            assert result['files_failed'] == 0
            assert result['integrity_verified'] is True
            assert result['migration_time'] < 60  # Should complete within 1 minute

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_cost_optimization(self, hybrid_project_config, mock_dual_connectors):
        """Test cost optimization between storage types."""
        service = ProjectBatchService()
        
        # Mock cost analysis data
        storage_costs = {
            'minio': {'per_gb_month': 0.023, 'transfer_cost': 0.09}
            'nas': {'per_gb_month': 0.015, 'transfer_cost': 0.12}
        }
        
        file_usage_patterns = [
            {'path': '/videos/popular.mp4', 'access_count': 1000, 'size_gb': 2.5}
            {'path': '/docs/archive.pdf', 'access_count': 10, 'size_gb': 0.1}
            {'path': '/images/banner.jpg', 'access_count': 500, 'size_gb': 0.05}
        ]
        
        with patch.object(service, '_analyze_storage_costs') as mock_analyze:
            mock_analyze.return_value = {
                'recommendations': [
                    {'file': '/videos/popular.mp4', 'recommended_storage': 'MinIO', 'cost_savings': 15.50}
                    {'file': '/docs/archive.pdf', 'recommended_storage': 'NAS-FTP', 'cost_savings': 2.30}
                    {'file': '/images/banner.jpg', 'recommended_storage': 'MinIO', 'cost_savings': 8.75}
                ],
                'total_monthly_savings': 26.55,
                'optimization_applied': True
            }
            
            result = service._analyze_storage_costs(
                file_usage_patterns, storage_costs, hybrid_project_config
            )
            
            assert len(result['recommendations']) == 3
            assert result['total_monthly_savings'] > 25
            assert result['optimization_applied'] is True
            
            # Verify high-access files go to MinIO
            popular_video_rec = next(r for r in result['recommendations'] if 'popular.mp4' in r['file'])
            assert popular_video_rec['recommended_storage'] == 'MinIO'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_redundancy_and_sync(self, hybrid_project_config, mock_dual_connectors):
        """Test storage redundancy and synchronization between storage types."""
        service = ProjectBatchService()
        
        critical_files = [
            {'path': '/critical/backup.sql', 'size': 500 * 1024 * 1024},  # 500MB
            {'path': '/critical/config.json', 'size': 1024},  # 1KB
        ]
        
        with patch.object(service, '_setup_storage_redundancy') as mock_setup:
            mock_setup.return_value = {
                'redundancy_enabled': True,
                'sync_strategy': 'master_slave',
                'primary_storage': 'MinIO',
                'backup_storage': 'NAS-FTP',
                'sync_interval': 300,  # 5 minutes
                'files_synchronized': len(critical_files)
            }
            
            with patch.object(service, '_verify_file_consistency') as mock_verify:
                mock_verify.return_value = {
                    'consistent_files': len(critical_files),
                    'inconsistent_files': 0,
                    'checksum_verification': True,
                    'last_sync_time': datetime.now().isoformat()
                }
                
                # Setup redundancy
                redundancy_config = service._setup_storage_redundancy(
                    critical_files, hybrid_project_config
                )
                
                assert redundancy_config['redundancy_enabled'] is True
                assert redundancy_config['files_synchronized'] == len(critical_files)
                
                # Verify consistency
                consistency_check = service._verify_file_consistency(
                    critical_files, hybrid_project_config
                )
                
                assert consistency_check['consistent_files'] == len(critical_files)
                assert consistency_check['inconsistent_files'] == 0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_ai_processing_with_hybrid_storage(self, hybrid_project_config, mock_dual_connectors):
        """Test AI processing service with hybrid storage backend."""
        service = AIProcessingService()
        
        ai_job = {
            'job_id': 'AI_HYBRID_001',
            'files': [
                {'path': '/ai/video_analyze.mp4', 'storage': 'MinIO', 'size': 200 * 1024 * 1024}
                {'path': '/ai/image_classify.jpg', 'storage': 'NAS-FTP', 'size': 5 * 1024 * 1024}
            ],
            'processing_type': 'multimodal_analysis'
        }
        
        with patch.object(service, '_get_file_from_storage') as mock_get_file:
            # Mock different response times for different storages
            def side_effect(file_path, storage_type):
                if storage_type == 'MinIO':
                    return {'content': b'mock_video_data', 'retrieval_time': 0.5}
                else:  # NAS-FTP
                    return {'content': b'mock_image_data', 'retrieval_time': 2.0}
            
            mock_get_file.side_effect = side_effect
            
            with patch.object(service, '_process_with_ai_model') as mock_ai_process:
                mock_ai_process.return_value = {
                    'results': {'video': 'analysis_complete', 'image': 'classified'}
                    'processing_time': 15.5,
                    'confidence': 0.94
                }
                
                result = await service.process_files_from_hybrid_storage(
                    ai_job, hybrid_project_config
                )
                
                assert result['files_processed'] == 2
                # MinIO should be faster for file retrieval
                assert result['storage_performance']['MinIO']['avg_retrieval_time'] < 1.0
                assert result['storage_performance']['NAS-FTP']['avg_retrieval_time'] > 1.0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_hybrid_storage_performance_comparison(self, hybrid_project_config, mock_dual_connectors, performance_monitor):
        """Test performance comparison between MinIO and NAS storage."""
        service = MediaStreamingService()
        
        test_scenarios = [
            {'operation': 'upload', 'file_size': '10MB', 'concurrent_ops': 5}
            {'operation': 'download', 'file_size': '100MB', 'concurrent_ops': 3}
            {'operation': 'streaming', 'file_size': '500MB', 'concurrent_ops': 10}
        ]
        
        performance_results = {}
        
        for scenario in test_scenarios:
            for storage_type in ['MinIO', 'NAS-FTP']:
                performance_monitor.start()
                
                with patch.object(service, '_simulate_storage_operation') as mock_operation:
                    # Simulate different performance characteristics
                    if storage_type == 'MinIO':
                        mock_operation.return_value = {
                            'throughput_mbps': 150,
                            'latency_ms': 25,
                            'success_rate': 0.99
                        }
                    else:  # NAS-FTP
                        mock_operation.return_value = {
                            'throughput_mbps': 80,
                            'latency_ms': 150,
                            'success_rate': 0.95
                        }
                    
                    # Execute simulated operation
                    ops_result = await service._simulate_storage_operation(
                        scenario, storage_type, hybrid_project_config
                    )
                    
                    performance_monitor.stop()
                    execution_time = performance_monitor.get_execution_time()
                    
                    key = f"{scenario['operation']}_{storage_type}"
                    performance_results[key] = {
                        'execution_time': execution_time,
                        'throughput': ops_result['throughput_mbps'],
                        'latency': ops_result['latency_ms'],
                        'success_rate': ops_result['success_rate']
                    }
        
        # Verify MinIO generally performs better for streaming operations
        minio_streaming = performance_results['streaming_MinIO']
        nas_streaming = performance_results['streaming_NAS-FTP']
        
        assert minio_streaming['throughput'] > nas_streaming['throughput']
        assert minio_streaming['latency'] < nas_streaming['latency']
        assert minio_streaming['success_rate'] >= nas_streaming['success_rate']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_quota_and_capacity_management(self, hybrid_project_config, mock_dual_connectors):
        """Test storage quota and capacity management across both storage types."""
        service = ProjectBatchService()
        
        storage_quotas = {
            'minio': {'total_gb': 1000, 'used_gb': 750, 'warning_threshold': 0.8}
            'nas': {'total_gb': 2000, 'used_gb': 400, 'warning_threshold': 0.8}
        }
        
        with patch.object(service, '_check_storage_capacity') as mock_check:
            mock_check.return_value = {
                'minio': {
                    'available_gb': 250,
                    'usage_percentage': 75.0,
                    'warning_triggered': False,
                    'can_accept_uploads': True
                }
                'nas': {
                    'available_gb': 1600,
                    'usage_percentage': 20.0,
                    'warning_triggered': False,
                    'can_accept_uploads': True
                }
            }
            
            capacity_check = service._check_storage_capacity(
                storage_quotas, hybrid_project_config
            )
            
            assert capacity_check['minio']['can_accept_uploads'] is True
            assert capacity_check['nas']['available_gb'] > capacity_check['minio']['available_gb']
            
            # Test capacity-based routing
            large_file_size = 300 * 1024 * 1024 * 1024  # 300GB
            
            with patch.object(service, '_route_based_on_capacity') as mock_route:
                mock_route.return_value = 'NAS-FTP'  # Should route to NAS due to more space
                
                recommended_storage = service._route_based_on_capacity(
                    large_file_size, capacity_check
                )
                
                assert recommended_storage == 'NAS-FTP'

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_network_failure_recovery_between_storages(self, hybrid_project_config, mock_dual_connectors):
        """Test recovery from network failures affecting storage connections."""
        service = MediaStreamingService()
        
        # Simulate network partition affecting MinIO
        network_scenarios = [
            {'affected_storage': 'MinIO', 'error_type': 'connection_timeout'}
            {'affected_storage': 'NAS-FTP', 'error_type': 'network_unreachable'}
            {'affected_storage': 'both', 'error_type': 'dns_resolution_failure'}
        ]
        
        for scenario in network_scenarios:
            with patch.object(service, '_detect_network_issue') as mock_detect:
                mock_detect.return_value = {
                    'issue_detected': True,
                    'affected_storage': scenario['affected_storage'],
                    'error_type': scenario['error_type'],
                    'recovery_strategy': 'automatic_failover'
                }
                
                with patch.object(service, '_execute_recovery_strategy') as mock_recover:
                    if scenario['affected_storage'] == 'both':
                        mock_recover.return_value = {
                            'recovery_successful': False,
                            'fallback_mode': 'offline_queue',
                            'retry_scheduled': True
                        }
                    else:
                        mock_recover.return_value = {
                            'recovery_successful': True,
                            'active_storage': 'NAS-FTP' if scenario['affected_storage'] == 'MinIO' else 'MinIO',
                            'failover_completed': True
                        }
                    
                    recovery_result = await service._execute_recovery_strategy(
                        scenario, hybrid_project_config
                    )
                    
                    if scenario['affected_storage'] != 'both':
                        assert recovery_result['recovery_successful'] is True
                        assert recovery_result['failover_completed'] is True
                    else:
                        assert recovery_result['fallback_mode'] == 'offline_queue'
