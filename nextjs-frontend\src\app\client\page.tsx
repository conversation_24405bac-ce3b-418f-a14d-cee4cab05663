"use client"

import React, { useEffect, useState } from 'react'
import { authFetch } from '@/lib/authFetch'

// Base API URL (includes /api prefix)
import { API_BASE_URL } from "@/lib/api";

// Minimal dataset for dropdown
type DatasetOption = {
  id: number;
  name: string;
};

// Detailed dataset metrics
type DatasetDetails = DatasetOption & {
  total_batches: number;
  completed_batches: number;
  progress_percentage: number;
  audited_batches: number;
  audit_percentage: number;
  annotators_count: number;
  auditors_count: number;
};

export default function ClientPage() {
  const [datasets, setDatasets] = useState<DatasetOption[]>([])
  const [selectedId, setSelectedId] = useState<number | null>(null)
  const [details, setDetails] = useState<DatasetDetails | null>(null)

  useEffect(() => {
    authFetch(`${API_BASE_URL}/client/datasets`)
      .then(res => res.json())
      .then(res => {
        if (res.success) {
          setDatasets(res.data.datasets)
        } else {
          console.error(res.error)
        }
      })
      .catch(console.error)
  }, [])

  useEffect(() => {
    if (selectedId !== null) {
      authFetch(`${API_BASE_URL}/client/datasets/${selectedId}`)
        .then(res => res.json())
        .then(res => {
          if (res.success) {
            setDetails(res.data)
          } else {
            console.error(res.error)
          }
        })
        .catch(console.error)
    }
  }, [selectedId])

  return (
    <div className="px-8 py-8 max-w-screen-2xl mx-auto">
      <div className="w-full text-center mb-8">
        <h1 className="text-6xl font-serif font-bold text-[var(--secondary-color)]">Client Dashboard</h1>
      </div>

      {/* Dataset Selection */}
      <div className="bg-white rounded-xl py-4 px-6 mb-8 shadow-md">
        <label htmlFor="dataset" className="block text-lg font-serif font-medium text-[var(--text-secondary)] mb-2">
          Select Dataset
        </label>
        <select
          id="dataset"
          value={selectedId ?? ''}
          onChange={e => setSelectedId(Number(e.target.value))}
          className="mt-1 block w-full border border-gray-300 rounded-md px-4 py-2 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="" disabled>Select a dataset</option>
          {datasets.map(ds => (
            <option key={ds.id} value={ds.id}>{ds.name}</option>
          ))}
        </select>
      </div>

      {/* Dataset Metrics */}
      {details && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl p-6 shadow-md text-center transition-transform transform hover:scale-105">
            <p className="text-base font-serif font-medium text-[var(--text-secondary)]">Annotation Progress</p>
            <p className="mt-2 text-3xl font-bold text-[var(--primary-color)]">{details.progress_percentage}%</p>
            <p className="text-sm text-[var(--text-secondary)]">{details.completed_batches}/{details.total_batches}</p>
          </div>
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl p-6 shadow-md text-center transition-transform transform hover:scale-105">
            <p className="text-base font-serif font-medium text-[var(--text-secondary)]">Audit Progress</p>
            <p className="mt-2 text-3xl font-bold text-[var(--primary-color)]">{details.audit_percentage}%</p>
            <p className="text-sm text-[var(--text-secondary)]">{details.audited_batches}/{details.total_batches}</p>
          </div>
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl p-6 shadow-md text-center transition-transform transform hover:scale-105">
            <p className="text-base font-serif font-medium text-[var(--text-secondary)]">Annotators Working</p>
            <p className="mt-2 text-3xl font-bold text-[var(--primary-color)]">{details.annotators_count}</p>
          </div>
          <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl p-6 shadow-md text-center transition-transform transform hover:scale-105">
            <p className="text-base font-serif font-medium text-[var(--text-secondary)]">Auditors Working</p>
            <p className="mt-2 text-3xl font-bold text-[var(--primary-color)]">{details.auditors_count}</p>
          </div>
        </div>
      )}
    </div>
  )
}