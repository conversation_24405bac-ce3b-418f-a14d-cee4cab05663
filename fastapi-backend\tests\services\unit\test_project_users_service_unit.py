"""
Unit tests for ProjectUsersService.
Tests user management with dynamic roles based on allocation strategies.

COVERAGE FOCUS:
- Project user management and roles
- Dynamic role assignment based on allocation strategies  
- Project strategy retrieval and validation
- User assignment to projects
- Role updates and permissions
- Cross-database operations (master + project DBs)
- User eligibility validation
- Batch assignment coordination
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from app.services.project_users_service import ProjectUsersService
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

class TestProjectUsersService:
    """Test suite for ProjectUsersService."""
    
    @pytest.fixture
    def service(self):
        """Create service instance with mocked dependencies."""
        with patch('app.services.project_users_service.ProjectDBManager') as mock_db_manager:
            service = ProjectUsersService()
            service.db_manager = mock_db_manager.return_value
            return service
    
    ,
            'dual_annotator': {
                'strategy_type': 'dual_annotator_verification', 
                'annotators_per_batch': 2,
                'verifiers_per_batch': 1,
                'verification_required': True
            }
            'three_annotator': {
                'strategy_type': 'three_annotator_verification',
                'annotators_per_batch': 3,
                'verifiers_per_batch': 1,
                'verification_required': True,
                'cross_verification': True
            }
        
            {
                'user_id': 102,
                'username': 'annotator2', 
                'email': '<EMAIL>',
                'role': 'annotator',
                'is_active': True
            }
            {
                'user_id': 201,
                'username': 'verifier1',
                'email': '<EMAIL>',
                'role': 'verifier',
                'is_active': True
            }
            {
                'user_id': 301,
                'username': 'admin1',
                'email': '<EMAIL>',
                'role': 'admin',
                'is_active': True
            }
        

    # ==================================================================
    # PROJECT STRATEGY RETRIEVAL TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_success(self, service, project_factory, allocation_strategy_factory):
        """Test successful retrieval of project allocation strategy."""
        
        with patch('app.services.project_users_service.MasterSessionLocal') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock project query
            mock_project = MagicMock()
            mock_project.allocation_strategy = allocation_strategy_factory.create_three_annotator_strategy()
        mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = mock_project
            mock_db.execute.return_value = mock_result
            
            result = await service.get_project_strategy(project_factory.create_project()['project_code'])
            
            assert result == allocation_strategy_factory.create_three_annotator_strategy()
            mock_db.execute.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_project_not_found(self, service):
        """Test handling when project is not found."""
        
        with patch('app.services.project_users_service.MasterSessionLocal') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock no project found
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = None
            mock_db.execute.return_value = mock_result
            
            result = await service.get_project_strategy('NONEXISTENT_PROJECT')
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_database_error(self, service, project_factory):
        """Test handling of database errors."""
        
        with patch('app.services.project_users_service.MasterSessionLocal') as mock_session:
            mock_session.side_effect = Exception("Database connection failed")
            
            result = await service.get_project_strategy(project_factory.create_project()['project_code'])
            
            assert result is None  # Should handle gracefully

    # ==================================================================
    # USER PROJECT ASSIGNMENT TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_add_user_to_project_annotator_role(self, service, project_factory, sample_users, allocation_strategy_factory):
        """Test adding user to project with annotator role."""
        
        annotator = sample_users[0]  # First user is annotator
        strategy = allocation_strategy_factory.create_three_annotator_strategy()
        
        # Mock project database operations
        service.db_manager.get_project_session = AsyncMock()
        mock_session = AsyncMock()
        service.db_manager.get_project_session.return_value.__aenter__.return_value = mock_session
        
        with patch.object(service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = strategy
            
            with patch.object(service, 'add_user_to_project') as mock_add_user:
                mock_add_user.return_value = {
                    'success': True,
                    'user_id': annotator['user_id'],
                    'project_code': project_factory.create_project()['project_code'],
                    'role': 'annotator',
                    'assigned_at': datetime.now()
                }
                
                result = await service.add_user_to_project(
                    project_factory.create_project()['project_code'],
                    annotator['user_id'],
                    'annotator'
                )
                
                assert result['success'] is True
                assert result['role'] == 'annotator'
                assert result['user_id'] == annotator['user_id']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_add_user_to_project_verifier_role(self, service, project_factory, sample_users, allocation_strategy_factory):
        """Test adding user to project with verifier role."""
        
        verifier = sample_users[2]  # Third user is verifier
        strategy = allocation_strategy_factory.create_three_annotator_strategy()
        
        with patch.object(service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = strategy
            
            with patch.object(service, 'add_user_to_project') as mock_add_user:
                mock_add_user.return_value = {
                    'success': True,
                    'user_id': verifier['user_id'],
                    'project_code': project_factory.create_project()['project_code'],
                    'role': 'verifier',
                    'assigned_at': datetime.now()
                }
                
                result = await service.add_user_to_project(
                    project_factory.create_project()['project_code'],
                    verifier['user_id'],
                    'verifier'
                )
                
                assert result['success'] is True
                assert result['role'] == 'verifier'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_add_user_to_project_role_mismatch_strategy(self, service, project_factory, sample_users, allocation_strategy_factory):
        """Test adding verifier to project that doesn't require verification."""
        
        verifier = sample_users[2]
        no_verification_strategy = allocation_strategy_factory.create_single_annotator_strategy()  # No verification required
        
        with patch.object(service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = no_verification_strategy
            
            with patch.object(service, 'add_user_to_project') as mock_add_user:
                mock_add_user.return_value = {
                    'success': False,
                    'error': 'Project strategy does not require verifiers'
                }
                
                result = await service.add_user_to_project(
                    project_factory.create_project()['project_code'],
                    verifier['user_id'],
                    'verifier'
                )
                
                assert result['success'] is False
                assert 'strategy' in result['error']

    # ==================================================================
    # DYNAMIC ROLE MANAGEMENT TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_update_user_role_based_on_strategy_change(self, service, project_factory, sample_users):
        """Test updating user roles when project strategy changes."""
        
        user = sample_users[0]
        old_strategy = {'annotators_per_batch': 1, 'verifiers_per_batch': 0}
        new_strategy = {'annotators_per_batch': 3, 'verifiers_per_batch': 1}
        
        with patch.object(service, 'update_user_role') as mock_update_role:
            mock_update_role.return_value = {
                'success': True,
                'user_id': user['user_id'],
                'old_role': 'annotator',
                'new_role': 'annotator',  # Same role but updated permissions
                'updated_permissions': True
            }
            
            result = await service.update_user_role(
                project_factory.create_project()['project_code'],
                user['user_id'],
                new_strategy
            )
            
            assert result['success'] is True
            assert 'updated_permissions' in result

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_user_eligibility_for_role(self, service, project_factory, sample_users, allocation_strategy_factory):
        """Test validation of user eligibility for specific roles."""
        
        strategy = allocation_strategy_factory.create_three_annotator_strategy()
        test_cases = [
            {
                'user': sample_users[0],  # Annotator
                'requested_role': 'annotator',
                'expected_eligible': True
            }
            {
                'user': sample_users[2],  # Verifier  
                'requested_role': 'verifier',
                'expected_eligible': True
            }
            {
                'user': sample_users[0],  # Annotator requesting verifier role
                'requested_role': 'verifier',
                'expected_eligible': False  # Assuming role restrictions
            }
        ]
        
        for case in test_cases:
            with patch.object(service, 'validate_user_eligibility') as mock_validate:
                mock_validate.return_value = case['expected_eligible']
                
                result = await service.validate_user_eligibility(
                    project_factory.create_project()['project_code'],
                    case['user']['user_id'],
                    case['requested_role'],
                    strategy
                )
                
                assert result == case['expected_eligible']

    # ==================================================================
    # PROJECT USER LISTING TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_users_by_role(self, service, project_factory, sample_users):
        """Test retrieving project users filtered by role."""
        
        annotators = [user for user in sample_users if user['role'] == 'annotator']
        verifiers = [user for user in sample_users if user['role'] == 'verifier']
        
        with patch.object(service, 'get_project_users_by_role') as mock_get_users:
            # Test getting annotators
            mock_get_users.return_value = annotators
            
            result_annotators = await service.get_project_users_by_role(
                project_factory.create_project()['project_code'],
                'annotator'
            )
            
            assert len(result_annotators) == 2
            assert all(user['role'] == 'annotator' for user in result_annotators)
            
            # Test getting verifiers
            mock_get_users.return_value = verifiers
            
            result_verifiers = await service.get_project_users_by_role(
                project_factory.create_project()['project_code'],
                'verifier'
            )
            
            assert len(result_verifiers) == 1
            assert result_verifiers[0]['role'] == 'verifier'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_all_project_users(self, service, project_factory, sample_users):
        """Test retrieving all users assigned to a project."""
        
        with patch.object(service, 'get_all_project_users') as mock_get_all:
            mock_get_all.return_value = sample_users
            
            result = await service.get_all_project_users(
                project_factory.create_project()['project_code']
            )
            
            assert len(result) == len(sample_users)
            assert any(user['role'] == 'annotator' for user in result)
            assert any(user['role'] == 'verifier' for user in result)
            assert any(user['role'] == 'admin' for user in result)

    # ==================================================================
    # USER REMOVAL TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_remove_user_from_project_success(self, service, project_factory, sample_users):
        """Test successful removal of user from project."""
        
        user_to_remove = sample_users[1]  # Second annotator
        
        with patch.object(service, 'remove_user_from_project') as mock_remove:
            mock_remove.return_value = {
                'success': True,
                'user_id': user_to_remove['user_id'],
                'project_code': project_factory.create_project()['project_code'],
                'removed_at': datetime.now()
            }
            
            result = await service.remove_user_from_project(
                project_factory.create_project()['project_code'],
                user_to_remove['user_id']
            )
            
            assert result['success'] is True
            assert result['user_id'] == user_to_remove['user_id']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_remove_user_with_active_batches(self, service, project_factory, sample_users):
        """Test removal of user who has active batch assignments."""
        
        user_with_active_batches = sample_users[0]
        
        with patch.object(service, 'check_user_active_batches') as mock_check_active:
            mock_check_active.return_value = {
                'has_active_batches': True,
                'active_batch_count': 3,
                'batch_ids': ['BATCH_001', 'BATCH_002', 'BATCH_003']
            }
            
            with patch.object(service, 'remove_user_from_project') as mock_remove:
                mock_remove.return_value = {
                    'success': False,
                    'error': 'User has active batch assignments',
                    'active_batches': 3
                }
                
                result = await service.remove_user_from_project(
                    project_factory.create_project()['project_code'],
                    user_with_active_batches['user_id']
                )
                
                assert result['success'] is False
                assert 'active batch' in result['error']

    # ==================================================================
    # ALLOCATION STRATEGY COMPLIANCE TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_ensure_annotator_count_compliance(self, service, project_factory, allocation_strategy_factory):
        """Test that project has required number of annotators per strategy."""
        
        three_annotator_strategy = allocation_strategy_factory.create_three_annotator_strategy()
        current_annotators = 2  # Less than required 3
        
        with patch.object(service, 'get_project_annotator_count') as mock_get_count:
            mock_get_count.return_value = current_annotators
            
            with patch.object(service, 'validate_annotator_count_compliance') as mock_validate:
                mock_validate.return_value = {
                    'compliant': False,
                    'required_count': three_annotator_strategy['annotators_per_batch'],
                    'current_count': current_annotators,
                    'shortfall': 1
                }
                
                result = await service.validate_annotator_count_compliance(
                    project_factory.create_project()['project_code'],
                    three_annotator_strategy
                )
                
                assert result['compliant'] is False
                assert result['shortfall'] == 1

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_ensure_verifier_count_compliance(self, service, project_factory, allocation_strategy_factory):
        """Test that project has required number of verifiers per strategy."""
        
        verification_strategy = allocation_strategy_factory.create_dual_annotator_strategy()
        current_verifiers = 0  # No verifiers assigned
        
        with patch.object(service, 'get_project_verifier_count') as mock_get_count:
            mock_get_count.return_value = current_verifiers
            
            with patch.object(service, 'validate_verifier_count_compliance') as mock_validate:
                mock_validate.return_value = {
                    'compliant': False,
                    'required_count': verification_strategy['verifiers_per_batch'],
                    'current_count': current_verifiers,
                    'shortfall': 1
                }
                
                result = await service.validate_verifier_count_compliance(
                    project_factory.create_project()['project_code'],
                    verification_strategy
                )
                
                assert result['compliant'] is False
                assert result['shortfall'] == 1

    # ==================================================================
    # CROSS-DATABASE OPERATION TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_master_and_project_user_data(self, service, project_factory, sample_users):
        """Test synchronization between master DB and project DB user data."""
        
        user = sample_users[0]
        
        # Mock master DB user data
        master_user_data = {
            'user_id': user['user_id'],
            'username': user['username'],
            'email': user['email'],
            'role': user['role'],
            'is_active': user['is_active'],
            'updated_at': datetime.now()
        }
        
        # Mock project DB user data (potentially outdated)
        project_user_data = {
            'user_id': user['user_id'],
            'role': user['role'],
            'last_sync': datetime.now() - timedelta(hours=2)  # 2 hours old
        }
        
        with patch.object(service, 'get_master_user_data') as mock_get_master:
            mock_get_master.return_value = master_user_data
            
            with patch.object(service, 'get_project_user_data') as mock_get_project:
                mock_get_project.return_value = project_user_data
                
                with patch.object(service, 'sync_user_data') as mock_sync:
                    mock_sync.return_value = {
                        'synced': True,
                        'user_id': user['user_id'],
                        'updated_fields': ['last_sync']
                    }
                    
                    result = await service.sync_user_data(
                        project_factory.create_project()['project_code'],
                        user['user_id']
                    )
                    
                    assert result['synced'] is True
                    assert 'updated_fields' in result

    # ==================================================================
    # PERFORMANCE TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_bulk_user_assignment_performance(self, service, project_factory, allocation_strategy_factory):
        """Test performance of bulk user assignments to project."""
        
        # Generate large user set
        bulk_users = []
        for i in range(50):  # 50 users
            bulk_users.append({
                'user_id': 1000 + i,
                'username': f'user_{i:02d}',
                'role': 'annotator' if i < 40 else 'verifier'  # 40 annotators, 10 verifiers
            })
        
        strategy = allocation_strategy_factory.create_three_annotator_strategy()
        
        with patch.object(service, 'bulk_assign_users_to_project') as mock_bulk_assign:
            mock_bulk_assign.return_value = {
                'assigned_count': len(bulk_users),
                'processing_time': 1.2,  # Seconds
                'successful_assignments': len(bulk_users),
                'failed_assignments': 0
            }
            
            import time
            start_time = time.time()
            
            result = await service.bulk_assign_users_to_project(
                project_factory.create_project()['project_code'],
                bulk_users,
                strategy
            )
            
            end_time = time.time()
            actual_processing_time = end_time - start_time
            
            # Performance assertions
            assert actual_processing_time < 3.0  # Should complete in under 3 seconds
            assert result['assigned_count'] == len(bulk_users)
            assert result['failed_assignments'] == 0

    # ==================================================================
    # ERROR HANDLING AND EDGE CASES
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_invalid_project_code(self, service):
        """Test handling of invalid/non-existent project codes."""
        
        invalid_project_codes = [
            '',  # Empty string
            'INVALID_PROJECT_123',  # Non-existent
            'PROJECT WITH SPACES',  # Invalid format
            'PROJECT_' + 'X' * 100  # Too long
        ]
        
        for invalid_code in invalid_project_codes:
            result = await service.get_project_strategy(invalid_code)
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_user_already_in_project(self, service, project_factory, sample_users):
        """Test handling when trying to add user already in project."""
        
        existing_user = sample_users[0]
        
        with patch.object(service, 'check_user_in_project') as mock_check:
            mock_check.return_value = True  # User already in project
            
            with patch.object(service, 'add_user_to_project') as mock_add:
                mock_add.return_value = {
                    'success': False,
                    'error': 'User already assigned to project',
                    'user_id': existing_user['user_id']
                }
                
                result = await service.add_user_to_project(
                    project_factory.create_project()['project_code'],
                    existing_user['user_id'],
                    'annotator'
                )
                
                assert result['success'] is False
                assert 'already assigned' in result['error']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_database_transaction_failure(self, service, project_factory, sample_users):
        """Test handling of database transaction failures."""
        
        user = sample_users[0]
        
        with patch.object(service, 'add_user_to_project') as mock_add:
            mock_add.side_effect = Exception("Transaction rollback: Constraint violation")
            
            try:
                result = await service.add_user_to_project(
                    project_factory.create_project()['project_code'],
                    user['user_id'],
                    'annotator'
                )
                # If we reach here, exception wasn't raised properly
                assert False, "Expected exception was not raised"
            except Exception as e:
                # Should handle database errors gracefully
                assert "Transaction" in str(e) or "Constraint" in str(e)
