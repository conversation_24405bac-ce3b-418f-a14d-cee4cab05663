
I'll analyze the frontend structure of your codebase by examining both the templates and static directories to understand the functionality and organization.

Read file: templates/base.html
Now let's look at the landing page to understand the main entry point:

Read file: templates/landing.html
Let's continue looking at the landing page:

Read file: templates/landing.html
Now let's examine the login page:

Read file: templates/login.html
Now let's check one of the main app features - examining the admin dashboard:

Ran tool
Let's look at the admin dashboard:

Read file: templates/admin/dashboard.html
Let's also check the annotator functionality:

Ran tool
Let's examine the annotator dashboard and annotate page:

Read file: templates/annotator/annotator_dashboard.html
Let's check the annotate page:

Read file: templates/annotator/annotate.html
Let's examine the auditor functionality:

Ran tool
Let's examine the auditor dashboard:

Read file: templates/auditor/dashboard.html
Now let's look at the JavaScript in the static directory to understand client-side functionality:

Ran tool
Let's examine the main.js and then look at specific JS files for annotator and auditor:

Read file: static/js/main.js
Let's check one of the annotator JavaScript files:

Ran tool
Let's look at the annotate.js file to understand the annotation functionality:

Read file: 

Read file: static/js/annotator/annotate.js
Let's look at an admin JavaScript file:

Ran tool
Let's also check the synthetic templates:

Ran tool
Based on all the files we've examined, I'll now provide a comprehensive analysis of the frontend structure and functionality.

The frontend of the DADP (Data Analytics & Delivery Platform) is a comprehensive web application designed for data annotation, auditing, and management. Here's a detailed breakdown:

## Overall Architecture

1. **Base Layout System**
   - Uses a template inheritance pattern with `base.html` as the main layout
   - Responsive design with Bootstrap 5
   - Support for different user roles (admin, auditor, annotator)

2. **Authentication System**
   - Login page with username/password authentication
   - User registration flow
   - Password change functionality
   - Role-based access control

3. **Role-Based Features**
   - **Admin:** User management, data source connections, task configuration
   - **Annotator:** Image labeling, verification, supervision
   - **Auditor:** Task verification, quality control, history tracking

## Key Frontend Modules

1. **Landing Page**
   - Modern UI with animations and gradient backgrounds
   - Information about the platform capabilities
   - Access to different services: SynGround, Documind-o, NoteOCR
   - Company information and contact details

2. **Admin Dashboard**
   - Data connector management (Google Drive, NAS storage)
   - User management interface
   - Auditor settings configuration
   - Telegram channel management
   - OCR directory management

3. **Annotation System**
   - Image viewing with zoom/pan functionality
   - Label input and verification
   - Batch processing of images
   - Progress tracking
   - Keyboard shortcuts for efficiency
   - Image cropping capabilities

4. **Auditor System**
   - Task assignment and review
   - History tracking of reviewed items
   - Quality control metrics

5. **SynGround Module**
   - Synthetic data generation and management
   - Dataset visualization
   - Upload functionality for conversations

## Frontend Technologies

1. **CSS/Styling**
   - Bootstrap 5 for layout and components
   - Custom CSS for specific UI elements
   - Responsive design for different screen sizes
   - Theme support (light/dark mode)

2. **JavaScript**
   - jQuery for DOM manipulation and AJAX
   - Cropper.js for image manipulation
   - Client-side form validation
   - Dynamic UI updates
   - Real-time user feedback

3. **UI Components**
   - Modal dialogs for configuration
   - Toast notifications for user feedback
   - Progress indicators
   - Interactive data tables
   - Form inputs with validation

4. **Data Flow**
   - AJAX-based communication with backend
   - Real-time updates for task status
   - Session management for user state
   - Local storage for certain UI preferences

## Notable Features

1. **Image Annotation Tools**
   - Zoom, pan, and crop functionality
   - Keyboard shortcuts for efficient workflow
   - Progress tracking across batches
   - Label verification mode

2. **Multi-modal Data Support**
   - Image annotation
   - Text/document processing
   - NoteOCR for handwritten content
   - Synthetic data generation

3. **User Experience**
   - Guided workflows
   - Contextual help and instructions
   - Visual feedback on actions
   - Efficient keyboard-driven interfaces

4. **Integration Points**
   - Google Drive connectivity
   - NAS/file system integration
   - Telegram channel integration
   - Possibly API-based data exchange

The frontend is designed to support Human-in-the-Loop (HITL) AI training, with specific workflows for data annotation, verification, and quality control. It appears to be a comprehensive platform for managing the entire lifecycle of AI training data creation and management.
