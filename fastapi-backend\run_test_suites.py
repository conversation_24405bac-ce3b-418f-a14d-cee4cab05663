#!/usr/bin/env python3
"""
Test Suite Runner for Database Integration Tests

This script provides easy commands to run different test suites based on your needs.

USAGE:
    python run_test_suites.py smoke          # Run smoke tests (5-10 minutes)
    python run_test_suites.py regression     # Run regression tests (30-60 minutes)
    python run_test_suites.py performance    # Run performance tests (60+ minutes)
    python run_test_suites.py security       # Run security tests
    python run_test_suites.py feature admin  # Run admin feature tests
    python run_test_suites.py all           # Run all tests

EXAMPLES:
    # Quick smoke test before deployment
    python run_test_suites.py smoke
    
    # Full regression before release
    python run_test_suites.py regression
    
    # Performance testing
    python run_test_suites.py performance
    
    # Test specific feature
    python run_test_suites.py feature admin
    python run_test_suites.py feature assignment
"""

import subprocess
import sys
import time
from datetime import datetime
from typing import List, Dict


class TestSuiteRunner:
    """Test suite runner with predefined test suite configurations."""
    
    def __init__(self):
        self.base_command = ["python", "-m", "pytest"]
        self.test_dir = "tests/integration/database/"
        
        # Define test suite configurations
        self.suites = {
            "smoke": {
                "description": "🚀 SMOKE TESTS: Critical path tests (5-10 minutes)",
                "markers": ["smoke"],
                "options": ["-v", "--tb=short", "--durations=10"],
                "estimated_time": "5-10 minutes"
            },
            
            "regression": {
                "description": "🔄 REGRESSION TESTS: Comprehensive test coverage (30-60 minutes)",
                "markers": ["regression", "smoke"],
                "options": ["-v", "--tb=line", "--durations=20"],
                "estimated_time": "30-60 minutes"
            },
            
            "performance": {
                "description": "⚡ PERFORMANCE TESTS: Load and performance testing (60+ minutes)",
                "markers": ["performance", "bulk_data", "slow"],
                "options": ["-v", "--tb=short", "--durations=50"],
                "estimated_time": "60+ minutes"
            },
            
            "security": {
                "description": "🔒 SECURITY TESTS: Authentication and authorization (10-20 minutes)",
                "markers": ["security", "auth"],
                "options": ["-v", "--tb=short"],
                "estimated_time": "10-20 minutes"
            },
            
            "stable": {
                "description": "✅ STABLE TESTS: Reliable tests for CI/CD (15-30 minutes)",
                "markers": ["stable"],
                "options": ["-v", "--tb=line"],
                "estimated_time": "15-30 minutes"
            },
            
            "critical": {
                "description": "🎯 CRITICAL TESTS: P0 priority tests (10-15 minutes)",
                "markers": ["critical"],
                "options": ["-v", "--tb=short"],
                "estimated_time": "10-15 minutes"
            },
            
            "unit": {
                "description": "⚡ UNIT TESTS: Fast unit tests (2-5 minutes)",
                "markers": ["unit"],
                "options": ["-v", "--tb=short", "-x"],
                "estimated_time": "2-5 minutes"
            },
        }
        
        # Feature-specific test suites
        self.feature_suites = {
            "admin": {
                "description": "👑 ADMIN FEATURE: Admin functionality tests",
                "markers": ["admin"],
                "files": ["test_admin_routes_database_integration.py"]
            },
            
            "assignment": {
                "description": "📋 ASSIGNMENT FEATURE: Assignment workflow tests",
                "markers": ["assignment"],
                "files": ["test_assignment_routes_database_integration.py", "test_annotator_assignment_operations.py"]
            },
            
            "auth": {
                "description": "🔐 AUTH FEATURE: Authentication tests",
                "markers": ["auth"],
                "files": ["test_auth_middleware_database_integration.py"]
            },
            
            "ai": {
                "description": "🤖 AI PROCESSING FEATURE: AI processing tests",
                "markers": ["ai_processing"],
                "files": ["test_ai_processing_operations.py"]
            },
            
            "media": {
                "description": "📁 MEDIA FEATURE: Media handling tests",
                "markers": ["media"],
                "files": ["test_media_routes_database_integration.py"]
            },
            
            "repository": {
                "description": "💾 REPOSITORY LAYER: Repository tests",
                "markers": ["repository"],
                "files": ["test_repository_layer_operations.py"]
            },
            
            "service": {
                "description": "⚙️ SERVICE LAYER: Service integration tests",
                "markers": ["service"],
                "files": ["test_service_layer_integration.py"]
            }
        }
    
    def run_suite(self, suite_name: str) -> bool:
        """Run a predefined test suite."""
        if suite_name not in self.suites:
            print(f"❌ Unknown test suite: {suite_name}")
            print(f"Available suites: {', '.join(self.suites.keys())}")
            return False
        
        suite = self.suites[suite_name]
        print(f"\n{suite['description']}")
        print(f"📅 Estimated time: {suite['estimated_time']}")
        print(f"🏷️  Markers: {', '.join(suite['markers'])}")
        print("="*80)
        
        # Build pytest command
        cmd = self.base_command.copy()
        cmd.extend(suite['options'])
        cmd.append(self.test_dir)
        
        # Add marker filters
        for marker in suite['markers']:
            cmd.extend(["-m", marker])
        
        return self._execute_command(cmd)
    
    def run_feature_suite(self, feature_name: str) -> bool:
        """Run tests for a specific feature."""
        if feature_name not in self.feature_suites:
            print(f"❌ Unknown feature: {feature_name}")
            print(f"Available features: {', '.join(self.feature_suites.keys())}")
            return False
        
        feature = self.feature_suites[feature_name]
        print(f"\n{feature['description']}")
        print(f"🏷️  Markers: {', '.join(feature['markers'])}")
        print(f"📁 Files: {', '.join(feature['files'])}")
        print("="*80)
        
        # Build pytest command
        cmd = self.base_command.copy()
        cmd.extend(["-v", "--tb=short"])
        
        # Add specific files
        for file in feature['files']:
            cmd.append(f"{self.test_dir}{file}")
        
        # Add marker filter
        for marker in feature['markers']:
            cmd.extend(["-m", marker])
        
        return self._execute_command(cmd)
    
    def run_all_tests(self) -> bool:
        """Run all database integration tests."""
        print("\n🌟 FULL TEST SUITE: All database integration tests")
        print("📅 Estimated time: 120+ minutes")
        print("="*80)
        
        cmd = self.base_command.copy()
        cmd.extend(["-v", "--tb=line", "--durations=50"])
        cmd.append(self.test_dir)
        
        return self._execute_command(cmd)
    
    def _execute_command(self, cmd: List[str]) -> bool:
        """Execute pytest command and return success status."""
        start_time = time.time()
        print(f"🚀 Executing: {' '.join(cmd)}")
        print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-"*80)
        
        try:
            result = subprocess.run(cmd, check=False)
            end_time = time.time()
            duration = end_time - start_time
            
            print("-"*80)
            print(f"⏱️  Duration: {duration:.2f} seconds ({duration/60:.1f} minutes)")
            print(f"🏁 Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            if result.returncode == 0:
                print("✅ All tests passed!")
                return True
            else:
                print(f"❌ Tests failed with return code: {result.returncode}")
                return False
                
        except KeyboardInterrupt:
            print("\n⚠️  Test execution interrupted by user")
            return False
        except Exception as e:
            print(f"❌ Error executing tests: {e}")
            return False
    
    def show_help(self):
        """Display help information."""
        print("\n🧪 DATABASE TEST SUITE RUNNER")
        print("="*80)
        
        print("\n📋 AVAILABLE TEST SUITES:")
        for name, suite in self.suites.items():
            print(f"  {name:12} - {suite['description']}")
        
        print("\n🎯 AVAILABLE FEATURES:")
        for name, feature in self.feature_suites.items():
            print(f"  {name:12} - {feature['description']}")
        
        print("\n💡 USAGE EXAMPLES:")
        print("  python run_test_suites.py smoke              # Quick smoke test")
        print("  python run_test_suites.py regression         # Full regression")
        print("  python run_test_suites.py performance        # Performance tests")
        print("  python run_test_suites.py feature admin      # Admin feature tests")
        print("  python run_test_suites.py all               # All tests")


def main():
    """Main entry point for test suite runner."""
    runner = TestSuiteRunner()
    
    if len(sys.argv) < 2:
        runner.show_help()
        return
    
    command = sys.argv[1].lower()
    
    if command == "help" or command == "-h" or command == "--help":
        runner.show_help()
        success = True
    elif command == "feature":
        if len(sys.argv) < 3:
            print("❌ Feature name required. Example: python run_test_suites.py feature admin")
            return
        feature_name = sys.argv[2].lower()
        success = runner.run_feature_suite(feature_name)
    elif command == "all":
        success = runner.run_all_tests()
    elif command in runner.suites:
        success = runner.run_suite(command)
    else:
        print(f"❌ Unknown command: {command}")
        runner.show_help()
        success = False
    
    # Exit with appropriate code for CI/CD
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
