"""
Main application module.
Entry point for the FastAPI application.
"""
from fastapi import FastAP<PERSON>, Request, HTTPException, status # type: ignore
from fastapi.responses import RedirectResponse, JSONResponse # type: ignore
from fastapi.middleware.cors import CORSMiddleware # type: ignore
from fastapi.exception_handlers import http_exception_handler # type: ignore
from contextlib import asynccontextmanager
from starlette.middleware.sessions import SessionMiddleware # type: ignore
from starlette.exceptions import HTTPException as StarletteHTTPException # type: ignore
import os
import pickle
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from routes.auth_routes import router as auth_router
# from routes.auditor_routes import router as auditor_router
from routes.admin_route_modules import admin_user_routes
from routes.admin_route_modules import admin_media_routes
from routes.admin_routes import router as admin_router
from routes.admin_route_modules import admin_connection_routes
from routes.admin_route_modules import admin_annotator_view
from routes.admin_route_modules import admin_instructions
from routes.admin_route_modules import admin_client_routes
from routes.admin_route_modules import admin_project_routes
from routes.admin_route_modules import admin_minio_pool_routes
from routes.admin_route_modules import admin_csv_upload_routes
from routes.annotator_routes import router as annotator_router
# from routes.NoteOCR_routes import router as NoteOCR_router
# from routes.annotator_supervision_routes import router as annotator_supervision_router
from routes.allocation_strategy_routes import router as allocation_strategy_router
from routes.project_routes_modules.project_routes import router as project_router
from routes.project_routes_modules.project_creation_routes import router as project_creation_router
from routes.project_routes_modules.project_batch_routes import router as project_batch_router
from routes.project_routes_modules.project_batch_allocations_routes import router as project_batch_allocations_router
from routes.project_routes_modules.project_users_routes import router as project_users_router
from routes.project_routes_modules.project_activation_routes import router as project_activation_router
from routes.assignment_routes import router as assignment_router
from routes.assignment_routes.user_routes import router as user_assignment_routes
from routes.assignment_routes.user_assignment_routes import router as user_assignment_operation_routes
from routes.assignment_routes.user_removal_routes import router as user_removal_routes
from routes.assignment_routes.batch_allocation_routes import router as batch_allocation_router
from routes.assignment_routes.annotator_assignment_routes import router as annotator_assignment_router
from routes.ai_processing import ai_processing_router
from routes.model_endpoints.ai_models_registry_routes import router as ai_models_registry_router
# from routes.synthetic_dataset_routes import router as synthetic_dataset_router
# from routes.knowledge_base_routes import router as knowledge_base_router
from routes.verifier_routes import router as verifier_router
from routes.export_routes import router as export_router
from routes.media_routes import router as media_router

from core.config import settings
from cache.redis_connector import create_redis_client, set_redis_client, set_redis_enabled
from cache.admin_cache import invalidate_stats_cache
from google_auth_oauthlib.flow import Flow # type: ignore
import uvicorn # type: ignore


from core.logging import configure_app_logging
logger = configure_app_logging(
    console_for_root=settings.debug,
    console_for_modules=settings.debug
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup event for application resources (e.g., caching, database)"""

    # Initialize Redis if enabled
    if settings.redis_settings.enabled:
        try:
            client = await create_redis_client()
            if client:
                set_redis_client(client)
                logger.info("Async Redis client initialized for caching")
            else:
                set_redis_enabled(False)
                logger.warning("Async Redis client not available, caching disabled")
        except Exception as e:
            set_redis_enabled(False)
            logger.error(f"Error initializing async Redis client: {e}")
    
    
    # if os.environ.get("GEMINI_API_KEY"):
    #     logger.info("GEMINI_API_KEY is available")
    # else:
    #     logger.warning("GEMINI_API_KEY is not set. Synthetic dataset generation will not work properly.")
    
    # Initialize MinIO connection pool
    try:
        from core.minio_pool import get_connection_pool
        pool = await get_connection_pool()
        logger.info("MinIO connection pool initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing MinIO connection pool: {e}")
    
    yield
    
    # Cleanup MinIO connection pool
    try:
        from core.minio_pool import shutdown_connection_pool
        await shutdown_connection_pool()
        logger.info("MinIO connection pool shut down successfully")
    except Exception as e:
        logger.error(f"Error shutting down MinIO connection pool: {e}")

app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="Backend API for the application",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_settings.allow_origins,
    allow_credentials=settings.cors_settings.allow_credentials,
    allow_methods=settings.cors_settings.allow_methods,
    allow_headers=settings.cors_settings.allow_headers,
)

app.add_middleware(SessionMiddleware, secret_key=settings.jwt_settings.secret_key)

@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request: Request, exc: StarletteHTTPException):
    response = await http_exception_handler(request, exc)
    response.headers["Access-Control-Allow-Origin"] = "http://localhost:3000"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, Origin, User-Agent, DNT, Cache-Control, X-Mx-ReqToken, Keep-Alive, X-Requested-With, If-Modified-Since"
    return response

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    response = JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )
    response.headers["Access-Control-Allow-Origin"] = "http://localhost:3000"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Authorization, Content-Type, Accept, Origin, User-Agent, DNT, Cache-Control, X-Mx-ReqToken, Keep-Alive, X-Requested-With, If-Modified-Since"
    logger.error(f"Unhandled exception: {exc}")
    return response


app.include_router(auth_router, prefix=settings.api_prefix)
# app.include_router(auditor_router, prefix=settings.api_prefix)
app.include_router(admin_router, prefix=settings.api_prefix)

app.include_router(admin_user_routes.router, prefix=settings.api_prefix)
app.include_router(admin_media_routes.router, prefix=settings.api_prefix)
app.include_router(admin_connection_routes.router, prefix=settings.api_prefix)
app.include_router(admin_annotator_view.router, prefix=settings.api_prefix)
app.include_router(admin_instructions.router, prefix=settings.api_prefix)
app.include_router(admin_client_routes.router, prefix=settings.api_prefix)
app.include_router(admin_project_routes.router, prefix=settings.api_prefix)
app.include_router(admin_minio_pool_routes.router, prefix=settings.api_prefix)
app.include_router(admin_csv_upload_routes.router, prefix=settings.api_prefix)
app.include_router(annotator_router, prefix=settings.api_prefix)
# app.include_router(NoteOCR_router, prefix=settings.api_prefix)
# app.include_router(annotator_supervision_router, prefix=settings.api_prefix)
app.include_router(allocation_strategy_router, prefix=settings.api_prefix)
app.include_router(project_router, prefix=settings.api_prefix)
app.include_router(project_creation_router, prefix=settings.api_prefix)
app.include_router(project_batch_router, prefix=settings.api_prefix)
app.include_router(project_batch_allocations_router, prefix=settings.api_prefix)
app.include_router(project_users_router, prefix=settings.api_prefix)
app.include_router(project_activation_router, prefix=settings.api_prefix)
app.include_router(assignment_router, prefix=settings.api_prefix)
app.include_router(user_assignment_routes, prefix=settings.api_prefix)
app.include_router(user_assignment_operation_routes, prefix=settings.api_prefix)
app.include_router(user_removal_routes, prefix=settings.api_prefix)
app.include_router(batch_allocation_router, prefix=settings.api_prefix)
app.include_router(annotator_assignment_router, prefix=settings.api_prefix)
app.include_router(ai_processing_router, prefix=f"{settings.api_prefix}/ai", tags=["AI Processing"])
app.include_router(ai_models_registry_router, prefix=settings.api_prefix)
app.include_router(verifier_router, prefix=settings.api_prefix)
app.include_router(export_router, prefix=settings.api_prefix)
app.include_router(media_router, prefix=settings.api_prefix)
# app.include_router(synthetic_dataset_router, prefix=settings.api_prefix)
# app.include_router(knowledge_base_router, prefix=settings.api_prefix)


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint - API information"""
    return {
        "app_name": settings.app_name,
        "version": settings.version,
        "docs_url": "/docs",
    }

if __name__ == "__main__":
    
    mode = "debug" if settings.debug else "production"
    logger.info(f"Starting {settings.app_name} v{settings.version} in {mode} mode")
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="debug" if settings.debug else "info",
        access_log=settings.debug,
        forwarded_allow_ips=os.environ.get("FORWARDED_ALLOW_IPS", "*"),
        workers=int(os.environ.get("WORKERS", 1))
    )