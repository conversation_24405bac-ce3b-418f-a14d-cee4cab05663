# routers/admin_user_routes.py

from fastapi import APIRouter, Depends, HTTPException, status # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from pydantic import BaseModel, ConfigDict
from typing import List, Optional
from core.session_manager import get_master_db_session
from dependencies.auth import get_user_service, UserService, create_user_response, get_current_active_user, require_admin
from schemas.UserSchemas import SuccessResponse, ErrorResponse, UserResponse, UserUpdate, UserCreate, AddUserRequest
from post_db.master_models.users import AnnotatorMode
from post_db.master_models.clients import Clients
from core.security import hash_password
from cache import flush_cache
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
import logging
from core.session_manager import get_project_db_session
from post_db.allocation_models.project_users import ProjectUsers

logger = logging.getLogger('admin_user_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin - Users"],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
)

class ActionRequest(BaseModel):
    action: str

@router.get("/users", response_model=List[UserResponse])
async def list_users(
    user_service: UserService = Depends(get_user_service)
):
    users = await user_service.get_all()
    return [create_user_response(user) for user in users]

@router.get("/users/{username}", response_model=UserResponse)
async def get_user(
    username: str,
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return create_user_response(user)

@router.put("/users/{username}", response_model=UserResponse)
async def update_user(
    username: str,
    user_update: UserUpdate,
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    updated_user = await user_service.update_user(user, user_update)
    return create_user_response(updated_user)

@router.post("/users/{username}/suspend", response_model=SuccessResponse)
async def suspend_user(
    username: str,
    action_req: ActionRequest,
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_username(username)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    if action_req.action == "suspend":
        user.is_active = False
    elif action_req.action == "unsuspend":
        user.is_active = True
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid action")
    await user_service.db.commit()
    return SuccessResponse(success=True, message=f"User {username} has been {'suspended' if not user.is_active else 'unsuspended'}")

@router.post("/add-user", response_model=SuccessResponse)
async def add_user(req: AddUserRequest, user_service: UserService = Depends(get_user_service)):
    """
    Add a new user to the database using plaintext password and optional annotation mode.
    """
    try:
        # Hash the provided password
        hashed = hash_password(req.password)
        # Prepare UserCreate DTO
        user_data = UserCreate(
            username=req.username,
            password_hash=hashed,
            full_name=req.full_name,
            email=req.email,
            role=req.role,
            is_active=req.is_active
        )
        user = await user_service.create_user(user_data)
        if not user:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to create user")
    except SQLAlchemyError as e:
        logger.error(f"Database error in create_user: {e}")
        await user_service.db.rollback()

        # Check for specific constraint violations
        if "duplicate key value violates unique constraint" in str(e):
            if "users_email_key" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User with email '{req.email}' already exists"
                )
            elif "users_username_key" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User with username '{req.username}' already exists"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this information already exists"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}"
            )
    # Set annotator_mode if provided and valid
    if req.annotation_mode:
        try:
            mode_enum = AnnotatorMode(req.annotation_mode) if isinstance(req.annotation_mode, str) else req.annotation_mode
            user.annotator_mode = mode_enum
            await user_service.db.commit()
        except ValueError:
            # ignore invalid annotation_mode
            pass
    # NEW: If project_code supplied, also add to project database
    if req.project_code:
        try:

            async with get_project_db_session(req.project_code) as proj_session:
                proj_session.add(ProjectUsers(
                    user_id=user.id,
                    username=user.username,
                    role=user.role
                ))
                await proj_session.commit()
        except Exception as e:
            # Log but don't fail the main user creation
            # logger.error(f"Failed to add user to project {req.project_code}: {e}") # This line was commented out in the original file
            pass
    return SuccessResponse(success=True, message="User added successfully", data={"user": create_user_response(user)})

class AddClientRequest(BaseModel):
    """Schema for adding a client to the clients table"""
    client_name: str
    contact_info: Optional[dict] = None

    model_config = ConfigDict(from_attributes=True)

@router.post("/add-client", response_model=SuccessResponse)
async def add_client(
    req: AddClientRequest,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    Add a new client to the clients table (not users table).
    This creates a client entry that can be used for project management.
    """
    try:
        # Create new client entry (using auto-generated integer id)
        new_client = Clients(
            name=req.client_name,
            contact_info=req.contact_info or {}
        )

        db.add(new_client)
        await db.commit()

        logger.info(f"Created new client: id={new_client.id}, name='{new_client.name}'")

        return SuccessResponse(
            success=True,
            message="Client added successfully",
            data={
                "client": {
                    "id": new_client.id,
                    "name": new_client.name,
                    "contact_info": new_client.contact_info,
                    "created_at": new_client.created_at.isoformat() if new_client.created_at else None
                }
            }
        )

    except Exception as e:
        logger.error(f"Error creating client: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating client: {str(e)}"
        )

@router.get("/clients", response_model=SuccessResponse)
async def list_clients(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    List all clients from the clients table.
    """
    try:
        result = await db.execute(select(Clients).order_by(Clients.created_at.desc()))
        clients = result.scalars().all()

        clients_data = []
        for client in clients:
            clients_data.append({
                "id": client.id,
                "name": client.name,
                "username": client.username,
                "email": client.email,
                "created_at": client.created_at.isoformat() if client.created_at else None,
                "updated_at": client.updated_at.isoformat() if client.updated_at else None
            })

        return SuccessResponse(
            success=True,
            message=f"Found {len(clients_data)} clients",
            data={"clients": clients_data}
        )

    except Exception as e:
        logger.error(f"Error listing clients: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving clients: {str(e)}"
        )

@router.post("/flush-db",response_model=SuccessResponse)
async def flush_db(db: AsyncSession = Depends(get_master_db_session)):
    """
    Flush the image-related tables and reset admin settings indices, then flush Redis cache.
    """
    try:
        total_deleted = 0
        
        # Delete records using modern async patterns
        result1 = await db.execute(text("DELETE FROM image_annotation"))
        total_deleted += result1.rowcount
        
        result2 = await db.execute(text("DELETE FROM image_verification"))
        total_deleted += result2.rowcount
        
        result3 = await db.execute(text("DELETE FROM supervision"))
        total_deleted += result3.rowcount
        
        result4 = await db.execute(text("DELETE FROM datasets"))
        total_deleted += result4.rowcount
        
        result5 = await db.execute(text("DELETE FROM admin_settings"))
        total_deleted += result5.rowcount

        await db.commit()
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                detail=f"Error flushing database tables: {str(e)}")

    try:
        cache_flushed = await flush_cache()
    except Exception as e:
        cache_flushed = False
        cache_error = str(e)

    if cache_flushed:
        message = f"Database tables and Redis cache flushed successfully. {total_deleted} records have been deleted."
    else:
        warning = cache_error if 'cache_error' in locals() else "Redis cache flush failed"
        message = (f"Database tables flushed successfully, but {warning}. "
                        f"{total_deleted} records have been deleted.")

    return SuccessResponse(success=True, message=message)
