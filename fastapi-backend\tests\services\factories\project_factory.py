"""
Project data factory for creating consistent test projects across all service tests.
"""

import time
from typing import Dict, Any
from datetime import datetime, timedelta


class ProjectFactory:
    """Factory for creating test project data."""
    
    @staticmethod
    def create_project(
        project_code: str = None,
        connection_type: str = 'NAS-FTP',
        allocation_strategy: str = 'three_annotator_verification'
    ) -> Dict[str, Any]:
        """Create basic test project data."""
        timestamp = int(time.time())
        code = project_code or f'TEST_PROJECT_{timestamp % 10000}'
        
        return {
            'project_code': code,
            'project_name': f'Test Project {code}',
            'database_name': f'{code.lower()}_db',
            'connection_type': connection_type,
            'is_active': True,
            'created_at': datetime.now() - timedelta(days=30),
            'updated_at': datetime.now() - timedelta(days=1),
            'allocation_strategy_name': allocation_strategy
        }
    
    @staticmethod
    def create_minio_project(project_code: str = None) -> Dict[str, Any]:
        """Create project configured for MinIO storage."""
        project = ProjectFactory.create_project(
            project_code=project_code,
            connection_type='MinIO'
        )
        
        project['storage_config'] = {
            'endpoint': 'test-minio.com:9000',
            'access_key': 'test_access_key',
            'secret_key': 'test_secret_key',
            'bucket': f"{project['project_code'].lower()}-bucket",
            'use_ssl': False
        }
        
        return project
    
    @staticmethod
    def create_nas_project(project_code: str = None) -> Dict[str, Any]:
        """Create project configured for NAS-FTP storage."""
        project = ProjectFactory.create_project(
            project_code=project_code,
            connection_type='NAS-FTP'
        )
        
        project['storage_config'] = {
            'ftp_host': 'test-nas.com',
            'ftp_port': 21,
            'ftp_username': 'test_ftp_user',
            'ftp_password': 'test_ftp_pass',
            'base_path': f'/projects/{project["project_code"]}'
        }
        
        return project
    
    @staticmethod
    def create_hybrid_project(project_code: str = None) -> Dict[str, Any]:
        """Create project with hybrid storage configuration."""
        project = ProjectFactory.create_project(
            project_code=project_code,
            connection_type='Hybrid'
        )
        
        project['storage_config'] = {
            'primary': 'MinIO',
            'secondary': 'NAS-FTP',
            'routing_rules': {
                'video': 'MinIO',
                'audio': 'MinIO',
                'image': 'NAS-FTP',
                'document': 'NAS-FTP',
                'archive': 'NAS-FTP'
            },
            'file_size_threshold': 50 * 1024 * 1024,  # 50MB
            'cost_optimization': True,
            'redundancy_enabled': True
        }
        
        return project
    
    @staticmethod
    def create_project_credentials(
        project_code: str,
        connection_type: str = 'NAS-FTP'
    ) -> Dict[str, Any]:
        """Create project storage credentials."""
        if connection_type == 'MinIO':
            return {
                'endpoint': 'test-minio.com:9000',
                'access_key': 'test_access_key',
                'secret_key': 'test_secret_key',
                'bucket': f'{project_code.lower()}-bucket',
                'use_ssl': False
            }
        elif connection_type == 'NAS-FTP':
            return {
                'ftp_host': 'test-nas.com',
                'ftp_port': 21,
                'ftp_username': 'test_ftp_user',
                'ftp_password': 'test_ftp_pass',
                'base_path': f'/projects/{project_code}'
            }
        else:
            return {}
    
    @staticmethod
    def create_batch_configuration(
        files_per_batch: int = 50,
        content_type: str = 'image'
    ) -> Dict[str, Any]:
        """Create batch configuration for project."""
        return {
            'files_per_batch': files_per_batch,
            'content_type': content_type,
            'auto_assignment': True,
            'priority_scoring': True,
            'quality_threshold': 0.8,
            'max_concurrent_batches': 10
        }
