"""
Comprehensive unit tests for VerifierDataService.
Tests verification data retrieval, batch file management, and user workflow coordination.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
import json
from datetime import datetime

from app.services.verifier_data_service import VerifierDataService
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

class TestVerifierDataServiceUnit:
    """Unit tests for VerifierDataService with mocked dependencies."""
    
    @pytest.fixture
    def verifier_service(self):
        """VerifierDataService instance for testing."""
        return VerifierDataService()
    
    @pytest.fixture
    def mock_user_data(self):
        """Mock user data for testing."""
        return {
            'active_verifier': {
                'id': 101,
                'username': 'verifier_user_001',
                'role': 'verifier',
                'active_project': 'PROJECT_VERIFY_001'
            },
            'no_active_project': {
                'id': 102,
                'username': 'verifier_user_002',
                'role': 'verifier',
                'active_project': None
            },
            'admin_user': {
                'id': 103,
                'username': 'admin_user',
                'role': 'admin',
                'active_project': 'ADMIN_PROJECT_001'
            }
        }
    
    @pytest.fixture
    def mock_verification_data(self):
        """Mock verification data for testing."""
        return {
            'ready_batch': {
                'batch_identifier': 'VERIFY_BATCH_001',
                'total_files': 20,
                'completed_annotations': 20,
                'annotation_data': [
                    {
                        'file_path': '/images/img1.jpg',
                        'annotator_reviews': [
                            {'annotator_id': 201, 'confidence': 0.95, 'labels': ['cat']},
                            {'annotator_id': 202, 'confidence': 0.87, 'labels': ['cat']}
                        ]
                    },
                    {
                        'file_path': '/images/img2.jpg',
                        'annotator_reviews': [
                            {'annotator_id': 201, 'confidence': 0.92, 'labels': ['dog']},
                            {'annotator_id': 202, 'confidence': 0.89, 'labels': ['dog']}
                        ]
                    }
                ]
            },
            'incomplete_batch': {
                'batch_identifier': 'VERIFY_BATCH_002',
                'total_files': 15,
                'completed_annotations': 10,  # Not ready for verification
                'annotation_data': []
            },
            'conflicting_batch': {
                'batch_identifier': 'VERIFY_BATCH_003', 
                'total_files': 10,
                'completed_annotations': 10,
                'annotation_data': [
                    {
                        'file_path': '/images/conflict.jpg',
                        'annotator_reviews': [
                            {'annotator_id': 201, 'confidence': 0.85, 'labels': ['cat']},
                            {'annotator_id': 202, 'confidence': 0.80, 'labels': ['dog']}  # Conflicting label
                        ]
                    }
                ]
            }
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_success(self, verifier_service, mock_user_data):
        """Test successful retrieval of user's active project."""
        user_id = mock_user_data['active_verifier']['id']
        expected_project = mock_user_data['active_verifier']['active_project']
        
        # Mock database query
        mock_user = MagicMock()
        mock_user.active_project = expected_project
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.return_value = mock_result
            
            result = await verifier_service.get_user_active_project(user_id)
            
            assert result == expected_project
            assert mock_session.execute.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_no_project(self, verifier_service, mock_user_data):
        """Test retrieval when user has no active project."""
        user_id = mock_user_data['no_active_project']['id']
        
        mock_user = MagicMock()
        mock_user.active_project = None
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_user
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.return_value = mock_result
            
            result = await verifier_service.get_user_active_project(user_id)
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_user_not_found(self, verifier_service):
        """Test retrieval when user does not exist."""
        user_id = 999  # Non-existent user
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.return_value = mock_result
            
            result = await verifier_service.get_user_active_project(user_id)
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_allocation_strategy_success(self, verifier_service):
        """Test successful project allocation strategy retrieval."""
        project_code = 'STRATEGY_TEST_001'
        
        # Mock project query
        mock_project = MagicMock()
        mock_project.project_code = project_code
        mock_project.allocation_strategy_id = 5
        
        mock_project_result = AsyncMock()
        mock_project_result.scalar_one_or_none.return_value = mock_project
        
        # Mock strategy query
        mock_strategy = MagicMock()
        mock_strategy.id = 5
        mock_strategy.strategy_type = StrategyType.PARALLEL
        mock_strategy.num_annotators = 3
        mock_strategy.requires_verification = True
        
        mock_strategy_result = AsyncMock()
        mock_strategy_result.scalar_one_or_none.return_value = mock_strategy
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.side_effect = [mock_project_result, mock_strategy_result]
            
            result = await verifier_service.get_project_allocation_strategy(project_code)
            
            assert result is not None
            assert result.num_annotators == 3
            assert result.requires_verification is True

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_files_for_verification_ready(self, verifier_service, mock_verification_data):
        """Test getting batch files ready for verification."""
        project_code = 'VERIFY_READY_001'
        batch_data = mock_verification_data['ready_batch']
        
        with patch.object(verifier_service, '_find_ready_verification_batch') as mock_find:
            mock_find.return_value = batch_data
            
            with patch.object(verifier_service, '_get_batch_annotation_data') as mock_get_data:
                mock_get_data.return_value = batch_data['annotation_data']
                
                result = await verifier_service.get_batch_files_for_verification(project_code)
                
                assert result['success'] is True
                assert result['batch_identifier'] == batch_data['batch_identifier']
                assert len(result['files_for_verification']) == 2
                assert result['total_files'] == batch_data['total_files']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_files_for_verification_none_ready(self, verifier_service):
        """Test getting batch files when none are ready for verification."""
        project_code = 'NO_READY_001'
        
        with patch.object(verifier_service, '_find_ready_verification_batch') as mock_find:
            mock_find.return_value = None  # No ready batches
            
            result = await verifier_service.get_batch_files_for_verification(project_code)
            
            assert result['success'] is False
            assert 'no batches ready' in result['message'].lower()
            assert result['files_for_verification'] == []

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_annotation_reviews_comprehensive(self, verifier_service, mock_verification_data):
        """Test comprehensive annotation review retrieval."""
        project_code = 'REVIEWS_001'
        batch_identifier = 'REVIEW_BATCH_001'
        
        batch_data = mock_verification_data['ready_batch']
        
        with patch.object(verifier_service, '_query_batch_annotations') as mock_query:
            mock_query.return_value = {
                'batch_identifier': batch_identifier,
                'annotator_reviews': batch_data['annotation_data'],
                'consensus_data': {
                    'agreement_percentage': 95.0,
                    'conflicting_files': [],
                    'unanimous_files': ['/images/img1.jpg', '/images/img2.jpg']
                }
            }
            
            result = await verifier_service.get_batch_annotation_reviews(project_code, batch_identifier)
            
            assert result['success'] is True
            assert result['agreement_percentage'] == 95.0
            assert len(result['unanimous_files']) == 2
            assert len(result['conflicting_files']) == 0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_annotation_reviews_conflicts(self, verifier_service, mock_verification_data):
        """Test annotation review retrieval with conflicts."""
        project_code = 'CONFLICTS_001'
        batch_identifier = 'CONFLICT_BATCH_001'
        
        conflicting_data = mock_verification_data['conflicting_batch']
        
        with patch.object(verifier_service, '_query_batch_annotations') as mock_query:
            mock_query.return_value = {
                'batch_identifier': batch_identifier,
                'annotator_reviews': conflicting_data['annotation_data'],
                'consensus_data': {
                    'agreement_percentage': 60.0,  # Low agreement due to conflicts
                    'conflicting_files': ['/images/conflict.jpg'],
                    'unanimous_files': []
                }
            }
            
            result = await verifier_service.get_batch_annotation_reviews(project_code, batch_identifier)
            
            assert result['success'] is True
            assert result['agreement_percentage'] == 60.0
            assert len(result['conflicting_files']) == 1
            assert '/images/conflict.jpg' in result['conflicting_files']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_save_verification_result_accept(self, verifier_service):
        """Test saving verification result - accepting annotations."""
        project_code = 'SAVE_ACCEPT_001'
        batch_identifier = 'ACCEPT_BATCH_001'
        verification_data = {
            'verifier_id': 301,
            'batch_identifier': batch_identifier,
            'verification_results': [
                {
                    'file_path': '/images/img1.jpg',
                    'verification_decision': 'accept',
                    'final_labels': ['cat'],
                    'confidence': 0.95,
                    'comments': 'Clear annotation, good quality'
                }
            ]
        }
        
        with patch.object(verifier_service, '_validate_verification_data') as mock_validate:
            mock_validate.return_value = {'valid': True, 'errors': []}
            
            with patch.object(verifier_service, '_save_verification_to_database') as mock_save:
                mock_save.return_value = {
                    'success': True,
                    'files_verified': 1,
                    'batch_status': 'completed'
                }
                
                result = await verifier_service.save_verification_result(
                    project_code, verification_data
                )
                
                assert result['success'] is True
                assert result['files_verified'] == 1
                assert result['batch_status'] == 'completed'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_save_verification_result_reject(self, verifier_service):
        """Test saving verification result - rejecting annotations."""
        project_code = 'SAVE_REJECT_001'
        verification_data = {
            'verifier_id': 302,
            'batch_identifier': 'REJECT_BATCH_001',
            'verification_results': [
                {
                    'file_path': '/images/bad_annotation.jpg',
                    'verification_decision': 'reject',
                    'rejection_reason': 'Incorrect labeling',
                    'reassign_to_annotator': True,
                    'comments': 'Needs re-annotation'
                }
            ]
        }
        
        with patch.object(verifier_service, '_validate_verification_data') as mock_validate:
            mock_validate.return_value = {'valid': True, 'errors': []}
            
            with patch.object(verifier_service, '_save_verification_to_database') as mock_save:
                mock_save.return_value = {
                    'success': True,
                    'files_rejected': 1,
                    'files_reassigned': 1,
                    'batch_status': 'pending_reannotation'
                }
                
                result = await verifier_service.save_verification_result(
                    project_code, verification_data
                )
                
                assert result['success'] is True
                assert result['files_rejected'] == 1
                assert result['batch_status'] == 'pending_reannotation'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_verification_queue_priority(self, verifier_service):
        """Test getting verification queue with priority ordering."""
        project_code = 'QUEUE_PRIORITY_001'
        
        mock_queue_data = [
            {
                'batch_identifier': 'URGENT_BATCH_001',
                'priority': 'high',
                'ready_since': '2024-09-20T10:00:00Z',
                'total_files': 50,
                'estimated_time': '2 hours'
            },
            {
                'batch_identifier': 'NORMAL_BATCH_001',
                'priority': 'normal',
                'ready_since': '2024-09-20T08:00:00Z',
                'total_files': 30,
                'estimated_time': '1.5 hours'
            },
            {
                'batch_identifier': 'LOW_BATCH_001',
                'priority': 'low',
                'ready_since': '2024-09-19T14:00:00Z',
                'total_files': 20,
                'estimated_time': '1 hour'
            }
        ]
        
        with patch.object(verifier_service, '_get_verification_queue') as mock_get_queue:
            mock_get_queue.return_value = mock_queue_data
            
            result = await verifier_service.get_verification_queue(project_code, priority_order=True)
            
            assert result['success'] is True
            assert len(result['batches_ready']) == 3
            # Should be ordered by priority (high first)
            assert result['batches_ready'][0]['priority'] == 'high'
            assert result['estimated_total_time'] == '4.5 hours'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_verifier_performance_stats(self, verifier_service):
        """Test getting verifier performance statistics."""
        verifier_id = 401
        project_code = 'PERF_STATS_001'
        
        mock_stats = {
            'verifier_id': verifier_id,
            'total_files_verified': 500,
            'total_batches_completed': 25,
            'average_verification_time': 45.0,  # seconds per file
            'accuracy_rate': 96.5,  # percentage
            'rejection_rate': 8.2,   # percentage
            'consensus_agreement': 94.8  # agreement with other verifiers
        }
        
        with patch.object(verifier_service, '_calculate_verifier_stats') as mock_calculate:
            mock_calculate.return_value = mock_stats
            
            result = await verifier_service.get_verifier_performance_stats(verifier_id, project_code)
            
            assert result['success'] is True
            assert result['total_files_verified'] == 500
            assert result['accuracy_rate'] == 96.5
            assert result['average_verification_time'] == 45.0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_performance_large_batch_processing(self, verifier_service, performance_monitor,
                                                     service_performance_data):
        """Test performance with large batch verification."""
        project_code = 'LARGE_VERIFY_001'
        
        # Mock large batch with many files
        large_batch_data = {
            'batch_identifier': 'LARGE_BATCH_001',
            'total_files': 1000,
            'annotation_data': [
                {
                    'file_path': f'/images/large_img_{i}.jpg',
                    'annotator_reviews': [
                        {'annotator_id': 201, 'confidence': 0.9, 'labels': ['object']},
                        {'annotator_id': 202, 'confidence': 0.85, 'labels': ['object']}
                    ]
                } for i in range(1000)
            ]
        }
        
        with patch.object(verifier_service, '_find_ready_verification_batch') as mock_find:
            mock_find.return_value = large_batch_data
            
            with patch.object(verifier_service, '_get_batch_annotation_data') as mock_get_data:
                performance_monitor.start()
                
                mock_get_data.return_value = large_batch_data['annotation_data']
                
                result = await verifier_service.get_batch_files_for_verification(project_code)
                
                performance_monitor.stop()
                
                execution_time = performance_monitor.get_execution_time()
                # Should handle large batches efficiently
                assert execution_time < 2.0, f"Large batch processing took {execution_time}s"
                assert len(result['files_for_verification']) == 1000

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_security_verifier_authorization(self, verifier_service, security_test_data):
        """Test security - verifier authorization validation."""
        project_code = 'SECURITY_001'
        
        # Test with unauthorized user trying to access verification data
        unauthorized_user_id = 999
        
        with patch.object(verifier_service, '_validate_verifier_permissions') as mock_validate:
            mock_validate.return_value = {
                'authorized': False,
                'reason': 'User is not assigned as verifier for this project'
            }
            
            result = await verifier_service.get_batch_files_for_verification(
                project_code, verifier_id=unauthorized_user_id
            )
            
            assert result['success'] is False
            assert 'not authorized' in result['error'].lower()

    @pytest.mark.unit
    def test_data_validation_verification_input(self, verifier_service, security_test_data):
        """Test validation of verification input data."""
        # Test malicious input sanitization
        malicious_verification_data = {
            'verifier_id': security_test_data['malicious_inputs'][0],  # SQL injection attempt
            'batch_identifier': security_test_data['malicious_inputs'][1],  # XSS attempt
            'verification_results': [
                {
                    'file_path': '../../../etc/passwd',  # Path traversal attempt
                    'verification_decision': 'accept',
                    'comments': security_test_data['malicious_inputs'][2]  # More malicious input
                }
            ]
        }
        
        with patch.object(verifier_service, '_validate_verification_data') as mock_validate:
            mock_validate.return_value = {
                'valid': False,
                'errors': [
                    'Invalid verifier_id format',
                    'Invalid batch_identifier format', 
                    'Suspicious file path detected',
                    'Invalid characters in comments'
                ]
            }
            
            result = verifier_service._validate_verification_data(malicious_verification_data)
            
            assert result['valid'] is False
            assert len(result['errors']) == 4

    @pytest.mark.unit
    def test_memory_usage_large_verification_data(self, verifier_service, service_performance_data):
        """Test memory usage with large verification datasets."""
        import sys
        
        # Simulate large verification data
        large_verification_data = []
        for i in range(5000):  # 5000 files
            file_data = {
                'file_path': f'/verification/file_{i}.jpg',
                'annotator_reviews': [
                    {
                        'annotator_id': 201 + (i % 3),
                        'confidence': 0.8 + (i % 20) * 0.01,
                        'labels': [f'class_{i % 10}'],
                        'annotation_time': datetime.now().isoformat(),
                        'metadata': {'quality_score': i % 100}
                    } for _ in range(3)  # 3 annotator reviews per file
                ]
            }
            large_verification_data.append(file_data)
        
        initial_size = sys.getsizeof(large_verification_data)
        
        # Simulate processing for verification
        processed_data = {}
        for file_data in large_verification_data:
            file_path = file_data['file_path']
            reviews = file_data['annotator_reviews']
            
            consensus = {
                'agreement_score': sum(r['confidence'] for r in reviews) / len(reviews),
                'label_consensus': max(set(r['labels'][0] for r in reviews), 
                                     key=[r['labels'][0] for r in reviews].count),
                'needs_verification': len(set(r['labels'][0] for r in reviews)) > 1
            }
            processed_data[file_path] = consensus
        
        final_size = sys.getsizeof(processed_data)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['batch_processing']
        assert memory_increase < max_memory, f"Verification data memory usage {memory_increase}MB exceeds limit"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_verification_operations(self, verifier_service):
        """Test concurrent verification operations by multiple verifiers."""
        import asyncio
        
        project_code = 'CONCURRENT_VERIFY_001'
        verifier_ids = [401, 402, 403]
        
        with patch.object(verifier_service, '_find_ready_verification_batch') as mock_find:
            # Different batches for different verifiers
            def side_effect(*args, **kwargs):
                verifier_id = kwargs.get('verifier_id', 401)
                return {
                    'batch_identifier': f'CONCURRENT_BATCH_{verifier_id}',
                    'total_files': 20,
                    'annotation_data': [{'file_path': f'/img_{verifier_id}_{i}.jpg'} for i in range(20)]
                }
            
            mock_find.side_effect = side_effect
            
            # Create concurrent verification tasks
            tasks = []
            for verifier_id in verifier_ids:
                task = verifier_service.get_batch_files_for_verification(
                    project_code, verifier_id=verifier_id
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All verifications should complete without conflicts
            exceptions = [r for r in results if isinstance(r, Exception)]
            assert len(exceptions) == 0
            
            successful_results = [r for r in results if r.get('success', False)]
            assert len(successful_results) == len(verifier_ids)
