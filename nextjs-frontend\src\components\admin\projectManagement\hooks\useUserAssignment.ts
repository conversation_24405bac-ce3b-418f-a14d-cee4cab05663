import { useState } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { 
  UserInfo, 
  AssignedUserInfo, 
  StrategyDetails, 
  ProjectRegistryResponse 
} from '../types';

import {  validateAnnotatorAssignment } from '../utils';
import { API_BASE_URL } from "../../../../lib/api";
export const useUserAssignment = () => {
  const [availableAnnotators, setAvailableAnnotators] = useState<UserInfo[]>([]);
  const [availableVerifiers, setAvailableVerifiers] = useState<UserInfo[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [selectedAnnotators, setSelectedAnnotators] = useState<number[]>([]);
  const [selectedVerifiers, setSelectedVerifiers] = useState<number[]>([]);
  const [assignedAnnotators, setAssignedAnnotators] = useState<AssignedUserInfo[]>([]);
  const [assignedVerifiers, setAssignedVerifiers] = useState<AssignedUserInfo[]>([]);
  const [loadingAssignedUsers, setLoadingAssignedUsers] = useState(false);
  const [assignmentLoading, setAssignmentLoading] = useState(false);

  // Fetch available users for assignment
  const fetchAvailableUsers = async (projectId: number) => {
    try {
      setLoadingUsers(true);
      
      // Fetch available annotators
      const annotatorsResponse = await authFetch(`${API_BASE_URL}/users/annotators?project_id=${projectId}`);
      if (!annotatorsResponse.ok) {
        throw new Error(`HTTP error! status: ${annotatorsResponse.status}`);
      }
      const annotatorsData = await annotatorsResponse.json();
      setAvailableAnnotators(annotatorsData.users || []);
      
      // Fetch available verifiers
      const verifiersResponse = await authFetch(`${API_BASE_URL}/users/verifiers?project_id=${projectId}`);
      if (!verifiersResponse.ok) {
        throw new Error(`HTTP error! status: ${verifiersResponse.status}`);
      }
      const verifiersData = await verifiersResponse.json();
      setAvailableVerifiers(verifiersData.users || []);
      
    } catch (error) {
      console.error("Error fetching available users:", error);
      showToast.error("Failed to load available users");
    } finally {
      setLoadingUsers(false);
    }
  };

  // Fetch already assigned users
  const fetchAssignedUsers = async (projectId: number) => {
    try {
      setLoadingAssignedUsers(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${projectId}/summary`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Set assigned users by role
      setAssignedAnnotators(data.annotators || []);
      setAssignedVerifiers(data.verifiers || []);
      
    } catch (error) {
      console.error("Error fetching assigned users:", error);
      showToast.error("Failed to load assigned users");
    } finally {
      setLoadingAssignedUsers(false);
    }
  };

  // Assign users to project
  const assignUsers = async (
    project: ProjectRegistryResponse,
    role: 'annotators' | 'verifiers',
    userIds: number[],
    strategyDetails?: StrategyDetails | null,
    onSuccess?: () => void
  ) => {
    if (userIds.length === 0) return false;
    
    // Validate annotator assignment against strategy requirements
    if (role === 'annotators' && strategyDetails) {
      const validation = validateAnnotatorAssignment(
        assignedAnnotators.length,
        userIds.length,
        strategyDetails.num_annotators
      );
      
      if (!validation.isValid) {
        showToast.warning(validation.message!);
        return false;
      }
    }
    
    try {
      setAssignmentLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/${role}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_ids: userIds }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `Failed to assign ${role}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success(`Successfully assigned ${data.assigned_users.length} ${role}`);
        
        // Reset selections
        if (role === 'annotators') setSelectedAnnotators([]);
        else if (role === 'verifiers') setSelectedVerifiers([]);
        
        // Remove assigned users from available lists immediately
        if (role === 'annotators') {
          setAvailableAnnotators(prev => prev.filter(user => !userIds.includes(user.id)));
        } else if (role === 'verifiers') {
          setAvailableVerifiers(prev => prev.filter(user => !userIds.includes(user.id)));
        }
        
        // Refresh assigned users list
        await fetchAssignedUsers(project.id);
        onSuccess?.();
        return true;
      } else {
        showToast.warning(`Some users could not be assigned: ${data.message}`);
        return false;
      }
    } catch (error) {
      console.error(`Error assigning ${role}:`, error);
      showToast.error(`Failed to assign ${role}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setAssignmentLoading(false);
    }
  };

  // Remove user from project
  const removeUser = async (
    project: ProjectRegistryResponse,
    role: 'annotators' | 'verifiers',
    userId: number,
    onSuccess?: () => void
  ) => {
    try {
      setAssignmentLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/${role}/${userId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || `Failed to remove ${role.slice(0, -1)}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success(`Successfully removed ${role.slice(0, -1)}`);
        
        // Add removed user back to available lists immediately
        if (role === 'annotators') {
          const removedUser = assignedAnnotators.find(user => user.id === userId);
          if (removedUser) {
            setAvailableAnnotators(prev => [...prev, {
              id: removedUser.id,
              username: removedUser.username,
              full_name: removedUser.full_name,
              email: removedUser.email,
              skills: null,
              max_concurrent_projects: 3,
              current_projects: 0,
              max_concurrent_batches: 5,
              overall_quality_score: 0
            }]);
          }
        } else if (role === 'verifiers') {
          const removedUser = assignedVerifiers.find(user => user.id === userId);
          if (removedUser) {
            setAvailableVerifiers(prev => [...prev, {
              id: removedUser.id,
              username: removedUser.username,
              full_name: removedUser.full_name,
              email: removedUser.email,
              skills: null,
              max_concurrent_projects: 3,
              current_projects: 0,
              max_concurrent_batches: 5,
              overall_quality_score: 0
            }]);
          }
        }
        
        // Refresh assigned users list
        await fetchAssignedUsers(project.id);
        onSuccess?.();
        return true;
      } else {
        showToast.warning(`Could not remove user: ${data.message}`);
        return false;
      }
    } catch (error) {
      console.error(`Error removing ${role.slice(0, -1)}:`, error);
      showToast.error(`Failed to remove ${role.slice(0, -1)}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return false;
    } finally {
      setAssignmentLoading(false);
    }
  };

  // Toggle user selection
  const toggleUserSelection = (role: 'annotators' | 'verifiers', userId: number) => {
    if (role === 'annotators') {
      setSelectedAnnotators(prev => 
        prev.includes(userId) 
          ? prev.filter(id => id !== userId)
          : [...prev, userId]
      );
    } else {
      setSelectedVerifiers(prev => 
        prev.includes(userId) 
          ? prev.filter(id => id !== userId)
          : [...prev, userId]
      );
    }
  };

  // Clear selections
  const clearSelections = () => {
    setSelectedAnnotators([]);
    setSelectedVerifiers([]);
  };

  // Clear all state (useful when closing modals)
  const clearState = () => {
    setAvailableAnnotators([]);
    setAvailableVerifiers([]);
    setSelectedAnnotators([]);
    setSelectedVerifiers([]);
    setAssignedAnnotators([]);
    setAssignedVerifiers([]);
  };

  return {
    // State
    availableAnnotators,
    availableVerifiers,
    loadingUsers,
    selectedAnnotators,
    selectedVerifiers,
    assignedAnnotators,
    assignedVerifiers,
    loadingAssignedUsers,
    assignmentLoading,
    
    // Actions
    fetchAvailableUsers,
    fetchAssignedUsers,
    assignUsers,
    removeUser,
    toggleUserSelection,
    clearSelections,
    clearState,
  };
};
