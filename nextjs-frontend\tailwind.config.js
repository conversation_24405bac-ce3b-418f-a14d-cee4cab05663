/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/utils/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#0D47A1',
        secondary: '#1a3b5d',
        'text-primary': '#0D47A1',
        'text-secondary': '#4a6385',
        blue: {
          50: '#f0f7ff',
          100: '#e0eefe',
          200: '#bae0fd',
          300: '#7dcffd',
          400: '#39b7fa',
          500: '#0e9be0',
          600: '#0084c7',
          700: '#0069a0',
          800: '#005885',
          900: '#0a4970',
        }
      },
      fontFamily: {
        sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
        serif: ['var(--font-poppins)', 'Georgia', 'serif'],
      },
      animation: {
        'float': 'floatAnimation 15s ease-in-out infinite',
      },
      keyframes: {
        floatAnimation: {
          '0%': { transform: 'translateY(0) rotate(0deg)' },
          '50%': { transform: 'translateY(-20px) rotate(5deg)' },
          '100%': { transform: 'translateY(0) rotate(0deg)' },
        },
        fadeIn: { 
          'from': { opacity: '0' }, 
          'to': { opacity: '1' }, 
        },
        fadeInDown: { 
          'from': { opacity: '0', transform: 'translateY(-30px)' }, 
          'to': { opacity: '1', transform: 'translateY(0)' }, 
        },
        fadeInUp: { 
          'from': { opacity: '0', transform: 'translateY(30px)' }, 
          'to': { opacity: '1', transform: 'translateY(0)' }, 
        },
      },
    },
  },
  plugins: [],
} 