import os
import logging
import ftplib
import io
import asyncio
from typing import Optional, List, Dict, Any, Union
from .storage_base import StorageConnector
from .ftp_pool import FTPPool
from schemas.FTPConfig import FTPConfig

logger = logging.getLogger('ftp_connector')

class FTPNASConnector(StorageConnector):
    def __init__(self, config: FTPConfig):
        # Construct FTP URL for base class
        ftp_url = f"ftp://{config.host}:{config.port}"
        super().__init__(ftp_url, config.user, config.pwd)
        self.logger = logging.getLogger('FTPNASConnector')
        self.config = config

        try:
            self.logger.info(f"Initializing FTP pool with host={config.host}, port={config.port}, timeout={config.timeout}s")
            self.pool = FTPPool(
                host=config.host,
                port=config.port,
                username=config.user,
                password=config.pwd,
                size=config.size,
                tls=config.tls,
                timeout=config.timeout
            )
        except Exception as e:
            self.logger.error(f"Error initializing FTP pool: {str(e)}")
            self.authenticated = False
            raise

    async def authenticate(self) -> bool:
        try:
            self.logger.info(f"Authenticating to FTP server {self.config.host}:{self.config.port}")
            async with self.pool.acquire() as ftp:
                await asyncio.to_thread(ftp.voidcmd, "NOOP")
                self.authenticated = True
                self.logger.info("FTP authentication successful")
                return True
        except Exception as e:
            self.logger.error(f"FTP authentication error: {str(e)}")
            self.authenticated = False
            return False
        
    # ------------- directory operations -------------
    async def list_directory(self, path: str = "/") -> List[Dict[str, str]]:
        items: List[str] = []
        try:
            async with self.pool.acquire() as ftp:
                self.logger.info(f"Listing directory: {path}")
                try:
                    await asyncio.to_thread(ftp.cwd, path)
                    def callback(line):
                        items.append(line)
                    await asyncio.to_thread(ftp.retrlines, "LIST", callback)
                except ftplib.error_perm as e:
                    self.logger.error(f"Permission error listing directory {path}: {str(e)}")
                    return []
                except Exception as e:
                    self.logger.error(f"Error listing directory {path}: {str(e)}")
                    return []

            dirs, files = [], []
            for line in items:
                try:
                    parts = line.split(None, 8)
                    if len(parts) < 9:
                        self.logger.warning(f"Skipping invalid line in LIST result: {line}")
                        continue
                    ftype = parts[0][0]
                    name = parts[8]
                    item_type = "directory" if ftype == "d" else "file"
                    item_path = f"{path.rstrip('/')}/{name}"
                    if item_type == "directory":
                        dirs.append({"name": name, "path": item_path, "type": item_type})
                    else:
                        files.append({"name": name, "path": item_path, "type": item_type})
                except Exception as e:
                    self.logger.warning(f"Error parsing line '{line}': {str(e)}")
                    continue
            return dirs + files
        except Exception as e:
            self.logger.error(f"General error in list_directory for {path}: {str(e)}")
            return []

    async def directory_exists(self, directory_path: str) -> bool:
        try:
            normalized_path = self.normalize_path(directory_path)
            self.logger.debug(f"Checking if directory exists: {normalized_path}")
            async with self.pool.acquire() as ftp:
                cur = await asyncio.to_thread(ftp.pwd)
                try:
                    await asyncio.to_thread(ftp.cwd, normalized_path)
                    await asyncio.to_thread(ftp.cwd, cur)
                    return True
                except Exception:
                    return False
        except Exception as e:
            self.logger.error(f"Error checking if directory exists: {str(e)}")
            return False
        
    async def create_directory(self, directory_path: str) -> bool:
        """Create a single directory under its parent path."""
        normalized_path = self.normalize_path(directory_path)
        parent_dir, folder = self.split_path(normalized_path)
        try:
            async with self.pool.acquire() as ftp:
                # Change into parent directory
                await asyncio.to_thread(ftp.cwd, parent_dir)
                try:
                    # Check if the target folder exists
                    await asyncio.to_thread(ftp.cwd, folder)
                    return True
                except Exception:
                    # Folder doesn't exist, attempt to create it
                    await asyncio.to_thread(ftp.mkd, folder)
                    self.logger.info(f"Successfully created directory: {normalized_path}")
                    return True
        except ftplib.error_perm as e:
            error_msg = str(e)
            if "550" in error_msg and "Permission denied" in error_msg:
                raise ValueError(f"Permission denied: Cannot create directory '{folder}' in '{parent_dir}'. Please check FTP user permissions.")
            self.logger.error(f"FTP error creating directory {directory_path}: {error_msg}")
            return False
        except Exception as e:
            self.logger.error(f"Error creating directory {directory_path}: {str(e)}")
            return False

    # ------------- file operations -------------
    async def get_file_info(self, file_path: str) -> Dict[str, Any]:
        try:
            normalized_path = self.normalize_path(file_path)
            directory, filename = self.split_path(normalized_path)
            async with self.pool.acquire() as ftp:
                await asyncio.to_thread(ftp.cwd, directory)
                size = await asyncio.to_thread(ftp.size, filename)
            return {"name": filename, "path": normalized_path, "size": size, "type": "file"}
        except Exception as e:
            self.logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return {"name": os.path.basename(file_path), "path": file_path, "size": 0, "type": "file"}

    async def get_file_content(self, file_path: str) -> Optional[bytes]:
        try:
            normalized_path = self.normalize_path(file_path)
            directory, filename = self.split_path(normalized_path)
            buf = io.BytesIO()
            async with self.pool.acquire() as ftp:
                await asyncio.to_thread(ftp.cwd, directory)
                await asyncio.to_thread(ftp.retrbinary, f"RETR {filename}", buf.write)
            content = buf.getvalue()
            self.logger.debug(f"Retrieved file: {normalized_path}, size: {len(content)} bytes")
            return content
        except Exception as e:
            self.logger.error(f"Error getting file content for {file_path}: {str(e)}")
            return None

    async def file_exists(self, file_path: str) -> bool:
        try:
            normalized_path = self.normalize_path(file_path)
            directory, filename = self.split_path(normalized_path)
            async with self.pool.acquire() as ftp:
                try:
                    await asyncio.to_thread(ftp.cwd, directory)
                    file_list: List[str] = []
                    def callback(name):
                        file_list.append(name)
                    await asyncio.to_thread(ftp.retrlines, 'NLST', callback)
                    return filename in file_list
                except Exception:
                    return False
        except Exception as e:
            self.logger.error(f"Error checking if file exists for {file_path}: {str(e)}")
            return False

    async def save_file(self, file_path: str, content: Union[str, bytes]) -> bool:
        try:
            normalized_path = self.normalize_path(file_path)
            directory, filename = self.split_path(normalized_path)
            if isinstance(content, str):
                content = content.encode("utf-8")
            async with self.pool.acquire() as ftp:
                try:
                    await self._ensure_dirs(ftp, directory)
                    await asyncio.to_thread(ftp.cwd, directory)
                    await asyncio.to_thread(ftp.storbinary, f"STOR {filename}", io.BytesIO(content))
                    self.logger.info(f"File saved successfully: {normalized_path}")
                    return True
                except ftplib.error_perm as e:
                    error_msg = str(e)
                    if "550" in error_msg and "Permission denied" in error_msg:
                        self.logger.error(f"Permission denied saving file {filename}: {error_msg}")
                        raise ValueError(f"Permission denied: Cannot save file '{filename}' in '{directory}'. Please check FTP user permissions.")
                    else:
                        self.logger.error(f"FTP error saving file {filename}: {error_msg}")
                        raise
        except ValueError:
            raise
        except Exception as e:
            self.logger.error(f"Error saving file {file_path}: {str(e)}")
            return False

    # ------------- helpers -------------
    async def _ensure_dirs(self, ftp, target: str) -> None:
        target = self.normalize_path(target)
        try:
            await asyncio.to_thread(ftp.cwd, "/")
        except Exception as e:
            self.logger.error(f"Cannot access root directory: {str(e)}")
            raise
        path_parts = [p for p in target.split('/') if p]
        current_path = ""
        for part in path_parts:
            current_path += f"/{part}"
            try:
                # Try to change into the directory
                await asyncio.to_thread(ftp.cwd, part)
            except Exception as e:
                error_msg = str(e)
                # If directory does not exist, create it
                if ("No such file" in error_msg or "No such directory" in error_msg):
                    try:
                        await asyncio.to_thread(ftp.mkd, part)
                        await asyncio.to_thread(ftp.cwd, part)
                    except Exception as create_error:
                        create_msg = str(create_error)
                        # Handle permission denied on creation
                        if isinstance(create_error, ftplib.error_perm) and "Permission denied" in create_msg:
                            self.logger.error(f"Permission denied creating directory {part}: {create_msg}")
                            raise ValueError(f"Permission denied: Cannot create directory '{part}' in '{current_path}'. Please check FTP user permissions.")
                        else:
                            self.logger.error(f"Error creating directory {part}: {create_msg}")
                            raise
                else:
                    self.logger.error(f"Error accessing directory {part}: {error_msg}")
                    raise