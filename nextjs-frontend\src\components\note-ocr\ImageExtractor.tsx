"use client";

import React, { useState, useRef } from "react";
import Link from "next/link";
import {
  FaArrowLeft,
  FaCloudUploadAlt,
  FaMagic,
  FaSpinner,
  FaFileAlt,
  FaClipboard,
  FaEdit,
  FaDownload,
  FaPaperPlane,
} from "react-icons/fa";
import Image from "next/image";

type ChatMessage = { role: "user" | "assistant" | "system"; content: string };

// Base URL for backend API
import { API_BASE_URL } from "@/lib/api";

const API_BASE = API_BASE_URL;

export default function ImageExtractor() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [imageData, setImageData] = useState<{ image_id: string } | null>(null);

  const [mode, setMode] = useState<"extract" | "chat" | null>(null);
  const [ocrText, setOcrText] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [editing, setEditing] = useState(false);

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    { role: "system", content: "You can ask questions about this image." },
  ]);
  const [chatInput, setChatInput] = useState("");
  const [isChatProcessing, setIsChatProcessing] = useState(false);
  const [prompt, setPrompt] = useState<string>("");

  // Handle file selection
  function handleFile(file: File) {
    if (!file.type.startsWith("image/")) return;
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    // Upload to server
    const form = new FormData();
    form.append("image", file);
    fetch(`${API_BASE}/NoteOCR/images/upload`, { method: "POST", body: form })
      .then((res) => res.json())
      .then((data) => setImageData(data.success ? data : null))
      .catch(() => setImageData(null));
  }

  // Drag events
  function handleDragOver(e: React.DragEvent) {
    e.preventDefault();
    setDragOver(true);
  }
  function handleDragLeave() {
    setDragOver(false);
  }
  function handleDrop(e: React.DragEvent) {
    e.preventDefault();
    setDragOver(false);
    if (e.dataTransfer.files.length) handleFile(e.dataTransfer.files[0]);
  }

  // File input change
  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files?.[0]) handleFile(e.target.files[0]);
  }

  // Extract text
  function handleExtract() {
    if (!imageData) return;
    setIsProcessing(true);
    fetch(`${API_BASE}/NoteOCR/images/extract-ocr`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(
        prompt.trim()
          ? { image_id: imageData.image_id, prompt: prompt.trim() }
          : { image_id: imageData.image_id }
      ),
    })
      .then((res) => res.json())
      .then((data) => {
        setIsProcessing(false);
        if (data.success) setOcrText(data.text);
      })
      .catch(() => setIsProcessing(false));
  }

  // Send chat
  function handleChatSend() {
    if (!imageData || !chatInput.trim()) return;
    setIsChatProcessing(true);
    const msg = chatInput.trim();
    setChatMessages((prev) => [...prev, { role: "user", content: msg }]);
    setChatInput("");
    fetch(`${API_BASE}/NoteOCR/images/chat`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        image_id: imageData.image_id,
        query: msg,
        chat_history: chatMessages,
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        setIsChatProcessing(false);
        if (data.success)
          setChatMessages((prev) => [
            ...prev,
            { role: "assistant", content: data.response },
          ]);
      })
      .catch(() => setIsChatProcessing(false));
  }

  // Copy and download
  function copyText() {
    navigator.clipboard.writeText(ocrText);
  }
  function downloadText() {
    const blob = new Blob([ocrText], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "ocr.txt";
    a.click();
  }

  return (
    <div className="min-h-screen bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-center relative mb-8">
          <div className="text-center flex-1">
            <h1 className="text-3xl md:text-4xl font-bold text-secondary">
              Image Extractor
            </h1>
            <div className="w-20 h-1 bg-[#0D47A1] mx-auto mt-2 rounded"></div>
            <p className="text-gray-500 mt-2">
              Extract text from any image with powerful OCR technology
            </p>
          </div>
          <Link
            href="/note-ocr"
            className="absolute right-0 inline-flex items-center px-4 py-2 bg-gradient-to-br from-[#0D47A1] to-[#1159B8] rounded-full text-white hover:bg-gray-100 transition no-underline"
          >
            <FaArrowLeft className="mr-2" /> Back to Dashboard
          </Link>
        </div>
        {/* Panels */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="bg-white border border-gray-200 rounded-2xl p-6">
            <h3 className="text-xl font-semibold mb-4">Upload Image</h3>
            <div
              className={`border-2 border-dashed ${
                dragOver ? "border-[#0D47A1]" : "border-gray-300"
              } rounded-lg h-48 flex flex-col items-center justify-center cursor-pointer transition`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <FaCloudUploadAlt className="text-[#0D47A1] text-4xl mb-2" />
              <p className="text-text-secondary">Drag & drop your image here</p>
              <p className="text-text-secondary">or</p>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  fileInputRef.current?.click();
                }}
                className="mt-2 px-4 py-2 bg-[#0D47A1] text-white rounded-lg"
              >
                Browse Files
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleFileChange}
              />
            </div>
            <div className="mt-6 h-40 bg-gray-100 rounded-lg flex items-center justify-center text-gray-400">
              {previewUrl ? (
                <Image
                  src={previewUrl}
                  alt="Preview"
                  className="max-h-full max-w-full"
                  width={400} // choose a default size, adjust as needed
                  height={160}
                  style={{ objectFit: "contain" }}
                />
              ) : (
                "Image preview will appear here"
              )}
            </div>
          </div>
          {/* Result Section */}
          <div className="bg-white border border-gray-200 rounded-2xl p-6 flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Results</h3>
              {mode && (
                <button
                  onClick={() => setMode(null)}
                  className="px-3 py-1 bg-gray-100 rounded hover:bg-gray-200"
                >
                  Change Mode
                </button>
              )}
            </div>
            {!mode && (
              <div className="bg-gray-50 p-6 rounded-xl flex flex-col items-center">
                <p className="text-lg font-medium mb-4">Choose Mode:</p>
                <div className="flex space-x-4">
                  <button
                    disabled={!imageData}
                    onClick={() => setMode("extract")}
                    className="px-6 py-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 disabled:opacity-50"
                  >
                    Extract with System Prompt
                  </button>
                  <button
                    disabled={!imageData}
                    onClick={() => setMode("chat")}
                    className="px-6 py-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 disabled:opacity-50"
                  >
                    Chat with Image
                  </button>
                </div>
                <div className="mt-12 flex flex-col items-center text-gray-400">
                  <FaFileAlt className="text-3xl mb-2" />
                  <p>Choose a mode above to process your image</p>
                </div>
              </div>
            )}
            {mode === "extract" && (
              <div className="mt-4 flex-1 flex flex-col">
                <textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Enter custom prompt (optional)"
                  className="w-full p-2 border rounded mb-2"
                  rows={3}
                />
                <button
                  onClick={handleExtract}
                  disabled={isProcessing}
                  className="px-4 py-2 bg-[#0D47A1] text-white rounded disabled:opacity-50 flex items-center justify-center"
                >
                  {isProcessing ? (
                    <FaSpinner className="animate-spin mr-2" />
                  ) : (
                    <FaMagic className="mr-2" />
                  )}
                  {isProcessing ? "Processing..." : "Extract Data"}
                </button>
                {ocrText && (
                  <div className="mt-4 flex-1 flex flex-col">
                    <div className="flex justify-end space-x-2 mb-2">
                      <button
                        onClick={() => setEditing(true)}
                        className="p-2 bg-gray-100 rounded"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={downloadText}
                        className="p-2 bg-gray-100 rounded"
                      >
                        <FaDownload />
                      </button>
                      <button
                        onClick={copyText}
                        className="p-2 bg-gray-100 rounded"
                      >
                        <FaClipboard />
                      </button>
                    </div>
                    {editing ? (
                      <textarea
                        value={ocrText}
                        onChange={(e) => setOcrText(e.target.value)}
                        className="w-full h-32 p-2 border rounded"
                      />
                    ) : (
                      <div className="p-4 bg-gray-50 rounded flex-1 overflow-y-auto whitespace-pre-wrap">
                        {ocrText}
                      </div>
                    )}
                    {editing && (
                      <div className="mt-2 flex justify-end space-x-2">
                        <button
                          onClick={() => setEditing(false)}
                          className="px-4 py-2 bg-gray-200 rounded"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={() => setEditing(false)}
                          className="px-4 py-2 bg-primary text-white rounded"
                        >
                          Save
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
            {mode === "chat" && (
              <div className="mt-4 flex flex-col flex-1">
                <div className="flex-1 p-2 overflow-y-auto h-64 border rounded mb-2">
                  {chatMessages.map((m, i) => (
                    <div
                      key={i}
                      className={m.role === "user" ? "text-right" : "text-left"}
                    >
                      <span className="inline-block p-2 rounded bg-gray-100">
                        {m.content}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleChatSend()}
                    className="flex-1 p-2 border rounded"
                    placeholder="Ask about this image..."
                  />
                  <button
                    onClick={handleChatSend}
                    disabled={isChatProcessing}
                    className="px-4 py-2 bg-[#0D47A1] text-white rounded disabled:opacity-50"
                  >
                    {isChatProcessing ? (
                      <FaSpinner className="animate-spin" />
                    ) : (
                      <FaPaperPlane />
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
