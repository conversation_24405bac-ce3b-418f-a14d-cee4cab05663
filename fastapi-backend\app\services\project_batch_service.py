"""
Service for managing project batches and files.
Handles business logic for batch creation and file registration.
"""

import os
import json
import logging
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from urllib.parse import unquote
from repositories.project_db_repository import ProjectDBRepository
from utils.media_utils import get_media_from_folder
from post_db.allocation_models.allocation_batches import BatchStatus
from post_db.allocation_models.files_registry import FileType
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select, update, text
from sqlalchemy.ext.asyncio import AsyncSession
from core.nas_connector import get_ftp_connector_from_credentials
from post_db.master_models.allocation_strategies import AllocationStrategies
from post_db.master_models.users import users
from repositories.project_db_repository import ProjectDBRepository
from post_db.master_models.user_project_access import UserProjectAccess
from post_db.master_models.users import users
from core.session_manager import get_project_db_session

logger = logging.getLogger(__name__)

class ProjectBatchService:
    """Service for managing project batches and files."""
    
    def __init__(self):
        self.default_batch_size = 20  # Default batch size
        self.repository = ProjectDBRepository()
    
    async def get_project_info(self, project_code: str) -> Dict[str, Any]:
        """
        Get project information from the projects_registry.
        
        Args:
            project_code: The project code
            
        Returns:
            Dict: Project information
        """
        try:
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project:
                    raise ValueError(f"Project with code {project_code} not found in master DB")
                
                # Get allocation strategy info if available
                allocation_strategy = None
                num_annotators = 1  # Default value
                
                if project.allocation_strategy_id:
                    from post_db.master_models.allocation_strategies import AllocationStrategies
                    strategy_result = await session.execute(
                        select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                    )
                    allocation_strategy = strategy_result.scalar_one_or_none()
                    if allocation_strategy:
                        num_annotators = allocation_strategy.num_annotators
                
                return {
                    'id': project.id,
                    'project_code': project.project_code,
                    'project_name': project.project_name,
                    'project_type': project.project_type,
                    'folder_path': project.folder_path,
                    'credentials': project.credentials,
                    'connection_type': project.connection_type,
                    'batch_size': project.batch_size,
                    'database_name': project.database_name,
                    'allocation_strategy_id': project.allocation_strategy_id,
                    'num_annotators': num_annotators,
                    'allocation_strategy': allocation_strategy
                }
                
        except Exception as e:
            logger.error(f"Error getting project info for {project_code}: {e}")
            raise
            
    async def create_batches_from_folder(self,
                                       project_code: str,
                                       folder_path: str = None,
                                       files_per_batch: int = None,
                                       content_type: str = None) -> Tuple[bool, str, int]:
        """
        Create allocation batches from files in a folder.
        
        Args:
            project_code: The project code
            folder_path: Path to the folder containing files (optional, will use project registry if not provided)
            files_per_batch: Number of files per batch (optional, will use project registry if not provided)
            content_type: Type of content (image, video, etc.) (optional, will use project registry if not provided)
            
        Returns:
            Tuple[bool, str, int]: Success status, message, and number of batches created
        """
        try:
            # Get project info from registry
            project_info = await self.get_project_info(project_code)
            
            # Use provided values or defaults from project registry
            folder_path = folder_path or project_info.get('folder_path')
            if not folder_path:
                return False, f"No folder path specified for project {project_code}", 0
                
            content_type = content_type or project_info.get('project_type', 'image')
            files_per_batch = files_per_batch or project_info.get('batch_size', self.default_batch_size)
            
            # Get storage credentials and connection type from project
            credentials = project_info.get('credentials')
            connection_type = project_info.get('connection_type', 'NAS-FTP')  # Default to NAS-FTP
            
            # Get all media files from the folder using proper storage credentials
            if credentials:
                connector = None
                
                # Create appropriate connector based on connection type
                if connection_type == 'MinIO':
                    from core.minio_utils import get_minio_connector_from_credentials
                    connector = await get_minio_connector_from_credentials(credentials)
                    logger.info(f"Created MinIO connector for project {project_code}")
                else:
                    # Default to NAS-FTP
                    connector = await get_ftp_connector_from_credentials(credentials)
                    logger.info(f"Created NAS connector for project {project_code}")
                
                if connector:
                    # Use list_directory to get files and filter by content_type
                    directory_contents = await connector.list_directory(folder_path)
                    all_media = []
                    
                    # Filter files by content_type extension
                    extensions = {
                        "image": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
                        "video": [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv", ".webm"],
                        "audio": [".mp3", ".wav", ".ogg", ".flac", ".aac", ".m4a"],
                        "pdf": [".pdf"],
                        "text": [".txt", ".doc", ".docx", ".rtf", ".md", ".csv"],
                        "csv": [".csv", ".xlsx", ".xls"]
                    }.get(content_type.lower(), [])
                    
                    for item in directory_contents:
                        if item.get("type") == "file":
                            file_path = item.get("path", "")
                            file_ext = os.path.splitext(file_path.lower())[1]
                            
                            # If no extensions specified or file matches extension
                            if not extensions or file_ext in extensions:
                                all_media.append({
                                    "path": file_path,
                                    "name": item.get("name", os.path.basename(file_path)),
                                    "size": item.get("size", 0)
                                })
                    
                    logger.info(f"Found {len(all_media)} {content_type} files in {folder_path} using {connection_type} connector")
                else:
                    logger.warning(f"Failed to create {connection_type} connector for project {project_code}")
                    all_media = []
            else:
                # Fallback to default method
                all_media, _ = await get_media_from_folder(
                    folder_path, content_type=content_type, page=1, items_per_page=100000, recursive=True
                )
            
            if not all_media:
                return False, f"No {content_type} files found in folder: {folder_path}", 0
            
            dataset_name = os.path.basename(folder_path)
            
            # Calculate batch size
            batch_size = files_per_batch if files_per_batch and files_per_batch > 0 else self.default_batch_size
            
            # Create batches
            batch_count = 0
            created_batches = []
            
            for i in range(0, len(all_media), batch_size):
                batch_media = all_media[i:i + batch_size]
                batch_identifier = f"BATCH_{batch_count + 1:03d}_{content_type.upper()}_{dataset_name}"
                media_paths = [media['path'] for media in batch_media]
                
                # Prepare batch data
                batch_data = {
                    'batch_identifier': batch_identifier,
                    'default_strategy': str(batch_size),
                    'total_files': len(batch_media),
                    'file_list': json.dumps(media_paths),
                    'total_allocations_needed': len(batch_media),
                    'max_concurrent_users': 10,
                    'is_priority': False,
                    'annotation_count': project_info.get('num_annotators', 1),  # Use num_annotators from allocation strategy
                    'auditor_count': 0     # Default to 0 auditors
                }
                
                # Create batch in database
                created_batch = await self.repository.create_allocation_batch(project_code, batch_data)
                batch_count += 1
                created_batches.append(created_batch)
                
                # Register files for this batch
                await self.register_files_for_batch(
                    project_code=project_code,
                    batch_id=created_batch['id'],
                    media_files=batch_media,
                    content_type=content_type
                )
            
            message = f"Created {batch_count} allocation batches with {batch_size} {content_type} files each (total: {len(all_media)})"
            logger.info(f"Successfully created {batch_count} allocation batches for project {project_code}")
            
            return True, message, batch_count
            
        except Exception as e:
            logger.error(f"Error creating batches from folder {folder_path}: {e}")
            return False, f"Error creating allocation batches: {e}", 0
    
    async def register_files_for_batch(self,
                                     project_code: str,
                                     batch_id: int,
                                     media_files: List[Dict[str, Any]],
                                     content_type: str) -> List[Dict[str, Any]]:
        """
        Register files for a batch.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            media_files: List of media file information
            content_type: Type of content
            
        Returns:
            List[Dict]: List of registered file information
        """
        try:
            files_data = []
            
            # Get project info to determine storage type
            project_info = await self.get_project_info(project_code)
            storage_type = 'minio' if project_info.get('connection_type') == 'MinIO' else 'nas'
            
            for i, media in enumerate(media_files):
                # Extract file extension
                filename = os.path.basename(media['path'])
                _, ext = os.path.splitext(filename)
                ext = ext.lstrip('.').lower()
                
                # Determine file type based on content_type
                file_type = FileType.IMAGE
                if content_type == "video":
                    file_type = FileType.VIDEO
                elif content_type == "audio":
                    file_type = FileType.AUDIO
                elif content_type == "pdf":
                    file_type = FileType.PDF
                elif content_type == "text":
                    file_type = FileType.TEXT
                elif content_type == "csv":
                    file_type = FileType.CSV
                
                # Prepare file data
                file_data = {
                    'file_identifier': media['path'],
                    'original_filename': filename,
                    'file_type': file_type,
                    'file_extension': ext,
                    'storage_location': json.dumps({
                        'type': storage_type,
                        'path': media['path']
                    }),
                    'file_size_bytes': int(media.get('size', 0)) if media.get('size') is not None else 0,
                    'sequence_order': i + 1,
                    'processing_priority': 1,
                    'client_priority': 1
                }
                
                files_data.append(file_data)
            
            # Register files in database
            registered_files = await self.repository.register_files(
                project_code=project_code,
                batch_id=batch_id,
                files_data=files_data
            )
            
            return registered_files
            
        except Exception as e:
            logger.error(f"Error registering files for batch {batch_id} in project {project_code}: {e}")
            raise
    
    async def get_project_batches(self, project_code: str) -> List[Dict[str, Any]]:
        """
        Get all batches for a project.
        
        Args:
            project_code: The project code
            
        Returns:
            List[Dict]: List of batch information
        """
        try:
            return await self.repository.get_allocation_batches(project_code)
            
        except Exception as e:
            logger.error(f"Error getting batches for project {project_code}: {e}")
            raise
    
    async def get_batch_files(self, project_code: str, batch_id: int) -> List[Dict[str, Any]]:
        """
        Get all files for a batch.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            
        Returns:
            List[Dict]: List of file information
        """
        try:
            return await self.repository.get_files_by_batch(project_code, batch_id)
            
        except Exception as e:
            logger.error(f"Error getting files for batch {batch_id} in project {project_code}: {e}")
            raise
    
    async def assign_allocation_batch_to_user_from_active_project(self, username: str, mode: str = "annotation") -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Assign an allocation batch to a user from their active project.
        
        Args:
            username: The username of the user
            mode: The mode (annotation, verification, etc.)
            
        Returns:
            Tuple of (success, message, batch_data)
        """
        try:
            logger.info(f"Assigning batch to user {username} in mode {mode}")
            
            from sqlalchemy import select
            
            async with get_master_db_context() as master_db:
                # Get user's project access by joining with users table
                user_access_result = await master_db.execute(
                    select(UserProjectAccess)
                    .join(users, UserProjectAccess.user_id == users.id)
                    .where(
                        users.username == username,
                        UserProjectAccess.is_active == True
                    ).limit(1)
                )
                user_access = user_access_result.scalar_one_or_none()
                
                if not user_access:
                    return False, "No active project found for user", None
                
                # Get project information
                project_result = await master_db.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.id == user_access.project_id)
                )
                project = project_result.scalar_one_or_none()
                
                if not project:
                    return False, "Project not found", None
                
                project_code = project.project_code
                logger.info(f"Found user's project: {project_code}")
            
            # Get available batches from project database
            repository = ProjectDBRepository()
            batches = await repository.get_allocation_batches(project_code)
            
            if not batches:
                return False, "No batches available in project", None
            
            # Get user's completed batches from project_users table
            async with get_project_db_session(project_code) as session:
                from post_db.allocation_models.project_users import ProjectUsers
                from post_db.master_models.users import users
                
                # Get user ID first
                user_result = await session.execute(
                    select(users.id).where(users.username == username)
                )
                user_id = user_result.scalar_one_or_none()
                
                completed_batches = []
                if user_id:
                    # Get user's completed batches
                    user_result = await session.execute(
                        select(ProjectUsers.completed_batches).where(ProjectUsers.user_id == user_id)
                    )
                    user_data = user_result.fetchone()
                    if user_data and user_data[0]:
                        completed_batches = user_data[0]
                
                logger.info(f"User {username} completed batches: {completed_batches}")
            
            # Find an available batch (status = 'created' or 'allocated') that user hasn't completed
            available_batch = None
            logger.info(f"Checking {len(batches)} batches for assignment")
            for batch in batches:
                logger.info(f"Batch {batch['id']} ({batch['batch_identifier']}): status={batch['batch_status']}, completed={batch['id'] in completed_batches}")
                if (batch['batch_status'] in ['created', 'allocated'] and 
                    batch['id'] not in completed_batches):
                    available_batch = batch
                    logger.info(f"Selected batch {batch['id']} for assignment")
                    break
            
            if not available_batch:
                logger.warning(f"No available batches found. User completed: {completed_batches}")
                return False, "No available batches for assignment (user may have completed all available batches)", None
            
            # Create user allocation
            from post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
            from post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
            from datetime import datetime
            
            async with get_project_db_session(project_code) as session:
                # Check if user already has an allocation for this batch
                existing_allocation = await session.execute(
                    select(UserAllocations).where(
                        UserAllocations.username == username,
                        UserAllocations.batch_id == available_batch['id'],
                        UserAllocations.is_active == True
                    )
                )
                if existing_allocation.scalar_one_or_none():
                    return False, "User already has allocation for this batch", None
                
                # Also check if user is already assigned in file_allocations table
                from sqlalchemy import text
                file_allocation_check = await session.execute(
                    text("""
                        SELECT COUNT(*) 
                        FROM file_allocations 
                        WHERE batch_id = :batch_id 
                        AND (annotator_1 = :user_id OR annotator_2 = :user_id)
                    """),
                    {"batch_id": available_batch['id'], "user_id": user_id}
                )
                existing_file_allocations = file_allocation_check.scalar()
                if existing_file_allocations > 0:
                    logger.warning(f"User {username} already has file allocations for batch {available_batch['id']}")
                    return False, "User already has file allocations for this batch", None
                
                # Create new allocation
                allocation = UserAllocations(
                    username=username,
                    batch_id=available_batch['id'],
                    allocation_role=AllocationRole.ANNOTATOR if mode == "annotation" else AllocationRole.REVIEWER,
                    is_active=True,
                    total_files=available_batch['total_files']
                )
                
                session.add(allocation)
                await session.commit()
                await session.refresh(allocation)
                
                # Update batch status to allocated if it was created
                if available_batch['batch_status'] == 'created':
                    batch_update_result = await session.execute(
                        select(AllocationBatches).where(AllocationBatches.id == available_batch['id'])
                    )
                    batch_obj = batch_update_result.scalar_one_or_none()
                    if batch_obj:
                        batch_obj.batch_status = BatchStatus.ALLOCATED
                        await session.commit()
                
                logger.info(f"Successfully assigned batch {available_batch['batch_identifier']} to user {username}")
                
                return True, "Batch assigned successfully", {
                    'id': available_batch['id'],
                    'batch_identifier': available_batch['batch_identifier'],
                    'total_files': available_batch['total_files'],
                    'project_code': project_code
                }
            
        except Exception as e:
            logger.error(f"Error assigning batch to user {username}: {e}")
            return False, f"Error assigning batch: {str(e)}", None
    
    async def get_user_current_allocation(self, username: str) -> Optional[Dict[str, Any]]:
        """
        Get the current allocation for a user.
        
        Args:
            username: The username of the user
            
        Returns:
            Dict with current allocation info or None
        """
        try:
            logger.info(f"Getting current allocation for user {username}")
        
            
            async with get_master_db_context() as master_db:
                logger.info(f"Querying UserProjectAccess for username: {username}")
                # Get user's project access by joining with users table - try exact match first
                user_access_result = await master_db.execute(
                    select(UserProjectAccess)
                    .join(users, UserProjectAccess.user_id == users.id)
                    .where(
                        users.username == username,
                        UserProjectAccess.is_active == True
                    ).limit(1)
                )
                user_access = user_access_result.scalar_one_or_none()
                logger.info(f"UserProjectAccess exact match result: {user_access}")
                
                # If no exact match, try case-insensitive search
                if not user_access:
                    logger.info(f"No exact match found, trying case-insensitive search for: {username}")
                    user_access_result = await master_db.execute(
                        select(UserProjectAccess)
                        .join(users, UserProjectAccess.user_id == users.id)
                        .where(
                            users.username.ilike(username),
                            UserProjectAccess.is_active == True
                        ).limit(1)
                    )
                    user_access = user_access_result.scalar_one_or_none()
                    logger.info(f"UserProjectAccess case-insensitive result: {user_access}")
                
                # If still no match, try without is_active filter
                if not user_access:
                    logger.info(f"No active match found, trying all records for: {username}")
                    user_access_result = await master_db.execute(
                        select(UserProjectAccess)
                        .join(users, UserProjectAccess.user_id == users.id)
                        .where(
                            users.username.ilike(username)
                        ).limit(1)
                    )
                    user_access = user_access_result.scalar_one_or_none()
                    logger.info(f"UserProjectAccess all records result: {user_access}")
                
                if not user_access:
                    logger.warning(f"No UserProjectAccess found for user: {username}")
                    return None
                
                # Get project information
                project_result = await master_db.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.id == user_access.project_id)
                )
                project = project_result.scalar_one_or_none()
                
                if not project:
                    return None
                
                project_code = project.project_code
                
                # Check if user has project access (even without specific batch allocation)
                # This covers the case where user is assigned to project but not yet to specific batches
                return {
                    'id': None,
                    'batch_id': None,
                    'username': username,
                    'allocation_role': None,
                    'project_code': project_code,
                    'total_files': 0,
                    'files_completed': 0
                }
            
        except Exception as e:
            logger.error(f"Error getting current allocation for user {username}: {e}")
            return None

    async def update_batch_status(self, project_code: str, batch_id: int, status: str) -> Dict[str, Any]:
        """
        Update the status of a batch.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            status: The new status
            
        Returns:
            Dict: Updated batch information
        """
        try:
            # Convert string status to enum
            batch_status = BatchStatus(status)
            
            return await self.repository.update_batch_status(
                project_code=project_code,
                batch_id=batch_id,
                new_status=batch_status
            )
            
        except Exception as e:
            logger.error(f"Error updating status for batch {batch_id} in project {project_code}: {e}")
            raise
    
    async def save_batch_labels(self, db: AsyncSession, combined_labels: Dict[str, Any], username: str, batch_name: str, mode: str) -> Tuple[bool, str]:
        """
        Save batch labels and form data to the file_allocations table.
        
        Args:
            db: Database session (not used in new implementation)
            combined_labels: Dictionary of labels and form data keyed by image path
            username: Username of the annotator
            batch_name: Batch identifier
            mode: annotation or verification mode
            
        Returns:
            Tuple[bool, str]: Success status and message
        """
        try:
            logger.info(f"Saving batch labels for user {username}, batch {batch_name}, mode {mode}")
            logger.info(f"Combined labels keys: {list(combined_labels.keys())[:3]}...")  # Log first 3 keys
            
            
            async with get_master_db_context() as master_db:
                # Get user ID and project info
                user_result = await master_db.execute(
                    select(users.id, UserProjectAccess.project_id)
                    .join(UserProjectAccess, UserProjectAccess.user_id == users.id)
                    .where(
                        users.username == username,
                        UserProjectAccess.is_active == True
                    ).limit(1)
                )
                user_data = user_result.fetchone()
                
                if not user_data:
                    return False, f"No active project found for user {username}"
                
                user_id, project_id = user_data
                
                # Get project code
                project_result = await master_db.execute(
                    select(ProjectsRegistry.project_code)
                    .where(ProjectsRegistry.id == project_id)
                )
                project_code = project_result.scalar_one_or_none()
                
                if not project_code:
                    return False, "Project not found"
            
            # Get project database session
            async with get_project_db_session(project_code) as session:
                # Find the batch by batch_identifier
                from post_db.allocation_models.allocation_batches import AllocationBatches
                batch_result = await session.execute(
                    select(AllocationBatches.id)
                    .where(AllocationBatches.batch_identifier == batch_name)
                )
                batch_id = batch_result.scalar_one_or_none()
                
                if not batch_id:
                    return False, f"Batch {batch_name} not found"
                
                # Determine which annotator slot this user occupies
                annotator_slot = await self._get_user_annotator_slot(session, batch_id, user_id)
                logger.info(f"User {username} (ID: {user_id}) annotator slot: {annotator_slot}")
                if not annotator_slot:
                    # Additional debugging for last batch issue
                    logger.error(f"CRITICAL: User {username} (ID: {user_id}) is not assigned to batch {batch_name} (ID: {batch_id})")
                    
                    # Check if user has any file allocations in this batch
                    debug_result = await session.execute(
                        text("SELECT * FROM file_allocations WHERE batch_id = :batch_id LIMIT 5"),
                        {"batch_id": batch_id}
                    )
                    debug_rows = debug_result.fetchall()
                    logger.error(f"Sample file allocations in batch {batch_id}: {[dict(row._mapping) for row in debug_rows]}")
                    
                    # Check allocation_batches table
                    batch_debug_result = await session.execute(
                        text("SELECT * FROM allocation_batches WHERE id = :batch_id"),
                        {"batch_id": batch_id}
                    )
                    batch_debug_row = batch_debug_result.fetchone()
                    if batch_debug_row:
                        logger.error(f"Allocation batch {batch_id}: {dict(batch_debug_row._mapping)}")
                    
                    return False, f"User {username} is not assigned to batch {batch_name}. This might be a last batch completion issue."
                
                annotator_field = f"annotator_{annotator_slot}"
                review_field = f"annotator_{annotator_slot}_review"
                
                logger.info(f"User {username} is {annotator_field}, updating {review_field}")
                
                # Get all files in the batch
                from post_db.allocation_models.files_registry import FilesRegistry
                files_result = await session.execute(
                    select(FilesRegistry.id, FilesRegistry.file_identifier, FilesRegistry.file_type, FilesRegistry.sequence_order)
                    .where(FilesRegistry.batch_id == batch_id)
                    .order_by(FilesRegistry.sequence_order)
                )
                batch_files = files_result.fetchall()
                
                if not batch_files:
                    return False, f"No files found in batch {batch_name}"
                
                logger.info(f" Found {len(batch_files)} files in batch {batch_name}")
                logger.info(f" Batch files: {[(f.id, f.file_identifier, f.file_type, f.sequence_order) for f in batch_files]}")
                logger.info(f"Combined labels keys: {list(combined_labels.keys())}")
                
                # Check if this is a CSV project
                is_csv_project = batch_files and batch_files[0].file_type == 'csv'
                logger.info(f"Is CSV project: {is_csv_project}")
                
                # Update file_allocations for each file with labels/form data
                files_updated = 0
                files_completed = 0
                
                for file_record in batch_files:
                    file_id = file_record.id
                    file_identifier = file_record.file_identifier
                    file_type = file_record.file_type
                    sequence_order = file_record.sequence_order
                    # Find matching label data by file path
                    label_data = None
                    logger.info(f"Looking for match for file_identifier: {file_identifier} (sequence_order: {sequence_order})")
                    
                    if is_csv_project:
                        # For CSV projects, match by file ID
                        # Frontend sends csv_row_1, csv_row_2, etc. where the number matches file ID
                        expected_key = f"csv_row_{file_id}"
                        logger.info(f"CSV project: looking for key '{expected_key}' (file_id: {file_id})")
                        
                        if expected_key in combined_labels:
                            label_data = combined_labels[expected_key]
                            logger.info(f"Found CSV match! Key: {expected_key}, Data: {label_data}")
                        else:
                            logger.warning(f"No CSV match found for {expected_key}. Available keys: {list(combined_labels.keys())}")
                    else:
                        # Original matching logic for non-CSV projects
                        for image_path, data in combined_labels.items():
                            logger.info(f"Checking image_path: {image_path}")
                            
                            # Normalize both paths for comparison
                            # Remove API prefix and normalize paths
                            normalized_image_path = image_path
                            if '/api/annotator/image/' in image_path:
                                normalized_image_path = image_path.split('/api/annotator/image/')[-1]
                            elif '/api/annotator/' in image_path:
                                normalized_image_path = image_path.split('/api/annotator/')[-1]
                            
                            # URL decode the path to handle %20 and other encoded characters
                            normalized_image_path = unquote(normalized_image_path)
                            
                            # Ensure both paths start with /
                            if not normalized_image_path.startswith('/'):
                                normalized_image_path = '/' + normalized_image_path
                            if not file_identifier.startswith('/'):
                                normalized_file_identifier = '/' + file_identifier
                            else:
                                normalized_file_identifier = file_identifier
                            
                            logger.info(f"Normalized comparison: '{normalized_image_path}' vs '{normalized_file_identifier}'")
                            
                            # Match by exact normalized path or by filename
                            if (normalized_image_path == normalized_file_identifier or 
                                normalized_image_path.endswith(normalized_file_identifier) or
                                normalized_file_identifier.endswith(normalized_image_path) or
                                os.path.basename(normalized_image_path) == os.path.basename(normalized_file_identifier)):
                                label_data = data
                                logger.info(f"Found match! Data: {data}")
                                break
                    
                    if label_data:
                        # Transform form_data keys from field_name to label if it exists
                        if isinstance(label_data, dict) and 'form_data' in label_data:
                            field_mapping = await self.get_field_name_to_label_mapping(project_code)
                            transformed_form_data = self._transform_data_keys_to_labels(
                                label_data['form_data'], 
                                field_mapping
                            )
                            label_data['form_data'] = transformed_form_data
                            logger.info(f"Transformed form_data keys for file {file_id}: {list(transformed_form_data.keys())}")
                        
                        # First, let's check what file_allocations exist for this file before updating
                        pre_check_result = await session.execute(
                            text("SELECT * FROM file_allocations WHERE file_id = :file_id AND batch_id = :batch_id"),
                            {"file_id": file_id, "batch_id": batch_id}
                        )
                        pre_allocations = pre_check_result.fetchall()
                        logger.info(f"Pre-update file_allocations for file {file_id}: {[dict(row._mapping) for row in pre_allocations]}")
                        logger.info(f"Trying to update: {review_field} for {annotator_field} = {user_id}")
                        
                        # Update file_allocations with the review data
                        update_result = await session.execute(
                            text(f"""
                                UPDATE file_allocations 
                                SET {review_field} = :review_data,
                                    completion_count = completion_count + 1
                                WHERE file_id = :file_id 
                                AND batch_id = :batch_id 
                                AND {annotator_field} = :user_id
                            """),
                            {
                                "review_data": json.dumps(label_data),
                                "file_id": file_id,
                                "batch_id": batch_id,
                                "user_id": user_id
                            }
                        )
                        
                        if update_result.rowcount > 0:
                            files_updated += 1
                            files_completed += 1
                            logger.info(f"Successfully updated file {file_id} with review data")
                        else:
                            logger.error(f"FAILED to update file_allocation for file {file_id}, user {user_id}, batch {batch_id}")
                            logger.error(f"Update query: UPDATE file_allocations SET {review_field} = ... WHERE file_id = {file_id} AND batch_id = {batch_id} AND {annotator_field} = {user_id}")
                            
                            # Try alternative update without annotator field constraint
                            alt_update_result = await session.execute(
                                text(f"""
                                    UPDATE file_allocations 
                                    SET {review_field} = :review_data,
                                        completion_count = completion_count + 1
                                    WHERE file_id = :file_id 
                                    AND batch_id = :batch_id 
                                    AND {annotator_field} IS NOT NULL
                                """),
                                {
                                    "review_data": json.dumps(label_data),
                                    "file_id": file_id,
                                    "batch_id": batch_id
                                }
                            )
                            
                            if alt_update_result.rowcount > 0:
                                files_updated += 1
                                files_completed += 1
                                logger.info(f"Successfully updated file {file_id} with alternative query")
                            else:
                                logger.error(f"Alternative update also failed for file {file_id}")
                                # Let's check what file_allocations exist for this file
                                check_result = await session.execute(
                                    text("SELECT * FROM file_allocations WHERE file_id = :file_id AND batch_id = :batch_id"),
                                    {"file_id": file_id, "batch_id": batch_id}
                                )
                                existing_allocations = check_result.fetchall()
                                logger.error(f"Existing file_allocations for file {file_id}: {[dict(row._mapping) for row in existing_allocations]}")
                
                # Update allocation_batches completion count - increment by 1 for this user's completion
                if files_completed > 0:
                    await session.execute(
                        text("""
                            UPDATE allocation_batches 
                            SET completion_count = completion_count + 1
                            WHERE id = :batch_id
                        """),
                        {
                            "batch_id": batch_id
                        }
                    )
                    logger.info(f"Updated batch {batch_id} completion_count by 1 (user completed {files_completed} files)")
                
                # Ensure all file updates are committed before updating batch completion status
                await session.flush()
                logger.info(f"Flushed {files_completed} file updates to database")
                
                # Update project_users table: add to completed_batches and handle current_batch based on strategy
                if files_completed > 0:
                    from post_db.allocation_models.project_users import ProjectUsers
                    from sqlalchemy import update
                    
                    # Get project allocation strategy to determine batch completion behavior
                    strategy_type = await self._get_project_allocation_strategy_type(project_code)
                    logger.info(f"Project {project_code} allocation strategy: {strategy_type}")
                    
                    # First get current completed_batches array for this user
                    current_user_result = await session.execute(
                        select(ProjectUsers.completed_batches)
                        .where(ProjectUsers.user_id == user_id)
                    )
                    current_user_data = current_user_result.fetchone()
                    
                    if current_user_data:
                        completed_batches = current_user_data[0] or []  # Handle None case
                        
                        # Add current batch to completed list if not already there
                        if batch_id not in completed_batches:
                            completed_batches.append(batch_id)
                        
                        # Handle current_batch based on allocation strategy
                        if strategy_type == "parallel":
                            # In parallel strategy, clear current_batch immediately when user completes their work
                            # This allows the user to be assigned to the next available batch right away
                            await session.execute(
                                update(ProjectUsers)
                                .where(ProjectUsers.user_id == user_id)
                                .values(
                                    current_batch=None,
                                    completed_batches=completed_batches
                                )
                            )
                            logger.info(f"Updated project_users for user {user_id} (parallel): cleared current_batch for immediate reassignment, added batch {batch_id} to completed_batches")
                        else:
                            # In sequential strategy, clear current_batch as before
                            await session.execute(
                                update(ProjectUsers)
                                .where(ProjectUsers.user_id == user_id)
                                .values(
                                    current_batch=None,
                                    completed_batches=completed_batches
                                )
                            )
                            logger.info(f"Updated project_users for user {user_id} (sequential): cleared current_batch, added batch {batch_id} to completed_batches")
                    else:
                        logger.warning(f"No project_users record found for user {user_id}")
                
                await session.commit()
                
                # Check if project should be auto-completed after annotation completion
                if files_completed > 0:
                    try:
                        await self._check_and_complete_annotation_project(project_code)
                    except Exception as auto_complete_error:
                        logger.error(f"Error in auto-completion check after annotation: {auto_complete_error}")
                        # Don't fail the annotation save if auto-completion fails
                
                logger.info(f"Successfully saved labels for {files_updated} files in batch {batch_name}")
                return True, f"Successfully saved labels for {files_updated} files"
                
        except Exception as e:
            logger.error(f"Error saving batch labels: {str(e)}")
            return False, f"Error saving labels: {str(e)}"
    
    async def _get_user_annotator_slot(self, session: AsyncSession, batch_id: int, user_id: int) -> Optional[int]:
        """
        Determine which annotator slot (1, 2, 3, etc.) the user occupies in the batch.
        
        Args:
            session: Database session
            batch_id: Batch ID
            user_id: User ID
            
        Returns:
            Optional[int]: Annotator slot number or None if not found
        """
        try:
            from sqlalchemy import text
            
            # Check file_allocations table instead of allocation_batches
            # Look for any file allocation where this user is assigned
            # First, check what annotator columns exist in the table
            columns_result = await session.execute(
                text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'file_allocations' 
                    AND column_name LIKE 'annotator_%'
                    AND column_name NOT LIKE '%_review'
                    ORDER BY column_name
                """)
            )
            annotator_columns = [row[0] for row in columns_result.fetchall()]
            logger.info(f"Found annotator columns: {annotator_columns}")
            
            if not annotator_columns:
                logger.warning("No annotator columns found in file_allocations table")
                return None
            
            # For each annotator column, check if this user is assigned
            for annotator_col in annotator_columns:
                # Extract slot number from column name (e.g., "annotator_1" -> 1)
                slot_num = int(annotator_col.split('_')[1])
                
                # Check if this user is assigned in this slot
                check_query = f"""
                    SELECT 1 
                    FROM file_allocations 
                    WHERE batch_id = :batch_id 
                    AND {annotator_col} = :user_id
                    LIMIT 1
                """
                
                result = await session.execute(
                    text(check_query),
                    {"batch_id": batch_id, "user_id": user_id}
                )
                
                if result.fetchone():
                    logger.info(f"Found user {user_id} in annotator slot {slot_num}")
                    return slot_num
            
            logger.warning(f"No file allocation found for user {user_id} in batch {batch_id}")
            # Let's see what file allocations exist for this batch
            debug_result = await session.execute(
                text("SELECT id, file_id, allocation_sequence, annotator_1, annotator_2 FROM file_allocations WHERE batch_id = :batch_id LIMIT 5"),
                {"batch_id": batch_id}
            )
            debug_rows = debug_result.fetchall()
            logger.info(f"Sample file allocations for batch {batch_id}: {[dict(row._mapping) for row in debug_rows]}")
            
            # Fallback: check allocation_batches table
            batch_result = await session.execute(
                text("SELECT * FROM allocation_batches WHERE id = :batch_id"),
                {"batch_id": batch_id}
            )
            batch_row = batch_result.fetchone()
            
            if not batch_row:
                return None
            
            batch_dict = dict(batch_row._mapping)
            
            # Check each annotator slot in allocation_batches as fallback
            for i in range(1, 10):  # Check up to 10 annotator slots
                annotator_field = f"annotator_{i}"
                if annotator_field in batch_dict and batch_dict[annotator_field] == user_id:
                    logger.info(f"Found user {user_id} in allocation_batches annotator slot {i}")
                    return i
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user annotator slot: {str(e)}")
            return None

    async def _get_project_allocation_strategy_type(self, project_code: str) -> str:
        """Get the allocation strategy type for a project (parallel or sequential)"""
        try:
            
            async with get_master_db_context() as master_session:
                # Get project info
                project_result = await master_session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = project_result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    logger.warning(f"No allocation strategy found for project {project_code}, defaulting to sequential")
                    return "sequential"
                
                # Get strategy info
                strategy_result = await master_session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                if not strategy:
                    logger.warning(f"Allocation strategy not found for project {project_code}, defaulting to sequential")
                    return "sequential"
                
                # Convert StrategyType enum to string
                strategy_type = str(strategy.strategy_type).lower()
                if "parallel" in strategy_type:
                    return "parallel"
                else:
                    return "sequential"
                    
        except Exception as e:
            logger.error(f"Error getting project allocation strategy type: {str(e)}")
            return "sequential"  # Default to sequential on error
    
    async def _check_and_complete_annotation_project(self, project_code: str) -> None:
        """
        Check if annotation project should be auto-completed.
        For annotation-only projects, complete when all batches are fully annotated.
        """
        try:
            
            async with get_master_db_context() as master_session:
                # Get project and strategy details
                project_result = await master_session.execute(
                    select(ProjectsRegistry, AllocationStrategies)
                    .join(AllocationStrategies, ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id)
                    .where(ProjectsRegistry.project_code == project_code)
                )
                project_data = project_result.fetchone()
                
                if not project_data:
                    logger.warning(f"Could not find project {project_code} for auto-completion check")
                    return
                    
                project, strategy = project_data
                
                if strategy.requires_verification:
                    logger.info(f"Project {project_code} requires verification, skipping annotation auto-completion")
                    return
                
                # For annotation-only projects: check if completed_files >= total_files
                is_complete = (project.completed_files >= project.total_files and project.total_files > 0)
                logger.info(f"Annotation project {project_code}: {project.completed_files}/{project.total_files} files completed")
                
                if is_complete:
                    # Auto-complete the project
                    await master_session.execute(
                        update(ProjectsRegistry)
                        .where(ProjectsRegistry.id == project.id)
                        .values(project_status="completed")
                    )
                    
                    # Clear active_project for all users in this project
                    clear_result = await master_session.execute(
                        update(users)
                        .where(users.active_project == project_code)
                        .values(active_project=None)
                    )
                    
                    await master_session.commit()
                    
                    logger.info(f"🎉 AUTO-COMPLETED ANNOTATION PROJECT: {project_code} - All annotation work finished! Cleared active_project for {clear_result.rowcount} users.")
                else:
                    logger.info(f"Annotation project {project_code} not ready for auto-completion yet")
                    
        except Exception as e:
            logger.error(f"Error in annotation project auto-completion check for {project_code}: {str(e)}")
            # Don't fail the main operation if auto-completion fails

    async def get_field_name_to_label_mapping(self, project_code: str) -> Dict[str, str]:
        """
        Get mapping from field_name to label from project's annotation requirements.
        
        Args:
            project_code: The project code
            
        Returns:
            Dict: Mapping from field_name to label
        """
        try:
            async with get_master_db_context() as master_session:
                # Get project info
                project_result = await master_session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = project_result.scalar_one_or_none()
                
                if not project or not project.annotation_requirements:
                    logger.warning(f"No annotation requirements found for project {project_code}")
                    return {}
                
                # Extract form_config from annotation_requirements
                annotation_requirements = project.annotation_requirements
                if isinstance(annotation_requirements, str):
                    import json
                    annotation_requirements = json.loads(annotation_requirements)
                
                form_config = annotation_requirements.get('form_config', [])
                
                # Create mapping from field_name to label
                field_mapping = {}
                for field in form_config:
                    field_name = field.get('field_name')
                    label = field.get('label')
                    if field_name and label:
                        field_mapping[field_name] = label
                
                logger.info(f"Created field mapping for project {project_code}: {field_mapping}")
                return field_mapping
                
        except Exception as e:
            logger.error(f"Error getting field mapping for project {project_code}: {str(e)}")
            return {}

    def _transform_data_keys_to_labels(self, data: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Transform data keys from field_name to label using the mapping.
        
        Args:
            data: Data with field_name keys
            field_mapping: Mapping from field_name to label
            
        Returns:
            Dict: Data with label keys
        """
        if not field_mapping:
            return data
        
        transformed_data = {}
        for key, value in data.items():
            # Use label if mapping exists, otherwise keep original key
            new_key = field_mapping.get(key, key)
            transformed_data[new_key] = value
        
        return transformed_data