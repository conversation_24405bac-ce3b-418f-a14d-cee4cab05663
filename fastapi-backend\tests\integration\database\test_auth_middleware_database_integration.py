"""
Integration tests for Authentication Middleware Database operations with REAL database operations.
Tests end-to-end authentication workflows through middleware with database persistence.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual authentication middleware from dependencies/auth.py
- Database operations for user validation and session management
- JWT token validation with real database user lookup
- Role-based access control tested with actual database roles
"""
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
import json
import time
from datetime import datetime, timedelta

from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.services.auth_service import AuthService
from app.schemas.UserSchemas import UserRegisterRequest, LoginRequest
from app.core.security import create_access_token, create_refresh_token, verify_token
from app.dependencies.auth import get_current_active_user, get_current_user, require_admin

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def auth_test_users(test_master_db: AsyncSession):
    """Create test users with different roles for authentication testing."""
    test_users = {}
    
    # Create users with different roles
    roles = ["admin", "annotator", "auditor", "verifier"]
    
    for role in roles:
        user_data = test_factory.users.create_user_register_request(role=role)
        success, user = await AuthService.register_user(test_master_db, user_data)
        assert success
        
        # Generate tokens
        token_data = {
            "sub": user.username,
            "user_id": user.id,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
            "email": user.email
        }
        access_token = create_access_token(data=token_data)
        refresh_token = create_refresh_token(data=token_data)
        
        test_users[role] = {
            "user": user,
            "password": "testpass123",
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_data": token_data
        }
    
    return test_users


@pytest_asyncio.fixture
async def auth_test_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up authentication test environment."""
    # Use factory to create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    return environment


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.smoke            # Suite marker - Critical path
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestJWTTokenValidation:
    """SECURITY SMOKE TEST SUITE: Critical JWT token validation."""
    
    @pytest.mark.asyncio
    async def test_valid_token_user_lookup_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test valid JWT token with user lookup in REAL database."""
        admin_user_data = auth_test_users["admin"]
        token = admin_user_data["access_token"]
        
        # Set authorization header
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test endpoint that requires authentication
        response = await client.get("/api/admin/dashboard")
        
        # Should succeed or fail gracefully based on implementation
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code != 401:  # 401 would indicate auth failure
            # Token was accepted and user was found in database
            
            # Verify user still exists in database
            stmt = select(users).where(users.id == admin_user_data["user"].id)
            db_result = await test_master_db.execute(stmt)
            db_user = db_result.scalar_one_or_none()
            
            assert db_user is not None
            assert db_user.username == admin_user_data["user"].username
            assert db_user.is_active is True
    
    @pytest.mark.asyncio
    async def test_invalid_token_rejection_real_database(
        self,
        client: AsyncClient,
        setup_test_database
    ):
        """Test invalid JWT token rejection."""
        # Use invalid token
        invalid_token = "invalid.jwt.token.here"
        client.headers.update({"Authorization": f"Bearer {invalid_token}"})
        
        # Test endpoint that requires authentication
        response = await client.get("/api/admin/dashboard")
        
        # Should reject with 401 Unauthorized
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_expired_token_rejection_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test expired JWT token rejection."""
        user_data = auth_test_users["admin"]
        
        # Create expired token
        expired_token_data = user_data["token_data"].copy()
        expired_token = create_access_token(
            data=expired_token_data,
            expires_delta=timedelta(seconds=-1)  # Already expired
        )
        
        client.headers.update({"Authorization": f"Bearer {expired_token}"})
        
        # Test endpoint that requires authentication
        response = await client.get("/api/admin/dashboard")
        
        # Should reject with 401 Unauthorized
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_inactive_user_token_rejection_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test token rejection for inactive user."""
        user_data = auth_test_users["annotator"]
        token = user_data["access_token"]
        
        # Deactivate user in database
        user = user_data["user"]
        user.is_active = False
        test_master_db.add(user)
        await test_master_db.commit()
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test endpoint that requires authentication
        response = await client.get("/api/annotator/start-annotation")
        
        # Should reject with 401 or 403
        assert response.status_code in [401, 403]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.smoke            # Suite marker - Critical security
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestRoleBasedAccessControl:
    """SECURITY SMOKE TEST SUITE: Critical role-based access control."""
    
    @pytest.mark.asyncio
    async def test_admin_access_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test admin access to admin endpoints."""
        admin_data = auth_test_users["admin"]
        token = admin_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test admin-only endpoints
        admin_endpoints = [
            "/api/admin/dashboard",
            "/api/admin/users",
            "/api/admin/projects"
        ]
        
        for endpoint in admin_endpoints:
            response = await client.get(endpoint)
            
            # Should not be rejected due to insufficient permissions
            assert response.status_code != 403, f"Admin should have access to {endpoint}"
            # Other status codes (404, 422, 500) are acceptable for implementation-specific reasons
    
    @pytest.mark.asyncio
    async def test_non_admin_access_restriction_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test non-admin users restricted from admin endpoints."""
        annotator_data = auth_test_users["annotator"]
        token = annotator_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test admin-only endpoints with non-admin token
        admin_endpoints = [
            "/api/admin/users",
            "/api/admin/add-user",
            "/api/admin/projects"
        ]
        
        for endpoint in admin_endpoints:
            if "add-user" in endpoint:
                response = await client.post(endpoint, json={})
            else:
                response = await client.get(endpoint)
            
            # Should be rejected with 403 Forbidden
            assert response.status_code in [403, 422], f"Non-admin should be restricted from {endpoint}"
    
    @pytest.mark.asyncio
    async def test_role_specific_access_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test role-specific access patterns."""
        # Test annotator access to annotator endpoints
        annotator_data = auth_test_users["annotator"]
        annotator_token = annotator_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {annotator_token}"})
        
        # Test annotator-specific endpoints
        response = await client.post("/api/annotator/start-annotation")
        
        # Should not be rejected due to role (other errors acceptable)
        assert response.status_code != 403
        
        # Test verifier access to verifier endpoints
        verifier_data = auth_test_users["verifier"]
        verifier_token = verifier_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {verifier_token}"})
        
        # Test verifier-specific endpoints
        response = await client.get("/api/verifier/batches")
        
        # Should not be rejected due to role (other errors acceptable)
        assert response.status_code != 403


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.regression       # Suite marker - Comprehensive testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestUserSessionManagement:
    """SECURITY REGRESSION TEST SUITE: User session management operations."""
    
    @pytest.mark.asyncio
    async def test_user_session_tracking_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test user session tracking with database updates."""
        user_data = auth_test_users["admin"]
        token = user_data["access_token"]
        user = user_data["user"]
        
        # Record last login time before request
        original_last_login = user.last_login
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Make authenticated request
        response = await client.get("/api/admin/dashboard")
        
        # Verify user session tracking (if implemented)
        stmt = select(users).where(users.id == user.id)
        db_result = await test_master_db.execute(stmt)
        updated_user = db_result.scalar_one_or_none()
        
        assert updated_user is not None
        # Note: last_login may or may not be updated depending on implementation
        # This test verifies the user lookup and session handling work correctly
    
    @pytest.mark.asyncio
    async def test_concurrent_user_sessions_real_database(
        self,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test concurrent user sessions with database operations."""
        user_data = auth_test_users["annotator"]
        user = user_data["user"]
        
        # Create multiple tokens for the same user
        token_data = user_data["token_data"]
        
        token1 = create_access_token(data=token_data)
        token2 = create_access_token(data=token_data)
        
        # Create multiple clients with different tokens
        from httpx import AsyncClient, ASGITransport
        from app.main import app
        
        transport = ASGITransport(app=app)
        
        async with AsyncClient(transport=transport, base_url="http://test") as client1:
            async with AsyncClient(transport=transport, base_url="http://test") as client2:
                
                client1.headers.update({"Authorization": f"Bearer {token1}"})
                client2.headers.update({"Authorization": f"Bearer {token2}"})
                
                # Make concurrent requests
                response1 = await client1.post("/api/annotator/start-annotation")
                response2 = await client2.post("/api/annotator/start-annotation")
                
                # Both should be authenticated (other errors acceptable)
                assert response1.status_code != 401
                assert response2.status_code != 401
                
                # Verify user still exists and is consistent
                stmt = select(users).where(users.id == user.id)
                db_result = await test_master_db.execute(stmt)
                db_user = db_result.scalar_one_or_none()
                
                assert db_user is not None
                assert db_user.username == user.username
                assert db_user.is_active is True


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.regression       # Suite marker - Integration testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestAuthenticationMiddlewareIntegration:
    """SECURITY REGRESSION TEST SUITE: Authentication middleware integration."""
    
    @pytest.mark.asyncio
    async def test_dependency_injection_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test authentication dependency injection with database lookup."""
        admin_data = auth_test_users["admin"]
        token = admin_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test endpoint that uses get_current_active_user dependency
        response = await client.get("/api/admin/users")
        
        # Dependency should inject user data successfully
        assert response.status_code != 401  # Authentication should succeed
        
        if response.status_code == 200:
            # Verify user data is properly injected
            result = response.json()
            assert isinstance(result, list)  # Should return list of users
    
    @pytest.mark.asyncio
    async def test_database_user_lookup_performance_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test database user lookup performance during authentication."""
        user_data = auth_test_users["admin"]
        token = user_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Make multiple authenticated requests to test database lookup performance
        start_time = time.time()
        
        for _ in range(5):
            response = await client.get("/api/admin/dashboard")
            assert response.status_code != 401  # Should remain authenticated
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Performance check: 5 requests should complete within reasonable time
        assert total_time < 10.0, f"Authentication took too long: {total_time}s"
        
        # Verify user is still active in database
        stmt = select(users).where(users.id == user_data["user"].id)
        db_result = await test_master_db.execute(stmt)
        db_user = db_result.scalar_one_or_none()
        
        assert db_user is not None
        assert db_user.is_active is True
    
    @pytest.mark.asyncio
    async def test_database_connection_error_handling_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        setup_test_database
    ):
        """Test authentication middleware database connection error handling."""
        user_data = auth_test_users["admin"]
        token = user_data["access_token"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Normal request should work
        response = await client.get("/api/admin/dashboard")
        
        # Should not fail due to authentication (other errors acceptable)
        assert response.status_code != 401
        
        # Note: Testing actual database connection failures would require
        # more complex setup, but this verifies the basic flow works


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.regression       # Suite marker - Error scenarios
@pytest.mark.high             # Priority marker - P1 (security errors are critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestAuthenticationErrorHandling:
    """SECURITY REGRESSION TEST SUITE: Authentication error handling."""
    
    @pytest.mark.asyncio
    async def test_malformed_authorization_header(
        self,
        client: AsyncClient,
        setup_test_database
    ):
        """Test handling of malformed authorization headers."""
        malformed_headers = [
            "InvalidToken",
            "Bearer",
            "Bearer ",
            "Basic dXNlcjpwYXNz",  # Basic auth instead of Bearer
            "Bearer invalid-token-format"
        ]
        
        for header_value in malformed_headers:
            client.headers.update({"Authorization": header_value})
            
            response = await client.get("/api/admin/dashboard")
            
            # Should reject with 401 Unauthorized
            assert response.status_code == 401, f"Malformed header '{header_value}' should be rejected"
    
    @pytest.mark.asyncio
    async def test_missing_authorization_header(
        self,
        client: AsyncClient,
        setup_test_database
    ):
        """Test handling of missing authorization header."""
        # Remove authorization header
        if "Authorization" in client.headers:
            del client.headers["Authorization"]
        
        # Test protected endpoints
        protected_endpoints = [
            "/api/admin/dashboard",
            "/api/admin/users",
            "/api/annotator/start-annotation"
        ]
        
        for endpoint in protected_endpoints:
            if "start-annotation" in endpoint:
                response = await client.post(endpoint)
            else:
                response = await client.get(endpoint)
            
            # Should reject with 401 Unauthorized
            assert response.status_code == 401, f"Endpoint {endpoint} should require authentication"
    
    @pytest.mark.asyncio
    async def test_user_not_found_in_database_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test handling when token is valid but user not found in database."""
        user_data = auth_test_users["annotator"]
        token = user_data["access_token"]
        user = user_data["user"]
        
        # Delete user from database
        await test_master_db.delete(user)
        await test_master_db.commit()
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Test authenticated endpoint
        response = await client.post("/api/annotator/start-annotation")
        
        # Should reject with 401 or 404 (user not found)
        assert response.status_code in [401, 404]


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker
@pytest.mark.security         # Security suite marker
@pytest.mark.regression       # Suite marker - Complex workflows
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Complex auth workflows take time
class TestComplexAuthenticationWorkflows:
    """SECURITY REGRESSION TEST SUITE: Complex authentication workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_authentication_flow_real_database(
        self,
        client: AsyncClient,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test complete authentication flow from registration to protected access."""
        # 1. Register new user
        user_data = test_factory.users.create_user_data(role="admin")
        user_data.update({
            "password": "testpass123",
            "confirm_password": "testpass123",
            "is_active": True
        })
        
        # Register user via API
        registration_response = await client.post("/api/auth/register", json=user_data)
        
        if registration_response.status_code != 200:
            pytest.skip("User registration not available for end-to-end test")
        
        # 2. Authenticate user
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        
        login_response = await client.post("/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            if "access_token" in login_result:
                access_token = login_result["access_token"]
                
                # 3. Use token for protected access
                client.headers.update({"Authorization": f"Bearer {access_token}"})
                
                protected_response = await client.get("/api/admin/dashboard")
                
                # Should succeed with proper authentication
                assert protected_response.status_code != 401
                
                # 4. Verify user persists in database
                stmt = select(users).where(users.username == user_data["username"])
                db_result = await test_master_db.execute(stmt)
                db_user = db_result.scalar_one_or_none()
                
                assert db_user is not None
                assert db_user.is_active is True
                assert db_user.role == UserRole.ADMIN
    
    @pytest.mark.asyncio
    async def test_role_change_token_invalidation_real_database(
        self,
        client: AsyncClient,
        auth_test_users,
        test_master_db: AsyncSession,
        setup_test_database
    ):
        """Test token behavior when user role changes in database."""
        user_data = auth_test_users["annotator"]
        token = user_data["access_token"]
        user = user_data["user"]
        
        client.headers.update({"Authorization": f"Bearer {token}"})
        
        # Initial request should work
        response = await client.post("/api/annotator/start-annotation")
        initial_status = response.status_code
        
        # Change user role in database
        user.role = UserRole.ADMIN
        test_master_db.add(user)
        await test_master_db.commit()
        
        # Token should still work (role change may not invalidate existing tokens)
        response = await client.post("/api/annotator/start-annotation")
        
        # Should still be authenticated (though role mismatch might cause other errors)
        assert response.status_code != 401
        
        # Verify role change in database
        stmt = select(users).where(users.id == user.id)
        db_result = await test_master_db.execute(stmt)
        updated_user = db_result.scalar_one_or_none()
        
        assert updated_user is not None
        assert updated_user.role == UserRole.ADMIN
