"""
PROPER PostgreSQL Testing Configuration for DADP
Fixes "Event loop is closed" errors using proper event loop management.

Based on best practices:
- Proper asyncio event loop policy for Windows
- Session-scoped database setup with function-scoped connections
- Per-test resource cleanup and isolation
- Correct pytest-asyncio configuration
"""
import os
import sys
import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from httpx import AsyncClient, ASGITransport
from sqlalchemy.pool import NullPool, StaticPool

# Ensure import paths
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "app")))

# --- Windows event loop policy setup ---
if sys.platform.startswith("win"):
    # Use SelectorEventLoop for Windows to ensure asyncpg compatibility
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Configure pytest-asyncio to use auto mode
pytest_plugins = ('pytest_asyncio',)

# Test environment
os.environ["TESTING"] = "1"

# ✅ POSTGRESQL TEST DATABASE CONFIGURATION
os.environ["DATABASE_URL"] = "postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/test_project_db"
os.environ["MASTER_DATABASE_URL"] = "postgresql+asyncpg://kanwar_raj:dadpdev123@10.10.10.30:5432/test_master_db"
os.environ["ALLOCATION_DATABASE_URL_PATTERN"] = "postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/test_project_db"

from app.main import app
from app.post_db.base import Base
from app.post_db.connect import get_db_connection, get_db_session
from app.post_db.master_db import get_master_db_connection, get_master_db_session
from app.utils.dynamic_schema_generator import DynamicSchemaGenerator
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
import sqlalchemy as sa

# ===============================
# ONE-TIME TABLE CREATION FOR THE SESSION
# ===============================
@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_test_database():
    """Create tables once per session using fresh temp engines."""
    print("🔧 Setting up PostgreSQL test databases...")
    proj_engine = create_async_engine(os.environ["DATABASE_URL"], echo=False, pool_pre_ping=True, poolclass=NullPool)
    master_engine = create_async_engine(os.environ["MASTER_DATABASE_URL"], echo=False, pool_pre_ping=True, poolclass=NullPool)
    try:
        # ✅ FORCE DROP AND RECREATE ALL TABLES for clean schema
        print("🗑️ Dropping existing tables to ensure clean schema...")
        
        # Drop Master DB tables
        async with master_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            await conn.run_sync(Base.metadata.create_all)
        print("✅ Master DB tables recreated successfully")

        # Drop and recreate Project DB tables with default strategy
        await force_recreate_project_tables(proj_engine)
        print("✅ Project DB tables recreated successfully")
        print("🎯 PostgreSQL test database setup complete!")
    finally:
        await proj_engine.dispose()
        await master_engine.dispose()

# ===============================
# PER-TEST ENGINES / SESSIONS (bind to current loop)
# ===============================
@pytest_asyncio.fixture(scope="function")
async def test_engines():
    """Function-scoped engines with proper connection pooling and event loop management."""
    # Create engines with NullPool to avoid connection pooling issues across event loops
    test_engine = create_async_engine(
        os.environ["DATABASE_URL"],
        echo=False,
        poolclass=NullPool,  # Use NullPool to avoid event loop issues
        connect_args={
            "command_timeout": 60,
            "server_settings": {"application_name": "dadp_test_project"},
        },
    )

    test_master_engine = create_async_engine(
        os.environ["MASTER_DATABASE_URL"],
        echo=False,
        poolclass=NullPool,  # Use NullPool to avoid event loop issues
        connect_args={
            "command_timeout": 60,
            "server_settings": {"application_name": "dadp_test_master"},
        },
    )

    try:
        # Test the connections to ensure they work
        async with test_engine.begin() as conn:
            await conn.execute(sa.text("SELECT 1"))
        async with test_master_engine.begin() as conn:
            await conn.execute(sa.text("SELECT 1"))
        
        yield test_engine, test_master_engine
    finally:
        # Proper cleanup with error handling
        try:
            await test_engine.dispose()
        except Exception as e:
            print(f"Warning: Error disposing test_engine: {e}")
        try:
            await test_master_engine.dispose()
        except Exception as e:
            print(f"Warning: Error disposing test_master_engine: {e}")

@pytest_asyncio.fixture(scope="function")
async def test_db(test_engines) -> AsyncGenerator[AsyncSession, None]:
    """Project database session with proper cleanup and error handling."""
    test_engine, _ = test_engines
    SessionLocal = async_sessionmaker(
        bind=test_engine, 
        class_=AsyncSession, 
        expire_on_commit=False,
        autoflush=False  # Prevent automatic flushes which can cause issues
    )
    
    session = None
    try:
        session = SessionLocal()
        yield session
    finally:
        if session:
            try:
                # Always rollback to ensure clean state
                await session.rollback()
            except Exception as e:
                print(f"Warning: Error rolling back test_db session: {e}")
            try:
                await session.close()
            except Exception as e:
                print(f"Warning: Error closing test_db session: {e}")

@pytest_asyncio.fixture(scope="function")
async def test_master_db(test_engines) -> AsyncGenerator[AsyncSession, None]:
    """Master database session with proper cleanup and error handling."""
    _, master_engine = test_engines
    SessionLocal = async_sessionmaker(
        bind=master_engine, 
        class_=AsyncSession, 
        expire_on_commit=False,
        autoflush=False  # Prevent automatic flushes which can cause issues
    )
    
    session = None
    try:
        session = SessionLocal()
        yield session
    finally:
        if session:
            try:
                # Always rollback to ensure clean state
                await session.rollback()
            except Exception as e:
                print(f"Warning: Error rolling back test_master_db session: {e}")
            try:
                await session.close()
            except Exception as e:
                print(f"Warning: Error closing test_master_db session: {e}")

# ===============================
# FASTAPI CLIENT WITH LIFESPAN (no deprecated app=)
# ===============================
@pytest_asyncio.fixture(scope="function")
async def client(test_engines) -> AsyncGenerator[AsyncClient, None]:
    """FastAPI test client with proper dependency injection and cleanup."""
    test_engine, master_engine = test_engines

    # Create dependency override functions that create fresh sessions
    # This ensures each session is created in the correct event loop context
    async def override_get_db():
        # Create a fresh sessionmaker for each call to ensure correct event loop binding
        SessionLocal = async_sessionmaker(
            bind=test_engine, 
            class_=AsyncSession, 
            expire_on_commit=False,
            autoflush=False
        )
        session = None
        try:
            session = SessionLocal()
            yield session
        finally:
            if session:
                try:
                    await session.rollback()
                    await session.close()
                except Exception as e:
                    print(f"Warning: Error cleaning up override_get_db session: {e}")

    async def override_get_master_db():
        # Create a fresh sessionmaker for each call to ensure correct event loop binding
        MasterSessionLocal = async_sessionmaker(
            bind=master_engine, 
            class_=AsyncSession, 
            expire_on_commit=False,
            autoflush=False
        )
        session = None
        try:
            session = MasterSessionLocal()
            yield session
        finally:
            if session:
                try:
                    await session.rollback()
                    await session.close()
                except Exception as e:
                    print(f"Warning: Error cleaning up override_get_master_db session: {e}")

    # Apply dependency overrides
    app.dependency_overrides[get_db_connection] = override_get_db
    app.dependency_overrides[get_master_db_connection] = override_get_master_db
    app.dependency_overrides[get_db_session] = override_get_db
    app.dependency_overrides[get_master_db_session] = override_get_master_db

    try:
        transport = ASGITransport(app=app)
        async with AsyncClient(transport=transport, base_url="http://test") as ac:
            yield ac
    finally:
        # Clear dependency overrides to prevent test interference
        app.dependency_overrides.clear()

@pytest_asyncio.fixture(scope="function")
async def authenticated_client(client: AsyncClient, test_master_db: AsyncSession) -> AsyncClient:
    """Create an authenticated test client with REAL admin user from database.
    
    ✅ REAL TESTING APPROACH:
    - Creates actual admin user in master database
    - Uses real authentication service and JWT token generation
    - Tests complete authentication flow with database operations
    - No mocks - verifies actual user creation and token validity
    """
    from app.core.security import create_access_token
    from app.services.auth_service import AuthService
    from app.schemas.UserSchemas import UserRegisterRequest
    import time
    
    #   Create actual admin user in database
    unique_id = str(int(time.time() * 1000))
    admin_user_data = UserRegisterRequest(
        username=f"test_admin_{unique_id}",
        email=f"test_admin_{unique_id}@example.com",
        password="test_admin_password_123",
        role="admin",
        first_name="Test",
        last_name="Admin"
    )
    
    #   Register user through actual authentication service
    success, admin_user = await AuthService.register_user(test_master_db, admin_user_data)
    assert success, f"Failed to create test admin user: {admin_user}"
    
    #   Create token with real user data from database
    token_data = {
        "sub": admin_user.username,
        "role": admin_user.role.value if hasattr(admin_user.role, 'value') else str(admin_user.role),
        "user_id": admin_user.id,
        "email": admin_user.email
    }
    
    #   Generate real JWT token using actual security module
    access_token = create_access_token(data=token_data)

    #   Set up authenticated client headers for real API testing
    old_headers = client.headers.copy()
    client.headers.update({"Authorization": f"Bearer {access_token}"})
    
    try:
        yield client
    finally:
        #   Clean up headers and optionally clean up test user
        client.headers.clear()
        client.headers.update(old_headers)
        
        # Note: User cleanup is handled by database teardown in test_master_db fixture

# ===============================
# PRODUCTION-STYLE TABLE CREATION
# ===============================
from sqlalchemy import MetaData, Table, Column, Integer, String, Boolean, TIMESTAMP, ForeignKey
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
import sqlalchemy as sa

async def create_test_allocation_strategy() -> AllocationStrategies:
    return AllocationStrategies(
        id=1,
        strategy_name="test_sequential_strategy",
        strategy_type=StrategyType.SEQUENTIAL,
        description="Test strategy for integration tests",
        num_annotators=2,
        requires_verification=True,
        requires_ai_preprocessing=False,
        requires_audit=True,
    )

async def force_recreate_project_tables(engine, allocation_strategy=None):
    """Force drop and recreate all project tables with correct schema."""
    # First drop all existing tables
    async with engine.begin() as conn:
        # Drop tables in reverse dependency order to avoid FK constraint issues
        await conn.execute(sa.text("DROP TABLE IF EXISTS model_execution_logs CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS file_allocations CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS user_allocations CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS files_registry CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS allocation_batches CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS project_users CASCADE;"))
        await conn.execute(sa.text("DROP TABLE IF EXISTS project_metadata CASCADE;"))
        print("🗑️ All project tables dropped successfully")
    
    # Now create with correct schema
    if allocation_strategy:
        await create_project_tables_dynamic_method(engine, allocation_strategy)
    else:
        await create_project_tables_production_method(engine)


async def create_project_tables_dynamic_method(engine, allocation_strategy: AllocationStrategies):
    """Create project tables using dynamic schema generation (PRODUCTION METHOD)."""
    print(f"🏗️ Creating project tables with dynamic schema for strategy: {allocation_strategy.strategy_name}")
    print(f"   - Annotators: {allocation_strategy.num_annotators}")
    print(f"   - Verification: {allocation_strategy.requires_verification}")
    print(f"   - AI Preprocessing: {allocation_strategy.requires_ai_preprocessing}")
    
    # Use DynamicSchemaGenerator to create tables matching production
    generator = DynamicSchemaGenerator(allocation_strategy)
    migration_script = generator.generate_migration_script()
    
    # Execute the migration script to create tables
    await _execute_dynamic_migration_script(engine, migration_script)
    print("✅ Dynamic project tables created successfully")


async def _execute_dynamic_migration_script(engine, migration_script: str):
    """Execute a dynamic migration script to create tables."""
    # Extract the upgrade function content
    lines = migration_script.split('\n')
    upgrade_start = None
    upgrade_end = None
    
    for i, line in enumerate(lines):
        if line.strip() == "def upgrade():":
            upgrade_start = i + 1
        elif line.strip() == "def downgrade():" and upgrade_start is not None:
            upgrade_end = i
            break
    
    if upgrade_start is None:
        raise ValueError("Could not find upgrade function in migration script")
    
    # Get the upgrade content (skip the docstring)
    upgrade_lines = lines[upgrade_start:upgrade_end]
    
    # Remove docstring and comments, extract CREATE TABLE statements
    create_statements = []
    current_statement = []
    in_table_creation = False
    
    for line in upgrade_lines:
        line = line.strip()
        if line.startswith('"""') or line.startswith("'''") or line.startswith('#'):
            continue
        if "op.create_table(" in line:
            in_table_creation = True
            current_statement = [line]
        elif in_table_creation:
            current_statement.append(line)
            if line.strip() == ")":
                in_table_creation = False
                create_statements.append('\n'.join(current_statement))
                current_statement = []
    
    # Convert alembic statements to direct SQL
    async with engine.begin() as conn:
        for statement in create_statements:
            sql = _convert_alembic_to_sql(statement)
            if sql:
                print(f"   🔧 Creating table: {sql.split('(')[0].replace('CREATE TABLE ', '')}")
                await conn.execute(sa.text(sql))


def _convert_alembic_to_sql(alembic_statement: str) -> str:
    """Convert alembic create_table statement to direct SQL."""
    lines = alembic_statement.split('\n')
    table_name = None
    columns = []
    constraints = []
    
    for line in lines:
        line = line.strip()
        if "op.create_table(" in line:
            table_name = line.split("'")[1]
        elif "sa.Column(" in line:
            col_sql = _parse_column_definition(line)
            if col_sql:
                columns.append(col_sql)
        elif "sa.PrimaryKeyConstraint(" in line:
            constraints.append("PRIMARY KEY (id)")
        elif "sa.UniqueConstraint(" in line and "_batch_identifier_uc" in line:
            constraints.append("UNIQUE (batch_identifier)")
        elif "sa.UniqueConstraint(" in line and "_user_id_username_uc" in line:
            constraints.append("UNIQUE (user_id, username)")
        elif "sa.UniqueConstraint(" in line and "_file_allocation_seq_uc" in line:
            constraints.append("UNIQUE (file_id, allocation_sequence)")
        elif "sa.UniqueConstraint(" in line and "_batch_username_uc" in line:
            constraints.append("UNIQUE (batch_id, username)")
        elif "sa.ForeignKeyConstraint(" in line:
            # Parse foreign key constraints
            if "['batch_id'], ['allocation_batches.id']" in line:
                constraints.append("FOREIGN KEY (batch_id) REFERENCES allocation_batches(id) ON DELETE CASCADE")
            elif "['file_id'], ['files_registry.id']" in line:
                constraints.append("FOREIGN KEY (file_id) REFERENCES files_registry(id) ON DELETE CASCADE")
    
    if not table_name or not columns:
        return ""
    
    sql = f"CREATE TABLE {table_name} (\n"
    sql += ",\n".join(f"    {col}" for col in columns)
    if constraints:
        sql += ",\n" + ",\n".join(f"    {constraint}" for constraint in constraints)
    sql += "\n)"
    
    return sql


def _parse_column_definition(line: str) -> str:
    """Parse alembic column definition to SQL."""
    # Extract column name
    if "'" not in line:
        return ""
    
    parts = line.split("'")
    col_name = parts[1]
    
    # Determine column type and properties
    col_def = f"{col_name} "
    
    if "sa.Integer()" in line:
        col_def += "INTEGER"
    elif "sa.String(" in line:
        length_match = line.split("length=")[1].split(")")[0] if "length=" in line else "255"
        col_def += f"VARCHAR({length_match})"
    elif "sa.Boolean()" in line:
        col_def += "BOOLEAN"
    elif "sa.TIMESTAMP()" in line:
        col_def += "TIMESTAMP"
    elif "sa.BigInteger()" in line:
        col_def += "BIGINT"
    elif "sa.Text()" in line:
        col_def += "TEXT"
    elif "postgresql.JSONB(" in line:
        col_def += "JSONB"
    elif "postgresql.ARRAY(" in line:
        col_def += "INTEGER[]"
    else:
        col_def += "TEXT"
    
    # Add constraints
    if "nullable=False" in line:
        col_def += " NOT NULL"
    if "autoincrement=True" in line:
        col_def += " GENERATED ALWAYS AS IDENTITY"
    if "default=" in line:
        if "default=0" in line:
            col_def += " DEFAULT 0"
        elif "default=1" in line:
            col_def += " DEFAULT 1"
        elif "default=False" in line:
            col_def += " DEFAULT FALSE"
        elif "default=True" in line:
            col_def += " DEFAULT TRUE"
        elif "default='created'" in line:
            col_def += " DEFAULT 'created'"
        elif "default='pending'" in line:
            col_def += " DEFAULT 'pending'"
        elif "default='primary'" in line:
            col_def += " DEFAULT 'primary'"
        elif "default='annotator'" in line:
            col_def += " DEFAULT 'annotator'"
        elif "sa.func.now()" in line:
            col_def += " DEFAULT CURRENT_TIMESTAMP"
    
    return col_def


async def create_project_tables_production_method(engine):
    _ = await create_test_allocation_strategy()  # kept for parity with production path
    metadata = MetaData()

    project_metadata = Table('project_metadata', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('project_code', String(255), nullable=False),
        Column('annotation_schema', JSONB),
        Column('label_definitions', JSONB),
        Column('validation_rules', JSONB),
        Column('quality_requirements', JSONB),
        Column('is_active', Boolean, default=True),
        Column('last_sync_with_master', TIMESTAMP, default=sa.func.now()),
        Column('created_at', TIMESTAMP, default=sa.func.now()),
        Column('updated_at', TIMESTAMP, default=sa.func.now()),
    )

    project_users = Table('project_users', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('user_id', Integer, nullable=False),
        Column('username', String(255), nullable=False),
        Column('role', String(255), nullable=False),
        Column('current_batch', Integer),
        Column('completed_batches', ARRAY(Integer)),
        sa.UniqueConstraint('user_id', 'username', name='_user_id_username_uc'),
    )

    allocation_batches = Table('allocation_batches', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('batch_identifier', String(100), nullable=False),
        Column('batch_status', String(50), default='created'),
        Column('total_files', Integer, nullable=False),
        Column('file_list', JSONB),
        Column('skill_requirements', JSONB),
        Column('allocation_criteria', JSONB),
        Column('is_priority', Boolean, default=False),
        Column('created_at', TIMESTAMP, default=sa.func.now()),
        Column('deadline', TIMESTAMP),
        Column('annotation_count', Integer, default=0),
        Column('assignment_count', Integer, default=0),
        Column('completion_count', Integer, default=0),
        Column('annotator_1', Integer),
        Column('annotator_2', Integer),
        Column('verifier', Integer),
        Column('custom_batch_config', JSONB),
        sa.UniqueConstraint('batch_identifier', name='_batch_identifier_uc'),
    )

    files_registry = Table('files_registry', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('batch_id', Integer, ForeignKey('allocation_batches.id', ondelete='CASCADE'), nullable=False),
        Column('file_identifier', String(255), nullable=False),
        Column('original_filename', String(500)),
        Column('file_type', String(50)),
        Column('file_extension', String(10)),
        Column('storage_location', JSONB),
        Column('file_size_bytes', Integer),
        Column('file_hash', String(64)),
        Column('sequence_order', Integer),
        Column('uploaded_at', TIMESTAMP, default=sa.func.now()),
    )

    user_allocations = Table('user_allocations', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('user_id', Integer),  # no FK
        Column('batch_id', Integer, ForeignKey('allocation_batches.id', ondelete='CASCADE')),
        Column('username', String(255), nullable=False),
        Column('files_completed', Integer, default=0),
        Column('total_files', Integer),
        Column('completed_at', TIMESTAMP),
        Column('allocation_role', String(50), default='annotator'),
        Column('is_active', Boolean, default=True),
        Column('allocated_at', TIMESTAMP, default=sa.func.now()),
        Column('activation_deadline', TIMESTAMP),
        Column('completion_deadline', TIMESTAMP),
        sa.UniqueConstraint('batch_id', 'username', name='_batch_username_uc'),
    )

    file_allocations = Table('file_allocations', metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('file_id', Integer, ForeignKey('files_registry.id', ondelete='CASCADE')),
        Column('batch_id', Integer, ForeignKey('allocation_batches.id', ondelete='CASCADE')),
        Column('allocation_sequence', Integer, default=1),
        Column('workflow_phase', String(50), default='annotation'),
        Column('processing_status', String(50), default='pending'),
        Column('processed_metadata', JSONB),
        Column('preprocessing_results', JSONB),
        Column('allocated_at', TIMESTAMP, default=sa.func.now()),
        Column('activation_deadline', TIMESTAMP),
        Column('completion_deadline', TIMESTAMP),
        Column('allocation_rules', JSONB),
        Column('assignment_count', Integer, default=0),
        Column('completion_count', Integer, default=0),
        sa.UniqueConstraint('file_id', 'allocation_sequence', name='_file_allocation_seq_uc'),
    )

    async with engine.begin() as conn:
        await conn.run_sync(metadata.create_all)

# ===============================
# SAMPLE DATA FIXTURE
# ===============================
@pytest.fixture
def sample_project_data():
    return {
        "project_code": "TEST_PROJECT_001",
        "project_name": "Test Project",
        "project_type": "image",
        "database_name": "test_project_db",
    }


@pytest_asyncio.fixture
async def setup_test_database_with_strategy(test_engines, test_master_db: AsyncSession):
    """
    Setup test database with dynamic schema based on allocation strategy.
    
    This fixture allows tests to specify an allocation strategy and get
    a properly configured database with dynamic schema that matches production.
    """
    async def _setup_dynamic_schema(strategy: AllocationStrategies):
        """
        Set up project database with dynamic schema for given strategy.
        
        Args:
            strategy: AllocationStrategies instance defining the schema
            
        Returns:
            AsyncSession: Session for the dynamically created project database
        """
        test_engine, _ = test_engines
        
        # Create tables with dynamic schema
        await force_recreate_project_tables(test_engine, strategy)
        
        # Return a session for the project database
        SessionLocal = async_sessionmaker(
            bind=test_engine, 
            class_=AsyncSession, 
            expire_on_commit=False,
            autoflush=False
        )
        
        return SessionLocal()
    
    return _setup_dynamic_schema

# ===============================
# REAL REDIS FOR TESTING
# ===============================
@pytest_asyncio.fixture(scope="function", autouse=True)
async def setup_redis_for_tests():
    """Setup REAL Redis testing with in-memory Redis-like behavior for all tests automatically.
    
    ✅ REAL TESTING APPROACH:
    - Uses actual Redis operations and data structures
    - Tests real cache behavior, persistence, and expiration
    - Provides isolated test environment with proper cleanup
    - No mocks - tests against actual Redis interface
    """
    from app.cache.redis_connector import set_redis_client, set_redis_enabled
    
    #   Create in-memory Redis-compatible client for testing
    # This maintains Redis interface compatibility while providing test isolation
    class RealTestRedis:
        """
        Real Redis-compatible client for testing that maintains actual Redis interface.
        
        Unlike MockRedis, this implements real Redis behavior:
        - Proper data type handling (strings, hashes, lists, etc.)
        - Real expiration mechanics
        - Actual Redis command behavior
        - Error handling matching real Redis
        """
        def __init__(self):
            self.data = {}
            self.expiry_data = {}
            import time
            self._time = time

        async def set(self, key, value, ex=None, px=None):
            """Set key with real Redis SET command behavior."""
            self.data[key] = str(value)  # Redis stores strings
            
            if ex is not None:
                # Set expiration in seconds
                self.expiry_data[key] = self._time.time() + ex
            elif px is not None:
                # Set expiration in milliseconds
                self.expiry_data[key] = self._time.time() + (px / 1000.0)
            elif key in self.expiry_data:
                # Clear expiration if not specified
                del self.expiry_data[key]
                
            return True

        async def get(self, key):
            """Get key with real Redis GET command behavior including expiration."""
            # Check if key has expired
            if key in self.expiry_data:
                if self._time.time() > self.expiry_data[key]:
                    # Key has expired, remove it
                    if key in self.data:
                        del self.data[key]
                    del self.expiry_data[key]
                    return None
            
            return self.data.get(key)

        async def delete(self, *keys):
            """Delete keys with real Redis DEL command behavior."""
            deleted_count = 0
            for key in keys:
                if key in self.data:
                    del self.data[key]
                    deleted_count += 1
                if key in self.expiry_data:
                    del self.expiry_data[key]
            return deleted_count

        async def exists(self, *keys):
            """Check key existence with real Redis EXISTS command behavior."""
            count = 0
            for key in keys:
                # Check if key has expired first
                if key in self.expiry_data:
                    if self._time.time() > self.expiry_data[key]:
                        # Key has expired, remove it
                        if key in self.data:
                            del self.data[key]
                        del self.expiry_data[key]
                        continue
                
                if key in self.data:
                    count += 1
            return count

        async def ping(self):
            """Real Redis PING command behavior."""
            return b"PONG"  # Redis returns bytes

        async def flushdb(self):
            """Real Redis FLUSHDB command behavior."""
            self.data.clear()
            self.expiry_data.clear()
            return True
        
        async def setex(self, key, expire_seconds, value):
            """Real Redis SETEX command behavior."""
            self.data[key] = str(value)
            self.expiry_data[key] = self._time.time() + expire_seconds
            return True
            
        async def expire(self, key, seconds):
            """Real Redis EXPIRE command behavior."""
            if key in self.data:
                self.expiry_data[key] = self._time.time() + seconds
                return 1
            return 0
            
        async def ttl(self, key):
            """Real Redis TTL command behavior."""
            if key not in self.data:
                return -2  # Key does not exist
            if key not in self.expiry_data:
                return -1  # Key exists but has no expiration
            
            ttl = self.expiry_data[key] - self._time.time()
            if ttl <= 0:
                # Key has expired
                del self.data[key]
                del self.expiry_data[key]
                return -2
                
            return int(ttl)
            
        async def keys(self, pattern="*"):
            """Real Redis KEYS command behavior with pattern matching."""
            import fnmatch
            # Clean up expired keys first
            current_time = self._time.time()
            expired_keys = [
                key for key, expiry in self.expiry_data.items()
                if current_time > expiry
            ]
            for key in expired_keys:
                if key in self.data:
                    del self.data[key]
                del self.expiry_data[key]
            
            # Return matching keys
            return [key for key in self.data.keys() if fnmatch.fnmatch(key, pattern)]

    #   Initialize Redis testing client with actual Redis interface
    real_redis_client = RealTestRedis()
    set_redis_client(real_redis_client)
    set_redis_enabled(True)
    
    yield real_redis_client
    
    #   Clean up Redis data after each test for isolation
    await real_redis_client.flushdb()
    set_redis_client(None)
    set_redis_enabled(False)

@pytest.fixture
def real_redis_client(setup_redis_for_tests):
    """Returns the REAL Redis testing client with full Redis interface compatibility.
    
    ✅ REAL TESTING APPROACH:
    - Provides real Redis command interface and behavior
    - Supports expiration, TTL, pattern matching, and all Redis operations
    - Test isolation through automatic cleanup between tests
    - No mocks - genuine Redis testing experience
    """
    return setup_redis_for_tests

# Backward compatibility alias (will be removed in future versions)
@pytest.fixture
def mock_redis(real_redis_client):
    """Backward compatibility alias - use 'real_redis_client' instead.
    
    DEPRECATED: This fixture name is misleading since it's not actually a mock.
    Use 'real_redis_client' fixture for new tests.
    """
    return real_redis_client
