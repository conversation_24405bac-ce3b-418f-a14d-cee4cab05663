# """
# OCR Extractor module for handling OCR extraction from images
# using HTTP API-based inference.
# """
# import io
# import base64
# import requests
# from datetime import datetime, timedelta
# from fastapi import HTTPException, status
# from typing import Dict, Any, Optional, Union, BinaryIO
# from pydantic import BaseModel
# import logging
# from core.config import settings
# from post_db.connect import get_db_session
# from post_db.models.image_extractor import ImageExtractor

# # Configure logging
# logger = logging.getLogger('image_extractor_service')

# class OCRResponse(BaseModel):
#     """Model for OCR API responses"""
#     success: bool
#     data: Union[str, Dict[str, Any]]

# class ImageUploadResponse(BaseModel):
#     """Model for image upload responses"""
#     image_id: str
#     filename: str

# class OCRExtractor:
#     """
#     Class for extracting text from images using OCR API
#     """
#     def __init__(self):
#         self.api_url = settings.api_settings.ocr_url
#         self.api_key = settings.api_settings.ocr_key
#         self.api_headers = settings.api_settings.ocr_headers
#         logger.info("OCRExtractor initialized with OCR API settings")
    
#     async def process_image(self, image_file: BinaryIO) -> ImageUploadResponse:
#         """
#         Process and store an image file
        
#         Args:
#             image_file: Image file object
            
#         Returns:
#             ImageUploadResponse: Response containing image ID and filename
#         """
#         try:
#             original_filename = image_file.filename
#             image_bytes = image_file.file.read()
        
#             encoded_data = base64.b64encode(image_bytes).decode('utf-8')
            
#             async with get_db_session() as session:
#                 record = ImageExtractor(
#                     image_name=original_filename,
#                     image_data=encoded_data
#                 )
#                 session.add(record)
#                 await session.commit()
#                 await session.refresh(record)
#                 image_id = str(record.id)
#             logger.info(f"Stored image to database with ID: {image_id}, filename: {original_filename}")
#             return ImageUploadResponse(
#                 image_id=image_id,
#                 filename=original_filename
#             )
#         except Exception as e:
#             error_msg = f"Error processing image: {str(e)}"
#             logger.error(error_msg, exc_info=True)
#             raise HTTPException(
#                 status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#                 detail=error_msg
#             )

#     async def get_stored_image(self, image_id: str) -> Dict[str, Any]:
#         """
#         Get stored image data by ID
        
#         Args:
#             image_id: ID of the stored image
            
#         Returns:
#             Dict containing image data and filename
#         """
#         try:
#             image_int = int(image_id)
#         except ValueError:
#             error_msg = f"Invalid image ID: {image_id}"
#             logger.error(error_msg)
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail=error_msg
#             )
#         async with get_db_session() as session:
#             record = await session.get(ImageExtractor, image_int)
#             if not record:
#                 error_msg = f"Image not found for ID: {image_id}"
#                 logger.error(error_msg)
#                 raise HTTPException(
#                     status_code=status.HTTP_404_NOT_FOUND,
#                     detail=error_msg
#                 )
#             image_bytes = base64.b64decode(record.image_data)
#             return {
#                 'uploaded_at': record.uploaded_at,
#                 'filename': record.image_name,
#                 'image_data': image_bytes
#             }

#     async def extract_text(
#         self,
#         image_data: Union[bytes, BinaryIO],
#         custom_prompt: Optional[str] = None
#     ) -> str:
#         """
#         Extract text from an image using OCR API

#         Args:
#             image_data: Image data (file object or bytes)
#             filename: Optional filename
#             custom_prompt: Optional custom extraction prompt

#         Returns:
#             str: Extracted and processed text
#         """
#         try:
#             # Create the prompt
#             prompt = custom_prompt or "Extract all text from this image carefully. Include all visible text, numbers, labels, and maintain the structure and layout if possible. Format the result in a clean, readable way."
            
#             # Prepare files for the API request
#             if isinstance(image_data, bytes):
#                 files = {
#                     'file': ( io.BytesIO(image_data)),
#                     'question': (None, prompt)
#                 }
#             else:
#                 files = {
#                     'file': ( image_data),
#                     'question': (None, prompt)
#                 }

#             response = requests.post(self.api_url, headers=self.api_headers, files=files)

#             if response.status_code == 200:
#                 extracted_text = response.json()["response"]
    
#                 return self._post_process_extracted_text(extracted_text)
#             else:
#                 error_msg = f"API Error {response.status_code}: {response.text}"
#                 logger.error(error_msg)
#                 raise HTTPException(
#                     status_code=status.HTTP_502_BAD_GATEWAY,
#                     detail=error_msg )
#         except HTTPException:
#             raise
#         except Exception as e:
#             error_msg = f"Error extracting text: {str(e)}"
#             logger.error(error_msg, exc_info=True)
#             raise HTTPException(
#                 status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#                 detail=error_msg
#             )
    
#     def _post_process_extracted_text(self, text: str) -> str:
#         """
#         Post-process extracted text to improve formatting
#         """
#         # Remove any "I see the following text:" or similar prefixes
#         prefixes_to_remove = [
#             "I can see the following text:",
#             "The text in the image says:",
#             "The image contains the following text:",
#             "Text extracted from the image:",
#             "The text reads:",
#             "The text in the image reads:",
#             "I see the text:",
#         ]
        
#         result = text
#         for prefix in prefixes_to_remove:
#             if result.startswith(prefix):
#                 result = result[len(prefix):].strip()
        
#         # Clean up extra whitespace and normalize line breaks
#         result = '\n'.join(line.strip() for line in result.splitlines())
        
#         return result
            
#     async def chat_about_image(
#         self, 
#         image_data: Union[bytes, BinaryIO], 
#         query: str, 
#     ) -> str:
#         """
#         Process a chat query about an image using API

#         Args:
#             image_data: Image data (file object or bytes)
#             query: User's question about the image

#         Returns:
#             str: Chat response
#         """
#         try:
#             enhanced_query = f"Look at this image carefully and answer the following question based ONLY on what you can see in the image. Do not make up information or provide generic responses. Question: {query}"
            
#             if isinstance(image_data, bytes):
#                 files = {
#                     'file': (io.BytesIO(image_data)),
#                     'question': (None, enhanced_query) }
#             else:
#                 files = {
#                     'file': (image_data),
#                     'question': (None, enhanced_query) }
            
#             response = requests.post(self.api_url+"/infer/text", headers=self.api_headers, files=files)
            
#             if response.status_code == 200:
#                 return response.json()["response"]
#             else:
#                 error_msg = f"API Error {response.status_code}: {response.text}"
#                 logger.error(error_msg)
#                 raise HTTPException(
#                     status_code=status.HTTP_502_BAD_GATEWAY,
#                     detail=error_msg
#                 )
                
#         except HTTPException:
#             raise
#         except Exception as e:
#             error_msg = f"Error processing chat: {str(e)}"
#             logger.error(error_msg, exc_info=True)
#             raise HTTPException(
#                 status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#                 detail=error_msg
#             )

#     async def cleanup_old_images(self, max_age_hours: int = 24) -> None:
#         """
#         Clean up old image metadata
#         Args:
#             max_age_hours: Maximum age in hours before deletion
#         """
#         try:
#             cutoff = datetime.now() - timedelta(hours=max_age_hours)
#             async with get_db_session() as session:
#                 from sqlalchemy import select, delete
#                 # Use async delete operation
#                 stmt = delete(ImageExtractor).where(ImageExtractor.uploaded_at < cutoff)
#                 result = await session.execute(stmt)
#                 deleted = result.rowcount
#                 await session.commit()
#                 if deleted:
#                     logger.info(f"Cleaned up {deleted} old image records from database")
#         except Exception as e:
#             logger.error(f"Error cleaning up old image metadata: {str(e)}", exc_info=True)

# # Create a singleton instance
# _ocr_extractor = OCRExtractor()

# def get_ocr_extractor() -> OCRExtractor:
#     """Get the singleton OCR extractor instance"""
#     return _ocr_extractor