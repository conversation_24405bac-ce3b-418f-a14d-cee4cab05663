export type SourceType = 'google_drive' | 'nas' | 'local' | 'api' | 'telegram' | 'twitter' | 'youtube' | 'linkedin' | 'instagram' | 'database';

export interface DataSource {
  id: string;
  name: string;
  type: SourceType;
  icon: string;
  description: string;
  features: string[];
  status: 'connected' | 'coming_soon';
  disabled?: boolean;
}

export const dataSources: DataSource[] = [
  {
    id: 'telegram',
    name: 'Telegram',
    type: 'telegram',
    icon: 'FaTelegram',
    description: 'Connect to Telegram channels and download media content for processing.',
    features: [
      'Access channel messages',
      'Download media files',
      'Filter by date and type',
      'Batch processing'
    ],
    status: 'connected'
  },
  {
    id: 'twitter',
    name: 'Twitter',
    type: 'twitter',
    icon: 'FaTwitter',
    description: 'Track hashtags and download media from Twitter for analysis.',
    features: [
      'Track hashtags',
      'Monitor accounts',
      'Download media',
      'Sentiment analysis'
    ],
    status: 'coming_soon'
  },
  {
    id: 'database',
    name: 'Database',
    type: 'database',
    icon: 'FaDatabase',
    description: 'Connect to external databases and import data for processing.',
    features: [
      'SQL databases',
      'NoSQL databases',
      'Custom Database',
      'Scheduled imports'
    ],
    status: 'coming_soon'
  },
  {
    id: 'youtube',
    name: 'YouTube',
    type: 'youtube',
    icon: 'FaYoutube',
    description: 'Access YouTube videos and channels for content analysis.',
    features: [
      'Download videos',
      'Channel analysis',
      'Comment extraction',
      'Metadata processing'
    ],
    status: 'coming_soon'
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    type: 'linkedin',
    icon: 'FaLinkedin',
    description: 'Monitor LinkedIn posts and profiles for professional insights.',
    features: [
      'Profile tracking',
      'Post analysis',
      'Media collection',
      'Engagement metrics'
    ],
    status: 'coming_soon'
  },
  {
    id: 'nas',
    name: 'NAS',
    type: 'nas',
    icon: 'FaServer',
    description: 'Connect to network storage devices for secure data import and processing.',
    features: [
      'Bulk file import',
      'Scheduled syncing',
      'Secure connections',
      'Automated backup'
    ],
    status: 'coming_soon'
  },
  {
    id: 'google_drive',
    name: 'Google Drive',
    type: 'google_drive',
    icon: 'FaGoogle',
    description: 'Import files and documents from Google Drive for analysis.',
    features: [
      'Document import',
      'Media collection',
      'Folder monitoring',
      'Auto-synchronization'
    ],
    status: 'coming_soon'
  },
  {
    id: 'instagram',
    name: 'Instagram',
    type: 'instagram',
    icon: 'FaInstagram',
    description: 'Track Instagram posts and stories for visual content analysis.',
    features: [
      'Post tracking',
      'Story collection',
      'Hashtag monitoring',
      'Image analysis'
    ],
    status: 'coming_soon'
  }
]; 