"""
Comprehensive unit tests for BatchAllocationSyncService.
Tests batch synchronization, progress tracking, and cross-database operations.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
import time
from datetime import datetime

from app.services.batch_allocation_sync_service import BatchAllocationSyncService

class TestBatchAllocationSyncServiceUnit:
    """Unit tests for BatchAllocationSyncService with mocked dependencies."""
    
    @pytest.fixture
    def sync_service(self):
        """BatchAllocationSyncService instance for testing."""
        return BatchAllocationSyncService()
    
    @pytest.fixture
    def mock_project_data(self):
        """Mock project data for testing."""
        return {
            'valid_project': {
                'id': 1,
                'project_code': 'SYNC_TEST_001',
                'total_files': 1000,
                'processed_files': 750,
                'status': 'active'
            },
            'completed_project': {
                'id': 2,
                'project_code': 'SYNC_COMPLETE_002', 
                'total_files': 500,
                'processed_files': 500,
                'status': 'completed'
            },
            'inactive_project': {
                'id': 3,
                'project_code': 'SYNC_INACTIVE_003',
                'total_files': 200,
                'processed_files': 50,
                'status': 'paused'
            }
        }
    
    @pytest.fixture
    def mock_batch_data(self):
        """Mock batch allocation data."""
        return {
            'active_batches': [
                {'id': 1, 'batch_identifier': 'BATCH_001', 'completion_count': 10, 'total_files': 15},
                {'id': 2, 'batch_identifier': 'BATCH_002', 'completion_count': 8, 'total_files': 12},
                {'id': 3, 'batch_identifier': 'BATCH_003', 'completion_count': 20, 'total_files': 20}
            ],
            'completed_batches': [
                {'id': 4, 'batch_identifier': 'BATCH_004', 'completion_count': 25, 'total_files': 25},
                {'id': 5, 'batch_identifier': 'BATCH_005', 'completion_count': 30, 'total_files': 30}
            ]
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_batch_allocations_success(self, sync_service, mock_project_data, 
                                                  mock_batch_data, mock_db_session):
        """Test successful batch allocation synchronization."""
        project_id = mock_project_data['valid_project']['id']
        
        # Mock project retrieval from master database
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = mock_project_data['valid_project']['project_code']
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        # Mock project repository
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock batch count query
            mock_count_result = AsyncMock()
            mock_count_result.scalar.return_value = len(mock_batch_data['active_batches'])
            mock_project_session.execute.return_value = mock_count_result
            
            result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
            
            assert result['success'] is True
            assert result['batches_synced'] == len(mock_batch_data['active_batches'])
            assert result['project_code'] == mock_project_data['valid_project']['project_code']
            assert mock_db_session.execute.called
            assert mock_get_session.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_batch_allocations_project_not_found(self, sync_service, mock_db_session):
        """Test sync when project is not found."""
        nonexistent_project_id = 999
        
        # Mock project not found
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result
        
        result = await sync_service.sync_batch_allocations(nonexistent_project_id, mock_db_session)
        
        assert result['success'] is False
        assert 'not found' in result['error'].lower()
        assert result['batches_synced'] == 0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_batch_allocations_database_error(self, sync_service, mock_db_session):
        """Test sync handling database errors."""
        project_id = 1
        
        # Mock database exception
        mock_db_session.execute.side_effect = Exception("Database connection failed")
        
        result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
        
        assert result['success'] is False
        assert 'error' in result
        assert 'database connection failed' in result['error'].lower()

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_refresh_project_progress_success(self, sync_service, mock_project_data, mock_db_session):
        """Test successful project progress refresh."""
        project_id = mock_project_data['valid_project']['id']
        project_code = mock_project_data['valid_project']['project_code']
        
        # Mock progress calculation
        with patch.object(sync_service, '_calculate_project_progress') as mock_calculate:
            mock_calculate.return_value = {
                'total_batches': 10,
                'completed_batches': 7,
                'in_progress_batches': 2,
                'pending_batches': 1,
                'completion_percentage': 70.0
            }
            
            # Mock progress update in master database
            with patch.object(sync_service, '_update_master_progress') as mock_update:
                mock_update.return_value = True
                
                result = await sync_service.refresh_project_progress(project_id, project_code, mock_db_session)
                
                assert result['success'] is True
                assert result['completion_percentage'] == 70.0
                assert result['completed_batches'] == 7
                assert mock_calculate.called
                assert mock_update.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_calculate_project_progress_accurate(self, sync_service, mock_batch_data):
        """Test accurate project progress calculation."""
        batches = mock_batch_data['active_batches'] + mock_batch_data['completed_batches']
        
        with patch.object(sync_service, '_get_project_batches') as mock_get_batches:
            mock_get_batches.return_value = batches
            
            with patch.object(sync_service, '_calculate_project_progress') as mock_calculate:
                # Calculate expected values
                total_batches = len(batches)
                completed_batches = len([b for b in batches if b['completion_count'] == b['total_files']])
                completion_percentage = (completed_batches / total_batches) * 100
                
                mock_calculate.return_value = {
                    'total_batches': total_batches,
                    'completed_batches': completed_batches,
                    'completion_percentage': completion_percentage
                }
                
                result = sync_service._calculate_project_progress(batches)
                
                assert result['total_batches'] == 5  # 3 active + 2 completed
                assert result['completed_batches'] == 2  # Only fully completed batches
                assert result['completion_percentage'] == 40.0  # 2/5 * 100

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_completion_statistics_comprehensive(self, sync_service, mock_batch_data):
        """Test comprehensive completion statistics calculation."""
        project_code = 'STATS_TEST_001'
        
        with patch.object(sync_service, '_query_project_statistics') as mock_query:
            mock_query.return_value = {
                'total_files': 1000,
                'completed_files': 750,
                'pending_files': 150,
                'in_progress_files': 100,
                'average_completion_time': 45.5,  # seconds per file
                'total_annotators': 5,
                'active_annotators': 3
            }
            
            result = await sync_service.get_completion_statistics(project_code)
            
            assert result['total_files'] == 1000
            assert result['completion_rate'] == 75.0  # 750/1000 * 100
            assert result['estimated_completion_time'] > 0
            assert result['active_annotators'] == 3

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_user_allocations_cross_batches(self, sync_service, mock_db_session):
        """Test synchronization of user allocations across batches."""
        project_code = 'USER_SYNC_001'
        
        mock_allocations = [
            {'user_id': 1, 'batch_id': 'BATCH_001', 'files_assigned': 10, 'files_completed': 8},
            {'user_id': 1, 'batch_id': 'BATCH_002', 'files_assigned': 12, 'files_completed': 5},
            {'user_id': 2, 'batch_id': 'BATCH_001', 'files_assigned': 8, 'files_completed': 8},
        ]
        
        with patch.object(sync_service, '_get_user_allocations') as mock_get_allocations:
            mock_get_allocations.return_value = mock_allocations
            
            with patch.object(sync_service, '_update_user_allocation_summary') as mock_update:
                mock_update.return_value = True
                
                result = await sync_service.sync_user_allocations(project_code, mock_db_session)
                
                assert result['success'] is True
                assert result['users_synced'] == 2  # Two unique users
                assert mock_get_allocations.called
                assert mock_update.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_performance_large_project_sync(self, sync_service, performance_monitor, 
                                                  service_performance_data, mock_db_session):
        """Test performance with large project synchronization."""
        project_id = 1
        
        # Mock large project with many batches
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = 'LARGE_PROJECT_001'
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock large batch count (1000 batches)
            mock_count_result = AsyncMock()
            mock_count_result.scalar.return_value = 1000
            mock_project_session.execute.return_value = mock_count_result
            
            performance_monitor.start()
            
            result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
            
            performance_monitor.stop()
            
            execution_time = performance_monitor.get_execution_time()
            acceptable_time = service_performance_data['acceptable_response_times']['sync_allocations']
            
            assert execution_time < acceptable_time, f"Sync time {execution_time}s exceeds limit"
            assert result['batches_synced'] == 1000

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_sync_operations(self, sync_service, mock_db_session):
        """Test concurrent synchronization operations on different projects."""
        import asyncio
        
        project_ids = [1, 2, 3, 4, 5]
        
        # Mock successful project retrieval for all projects
        mock_projects = []
        for pid in project_ids:
            mock_project = MagicMock()
            mock_project.id = pid
            mock_project.project_code = f'CONCURRENT_PROJECT_{pid:03d}'
            mock_projects.append(mock_project)
        
        mock_results = [AsyncMock() for _ in project_ids]
        for i, mock_result in enumerate(mock_results):
            mock_result.scalar_one_or_none.return_value = mock_projects[i]
        
        mock_db_session.execute.side_effect = mock_results
        
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            mock_count_result = AsyncMock()
            mock_count_result.scalar.return_value = 10  # 10 batches per project
            mock_project_session.execute.return_value = mock_count_result
            
            # Run concurrent sync operations
            tasks = []
            for project_id in project_ids:
                task = sync_service.sync_batch_allocations(project_id, mock_db_session)
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All operations should complete successfully
            exceptions = [r for r in results if isinstance(r, Exception)]
            assert len(exceptions) == 0
            
            successful_results = [r for r in results if r.get('success', False)]
            assert len(successful_results) == len(project_ids)

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_data_consistency_validation(self, sync_service, mock_db_session):
        """Test data consistency validation during sync."""
        project_id = 1
        
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = 'CONSISTENCY_001'
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        # Mock inconsistent data scenario
        with patch.object(sync_service, '_validate_data_consistency') as mock_validate:
            mock_validate.return_value = {
                'consistent': False,
                'issues': [
                    'Batch BATCH_001: completion_count > total_files',
                    'User allocation mismatch in BATCH_002'
                ]
            }
            
            with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
                mock_project_session = AsyncMock()
                mock_get_session.return_value = mock_project_session
                
                result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
                
                # Should detect and report inconsistencies
                assert 'data_consistency_issues' in result
                assert len(result['data_consistency_issues']) == 2

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_rollback_on_sync_failure(self, sync_service, mock_db_session):
        """Test transaction rollback when sync operation fails."""
        project_id = 1
        
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = 'ROLLBACK_001'
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock database error during sync
            mock_project_session.execute.side_effect = Exception("Constraint violation")
            
            result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
            
            # Should rollback and return error
            assert result['success'] is False
            assert 'constraint violation' in result['error'].lower()
            # Verify rollback was called (in real implementation)

    @pytest.mark.unit
    def test_memory_usage_large_sync(self, sync_service, service_performance_data):
        """Test memory usage during large synchronization operations."""
        import sys
        
        # Simulate large dataset synchronization
        large_batch_data = []
        for i in range(10000):
            batch = {
                'id': i,
                'batch_identifier': f'BATCH_{i:06d}',
                'total_files': 50,
                'completion_count': i % 50,  # Varying completion
                'assignment_data': [f'user_{j}' for j in range(i % 5)],  # Varying assignments
                'metadata': {'created_at': datetime.now(), 'updated_at': datetime.now()}
            }
            large_batch_data.append(batch)
        
        initial_size = sys.getsizeof(large_batch_data)
        
        # Simulate processing and progress calculation
        processed_data = {}
        for batch in large_batch_data:
            processed_data[batch['batch_identifier']] = {
                'completion_rate': batch['completion_count'] / batch['total_files'],
                'assigned_users': len(batch['assignment_data']),
                'status': 'completed' if batch['completion_count'] == batch['total_files'] else 'in_progress'
            }
        
        final_size = sys.getsizeof(processed_data)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['batch_processing']
        assert memory_increase < max_memory, f"Memory usage {memory_increase}MB exceeds limit"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_sync_with_deleted_batches_handling(self, sync_service, mock_db_session):
        """Test handling of deleted batches during synchronization."""
        project_id = 1
        
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = 'DELETED_BATCH_001'
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock scenario with deleted batches (references exist but batches don't)
            with patch.object(sync_service, '_identify_orphaned_references') as mock_identify:
                mock_identify.return_value = {
                    'orphaned_allocations': ['BATCH_999', 'BATCH_888'],
                    'missing_batches': 2
                }
                
                with patch.object(sync_service, '_cleanup_orphaned_references') as mock_cleanup:
                    mock_cleanup.return_value = {'cleaned_references': 2}
                    
                    result = await sync_service.sync_batch_allocations(project_id, mock_db_session)
                    
                    assert result['success'] is True
                    assert 'orphaned_references_cleaned' in result
                    assert result['orphaned_references_cleaned'] == 2

    @pytest.mark.unit 
    @pytest.mark.asyncio
    async def test_sync_progress_reporting(self, sync_service, mock_db_session):
        """Test progress reporting during long synchronization operations."""
        project_id = 1
        
        mock_project = MagicMock()
        mock_project.id = project_id
        mock_project.project_code = 'PROGRESS_001'
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        mock_db_session.execute.return_value = mock_result
        
        # Mock progress callback
        progress_reports = []
        
        def progress_callback(current, total, status):
            progress_reports.append({
                'current': current,
                'total': total,
                'status': status,
                'percentage': (current / total) * 100
            })
        
        with patch.object(sync_service.project_repo, 'get_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock batch processing with progress reporting
            with patch.object(sync_service, '_sync_with_progress') as mock_sync_progress:
                mock_sync_progress.return_value = {'batches_synced': 100}
                
                # Simulate progress callback calls
                for i in range(0, 101, 10):
                    progress_callback(i, 100, f'Processing batch {i}')
                
                result = await sync_service.sync_batch_allocations(
                    project_id, mock_db_session, progress_callback=progress_callback
                )
                
                assert len(progress_reports) == 11  # 0, 10, 20, ..., 100
                assert progress_reports[-1]['percentage'] == 100.0  # Final report is 100%
