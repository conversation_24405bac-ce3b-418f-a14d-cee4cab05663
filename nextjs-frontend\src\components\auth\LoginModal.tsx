"use client";

import { useState, useEffect, useRef } from "react";
import { FaEye, FaEyeSlash, FaArrowLeft } from "react-icons/fa";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { API_BASE_URL } from "../../lib/api";

type LoginModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onRegisterClick: () => void;
};


const LoginModal = ({ isOpen, onClose, onRegisterClick }: LoginModalProps) => {
  const router = useRouter();
  const { login } = useAuth();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const modalRef = useRef<HTMLDivElement>(null);
  const initialFocusRef = useRef<HTMLInputElement>(null);

  // Handle modal close on escape and outside click
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.addEventListener("mousedown", handleClickOutside);
      // Set focus to username input
      setTimeout(() => initialFocusRef.current?.focus(), 100);
      // Prevent scrolling of background content
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "";
    };
  }, [isOpen, onClose]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      setFormError("Please enter both username and password");
      return;
    }

    setIsLoading(true);
    setFormError(null);

    try {
      // Actual login API call to FastAPI backend
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: new URLSearchParams({ username, password }).toString(),
        credentials: "include", // Important: needed for cookies
      });

      if (!response.ok) {
        const errorData = await response.json();
        setFormError(errorData.detail || "Invalid username or password");
        return;
      }

      const data = await response.json();

      // Redirect based on user role (tokens are now in cookies)
      const userRole = data.user?.role;

      console.log("Login successful:", { username });

      // Reset form
      setUsername("");
      setPassword("");

      // Update authentication state
      await login();

      // Close modal
      onClose();

      // Redirect after auth state is updated
      if (userRole) {
        router.push(`/${userRole}`);
      }
    } catch {
      setFormError("Invalid username or password");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div
        ref={modalRef}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden"
      >
        {/* Back button */}
        <button
          onClick={onClose}
          className="absolute top-4 left-4 text-gray-600 hover:text-gray-900 transition-colors z-10"
          aria-label="Go back"
        >
          <FaArrowLeft />
        </button>

        {/* Colored indicator line */}
        <div className="h-1 w-full bg-gradient-to-r from-blue-500 to-purple-600"></div>

        <div className="p-8">
          {/* Header with logo and title */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="mr-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-800 font-bold">
                  DADP
                </div>
              </div>
              <div className="text-left">
                <h2 className="text-2xl font-bold text-gray-800 mb-0">DADP</h2>
                <p className="text-gray-500 italic">
                  &quot;Place to Teach AI&quot;
                </p>
              </div>
            </div>

            <h3 className="text-xl font-semibold">Sign In</h3>
            <div className="h-1 w-16 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mt-2"></div>
          </div>

          {/* Login form */}
          <form onSubmit={handleSubmit}>
            {/* Username */}
            <div className="mb-4">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Username
              </label>
              <input
                type="text"
                id="username"
                ref={initialFocusRef}
                autoComplete="username"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                placeholder="Username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
              />
            </div>

            {/* Password */}
            <div className="mb-6">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  autoComplete="current-password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            {/* Error message */}
            {formError && (
              <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-lg text-sm text-center">
                {formError}
              </div>
            )}

            {/* Submit button */}
            <button
              type="submit"
              className="w-full py-2 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all flex justify-center items-center"
              disabled={isLoading}
            >
              {isLoading ? (
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              ) : null}
              <span>{isLoading ? "Logging in\u2026" : "Login"}</span>
            </button>

            {/* Divider */}
            <div className="flex items-center my-6">
              <div className="flex-grow h-px bg-gray-300"></div>
              <span className="px-4 text-sm text-gray-500">OR</span>
              <div className="flex-grow h-px bg-gray-300"></div>
            </div>

            {/* Register link */}
            <div className="text-center">
              <p className="mb-4 text-sm text-gray-600">
                Don&apos;t have an account?
              </p>
              <button
                type="button"
                onClick={onRegisterClick}
                className="w-full py-2 px-4 border border-gray-300 rounded-lg font-medium hover:bg-gray-50 transition-all"
              >
                Create New Account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;
