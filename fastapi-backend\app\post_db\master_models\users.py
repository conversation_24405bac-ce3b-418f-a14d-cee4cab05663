from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateTime, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..base import Base


class AnnotationMode(str, PyEnum):
    """Annotation mode enumeration."""
    AI_ONLY = "ai_only"
    AI_HUMAN_HYBRID = "ai_human_hybrid"
    HUMAN_ONLY = "human_only"


class AnnotatorMode(PyEnum):
        ANNOTATION = "annotation"
        VERIFICATION = "verification"
        SUPERVISION = "supervision"
class UserRole(str, PyEnum):
    """User role enumeration."""
    ADMIN = "admin"
    ANNOTATOR = "annotator"
    AUDITOR = "auditor"
    VERIFIER = "verifier"
    CLIENT = "client"
    MANAGER = "manager"
class users(Base):
    """
    Centralized user registry for all platform users.
    Manages authentication, authorization, skills, and cross-project performance tracking 
    for annotators, reviewers, and administrators.
    """
    __tablename__ = 'users'

    # Primary Identity
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(255), nullable=False, unique=True, 
                     comment='Unique username for login and user references across system')
    email = Column(String(255), nullable=False, unique=True, 
                  comment='Primary email for authentication and notifications')
    full_name = Column(String(255), 
                      comment='Display name for UI and reporting purposes')
    role = Column(String(50), default=UserRole.ANNOTATOR.value, nullable=False, 
                      comment='Global role (admin, annotator, reviewer, client) determining base permissions')
    
    # Authentication & Access Control
    password_hash = Column(String(255), 
                          comment='Hashed password for secure authentication')
    is_active = Column(Boolean, default=True, 
                      comment='Account status for enabling/disabling user access globally')
    last_login = Column(DateTime, 
                       comment='Last login timestamp for security monitoring and session management')
    # annotator_mode = Column(String(50), default=AnnotatorMode.ANNOTATION.value, nullable=False)
    
    # Skills & Qualification Management
    annotation_skills = Column(JSONB, 
                             comment='User\'s annotation capabilities by media type (e.g., {"image": "expert", "pdf": "intermediate"})')
    max_concurrent_projects = Column(Integer, default=3, 
                                   comment='Maximum number of projects user can work on simultaneously (workload management)')
    max_concurrent_batches = Column(Integer, default=5, 
                                  comment='Maximum number of annotation batches user can handle at once')
    # annotation_mode = Column(CaseInsensitiveEnum(AnnotationMode), default=AnnotationMode.HUMAN_ONLY, nullable=False,
    #                        comment='Default annotation mode for this user (human_only, ai_human_hybrid, ai_only)')
    
    # Cross-Project Performance Tracking
    overall_quality_score = Column(Numeric(5, 2), 
                                 comment='Aggregated quality score across all projects (0-100) for assignment optimization')
    total_completed_assignments = Column(Integer, default=0, 
                                       comment='Total number of completed annotation assignments across all projects')
    
    # Audit Trail
    created_at = Column(DateTime, default=func.current_timestamp(), 
                       comment='Account creation timestamp for user lifecycle tracking')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), 
                       comment='Last profile update timestamp for change monitoring')
    
    # Project Assignment
    active_project = Column(String(50), ForeignKey('projects_registry.project_code'), nullable=True, 
                           comment='Currently active project code (null if not assigned to any project)')
    

    def __repr__(self):
        return f"<Users(id={self.id}, username='{self.username}', email='{self.email}', role='{self.role}')>"

User = users 