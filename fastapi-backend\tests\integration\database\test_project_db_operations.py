"""
Integration tests for Project Database operations.
Tests batch management, file allocation, and user assignment workflows.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE (@utils/dynamic_schema_generator.py):
- NO foreign key constraint from user_allocations.user_id to project_users.user_id
- NO individual unique constraint on project_users.user_id (only composite with username)  
- user_id values are managed by business logic, not database constraints
- Tables created via DynamicSchemaGenerator, not SQLAlchemy metadata
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from httpx import AsyncClient

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.file_allocations import FileAllocations, WorkflowPhase
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.allocation_models.project_users import ProjectUsers
from app.repositories.batch_assignment_repository import BatchAssignmentRepository
from app.repositories.project_db_repository import ProjectDBRepository
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.core_features    # Feature marker - Core database
@pytest.mark.smoke            # Suite marker - Core functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestProjectDatabaseOperations:
    """SMOKE TEST SUITE: Critical project database CRUD operations."""
    
    @pytest.mark.asyncio
    async def test_allocation_batch_creation(self, test_db: AsyncSession, sample_batch_data, setup_test_database):
        """Test allocation batch creation and retrieval."""
        # Create allocation batch
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier=sample_batch_data["batch_identifier"],
            total_files=sample_batch_data["total_files"],
            file_list=sample_batch_data["file_list"],
            annotation_count=sample_batch_data["annotation_count"],
            is_priority=sample_batch_data["is_priority"],
            skill_requirements=sample_batch_data["skill_requirements"],
            allocation_criteria=sample_batch_data["allocation_criteria"]
        )
        
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Verify batch creation
        assert batch.id is not None
        assert batch.batch_identifier == sample_batch_data["batch_identifier"]
        assert batch.batch_status == BatchStatus.CREATED
        assert batch.total_files == sample_batch_data["total_files"]
        assert batch.assignment_count == 0  # Should start at 0
        
        # Test batch retrieval
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier == sample_batch_data["batch_identifier"]
        )
        result = await test_db.execute(stmt)
        retrieved_batch = result.scalar_one_or_none()
        
        assert retrieved_batch is not None
        assert retrieved_batch.annotation_count == sample_batch_data["annotation_count"]
    
    @pytest.mark.asyncio
    async def test_dynamic_annotator_columns(self, test_db: AsyncSession, setup_test_database):
        """Test dynamic annotator column functionality."""
        # Create batch first
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="DYNAMIC_TEST",
            total_files=5,
            annotation_count=3
        )
        
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Test adding dynamic columns using raw SQL
        # This simulates the dynamic schema generation
        annotator_columns = []
        for i in range(1, 4):  # annotation_count = 3
            column_name = f"annotator_{i}"
            try:
                await test_db.execute(text(f"""
                    ALTER TABLE allocation_batches 
                    ADD COLUMN {column_name} INTEGER
                """))
                annotator_columns.append(column_name)
            except Exception:
                # Column might already exist in test
                pass
        
        await test_db.commit()
        
        # Test assigning users to dynamic columns (production architecture)
        # Note: annotator_N columns store user_id values without FK constraints
        user_id = 123  # Arbitrary value - no FK validation in production
        slot = 1
        annotator_field = f"annotator_{slot}"
        
        await test_db.execute(text(f"""
            UPDATE allocation_batches 
            SET {annotator_field} = :user_id,
                assignment_count = assignment_count + 1
            WHERE id = :batch_id
        """), {"user_id": user_id, "batch_id": batch.id})
        
        await test_db.commit()
        
        # Verify assignment
        result = await test_db.execute(text("""
            SELECT annotator_1, assignment_count 
            FROM allocation_batches 
            WHERE id = :batch_id
        """), {"batch_id": batch.id})
        
        row = result.fetchone()
        if row:  # Only check if columns exist
            assert row[0] == user_id  # annotator_1
            assert row[1] == 1  # assignment_count
    
    @pytest.mark.asyncio
    async def test_files_registry_operations(self, test_db: AsyncSession, sample_file_data, setup_test_database):
        """Test files registry CRUD operations."""
        # Create batch first
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="FILES_TEST",
            total_files=1
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Create file
        file = test_factory.files.create_files_registry(
            batch.id,
            file_identifier=sample_file_data["file_identifier"],
            original_filename=sample_file_data["original_filename"],
            file_type=FileType.IMAGE,
            file_size_bytes=sample_file_data["file_size_bytes"],
            storage_location=sample_file_data["storage_location"], 
        )
        
        test_db.add(file)
        await test_db.commit()
        await test_db.refresh(file)
        
        # Verify file creation
        assert file.id is not None
        assert file.batch_id == batch.id
        assert file.file_identifier == sample_file_data["file_identifier"]
        assert file.file_type == FileType.IMAGE
        assert file.storage_location == sample_file_data["storage_location"]
        
        # Test file retrieval by batch
        stmt = select(FilesRegistry).where(FilesRegistry.batch_id == batch.id)
        result = await test_db.execute(stmt)
        files = result.scalars().all()
        
        assert len(files) == 1
        assert files[0].file_identifier == sample_file_data["file_identifier"]
    
    @pytest.mark.asyncio
    async def test_file_allocation_workflow(self, test_db: AsyncSession, setup_test_database):
        """Test file allocation workflow."""
        # Create batch and file
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="ALLOCATION_TEST", 
            total_files=1
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        file = test_factory.files.create_files_registry(
            batch.id,
            file_identifier="allocation_test_file.jpg",
            file_type=FileType.IMAGE
        )
        test_db.add(file)
        await test_db.commit()
        await test_db.refresh(file)
        
        # Create file allocation
        allocation = FileAllocations(
            file_id=file.id,
            batch_id=batch.id,
            allocation_sequence=1,
            workflow_phase=WorkflowPhase.ANNOTATION,
            processing_status="pending",
            assignment_count=0,
            completion_count=0
        )
        
        test_db.add(allocation)
        await test_db.commit()
        await test_db.refresh(allocation)
        
        # Verify allocation creation
        assert allocation.id is not None
        assert allocation.file_id == file.id
        assert allocation.batch_id == batch.id
        assert allocation.workflow_phase == WorkflowPhase.ANNOTATION
        assert allocation.processing_status == "pending"
        
        # Test allocation update (simulate assignment)
        allocation.assignment_count = 1
        allocation.processing_status = "processing"  #  FIXED: Use valid ProcessingStatus enum value
        await test_db.commit()
        
        # Verify update
        stmt = select(FileAllocations).where(FileAllocations.id == allocation.id)
        result = await test_db.execute(stmt)
        updated_allocation = result.scalar_one_or_none()
        
        assert updated_allocation.assignment_count == 1
        assert updated_allocation.processing_status == "processing"  #  FIXED: Use valid ProcessingStatus enum value
    
    @pytest.mark.asyncio
    async def test_user_allocation_workflow(self, test_db: AsyncSession, setup_test_database):
        """Test user allocation to batch workflow."""
        # Create batch
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="USER_ALLOCATION_TEST",
            total_files=5,
            annotation_count=2
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Create project user
        # Note: In production, user_id comes from master DB and is managed by business logic
        # Tests use arbitrary values since there's no FK constraint (production architecture)
        import random
        user_id = random.randint(100000, 999999)
        username = "test_annotator"
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user_id,
            username=username,
            current_batch=None
        )
        test_db.add(project_user)
        await test_db.commit()
        await test_db.refresh(project_user)
        
        # Create user allocation
        # Note: user_id references project_users.user_id but NO FK constraint (production style)
        user_allocation = UserAllocations(
            user_id=user_id,  # References project_users.user_id - managed by business logic
            batch_id=batch.id,
            username=username,
            total_files=5,
            allocation_role=AllocationRole.ANNOTATOR,
            is_active=True
        )
        
        test_db.add(user_allocation)
        await test_db.commit()
        await test_db.refresh(user_allocation)
        
        # Update project user current batch
        project_user.current_batch = batch.id
        await test_db.commit()
        
        # Verify allocation
        assert user_allocation.id is not None
        assert user_allocation.user_id == user_id
        assert user_allocation.batch_id == batch.id
        assert user_allocation.allocation_role == AllocationRole.ANNOTATOR
        assert user_allocation.is_active is True
        
        # Verify project user update
        stmt = select(ProjectUsers).where(ProjectUsers.user_id == user_id)
        result = await test_db.execute(stmt)
        updated_user = result.scalar_one_or_none()
        
        assert updated_user.current_batch == batch.id


# REMOVED: TestRepositoryOperations class 
# → Duplicate functions moved to test_repository_layer_operations.py
# → test_batch_assignment_repository() and test_project_db_repository() now have comprehensive coverage

@pytest.mark.integration
@pytest.mark.database
@pytest.mark.core_features    # Feature marker - Core database
@pytest.mark.regression       # Suite marker - Complex workflows
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestComplexWorkflows:
    """REGRESSION TEST SUITE: Complex database workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_annotation_workflow(self, test_db: AsyncSession, setup_test_database):
        """Test complete annotation workflow from batch creation to completion."""
        # 1. Create batch
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="WORKFLOW_TEST",
            total_files=2,
            annotation_count=1,
            assignment_count=0
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # 2. Add files to batch
        files = [
            test_factory.files.create_files_registry(
                batch.id,
                file_identifier=f"workflow_file_{i}.jpg",
                file_type=FileType.IMAGE
            ) for i in range(1, 3)
        ]
        
        for file in files:
            test_db.add(file)
        await test_db.commit()
        
        for file in files:
            await test_db.refresh(file)
        
        # 3. Create file allocations
        allocations = [
            FileAllocations(
                file_id=file.id,
                batch_id=batch.id,
                allocation_sequence=1,
                workflow_phase=WorkflowPhase.ANNOTATION,
                processing_status="pending",
                assignment_count=0,
                completion_count=0
            ) for file in files
        ]
        
        for allocation in allocations:
            test_db.add(allocation)
        await test_db.commit()
        
        # 4. Create project user and assign to batch
        # Note: Production architecture - user_id from master DB, no FK constraints
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=789,  # Master DB reference - managed by business logic
            username="workflow_annotator"
        )
        test_db.add(project_user)
        await test_db.commit()
        await test_db.refresh(project_user)
        
        # 5. Create user allocation
        # Note: user_id references project_users.user_id but NO FK constraint (production style)
        user_allocation = UserAllocations(
            user_id=789,  # References project_users.user_id - no FK validation
            batch_id=batch.id,
            username="workflow_annotator",
            total_files=2,
            files_completed=0,
            allocation_role=AllocationRole.ANNOTATOR,
            is_active=True
        )
        test_db.add(user_allocation)
        await test_db.commit()
        
        # 6. Update batch assignment count
        batch.assignment_count = 1
        batch.batch_status = BatchStatus.ALLOCATED
        await test_db.commit()
        
        # 7. Simulate work completion
        for allocation in allocations:
            allocation.assignment_count = 1
            allocation.completion_count = 1
            allocation.processing_status = "processing"  #  FIXED: Use valid ProcessingStatus enum value
        
        user_allocation.files_completed = 2
        await test_db.commit()
        
        # 8. Verify final state
        stmt = select(AllocationBatches).where(AllocationBatches.id == batch.id)
        result = await test_db.execute(stmt)
        final_batch = result.scalar_one_or_none()
        
        assert final_batch.assignment_count == 1
        assert final_batch.batch_status == BatchStatus.ALLOCATED
        
        stmt = select(UserAllocations).where(UserAllocations.batch_id == batch.id)
        result = await test_db.execute(stmt)
        final_user_allocation = result.scalar_one_or_none()
        
        assert final_user_allocation.files_completed == 2
        
        stmt = select(FileAllocations).where(FileAllocations.batch_id == batch.id)
        result = await test_db.execute(stmt)
        final_allocations = result.scalars().all()
        
        assert all(alloc.processing_status == "processing" for alloc in final_allocations)  #  FIXED: Use valid ProcessingStatus enum value
        assert all(alloc.completion_count == 1 for alloc in final_allocations)


# ===============================
# FIXTURES
# ===============================

@pytest.fixture
def sample_batch_data():
    """Sample batch data fixture for testing."""
    import time
    import uuid
    import random
    # Create truly unique ID with timestamp, UUID, and random number
    timestamp = int(time.time() * 1000000)  # microsecond precision
    unique_suffix = f"{timestamp}_{uuid.uuid4().hex[:8]}_{random.randint(1000, 9999)}"
    return {
        "batch_identifier": f"TEST_BATCH_{unique_suffix}",
        "total_files": 10,
        "file_list": ["file1.jpg", "file2.jpg", "file3.jpg"],  # Simple list for SQLite compatibility
        "annotation_count": 2,
        "is_priority": False,
        "skill_requirements": {"image_annotation": "basic"},  # Simple dict for SQLite compatibility
        "allocation_criteria": {"min_accuracy": 0.8}  # Simple dict for SQLite compatibility
    }

@pytest.fixture
def sample_file_data():
    """Sample file data fixture for testing."""
    import time
    import uuid
    import random
    # Create truly unique ID with timestamp, UUID, and random number
    timestamp = int(time.time() * 1000000)  # microsecond precision
    unique_suffix = f"{timestamp}_{uuid.uuid4().hex[:8]}_{random.randint(1000, 9999)}"
    return {
        "file_identifier": f"test_file_{unique_suffix}.jpg",
        "original_filename": f"original_file_{unique_suffix}.jpg",
        "file_extension": ".jpg",
        "storage_location": {"type": "local", "path": test_factory.files.create_test_folder_path("test") + f"/path/{unique_suffix}"},
        "file_size_bytes": 1024,
        "file_hash": f"hash_{unique_suffix}",
        "sequence_order": 1
    }
