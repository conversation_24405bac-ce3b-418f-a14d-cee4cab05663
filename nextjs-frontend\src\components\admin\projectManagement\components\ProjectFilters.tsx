import React from 'react';
import { FaSearch } from 'react-icons/fa';
import { ProjectFilters as ProjectFiltersType } from '../types';

interface ProjectFiltersProps {
  filters: ProjectFiltersType;
  availableTypes: string[];
  availableStatuses: string[];
  onFilterChange: (filters: Partial<ProjectFiltersType>) => void;
  onClearFilters: () => void;
}

export const ProjectFilters: React.FC<ProjectFiltersProps> = ({
  filters,
  availableTypes,
  availableStatuses,
  onFilterChange,
  onClearFilters,
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <FaSearch className="absolute left-3 top-3 text-gray-400" />
          <input
            type="text"
            placeholder="Search projects..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0052CC] focus:border-transparent"
            value={filters.search || ''}
            onChange={(e) => onFilterChange({ search: e.target.value })}
          />
        </div>

        {/* Project Type Filter */}
        <div>
          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0052CC] focus:border-transparent"
            value={filters.project_type || ''}
            onChange={(e) => onFilterChange({ project_type: e.target.value || undefined })}
          >
            <option value="">All Types</option>
            {availableTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        {/* Status Filter */}
        <div>
          <select
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#0052CC] focus:border-transparent"
            value={filters.project_status || ''}
            onChange={(e) => onFilterChange({ project_status: e.target.value || undefined })}
          >
            <option value="">All Statuses</option>
            {availableStatuses.map(status => (
              <option key={status} value={status}>{status}</option>
            ))}
          </select>
        </div>

        {/* Clear Filters */}
        <div className="flex items-end">
          <button
            onClick={onClearFilters}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  );
};
