// Shared types and constants for Telegram components
export const BASE_URL = 'http://localhost:8002';



// Channel interfaces
export interface TelegramChannel {
  id: number;
  title: string;
  username?: string | null;
  participants_count?: number;
}

// Analytics interfaces
export interface AnalyticsData {
  total_images: number;
  dates?: string[];
  first_date?: string;
  last_date?: string;
  error?: string;
}

export interface TelegramAnalyticsData {
  channel?: {
    id: number;
    title: string;
    username?: string;
    error?: string;
  };
  total_images?: number;
  dates?: string[];
  date_image_counts?: Record<string, number>;
  monthly_counts?: Record<string, number>;
  weekday_counts?: Record<string, number>;
  hour_of_day_counts?: Record<string, number>;
  images_with_captions?: number;
  first_date?: string;
  last_date?: string;
  error?: string;
}

// Image interfaces
export interface TelegramImage {
  id: number;
  image: string;
  date: string;
  caption: string;
}

export interface CropSelection {
  x: number;
  y: number;
  width: number;
  height: number;
  preview: string;
}

// Component props interfaces
export interface TelegramChannelsProps {
  selectedChannelId: number | null;
  onSelectChannel: (id: number) => void;
  onShowImages: (id: number) => void;
  onDisconnect?: () => void;
}

export interface TelegramImagesProps {
  channelId: number;
}

export interface TelegramAnalyticsProps {
  initialChannelId?: number | '';
}

export interface TelegramConnectProps { 
  onConnected?: () => void;
}

// Shared utility functions
export const showStatus = (
  setStatusMessage: (msg: {message: string; type: 'success'|'danger'|'warning'|'info'} | null) => void,
  msg: string, 
  type: 'success'|'danger'|'warning'|'info' = 'success', 
  duration = 3000
) => {
  setStatusMessage({ message: msg, type });
  setTimeout(() => setStatusMessage(null), duration);
};

// Common fetch functions
export const fetchTelegramChannels = async (refresh: boolean = false): Promise<TelegramChannel[]> => {
  try {
    const refreshParam = refresh ? '?refresh=true' : '';
    const res = await fetch(`${BASE_URL}/telegram/channels${refreshParam}`, {
      credentials: 'include'
    });
    const data = await res.json();
    return data.channels || [];
  } catch (err) {
    console.error('Error fetching channels:', err);
    return [];
  }
};

export const checkTelegramAuth = async (): Promise<{authenticated: boolean; username?: string}> => {
  try {
    const res = await fetch(`${BASE_URL}/telegram/check-auth?refresh=1`, {
      credentials: 'include'
    });
    return await res.json();
  } catch (err) {
    console.error('Error checking auth:', err);
    return { authenticated: false };
  }
}; 