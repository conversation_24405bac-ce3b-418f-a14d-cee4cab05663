"use client";

import { useState, useEffect } from "react";
import { MediaViewerProps } from "./types";
import { 
  FaCheck, 
  FaFilePdf, 
  FaDownload, 
  FaExternalLinkAlt,
  FaSpinner
} from "react-icons/fa";

interface PDFViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  isLabeled?: boolean;
}

export default function PDFViewer({
  mediaUrl,
  zoomLevel = 100,
  onLoad,
  onError,
  isLabeled = false
}: PDFViewerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [pdfError, setPdfError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate loading check
    const checkPdfLoad = async () => {
      try {
        console.log('PDFViewer: Checking PDF load for URL:', mediaUrl);
        const response = await fetch(mediaUrl, { 
          method: 'HEAD',
          credentials: 'include',
          headers: {
            'Accept': 'application/pdf',
          }
        });
        console.log('PDFViewer: HEAD response status:', response.status);
        console.log('PDFViewer: HEAD response headers:', Object.fromEntries(response.headers.entries()));
        
        if (response.ok) {
          console.log('PDFViewer: PDF load check successful');
          setIsLoading(false);
          onLoad?.();
        } else {
          console.error('PDFViewer: PDF not accessible, status:', response.status);
          const errorText = await response.text().catch(() => 'Unknown error');
          console.error('PDFViewer: Error response:', errorText);
          throw new Error(`PDF not accessible (status: ${response.status}): ${errorText}`);
        }
      } catch (error) {
        console.error('PDFViewer: Error checking PDF load:', error);
        setIsLoading(false);
        setPdfError(`Failed to load PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
        onError?.('Failed to load PDF');
      }
    };

    if (mediaUrl) {
      checkPdfLoad();
    }
  }, [mediaUrl, onLoad, onError]);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = mediaUrl;
    link.download = 'document.pdf';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openInNewTab = () => {
    window.open(mediaUrl, '_blank');
  };

  if (isLoading) {
    return (
      <div className="h-full flex justify-center items-center bg-gray-50 rounded-lg">
        <div className="flex flex-col items-center gap-2.5 text-gray-500">
          <FaSpinner className="animate-spin" />
          <div className="text-sm">Loading PDF...</div>
        </div>
      </div>
    );
  }

  if (pdfError) {
    return (
      <div className="h-full flex justify-center items-center bg-gray-50 rounded-lg">
        <div className="flex flex-col items-center gap-4 text-red-600">
          <FaFilePdf size={48} className="opacity-60" />
          <div className="text-base font-medium">Failed to load PDF</div>
          <button 
            className="bg-blue-500 text-white border-none px-4 py-2 rounded cursor-pointer text-sm hover:bg-blue-600 transition-colors"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col relative bg-gray-50 rounded-lg overflow-hidden">
      {/* PDF Embed */}
      <div className="flex-1 overflow-hidden relative">
        <iframe
          src={`${mediaUrl}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
          style={{
            transform: `scale(${zoomLevel / 100})`,
            transformOrigin: 'top left',
            width: `${100 / (zoomLevel / 100)}%`,
            height: `${100 / (zoomLevel / 100)}%`,
          }}
          className="border-none bg-white"
          title="PDF Viewer"
          onLoad={() => console.log('PDFViewer: iframe loaded successfully')}
          onError={(e) => {
            console.error('PDFViewer: iframe load error:', e);
            setPdfError('Failed to load PDF in viewer');
          }}
        />
      </div>
      
      {/* PDF Controls */}
      <div className="bg-black/80 text-white px-4 py-2.5 flex justify-between items-center">
        <div className="flex items-center gap-2 text-sm">
          <FaFilePdf className="text-red-500" />
          <span>PDF Document</span>
        </div>
        
        <div className="flex gap-2.5">
          <button 
            className="bg-white/20 border-none text-white cursor-pointer px-3 py-1.5 rounded transition-colors duration-200 flex items-center gap-1 text-xs hover:bg-white/30" 
            onClick={openInNewTab} 
            title="Open in new tab"
          >
            <FaExternalLinkAlt />
            <span>Open</span>
          </button>
          
          <button 
            className="bg-white/20 border-none text-white cursor-pointer px-3 py-1.5 rounded transition-colors duration-200 flex items-center gap-1 text-xs hover:bg-white/30" 
            onClick={handleDownload} 
            title="Download PDF"
          >
            <FaDownload />
            <span>Download</span>
          </button>
        </div>
      </div>
      
      {isLabeled && (
        <span className="absolute top-2.5 left-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center z-10">
          <FaCheck className="me-1" /> Labeled
        </span>
      )}

    </div>
  );
}
