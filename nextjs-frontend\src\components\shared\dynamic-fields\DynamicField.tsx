import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './ShortAnswerField';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from './LongAnswerField';
import MultipleC<PERSON>iceField from './MultipleChoiceField';
import CheckboxField from './CheckboxField';

export interface FormFieldConfig {
  field_name: string;
  field_type: 'short_answer' | 'long_answer' | 'multiple_choice' | 'checkboxes';
  label: string;
  required: boolean;
  options?: string[];
  placeholder?: string;
  max_length?: number;
  description?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: string;
  };
}

interface DynamicFieldProps {
  config: FormFieldConfig;
  value: any;
  onChange: (fieldName: string, value: any) => void;
  error?: string;
}

export default function DynamicField({ 
  config, 
  value, 
  onChange, 
  error 
}: DynamicFieldProps) {
  switch (config.field_type) {
    case 'short_answer':
      return (
        <ShortAnswerField
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );
      
    case 'long_answer':
      return (
        <LongAnswerField
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );
      
    case 'multiple_choice':
      return (
        <MultipleChoiceField
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );
      
    case 'checkboxes':
      return (
        <CheckboxField
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );
      
    default:
      return (
        <div className="mb-4">
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              Unknown field type: {config.field_type}
            </p>
          </div>
        </div>
      );
  }
}
