"use client";

import { useState, useEffect } from "react";
import { MediaViewerProps } from "./types";
import { 
  FaCheck, 
  FaFileAlt, 
  FaDownload, 
  FaCopy,
  FaSpinner,
  FaSearch,
  FaTable
} from "react-icons/fa";

interface TextViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  isLabeled?: boolean;
}

export default function TextViewer({
  mediaUrl,
  zoomLevel = 100,
  onLoad,
  onError,
  isLabeled = false
}: TextViewerProps) {
  const [textContent, setTextContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [textError, setTextError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [lineNumbers, setLineNumbers] = useState(true);
  const [csvData, setCsvData] = useState<string[][]>([]);

  useEffect(() => {
    const loadTextContent = async () => {
      try {
        setIsLoading(true);
        console.log('TextViewer: Loading text from URL:', mediaUrl);
        
        const response = await fetch(mediaUrl, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'text/plain, text/*, application/json, application/xml, text/markdown, text/csv, text/yaml',
          }
        });
        
        console.log('TextViewer: Response status:', response.status);
        console.log('TextViewer: Response headers:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
          const errorText = await response.text().catch(() => 'Unknown error');
          console.error('TextViewer: Error response:', errorText);
          throw new Error(`Failed to fetch text file (status: ${response.status}): ${errorText}`);
        }
        
        const content = await response.text();
        console.log('TextViewer: Successfully loaded text content, length:', content.length);
        setTextContent(content);
        
        // Check if this is a CSV file and parse it
        const isCSV = mediaUrl.toLowerCase().includes('.csv') || 
                     mediaUrl.toLowerCase().includes('csv') ||
                     response.headers.get('content-type')?.includes('csv');
        
        if (isCSV) {
          try {
            const parsed = parseCSV(content);
            setCsvData(parsed);
            console.log('TextViewer: Parsed CSV data, rows:', parsed.length);
          } catch (csvError) {
            console.warn('TextViewer: Failed to parse as CSV, showing as text:', csvError);
          }
        }
        
        onLoad?.();
      } catch (error) {
        console.error('TextViewer: Error loading text content:', error);
        setTextError(`Failed to load text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        onError?.('Failed to load text file');
      } finally {
        setIsLoading(false);
      }
    };

    if (mediaUrl) {
      loadTextContent();
    }
  }, [mediaUrl, onLoad, onError]);

  // Simple CSV parser - handles quotes and commas
  const parseCSV = (text: string): string[][] => {
    const lines = text.split('\n').filter(line => line.trim());
    const result: string[][] = [];
    
    for (const line of lines) {
      const row: string[] = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          row.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      row.push(current.trim());
      result.push(row);
    }
    
    return result;
  };

  const handleDownload = () => {
    const blob = new Blob([textContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'document.txt';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textContent);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const highlightSearchTerm = (text: string, term: string) => {
    if (!term.trim()) return text;
    
    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  };

  const getDisplayContent = () => {
    // Show CSV table if we have CSV data, otherwise show text
    if (csvData.length > 0) {
      return renderCSVTable();
    }
    
    const lines = textContent.split('\n');
    
    return lines.map((line, index) => {
      const lineNumber = index + 1;
      const highlightedLine = highlightSearchTerm(line, searchTerm);
      
      return (
        <div key={index} className="flex min-h-[1.5em]">
          {lineNumbers && (
            <span className="text-gray-500 mr-4 select-none min-w-[40px] text-right text-xs border-r border-gray-200 pr-2.5">{lineNumber}</span>
          )}
          <span 
            className="flex-1"
            dangerouslySetInnerHTML={{ __html: highlightedLine }}
          />
        </div>
      );
    });
  };

  const renderCSVTable = () => {
    if (csvData.length === 0) return null;
    
    const headers = csvData[0];
    const rows = csvData.slice(1);
    
    // Filter rows based on search term
    const filteredRows = searchTerm.trim() 
      ? rows.filter(row => 
          row.some(cell => 
            cell.toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : rows;
    
    return (
      <div className="overflow-auto">
        <table className="w-full border-collapse border border-gray-300 text-sm">
          <thead className="bg-gray-50">
            <tr>
              {headers.map((header, index) => (
                <th 
                  key={index} 
                  className="border border-gray-300 px-3 py-2 text-left font-semibold text-gray-700 sticky top-0 bg-gray-50"
                >
                  {highlightSearchTerm(header, searchTerm) ? (
                    <span dangerouslySetInnerHTML={{ __html: highlightSearchTerm(header, searchTerm) }} />
                  ) : (
                    header
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredRows.map((row, rowIndex) => (
              <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}>
                {row.map((cell, cellIndex) => (
                  <td 
                    key={cellIndex} 
                    className="border border-gray-300 px-3 py-2 max-w-[200px] overflow-hidden text-ellipsis"
                    title={cell}
                  >
                    {highlightSearchTerm(cell, searchTerm) ? (
                      <span dangerouslySetInnerHTML={{ __html: highlightSearchTerm(cell, searchTerm) }} />
                    ) : (
                      cell
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        
        {searchTerm.trim() && (
          <div className="mt-2 text-sm text-gray-600 text-center">
            Showing {filteredRows.length} of {rows.length} rows
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="h-full flex justify-center items-center bg-gray-50 rounded-lg">
        <div className="flex flex-col items-center gap-2.5 text-gray-500">
          <FaSpinner className="animate-spin" />
          <div className="text-sm">Loading text file...</div>
        </div>
      </div>
    );
  }

  if (textError) {
    return (
      <div className="h-full flex justify-center items-center bg-gray-50 rounded-lg">
        <div className="flex flex-col items-center gap-4 text-red-600">
          <FaFileAlt size={48} className="opacity-60" />
          <div className="text-base font-medium">Failed to load text file</div>
          <button 
            className="bg-blue-500 text-white border-none px-4 py-2 rounded cursor-pointer text-sm hover:bg-blue-600 transition-colors"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col relative bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Text Controls */}
      <div className="bg-gray-50 border-b border-gray-200 px-4 py-2.5 flex justify-between items-center gap-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          {csvData.length > 0 ? (
            <>
              <FaTable className="text-green-500" />
              <span>CSV Document ({csvData.length - 1} rows, {csvData[0]?.length || 0} columns)</span>
            </>
          ) : (
            <>
              <FaFileAlt className="text-blue-500" />
              <span>Text Document ({textContent.split('\n').length} lines)</span>
            </>
          )}
        </div>
        
        <div className="flex-1 max-w-[300px] relative">
          <FaSearch className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs" />
          <input
            type="text"
            placeholder="Search in text..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full py-1.5 px-3 pl-7 border border-gray-300 rounded text-xs outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500/25"
          />
        </div>
        
        <div className="flex gap-2">
          {csvData.length === 0 && (
            <button 
              className={`px-2.5 py-1.5 border rounded text-xs min-w-8 h-8 flex items-center justify-center transition-all duration-200 ${
                lineNumbers 
                  ? 'bg-blue-500 text-white border-blue-500' 
                  : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-100 hover:border-gray-400'
              }`}
              onClick={() => setLineNumbers(!lineNumbers)}
              title="Toggle line numbers"
            >
              #
            </button>
          )}
          
          <button 
            className="bg-white border border-gray-300 text-gray-600 cursor-pointer px-2.5 py-1.5 rounded transition-all duration-200 flex items-center justify-center text-xs min-w-8 h-8 hover:bg-gray-100 hover:border-gray-400" 
            onClick={handleCopy} 
            title="Copy text"
          >
            <FaCopy />
          </button>
          
          <button 
            className="bg-white border border-gray-300 text-gray-600 cursor-pointer px-2.5 py-1.5 rounded transition-all duration-200 flex items-center justify-center text-xs min-w-8 h-8 hover:bg-gray-100 hover:border-gray-400" 
            onClick={handleDownload} 
            title="Download text"
          >
            <FaDownload />
          </button>
        </div>
      </div>
      
      {/* Text Content */}
      <div 
        className={`flex-1 overflow-auto ${
          csvData.length > 0 ? 'p-2' : 'p-4 font-mono leading-relaxed whitespace-pre-wrap'
        } [&_mark]:bg-yellow-100 [&_mark]:px-0.5 [&_mark]:py-0.5 [&_mark]:rounded-sm`}
        style={{
          fontSize: csvData.length > 0 ? '14px' : `${zoomLevel}%`,
        }}
      >
        {getDisplayContent()}
      </div>
      
      {isLabeled && (
        <span className="absolute top-2.5 right-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center z-10">
          <FaCheck className="me-1" /> Labeled
        </span>
      )}

    </div>
  );
}
