"use client";

import React, { useState, useEffect } from "react";
import { authFetch } from "@/lib/authFetch";
import { API_BASE_URL } from "@/lib/api";
type ClientUser = { username: string; full_name?: string; role?: string };

type ConnectNASModalProps = {
  show: boolean;
  onClose: () => void;
  onSubmit: (data: {
    nasType: string;
    url: string;
    username: string;
    password: string;
    clientId?: string;
  }) => void;
  isLoading: boolean;
};

const ConnectNASModal: React.FC<ConnectNASModalProps> = ({
  show,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [nasType, setNasType] = useState("ftp");
  const [url, setUrl] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [clients, setClients] = useState<ClientUser[]>([]);
  const [clientId, setClientId] = useState("");



  // Fetch client users for dropdown
  useEffect(() => {
    async function fetchClients() {
      try {
        const res = await authFetch(`${API_BASE_URL}/admin/users`);
        const data: unknown = await res.json();
        if (Array.isArray(data)) {
          // Here, enforce type assertion for array items
          const clientUsers = data.filter(
            (u: ClientUser) => u.role === "client"
          );
          setClients(clientUsers);
        }
      } catch (e) {
        console.error("Failed to fetch clients:", e);
      }
    }
    fetchClients();
  }, [API_BASE_URL]);

  if (!show) return null;

  return (
    <>
      <div
        className="modal show block"
        tabIndex={-1}
        role="dialog"
      >
        <div className="modal-dialog" role="document">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Connect to NAS</h5>
              <button
                type="button"
                className="btn-close"
                aria-label="Close"
                onClick={onClose}
              ></button>
            </div>
            <div className="modal-body">
              {/* Client selection (optional) */}
              <div className="mb-3">
                <label className="form-label">Client (optional)</label>
                <select
                  className="form-select"
                  value={clientId}
                  onChange={(e) => setClientId(e.target.value)}
                >
                  <option value="">Own Data</option>
                  {clients.map((c) => (
                    <option key={c.username} value={c.username}>
                      {c.full_name || c.username}
                    </option>
                  ))}
                </select>
              </div>
              <div className="mb-3">
                <label className="form-label">NAS Type</label>
                <select
                  className="form-select"
                  value={nasType}
                  onChange={(e) => setNasType(e.target.value)}
                >
                  <option value="ftp">FTP</option>
                </select>
              </div>
              <div className="mb-3">
                <label className="form-label">NAS URL</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Example: ************:6001"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
                <div className="form-text">
                  IP address or hostname with port (if needed)
                </div>
              </div>
              <div className="mb-3">
                <label className="form-label">Username</label>
                <input
                  type="text"
                  className="form-control"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Password</label>
                <input
                  type="password"
                  className="form-control"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="alert alert-info">
                These credentials will be used for the current session only.
              </div>
            </div>
            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={isLoading}
              >
                Close
              </button>
              <button
                type="button"
                className="btn btn-success"
                disabled={isLoading}
                onClick={() =>
                  onSubmit({ nasType, url, username, password, clientId })
                }
              >
                {isLoading ? "Connecting..." : "Connect"}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
    </>
  );
};

export default ConnectNASModal;
