"use client";

import { useState, useEffect, useCallback } from "react";
import {
  FaArrowLeft,
  FaArrowRight,
  FaSave,
  FaUsers,
  FaEye,
  FaCheck,
  FaTimes,
  FaEdit,
  FaSpinner
} from "react-icons/fa";
import { useRouter } from "next/navigation";
import { showToast } from "@/lib/toast";
import { MediaViewer } from "../shared/media";
import { detectMediaType } from "../shared/media/types";
import { API_BASE_URL } from "@/lib/api";
import AnnotatorComparisonPanel from "./AnnotatorComparisonPanel";
// import VerificationProgress from "./VerificationProgress";

interface AnnotatorReview {
  annotator_id: number;
  annotator_number: number;
  review_data: any;
  submitted_at: string;
}

interface ParallelVerificationFile {
  file_id: number;
  filename: string;
  file_path: string;
  file_type?: string;
  file_extension?: string;
  processing_status: string;
  completion_count: number;
  annotator_reviews: AnnotatorReview[];
  batch_id?: number;
  url: string;
  is_csv?: boolean;
}

interface ParallelVerificationInterfaceProps {
  files: ParallelVerificationFile[];
  batchName: string;
  batchInfo: any;
  totalFiles: number;
  formConfig: any[];
}

interface VerifierDecision {
  file_id: number;
  field_decisions: Record<string, {
    chosen_annotator_id?: number;
    custom_response?: any;
    decision_type: 'annotator_choice' | 'custom_input';
    final_value: any;
  }>;
  completed: boolean;
}

export default function ParallelVerificationInterface({
  files = [],
  batchName,
  batchInfo,
  totalFiles,
  formConfig = []
}: ParallelVerificationInterfaceProps) {
  const router = useRouter();
  
  // State management
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [verifierDecisions, setVerifierDecisions] = useState<Record<number, VerifierDecision>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Get current file
  const currentFile = files.length > 0 ? files[currentFileIndex] : null;
  const currentDecision = currentFile ? verifierDecisions[currentFile.file_id] : null;

  // Initialize verifier decisions
  useEffect(() => {
    const decisions: Record<number, VerifierDecision> = {};
    files.forEach(file => {
      const fieldDecisions: Record<string, any> = {};
      
      // Initialize decisions for each field in the form config
      formConfig.forEach(field => {
        fieldDecisions[field.field_name] = {
          decision_type: 'annotator_choice',
          chosen_annotator_id: null,
          custom_response: null,
          final_value: null
        };
      });
      
      decisions[file.file_id] = {
        file_id: file.file_id,
        field_decisions: fieldDecisions,
        completed: false
      };
    });
    
    setVerifierDecisions(decisions);
  }, [files, formConfig]);

  // Navigation functions
  const handlePrevious = useCallback(() => {
    if (currentFileIndex > 0) {
      setCurrentFileIndex(currentFileIndex - 1);
    }
  }, [currentFileIndex]);

  const handleNext = useCallback(() => {
    if (currentFileIndex < files.length - 1) {
      setCurrentFileIndex(currentFileIndex + 1);
    }
  }, [currentFileIndex, files.length]);

  // Zoom functions
  const handleZoomIn = useCallback(() => setZoomLevel(prev => Math.min(prev + 10, 200)), []);
  const handleZoomOut = useCallback(() => setZoomLevel(prev => Math.max(prev - 10, 50)), []);
  const handleResetZoom = useCallback(() => setZoomLevel(100), []);

  // Handle field decision change
  const handleFieldDecisionChange = useCallback((fieldName: string, decision: any) => {
    if (!currentFile) return;
    
    setVerifierDecisions(prev => ({
      ...prev,
      [currentFile.file_id]: {
        ...prev[currentFile.file_id],
        field_decisions: {
          ...prev[currentFile.file_id].field_decisions,
          [fieldName]: decision
        }
      }
    }));
  }, [currentFile]);

  // Mark current file as completed
  const markFileCompleted = useCallback(() => {
    if (!currentFile) return;
    
    const fileDecision = verifierDecisions[currentFile.file_id];
    const allFieldsCompleted = Object.values(fileDecision.field_decisions).every(
      decision => decision.final_value !== null && decision.final_value !== undefined
    );
    
    if (allFieldsCompleted) {
      setVerifierDecisions(prev => ({
        ...prev,
        [currentFile.file_id]: {
          ...prev[currentFile.file_id],
          completed: true
        }
      }));
      
      showToast.success(`File ${currentFileIndex + 1} completed!`);
      
      // Auto-advance to next file if not last
      if (currentFileIndex < files.length - 1) {
        setTimeout(() => handleNext(), 500);
      }
    } else {
      showToast.warning("Please make decisions for all fields before marking as complete.");
    }
  }, [currentFile, verifierDecisions, currentFileIndex, files.length, handleNext]);

  // Save all decisions
  const handleSaveAll = async () => {
    setIsSaving(true);
    
    try {
      console.log('Saving parallel verification results:', verifierDecisions);
      
      const API_BASE = API_BASE_URL;
      
      // Prepare decisions for API
      const decisionsToSave = Object.values(verifierDecisions).map(decision => ({
        file_id: decision.file_id,
        verification_type: 'parallel',
        field_decisions: decision.field_decisions,
        completed: decision.completed,
        batch_name: batchName
      }));
      
      const response = await fetch(`${API_BASE}/verifier/save-parallel-review`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          batch_name: batchName,
          decisions: decisionsToSave
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save parallel verification: ${response.status}`);
      }
      
      await response.json();
      showToast.success("All parallel verification results saved successfully!");
      
      setTimeout(() => {
        router.push('/verifier');
      }, 2000);
      
    } catch (error) {
      console.error('Error saving parallel verification results:', error);
      showToast.error("Failed to save verification results");
    } finally {
      setIsSaving(false);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") handlePrevious();
      else if (e.key === "ArrowRight") handleNext();
      else if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        handleSaveAll();
      }
    };
    
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handlePrevious, handleNext, handleSaveAll]);

  // Calculate progress
  const completedFiles = Object.values(verifierDecisions).filter(d => d.completed).length;
  const progress = files.length > 0 ? Math.round((completedFiles / files.length) * 100) : 0;
  const allCompleted = completedFiles === files.length;

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">No files to verify</h2>
          <button
            onClick={() => router.push('/verifier')}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-16 left-0 right-0 bottom-0 bg-gray-50 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center flex-shrink-0 h-20 overflow-hidden">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2 mb-1">
            <FaUsers className="text-blue-500" />
            Parallel Verification: {batchName}
          </h1>
          <p className="text-gray-600 m-0">
            File {currentFileIndex + 1} of {files.length} • 
            {completedFiles} completed • 
            Progress: {progress}%
          </p>
        </div>
        
        <div className="flex gap-4">
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg font-medium bg-green-100 text-green-800">
            <FaCheck className="text-sm" />
            <span>{completedFiles} Completed</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg font-medium bg-yellow-100 text-yellow-800">
            <FaSpinner className="text-sm" />
            <span>{files.length - completedFiles} Pending</span>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 grid grid-cols-2 gap-0 h-[calc(100vh-144px)] overflow-hidden">
        {/* Left side: Media viewer */}
        <div className="bg-white border-r border-gray-200 flex flex-col h-full overflow-hidden relative">
          <div className="h-[45%] relative overflow-hidden">
            {currentFile && (
              <MediaViewer
                key={`${currentFile.file_id}-${currentFile.file_path}`}
                mediaPath={currentFile.file_type === 'csv' || currentFile.is_csv ? currentFile.url : currentFile.file_path}
                mediaType={currentFile.file_type === 'csv' || currentFile.is_csv ? 'csv' : detectMediaType(currentFile.file_path)}
                zoomLevel={zoomLevel}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetZoom={handleResetZoom}
                showZoomControls={true}
              />
            )}
          </div>

          {/* Integrated Progress and File Info */}
          <div className="border-t border-gray-200 bg-gray-50 flex flex-col gap-3.5 p-3.5 h-[55%] overflow-hidden">
            <div className="relative flex-shrink-0 bg-white rounded-lg border border-gray-200 px-3.5 py-2.5 shadow-sm h-[70px] flex flex-col justify-center">
              <h4 className="m-0 mb-1.5 text-base font-semibold text-gray-800">{currentFile?.filename}</h4>
              <p className="m-0 text-sm text-gray-500">
                Type: {currentFile?.file_type || 'Unknown'} • 
                Annotator Reviews: {currentFile?.annotator_reviews?.length || 0}/{batchInfo?.annotation_count || 0}
              </p>
              
              {currentDecision?.completed && (
                <div className="absolute top-3 right-3 bg-green-500 text-white px-2.5 py-1.5 rounded-xl text-xs font-semibold flex items-center gap-1 shadow-md shadow-green-500/30">
                  <FaCheck /> Completed
                </div>
              )}
            </div>

            {/* Compact Verification Progress */}
            {/* <div className="bg-white rounded-lg border border-gray-200 overflow-hidden flex-1 shadow-sm min-h-[200px] flex flex-col">
              <VerificationProgress
                totalFiles={files.length}
                verifierDecisions={verifierDecisions}
                formConfig={formConfig}
                className="compact"
              />
            </div> */}
          </div>
        </div>

        {/* Right side: Annotator comparison and verifier choices */}
        <div className="bg-white flex flex-col min-h-0">
          <div className="px-4 py-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
            <h3 className="text-lg font-semibold text-gray-800">Annotator Responses</h3>
            <p className="text-sm text-gray-600">
              Compare responses from {currentFile?.annotator_reviews?.length || 0} annotators
            </p>
          </div>

          {/* Scrollable Comparison Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {currentFile && currentFile.annotator_reviews && (
              <AnnotatorComparisonPanel
                annotatorReviews={currentFile.annotator_reviews}
                formConfig={formConfig}
                currentDecisions={currentDecision?.field_decisions || {}}
                onDecisionChange={handleFieldDecisionChange}
              />
            )}
          </div>
              
          {/* Fixed File Controls */}
          <div className="border-t border-gray-200 px-4 py-4 bg-gray-50 flex flex-col gap-3 flex-shrink-0">
            <div className="flex justify-between items-center">
              <button
                className="px-2.5 py-1.5 bg-gray-500 text-white border border-gray-600 rounded cursor-pointer transition-all duration-200 flex items-center gap-1 text-xs min-w-[80px] max-w-[100px] font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-400 hover:bg-gray-600 hover:border-gray-700"
                onClick={handlePrevious}
                disabled={currentFileIndex === 0}
              >
                <FaArrowLeft />
                Previous
              </button>

              <span className="font-medium text-gray-700">
                {currentFileIndex + 1} / {files.length}
              </span>

              <button
                className="px-2.5 py-1.5 bg-gray-500 text-white border border-gray-600 rounded cursor-pointer transition-all duration-200 flex items-center gap-1 text-xs min-w-[80px] max-w-[100px] font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-400 hover:bg-gray-600 hover:border-gray-700"
                onClick={handleNext}
                disabled={currentFileIndex === files.length - 1 || !currentDecision?.completed}
              >
                Next
                <FaArrowRight />
              </button>
            </div>
            
            <button
              className="w-full px-3 py-3 bg-green-600 text-white border-none rounded-lg font-medium cursor-pointer transition-colors duration-200 flex items-center justify-center gap-2 disabled:bg-gray-500 disabled:cursor-not-allowed hover:bg-green-700"
              onClick={markFileCompleted}
              disabled={currentDecision?.completed}
            >
              {currentDecision?.completed ? (
                <>
                  <FaCheck /> Completed
                </>
              ) : (
                <>
                  <FaCheck /> Mark Complete
                </>
              )}
            </button>
            
            {allCompleted && (
              <button
                className="w-full px-3 py-3 bg-blue-600 text-white border-none rounded-lg font-medium cursor-pointer transition-colors duration-200 flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed hover:bg-blue-700"
                onClick={handleSaveAll}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <FaSpinner className="animate-spin" /> Saving...
                  </>
                ) : (
                  <>
                    <FaSave /> Save All Results
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

    </div>
  );
}
