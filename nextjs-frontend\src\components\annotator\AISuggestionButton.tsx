"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaLightbulb, FaExclamationTriangle } from 'react-icons/fa';

interface AISuggestionButtonProps {
  fieldName: string;
  aiSuggestions?: Record<string, any> | null;
  onApplySuggestion: (fieldName: string, value: any) => void;
  className?: string;
  fieldType?: string; // Add field type to handle different data formats
}

export default function AISuggestionButton({
  fieldName,
  aiSuggestions,
  onApplySuggestion,
  className = "",
  fieldType
}: AISuggestionButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check if AI suggestions are available for this field
  const hasAISuggestion = aiSuggestions && aiSuggestions[fieldName] !== undefined && aiSuggestions[fieldName] !== null && aiSuggestions[fieldName] !== "";
  
  if (!hasAISuggestion) {
    return null; // Don't render button if no AI suggestion available
  }

  const rawAiValue = aiSuggestions[fieldName];
  
  
  // Format AI value based on field type
  const formatAiValueForField = (value: any, fieldType?: string): any => {
    if (value === null || value === undefined) return value;
    
    switch (fieldType) {
      case 'checkboxes':
        // For checkbox fields, ensure we return an array
        if (Array.isArray(value)) return value;
        if (typeof value === 'string') {
          // Try to parse comma-separated values or split by common delimiters
          return value.split(/[,;|]/).map(v => v.trim()).filter(v => v.length > 0);
        }
        return [String(value)];
      
      case 'multiple_choice':
        // For single choice fields, return as string
        return Array.isArray(value) ? value[0] || '' : String(value);
      
      case 'long_answer':
      case 'short_answer':
      case 'text':
        // For text fields, return as string
        return String(value);
      
      default:
        return value;
    }
  };
  
  const aiValue = formatAiValueForField(rawAiValue, fieldType);
  
  const handleApplySuggestion = () => {
    setIsLoading(true);
    try {
      onApplySuggestion(fieldName, aiValue);
      setIsOpen(false);
    } catch (error) {
      console.error('Error applying AI suggestion:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatAIValue = (value: any): string => {
    if (value === null || value === undefined) return '';
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  return (
    <div className="relative inline-block">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`inline-flex items-center px-2 py-1 bg-gradient-to-r from-indigo-500 to-purple-600 text-white border-none rounded-md text-xs cursor-pointer transition-all duration-200 ease-in-out shadow-sm hover:shadow-md hover:-translate-y-0.5 disabled:opacity-60 disabled:cursor-not-allowed disabled:transform-none ${className}`}
        title="View AI Suggestion"
        disabled={isLoading}
      >
        {isLoading ? (
          <FaSpinner className="animate-spin" />
        ) : (
          <FaRobot />
        )}
        <span className="ml-1 text-xs">AI</span>
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 z-[1000] bg-white border border-gray-300 rounded-lg shadow-xl min-w-[300px] max-w-[400px] mt-1">
          <div className="flex items-center px-4 py-3 bg-gray-50 border-b border-gray-300 rounded-t-lg">
            <FaLightbulb className="text-yellow-500" />
            <span className="flex-1 ml-2 font-medium text-gray-700">AI Suggestion</span>
            <button
              type="button"
              onClick={() => setIsOpen(false)}
              className="bg-transparent border-none text-gray-500 cursor-pointer text-lg leading-none p-0 w-5 h-5 flex items-center justify-center hover:text-gray-700 transition-colors"
            >
              ×
            </button>
          </div>
          
          <div className="p-4">
            <div className="bg-gray-100 border border-gray-300 rounded-md p-3 mb-3 font-mono text-sm text-gray-700 whitespace-pre-wrap break-words max-h-[200px] overflow-y-auto">
              {formatAIValue(aiValue)}
            </div>
            
            <div className="flex gap-2 mb-3">
              <button
                type="button"
                onClick={handleApplySuggestion}
                className="flex-1 bg-emerald-500 text-white border-none rounded-md px-3 py-2 text-sm cursor-pointer transition-colors duration-200 flex items-center justify-center hover:bg-emerald-600 disabled:opacity-60 disabled:cursor-not-allowed"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <FaSpinner className="animate-spin mr-1" />
                    Applying...
                  </>
                ) : (
                  'Apply Suggestion'
                )}
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="bg-gray-500 text-white border-none rounded-md px-3 py-2 text-sm cursor-pointer transition-colors duration-200 hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
            
            <div className="flex items-start p-2 bg-amber-50 rounded-md border-l-4 border-amber-400">
              <FaExclamationTriangle className="text-amber-500 mr-1" />
              <span className="flex-1 text-xs text-gray-600 leading-relaxed">
                AI suggestions may not always be accurate. Please review before applying.
              </span>
            </div>
          </div>
        </div>
      )}
      
    </div>
  );
}
