"""
Project routes modules package.
"""
from fastapi import APIRouter

router = APIRouter()

from .project_routes import router as project_router
from .project_creation_routes import router as project_creation_router
from .project_batch_routes import router as project_batch_router
from .project_batch_allocations_routes import router as project_batch_allocations_router
from .project_users_routes import router as project_users_router
from .project_activation_routes import router as project_activation_router

__all__ = [
    "project_router",
    "project_creation_router",
    "project_batch_router",
    "project_batch_allocations_router",
    "project_users_router",
    "project_activation_router"
]
