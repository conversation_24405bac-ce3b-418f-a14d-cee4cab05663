"""
FastAPI routes for project batch management.
"""

from fastapi import APIRouter, HTTP<PERSON>xception, Depends, Query, Body #type:ignore
from fastapi.responses import JSONResponse #type:ignore
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging
from services.project_batch_service import ProjectBatchService
from services.project_batch_service_dynamic import DynamicProjectBatchService
from services.batch_allocation_sync_service import BatchAllocationSyncService
from services.csv_batch_service import CSVBatchService
from dependencies.auth import get_current_user
from sqlalchemy.ext.asyncio import AsyncSession
from core.session_manager import get_master_db_session
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/project-batches", tags=["project-batches"])

# Request and response models
class CreateBatchesRequest(BaseModel):
    folder_path: Optional[str] = None
    files_per_batch: Optional[int] = None
    content_type: Optional[str] = None

class BatchResponse(BaseModel):
    id: int
    batch_identifier: str
    total_files: int
    batch_status: str
    created_at: Any

class FileResponse(BaseModel):
    id: int
    batch_id: int
    file_identifier: str
    original_filename: Optional[str] = None
    file_type: Optional[str] = None
    processing_status: str

class UpdateBatchStatusRequest(BaseModel):
    status: str

# Service dependencies
def get_batch_service():
    return ProjectBatchService()

def get_dynamic_batch_service():
    return DynamicProjectBatchService()

def get_sync_service():
    return BatchAllocationSyncService()

def get_csv_batch_service():
    return CSVBatchService()

@router.post("/{project_code}/create-batches", response_model=Dict[str, Any])
async def create_batches(
    project_code: str,
    request: CreateBatchesRequest,
    batch_service: ProjectBatchService = Depends(get_batch_service)
):
    """
    Create allocation batches from files in a folder.
    
    Args:
        project_code: The project code
        request: Batch creation request
        
    Returns:
        Dict: Result of batch creation
    """
    try:
        success, message, batch_count = await batch_service.create_batches_from_folder(
            project_code=project_code,
            folder_path=request.folder_path,
            files_per_batch=request.files_per_batch,
            content_type=request.content_type
        )
        
        if not success:
            raise HTTPException(status_code=400, detail=message)
        
        return {
            "success": True,
            "message": message,
            "batch_count": batch_count,
            "project_code": project_code
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating batches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating batches: {str(e)}")

@router.post("/{project_code}/create-csv-batches", response_model=Dict[str, Any])
async def create_csv_batches(
    project_code: str,
    csv_batch_service: CSVBatchService = Depends(get_csv_batch_service),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Create allocation batches from CSV data for CSV projects.
    This endpoint reads the uploaded CSV file, gets the configuration from Redis,
    and creates batches based on the selected column and batch size.
    
    Args:
        project_code: The project code for the CSV project
        csv_batch_service: CSV batch service dependency
        master_db: Master database session
        
    Returns:
        Dict: Result of CSV batch creation
    """
    try:
        logger.info(f"Creating CSV batches for project: {project_code}")
        
        # Create CSV batches in the project database
        result = await csv_batch_service.create_csv_batches_in_project_db(project_code, master_db)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        logger.info(f"Successfully created CSV batches for project {project_code}: {result['created_batches']} batches, {result['created_files']} files")
        
        return {
            "success": True,
            "message": result["message"],
            "project_code": project_code,
            "batch_count": result["created_batches"],
            "file_count": result["created_files"],
            "batch_ids": result["batch_ids"],
            "statistics": result["statistics"]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating CSV batches for project {project_code}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating CSV batches: {str(e)}")

@router.get("/{project_code}/batches", response_model=List[Dict[str, Any]])
async def get_batches(
    project_code: str,
    batch_service: ProjectBatchService = Depends(get_batch_service)
):
    """
    Get all batches for a project.
    
    Args:
        project_code: The project code
        
    Returns:
        List[Dict]: List of batch information
    """
    try:
        batches = await batch_service.get_project_batches(project_code)
        return batches
        
    except Exception as e:
        logger.error(f"Error getting batches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting batches: {str(e)}")

@router.get("/{project_code}/batches/{batch_id}/files", response_model=List[Dict[str, Any]])
async def get_batch_files(
    project_code: str,
    batch_id: int,
    batch_service: ProjectBatchService = Depends(get_batch_service)
):
    """
    Get all files for a batch.
    
    Args:
        project_code: The project code
        batch_id: The batch ID
        
    Returns:
        List[Dict]: List of file information
    """
    try:
        files = await batch_service.get_batch_files(project_code, batch_id)
        return files
        
    except Exception as e:
        logger.error(f"Error getting batch files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting batch files: {str(e)}")

@router.put("/{project_code}/batches/{batch_id}/status", response_model=Dict[str, Any])
async def update_batch_status(
    project_code: str,
    batch_id: int,
    request: UpdateBatchStatusRequest,
    batch_service: ProjectBatchService = Depends(get_batch_service)
):
    """
    Update the status of a batch.
    
    Args:
        project_code: The project code
        batch_id: The batch ID
        request: Status update request
        
    Returns:
        Dict: Updated batch information
    """
    try:
        updated_batch = await batch_service.update_batch_status(
            project_code=project_code,
            batch_id=batch_id,
            status=request.status
        )
        
        return {
            "success": True,
            "batch": updated_batch
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating batch status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating batch status: {str(e)}")

@router.post("/{project_code}/register-files", response_model=Dict[str, Any])
async def register_files(
    project_code: str,
    batch_id: int = Query(..., description="The batch ID to associate files with"),
    folder_path: str = Query(..., description="Path to folder containing files"),
    content_type: str = Query("image", description="Type of content (image, video, etc.)"),
    batch_service: ProjectBatchService = Depends(get_batch_service)
):
    """
    Register files for a batch from a folder.
    
    Args:
        project_code: The project code
        batch_id: The batch ID
        folder_path: Path to folder containing files
        content_type: Type of content
        
    Returns:
        Dict: Result of file registration
    """
    try:
        # Get media files from folder
        from utils.media_utils import get_media_from_folder
        all_media, _ = await get_media_from_folder(
            folder_path, content_type=content_type, page=1, items_per_page=100000, recursive=True
        )
        
        if not all_media:
            raise HTTPException(status_code=400, detail=f"No {content_type} files found in folder: {folder_path}")
        
        # Register files
        registered_files = await batch_service.register_files_for_batch(
            project_code=project_code,
            batch_id=batch_id,
            media_files=all_media,
            content_type=content_type
        )
        
        return {
            "success": True,
            "message": f"Registered {len(registered_files)} files for batch {batch_id}",
            "file_count": len(registered_files),
            "batch_id": batch_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error registering files: {str(e)}")

# Dynamic assignment routes for strategy-based schemas

class AssignAnnotatorsRequest(BaseModel):
    annotator_ids: List[int]

class AssignAuditorsRequest(BaseModel):
    auditor_ids: List[int]

@router.post("/{project_code}/batches/{batch_id}/assign-annotators", response_model=Dict[str, Any])
async def assign_annotators_to_batch(
    project_code: str,
    batch_id: int,
    request: AssignAnnotatorsRequest,
    dynamic_service: DynamicProjectBatchService = Depends(get_dynamic_batch_service)
):
    """
    Assign annotators to a batch based on the project's allocation strategy.
    
    Args:
        project_code: The project code
        batch_id: The batch ID
        request: Annotator assignment request
        
    Returns:
        Dict: Result of annotator assignment
    """
    try:
        result = await dynamic_service.assign_annotators_to_batch(
            project_code=project_code,
            batch_id=batch_id,
            annotator_ids=request.annotator_ids
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning annotators: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error assigning annotators: {str(e)}")

@router.get("/{project_code}/batches/{batch_id}/assignments", response_model=Dict[str, Any])
async def get_batch_assignments(
    project_code: str,
    batch_id: int,
    dynamic_service: DynamicProjectBatchService = Depends(get_dynamic_batch_service)
):
    """
    Get all assignments for a batch (annotators, auditors, etc.).
    
    Args:
        project_code: The project code
        batch_id: The batch ID
        
    Returns:
        Dict: Batch assignment information
    """
    try:
        result = await dynamic_service.get_batch_assignments(
            project_code=project_code,
            batch_id=batch_id
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting batch assignments: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting batch assignments: {str(e)}")

# Batch Allocation Sync Routes

@router.post("/projects/{project_id}/sync-batch-allocations", response_model=Dict[str, Any])
async def sync_project_batch_allocations(
    project_id: int,
    sync_service: BatchAllocationSyncService = Depends(get_sync_service),
    master_db: AsyncSession = Depends(get_master_db_session),
):
    """
    Sync all batch allocation counts for a project based on actual completion data.
    Updates hardcoded counts in allocation_batches table with real completion counts.
    
    Args:
        project_id: The project ID from master database
        sync_service: Batch allocation sync service
        master_db: Master database session
        current_user: Current authenticated user
        
    Returns:
        Dict: Sync results for all batches in the project
    """
    try:
        result = await sync_service.sync_batch_allocations(project_id, master_db)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing project batch allocations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error syncing project batch allocations: {str(e)}")

@router.post("/projects/{project_id}/batches/{batch_id}/sync-allocation", response_model=Dict[str, Any])
async def sync_single_batch_allocation(
    project_id: int,
    batch_id: int,
    sync_service: BatchAllocationSyncService = Depends(get_sync_service),
    master_db: AsyncSession = Depends(get_master_db_session),
):
    """
    Sync allocation counts for a single batch based on actual completion data.
    Updates hardcoded counts in allocation_batches table with real completion counts.
    
    Args:
        project_id: The project ID from master database
        batch_id: The specific batch ID to sync
        sync_service: Batch allocation sync service
        master_db: Master database session
        current_user: Current authenticated user
        
    Returns:
        Dict: Sync results for the specific batch
    """
    try:
        result = await sync_service.sync_single_batch_allocation(project_id, batch_id, master_db)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing single batch allocation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error syncing single batch allocation: {str(e)}")

# Project Progress Sync Routes

@router.post("/projects/{project_id}/sync-progress", response_model=Dict[str, Any])
async def sync_project_progress(
    project_id: int,
    sync_service: BatchAllocationSyncService = Depends(get_sync_service),
    master_db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_user)
):
    """
    Sync project progress (completed_files) in master database based on actual completion data.
    Updates the progress bar in the main projects table.
    
    Args:
        project_id: The project ID from master database
        sync_service: Batch allocation sync service (now includes progress sync)
        master_db: Master database session
        current_user: Current authenticated user
        
    Returns:
        Dict: Sync results for project progress
    """
    try:
        result = await sync_service.sync_project_progress(project_id, master_db)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error syncing project progress: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error syncing project progress: {str(e)}")
