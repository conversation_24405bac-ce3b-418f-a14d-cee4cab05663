'use client';

// import { useContext } from 'react';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import About from '@/components/landing/About';
import Contact from '@/components/landing/Contact';

export default function Home() {
  // We need to access the login modal open function from the RootLayout
  // This is a placeholder implementation - we'll use context or other state management in a real app
  
  const handleOpenLogin = () => {
    // Find the login button in the header and trigger it
    const loginButton = document.querySelector('header button:last-child') as HTMLButtonElement;
    if (loginButton) {
      loginButton.click();
    }
  };
  
  return (
    <>
      <Hero onOpenLogin={handleOpenLogin} />
      <Features />
      <About />
      <Contact />
    </>
  );
} 