import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON>, FaCheckCircle, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaArrowLeft } from 'react-icons/fa';
import { Client } from '../types';
import { API_BASE_URL } from "@/lib/api";

interface ProjectStartProps {
  projectCode: string | null;
  selectedClient: Client | null;
  onGoToStep: (step: number) => void;
  markStepCompleted: (step: number) => void;
}

const ProjectStart: React.FC<ProjectStartProps> = ({ projectCode, selectedClient, onGoToStep, markStepCompleted }) => {
  const [starting, setStarting] = useState(false);
  const [started, setStarted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [databaseName, setDatabaseName] = useState<string | null>(null);
  const [loadingDbName, setLoadingDbName] = useState(false);
  const [showWarning, setShowWarning] = useState(true);

  // Fetch database name when component mounts
  useEffect(() => {
    const fetchDatabaseName = async () => {
      if (!projectCode) return;
      
      setLoadingDbName(true);
      setError(null);
      
      try {
        // First try to get database name from localStorage
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          if (project.database_name) {
            setDatabaseName(project.database_name);
            setLoadingDbName(false);
            return;
          }
        }
        
        // Fallback: fetch from API if not in localStorage
        const response = await fetch(`${API_BASE_URL}/projects/${projectCode}/get-database-name`);
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch database name');
        }
        
        if (data.success && data.database_name) {
          setDatabaseName(data.database_name);
          // Store in localStorage for future use
          if (projectData) {
            const project = JSON.parse(projectData);
            project.database_name = data.database_name;
            localStorage.setItem('currentProject', JSON.stringify(project));
          }
        } else {
          throw new Error('No database name returned');
        }
      } catch (e: any) {
        console.error('Error fetching database name:', e);
        setError(`Error fetching database name: ${e.message}`);
      } finally {
        setLoadingDbName(false);
      }
    };
    
    fetchDatabaseName();
  }, [projectCode]);

  const handleStart = async () => {
    if (!projectCode) return;
    setStarting(true);
    setError(null);
    try {
      // Step 1: Provision the database
      const payload = databaseName ? { database_name: databaseName } : {};
      
      const resp = await fetch(`${API_BASE_URL}/projects/${projectCode}/provision-database`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      
      if (!resp.ok) {
        const body = await resp.json();
        throw new Error(body.detail || 'Failed to provision database');
      }
      
      // Step 2: Sync project metadata from master DB to project DB
      const syncMetadataResp = await fetch(`${API_BASE_URL}/projects/${projectCode}/sync-metadata`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!syncMetadataResp.ok) {
        const body = await syncMetadataResp.json();
        throw new Error(body.detail || 'Failed to sync project metadata');
      }
      
      // Step 3: Create batches - different logic for CSV vs file-based projects
      let createBatchesResp;
      
      // Check if this is a CSV project by looking at localStorage or making an API call
      const projectData = localStorage.getItem('currentProject');
      let isCSVProject = false;
      
      if (projectData) {
        try {
          const project = JSON.parse(projectData);
          isCSVProject = project.project_type === 'csv';
        } catch (e) {
          console.warn('Error parsing project data from localStorage:', e);
        }
      }
      
      if (isCSVProject) {
        // For CSV projects, create batches from CSV data
        createBatchesResp = await fetch(`${API_BASE_URL}/project-batches/${projectCode}/create-csv-batches`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        // For file-based projects, create batches from folder (using project registry folder path)
        createBatchesResp = await fetch(`${API_BASE_URL}/project-batches/${projectCode}/create-batches`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            batch_purpose: "annotation"
            // No folder_path, files_per_batch or content_type - will use values from project registry
          })
        });
      }
      
      if (!createBatchesResp.ok) {
        const body = await createBatchesResp.json();
        throw new Error(body.detail || 'Failed to create batches');
      }
      
      setStarted(true);
      // Mark step 7 as completed when project processing is successful
      markStepCompleted(7);
    } catch (e: any) {
      setError(e.message);
    } finally {
      setStarting(false);
    }
  };

  return (
    <div className="flex flex-col items-center p-8 bg-white rounded shadow-md">
      <h2 className="text-2xl font-semibold mb-4">Start Project Processing</h2>
      
      {/* Warning Message */}
      {showWarning && (
        <div className="w-full mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Important: Database Provisioning
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>Once provisioning is started, you cannot review or change the project configuration. Please ensure all settings are correct before proceeding.</p>
              </div>
              <div className="mt-3">
                <button
                  type="button"
                  onClick={() => setShowWarning(false)}
                  className="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                >
                  I understand, hide this warning
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {selectedClient && (
        <p className="mb-2 text-gray-600">Client: {selectedClient.full_name} (@{selectedClient.username})</p>
      )}
      <p className="mb-6 text-gray-600">This will create the dedicated database and begin dataset batching.</p>
      
      {loadingDbName ? (
        <p className="mb-4 text-blue-600">Loading database information...</p>
      ) : databaseName ? (
        <p className="mb-4 text-gray-600">Database name: <span className="font-semibold">{databaseName}</span></p>
      ) : null}

      {error && <p className="text-red-600 mb-4">{error}</p>}

      {started ? (
        <div className="text-center">
          <p className="text-green-600 flex items-center gap-2 mb-4"><FaCheckCircle /> Project setup completed successfully!</p>
          <p className="text-gray-600 mb-2">The following actions were completed:</p>
          <ul className="list-disc text-left ml-8 mb-4">
            <li>Project database provisioned</li>
            <li>{(() => {
              const projectData = localStorage.getItem('currentProject');
              let isCSVProject = false;
              if (projectData) {
                try {
                  const project = JSON.parse(projectData);
                  isCSVProject = project.project_type === 'csv';
                } catch (e) {}
              }
              return isCSVProject ? 'CSV data processed into annotation batches' : 'Media files processed into batches';
            })()}</li>
            <li>{(() => {
              const projectData = localStorage.getItem('currentProject');
              let isCSVProject = false;
              if (projectData) {
                try {
                  const project = JSON.parse(projectData);
                  isCSVProject = project.project_type === 'csv';
                } catch (e) {}
              }
              return isCSVProject ? 'CSV cells registered for annotation' : 'Files registered for annotation';
            })()}</li>
          </ul>
          <p className="text-gray-600 mb-6">For further details and project management, please check the <strong>Projects</strong> section.</p>
        </div>
      ) : (
        <button
          disabled={!projectCode || starting}
          onClick={handleStart}
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center gap-2 disabled:opacity-50"
        >
          {starting ? <FaSpinner className="animate-spin" /> : <FaPlay />}
          Start Processing
        </button>
      )}

      <button
        onClick={() => onGoToStep(4)}
        disabled={starting || started}
        className={`mt-6 flex items-center gap-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ${
          starting || started ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        <FaArrowLeft /> Back to Instructions
      </button>
    </div>
  );
};

export default ProjectStart;
