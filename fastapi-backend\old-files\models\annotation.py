"""
This module defines the SQLAlchemy models for the annotation workflow, 
based on the schema defined in annotation_workflow_schema.md.
"""

from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, Boolean, func, Foreign<PERSON>ey, Text, DECIMAL
)
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>
from sqlalchemy.orm import relationship
# from ..base import Base
# from ..enums import CaseInsensitiveEnum
# from enum import Enum as PyEnum

from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum


# Enum definitions based on the schema

class TaskType(str, PyEnum):
    ANNOTATION = 'annotation'
    REVIEW = 'review'
    AUDIT = 'audit'
    VERIFICATION = 'verification'

class TaskPhase(str, PyEnum):
    PRIMARY = 'primary'
    SECONDARY = 'secondary'
    AUDIT_LEVEL_1 = 'audit_level_1'
    AUDIT_LEVEL_2 = 'audit_level_2'

class AnnotationMode(str, PyEnum):
    AI_ONLY = 'ai_only'
    AI_HUMAN_HYBRID = 'ai_human_hybrid'
    HUMAN_ONLY = 'human_only'

class TaskStatus(str, PyEnum):
    ASSIGNED = 'assigned'
    IN_PROGRESS = 'in_progress'
    SUBMITTED = 'submitted'
    APPROVED = 'approved'
    REJECTED = 'rejected'

class AnnotationSource(str, PyEnum):
    AI = 'ai'
    HUMAN = 'human'
    AI_VERIFIED_BY_HUMAN = 'ai_verified_by_human'

class CreationMethod(str, PyEnum):
    MANUAL = 'manual'
    AI_GENERATED = 'ai_generated'
    AI_ASSISTED = 'ai_assisted'
    IMPORTED = 'imported'

class ValidationStatus(str, PyEnum):
    PENDING = 'pending'
    VALID = 'valid'
    INVALID = 'invalid'

class AnnotationStatus(str, PyEnum):
    DRAFT = 'draft'
    SUBMITTED = 'submitted'
    APPROVED = 'approved'
    REJECTED = 'rejected'

class SubmissionStatus(str, PyEnum):
    NOT_SUBMITTED = 'not_submitted'
    SUBMITTED = 'submitted'
    RESUBMITTED = 'resubmitted'

class ReviewType(str, PyEnum):
    QUALITY_CHECK = 'quality_check'
    AUDIT_LEVEL_1 = 'audit_level_1'
    AUDIT_LEVEL_2 = 'audit_level_2'
    AUDIT_LEVEL_3 = 'audit_level_3'
    TASK_VERIFICATION = 'task_verification'

class ReviewDecision(str, PyEnum):
    APPROVE_TASK = 'approve_task'
    REJECT_TASK = 'reject_task'
    NEEDS_REVISION = 'needs_revision'
    ESCALATE = 'escalate'
    REASSIGN = 'reassign'

class RecommendedAction(str, PyEnum):
    ACCEPT = 'accept'
    REVISE = 'revise'
    REASSIGN = 'reassign'
    ESCALATE = 'escalate'

class SelectionMethod(str, PyEnum):
    SINGLE_TASK = 'single_task'
    MULTI_TASK_CONSENSUS = 'multi_task_consensus'
    AUDIT_APPROVED = 'audit_approved'
    TASK_AGGREGATION = 'task_aggregation'
    AUDITOR_CREATED = 'auditor_created'


class ApprovalStatus(str, PyEnum):
    PENDING = 'pending'
    APPROVED = 'approved'
    DELIVERED = 'delivered'
    REJECTED = 'rejected'

class DeliveryStatus(str, PyEnum):
    PENDING = 'pending'
    READY = 'ready'
    DELIVERED = 'delivered'
    ACCEPTED = 'accepted'


class AnnotationWork(ProjectBase):
    __tablename__ = 'annotation_work'
    
    # Primary Identity & References
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    allocation_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("project_users.user_id", ondelete="CASCADE"), nullable=False)
    username = Column(String(255), nullable=False)
    
    task_type = Column(CaseInsensitiveEnum(TaskType), nullable=False)
    task_phase = Column(String(50), nullable=False)
    annotation_mode = Column(CaseInsensitiveEnum(AnnotationMode), default=AnnotationMode.HUMAN_ONLY)
    
    task_status = Column(CaseInsensitiveEnum(TaskStatus), default=TaskStatus.ASSIGNED)
    progress_percentage = Column(DECIMAL(5, 2), default=0)
    
    task_started_at = Column(TIMESTAMP, nullable=True)
    task_submitted_at = Column(TIMESTAMP, nullable=True)
    task_completed_at = Column(TIMESTAMP, nullable=True)
    time_spent_seconds = Column(Integer, default=0)
    
    self_confidence_score = Column(DECIMAL(5, 2), nullable=True)
    task_quality_score = Column(DECIMAL(5, 2), nullable=True)
    revision_count = Column(Integer, default=0)
    
    depends_on_task_id = Column(Integer, ForeignKey("annotation_work.id"), nullable=True)
    
    task_instructions = Column(Text, nullable=True)
    expected_deliverable = Column(JSONB, nullable=True)
    custom_task_attributes = Column(JSONB, nullable=True)
    
    allocation = relationship("AllocationBatches", back_populates="annotation_work")
    depends_on_task = relationship("AnnotationWork", remote_side=[id], back_populates="dependent_tasks")
    dependent_tasks = relationship("AnnotationWork", back_populates="depends_on_task")
    user = relationship("ProjectUsers", back_populates="annotation_work")
    annotations = relationship("Annotations", back_populates="task")
    reviewed_tasks = relationship("AnnotationReviews", foreign_keys="[AnnotationReviews.reviewed_task_id]", back_populates="reviewed_task")
    reviewer_tasks = relationship("AnnotationReviews", foreign_keys="[AnnotationReviews.reviewer_task_id]", back_populates="reviewer_task")


class Annotations(ProjectBase):
    __tablename__ = 'annotations'
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    file_id = Column(Integer, ForeignKey("files_registry.id", ondelete="CASCADE"), nullable=False)
    allocation_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"))
    user_id = Column(Integer, ForeignKey("project_users.user_id", ondelete="CASCADE"), nullable=False)
    
    annotation_data = Column(JSONB, nullable=False)
    
    annotation_mode = Column(CaseInsensitiveEnum(AnnotationMode), nullable=False)
    expected_schema_version = Column(Integer, default=1)
    follows_allocation_strategy = Column(String(50), nullable=False)
    
    annotation_source = Column(CaseInsensitiveEnum(AnnotationSource), default=AnnotationSource.HUMAN)
    creation_method = Column(CaseInsensitiveEnum(CreationMethod), default=CreationMethod.MANUAL)
    annotation_version = Column(Integer, default=1)
    
    ai_raw_output = Column(JSONB, nullable=True)
    ai_confidence_scores = Column(JSONB, nullable=True)
    ai_model_info = Column(JSONB, nullable=True)
    human_modifications = Column(JSONB, nullable=True)
    verification_by_human = Column(Boolean, default=False)
    
    file_type = Column(String(50), nullable=False)
    original_file_content_ref = Column(JSONB, nullable=True)
    
    parallel_annotation_sequence = Column(Integer, default=1)
    is_blind_annotation = Column(Boolean, default=False)
    peer_annotation_ids = Column(JSONB, nullable=True)
    isolation_level = Column(String(50), default='none')
    
    confidence_score = Column(DECIMAL(5, 4), nullable=True)
    validation_status = Column(CaseInsensitiveEnum(ValidationStatus), default=ValidationStatus.PENDING)
    validation_errors = Column(JSONB, nullable=True)
    quality_requirements_met = Column(Boolean, default=False)
    
    annotation_status = Column(CaseInsensitiveEnum(AnnotationStatus), default=AnnotationStatus.DRAFT)
    submission_status = Column(CaseInsensitiveEnum(SubmissionStatus), default=SubmissionStatus.NOT_SUBMITTED)
    
    created_at = Column(TIMESTAMP, default=func.now())
    submitted_at = Column(TIMESTAMP, nullable=True)
    last_modified_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now())
    
    custom_annotation_fields = Column(JSONB, nullable=True)
    client_specific_metadata = Column(JSONB, nullable=True)
    
    file = relationship("FilesRegistry", back_populates="annotations")
    allocation = relationship("AllocationBatches", back_populates="annotations")
    user = relationship("ProjectUsers", back_populates="annotations")

class AnnotationReviews(ProjectBase):
    __tablename__ = 'annotation_reviews'
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    reviewed_task_id = Column(Integer, ForeignKey("annotation_work.id", ondelete="CASCADE"))
    reviewer_task_id = Column(Integer, ForeignKey("annotation_work.id", ondelete="CASCADE"))
    reviewer_user_id = Column(Integer, ForeignKey("project_users.user_id", ondelete="CASCADE"), nullable=False)
    reviewer_username = Column(String(255), ForeignKey("project_users.username", ondelete="CASCADE"), nullable=False)
    
    review_type = Column(CaseInsensitiveEnum(ReviewType), nullable=False)
    review_phase = Column(String(50), nullable=False)
    audit_level = Column(Integer, default=0)
    follows_allocation_audit = Column(Boolean, default=True)
    
    review_decision = Column(CaseInsensitiveEnum(ReviewDecision), nullable=False)
    review_score = Column(DECIMAL(5, 2), nullable=True)
    review_feedback = Column(Text, nullable=True)   
    quality_criteria_met = Column(JSONB, nullable=True)
    
    task_accuracy = Column(DECIMAL(5, 2), nullable=True)
    task_completeness = Column(DECIMAL(5, 2), nullable=True)
    meets_task_requirements = Column(Boolean, default=False)
    
    recommended_action = Column(CaseInsensitiveEnum(RecommendedAction), nullable=True)
    revision_instructions = Column(Text, nullable=True)
    escalation_reason = Column(Text, nullable=True)
    
    review_started_at = Column(TIMESTAMP, default=func.now())
    review_completed_at = Column(TIMESTAMP, nullable=True)
    
    depends_on_review_id = Column(Integer, ForeignKey("annotation_reviews.id"), nullable=True)
    blocks_task_ids = Column(JSONB, nullable=True)
    
    task_compliance = Column(JSONB, nullable=True)
    expected_review_criteria = Column(JSONB, nullable=True)
    custom_review_attributes = Column(JSONB, nullable=True)
    
    reviewer = relationship("ProjectUsers", foreign_keys=[reviewer_user_id], back_populates="reviewer_tasks")
    reviewed_task = relationship("AnnotationWork", foreign_keys=[reviewed_task_id], back_populates="reviewed_tasks")
    reviewer_task = relationship("AnnotationWork", foreign_keys=[reviewer_task_id], back_populates="reviewer_tasks")
    depends_on_review = relationship("AnnotationReviews", remote_side=[id], back_populates="dependent_reviews")
    dependent_reviews = relationship("AnnotationReviews", back_populates="depends_on_review")


