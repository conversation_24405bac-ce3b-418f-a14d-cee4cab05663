"""
Service for managing verifier batch assignments.
Handles the logic for assigning completed batches to verifiers for verification.
"""

import logging
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from sqlalchemy.sql import text

from core.session_manager import get_project_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users
from post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from core.session_manager import get_master_db_context
from post_db.allocation_models.project_users import ProjectUsers
from services.batch_allocation_sync_service import BatchAllocationSyncService

logger = logging.getLogger(__name__)


class VerifierBatchAssignmentService:
    """
    Service for managing verifier batch assignments.
    Finds completed batches (where completion_count == annotation_count) 
    and assigns them to verifiers for verification.
    """

    def __init__(self):
        pass

    async def get_user_active_project(self, user_id: int) -> Optional[str]:
        """Get the user's active project from master database."""
        try:
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(users).where(users.id == user_id)
                )
                user = result.scalar_one_or_none()
                if user:
                    return user.active_project
                return None
        except Exception as e:
            logger.error(f"Error getting user active project: {str(e)}")
            return None

    async def get_project_database_name(self, project_code: str) -> Optional[str]:
        """Get the database name for the project from master database."""
        try:
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                if project:
                    return project.database_name
                return None
        except Exception as e:
            logger.error(f"Error getting project database name: {str(e)}")
            return None

    async def get_project_allocation_strategy(self, project_code: str) -> Optional[AllocationStrategies]:
        """Get the allocation strategy for a project."""
        try:
            async with get_master_db_context() as session:
                # Get project info
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    logger.warning(f"No allocation strategy found for project {project_code}")
                    return None
                
                # Get strategy info
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                return strategy
                
        except Exception as e:
            logger.error(f"Error getting project allocation strategy: {str(e)}")
            return None

    async def check_verifier_has_active_batch(self, project_code: str, user_id: int) -> Optional[Dict[str, Any]]:
        """Check if verifier already has an active batch assigned and determine verification status."""
        try:
            async with get_project_db_session(project_code) as session:
                logger.info(f"Checking active batch for verifier {user_id} in project {project_code}")
                
                # Check project_users table for current_batch - this is the authoritative source for active assignments
                from post_db.allocation_models.project_users import ProjectUsers
                from sqlalchemy import select
                
                project_users_result = await session.execute(
                    select(ProjectUsers.current_batch)
                    .where(ProjectUsers.user_id == user_id)
                )
                project_user_data = project_users_result.fetchone()
                
                if not project_user_data or not project_user_data[0]:
                    logger.info(f"No active batch found in project_users for verifier {user_id}")
                    return None
                
                batch_id = project_user_data[0]
                logger.info(f"Found current_batch in project_users: {batch_id}")
                
                # Get batch details and check if ALL files in the batch have verifier_review
                query = text("""
                    SELECT ab.id, ab.batch_identifier, ab.annotation_count, ab.completion_count,
                           ab.total_files,
                           CASE 
                               WHEN (
                                   SELECT COUNT(*) FROM file_allocations fa 
                                   WHERE fa.batch_id = ab.id 
                                   AND fa.verifier_review IS NOT NULL
                               ) = ab.total_files
                               THEN 1 
                               ELSE 0 
                           END as verification_completed,
                           (
                               SELECT COUNT(*) FROM file_allocations fa 
                               WHERE fa.batch_id = ab.id 
                               AND fa.verifier_review IS NOT NULL
                           ) as verified_files_count
                    FROM allocation_batches ab
                    WHERE ab.id = :batch_id
                """)
                result = await session.execute(query, {"batch_id": batch_id})
                row = result.fetchone()
                
                if row:
                    logger.info(f"Found active batch: {row[0]} ({row[1]}) - {row[6]} of {row[4]} files verified")
                    return {
                        "batch_id": row[0],
                        "batch_identifier": row[1],
                        "annotation_count": row[2],
                        "completion_count": row[3],
                        "total_files": row[4],
                        "verification_completed": bool(row[5]),
                        "verified_files_count": row[6]
                    }
                else:
                    logger.warning(f"Batch {batch_id} found in project_users but not in allocation_batches")
                    return None
        except Exception as e:
            logger.error(f"Error checking verifier active batch: {str(e)}")
            return None

    async def find_available_batch_for_verification(self, project_code: str, verifier_id: int) -> Optional[Dict[str, Any]]:
        """
        Find the next available batch for verification.
        Checks completion counts in both allocation_batches and file_allocations tables,
        and ensures the verifier hasn't already completed this batch.
        """
        try:
            async with get_project_db_session(project_code) as session:
                
                completed_batches_result = await session.execute(
                    select(ProjectUsers.completed_batches)
                    .where(ProjectUsers.user_id == verifier_id)
                )
                completed_batches_data = completed_batches_result.fetchone()
                completed_batches = completed_batches_data[0] if completed_batches_data and completed_batches_data[0] else []
                
                logger.info(f"Verifier {verifier_id} completed batches: {completed_batches}")
                
                # Find batches that are completed but not yet fully verified
                # Check completion in both allocation_batches and file_allocations tables
                query = text("""
                    SELECT ab.id, ab.batch_identifier, ab.annotation_count, ab.completion_count, ab.total_files
                    FROM allocation_batches ab
                    WHERE ab.completion_count = ab.annotation_count 
                    AND ab.completion_count > 0
                    AND (
                        SELECT COUNT(*) FROM file_allocations fa 
                        WHERE fa.batch_id = ab.id 
                        AND fa.verifier_review IS NOT NULL
                    ) < ab.total_files
                    AND ab.id NOT IN (
                        SELECT pu.current_batch FROM project_users pu 
                        WHERE pu.current_batch = ab.id 
                        AND pu.role = 'verifier'
                        AND pu.current_batch IS NOT NULL
                    )
                    ORDER BY ab.created_at ASC
                    LIMIT 10
                """)
                result = await session.execute(query)
                rows = result.fetchall()
                
                # Filter out batches that the verifier has already completed
                for row in rows:
                    batch_id = row[0]
                    if batch_id not in completed_batches:
                        logger.info(f"Found available batch for verification: {batch_id} ({row[1]})")
                        return {
                            "batch_id": batch_id,
                            "batch_identifier": row[1],
                            "annotation_count": row[2],
                            "completion_count": row[3],
                            "total_files": row[4]
                        }
                    else:
                        logger.info(f"Skipping batch {batch_id} ({row[1]}) - already completed by verifier")
                
                logger.info("No available batches found for verification")
                return None
        except Exception as e:
            logger.error(f"Error finding available batch for verification: {str(e)}")
            return None

    async def assign_batch_to_verifier(self, project_code: str, batch_id: int, verifier_id: int) -> bool:
        """
        Assign a batch to a verifier by updating allocation_batches, file_allocations, 
        project_users, and creating user_allocations record.
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Get verifier username from master database
                async with get_master_db_context() as master_session:
                    result = await master_session.execute(
                        select(users).where(users.id == verifier_id)
                    )
                    user_obj = result.scalar_one_or_none()
                    if not user_obj:
                        logger.error(f"Verifier {verifier_id} not found in master database")
                        return False
                    verifier_username = user_obj.username
                
                # Update allocation_batches table - set verifier column
                batch_update_query = text("""
                    UPDATE allocation_batches 
                    SET verifier = :verifier_id 
                    WHERE id = :batch_id
                """)
                await session.execute(batch_update_query, {
                    "verifier_id": verifier_id,
                    "batch_id": batch_id
                })
                
                # Update file_allocations table - set verifier column for all files in the batch
                file_update_query = text("""
                    UPDATE file_allocations 
                    SET verifier = :verifier_id 
                    WHERE batch_id = :batch_id
                """)
                await session.execute(file_update_query, {
                    "verifier_id": verifier_id,
                    "batch_id": batch_id
                })
                
                # Update project_users table - set current_batch for the verifier
                # Use SQLAlchemy ORM like annotator assignment does
                from post_db.allocation_models.project_users import ProjectUsers
                from sqlalchemy import update
                
                project_users_result = await session.execute(
                    update(ProjectUsers)
                    .where(ProjectUsers.user_id == verifier_id)
                    .values(current_batch=batch_id)
                )
                logger.info(f"Updated project_users for verifier {verifier_id}, rows affected: {project_users_result.rowcount}")
                
                # If no rows were affected, the user might not exist in project_users table
                if project_users_result.rowcount == 0:
                    logger.warning(f"No project_users record found for verifier {verifier_id}, this might cause issues")
                
                # Create user_allocations record for the verifier
                user_allocations_insert_query = text("""
                    INSERT INTO user_allocations (
                        user_id, batch_id, username, files_completed, total_files, 
                        allocation_role, is_active, allocated_at
                    ) VALUES (
                        :user_id, :batch_id, :username, 0, 
                        (SELECT total_files FROM allocation_batches WHERE id = :batch_id),
                        'verifier', true, NOW()
                    )
                """)
                await session.execute(user_allocations_insert_query, {
                    "user_id": verifier_id,
                    "batch_id": batch_id,
                    "username": verifier_username
                })
                
                await session.commit()
                logger.info(f"Successfully assigned batch {batch_id} to verifier {verifier_id} with user_allocations record")
                return True
                
        except Exception as e:
            logger.error(f"Error assigning batch to verifier: {str(e)}")
            return False

    async def get_batch_assignment_status(self, project_code: str) -> Dict[str, Any]:
        """
        Get the overall status of batch assignments for display messages.
        Returns information about completed batches, assigned batches, etc.
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Count different categories of batches with improved verification completion detection
                status_query = text("""
                    SELECT 
                        COUNT(*) as total_batches,
                        COUNT(CASE WHEN completion_count = annotation_count AND completion_count > 0 THEN 1 END) as completed_batches,
                        COUNT(CASE WHEN completion_count < annotation_count THEN 1 END) as incomplete_batches,
                        COUNT(CASE WHEN 
                            completion_count = annotation_count 
                            AND completion_count > 0 
                            AND (
                                SELECT COUNT(*) FROM file_allocations fa 
                                WHERE fa.batch_id = ab.id 
                                AND fa.verifier_review IS NOT NULL
                            ) = ab.total_files
                        THEN 1 END) as fully_verified_batches,
                        COUNT(CASE WHEN 
                            completion_count = annotation_count 
                            AND completion_count > 0 
                            AND (
                                SELECT COUNT(*) FROM file_allocations fa 
                                WHERE fa.batch_id = ab.id 
                                AND fa.verifier_review IS NOT NULL
                            ) < ab.total_files
                        THEN 1 END) as available_for_verification
                    FROM allocation_batches ab
                """)
                result = await session.execute(status_query)
                row = result.fetchone()
                
                if row:
                    return {
                        "total_batches": row[0],
                        "completed_batches": row[1],
                        "incomplete_batches": row[2],
                        "assigned_to_verifier": row[3],  # fully_verified_batches
                        "available_for_verification": row[4]
                    }
                return {
                    "total_batches": 0,
                    "completed_batches": 0,
                    "incomplete_batches": 0,
                    "assigned_to_verifier": 0,
                    "available_for_verification": 0
                }
        except Exception as e:
            logger.error(f"Error getting batch assignment status: {str(e)}")
            return {
                "total_batches": 0,
                "completed_batches": 0,
                "incomplete_batches": 0,
                "assigned_to_verifier": 0,
                "available_for_verification": 0
            }

    async def assign_verifier_to_next_batch(self, user_id: int) -> Dict[str, Any]:
        """
        Main method called when a verifier clicks "Start Verifying" button.
        
        Logic:
        1. Check if verifier already has an active batch
        2. Get project allocation strategy
        3. Handle based on strategy type (sequential vs parallel)
        4. For sequential: find next available completed batch and assign
        5. For parallel: return placeholder message
        """
        try:
            logger.info(f"Starting batch assignment for verifier {user_id}")
            
            # 1. Get user's active project
            project_code = await self.get_user_active_project(user_id)
            logger.info(f"Verifier's active project: {project_code}")
            if not project_code:
                logger.error(f"No active project for verifier {user_id}")
                return {
                    "success": False,
                    "error": "Verifier has no active project assigned",
                    "error_code": "NO_ACTIVE_PROJECT"
                }
            
            # 2. Get project allocation strategy
            strategy = await self.get_project_allocation_strategy(project_code)
            logger.info(f"Project strategy: {strategy.strategy_type if strategy else 'None'}")
            if not strategy:
                logger.error(f"No allocation strategy found for project {project_code}")
                return {
                    "success": False,
                    "error": "No allocation strategy configured for this project",
                    "error_code": "NO_STRATEGY"
                }
            
            # 3. Handle parallel strategy
            if strategy.strategy_type == StrategyType.PARALLEL:
                logger.info(f"Processing parallel verification assignment")
                return await self.assign_parallel_verifier_batch(user_id, project_code, strategy)
            
            # 4. Check if verifier already has a current batch (for sequential)
            current_batch = await self.check_verifier_has_active_batch(project_code, user_id)
            logger.info(f"Verifier's current batch: {current_batch}")
            if current_batch:
                if current_batch.get("verification_completed", False):
                    # Verification is already completed, complete the batch and find new batch
                    logger.info(f"Batch {current_batch['batch_id']} verification already completed, completing batch")
                    completion_success = await self.complete_verifier_batch(project_code, current_batch['batch_id'], user_id)
                    if completion_success:
                        logger.info(f"Successfully completed batch {current_batch['batch_id']}, will find new batch")
                    else:
                        logger.warning(f"Failed to complete batch {current_batch['batch_id']}, but will continue to find new batch")
                    # Continue to find a new batch below
                else:
                    # Verification is in progress, return continue state
                    return {
                        "success": True,
                        "message": "Verifier already has an active batch",
                        "batch": current_batch,
                        "already_assigned": True,
                        "strategy_type": "sequential"
                    }
            
            # 5. Get batch assignment status (for sequential)
            status = await self.get_batch_assignment_status(project_code)
            logger.info(f"Batch status: {status}")
            
            # 6. Check different scenarios and return appropriate messages
            if status["completed_batches"] == 0:
                return {
                    "success": False,
                    "error": "No batches have been completed yet",
                    "error_code": "NO_COMPLETED_BATCHES",
                    "status": status,
                    "strategy_type": "sequential"
                }
            
            if status["available_for_verification"] == 0:
                if status["assigned_to_verifier"] > 0:
                    return {
                        "success": False,
                        "error": "All batches have been assigned. Wait for admin to assign another project.",
                        "error_code": "ALL_BATCHES_ASSIGNED",
                        "status": status,
                        "strategy_type": "sequential"
                    }
                else:
                    return {
                        "success": False,
                        "error": "No batches are available for verification",
                        "error_code": "NO_AVAILABLE_BATCHES",
                        "status": status,
                        "strategy_type": "sequential"
                    }
            
            # 7. Find and assign the next available batch
            available_batch = await self.find_available_batch_for_verification(project_code, user_id)
            if not available_batch:
                return {
                    "success": False,
                    "error": "No available batches found for verification",
                    "error_code": "NO_AVAILABLE_BATCHES",
                    "strategy_type": "sequential"
                }
            
            # 8. Assign the batch to the verifier
            assignment_success = await self.assign_batch_to_verifier(
                project_code, 
                available_batch["batch_id"], 
                user_id
            )
            
            if assignment_success:
                logger.info(f"Successfully assigned batch {available_batch['batch_id']} to verifier {user_id}")
                return {
                    "success": True,
                    "message": f"Successfully assigned batch {available_batch['batch_identifier']} for verification",
                    "batch": available_batch,
                    "newly_assigned": True,
                    "strategy_type": "sequential"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to assign batch to verifier",
                    "error_code": "ASSIGNMENT_FAILED",
                    "strategy_type": "sequential"
                }
                
        except Exception as e:
            logger.error(f"Error in verifier batch assignment: {str(e)}")
            return {
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "error_code": "INTERNAL_ERROR"
            }

    async def complete_verifier_batch(self, project_code: str, batch_id: int, verifier_id: int) -> bool:
        """
        Mark a verifier's batch as completed and update related records.
        """
        try:
            logger.info(f"Starting completion for verifier {verifier_id}, batch {batch_id}, project {project_code}")
            
            async with get_project_db_session(project_code) as session:
                # First check if all files in the batch have been verified
                verification_check_query = text("""
                    SELECT 
                        ab.total_files,
                        COUNT(fa.verifier_review) as verified_files
                    FROM allocation_batches ab
                    LEFT JOIN file_allocations fa ON ab.id = fa.batch_id AND fa.verifier_review IS NOT NULL
                    WHERE ab.id = :batch_id
                    GROUP BY ab.total_files
                """)
                verification_result = await session.execute(verification_check_query, {"batch_id": batch_id})
                verification_row = verification_result.fetchone()
                
                if not verification_row or verification_row[0] != verification_row[1]:
                    logger.warning(f"Batch {batch_id} is not fully verified yet. Total files: {verification_row[0] if verification_row else 0}, Verified files: {verification_row[1] if verification_row else 0}")
                    return False
                
                logger.info(f"Batch {batch_id} is fully verified ({verification_row[1]} of {verification_row[0]} files)")
                
                # Update user_allocations - mark as completed
                logger.info(f"Updating user_allocations for verifier {verifier_id}, batch {batch_id}")
                user_allocations_update_query = text("""
                    UPDATE user_allocations 
                    SET completed_at = NOW(), is_active = false
                    WHERE batch_id = :batch_id AND user_id = :verifier_id AND allocation_role = 'verifier'
                """)
                user_alloc_result = await session.execute(user_allocations_update_query, {
                    "batch_id": batch_id,
                    "verifier_id": verifier_id
                })
                logger.info(f"User allocations update affected {user_alloc_result.rowcount} rows")
                
                # Update project_users - clear current_batch and add to completed_batches
        
                
                logger.info(f"Fetching current completed_batches for verifier {verifier_id}")
                # First get current completed_batches array for this user
                current_user_result = await session.execute(
                    select(ProjectUsers.completed_batches)
                    .where(ProjectUsers.user_id == verifier_id)
                )
                current_user_data = current_user_result.fetchone()
                logger.info(f"Current user data: {current_user_data}")
                
                if current_user_data:
                    completed_batches = current_user_data[0] or []  # Handle None case
                    logger.info(f"Current completed_batches: {completed_batches}")
                    
                    # Only add batch_id if it's not already in completed_batches (prevent duplicates)
                    if batch_id not in completed_batches:
                        completed_batches.append(batch_id)
                        logger.info(f"Added batch {batch_id} to completed_batches: {completed_batches}")
                    else:
                        logger.info(f"Batch {batch_id} already in completed_batches: {completed_batches}, skipping duplicate")
                    
                    # Clear current_batch and update completed_batches
                    logger.info(f"Updating project_users: clearing current_batch and setting completed_batches to {completed_batches}")
                    project_users_result = await session.execute(
                        update(ProjectUsers)
                        .where(ProjectUsers.user_id == verifier_id)
                        .values(
                            current_batch=None,
                            completed_batches=completed_batches
                        )
                    )
                    logger.info(f"Updated project_users for verifier {verifier_id}: cleared current_batch, added batch {batch_id} to completed_batches, rows affected: {project_users_result.rowcount}")
                else:
                    logger.warning(f"No project_users record found for verifier {verifier_id} during completion")
                
                # Keep verifier assignment in allocation_batches and file_allocations for audit trail
                # The verifier column should remain to show who verified the batch
                logger.info(f"Keeping verifier {verifier_id} assignment in batch {batch_id} for audit trail")
               
                await session.commit()
                logger.info(f"Successfully completed batch {batch_id} for verifier {verifier_id}")
                
                # Sync project progress in master database immediately after completion
                try:                    
                    sync_service = BatchAllocationSyncService()
                    async with get_master_db_context() as master_session:
                        # Get project ID from project code
                        project_result = await master_session.execute(
                            select(ProjectsRegistry.id).where(ProjectsRegistry.project_code == project_code)
                        )
                        project_id = project_result.scalar_one_or_none()
                        
                        if project_id:
                            await sync_service.sync_project_progress(project_id, master_session)
                            logger.info(f"Synced project progress for {project_code} after verification completion")
                            
                            # Check if project should be auto-completed
                            await self._check_and_complete_project(project_id, project_code, master_session)
                        else:
                            logger.warning(f"Could not find project ID for {project_code}")
                            
                except Exception as sync_error:
                    logger.error(f"Failed to sync project progress after verification completion: {sync_error}")
                    # Don't fail the completion if sync fails
                
                return True
                
        except Exception as e:
            logger.error(f"Error completing verifier batch: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    async def remove_verifier_assignment(self, project_code: str, batch_id: int, verifier_id: int) -> bool:
        """
        Remove a verifier's assignment from a batch and update related records.
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Update user_allocations - mark as inactive
                user_allocations_update_query = text("""
                    UPDATE user_allocations 
                    SET is_active = false
                    WHERE batch_id = :batch_id AND user_id = :verifier_id AND allocation_role = 'verifier'
                """)
                await session.execute(user_allocations_update_query, {
                    "batch_id": batch_id,
                    "verifier_id": verifier_id
                })
                
                # Update project_users - clear current_batch
                project_users_update_query = text("""
                    UPDATE project_users 
                    SET current_batch = NULL
                    WHERE user_id = :verifier_id AND role = 'verifier'
                """)
                await session.execute(project_users_update_query, {
                    "verifier_id": verifier_id
                })
                
                # Clear verifier from allocation_batches
                batch_clear_query = text("""
                    UPDATE allocation_batches 
                    SET verifier = NULL 
                    WHERE id = :batch_id
                """)
                await session.execute(batch_clear_query, {"batch_id": batch_id})
                
                # Clear verifier from file_allocations
                file_clear_query = text("""
                    UPDATE file_allocations 
                    SET verifier = NULL 
                    WHERE batch_id = :batch_id
                """)
                await session.execute(file_clear_query, {"batch_id": batch_id})
                
                await session.commit()
                logger.info(f"Successfully removed verifier {verifier_id} assignment from batch {batch_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error removing verifier assignment: {str(e)}")
            return False

    async def clear_completed_verifier_assignment(self, project_code: str, batch_id: int, verifier_id: int) -> bool:
        """
        Clear a verifier's assignment from a completed batch.
        This is used when verification is already completed but verifier column is still set.
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Clear verifier from allocation_batches
                batch_clear_query = text("""
                    UPDATE allocation_batches 
                    SET verifier = NULL 
                    WHERE id = :batch_id AND verifier = :verifier_id
                """)
                await session.execute(batch_clear_query, {
                    "batch_id": batch_id,
                    "verifier_id": verifier_id
                })
                
                # Update user_allocations - mark as completed if not already
                user_allocations_update_query = text("""
                    UPDATE user_allocations 
                    SET completed_at = COALESCE(completed_at, NOW()), is_active = false
                    WHERE batch_id = :batch_id AND user_id = :verifier_id AND allocation_role = 'verifier'
                """)
                await session.execute(user_allocations_update_query, {
                    "batch_id": batch_id,
                    "verifier_id": verifier_id
                })
                
                await session.commit()
                logger.info(f"Successfully cleared completed verification assignment for batch {batch_id}, verifier {verifier_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error clearing completed verifier assignment: {str(e)}")
            return False

    async def assign_parallel_verifier_batch(self, user_id: int, project_code: str, strategy) -> Dict[str, Any]:
        """
        Assign verifier to the next available completed batch for parallel strategy.
        
        Logic:
        1. Check if verifier already has an active batch
        2. Get all batches ordered by ID
        3. For each batch, check if completion_count == annotation_count AND verifier = null
        4. If found, assign the batch
        5. Handle different scenarios:
           - No batches with completion_count == annotation_count: wait for annotations
           - All completed batches already have verifiers: all batches assigned
        """
        try:
            logger.info(f"Starting parallel verification assignment for verifier {user_id}")
            
            # 1. Check if verifier already has a current batch
            current_batch = await self.check_verifier_has_active_batch(project_code, user_id)
            logger.info(f"Verifier's current batch: {current_batch}")
            if current_batch:
                if current_batch.get("verification_completed", False):
                    # Verification is already completed, complete the batch and find new batch
                    logger.info(f"Batch {current_batch['batch_id']} verification already completed, completing batch")
                    completion_success = await self.complete_verifier_batch(project_code, current_batch['batch_id'], user_id)
                    if completion_success:
                        logger.info(f"Successfully completed batch {current_batch['batch_id']}, will find new batch")
                    else:
                        logger.warning(f"Failed to complete batch {current_batch['batch_id']}, but will continue to find new batch")
                    # Continue to find a new batch below
                else:
                    # Verification is in progress, return continue state
                    return {
                        "success": True,
                        "message": "Continue verifying your assigned batch",
                        "batch": current_batch,
                        "continue_verification": True,
                        "strategy_type": "parallel"
                    }
            
            # 2. Get verifier's completed batches and all batch data
            async with get_project_db_session(project_code) as session:
                
                completed_batches_result = await session.execute(
                    select(ProjectUsers.completed_batches)
                    .where(ProjectUsers.user_id == user_id)
                )
                completed_batches_data = completed_batches_result.fetchone()
                completed_batches = completed_batches_data[0] if completed_batches_data and completed_batches_data[0] else []
                
                logger.info(f"Verifier {user_id} completed batches: {completed_batches}")
                
                # Get all batches with their completion and assignment status
                # Check completion in both allocation_batches and file_allocations tables
                batches_query = text("""
                    SELECT ab.id, ab.batch_identifier, ab.total_files, ab.completion_count, ab.annotation_count, ab.verifier,
                           (
                               SELECT COUNT(*) FROM file_allocations fa 
                               WHERE fa.batch_id = ab.id 
                               AND fa.verifier_review IS NOT NULL
                           ) as verified_files_count,
                           CASE 
                               WHEN (
                                   SELECT COUNT(*) FROM file_allocations fa 
                                   WHERE fa.batch_id = ab.id 
                                   AND fa.verifier_review IS NOT NULL
                               ) = ab.total_files
                               THEN 1 
                               ELSE 0 
                           END as verification_completed
                    FROM allocation_batches ab
                    ORDER BY ab.id ASC
                """)
                batches_result = await session.execute(batches_query)
                all_batches = batches_result.fetchall()
                
                if not all_batches:
                    return {
                        "success": False,
                        "error": "No batches found in project",
                        "error_code": "NO_BATCHES_FOUND",
                        "strategy_type": "parallel"
                    }
                
                logger.info(f"Found {len(all_batches)} total batches")
                
                # 3. Analyze batch completion status
                annotated_batches = []
                available_for_verification = []
                assigned_to_verifier = []
                
                for batch in all_batches:
                    batch_dict = {
                        'id': batch[0],
                        'batch_identifier': batch[1],
                        'total_files': batch[2],
                        'completion_count': batch[3],
                        'annotation_count': batch[4],
                        'verifier': batch[5],
                        'verified_files_count': batch[6],
                        'verification_completed': batch[7]
                    }
                    
                    logger.info(f"Batch {batch_dict['id']} ({batch_dict['batch_identifier']}): "
                              f"batch_completion={batch_dict['completion_count']}, "
                              f"annotation_count={batch_dict['annotation_count']}, "
                              f"verified_files={batch_dict['verified_files_count']}/{batch_dict['total_files']}, "
                              f"verification_completed={batch_dict['verification_completed']}")
                    
                    # Check if batch is fully annotated
                    batch_fully_annotated = (batch_dict['completion_count'] == batch_dict['annotation_count'])
                    
                    if batch_fully_annotated:
                        annotated_batches.append(batch_dict)
                        
                        # Check if available for verification (not completed and not already completed by this verifier)
                        if (batch_dict['verification_completed'] == 0 and 
                            batch_dict['id'] not in completed_batches):
                            available_for_verification.append(batch_dict)
                        else:
                            assigned_to_verifier.append(batch_dict)
                
                logger.info(f"Batch analysis: {len(annotated_batches)} annotated, "
                          f"{len(available_for_verification)} available for verification, "
                          f"{len(assigned_to_verifier)} already assigned")
                
                # 4. Handle different scenarios
                if len(annotated_batches) == 0:
                    # No batches have been fully annotated yet
                    return {
                        "success": False,
                        "error": "No batches have been completed yet. Please wait for annotations to be finished.",
                        "error_code": "NO_COMPLETED_BATCHES",
                        "status": {
                            "total_batches": len(all_batches),
                            "completed_batches": len(annotated_batches),
                            "available_for_verification": len(available_for_verification),
                            "assigned_to_verifier": len(assigned_to_verifier)
                        },
                        "strategy_type": "parallel"
                    }
                
                if len(available_for_verification) == 0:
                    # All completed batches have been assigned to verifiers
                    return {
                        "success": False,
                        "error": "All batches have been assigned. Wait for admin to assign another project.",
                        "error_code": "ALL_BATCHES_ASSIGNED",
                        "status": {
                            "total_batches": len(all_batches),
                            "completed_batches": len(completed_batches),
                            "available_for_verification": len(available_for_verification),
                            "assigned_to_verifier": len(assigned_to_verifier)
                        },
                        "strategy_type": "parallel"
                    }
                
                # 5. Assign the first available batch (ordered by ID)
                batch_to_assign = available_for_verification[0]
                logger.info(f"Assigning batch {batch_to_assign['id']} ({batch_to_assign['batch_identifier']}) to verifier {user_id}")
                
                # Assign the verifier to the batch
                assignment_success = await self.assign_batch_to_verifier(
                    project_code, 
                    batch_to_assign['id'], 
                    user_id
                )
                
                if assignment_success:
                    # Get the complete batch details for return
                    batch_details = await self.get_batch_details_for_verification(
                        project_code, 
                        batch_to_assign['id']
                    )
                    
                    return {
                        "success": True,
                        "message": f"Successfully assigned to batch {batch_to_assign['batch_identifier']} for verification",
                        "batch": batch_details,
                        "assigned_batch_id": batch_to_assign['id'],
                        "strategy_type": "parallel"
                    }
                else:
                    return {
                        "success": False,
                        "error": "Failed to assign batch due to database error",
                        "error_code": "ASSIGNMENT_FAILED",
                        "strategy_type": "parallel"
                    }
                    
                
        except Exception as e:
            logger.error(f"Error in parallel verification assignment: {str(e)}")
            return {
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "error_code": "INTERNAL_ERROR",
                "strategy_type": "parallel"
            }

    async def get_batch_details_for_verification(self, project_code: str, batch_id: int) -> Optional[Dict[str, Any]]:
        """
        Get complete batch details with files for verification interface.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            
        Returns:
            Optional[Dict]: Batch details with files or None if not found
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Get batch details
                batch_query = text("""
                    SELECT 
                        id, 
                        batch_identifier, 
                        batch_status,
                        total_files, 
                        assignment_count,
                        annotation_count,
                        completion_count,
                        verifier,
                        created_at
                    FROM allocation_batches 
                    WHERE id = :batch_id
                """)
                batch_result = await session.execute(batch_query, {"batch_id": batch_id})
                batch_row = batch_result.fetchone()
                
                if not batch_row:
                    logger.warning(f"Batch {batch_id} not found in project {project_code}")
                    return None
                
                # Get files in the batch
                files_query = text("""
                    SELECT 
                        id,
                        original_filename,
                        file_identifier,
                        file_type,
                        file_size_bytes,
                        storage_location
                    FROM files_registry
                    WHERE batch_id = :batch_id
                    ORDER BY id
                """)
                files_result = await session.execute(files_query, {"batch_id": batch_id})
                files_rows = files_result.fetchall()
                
                # Format batch details
                batch_details = {
                    "batch_id": batch_row[0],
                    "batch_identifier": batch_row[1],
                    "batch_status": batch_row[2],
                    "total_files": batch_row[3],
                    "assignment_count": batch_row[4],
                    "annotation_count": batch_row[5],
                    "completion_count": batch_row[6],
                    "verifier": batch_row[7],
                    "created_at": batch_row[8],
                    "files": [
                        {
                            "id": file_row[0],
                            "filename": file_row[1],
                            "file_identifier": file_row[2],
                            "file_type": file_row[3],
                            "file_size": file_row[4],
                            "storage_location": file_row[5]
                        }
                        for file_row in files_rows
                    ]
                }
                
                logger.info(f"Retrieved batch details for verification: batch {batch_id} with {len(files_rows)} files")
                return batch_details
                
                
        except Exception as e:
            logger.error(f"Error getting batch details for verification: {str(e)}")
            return None

    async def _check_and_complete_project(self, project_id: int, project_code: str, master_session) -> None:
        """
        Check if project should be auto-completed and complete it if necessary.
        A project is auto-completed when:
        1. All files are completed (completed_files == total_files)
        2. For verification projects: all files have verifier_review
        3. For non-verification projects: all batches are completed
        """
        try:
            
            # Get project and strategy details
            project_result = await master_session.execute(
                select(ProjectsRegistry, AllocationStrategies)
                .join(AllocationStrategies, ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id)
                .where(ProjectsRegistry.id == project_id)
            )
            project_data = project_result.fetchone()
                
            project, strategy = project_data
            
            # Check if all work is done
            is_complete = False
            
            if strategy.requires_verification:
                # For verification projects: check if all files have verifier_review
                async with get_project_db_session(project_code) as session:
                    verification_check = await session.execute(text("""
                        SELECT 
                            COUNT(*) as total_files,
                            COUNT(CASE WHEN fa.verifier_review IS NOT NULL THEN 1 END) as verified_files
                        FROM file_allocations fa
                    """))
                    verification_result = verification_check.fetchone()
                    
                    if verification_result and verification_result[0] > 0:
                        is_complete = verification_result[0] == verification_result[1]
                        logger.info(f"Verification project {project_code}: {verification_result[1]}/{verification_result[0]} files verified")
            else:
                # For non-verification projects: check if completed_files == total_files
                is_complete = (project.completed_files >= project.total_files and project.total_files > 0)
                logger.info(f"Non-verification project {project_code}: {project.completed_files}/{project.total_files} files completed")
            
            if is_complete:
                # Auto-complete the project
                await master_session.execute(
                    update(ProjectsRegistry)
                    .where(ProjectsRegistry.id == project_id)
                    .values(project_status="completed")
                )
                
                # Clear active_project for all users in this project
                from post_db.master_models.users import users
                clear_result = await master_session.execute(
                    update(users)
                    .where(users.active_project == project_code)
                    .values(active_project=None)
                )
                
                await master_session.commit()
                
                logger.info(f"🎉 AUTO-COMPLETED PROJECT: {project_code} - All work finished! Cleared active_project for {clear_result.rowcount} users.")
            else:
                logger.info(f"Project {project_code} not ready for auto-completion yet")
                
        except Exception as e:
            logger.error(f"Error in auto-completion check for project {project_code}: {str(e)}")
            # Don't fail the main operation if auto-completion fails
