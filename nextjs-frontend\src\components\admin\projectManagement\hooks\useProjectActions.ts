import { useState } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { ProjectRegistryResponse } from '../types';
import { API_BASE_URL } from "../../../../lib/api";

export const useProjectActions = () => {
  const [activationLoading, setActivationLoading] = useState(false);
  const [deadlineLoading, setDeadlineLoading] = useState(false);

  // Activate project
  const activateProject = async (
    project: ProjectRegistryResponse,
    createBatches: boolean = false,
    onSuccess?: (updatedProject: ProjectRegistryResponse) => void
  ) => {
    try {
      setActivationLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ create_batches: createBatches }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to activate project");
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success("Project activated successfully");
        const updatedProject = { ...project, project_status: 'active' };
        onSuccess?.(updatedProject);
        return updatedProject;
      } else {
        showToast.warning(`Project activation failed: ${data.message}`);
        return null;
      }
    } catch (error) {
      console.error("Error activating project:", error);
      showToast.error(`Failed to activate project: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setActivationLoading(false);
    }
  };

  // Deactivate project
  const deactivateProject = async (
    project: ProjectRegistryResponse,
    onSuccess?: (updatedProject: ProjectRegistryResponse) => void
  ) => {
    try {
      setActivationLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/deactivate`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to deactivate project");
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success("Project deactivated successfully");
        const updatedProject = { ...project, project_status: 'inactive' };
        onSuccess?.(updatedProject);
        return updatedProject;
      } else {
        showToast.warning(`Project deactivation failed: ${data.message}`);
        return null;
      }
    } catch (error) {
      console.error("Error deactivating project:", error);
      showToast.error(`Failed to deactivate project: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setActivationLoading(false);
    }
  };

  // Set project deadline
  const setProjectDeadline = async (
    project: ProjectRegistryResponse,
    deadline: string,
    onSuccess?: (updatedProject: ProjectRegistryResponse) => void
  ) => {
    if (!deadline) return null;
    
    try {
      setDeadlineLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/deadline`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deadline }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to set project deadline");
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success("Project deadline set successfully");
        const updatedProject = { ...project, project_deadline: deadline };
        onSuccess?.(updatedProject);
        return updatedProject;
      } else {
        showToast.warning(`Failed to set deadline: ${data.message}`);
        return null;
      }
    } catch (error) {
      console.error("Error setting project deadline:", error);
      showToast.error(`Failed to set deadline: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setDeadlineLoading(false);
    }
  };

  // Pause project
  const pauseProject = async (
    project: ProjectRegistryResponse,
    onSuccess?: (updatedProject: ProjectRegistryResponse) => void
  ) => {
    try {
      setActivationLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/pause`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to pause project");
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success("Project paused successfully");
        const updatedProject = { ...project, project_status: 'paused' };
        onSuccess?.(updatedProject);
        return updatedProject;
      } else {
        showToast.warning(`Project pause failed: ${data.message}`);
        return null;
      }
    } catch (error) {
      console.error("Error pausing project:", error);
      showToast.error(`Failed to pause project: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setActivationLoading(false);
    }
  };

  // Complete project
  const completeProject = async (
    project: ProjectRegistryResponse,
    onSuccess?: (updatedProject: ProjectRegistryResponse) => void
  ) => {
    try {
      setActivationLoading(true);
      
      const response = await authFetch(`${API_BASE_URL}/projects/${project.id}/complete`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to complete project");
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast.success("Project completed successfully");
        const updatedProject = { ...project, project_status: 'completed' };
        onSuccess?.(updatedProject);
        return updatedProject;
      } else {
        showToast.warning(`Project completion failed: ${data.message}`);
        return null;
      }
    } catch (error) {
      console.error("Error completing project:", error);
      showToast.error(`Failed to complete project: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    } finally {
      setActivationLoading(false);
    }
  };

  return {
    activationLoading,
    deadlineLoading,
    activateProject,
    deactivateProject,
    pauseProject,
    completeProject,
    setProjectDeadline,
  };
};
