// src/app/admin/layout.tsx

"use client";

import { ReactNode, useState, useEffect, useRef, Suspense } from "react";
import Link from "next/link";
import {
  FaBars,
  FaThLarge,
  FaUsers,
  FaCloudDownloadAlt,
  FaRobot,
  FaChartLine,
  FaUserCircle,
  FaSignOutAlt,
  FaKey,
  FaChevronDown,
  FaUserPlus,
  FaTimes,
  FaMicrochip,
  FaBalanceScale,
  FaFolderOpen,
} from "react-icons/fa";
import PasswordModal from "@/components/auth/PasswordModal";
import { useAuth } from "@/contexts/AuthContext";
import RoleGuard from "@/components/auth/RoleGuard";
import { useSearchParams } from "next/navigation";

interface LayoutProps {
  children: ReactNode;
}

const NAV_LINKS = [
  {
    href: "/admin",
    icon: <FaThLarge />,
    label: "Dashboard",
    view: "dashboard",
  },
  {
    href: "/admin?view=userManagement",
    icon: <FaUsers />,
    label: "Manage Users",
    view: "userManagement",
  },
 
  {
    href: "/admin?view=clientOnboarding",
    icon: <FaUserPlus />,
    label: "Client Onboarding",
    view: "clientOnboarding",
  },
  {
    href: "/admin?view=projects",
    icon: <FaFolderOpen />,
    label: "Projects",
    view: "projects",
  },
  
  
  {
    href: "/admin?view=aiModelsRegistry",
    icon: <FaMicrochip />,
    label: "Models View",
    view: "aiModelsRegistry",
  },
  {
    href: "/admin?view=aiProcessing",
    icon: <FaRobot />,
    label: "AI Assisted Labeling",
    view: "aiProcessing",
  },
  {
    href: "/admin?view=dataSources",
    icon: <FaCloudDownloadAlt />,
    label: "Fetch Data",
    view: "dataSources",
  },
  {
    href: "/admin?view=syntheticData",
    icon: <FaRobot />,
    label: "Synthetic Data",
    view: "syntheticData",
  },
  {
    href: "/admin?view=dataDelivery",
    icon: <FaChartLine />,
    label: "Data Delivery",
    view: "dataDelivery",
  },
  {
    href: "/admin?view=allocationStrategy",
    icon: <FaBalanceScale />,
    label: "Allocation Strategy",
    view: "allocationStrategy",
  }
];

// **Inline** component that calls useSearchParams
function SidebarNav({ collapsed }: { collapsed: boolean }) {
  const searchParams = useSearchParams();
  const view = searchParams.get("view") || "dashboard";

  return (
    <>
      {NAV_LINKS.map((link) => (
        <Link
          key={link.view}
          href={link.href}
          className={`
            group flex items-center gap-4 rounded-xl font-medium text-white
            px-4 py-3 mx-1 transition
            ${collapsed ? "justify-center" : ""}
            ${
              view === link.view
                ? "bg-gradient-to-r from-[#004BB5] to-[#3673ea] shadow-lg"
                : "hover:bg-[#004BB5]/80"
            }
            ${collapsed ? "w-12 h-12 my-1" : ""}
          `}
          aria-current={view === link.view ? "page" : undefined}
        >
          <span className="text-xl">{link.icon}</span>
          {!collapsed && (
            <span className="whitespace-nowrap">{link.label}</span>
          )}
        </Link>
      ))}
    </>
  );
}

export default function AdminLayout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const { user, logout } = useAuth();
  const displayName = user?.full_name || user?.username || "";
  const dropdownRef = useRef<HTMLDivElement>(null);

  // close dropdown on outside click
  useEffect(() => {
    function onClick(e: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setDropdownOpen(false);
      }
    }
    if (dropdownOpen) document.addEventListener("mousedown", onClick);
    return () => document.removeEventListener("mousedown", onClick);
  }, [dropdownOpen]);

  // ESC closes menus
  useEffect(() => {
    function onKey(e: KeyboardEvent) {
      if (e.key === "Escape") {
        setSidebarOpen(false);
        setDropdownOpen(false);
      }
    }
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, []);

  const handleLogout = async () => {
    setDropdownOpen(false);
    await logout();
  };

  const sidebarClasses = [
    "fixed z-40 inset-y-0 left-0 flex flex-col bg-[#0052CC] text-white shadow-xl",
    "transition-all duration-300",
    collapsed ? "w-20" : "w-64",
    "h-full md:static md:z-auto md:shadow-none",
    sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
  ].join(" ");

  return (
    <RoleGuard allowedRoles={["admin"]}>
      {/* Mobile header */}
      <header className="fixed md:hidden top-0 left-0 w-full h-14 flex items-center bg-[#0052CC] text-white px-4 shadow z-30">
        <button
          onClick={() => setSidebarOpen(true)}
          className="mr-3 p-2 rounded-full hover:bg-[#004BB5]"
        >
          <FaBars className="text-xl" />
        </button>
        <span className="font-bold text-lg">DADP Admin</span>
      </header>

      {/* Mobile overlay */}
      <div
        className={`fixed inset-0 z-30 bg-black/40 transition-opacity duration-300 md:hidden ${
          sidebarOpen ? "opacity-100 visible" : "opacity-0 invisible"
        }`}
        onClick={() => setSidebarOpen(false)}
      />

      <div className="flex h-screen pt-14 md:pt-0">
        {/* Sidebar */}
        <aside className={sidebarClasses}>
          {/* Logo + toggle */}
          <div className="flex items-center justify-between py-4 px-4 border-b border-[#004BB5]">
            {!collapsed && (
              <Link href="/admin" className="text-2xl font-extrabold">
                DADP
              </Link>
            )}
            <div className="flex gap-2">
              <button
                onClick={() => setCollapsed((c) => !c)}
                className="hidden md:inline-flex p-2 rounded hover:bg-[#004BB5]"
              >
                <FaBars className="text-xl" />
              </button>
              <button
                onClick={() => setSidebarOpen(false)}
                className="md:hidden p-2 rounded hover:bg-[#004BB5]"
              >
                <FaTimes className="text-xl" />
              </button>
            </div>
          </div>

          {/* NAV: Suspense-wrapped */}
          <nav className="flex-1 mt-2 overflow-y-auto px-1 scrollbar-thin scrollbar-thumb-[#004BB5]/50">
            <Suspense
              fallback={<div className="p-4 text-white">Loading menu…</div>}
            >
              <SidebarNav collapsed={collapsed} />
            </Suspense>
          </nav>

          {/* Footer */}
          <div className="mt-auto border-t border-[#004BB5] py-4 px-2">
            <button
              onClick={handleLogout}
              className="flex items-center gap-3 p-3 rounded hover:bg-[#004BB5]/90 w-full"
            >
              <FaSignOutAlt />
              {!collapsed && <span>Logout</span>}
            </button>
            <div ref={dropdownRef} className="relative mt-2">
              <button
                onClick={() => setDropdownOpen((o) => !o)}
                className="flex items-center gap-3 p-3 rounded hover:bg-[#004BB5]/90 w-full"
              >
                <FaUserCircle />
                {!collapsed && (
                  <>
                    <span>{displayName}</span>
                    <FaChevronDown className="ml-auto" />
                  </>
                )}
              </button>
              {dropdownOpen && !collapsed && (
                <div className="absolute left-0 bottom-14 w-48 bg-[#141A29]/90 backdrop-blur-md rounded-xl shadow-lg p-2">
                  <div className="text-white font-semibold text-sm mb-1">
                    {user?.role || "Admin"}
                  </div>
                  <button
                    onClick={() => {
                      setIsPasswordModalOpen(true);
                      setDropdownOpen(false);
                    }}
                    className="flex items-center gap-2 w-full p-2 rounded hover:bg-[#223366]"
                  >
                    <FaKey /> Change Password
                  </button>
                </div>
              )}
            </div>
          </div>
        </aside>

        {/* Main */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-[#f5f8fc] to-[#e9f0fd] max-md:!pt-14">
          <div id="flash-container" className="p-2" />
          <div className="p-4 md:p-6">{children}</div>
        </main>

        <PasswordModal
          isOpen={isPasswordModalOpen}
          onClose={() => setIsPasswordModalOpen(false)}
        />
      </div>

    </RoleGuard>
  );
}