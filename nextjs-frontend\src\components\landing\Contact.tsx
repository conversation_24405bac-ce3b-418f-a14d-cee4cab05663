  // components/Contact.tsx
  'use client';

  import { FaGlobe, FaLinkedin, FaTwitter, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';
  import { motion } from 'framer-motion';

  const socialLinks = [
    { href: 'https://www.processvenue.com/', icon: <FaGlobe />, label: 'Visit Website', color: 'text-blue-600' },
    { href: 'https://www.linkedin.com/showcase/processvenue/about/', icon: <FaLinkedin />, label: 'LinkedIn', color: 'text-[#0077b5]' },
    { href: 'https://twitter.com/processvenue', icon: <FaTwitter />, label: 'Twitter', color: 'text-[#1da1f2]' },
  ];

  const contacts = [
    {
      icon: <FaEnvelope />,
      title: 'Email Us',
      content: <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>,
    },
    {
      icon: <FaPhone />,
      title: 'Call Us',
      content: (
        <div className="space-y-1 text-sm text-gray-700">
          <div><span className="font-semibold">India:</span> <a href="tel:+************" className="text-blue-600 hover:underline">+91 ************</a></div>
          <div><span className="font-semibold">USA:</span> <a href="tel:+14156854332" className="text-blue-600 hover:underline">****** 685 4332</a></div>
          <div><span className="font-semibold">UK:</span> <a href="tel:+************" className="text-blue-600 hover:underline">+44 20 3289 4232</a></div>
        </div>
      ),
    },
    {
      icon: <FaMapMarkerAlt />,
      title: 'Visit Us',
      content: (
        <p className="text-sm text-gray-700 leading-relaxed">
          130, New Sanganer Rd, opp. Metro Station,<br />
          Shiva Colony, Sodala,<br />
          Jaipur, Rajasthan 302019
        </p>
      ),
    },
  ];

  export default function Contact() {
    return (
      <section id="contact" className="py-24 bg-gradient-to-b from-blue-50 to-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-12"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Get in Touch
          </motion.h2>

          <motion.div
            className="flex flex-wrap justify-center gap-4 mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.1 } }
            }}
          >
            {socialLinks.map(({ href, icon, label, color }, i) => (
              <motion.a
                key={i}
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className={`flex items-center gap-2 bg-gray-100 px-5 py-2 rounded-full 
                  shadow-[8px_8px_16px_#c1c8e4,-8px_-8px_16px_#ffffff] 
                  hover:shadow-inner transition-shadow duration-200 no-underline ${color}`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <span className="text-lg">{icon}</span>
                <span className="font-medium">{label}</span>
              </motion.a>
            ))}
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contacts.map(({ icon, title, content }, i) => (
              <motion.div
                key={i}
                className="flex items-start gap-4 bg-gray-100 p-6 rounded-xl 
                  shadow-[8px_8px_16px_#c1c8e4,-8px_-8px_16px_#ffffff] 
                  transition-shadow duration-300"
                whileHover={{ y: -5, boxShadow: '8px 8px 16px #c1c8e4, -8px -8px 16px #ffffff' }}
              >
                <div className="flex-shrink-0 bg-gray-100 p-3 rounded-full 
                  shadow-inner flex items-center justify-center h-12 w-12 text-blue-600">
                  {icon}
                </div>
                <div>
                  <h3 className="text-lg font-bold mb-2 text-gray-800">{title}</h3>
                {content}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
