from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON>olean, DateTime, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..base import Base
from .users import UserRole

class UserMetadata(Base):
    """
    Centralized user registry for all platform users.
    Manages authentication, authorization, skills, and cross-project performance tracking 
    for annotators, reviewers, and administrators.
    """
    __tablename__ = 'user_metadata'

    # Primary Identity
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, 
                    comment='Reference to global user record')
    username = Column(String(255), nullable=False, unique=True, 
                     comment='Unique username for login and user references across system')
    email = Column(String(255), nullable=False, unique=True, 
                  comment='Primary email for authentication and notifications')
    full_name = Column(String(255), 
                      comment='Display name for UI and reporting purposes')
    project_id = Column(Integer, ForeignKey('projects_registry.id', ondelete='CASCADE'), nullable=False, 
                       comment='Reference to specific project')
    
    role = Column(String(50), default=UserRole.ANNOTATOR.value, nullable=False, 
                          comment='Global role (admin, annotator, reviewer, client) determining base permissions')
    batches_completed = Column(Integer, default=0, 
                              comment='Total number of batches completed by the user')
    batches=Column(JSONB, 
                   comment='List of batches completed by the user')
    files_completed = Column(Integer, default=0, 
                            comment='Total number of files completed by the user')
    files=Column(JSONB, 
                 comment='List of files completed by the user')
    quality_score = Column(Numeric(5, 2), 
                          comment='Aggregated quality score across all projects (0-100) for assignment optimization')
    is_active = Column(Boolean, default=True, 
                      comment='Account status for enabling/disabling user access globally')
    
    # Audit Trail
    created_at = Column(DateTime, default=func.current_timestamp(), 
                       comment='Account creation timestamp for user lifecycle tracking')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), 
                       comment='Last profile update timestamp for change monitoring')
    

    def __repr__(self):
        return f"<Users(id={self.id}, username='{self.username}', email='{self.email}', role='{self.role}')>"

User = UserMetadata 