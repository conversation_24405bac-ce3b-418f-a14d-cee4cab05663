import { NextRequest, NextResponse } from "next/server";

import { API_BASE_URL } from "@/lib/api";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const skip = searchParams.get("skip") || "0";
    const limit = searchParams.get("limit") || "50";
    const name_like = searchParams.get("name_like") || "";

    const queryParams = new URLSearchParams({
      skip,
      limit,
      ...(name_like && { name_like }),
    });

    const response = await fetch(
      `${API_BASE_URL}/allocation-strategies?${queryParams}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching allocation strategies:", error);
    return NextResponse.json(
      { error: "Failed to fetch allocation strategies" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}/allocation-strategies`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        { error: errorData.detail || "Failed to create allocation strategy" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("Error creating allocation strategy:", error);
    return NextResponse.json(
      { error: "Failed to create allocation strategy" },
      { status: 500 }
    );
  }
}
