#!/usr/bin/env python3
"""
Quick database connectivity check for DADP testing infrastructure.
Run this before running tests to verify PostgreSQL connections.
"""

import asyncio
import os
import sys
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# Test database URLs
TEST_PROJECT_DB = "postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/test_project_db"
TEST_MASTER_DB = "postgresql+asyncpg://kanwar_raj:dadpdev123@10.10.10.30:5432/test_master_db"

async def check_database_connection(db_url: str, db_name: str) -> bool:
    """Check if database is accessible."""
    try:
        engine = create_async_engine(
            db_url,
            pool_pre_ping=True,
            connect_args={
                "command_timeout": 10,  # 10 second timeout for remote check
                "ssl": "disable",  # ⭐ DISABLE SSL - Match test configuration
                "server_settings": {
                    "application_name": f"dadp_test_check_{db_name}",
                }
            }
        )
        
        print(f"🔍 Checking {db_name} connection...")
        
        async with engine.connect() as conn:
            result = await conn.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            if row and row[0] == 1:
                print(f"✅ {db_name}: Connection successful")
                return True
            else:
                print(f"❌ {db_name}: Unexpected query result")
                return False
                
    except Exception as e:
        print(f"❌ {db_name}: Connection failed - {e}")
        return False
    finally:
        try:
            await engine.dispose()
        except Exception:
            pass

async def main():
    """Check both test databases."""
    print("🚀 DADP Test Database Connectivity Check")
    print("=" * 50)
    
    # Check both databases
    results = await asyncio.gather(
        check_database_connection(TEST_PROJECT_DB, "Test Project DB"),
        check_database_connection(TEST_MASTER_DB, "Test Master DB"),
        return_exceptions=True
    )
    
    # Summary
    print("\n📊 SUMMARY:")
    project_ok = isinstance(results[0], bool) and results[0]
    master_ok = isinstance(results[1], bool) and results[1]
    
    if project_ok and master_ok:
        print("🎉 All databases are accessible! Tests should work.")
        sys.exit(0)
    else:
        print("💥 Database connection issues detected!")
        print("\n🔧 TROUBLESHOOTING:")
        
        if not project_ok:
            print("- test_project_db: Check if database exists and user 'mansi' has access")
        if not master_ok:
            print("- test_master_db: Check if database exists and user 'kanwar_raj' has access")
            
        print("- Verify PostgreSQL server is running at 10.10.10.30:5432")
        print("- Check network connectivity and firewall settings")
        print("- Run: python setup_test_databases.py setup")
        
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
