// steps/DatasetSelection.tsx
import React, { useState, useEffect } from 'react';
import {
  FaDatabase,
  FaArrowRight,
  FaArrowLeft,
  FaSyncAlt,
  FaPenSquare,
  FaCheckSquare,
  FaFolderPlus,
  FaCheckCircle,
  FaFolderOpen,
  FaFile
} from 'react-icons/fa';
import { Client, Dataset, ActiveTab, SelectionTarget, Directory } from '../types';
import { DatasetInfoCard } from '../components/DatasetInfoCard';
import { StorageBrowserModal } from '../components/StorageBrowserModal';
import { ImageBrowser } from '../components/ImageBrowser';
import { useStorageBrowser } from '../hooks/useStorageBrowser';

interface DatasetSelectionProps {
  selectedClient: Client | null;
  activeTab: ActiveTab;
  setActiveTab: (tab: ActiveTab) => void;
  
  // Dataset lists and selection
  manualDatasetList: Dataset[];
  verificationDatasetList: Dataset[];
  selectedManualDatasetId: string | null;
  selectedVerificationDatasetId: string | null;
  onManualDatasetChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onVerificationDatasetChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  
  // Loading and assignment
  loadingDatasets: boolean;
  refreshing: boolean;
  assigning: boolean;
  onRefreshDatasets: () => void;
  onRegisterDataset: (mode: "annotation" | "verification", folderPath: string, filesPerBatch: number|null) => Promise<{ success: boolean }>;
  
  // New dataset creation
  manualFolderPath: string;
  setManualFolderPath: (path: string) => void;
  verificationImageFolderPath: string;
  setVerificationImageFolderPath: (path: string) => void;
  verificationLabelFilePath: string;
  setVerificationLabelFilePath: (path: string) => void;
  filesPerBatch: number | null;
  setFilesPerBatch: (count: number | null) => void;
  creatingAnnotationBatch: boolean;
  creatingVerificationBatch: boolean;
  onSetManualFolder: () => Promise<{ success: boolean; data?: any; project_code?: string }>;
  onSetVerificationFolders: () => void;
  
  // Storage Browser (legacy props - will be replaced by useStorageBrowser hook)
  isNasBrowserOpen?: boolean;
  setIsNasBrowserOpen?: (open: boolean) => void;
  currentSelectionTarget?: SelectionTarget;
  currentBrowsePath?: string;
  currentSelection?: string;
  isSelectingFile?: boolean;
  directoryContents?: Directory[];
  isLoadingDirectory?: boolean;
  onOpenNasBrowser?: (target: SelectionTarget) => void;
  onSelectItem?: (item: Directory) => void;
  onBreadcrumbClick?: (path: string) => void;
  onSelectPath?: () => void;
  
  // Image browser
  isGridView: boolean;
  onOpenImageBrowser: () => void;
  onBackFromImageBrowser: () => void;
  imagesBrowserProps: {
    images: Array<{path: string; name: string}>;
    folder: string;
    page: number;
    totalPages: number;
    loading: boolean;
    onRefresh: () => void;
    onPageChange: (page: number) => void;
  };
  
  // Navigation
  onGoToStep: (step: number, projectCode?: string) => void;
  isStepCompleted: boolean;
}

interface CurrentProject {
  id: number;
  project_code: string;
  project_name: string;
  project_type: string;
  folder_path?: string;
  batch_size?: number;
}

export const DatasetSelection: React.FC<DatasetSelectionProps> = ({
  selectedClient,
  activeTab,
  setActiveTab,
  manualDatasetList,
  verificationDatasetList,
  selectedManualDatasetId,
  selectedVerificationDatasetId,
  onManualDatasetChange,
  onVerificationDatasetChange,
  loadingDatasets,
  refreshing,
  assigning,
  onRefreshDatasets,
  onRegisterDataset,
  manualFolderPath,
  setManualFolderPath,
  verificationImageFolderPath,
  setVerificationImageFolderPath,
  verificationLabelFilePath,
  setVerificationLabelFilePath,
  filesPerBatch,
  setFilesPerBatch,
  creatingAnnotationBatch,
  creatingVerificationBatch,
  onSetManualFolder,
  onSetVerificationFolders,
  isGridView,
  onOpenImageBrowser,
  onBackFromImageBrowser,
  imagesBrowserProps,
  onGoToStep,
  isStepCompleted,
}) => {
  // Use the storage browser hook
  const {
    isStorageBrowserOpen,
    setIsStorageBrowserOpen,
    storageType,
    bucketName,
    currentSelectionTarget,
    currentBrowsePath,
    currentSelection,
    isSelectingFile,
    directoryContents,
    isLoadingDirectory,
    openStorageBrowser,
    selectItem,
    breadcrumbClick,
    selectPath,
    currentProject: storageBrowserProject
  } = useStorageBrowser();
  const [currentProject, setCurrentProject] = useState<CurrentProject | null>(null);
  const [projectLoading, setProjectLoading] = useState(false);
  const [folderStats, setFolderStats] = useState<{
    totalFiles: number;
    totalBatches: number;
    folderConfigured: boolean;
  } | null>(null);

  // Load the project that was created in step 1
  useEffect(() => {
    const loadCurrentProject = () => {
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          setCurrentProject(project);
          console.log('Loaded project from localStorage:', project);
        } else {
          console.warn('No project data found in localStorage');
        }
      } catch (error) {
        console.error('Error loading project from localStorage:', error);
      }
    };

    loadCurrentProject();
  }, []);

  // Handle storage path selection
  useEffect(() => {
    const handleStoragePathSelected = (event: CustomEvent) => {
      const { target, path, storageType } = event.detail;
      
      switch (target) {
        case "manual-folder":
          setManualFolderPath(path);
          break;
        case "verification-image-folder":
          setVerificationImageFolderPath(path);
          break;
        case "verification-label-file":
          setVerificationLabelFilePath(path);
          break;
      }
      
      console.log(`Selected ${target}: ${path} (${storageType})`);
    };

    window.addEventListener('storage-path-selected', handleStoragePathSelected as EventListener);
    
    return () => {
      window.removeEventListener('storage-path-selected', handleStoragePathSelected as EventListener);
    };
  }, [setManualFolderPath, setVerificationImageFolderPath, setVerificationLabelFilePath]);

  // Set initial values from current project
  useEffect(() => {
    if (currentProject) {
      if (currentProject.folder_path) {
        setManualFolderPath(currentProject.folder_path);
      }
      if (currentProject.batch_size) {
        setFilesPerBatch(currentProject.batch_size);
      }
    }
  }, [currentProject, setManualFolderPath, setFilesPerBatch]);

  const getSelectedDataset = () => {
    if (activeTab === "annotation" && selectedManualDatasetId) {
      return manualDatasetList.find((d) => d.id === selectedManualDatasetId);
    } else if (activeTab === "verification" && selectedVerificationDatasetId) {
      return verificationDatasetList.find(
        (d) => d.id === selectedVerificationDatasetId
      );
    }
    return null;
  };

  if (isGridView) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 -m-2 min-h-[calc(100vh-400px)]">
        <ImageBrowser
          {...imagesBrowserProps}
          onBack={onBackFromImageBrowser}
        />
      </div>
    );
  }

  return (
    <div className="dataset-selection-wrapper -m-2 p-6 bg-white rounded-lg shadow-sm border-0 min-h-[calc(100vh-400px)] flex flex-col max-w-none w-full">
      <div className="header-section mb-4">
        <h3 className="text-xl font-semibold mb-2 flex items-center">
          Dataset Path Selection
        </h3>

        {selectedClient && (
          <div className="mb-2 p-2 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <strong>Selected Client:</strong>@
              {selectedClient.username}
            </div>
          </div>
        )}
      </div>

      {/* Full Page Loading Overlay */}
      {(creatingAnnotationBatch || creatingVerificationBatch) && (
        <div className="fixed inset-0 w-full h-full flex items-center justify-center bg-black/70 z-[9999]">
          <div className="text-center text-white">
            <div className="animate-spin rounded-full border-4 border-t-transparent border-white mb-3 w-12 h-12" role="status">
              <span className="sr-only">Loading...</span>
            </div>
            <h4 className="mb-2">
              {creatingAnnotationBatch ? 'Setting Folder Path' : 'Creating Verification Batches'}
            </h4>
            <p className="mb-0">
              Processing files...<br />
              This may take a few moments for large folders.
            </p>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="content-section flex-1 mb-8">
        {/* Main Layout Container */}
        <div className="dataset-layout grid grid-cols-[1fr_400px] gap-6 items-start xl:grid-cols-[1fr_350px] xl:gap-4 lg:grid-cols-1 lg:gap-6">
          {/* Create New Dataset Section - Right Column */}
          <div className="create-dataset-column lg:order-first">
            <div className="flex flex-col gap-4 h-fit">             
              {/* Manual Mode Card */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-4">
                  {currentProject && (
                    <div className="bg-blue-50 border border-blue-200 text-blue-800 px-3 py-2 mb-3 rounded-md">
                      <small className="text-blue-800">
                        <strong>Configuring:</strong> {currentProject.project_name}
                      </small>
                    </div>
                  )}

                  {/* Folder Statistics Display */}
                  {folderStats && folderStats.folderConfigured && (
                    <div className="alert alert-success py-2 mb-3">
                      <small className="text-success">
                        <strong>Folder Configured Successfully!</strong><br />
                        <span className="me-3"><strong>Total Files:</strong> {folderStats.totalFiles}</span>
                        <span><strong>Total Batches:</strong> {folderStats.totalBatches}</span>
                      </small>
                    </div>
                  )}
                  <div className="mb-3">
                    <label className="form-label">Image/Video Folder</label>
                    <div className="input-group input-group-sm">
                      <div className="form-control text-sm px-3 py-1.5">
                        {manualFolderPath || "No folder selected"}
                      </div>
                      <button
                        type="button"
                        className="btn btn-primary px-3 py-1.5 text-sm flex items-center gap-1"
                        onClick={() => openStorageBrowser("manual-folder")}
                      >
                        <span className="flex items-center">
                          <FaFolderOpen className="me-1" />
                          <span>Browse</span>
                        </span>
                      </button>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className="form-label">
                      Files per Batch 
                      <small className="text-muted ms-1">(optional)</small>
                    </label>
                    <input
                      type="number"
                      className="form-control text-sm px-3 py-1.5"
                      placeholder="Default: 50 files per batch"
                      min="1"
                      value={filesPerBatch || ""}
                      onChange={(e) => {
                        const value = e.target.value ? parseInt(e.target.value) : null;
                        setFilesPerBatch(value);
                      }}
                    />
                    <div className="form-text small">
                      {filesPerBatch 
                        ? `Each batch will contain ${filesPerBatch} files`
                        : "Default: 50 files per batch"
                      }
                    </div>
                  </div>
                </div>
                <div className="card-footer px-4 py-3">
                  <button
                    type="button"
                    className="btn btn-success px-3 py-1.5 text-sm w-100 flex items-center justify-center gap-2"
                    onClick={async () => {
                      console.log("DatasetSelection: Calling onSetManualFolder for project:", currentProject?.project_code);
                      const result = await onSetManualFolder();

                      // Store folder statistics if available in the result
                      if (result.success && result.data) {
                        setFolderStats({
                          totalFiles: result.data.total_files || 0,
                          totalBatches: result.data.total_batches || 0,
                          folderConfigured: true
                        });
                        console.log("DatasetSelection: Stored folder stats:", {
                          totalFiles: result.data.total_files,
                          totalBatches: result.data.total_batches
                        });
                      }

                      // If successful, pass the project_code when navigating to next step
                      if (result.success && currentProject?.project_code) {
                        onGoToStep(4, currentProject.project_code);
                      }
                    }}
                    disabled={!manualFolderPath || !currentProject || creatingAnnotationBatch}
                  >
                    <span className="flex items-center">
                      {creatingAnnotationBatch ? (
                        <span
                          className="spinner-border spinner-border-sm me-1"
                          role="status"
                        ></span>
                      ) : (
                        <FaCheckCircle className="me-1" />
                      )}
                      <span>{creatingAnnotationBatch
                        ? "Configuring..."
                        : `Configure ${currentProject?.project_name || 'Project'} →`}</span>
                    </span>
                  </button>
                </div>
              </div>
              
            </div>
          </div>
        </div>
      </div>

      {/* Storage Browser Modal */}
      <StorageBrowserModal
        isOpen={isStorageBrowserOpen}
        onClose={() => setIsStorageBrowserOpen(false)}
        storageType={storageType}
        bucketName={bucketName}
        currentBrowsePath={currentBrowsePath}
        currentSelection={currentSelection}
        isSelectingFile={isSelectingFile}
        directoryContents={directoryContents}
        isLoadingDirectory={isLoadingDirectory}
        onSelectItem={selectItem}
        onBreadcrumbClick={breadcrumbClick}
        onSelectPath={selectPath}
        currentSelectionTarget={currentSelectionTarget}
      />

      {/* Navigation Footer */}
      <div className="navigation-footer flex justify-between items-center pt-6 border-t border-gray-300 mt-auto">
        <button
          onClick={() => onGoToStep(2, currentProject?.project_code)}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Back to Data Connectors
        </button>

        {isStepCompleted && (
          <div className="d-flex flex-column align-items-end">
            <div className="text-success mb-2">
              <i className="fas fa-check-circle me-2"></i>
              Dataset processed successfully! Project code will be used in next steps.
            </div>
                      <button
            onClick={() => onGoToStep(4, currentProject?.project_code)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
          >
            Continue to Instructions
            <FaArrowRight className="ml-2" />
          </button>
          </div>
        )}
      </div>

    </div>
  );
};