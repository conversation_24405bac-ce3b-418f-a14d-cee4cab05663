"use client";
import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
// @ts-expect-error TypeScript cannot resolve the export from caption.tsx
import { Caption } from "./caption/caption";
// @ts-expect-error TypeScript cannot resolve the export from OCR component
import { OCR } from "./ocr/ocr";
// @ts-expect-error TypeScript cannot resolve the export from VQA component
import { VQA } from "./vqa/vqa";
// @ts-expect-error TypeScript cannot resolve the export from Transcription component
import { Transcription } from "./transcription/transcription";
import { CaptionResponse, OCRResponse, VQAResponse, TranscriptionResponse } from "./index";
import { API_BASE_URL } from "@/lib/api";

// Common interface for all processing results
interface ProcessingResult {
  metadata?: Record<string, unknown>;
  results?: Array<Record<string, unknown>>;
  processed_files?: Array<Record<string, unknown>>;
  status?: string;
  project_code?: string;
  project_name?: string;
  folder_path?: string;
}
import ProcessingResults from "./ProcessingResults";
import { useProjectService } from "./projectService";



// Component mapping approach - removed unnecessary icons
const AI_COMPONENTS = {
  caption: { component: Caption, label: "Caption" },
  ocr: { component: OCR, label: "OCR" },
  vqa: { component: VQA, label: "VQA" },
  transcription: { component: Transcription, label: "Transcription" }
};

export default function AIProcessing() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get active tab from URL (no local state)
  const activeTab = (searchParams.get('tab') as keyof typeof AI_COMPONENTS) || "caption";

  const handleTabChange = (tab: keyof typeof AI_COMPONENTS) => {
    router.push(`/admin?view=aiProcessing&tab=${tab}`);
  };

  // Get current component configuration
  const currentConfig = AI_COMPONENTS[activeTab];
  const [result, setResult] = useState<ProcessingResult | null>(null);

  // State for project selection in results view
  const [selectedProjectForResults, setSelectedProjectForResults] = useState<string>("");
  
  // Use project service to get projects
  const { projects } = useProjectService();
  

  const handleResultReceived = (result: CaptionResponse | OCRResponse | VQAResponse | TranscriptionResponse) => {
    setResult(result);
    // Set the project code for results view if available
    if (result.project_code) {
      setSelectedProjectForResults(result.project_code);
    }
  };


  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">AI Assisted Labeling Playground</h1>
      </div>
      <p className="text-gray-600 mb-6">Process images, audio, and text using AI models</p>

      {/* Tab Navigation */}
      <div className="flex mb-6 border-b overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === "caption"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => handleTabChange("caption")}
        >
          {AI_COMPONENTS.caption.label}
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === "ocr"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => handleTabChange("ocr")}
        >
          {AI_COMPONENTS.ocr.label}
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === "vqa"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => handleTabChange("vqa")}
        >
          {AI_COMPONENTS.vqa.label}
        </button>
        <button
          className={`px-4 py-2 font-medium flex items-center ${
            activeTab === "transcription"
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
          onClick={() => handleTabChange("transcription")}
        >
          {AI_COMPONENTS.transcription.label}
        </button>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Left column - AI Processing Form */}
        <div className="w-full lg:w-1/2 bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">{currentConfig.label} Processor</h2>
          </div>
          <currentConfig.component onResultReceived={handleResultReceived} />
        </div>

        {/* Right column - Processed Files */}
        <div className="w-full lg:w-1/2 bg-white rounded-xl p-6 shadow-sm border border-gray-200 mt-6 lg:mt-0">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold text-gray-900">Processing Results</h2>
            </div>
            <div>
              <select 
                className="text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 p-1"
                value={selectedProjectForResults}
                onChange={(e) => setSelectedProjectForResults(e.target.value)}
              >
                <option value="">Select Project</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.project_code}>
                    {project.project_code} - {project.project_name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="overflow-y-auto max-h-[600px]">
            {/* Use ProcessingResults component here */}
            <ProcessingResults projectCode={selectedProjectForResults} />
          </div>
        </div>
      </div>
    </div>
  );
}



