"use client";

import React, { useState } from "react";
import { ProjectRegistryResponse } from "./types";
import { 
  useProjects, 
  useProjectActions, 
  useUserAssignment, 
  useStrategy, 
  useBatchAllocations 
} from "./hooks";
import { ProjectFilters, ProjectsTable } from "./components";
import { ProjectDetailsModal, ProjectActionsModal, DeadlineModal } from "./modals";

function ProjectsManagement() {
  // Main projects state and actions
  const {
    projects,
    loading,
    filters,
    pagination,
    availableTypes,
    availableStatuses,
    fetchProjects,
    handleFilterChange,
    handlePageChange,
    clearFilters,
    updateProjectInList,
  } = useProjects();

  // Project actions (activate, deactivate, pause, complete, deadline)
  const {
    activationLoading,
    deadlineLoading,
    activateProject,
    deactivateProject,
    pauseProject,
    completeProject,
    setProjectDeadline,
  } = useProjectActions();

  // User assignment functionality
  const {
    availableAnnotators,
    availableVerifiers,
    loadingUsers,
    selectedAnnotators,
    selectedVerifiers,
    assignedAnnotators,
    assignedVerifiers,
    loadingAssignedUsers,
    assignmentLoading,
    fetchAvailableUsers,
    fetchAssignedUsers,
    assignUsers,
    removeUser,
    toggleUserSelection,
    clearState: clearUserAssignmentState,
  } = useUserAssignment();

  // Strategy details
  const {
    strategyDetails,
    loadingStrategy,
    fetchStrategyDetails,
    clearStrategyDetails,
  } = useStrategy();

  // Batch allocations
  const {
    batchAllocations,
    assignmentProgress,
    loadingBatchAllocations,
    syncingBatchAllocations,
    fetchBatchAllocations,
    syncBatchAllocations,
    clearBatchAllocations,
  } = useBatchAllocations();

  // Modal states
  const [selectedProject, setSelectedProject] = useState<ProjectRegistryResponse | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showActionsModal, setShowActionsModal] = useState(false);
  const [actionProject, setActionProject] = useState<ProjectRegistryResponse | null>(null);
  const [showDeadlineModal, setShowDeadlineModal] = useState(false);
  const [selectedDeadline, setSelectedDeadline] = useState<string>('');

  // Modal handlers
  const openDetailsModal = (project: ProjectRegistryResponse) => {
    setSelectedProject(project);
    setShowDetailsModal(true);
  };

  const closeDetailsModal = () => {
    setSelectedProject(null);
    setShowDetailsModal(false);
  };

  const openActionsModal = async (project: ProjectRegistryResponse) => {
    setActionProject(project);
    setShowActionsModal(true);
    
    // Fetch all necessary data
    await Promise.all([
      fetchStrategyDetails(project.id),
      fetchAvailableUsers(project.id),
      fetchAssignedUsers(project.id),
      fetchBatchAllocations(project.id),
    ]);
  };

  const closeActionsModal = () => {
    // Update the main projects list with the updated batch count before closing
    if (actionProject) {
      updateProjectInList(actionProject.id, { 
        total_batches: batchAllocations.length || actionProject.total_batches 
      });
    }
    
    setActionProject(null);
    setShowActionsModal(false);
    clearStrategyDetails();
    clearUserAssignmentState();
    clearBatchAllocations();
    setShowDeadlineModal(false);
    setSelectedDeadline('');
  };

  const openDeadlineModal = () => {
    if (actionProject) {
      setSelectedDeadline(actionProject.project_deadline || '');
      setShowDeadlineModal(true);
    }
  };

  const closeDeadlineModal = () => {
    setShowDeadlineModal(false);
    setSelectedDeadline('');
  };

  // Project action handlers
  const handleActivateProject = async (createBatches: boolean) => {
    if (!actionProject) return;
    
    const updatedProject = await activateProject(actionProject, createBatches, (updated) => {
      setActionProject(updated);
      updateProjectInList(updated.id, updated);
    });
    
    if (updatedProject) {
      // Refresh assigned users and batch allocations
      await Promise.all([
        fetchAssignedUsers(updatedProject.id),
        fetchBatchAllocations(updatedProject.id),
      ]);
    }
  };

  const handleDeactivateProject = async () => {
    if (!actionProject) return;
    
    const updatedProject = await deactivateProject(actionProject, (updated) => {
      setActionProject(updated);
      updateProjectInList(updated.id, updated);
    });
    
    if (updatedProject) {
      // Clear assigned users and batch allocations since deactivation removes all assignments
      clearUserAssignmentState();
      clearBatchAllocations();
      // Refresh to reflect current state
      await Promise.all([
        fetchAssignedUsers(updatedProject.id),
        fetchBatchAllocations(updatedProject.id),
      ]);
    }
  };

  const handlePauseProject = async () => {
    if (!actionProject) return;
    
    const updatedProject = await pauseProject(actionProject, (updated) => {
      setActionProject(updated);
      updateProjectInList(updated.id, updated);
    });
  };

  const handleCompleteProject = async () => {
    if (!actionProject) return;
    
    const updatedProject = await completeProject(actionProject, (updated) => {
      setActionProject(updated);
      updateProjectInList(updated.id, updated);
    });
    
    if (updatedProject) {
      // Clear assigned users and batch allocations since completion removes all assignments
      clearUserAssignmentState();
      clearBatchAllocations();
      // Refresh to reflect current state
      await Promise.all([
        fetchAssignedUsers(updatedProject.id),
        fetchBatchAllocations(updatedProject.id),
      ]);
    }
  };

  const handleSetDeadline = async () => {
    if (!actionProject || !selectedDeadline) return;
    
    const updatedProject = await setProjectDeadline(actionProject, selectedDeadline, (updated) => {
      setActionProject(updated);
      updateProjectInList(updated.id, updated);
    });
    
    if (updatedProject) {
      closeDeadlineModal();
    }
  };

  // Batch allocation sync handler
  const handleSyncBatchAllocations = async () => {
    if (!actionProject) return;
    
    try {
      await syncBatchAllocations(actionProject.id);
      // Refresh batch allocations after sync
      await fetchBatchAllocations(actionProject.id, false); // Don't auto-sync again
      
      // Refresh the projects list to show updated progress
      await fetchProjects(pagination.page, pagination.page_size, filters);
    } catch (error) {
      console.error("Error in sync handler:", error);
    }
  };

  // User assignment handlers
  const handleAssignUsers = async (role: 'annotators' | 'verifiers', userIds: number[]) => {
    if (!actionProject) return;
    
    await assignUsers(actionProject, role, userIds, strategyDetails, () => {
      // Refresh projects list to update counts
      updateProjectInList(actionProject.id, { 
        active_annotators: role === 'annotators' ? assignedAnnotators.length + userIds.length : actionProject.active_annotators 
      });
    });
  };

  const handleRemoveUser = async (role: 'annotators' | 'verifiers', userId: number) => {
    if (!actionProject) return;
    
    await removeUser(actionProject, role, userId, () => {
      // Refresh projects list to update counts
      updateProjectInList(actionProject.id, { 
        active_annotators: role === 'annotators' ? Math.max(0, assignedAnnotators.length - 1) : actionProject.active_annotators 
      });
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Projects Management</h1>
          <p className="text-gray-600">View and manage all annotation projects</p>
        </div>
      </div>

      {/* Filters */}
      <ProjectFilters
        filters={filters}
        availableTypes={availableTypes}
        availableStatuses={availableStatuses}
        onFilterChange={handleFilterChange}
        onClearFilters={clearFilters}
      />

      {/* Projects Table */}
      <ProjectsTable
        projects={projects}
        loading={loading}
        pagination={pagination}
        onViewDetails={openDetailsModal}
        onManageProject={openActionsModal}
        onPageChange={handlePageChange}
      />

      {/* Project Details Modal */}
      {selectedProject && (
        <ProjectDetailsModal
          project={selectedProject}
          isOpen={showDetailsModal}
          onClose={closeDetailsModal}
        />
      )}

      {/* Project Actions Modal */}
      {actionProject && (
        <ProjectActionsModal
          project={actionProject}
          isOpen={showActionsModal}
          onClose={closeActionsModal}
          // Strategy
          strategyDetails={strategyDetails}
          loadingStrategy={loadingStrategy}
          // User Assignment
          availableAnnotators={availableAnnotators}
          availableVerifiers={availableVerifiers}
          loadingUsers={loadingUsers}
          selectedAnnotators={selectedAnnotators}
          selectedVerifiers={selectedVerifiers}
          assignedAnnotators={assignedAnnotators}
          assignedVerifiers={assignedVerifiers}
          loadingAssignedUsers={loadingAssignedUsers}
          assignmentLoading={assignmentLoading}
          // Batch Allocations
          batchAllocations={batchAllocations}
          assignmentProgress={assignmentProgress}
          loadingBatchAllocations={loadingBatchAllocations}
          syncingBatchAllocations={syncingBatchAllocations}
          onSyncBatchAllocations={handleSyncBatchAllocations}
          // Loading states
          activationLoading={activationLoading}
          deadlineLoading={deadlineLoading}
          // Actions
          onActivateProject={handleActivateProject}
          onDeactivateProject={handleDeactivateProject}
          onPauseProject={handlePauseProject}
          onCompleteProject={handleCompleteProject}
          onSetDeadline={openDeadlineModal}
          onToggleUserSelection={toggleUserSelection}
          onAssignUsers={handleAssignUsers}
          onRemoveUser={handleRemoveUser}
        />
      )}

      {/* Deadline Modal */}
      {actionProject && (
        <DeadlineModal
          project={actionProject}
          isOpen={showDeadlineModal}
          onClose={closeDeadlineModal}
          selectedDeadline={selectedDeadline}
          onDeadlineChange={setSelectedDeadline}
          onSave={handleSetDeadline}
          loading={deadlineLoading}
        />
      )}
    </div>
  );
}

export default ProjectsManagement;
