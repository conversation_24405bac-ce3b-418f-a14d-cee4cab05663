"""
Integration tests for VerifierDataService with real external systems.
Tests verification data workflows with real database operations and data validation.

REAL SYSTEM INTEGRATION:
- Real PostgreSQL database connections
- Real verification data processing workflows
- Real data validation and integrity checks
- Real cross-service data coordination
"""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
import time
from datetime import datetime

from app.services.verifier_data_service import VerifierDataService

class TestVerifierDataServiceIntegration:
    """Integration tests for VerifierDataService with real dependencies."""
    
    @pytest.fixture
    async def real_db_session(self):
        """Real database session."""
        from app.post_db.master_db import MasterSessionLocal
        async with MasterSessionLocal() as session:
            yield session

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_verification_data_workflow(self, real_db_session):
        """Test verification data workflow with real database operations."""
        
        service = VerifierDataService()
        project_code = f'INTEGRATION_VERIFY_TEST_{int(time.time())}'
        
        try:
            # Test verification workflow
            result = await service.process_verification_data(
                project_code,
                verifier_id=9001,
                batch_id='TEST_BATCH_001'
            )
            
            # Should handle non-existent entities gracefully
            assert isinstance(result, dict)
            assert 'success' in result or 'error' in result
            
        except Exception as e:
            # Expected for non-existent project/data
            assert any(keyword in str(e).lower() for keyword in ["project", "database", "verification", "not found"])

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_real_data_validation_workflow(self, real_db_session):
        """Test data validation workflow with real database operations."""
        
        service = VerifierDataService()
        project_code = f'INTEGRATION_VALIDATION_TEST_{int(time.time())}'
        
        try:
            # Test data validation
            result = await service.validate_verification_data(project_code)
            
            # Should handle gracefully
            assert isinstance(result, dict)
            
        except Exception as e:
            # Database validation may fail
            assert any(keyword in str(e).lower() for keyword in ["validation", "database", "project"])
