// Client Information
export interface ClientInfo {
  id: number;
  name: string;
  username: string;
  email: string;
  created_at?: string;
  updated_at?: string;
}

// Allocation Strategy Information
export interface AllocationStrategyInfo {
  id: number;
  strategy_name: string;
  strategy_type?: string;
  description?: string;
}

// Annotation Field Configuration
export interface AnnotationField {
  field_name: string;
  field_type: string;
  label: string;
  max_length?: number;
  options?: string[];
  placeholder?: string;
  required: boolean;
}

// Project Registry Response
export interface ProjectRegistryResponse {
  id: number;
  project_code: string;
  project_name: string;
  project_type: string;
  client_id: number;
  client?: ClientInfo;
  database_name: string;
  database_host: string;
  database_port: number;
  database_connection_params?: any;
  folder_path?: string;
  batch_size?: number;
  allocation_strategy_id?: number;
  allocation_strategy?: AllocationStrategyInfo;
  annotation_requirements?: any;
  instructions?: string;
  project_status: string;
  priority_level: number;
  total_files: number;
  total_batches: number;
  completed_files: number;
  active_annotators: number;
  project_deadline?: string;
  created_at?: string;
  updated_at?: string;
  last_sync_at?: string;
}

// User Information
export interface UserInfo {
  id: number;
  username: string;
  full_name: string;
  email?: string;
  skills?: any;
  max_concurrent_projects?: number;
  current_projects?: number;
  max_concurrent_batches?: number;
  overall_quality_score?: number;
}

// Assigned User Information
export interface AssignedUserInfo {
  id: number;
  username: string;
  full_name: string;
  email?: string;
  is_active: boolean;
  assigned_at: string;
  last_activity?: string;
}

// Batch Allocation Information
export interface BatchAllocationInfo {
  batch_id: number;
  batch_name: string;
  annotator_count: number;
  verifier_count: number;
  verified_count: number;
  total_annotators_required: number;
  total_verifiers_required: number;
  status: 'pending' | 'annotating' | 'verifying' | 'completed';
}

// Assignment Progress
export interface AssignmentProgress {
  total_batches: number;
  annotator_phase: {
    completed_batches: number;
    in_progress_batches: number;
    pending_batches: number;
  };
  verifier_phase: {
    completed_batches: number;
    in_progress_batches: number;
    pending_batches: number;
  };
}

// Strategy Details
export interface StrategyDetails {
  id: number;
  name: string;
  type: string;
  description?: string;
  num_annotators: number;
  requires_audit: boolean;
  requires_verification: boolean;
  requires_verifiers: boolean;
}

// Project List Response
export interface ProjectListResponse {
  projects: ProjectRegistryResponse[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

// Project Filters
export interface ProjectFilters {
  client_id?: number;
  project_type?: string;
  project_status?: string;
  search?: string;
}

// Pagination
export interface Pagination {
  page: number;
  page_size: number;
  total: number;
  total_pages: number;
}

// Project Management State
export interface ProjectManagementState {
  projects: ProjectRegistryResponse[];
  loading: boolean;
  selectedProject: ProjectRegistryResponse | null;
  showDetailsModal: boolean;
  showActionsPane: boolean;
  actionProject: ProjectRegistryResponse | null;
  strategyDetails: StrategyDetails | null;
  loadingStrategy: boolean;
  availableAnnotators: UserInfo[];
  availableVerifiers: UserInfo[];
  loadingUsers: boolean;
  selectedAnnotators: number[];
  selectedVerifiers: number[];
  assignedAnnotators: AssignedUserInfo[];
  assignedVerifiers: AssignedUserInfo[];
  batchAllocations: BatchAllocationInfo[];
  assignmentProgress: AssignmentProgress | null;
  loadingAssignedUsers: boolean;
  loadingBatchAllocations: boolean;
  assignmentLoading: boolean;
  activationLoading: boolean;
  filters: ProjectFilters;
  pagination: Pagination;
  availableTypes: string[];
  availableStatuses: string[];
  showDeadlineModal: boolean;
  deadlineLoading: boolean;
  selectedDeadline: string;
}
