from pydantic import BaseModel, Field
from typing import Optional

class RegisterDatasetRequest(BaseModel):
    folder_path: str = Field(..., description="Path to dataset root in NAS")
    files_per_batch: Optional[int] = Field(None, description="Desired batch size; just stored for later")
    client_id: int = Field(..., description="Integer client ID from clients table")
    project_type: str = Field("image", description="Media type")


