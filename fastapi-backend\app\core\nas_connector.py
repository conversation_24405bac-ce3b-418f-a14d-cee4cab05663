"""NAS connector module for FTP connections with FastAPI support."""

import logging
from typing import Optional, Dict, Any
from urllib.parse import urlparse
from .config import settings
from .ftp_connector import FTPNASConnector
from schemas import FTPConfig
import asyncio
from cache import cache_get, cache_set, cache_delete


logger = logging.getLogger('nas_connector')

# Directory existence cache
directory_cache: Dict[str, Dict[str, Any]] = {}

def parse_ftp_url(url: str) -> tuple[str, int]:
    """Parse FTP URL into host and port components.
    
    Args:
        url: FTP URL (e.g., 'ftp://host:21/path' or 'host:21' or 'host')
        
    Returns:
        Tuple of (host, port)
    """
    # Remove any protocol prefix if present
    if "://" in url:
        parsed = urlparse(url)
        host = parsed.hostname or ""
        port = parsed.port or 21
    else:
        # Handle cases without protocol
        if ":" in url:
            host, port_str = url.split(":", 1)
            host = host.split("/")[0]
            try:
                port = int(port_str.split("/")[0])
            except (ValueError, IndexError):
                port = 21
        else:
            host = url.split("/")[0]
            port = 21
    
    return host, port

async def create_nas_connector(config: FTPConfig) -> Optional[FTPNASConnector]:
    """Create and return an FTP connector with the given configuration.

    Args:
        config: FTPConfig instance with connection details

    Returns:
        FTPNASConnector instance
    """
    logger.info(f"Creating FTP connector: host={config.host}, port={config.port}, user={config.user}, size={config.size}, tls={config.tls}")
    return FTPNASConnector(config)

async def get_ftp_connector_from_credentials(nas_credentials: dict) -> Optional[FTPNASConnector]:
    """Get an FTP connector using project-specific credentials"""
    try:
        if not nas_credentials:
            logger.warning("No NAS credentials provided")
            return None

        url = nas_credentials.get("nas_url")
        nas_username = nas_credentials.get("nas_username")
        nas_password = nas_credentials.get("nas_password")
        nas_type = nas_credentials.get("nas_type", "ftp")

        if not all([url, nas_username, nas_password]):
            logger.warning("Missing NAS credentials in project registry")
            return None

        host, port = parse_ftp_url(url)
        tls = nas_type == "ftps"
        config = FTPConfig(host=host, port=port, user=nas_username, pwd=nas_password, size=10, tls=tls, timeout=15)
        connector = await create_nas_connector(config)
        if not connector or not await connector.authenticate():
            logger.error("FTP authentication failed on connector")
            return None

        logger.info(f"FTP connector ready for use with project credentials ({nas_type})")
        return connector
    except Exception as e:
        logger.error(f"Error creating FTP connector from project credentials: {e}")
        return None


async def get_ftp_connector(username: Optional[str] = None) -> Optional[FTPNASConnector]:
    """Get a fresh FTP connector as a FastAPI dependency - LEGACY FUNCTION"""
    logger.warning("Using legacy get_ftp_connector without project context. Consider using get_ftp_connector_from_credentials instead.")
    try:
        # Try to get credentials from Redis cache (user-specific or global fallback)
        creds = None
        try:
            # First try user-specific cache key
            if username:
                user_cache_key = f"nas_credentials:{username}"
                creds = await cache_get(user_cache_key, json_decode=True)
                if creds:
                    logger.debug(f"Found user-specific NAS credentials for {username}")
                else:
                    logger.debug(f"No user-specific NAS credentials found for {username}, trying global fallback")

            # If no user-specific creds or no username, try global cache for backward compatibility
            if not creds:
                creds = await cache_get("nas_credentials", json_decode=True)
                if creds:
                    logger.debug("Using global NAS credentials (backward compatibility)")
        except Exception as e:
            logger.debug(f"Error reading NAS creds from Redis: {e}")

        if creds:
            return await get_ftp_connector_from_credentials(creds)
        else:
            logger.warning(f"No NAS credentials found in Redis cache for user {username or 'unknown'}")
            return None
    except Exception as e:
        logger.error(f"Error creating FTP connector: {e}")
        return None

async def cache_nas_credentials(credentials: dict, username: Optional[str] = None) -> bool:
    """
    Cache NAS credentials with user-specific key

    Args:
        credentials: NAS credentials dictionary
        username: Username for user-specific caching (optional)

    Returns:
        bool: Success status
    """
    try:
        if username:
            # Use user-specific cache key
            cache_key = f"nas_credentials:{username}"
            logger.debug(f"Caching NAS credentials for user: {username}")
        else:
            # Use global cache key for backward compatibility
            cache_key = "nas_credentials"
            logger.debug("Caching NAS credentials globally (backward compatibility)")

        success = await cache_set(cache_key, credentials, ttl=3600)  # 1 hour TTL
        if success:
            logger.info(f"Successfully cached NAS credentials with key: {cache_key}")
        else:
            logger.error(f"Failed to cache NAS credentials with key: {cache_key}")

        return success
    except Exception as e:
        logger.error(f"Error caching NAS credentials: {e}")
        return False

async def get_cached_nas_credentials(username: Optional[str] = None) -> Optional[dict]:
    """
    Get cached NAS credentials with user-specific or global fallback

    Args:
        username: Username for user-specific caching (optional)

    Returns:
        dict: NAS credentials or None if not found
    """
    try:
        # First try user-specific cache key
        if username:
            user_cache_key = f"nas_credentials:{username}"
            creds = await cache_get(user_cache_key, json_decode=True)
            if creds:
                logger.debug(f"Found user-specific NAS credentials for {username}")
                return creds
            else:
                logger.debug(f"No user-specific NAS credentials found for {username}, trying global fallback")

        # If no user-specific creds or no username, try global cache for backward compatibility
        creds = await cache_get("nas_credentials", json_decode=True)
        if creds:
            logger.debug("Using global NAS credentials (backward compatibility)")
            return creds
        else:
            logger.debug("No NAS credentials found in cache")
            return None
    except Exception as e:
        logger.error(f"Error getting cached NAS credentials: {e}")
        return None

async def clear_nas_credentials_cache(username: Optional[str] = None) -> bool:
    """
    Clear NAS credentials cache for user or globally

    Args:
        username: Username to clear user-specific cache (optional)

    Returns:
        bool: Success status
    """
    try:
        success = True
        if username:
            # Clear user-specific cache
            user_cache_key = f"nas_credentials:{username}"
            user_success = await cache_delete(user_cache_key)
            if user_success:
                logger.info(f"Cleared user-specific NAS credentials cache for {username}")
            else:
                logger.warning(f"Failed to clear user-specific NAS credentials cache for {username}")
            success = success and user_success

        # Always try to clear global cache for backward compatibility
        global_success = await cache_delete("nas_credentials")
        if global_success:
            logger.debug("Cleared global NAS credentials cache")
        success = success and global_success

        return success
    except Exception as e:
        logger.error(f"Error clearing NAS credentials cache: {e}")
        return False

async def cached_directory_exists(connector: FTPNASConnector, path: str) -> bool:
    """
    Check if a directory exists, caching the result to avoid repeated FTP calls.
    """
    try:
        # Use connector ID and path as cache key
        cache_key = (id(connector), path)
        # Return cached result if available for this connector
        if cache_key in directory_cache:
            return directory_cache[cache_key]
        # Call connector.directory_exists (may be async)
        exists = connector.directory_exists(path)
        if asyncio.iscoroutine(exists):
            exists = await exists
        # Cache and return
        directory_cache[cache_key] = exists
        return exists
    except Exception as e:
        logger.error(f"Error checking directory existence for {path}: {e}")
        return False

async def count_files_in_directory(connector: FTPNASConnector, directory_path: str) -> int:
    """
    Count the number of files in a NAS directory (excluding subdirectories).

    Args:
        connector: FTPNASConnector instance
        directory_path: Path to the directory to count files in

    Returns:
        int: Number of files in the directory
    """
    try:
        logger.info(f"Counting files in directory: {directory_path}")

        # Use the existing list_directory method
        directory_contents = await connector.list_directory(directory_path)

        # Filter out directories and count only files
        file_count = sum(1 for item in directory_contents if item.get("type") == "file")

        logger.info(f"Found {file_count} files in directory: {directory_path}")
        return file_count

    except Exception as e:
        logger.error(f"Error counting files in directory {directory_path}: {e}")
        return 0
