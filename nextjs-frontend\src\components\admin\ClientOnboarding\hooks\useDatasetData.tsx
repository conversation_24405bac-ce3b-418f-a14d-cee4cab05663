// hooks/useDatasetData.tsx
import { useState, useEffect, useCallback } from 'react';
import { Dataset, LocalDataset, ActiveTab, ImageItem } from '../types';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "../../../../lib/api";

export const useDatasetData = () => {
  const [activeTab, setActiveTab] = useState<ActiveTab>("annotation");
  const [availableDatasets, setAvailableDatasets] = useState<LocalDataset[]>([]);
  const [selectedDatasets, setSelectedDatasets] = useState<number[]>([]);
  const [loadingDatasets, setLoadingDatasets] = useState(false);
  const [assigningDatasets, setAssigningDatasets] = useState(false);
  const [assigning, setAssigning] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // State for datasets
  const [manualDatasetList, setManualDatasetList] = useState<Dataset[]>([]);
  const [verificationDatasetList, setVerificationDatasetList] = useState<Dataset[]>([]);

  // State for selected datasets
  const [selectedManualDatasetId, setSelectedManualDatasetId] = useState<string | null>(null);
  const [selectedVerificationDatasetId, setSelectedVerificationDatasetId] = useState<string | null>(null);

  // State for batch creation
  const [creatingAnnotationBatch, setCreatingAnnotationBatch] = useState<boolean>(false);
  const [creatingVerificationBatch, setCreatingVerificationBatch] = useState<boolean>(false);

  // State for new dataset paths
  const [manualFolderPath, setManualFolderPath] = useState<string>("");
  const [verificationImageFolderPath, setVerificationImageFolderPath] = useState<string>("");
  const [verificationLabelFilePath, setVerificationLabelFilePath] = useState<string>("");
  
  // State for dynamic batch sizing
  const [filesPerBatch, setFilesPerBatch] = useState<number | null>(null);

  // State for grid browser view
  const [isGridView, setIsGridView] = useState(false);
  const [currentImageFolder, setCurrentImageFolder] = useState<string>('');
  const [imagePage, setImagePage] = useState<number>(1);
  const [totalImagePages, setTotalImagePages] = useState<number>(1);
  const [loadingImages, setLoadingImages] = useState<boolean>(false);
  const [images, setImages] = useState<ImageItem[]>([]);

  const fetchDatasets = useCallback(
    async (showRefreshToast = false) => {
      setLoadingDatasets(true);
      try {
        const res = await authFetch(
          `${API_BASE_URL}/admin/get-datasets?mode=${activeTab}`,
          { credentials: "include" }
        );
        const json = await res.json();
        if (json.success) {
          const rawDatasets = json.data?.datasets || [];
          const datasets: Dataset[] = rawDatasets.map((item: any) => ({
            id: item.id,
            name: item.name,
            total_batches: item.total_batches,
            completed_batches: item.completed_batches,
            progress_percentage: item.progress_percentage,
            folder_path: item.folder_path,
            label_file: item.label_file || item.label_folder,
          }));
          if (activeTab === "annotation") {
            setManualDatasetList(datasets);
            // Don't auto-select first dataset - let user choose
            // Only keep selection if it still exists in the list
            if (selectedManualDatasetId && !datasets.find(d => d.id === selectedManualDatasetId)) {
              setSelectedManualDatasetId(null);
            }
          } else {
            setVerificationDatasetList(datasets);
            // Don't auto-select first dataset - let user choose
            // Only keep selection if it still exists in the list
            if (selectedVerificationDatasetId && !datasets.find(d => d.id === selectedVerificationDatasetId)) {
              setSelectedVerificationDatasetId(null);
            }
          }
          if (showRefreshToast) {
            showToast.success("Datasets refreshed successfully");
          }
        } else {
          if (activeTab === "annotation") setManualDatasetList([]);
          else setVerificationDatasetList([]);
          if (showRefreshToast) {
            showToast.error("Failed to refresh datasets");
          }
        }
      } catch (err) {
        console.error(err);
        if (activeTab === "annotation") setManualDatasetList([]);
        else setVerificationDatasetList([]);
        if (showRefreshToast) {
          showToast.error("Failed to refresh datasets");
        }
      } finally {
        setLoadingDatasets(false);
      }
    },
    [activeTab, selectedManualDatasetId, selectedVerificationDatasetId]
  );

  useEffect(() => {
    fetchDatasets();
  }, [fetchDatasets]);

  const handleRefreshDatasets = async () => {
    setRefreshing(true);
    await fetchDatasets(true);
    setRefreshing(false);
  };

  const getSelectedDataset = () => {
    if (activeTab === "annotation" && selectedManualDatasetId) {
      return manualDatasetList.find((d) => d.id === selectedManualDatasetId);
    } else if (activeTab === "verification" && selectedVerificationDatasetId) {
      return verificationDatasetList.find(
        (d) => d.id === selectedVerificationDatasetId
      );
    }
    return null;
  };

  const handleAssignDataset = async (mode: "annotation" | "verification") => {
    const datasetId =
      mode === "annotation"
        ? selectedManualDatasetId
        : selectedVerificationDatasetId;
    if (!datasetId) {
      showToast.warning("Please select a dataset first.");
      return { success: false };
    }
    setAssigning(true);
    try {
      const res = await authFetch(`${API_BASE_URL}/admin/select-dataset`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ dataset_id: datasetId, mode }),
      });
      const json = await res.json();
      if (json.success) {
        showToast.success(json.message || "Dataset assigned successfully");
        return { success: true };
      } else {
        showToast.error(`Error: ${json.message}`);
        return { success: false };
      }
    } catch (err: any) {
      console.error(err);
      showToast.error(`Error: ${err.message}`);
      return { success: false };
    } finally {
      setAssigning(false);
    }
  };

  const loadImages = async (folderPath: string, page: number = 1) => {
    setLoadingImages(true);
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/browser/${encodeURIComponent(folderPath)}?page=${page}`,
        { credentials: 'include' }
      );
      const json = await res.json();
      const items = json.data?.images || [];
      const imgs = items
        .filter((item: any) => item.type === 'file')
        .map((item: any) => ({
          path: encodeURI(`${API_BASE_URL}/admin/image${item.path}`),
          name: item.name
        }));
      setImages(imgs);
      setImagePage(page);
      setTotalImagePages(json.data?.total_pages || 1);
    } catch (err) {
      console.error('Failed to load images:', err);
    } finally {
      setLoadingImages(false);
    }
  };

  const openImageBrowser = async () => {
    const ds = getSelectedDataset();
    if (!ds?.folder_path) return;
    setCurrentImageFolder(ds.folder_path);
    setIsGridView(true);
    await loadImages(ds.folder_path, 1);
  };

  return {
    activeTab,
    setActiveTab,
    availableDatasets,
    setAvailableDatasets,
    selectedDatasets,
    setSelectedDatasets,
    loadingDatasets,
    assigningDatasets,
    setAssigningDatasets,
    assigning,
    refreshing,
    manualDatasetList,
    verificationDatasetList,
    selectedManualDatasetId,
    setSelectedManualDatasetId,
    selectedVerificationDatasetId,
    setSelectedVerificationDatasetId,
    creatingAnnotationBatch,
    setCreatingAnnotationBatch,
    creatingVerificationBatch,
    setCreatingVerificationBatch,
    manualFolderPath,
    setManualFolderPath,
    verificationImageFolderPath,
    setVerificationImageFolderPath,
    verificationLabelFilePath,
    setVerificationLabelFilePath,
    filesPerBatch,
    setFilesPerBatch,
    isGridView,
    setIsGridView,
    currentImageFolder,
    imagePage,
    totalImagePages,
    loadingImages,
    images,
    fetchDatasets,
    handleRefreshDatasets,
    getSelectedDataset,
    handleAssignDataset,
    loadImages,
    openImageBrowser,
  };
};