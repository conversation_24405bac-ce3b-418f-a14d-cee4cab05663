"use client";

import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';

export interface ProcessedMetadata {
  file_identifier: string;
  original_filename: string;
  file_type: string;
  file_size_bytes: number;
  processing_type: string;
  model_name: string;
  processed_at: string;
}

export interface FileAllocationResult {
  id: number;
  file_id: number;
  file_identifier: string;
  original_filename: string;
  project_code: string;
  preprocessing_results: Record<string, unknown>;
  processed_metadata: ProcessedMetadata;
  processing_status: string;
  processing_type: string;
  model_name?: string;
  processed_at?: string;
}

// Declare process for Next.js environment variables
declare const process: {
  env: {
    NEXT_PUBLIC_API_URL?: string;
  };
};

export const useFileAllocationService = () => {
  const [results, setResults] = useState<FileAllocationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const API_URL = process.env.NEXT_PUBLIC_API_URL || '';

  // Fetch file allocation results with optional filters
  const fetchFileAllocationResults = useCallback(async (
    projectCode?: string,
    processingType?: string,
    limit?: number,
    offset?: number
  ): Promise<FileAllocationResult[]> => {
    setIsLoading(true);
    setError(null);

    try {
      // Build query parameters and ensure they are valid
      const params = new URLSearchParams();
      if (projectCode) params.append('project_code', projectCode);
      if (processingType) params.append('processing_type', processingType);
      
      // Ensure limit is a valid number between 1 and 500
      const validLimit = typeof limit === 'number' && !isNaN(limit) && limit > 0 ? 
        Math.min(limit, 500) : 100;
      params.append('limit', String(validLimit));
      
      // Ensure offset is a valid non-negative number
      const validOffset = typeof offset === 'number' && !isNaN(offset) && offset >= 0 ? 
        offset : 0;
      params.append('offset', String(validOffset));

      const response = await fetch(`${API_URL}/ai/file-allocations?${params.toString()}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file allocation results: ${response.status}`);
      }

      const data = await response.json();
      const resultsList = data.results || [];
      setResults(resultsList);
      return resultsList;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching file allocation results:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load processing results: ${errorMessage}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [API_URL]);

  // Get a single file allocation result by ID
  const getFileAllocationById = useCallback(async (allocationId: number, projectCode: string): Promise<FileAllocationResult | null> => {
    try {
      const response = await fetch(`${API_URL}/ai/file-allocations/${allocationId}?project_code=${projectCode}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file allocation result: ${response.status}`);
      }

      const result = await response.json();
      return result;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(`Error fetching file allocation result ${allocationId}:`, errorMessage);
      toast.error(`Failed to load file allocation result: ${errorMessage}`);
      return null;
    }
  }, [API_URL]);

  // Get file allocations for a specific file
  const getFileAllocationsByFileId = useCallback(async (fileId: number, projectCode: string): Promise<FileAllocationResult[]> => {
    try {
      const response = await fetch(`${API_URL}/ai/file-allocations/file/${fileId}?project_code=${projectCode}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch file allocations: ${response.status}`);
      }

      const data = await response.json();
      return data.results || [];

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(`Error fetching allocations for file ${fileId}:`, errorMessage);
      toast.error(`Failed to load file allocations: ${errorMessage}`);
      return [];
    }
  }, [API_URL]);

  return {
    results,
    isLoading,
    error,
    fetchFileAllocationResults,
    getFileAllocationById,
    getFileAllocationsByFileId,
  };
};

