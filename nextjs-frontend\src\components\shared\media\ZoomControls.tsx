"use client";

import { FaSearchMinus, FaSearchPlus, FaUndo } from "react-icons/fa";
import { ZoomControlsProps } from "./types";

export default function ZoomControls({
  zoomLevel,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  disabled = false
}: ZoomControlsProps) {
  return (
    <>
      <span className="absolute top-2.5 right-2.5 bg-black/70 text-white px-2.5 py-1.5 rounded text-sm z-10 flex items-center">
        <FaSearchPlus className="me-1" /> <span>{zoomLevel}%</span>
      </span>
      
      <div className="absolute bottom-4 right-4 bg-white/80 rounded-2xl px-2.5 py-1.5 flex items-center z-10">
        <div
          className={`cursor-pointer p-1.5 rounded transition-colors duration-200 hover:bg-black/10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={disabled ? undefined : onZoomOut}
          title="Zoom Out (-)"
        >
          <FaSearchMinus />
        </div>
        <div className="mx-2 text-sm font-medium">{zoomLevel}%</div>
        <div
          className={`cursor-pointer p-1.5 rounded transition-colors duration-200 hover:bg-black/10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={disabled ? undefined : onZoomIn}
          title="Zoom In (+)"
        >
          <FaSearchPlus />
        </div>
        <div
          className={`cursor-pointer p-1.5 rounded transition-colors duration-200 hover:bg-black/10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          onClick={disabled ? undefined : onResetZoom}
          title="Reset Zoom"
        >
          <FaUndo />
        </div>
      </div>
    </>
  );
}
