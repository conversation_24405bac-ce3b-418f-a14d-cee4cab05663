# Import the ProjectBase for project-specific models
from ..project_base import ProjectBase

# Import all allocation models to ensure they are registered with ProjectBase.metadata
from .project_metadata import ProjectMetadata
from .files_registry import FilesRegistry
from .allocation_batches import AllocationBatches
from .user_allocations import UserAllocations
from .file_allocations import FileAllocations
from .project_users import ProjectUsers
from .model_execution_logs import ModelExecutionLogs, ExecutionStatus

__all__ = [
    'Base',
    'ProjectBase',
    'ProjectMetadata',
    'FilesRegistry', 
    'AllocationBatches',
    'UserAllocations',
    'FileAllocations',
    'ProjectUsers',
    'ModelExecutionLogs',
    'ExecutionStatus'
]
Base = ProjectBase