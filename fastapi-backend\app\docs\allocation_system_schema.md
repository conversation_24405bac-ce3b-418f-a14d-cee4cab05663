# Allocation System Schema

## **ARCHITECTURE OVERVIEW**

### **Core Concept: Allocation-Focused System**
- **Purpose**: Manages how files and tasks are allocated to users for annotation work
- **Separation of Concerns**: Pure allocation logic separate from annotation results
- **Flexibility**: Supports single, parallel, blind, and consensus workflows
- **Scalability**: Handles complex multi-level allocation strategies

---

## **ALLOCATION SYSTEM TABLES**

### **Purpose**: Each project database contains allocation management tables that handle task distribution, user assignments, and workflow coordination.

### 1. **project_metadata** (Project-specific configuration)
**Role**: Central configuration store for each project database. Contains all project-specific rules, schemas, and processing instructions that govern how annotation work is performed in this project.

```sql
CREATE TABLE project_metadata (
    -- Primary Identity & Master Database Link
    id SERIAL PRIMARY KEY,                         -- Unique metadata record identifier within this project database
    project_code VARCHAR(50) NOT NULL,            -- Human-readable project code matching master database for cross-reference
    master_db_project_id INTEGER NOT NULL,        -- Reference to projects_registry.id in master database for synchronization
    
    -- Dynamic Annotation Configuration
    annotation_requirements JSONB NOT NULL,       -- Dynamic annotation requirements needed for this project
    validation_rules JSONB,                       -- Project-specific validation rules for quality control and consistency
    allocation_strategy JSONB,                    -- Assignment strategies, review processes, and approval workflows for this project
    
    -- File Management Configuration
    connection_type VARCHAR(50)                   -- Type of connection 
    credentials JSONB,                            -- Storage connection credentials and authentication details for this project (NAS, MinIO, Google Drive)
    folder_path VARCHAR(500),                     -- Base folder path for project files on NAS or storage system
    instructions TEXT,                            -- Project-specific instructions for annotators and reviewers
    batch_size INTEGER,                           -- Default batch size for file processing and allocation
    
    -- File Processing & Media Handling Rules
    supported_file_types JSONB,                   -- Allowed file extensions and media types (e.g., ["jpg", "png", "mp4"])
    file_processing_pipeline JSONB,               -- Custom preprocessing steps, resizing, format conversion requirements
    quality_requirements JSONB,                   -- Quality standards, resolution requirements, and acceptance criteria
    
    -- Project Status & Synchronization
    is_active BOOLEAN DEFAULT TRUE,               -- Whether project is currently accepting new work
    last_sync_with_master TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Last successful synchronization with master database
    
    -- Audit Trail
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Project database creation timestamp
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- Last configuration update timestamp
);
```

### 2. **files_registry** (File registry - PURE file management)
**Role**: Pure file registry without allocation concerns. Manages file metadata, storage, and processing status only.

```sql
CREATE TABLE files_registry (
    -- Primary Identity & File Information
    id SERIAL PRIMARY KEY,                          -- Unique file identifier within this project database
    batch_id INTEGER NOT NULL REFERENCES allocation_batches(id) ON DELETE CASCADE, -- Reference to allocation batch
    file_identifier VARCHAR(255) NOT NULL UNIQUE,  -- Client-provided file ID or path for external reference and tracking
    original_filename VARCHAR(500),                -- Original filename as uploaded by client (for user display and debugging)
    file_type VARCHAR(50),                         -- Media type ('image', 'video', 'audio', 'pdf', 'text') determining annotation interface
    file_extension VARCHAR(10),                    -- File extension for processing pipeline selection
    
    -- File Storage & Location Management
    storage_location JSONB,                        -- Flexible storage reference (e.g., {"type": "s3", "bucket": "...", "path": "..."})
    file_size_bytes BIGINT,                       -- File size for storage management and processing optimization
    file_hash VARCHAR(64),                        -- File hash for integrity verification and duplicate detection
    
    -- Processing Order Management
    sequence_order INTEGER,                        -- Sequential processing order for ordered workflows
    
    -- Lifecycle Timestamps
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- When file was initially uploaded to system
);
```

### 3. **allocation_batches** (Batch allocation management)
**Role**: Groups files into batches for efficient allocation management. Focuses purely on batch-level allocation logistics.

```sql
CREATE TABLE allocation_batches (
    -- Primary Identity & Batch Reference
    id SERIAL PRIMARY KEY,                          -- Unique batch identifier within this project database
    batch_identifier VARCHAR(100) NOT NULL UNIQUE, -- Human-readable batch reference (e.g., "BATCH_001_IMAGES") for tracking and communication
    
    -- Batch Status
    batch_status VARCHAR(50) DEFAULT 'created',    -- Current batch state ('created', 'allocating', 'allocated', 'completed')
    
    -- Batch Content Management
    total_files INTEGER NOT NULL,                  -- Total number of files included in this batch
    file_list JSONB,                              -- Array of file IDs included in this batch
    
    -- Allocation Requirements & Criteria
    skill_requirements JSONB,                      -- Required skills for users who can be allocated to this batch
    allocation_criteria JSONB,                     -- Custom criteria for user selection (experience, certification, etc.)
    
    -- Priority & Timeline Management
    is_priority BOOLEAN DEFAULT FALSE,             -- Whether this batch has priority for allocation
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- When batch was created
    deadline TIMESTAMP,                            -- Target completion deadline
    
    -- Dynamic Allocation Columns
    annotation_count INTEGER DEFAULT 0,            -- Number of annotators for this batch
    assignment_count INTEGER DEFAULT 0,            -- Number of annotator assignments in this batch
    completion_count INTEGER DEFAULT 0,            -- Number of annotator completions in this batch
    
    -- Project-Specific Extensibility
    custom_batch_config JSONB                      -- Project-specific batch allocation configuration
);
```

### 4. **project_users** (Project user management)
**Role**: Manages users within a project database. Tracks user roles and batch assignments.

```sql
CREATE TABLE project_users (
    -- Primary Identity & Relationships
    id SERIAL PRIMARY KEY,                          -- Unique user identifier within this project database
    user_id INTEGER NOT NULL,                      -- Reference to users.id in master database
    username VARCHAR(255) NOT NULL,                -- Username for quick reference
    role VARCHAR(255) NOT NULL,                    -- Role for quick reference
    
    -- Batch Tracking
    current_batch INTEGER,                          -- ID of the current batch the user is working on
    completed_batches INTEGER[],                    -- List of batch IDs that the user has completed
    
    -- Constraints
    UNIQUE(user_id, username)                      -- Ensures one record per user per project
);
```

### 5. **user_allocations** (User-to-batch allocations)
**Role**: Manages allocation of users to batches. Tracks allocation status and capacity, separate from annotation work tracking.

```sql
CREATE TABLE user_allocations (
    -- Primary Identity & Relationships
    id SERIAL PRIMARY KEY,                                     -- Unique allocation identifier within this project database
    user_id INTEGER REFERENCES project_users(user_id) ON DELETE CASCADE, -- Reference to project users
    batch_id INTEGER REFERENCES allocation_batches(id) ON DELETE CASCADE, -- Reference to allocation batch
    username VARCHAR(255) NOT NULL,                           -- Username for quick reference
    
    -- Work Progress Tracking
    files_completed INTEGER DEFAULT 0,                        -- Number of files user has completed in this batch
    total_files INTEGER,                                       -- Total files in this batch
    completed_at TIMESTAMP,                                    -- When user completed the batch
    
    -- Allocation Configuration
    allocation_role VARCHAR(50) DEFAULT 'annotator',          -- User's role for this allocation ('annotator', 'reviewer', 'auditor', 'verifier')
    
    -- Allocation Status & Timeline
    is_active BOOLEAN DEFAULT TRUE,                           -- Whether this allocation is currently active
    allocated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- When user was allocated to this batch
    activation_deadline TIMESTAMP,                            -- Deadline for user to start working
    completion_deadline TIMESTAMP,                            -- Deadline for completing allocated work
    
    -- Data Integrity
    UNIQUE(batch_id, username)                                -- Ensures one allocation per user per batch
);
```

### 6. **file_allocations** (Individual file allocations - supports parallel/blind workflows)
**Role**: Manages allocation of individual files to specific users. Handles parallel and blind allocation workflows where multiple users work on the same file.

```sql
CREATE TABLE file_allocations (
    -- Primary Identity & Core Relationships
    id SERIAL PRIMARY KEY,                                     -- Unique file allocation identifier
    file_id INTEGER REFERENCES files_registry(id) ON DELETE CASCADE, -- Reference to file being allocated
    batch_id INTEGER REFERENCES allocation_batches(id) ON DELETE CASCADE, -- Reference to allocation batch
    
    -- Allocation Context & Positioning
    allocation_sequence INTEGER DEFAULT 1,                     -- Sequence number for parallel allocations (1st, 2nd, 3rd annotator for same file)
    workflow_phase VARCHAR(50) DEFAULT 'annotation',          -- Workflow phase ('annotation', 'review', 'verification')
    
    -- File Processing & Preparation
    processing_status VARCHAR(50) DEFAULT 'pending',           -- Current processing state ('pending', 'processing', 'ready', 'failed')
    processed_metadata JSONB,                                 -- Extracted file metadata (dimensions, duration, format details) for annotation tools
    preprocessing_results JSONB,                              -- Results of preprocessing steps (resizing, format conversion, thumbnails)
    
    -- Timeline & Deadlines
    allocated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- When file was allocated to this user
    activation_deadline TIMESTAMP,                            -- Deadline for user to start working
    completion_deadline TIMESTAMP,                            -- Deadline for completing this allocation
    
    -- Allocation Rules & Constraints
    allocation_rules JSONB,                                   -- Custom rules for this specific allocation
    
    -- Assignment & Completion Tracking
    assignment_count INTEGER DEFAULT 0,                       -- Number of times this file has been assigned
    completion_count INTEGER DEFAULT 0,                       -- Number of times this file has been completed
    
    -- Data Integrity
    UNIQUE(file_id, allocation_sequence)                      -- Ensures unique sequence per file
);
```

### 7. **model_execution_logs** (AI model execution tracking)
**Role**: Tracks all AI model inference operations, performance metrics, and human verification for complete audit trail and quality assurance within each project.

```sql
CREATE TABLE model_execution_logs (
    -- Primary Identity & References
    id SERIAL PRIMARY KEY,                                     -- Unique execution log identifier within this project database
    model_name VARCHAR(255) NOT NULL,                         -- Name of the AI model used for this execution
    batch_id INTEGER REFERENCES allocation_batches(id) ON DELETE CASCADE, -- Batch identifier for grouped processing
    file_id INTEGER REFERENCES files_registry(id) ON DELETE CASCADE, -- File being processed in this execution
    
    -- Prompt Information
    user_prompt TEXT,                                          -- Prompt received from user for this execution
    system_prompt TEXT,                                        -- System prompt for this execution
    
    -- Execution Timing & Performance
    execution_start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Timestamp when model execution began
    execution_end_time TIMESTAMP,                             -- Timestamp when model execution completed
    execution_duration_ms INTEGER,                            -- Total execution time in milliseconds for performance tracking
    execution_status VARCHAR(50) NOT NULL,                    -- Final status of model execution ('success', 'failed', 'timeout', 'cancelled', 'in_progress')
    
    -- Model Input/Output Data
    input_data_info JSONB,                                    -- Information about input data (size, format, preprocessing applied)
    output_data JSONB,                                        -- Model predictions and results in structured format
    confidence_scores JSONB,                                  -- Confidence scores for each prediction element
    model_config_snapshot JSONB,                              -- Complete model configuration used for this execution
    
    -- Error Handling & Debugging
    error_message TEXT,                                        -- Detailed error message if execution failed
    error_code VARCHAR(50),                                   -- Standardized error code for categorization and handling
    retry_count INTEGER DEFAULT 0,                            -- Number of times this execution has been retried
    
    -- Audit Trail & System Context
    triggered_by VARCHAR(255)                                 -- System component or user that initiated this execution
);
```

---

## **ALLOCATION WORKFLOW EXAMPLES**

### **1. Basic Batch Allocation Workflow**
```sql
-- Create allocation batch
INSERT INTO allocation_batches (batch_identifier, total_files, file_list)
VALUES ('BATCH_001_IMAGES', 100, '[1,2,3,...,100]');

-- Add users to project
INSERT INTO project_users (user_id, username, role)
VALUES 
    (101, 'annotator1', 'annotator'),
    (102, 'annotator2', 'annotator'),
    (201, 'reviewer1', 'reviewer');

-- Allocate users to batch
INSERT INTO user_allocations (batch_id, user_id, username, allocation_role, total_files)
VALUES 
    (1, 101, 'annotator1', 'annotator', 100),
    (1, 102, 'annotator2', 'annotator', 100),
    (1, 201, 'reviewer1', 'reviewer', 100);

-- Create file allocations for specific files
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES 
    (1, 1, 1, 'annotation'),
    (2, 1, 1, 'annotation'),
    (3, 1, 1, 'annotation');
```

### **2. Parallel Annotation Workflow**
```sql
-- Create file allocations for parallel annotation (multiple annotators on same file)
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES 
    (1, 1, 1, 'annotation'),  -- First annotator
    (1, 1, 2, 'annotation'),  -- Second annotator (parallel)
    (1, 1, 3, 'annotation');  -- Third annotator (parallel)
```

### **3. Sequential Review Workflow**
```sql
-- Create annotation allocation
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES (1, 1, 1, 'annotation');

-- Create review allocation (after annotation)
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES (1, 1, 2, 'review');

-- Create verification allocation (after review)
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES (1, 1, 3, 'verification');
```

### **4. AI Model Execution Tracking**
```sql
-- Log AI model execution
INSERT INTO model_execution_logs (model_name, batch_id, file_id, user_prompt, system_prompt, execution_status, output_data)
VALUES ('gpt-4', 1, 1, 'Analyze this image for objects', 'You are an image analysis AI', 'success', '{"objects": ["car", "person"], "confidence": 0.95}');
```

---

## **🎮 ALLOCATION STATUS MANAGEMENT**

### **Allocation Status Progression**
```
File Upload → Batch Creation → User Allocation → File Assignment → Work Execution

uploaded → batch_created → user_allocated → file_assigned → in_progress → completed
```

### **Batch Allocation Workflow**
```sql
-- Create allocation batch
INSERT INTO allocation_batches (batch_identifier, total_files, file_list)
VALUES ('BATCH_001_IMAGES', 100, '[1,2,3,...,100]');

-- Add users to project
INSERT INTO project_users (user_id, username, role)
VALUES 
    (101, 'annotator1', 'annotator'),
    (102, 'annotator2', 'annotator'),
    (201, 'reviewer1', 'reviewer');

-- Allocate users to batch
INSERT INTO user_allocations (batch_id, user_id, username, allocation_role, total_files)
VALUES 
    (1, 101, 'annotator1', 'annotator', 100),
    (1, 102, 'annotator2', 'annotator', 100),
    (1, 201, 'reviewer1', 'reviewer', 100);

-- Allocate specific files
INSERT INTO file_allocations (file_id, batch_id, allocation_sequence, workflow_phase)
VALUES 
    (1, 1, 1, 'annotation'),
    (2, 1, 1, 'annotation'),
    (3, 1, 1, 'annotation');
```

### **Query Allocation Status**
```sql
-- Check allocation progress for a batch
SELECT 
    ab.batch_identifier,
    ab.batch_status,
    ab.total_files,
    ab.annotation_count,
    ab.assignment_count,
    ab.completion_count,
    ROUND((ab.completion_count::DECIMAL / ab.total_files * 100), 2) as completion_percentage
FROM allocation_batches ab
WHERE ab.batch_identifier = 'BATCH_001_IMAGES';

-- Check file allocation status
SELECT 
    fr.file_identifier,
    fr.file_type,
    fa.allocation_sequence,
    fa.workflow_phase,
    fa.processing_status,
    fa.assignment_count,
    fa.completion_count
FROM files_registry fr
LEFT JOIN file_allocations fa ON fr.id = fa.file_id
WHERE fr.batch_id = 1;
```

---

## **🔧 ALLOCATION MANAGEMENT FEATURES**

### **Key Capabilities**

1. **Pure Allocation Logic**: Separated from annotation results for clean architecture
2. **Flexible Strategies**: Single, parallel, blind, consensus workflows supported
3. **Batch Management**: Efficient grouping and allocation of files
4. **User Capacity Management**: Prevents overloading users with too many assignments
5. **Priority Handling**: Files and batches can have different priority levels
6. **Dependency Management**: Sequential workflows with prerequisite dependencies
7. **Isolation Control**: Different levels of isolation between parallel workers

### **Allocation Strategies Supported**

- **Single**: One annotator per file
- **Parallel**: Multiple annotators work on same file simultaneously (using allocation_sequence)
- **Sequential**: Annotation → Review → Verification chain (using workflow_phase)
- **Hybrid**: AI + Human verification workflows (using model_execution_logs)

### **📊 Allocation Analytics**

```sql
-- User workload analysis
SELECT 
    ua.username,
    ua.allocation_role,
    ua.files_completed,
    ua.total_files,
    ROUND((ua.files_completed::DECIMAL / ua.total_files * 100), 2) as completion_percentage
FROM user_allocations ua
WHERE ua.is_active = true;

-- Batch allocation efficiency
SELECT 
    ab.batch_identifier,
    ab.created_at,
    ab.deadline,
    ab.total_files,
    ab.annotation_count,
    ab.assignment_count,
    ab.completion_count,
    ROUND((ab.completion_count::DECIMAL / ab.total_files * 100), 2) as completion_percentage
FROM allocation_batches ab
WHERE ab.batch_status = 'completed';

-- File processing status
SELECT 
    fr.file_type,
    fa.processing_status,
    COUNT(*) as file_count
FROM files_registry fr
LEFT JOIN file_allocations fa ON fr.id = fa.file_id
GROUP BY fr.file_type, fa.processing_status;

-- AI model execution performance
SELECT 
    model_name,
    execution_status,
    COUNT(*) as execution_count,
    AVG(execution_duration_ms) as avg_duration_ms,
    COUNT(CASE WHEN execution_status = 'success' THEN 1 END) as success_count
FROM model_execution_logs
GROUP BY model_name, execution_status;
```

This allocation system provides the foundation for sophisticated task distribution while maintaining clear separation from the actual annotation work and results.