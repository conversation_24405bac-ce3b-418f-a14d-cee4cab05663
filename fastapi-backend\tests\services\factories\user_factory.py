"""
User data factory for creating consistent test users across all service tests.
"""

import time
from typing import Dict, List, Any
from datetime import datetime, timedelta
try:
    from app.post_db.master_models.users import UserRole
except ImportError:
    # Mock UserRole for testing
    class UserRole:
        ANNOTATOR = "annotator"
        VERIFIER = "verifier" 
        ADMIN = "admin"


class UserFactory:
    """Factory for creating test user data."""
    
    @staticmethod
    def create_annotator(username: str = None, user_id: int = None) -> Dict[str, Any]:
        """Create test annotator user data."""
        timestamp = int(time.time())
        return {
            'user_id': user_id or (1000 + timestamp % 1000),
            'username': username or f'test_annotator_{timestamp % 1000}',
            'email': f'annotator_{timestamp % 1000}@test.com',
            'first_name': 'Test',
            'last_name': 'Annotator',
            'role': 'annotator',
            'user_role': UserRole.ANNOTATOR,
            'is_active': True,
            'created_at': datetime.now() - timedelta(days=30),
            'active_project': None,
            'experience_level': 'intermediate'
        }
    
    @staticmethod
    def create_verifier(username: str = None, user_id: int = None) -> Dict[str, Any]:
        """Create test verifier user data."""
        timestamp = int(time.time())
        return {
            'user_id': user_id or (2000 + timestamp % 1000),
            'username': username or f'test_verifier_{timestamp % 1000}',
            'email': f'verifier_{timestamp % 1000}@test.com',
            'first_name': 'Test',
            'last_name': 'Verifier',
            'role': 'verifier',
            'user_role': UserRole.VERIFIER,
            'is_active': True,
            'created_at': datetime.now() - timedelta(days=20),
            'active_project': None,
            'experience_level': 'experienced'
        }
    
    @staticmethod
    def create_admin(username: str = None, user_id: int = None) -> Dict[str, Any]:
        """Create test admin user data."""
        timestamp = int(time.time())
        return {
            'user_id': user_id or (3000 + timestamp % 1000),
            'username': username or f'test_admin_{timestamp % 1000}',
            'email': f'admin_{timestamp % 1000}@test.com',
            'first_name': 'Test',
            'last_name': 'Admin',
            'role': 'admin',
            'user_role': UserRole.ADMIN,
            'is_active': True,
            'created_at': datetime.now() - timedelta(days=10),
            'active_project': None,
            'permissions': ['all']
        }
    
    @staticmethod
    def create_users_set(
        annotator_count: int = 3,
        verifier_count: int = 1,
        admin_count: int = 1
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Create a complete set of users for testing."""
        timestamp = int(time.time())
        
        users = {
            'annotators': [],
            'verifiers': [],
            'admins': []
        }
        
        # Create annotators
        for i in range(annotator_count):
            users['annotators'].append(
                UserFactory.create_annotator(
                    username=f'annotator_{i+1}_{timestamp % 1000}',
                    user_id=1001 + i
                )
            )
        
        # Create verifiers
        for i in range(verifier_count):
            users['verifiers'].append(
                UserFactory.create_verifier(
                    username=f'verifier_{i+1}_{timestamp % 1000}',
                    user_id=2001 + i
                )
            )
        
        # Create admins
        for i in range(admin_count):
            users['admins'].append(
                UserFactory.create_admin(
                    username=f'admin_{i+1}_{timestamp % 1000}',
                    user_id=3001 + i
                )
            )
        
        # Also create flat list
        users['all'] = (
            users['annotators'] + 
            users['verifiers'] + 
            users['admins']
        )
        
        return users
    
    @staticmethod
    def create_user_with_active_project(
        project_code: str,
        role: str = 'annotator',
        user_id: int = None
    ) -> Dict[str, Any]:
        """Create user with active project assigned."""
        if role == 'annotator':
            user = UserFactory.create_annotator(user_id=user_id)
        elif role == 'verifier':
            user = UserFactory.create_verifier(user_id=user_id)
        else:
            user = UserFactory.create_admin(user_id=user_id)
        
        user['active_project'] = project_code
        return user

    @staticmethod
    def create_user_response_mock(
        user_id: int = None,
        username: str = None,
        role: str = 'annotator'
    ) -> Dict[str, Any]:
        """Create mock user response for testing."""
        timestamp = int(time.time())
        return {
            'user_id': user_id or (1000 + timestamp % 1000),
            'username': username or f'test_user_{timestamp % 1000}',
            'email': f'user_{timestamp % 1000}@test.com',
            'full_name': 'Test User',
            'role': role,
            'is_active': True,
            'created_at': datetime.now().isoformat(),
            'message': 'User registered successfully'
        }
