"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  FaCloudUploadAlt,
  FaInfoCircle,
  FaChevronDown,
  FaFolderOpen,
  FaDownload,
  FaCheck,
  FaFileAlt,
  FaLayerGroup,
} from "react-icons/fa";
import { API_BASE_URL } from "@/lib/api";
import { authFetch } from "@/lib/authFetch";
import { showToast } from "@/lib/toast";

interface CompletedProject {
  id: number;
  project_code: string;
  project_name: string;
  total_files: number;
  total_batches: number;
  completed_files: number;
  folder_path: string;
  project_deadline: string | null;
  created_at: string;
}

export default function DataDelivery() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [selectedProject, setSelectedProject] = useState<string>("");
  const [completedProjects, setCompletedProjects] = useState<CompletedProject[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProjectDetails, setSelectedProjectDetails] = useState<CompletedProject | null>(null);
  const [actualBatchCount, setActualBatchCount] = useState<number | null>(null);
  const [loadingBatchCount, setLoadingBatchCount] = useState(false);

  // Fetch completed projects
  const fetchCompletedProjects = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authFetch(`${API_BASE_URL}/admin/projects?project_status=completed&page_size=100`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      setCompletedProjects(data.projects || []);
    } catch (error) {
      console.error("Error fetching completed projects:", error);
      showToast.error("Failed to load completed projects");
    } finally {
      setLoading(false);
    }
  }, []);

  // Load projects on component mount
  useEffect(() => {
    fetchCompletedProjects();
  }, [fetchCompletedProjects]);

  // Fetch actual batch count from project database
  const fetchActualBatchCount = useCallback(async (projectId: number) => {
    try {
      setLoadingBatchCount(true);
      const response = await authFetch(`${API_BASE_URL}/projects/${projectId}/batch-allocations`);
      
      if (!response.ok) {
        console.error(`Failed to fetch batch allocations: ${response.status}`);
        setActualBatchCount(null);
        return;
      }
      
      const data = await response.json();
      const batchCount = data.batches?.length || 0;
      setActualBatchCount(batchCount);
      
    } catch (error) {
      console.error("Error fetching actual batch count:", error);
      setActualBatchCount(null);
    } finally {
      setLoadingBatchCount(false);
    }
  }, []);

  // Update selected project details when selection changes
  useEffect(() => {
    if (selectedProject) {
      const project = completedProjects.find(p => p.project_code === selectedProject);
      setSelectedProjectDetails(project || null);
      
      // Fetch actual batch count for the selected project
      if (project) {
        fetchActualBatchCount(project.id);
      }
    } else {
      setSelectedProjectDetails(null);
      setActualBatchCount(null);
    }
  }, [selectedProject, completedProjects, fetchActualBatchCount]);

  const handleProjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedProject(value);
    const params = new URLSearchParams(window.location.search);
    params.set("project", value);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const handleExportCSV = async () => {
    if (!selectedProjectDetails) return;
    
    try {
      showToast.info("Preparing CSV export...");
      
      const response = await authFetch(`${API_BASE_URL}/export/csv/${selectedProjectDetails.project_code}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          showToast.error("No review data found for this project");
          return;
        }
        throw new Error(`Export failed: ${response.status}`);
      }
      
      // Get the CSV data as blob
      const blob = await response.blob();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Get filename from Content-Disposition header or create default
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `${selectedProjectDetails.project_code}_export.csv`;
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/"/g, '');
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
      
      showToast.success("CSV export completed successfully!");
      
    } catch (error) {
      console.error("Error exporting CSV:", error);
      showToast.error("Failed to export CSV. Please try again.");
    }
  };

  return (
    <div className="container mx-auto p-3 space-y-3">
      {/* Main UI Content */}
      <div className="relative text-center">
        <h1 className="text-2xl font-bold inline-flex items-center text-blue-700 mx-auto border-b-2 border-blue-500 pb-1">
          <FaCloudUploadAlt className="mr-2 text-3xl" /> Data Delivery
        </h1>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
        <div className="p-4">
          {/* Main Content */}
          <div className="space-y-4">
            {/* Project Selection Card */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <FaFolderOpen className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Select Completed Project</h3>
                  <p className="text-sm text-gray-600">Choose a project to view statistics and export data</p>
                </div>
              </div>
              
              <div className="relative">
                <select
                  id="project-select"
                  value={selectedProject || ""}
                  onChange={handleProjectChange}
                  disabled={loading}
                  className="block w-full appearance-none bg-white border border-gray-300 text-gray-700 py-3 pl-4 pr-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed shadow-sm font-medium"
                >
                  <option value="">
                    {loading ? "Loading projects..." : "Select a completed project"}
                  </option>
                  {completedProjects.map((project) => (
                    <option key={project.project_code} value={project.project_code}>
                      {project.project_name} ({project.project_code})
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4">
                  <FaChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>

            {/* Project Details Card */}
            {selectedProjectDetails && (
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-4">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <FaCheck className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">
                      {selectedProjectDetails.project_name}
                    </h3>
                    <p className="text-sm text-gray-600 font-medium">{selectedProjectDetails.project_code}</p>
                  </div>
                </div>

                {/* Statistics Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <FaFileAlt className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Files</p>
                        <p className="text-2xl font-bold text-blue-600">
                          {selectedProjectDetails.total_files.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <FaLayerGroup className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Batches</p>
                        <p className="text-2xl font-bold text-green-600">
                          {loadingBatchCount ? (
                            <span className="text-gray-400">Loading...</span>
                          ) : (
                            (actualBatchCount ?? selectedProjectDetails.total_batches).toLocaleString()
                          )}
                        </p>
                      </div>
                    </div>
                  </div>

                 
                </div>

                {/* Export Section */}
                <div className="bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-2">
                        <FaDownload className="w-4 h-4 text-indigo-600" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900">Data Export</h4>
                    </div>
                    
                    <button
                      onClick={handleExportCSV}
                      className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 shadow-sm hover:shadow-md text-sm"
                    >
                      <FaDownload className="w-4 h-4 mr-2" />
                      Export as CSV
                    </button>
                  </div>
                </div>
              </div>
            )}

          
          </div>
        </div>
      </div>
    </div>
  );
}
