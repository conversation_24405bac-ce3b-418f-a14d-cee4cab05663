from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

class ImageBase(BaseModel):
    image_data: str
    image_type: str


class Image(ImageBase):
    id: int
    page_id: int

    class Config:
        from_attributes = True

class PageBase(BaseModel):
    page_number: int
    text_content: str
    metadata_json: Optional[str] = None

class Page(PageBase):
    id: int
    document_id: int
    images: List[Image] = Field(default_factory=list)

    class Config:
        from_attributes = True

class DocumentBase(BaseModel):
    filename: str

class Document(DocumentBase):
    id: int
    upload_time: datetime
    pages: List[Page] = Field(default_factory=list)

    class Config:
        from_attributes = True

class DocumentSummary(BaseModel):
    id: int
    filename: str
    upload_time: datetime
    page_count: int
    total_images: int

    class Config:
        from_attributes = True 