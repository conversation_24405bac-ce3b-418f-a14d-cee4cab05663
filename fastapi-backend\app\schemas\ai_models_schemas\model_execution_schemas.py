"""
Model Execution Pydantic Schemas - Simplified for Actual Use Case.
Only includes schemas that match the actual model_execution_logs table
and annotation workflow integration.
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List
from datetime import datetime

from .shared_schemas import (
    PaginationInfo, JsonDict, ExecutionStatus
)


# ===== REQUEST SCHEMAS =====

class ModelExecutionCreate(BaseModel):
    """Schema for creating a new model execution log entry."""
    model_id: int = Field(..., description="ID of the AI model being executed")
    project_id: Optional[int] = Field(None, description="Project ID for context")
    batch_id: Optional[int] = Field(None, description="Batch ID for grouped processing")
    file_id: Optional[int] = Field(None, description="Specific file being processed")
    annotation_task_id: Optional[int] = Field(None, description="Related annotation task")
    
    # Configuration used for execution
    model_config_snapshot: Optional[JsonDict] = Field(None, description="Model configuration used")
    human_verification_required: bool = Field(False, description="Whether human verification is required")
    triggered_by: Optional[str] = Field(None, description="What triggered this execution")
    
    model_config = ConfigDict(from_attributes=True)


class ModelExecutionUpdate(BaseModel):
    """Schema for updating execution results and status."""
    execution_status: Optional[ExecutionStatus] = Field(None, description="Updated execution status")
    execution_end_time: Optional[datetime] = Field(None, description="When execution completed")
    execution_duration_ms: Optional[int] = Field(None, ge=0, description="Execution duration in milliseconds")
    
    # AI Results (matches annotations.ai_raw_output and ai_model_info)
    output_data: Optional[JsonDict] = Field(None, description="Model predictions and results")
    confidence_scores: Optional[JsonDict] = Field(None, description="Confidence scores")
    input_data_info: Optional[JsonDict] = Field(None, description="Information about input data")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if execution failed")
    error_code: Optional[str] = Field(None, description="Standardized error code")
    retry_count: Optional[int] = Field(None, ge=0, description="Number of retries")
    
    model_config = ConfigDict(from_attributes=True)


class HumanVerificationUpdate(BaseModel):
    """Schema for updating human verification status."""
    human_verified: bool = Field(..., description="Whether verification is completed")
    human_verifier_id: int = Field(..., description="User ID performing verification")
    verification_timestamp: datetime = Field(..., description="When verification was completed")
    human_corrections: Optional[JsonDict] = Field(None, description="Corrections made by human")
    
    model_config = ConfigDict(from_attributes=True)


# ===== RESPONSE SCHEMAS =====

class ModelExecutionSummary(BaseModel):
    """Brief execution information for lists."""
    id: int = Field(..., description="Unique execution ID")
    model_id: int = Field(..., description="ID of executed model")
    execution_status: ExecutionStatus = Field(..., description="Current execution status")
    execution_start_time: datetime = Field(..., description="When execution started")
    execution_duration_ms: Optional[int] = Field(None, description="Execution duration")
    project_id: Optional[int] = Field(None, description="Associated project")
    batch_id: Optional[int] = Field(None, description="Associated batch")
    human_verified: bool = Field(False, description="Whether human verified")
    
    model_config = ConfigDict(from_attributes=True)


class ModelExecutionResponse(BaseModel):
    """Complete execution information matching model_execution_logs table."""
    id: int = Field(..., description="Unique execution ID")
    model_id: int = Field(..., description="ID of executed model")
    project_id: Optional[int] = Field(None, description="Associated project ID")
    batch_id: Optional[int] = Field(None, description="Associated batch ID")
    file_id: Optional[int] = Field(None, description="Processed file ID")
    annotation_task_id: Optional[int] = Field(None, description="Related annotation task ID")
    
    # Execution details
    execution_status: ExecutionStatus = Field(..., description="Current execution status")
    execution_start_time: datetime = Field(..., description="When execution started")
    execution_end_time: Optional[datetime] = Field(None, description="When execution completed")
    execution_duration_ms: Optional[int] = Field(None, description="Execution duration")
    
    # Input/output data (matches annotations table fields)
    input_data_info: Optional[JsonDict] = Field(None, description="Input data information")
    output_data: Optional[JsonDict] = Field(None, description="Model predictions (ai_raw_output)")
    confidence_scores: Optional[JsonDict] = Field(None, description="Confidence scores (ai_confidence_scores)")
    model_config_snapshot: Optional[JsonDict] = Field(None, description="Configuration used")
    
    # Human verification (matches annotations workflow)
    human_verification_required: bool = Field(False, description="Whether verification is required")
    human_verified: bool = Field(False, description="Whether verification is completed")
    human_verifier_id: Optional[int] = Field(None, description="Verifier user ID")
    verification_timestamp: Optional[datetime] = Field(None, description="When verified")
    human_corrections: Optional[JsonDict] = Field(None, description="Human corrections")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if failed")
    error_code: Optional[str] = Field(None, description="Error code")
    retry_count: int = Field(0, description="Number of retries")
    triggered_by: Optional[str] = Field(None, description="What triggered execution")
    
    model_config = ConfigDict(from_attributes=True)


class ExecutionListResponse(BaseModel):
    """Paginated list of model executions."""
    executions: List[ModelExecutionSummary] = Field(..., description="List of execution summaries")
    pagination: PaginationInfo = Field(..., description="Pagination information")
    
    model_config = ConfigDict(from_attributes=True)


# Export only relevant schemas
__all__ = [
    "ModelExecutionCreate",
    "ModelExecutionUpdate",
    "HumanVerificationUpdate",
    "ModelExecutionSummary",
    "ModelExecutionResponse",
    "ExecutionListResponse"
]