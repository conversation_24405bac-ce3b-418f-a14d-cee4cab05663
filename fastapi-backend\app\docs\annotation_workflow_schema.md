# Annotation Workflow Schema

## **ARCHITECTURE OVERVIEW (Unified with Allocation System Integration)**

### **Core Concept: Allocation-Driven Annotation System**
- **Purpose**: Manages actual annotation work, results, and quality control following client requirements defined in allocation system
- **Integration**: Annotation workflow automatically follows allocation strategies, annotation modes, and client requirements
- **Unified Storage**: Single `annotations` table handles all file types with dynamic schema based on client requirements
- **Client-Driven**: All annotation structure, validation rules, and workflows defined by client during project handover in allocation system
- **Flexibility**: Supports AI-only, human-only, and hybrid workflows as configured in allocation strategies
- **Quality Focus**: Multi-level review, audit, and consensus mechanisms following allocation system audit configuration

---

## **🏷️ UNIFIED ANNOTATION SYSTEM TABLES**

### **Purpose**: Each project database contains annotation workflow tables that handle the actual annotation work, results storage, and quality control processes. All tables are integrated with the allocation system to ensure annotation workflow follows exact client requirements defined during project handover.

### ** KEY ARCHITECTURE DECISION **
- **FILE-LEVEL STORAGE**: Only `annotations` and `final_deliverables` tables store data at file level
- **TASK-SPECIFIC TRACKING**: `annotation_work` and `annotation_reviews` are task-focused, tracking individual work assignments and their reviews
- **INTEGRATION**: Task-specific work flows into file-level annotations, which aggregate into file-level deliverables for clients

### 1. **annotation_work** (Task-specific work tracking)
**Role**: Tracks individual annotation tasks/assignments being performed. Task-focused, not file-focused - tracks specific work units assigned to users.

```sql
CREATE TABLE annotation_work (
    -- Primary Identity & Task Relationships
    id SERIAL PRIMARY KEY,                                     -- Unique annotation work/task identifier
    allocation_id INTEGER REFERENCES file_allocations(id) ON DELETE CASCADE, -- Reference to specific allocation that created this task
    user_id INTEGER NOT NULL,                                  -- User performing this specific task
    username VARCHAR(255) NOT NULL,                           -- Username for quick reference
    
    -- Task Context & Classification
    task_type VARCHAR(50) NOT NULL,                           -- Type of task ('annotation', 'review', 'audit', 'verification')
    task_phase VARCHAR(50) NOT NULL,                          -- Phase in workflow ('primary', 'secondary', 'audit_level_1', 'audit_level_2')
    annotation_mode VARCHAR(50) DEFAULT 'human_only',         -- Mode from allocation ('ai_only', 'ai_human_hybrid', 'human_only')
    
    -- Task Status & Progress
    task_status VARCHAR(50) DEFAULT 'assigned',               -- Current task status ('assigned', 'in_progress', 'submitted', 'approved', 'rejected')
    progress_percentage DECIMAL(5,2) DEFAULT 0,               -- Task completion percentage (0-100)
    
    -- Performance Tracking (task-specific)
    task_started_at TIMESTAMP,                                 -- When this specific task began
    task_submitted_at TIMESTAMP,                               -- When this task was submitted for review
    task_completed_at TIMESTAMP,                               -- When this task was finally approved/completed
    time_spent_seconds INTEGER DEFAULT 0,                     -- Total time spent on this specific task
    
    -- Quality & Performance Metrics (task-specific)
    self_confidence_score DECIMAL(5,2),                       -- User's confidence in this specific task (0-100)
    task_quality_score DECIMAL(5,2),                          -- Quality score assigned to this task (0-100)
    revision_count INTEGER DEFAULT 0,                         -- Number of times this task was revised
    
    -- Task Dependencies (for sequential workflows)
    depends_on_task_id INTEGER REFERENCES annotation_work(id), -- Previous task that must be completed before this can start
    
    -- Task-Specific Context
    task_instructions TEXT,                                    -- Specific instructions for this task
    expected_deliverable JSONB,                               -- What this task should produce
    
    -- Project-Specific Extensibility
    custom_task_attributes JSONB                              -- Project-specific task tracking attributes
);
```

### 2. **annotations** ( UNIFIED ANNOTATION RESULTS STORAGE )
**Role**: Unified table that stores all annotation results and labels regardless of file type. Schema and structure are dynamically determined by client requirements defined in the allocation system (project_metadata.annotation_schema).

```sql
CREATE TABLE annotations (
    -- Primary Identity & Core Relationships
    id SERIAL PRIMARY KEY,                                     -- Unique annotation identifier within this project database
    file_id INTEGER REFERENCES files_registry(id) ON DELETE CASCADE, -- Reference to the file that was annotated
    task_id INTEGER REFERENCES annotation_work(id) ON DELETE CASCADE, -- Reference to annotation task that created this result
    allocation_id INTEGER REFERENCES file_allocations(id) ON DELETE CASCADE, -- Reference to allocation that produced this annotation
    user_id INTEGER NOT NULL,                                  -- User who created this annotation
    username VARCHAR(255) NOT NULL,                           -- Username for quick reference and audit trail
    
    -- 🏷️ DYNAMIC ANNOTATION RESULTS STORAGE ( CORE DELIVERABLE DATA )
    annotation_data JSONB NOT NULL,                          --  PRIMARY LABEL STORAGE - ALL ANNOTATION RESULTS STORED HERE 
                                                             -- Structure defined by project_metadata.annotation_schema
                                                             -- Content validated against project_metadata.label_definitions
    
    -- Allocation-Driven Configuration (inherits from allocation system)
    annotation_mode VARCHAR(50) NOT NULL,                    -- Mode from allocation_strategies.annotation_mode ('ai_only', 'ai_human_hybrid', 'human_only')
    expected_schema_version INTEGER DEFAULT 1,               -- Version of annotation schema from project_metadata
    follows_allocation_strategy VARCHAR(50) NOT NULL,        -- Strategy type from allocation_strategies.strategy_type
    
    -- Annotation Source & Creation Context
    annotation_source VARCHAR(50) DEFAULT 'human',           -- 'ai', 'human', 'ai_verified_by_human'
    creation_method VARCHAR(50) DEFAULT 'manual',            -- 'manual', 'ai_generated', 'ai_assisted', 'imported'
    annotation_version INTEGER DEFAULT 1,                    -- Version number for tracking revisions
    
    -- AI-Specific Results (when applicable - based on allocation_strategies.annotation_mode)
    ai_raw_output JSONB,                                     -- Original AI model output before any human modification
    ai_confidence_scores JSONB,                              -- AI confidence scores for each annotation element
    ai_model_info JSONB,                                     -- AI model metadata from allocation_strategies.ai_model_config
    human_modifications JSONB,                               -- Changes made by human to AI output (for ai_human_hybrid mode)
    verification_by_human BOOLEAN DEFAULT FALSE,             -- Whether AI output was verified by human (from allocation_strategies.requires_human_verification)
    
    -- File Type & Content Reference (supports all media types)
    file_type VARCHAR(50) NOT NULL,                          -- File type from files_registry.file_type ('image', 'video', 'audio', 'pdf', 'text')
    original_file_content_ref JSONB,                         -- Reference to original file content (text content, image dimensions, etc.)
    
    -- Parallel Workflow Support (follows allocation system configuration)
    parallel_annotation_sequence INTEGER DEFAULT 1,          -- Sequence from file_allocations.allocation_sequence
    is_blind_annotation BOOLEAN DEFAULT FALSE,               -- From file_allocations.is_blind_allocation
    peer_annotation_ids JSONB,                              -- Array of annotation IDs for other annotations of same file (for comparison and consensus)
    isolation_level VARCHAR(50) DEFAULT 'none',              -- From file_allocations.isolation_level
    
    -- Quality & Validation (follows project configuration)
    confidence_score DECIMAL(5,4),                          -- Annotator's self-assessed confidence in their annotation (0-1)
    validation_status VARCHAR(50) DEFAULT 'pending',        -- Status of automated validation against project_metadata.validation_rules
    validation_errors JSONB,                                -- Details of validation failures or quality issues
    quality_requirements_met BOOLEAN DEFAULT FALSE,         -- Whether annotation meets project_metadata.quality_requirements
    
    -- Annotation Status & Lifecycle
    annotation_status VARCHAR(50) DEFAULT 'draft',          -- Current annotation status ('draft', 'submitted', 'approved', 'rejected')
    submission_status VARCHAR(50) DEFAULT 'not_submitted',  -- Submission status ('not_submitted', 'submitted', 'resubmitted')
    
    -- Timeline & Activity Tracking
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,         -- When annotation was created
    submitted_at TIMESTAMP,                                 -- When annotation was submitted
    last_modified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- Last modification timestamp
    
    -- Dynamic Project-Specific Fields (based on client requirements)
    custom_annotation_fields JSONB,                         -- Project-specific annotation attributes from project_metadata.annotation_schema
    client_specific_metadata JSONB                          -- Additional client-defined fields and requirements
);

/*
ANNOTATION_DATA EXAMPLES - Dynamic Structure Based on Client Requirements:

Example 1: Image Classification Project
{
    "objects": [
        {
            "class": "person",
            "confidence": 0.95,
            "bounding_box": {"x": 100, "y": 150, "width": 200, "height": 300},
            "attributes": {"age": "adult", "gender": "female", "pose": "standing"}
        }
    ],
    "scene_type": "outdoor",
    "weather": "sunny",
    "custom_labels": {"vehicle_count": 2, "has_traffic_light": true}
}

Example 2: Text Classification Project
{
    "sentiment": "positive",
    "entities": [
        {"text": "Apple", "label": "ORGANIZATION", "start": 0, "end": 5},
        {"text": "iPhone", "label": "PRODUCT", "start": 15, "end": 21}
    ],
    "categories": ["technology", "product_review"],
    "intent": "purchase_inquiry",
    "confidence_scores": {"sentiment": 0.87, "intent": 0.92}
}

Example 3: Audio Transcription Project
{
    "transcription": "Hello, how can I help you today?",
    "speaker_labels": [
        {"speaker": "Agent", "start_time": 0.0, "end_time": 2.5}
    ],
    "emotions": [
        {"emotion": "friendly", "confidence": 0.82, "timestamp": 1.2}
    ],
    "quality_metrics": {"audio_clarity": "high", "background_noise": "low"}
}

Example 4: PDF Document Analysis
{
    "document_type": "invoice",
    "extracted_fields": {
        "invoice_number": "INV-2024-001",
        "date": "2024-01-15",
        "total_amount": 1250.00,
        "currency": "USD"
    },
    "tables": [
        {
            "table_id": 1,
            "rows": 5,
            "columns": 4,
            "data": [[...]]
        }
    ],
    "confidence_scores": {"field_extraction": 0.92, "table_detection": 0.88}
}

*/
```

### 3. **annotation_reviews** (Task-specific review and audit results)
**Role**: Manages review and audit of specific annotation tasks. Task-focused - reviews specific work assignments, not files directly. Handles multiple review levels and task-specific quality control.

```sql
CREATE TABLE annotation_reviews (
    -- Primary Identity & Task Relationships
    id SERIAL PRIMARY KEY,                                   -- Unique review identifier
    reviewed_task_id INTEGER REFERENCES annotation_work(id) ON DELETE CASCADE, -- Specific task being reviewed
    reviewer_task_id INTEGER REFERENCES annotation_work(id) ON DELETE CASCADE, -- Review task itself
    reviewer_user_id INTEGER NOT NULL,                       -- User performing the review task
    reviewer_username VARCHAR(255) NOT NULL,                 -- Reviewer username
    
    -- Review Task Context (follows allocation system audit configuration)
    review_type VARCHAR(50) NOT NULL,                        -- 'quality_check', 'audit_level_1', 'audit_level_2', 'audit_level_3', 'task_verification'
    review_phase VARCHAR(50) NOT NULL,                       -- Review phase from allocation system
    audit_level INTEGER DEFAULT 0,                           -- Audit level from allocation_strategies.audit_levels
    follows_allocation_audit BOOLEAN DEFAULT TRUE,           -- Whether review follows allocation_strategies.requires_audit
    
    -- Review Decision & Results (task-specific)
    review_decision VARCHAR(50) NOT NULL,                    -- 'approve_task', 'reject_task', 'needs_revision', 'escalate', 'reassign'
    review_score DECIMAL(5,2),                              -- Quality score assigned to this task (0-100)
    review_feedback TEXT,                                   -- Detailed feedback on the task
    quality_criteria_met JSONB,                             -- Task-specific quality criteria evaluation
    
    -- Task Review Metrics
    task_accuracy DECIMAL(5,2),                             -- Accuracy assessment of the task
    task_completeness DECIMAL(5,2),                         -- Completeness assessment of the task  
    meets_task_requirements BOOLEAN DEFAULT FALSE,          -- Whether task meets specified requirements
    
    -- Review Actions & Recommendations
    recommended_action VARCHAR(50),                          -- 'accept', 'revise', 'reassign', 'escalate'
    revision_instructions TEXT,                             -- Specific instructions for task revision
    escalation_reason TEXT,                                 -- Reason for escalating this task
    
    -- Review Timeline (task-specific)
    review_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,   -- When review of this task began
    review_completed_at TIMESTAMP,                          -- When review of this task was completed
    
    -- Task Review Dependencies
    depends_on_review_id INTEGER REFERENCES annotation_reviews(id), -- Previous task review that must be completed first
    blocks_task_ids JSONB,                                  -- Array of task IDs that are blocked by this review
    
    -- Allocation System Integration
    task_compliance JSONB,                                  -- How task complies with allocation requirements
    expected_review_criteria JSONB,                         -- Expected review criteria from project_metadata
    
    -- Project-Specific Extensibility
    custom_review_attributes JSONB                          -- Project-specific task review attributes
);
```

### 4. **final_deliverables** (FILE-LEVEL: Final approved annotation results for client delivery)
**Role**: Stores the final selected and approved annotation results that will be delivered to clients PER FILE. This table remains file-level since deliverables are organized by files that get delivered to clients. Aggregates task-level work into final file-level results.

```sql
CREATE TABLE final_deliverables (
    -- Primary Identity & File Reference (FILE-LEVEL TABLE)
    id SERIAL PRIMARY KEY,                          -- Unique deliverable identifier
    file_id INTEGER REFERENCES files_registry(id) ON DELETE CASCADE, -- File this deliverable is for
    
    -- Final Annotation Selection (references file-level annotations table)
    selected_annotation_id INTEGER REFERENCES annotations(id), -- Final selected annotation from unified annotations table
    final_annotation_data JSONB NOT NULL,          --  FINAL ANNOTATION DATA FOR CLIENT DELIVERY 
                                                   -- Structured according to project_metadata.annotation_schema
    
    -- Task Aggregation (consolidates multiple tasks into file-level result)
    contributing_task_ids JSONB,                   -- Array of annotation_work task IDs that contributed to this deliverable
    total_tasks_completed INTEGER DEFAULT 0,       -- Total number of tasks completed for this file
    all_reviews_passed BOOLEAN DEFAULT FALSE,      -- Whether all task reviews passed for this file
    
    -- Allocation System Compliance (file-level)
    complies_with_allocation_strategy BOOLEAN DEFAULT TRUE, -- Whether deliverable follows allocation_strategies configuration
    allocation_strategy_used VARCHAR(50) NOT NULL, -- Strategy type used from allocation_strategies.strategy_type
    annotation_mode_delivered VARCHAR(50) NOT NULL, -- Final annotation mode delivered ('ai_only', 'ai_human_hybrid', 'human_only')
    meets_client_requirements BOOLEAN DEFAULT TRUE, -- Whether deliverable meets project_metadata requirements
    
    -- Selection Context & Decision (file-level)
    selection_method VARCHAR(50) NOT NULL,         -- How final was selected ('single_task', 'multi_task_consensus', 'audit_approved', 'task_aggregation')
    selected_by_user_id INTEGER NOT NULL,          -- User who made final file-level selection
    selected_by_username VARCHAR(255) NOT NULL,    -- Username of final selector
    selection_reason TEXT,                         -- Explanation of selection decision
    task_completion_summary JSONB,                 -- Summary of how tasks were completed and aggregated
    
    -- Quality & Approval Status (file-level)
    final_quality_score DECIMAL(5,2),              -- Final quality score for this file's deliverable
    meets_quality_requirements BOOLEAN DEFAULT FALSE, -- Whether meets project_metadata.quality_requirements
    validation_against_schema JSONB,               -- Validation results against project_metadata.validation_rules
    approval_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'delivered', 'rejected'
    approved_by_user_id INTEGER,                   -- User who gave final approval
    approved_by_username VARCHAR(255),             -- Username of final approver
    
    -- Delivery Status & Timeline (file-level)
    delivery_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'ready', 'delivered', 'accepted'
    delivery_format JSONB,                         -- Client-specified delivery format from project_metadata
    finalized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- When final selection was made
    approved_at TIMESTAMP,                         -- When final approval was given
    delivered_at TIMESTAMP,                        -- When delivered to client
    
    -- Quality Assurance (file-level)
    passes_quality_check BOOLEAN DEFAULT FALSE,    -- Whether deliverable passes all quality checks
    quality_check_notes TEXT,                      -- Quality assurance notes
    task_audit_trail JSONB,                       -- Complete audit trail of all tasks contributing to this file
    
    -- Client-Specific Extensibility (file-level)
    custom_deliverable_attributes JSONB,           -- Project-specific deliverable attributes from project_metadata
    client_delivery_metadata JSONB                 -- Additional client-specific delivery requirements and formats
);
```



---

## **AI/HUMAN ANNOTATION WORKFLOW CAPABILITIES**

### ** THREE ANNOTATION MODES SUPPORTED **

#### **Mode 1: AI-Only Processing (No Human Verification)**
```sql
-- Project setup for AI-only mode (defined during client onboarding)
UPDATE allocation_strategies SET 
    annotation_mode = 'ai_only',
    ai_model_config = '{"model": "yolo_v8", "confidence_threshold": 0.8}',
    requires_human_verification = FALSE
WHERE file_id = 1;

-- AI Processing Task Assignment (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, annotation_mode)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation'),
    999, -- AI system user ID
    'ai_annotation', 
    'ai_only'
);

-- AI Annotation Storage (FILE-LEVEL: unified table with allocation system integration)
INSERT INTO annotations (
    file_id, task_id, allocation_id, user_id, username,
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, ai_raw_output, ai_model_info, file_type
)
VALUES (1, 
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation') AND task_type = 'ai_annotation'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation'),
    999, 'ai_system',
    '{"objects": [{"class": "cat", "confidence": 0.95, "bbox": [100,150,200,180]}]}',
    'ai_only',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'ai',
    '{"raw_predictions": [{"class": "cat", "score": 0.953}]}',
    '{"model": "yolo_v8", "version": "1.2.3", "processed_at": "2024-01-15T10:30:00Z"}',
    (SELECT file_type FROM files_registry WHERE id = 1)
);

-- Direct completion (FILE-LEVEL: no verification needed - follows allocation system)
INSERT INTO final_deliverables (
    file_id, selected_annotation_id, final_annotation_data, 
    selection_method, allocation_strategy_used, annotation_mode_delivered,
    selected_by_user_id, selected_by_username, contributing_task_ids
)
VALUES (1, 1, 
    (SELECT annotation_data FROM annotations WHERE id = 1),
    'single_task',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'ai_only',
    999, 'ai_system',
    '[(SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1) AND task_type = \'ai_annotation\')]'
);
```

#### **Mode 2: AI + Human Hybrid (Human Verifies AI Output)**
```sql
-- Project setup for AI+Human hybrid mode (defined during client onboarding)
UPDATE allocation_strategies SET 
    annotation_mode = 'ai_human_hybrid',
    ai_model_config = '{"model": "yolo_v8", "confidence_threshold": 0.7}',
    requires_human_verification = TRUE
WHERE file_id = 1;

-- Step 1: AI Processing Task (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, annotation_mode, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation' AND workflow_phase = 'primary'),
    999, -- AI system user ID
    'ai_annotation', 'ai_human_hybrid', 'ai_processing'
);

-- AI Initial Processing (FILE-LEVEL: unified table)
INSERT INTO annotations (
    file_id, task_id, allocation_id, user_id, username,
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, ai_raw_output, file_type, verification_by_human
)
VALUES (1, 
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation') AND task_type = 'ai_annotation'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation'),
    999, 'ai_system',
    '{"objects": [{"class": "cat", "confidence": 0.85, "bbox": [100,150,200,180]}]}',
    'ai_human_hybrid',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'ai',
    '{"raw_predictions": [{"class": "cat", "score": 0.853}, {"class": "dog", "score": 0.234}]}',
    (SELECT file_type FROM files_registry WHERE id = 1),
    FALSE
);

-- Step 2: Human Verification Task Assignment (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, annotation_mode, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'verification'),
    101, 'human_verification', 'ai_human_hybrid', 'verification'
);

-- Human verifies and modifies AI output (FILE-LEVEL: creates new annotation)
INSERT INTO annotations (
    file_id, task_id, allocation_id, user_id, username,
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, ai_raw_output, human_modifications, file_type, verification_by_human
)
VALUES (1,
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'verification') AND task_type = 'human_verification'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'verification'),
    101, 'expert_annotator_jane',
    '{"objects": [{"class": "cat", "confidence": 0.95, "bbox": [105,148,195,182], "verified": true}]}',
    'ai_human_hybrid',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'ai_verified_by_human',
    '{"objects": [{"class": "cat", "confidence": 0.85, "bbox": [100,150,200,180]}]}',
    '{"bbox_adjusted": true, "confidence_updated": true, "human_notes": "Refined bounding box for better accuracy"}',
    (SELECT file_type FROM files_registry WHERE id = 1),
    TRUE
);

-- Completion after human verification (FILE-LEVEL: final deliverable)
INSERT INTO final_deliverables (
    file_id, selected_annotation_id, final_annotation_data, 
    selection_method, allocation_strategy_used, annotation_mode_delivered,
    selected_by_user_id, selected_by_username, contributing_task_ids
)
VALUES (1, 2, 
    (SELECT annotation_data FROM annotations WHERE id = 2),
    'multi_task_consensus',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'ai_human_hybrid',
    101, 'expert_annotator_jane',
    (SELECT json_agg(id) FROM annotation_work WHERE allocation_id IN (SELECT id FROM file_allocations WHERE file_id = 1))
);
```

#### **Mode 3: Human-Only Manual Annotation**
```sql
-- Project setup for human-only mode (defined during client onboarding)
UPDATE allocation_strategies SET 
    annotation_mode = 'human_only',
    ai_model_config = NULL,
    requires_human_verification = FALSE,
    requires_audit = TRUE,
    audit_levels = 1
WHERE file_id = 1;

-- Human Annotation Task Assignment (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, annotation_mode)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation'),
    102, 'human_annotation', 'human_only'
);

-- Human Annotation Storage (FILE-LEVEL: unified table)
INSERT INTO annotations (
    file_id, task_id, allocation_id, user_id, username,
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, file_type
)
VALUES (1,
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation') AND task_type = 'human_annotation'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation'),
    102, 'expert_annotator_jane',
    '{"objects": [{"class": "cat", "confidence": 1.0, "bbox": [102,149,198,181], "attributes": {"breed": "persian"}}]}',
    'human_only',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'human',
    (SELECT file_type FROM files_registry WHERE id = 1)
);

-- Audit Task Assignment (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'audit'),
    103, 'audit_level_1', 'audit'
);

-- Auditor reviews annotation task (task-specific review)
INSERT INTO annotation_reviews (
    reviewed_task_id, reviewer_task_id, reviewer_user_id, reviewer_username, 
    review_type, review_phase, audit_level, review_decision, review_score, follows_allocation_audit
)
VALUES (
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'annotation') AND task_type = 'human_annotation'),
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND allocation_type = 'audit') AND task_type = 'audit_level_1'),
    103, 'senior_auditor_bob',
    'audit_level_1', 'audit_1', 1, 'approve_task', 95.0, TRUE
);
```

---

## **COMPLETE AUDIT WORKFLOW WITH FINAL LABELS**

### **Step-by-Step Audit Process (Task-Specific with File-Level Annotations):**

```sql
-- 🟢 STEP 1: Create annotation tasks for multiple annotators (task-specific)
-- Multiple annotators get separate tasks for parallel blind workflow
INSERT INTO annotation_work (allocation_id, user_id, task_type, annotation_mode, task_phase) VALUES
-- Annotator 1 Task (Alice)
((SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 101 AND allocation_type = 'annotation'),
 101, 'human_annotation', (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1), 'primary'),
-- Annotator 2 Task (Bob)
((SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 102 AND allocation_type = 'annotation'),
 102, 'human_annotation', (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1), 'primary'),
-- Annotator 3 Task (Charlie)
((SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 103 AND allocation_type = 'annotation'),
 103, 'human_annotation', (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1), 'primary');

-- Annotators create their annotations (FILE-LEVEL: following allocation system parallel strategy)
INSERT INTO annotations (
    file_id, task_id, allocation_id, user_id, username, 
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, parallel_annotation_sequence, is_blind_annotation, file_type
) VALUES
-- Annotator 1 (Alice)
(1, 
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 101) AND task_type = 'human_annotation'),
 (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 101 AND allocation_type = 'annotation'),
 101, 'annotator_alice',
 '{"objects": [{"class":"cat", "bbox":[100,150,200,180], "confidence":0.95}]}',
 (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
 (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
 'human', 1, TRUE, (SELECT file_type FROM files_registry WHERE id = 1)),
-- Annotator 2 (Bob)  
(1,
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 102) AND task_type = 'human_annotation'),
 (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 102 AND allocation_type = 'annotation'),
 102, 'annotator_bob',
 '{"objects": [{"class":"cat", "bbox":[98,152,202,178], "confidence":0.88}]}',
 (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
 (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
 'human', 1, TRUE, (SELECT file_type FROM files_registry WHERE id = 1)),
-- Annotator 3 (Charlie)
(1,
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 103) AND task_type = 'human_annotation'),
 (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 103 AND allocation_type = 'annotation'),
 103, 'annotator_charlie',
 '{"objects": [{"class":"dog", "bbox":[105,148,195,182], "confidence":0.82}]}',
 (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
 (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
 'human', 1, TRUE, (SELECT file_type FROM files_registry WHERE id = 1));

-- 🔵 STEP 2: Level 1 Auditor task and review (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201 AND allocation_type = 'audit'),
    201, 'audit_level_1', 'audit_1'
);

-- Level 1 Auditor reviews annotation tasks (task-specific reviews)
INSERT INTO annotation_reviews (
    reviewed_task_id, reviewer_task_id, reviewer_user_id, reviewer_username, 
    review_type, review_phase, audit_level, review_decision, review_score, 
    review_feedback, follows_allocation_audit
) VALUES
-- Review Alice's task
((SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 101) AND task_type = 'human_annotation'),
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201) AND task_type = 'audit_level_1'),
 201, 'auditor_level1', 'audit_level_1', 'audit_1', 1,
 'approve_task', 92.0, 'Most precise bounding box', TRUE),
-- Review Bob's task
((SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 102) AND task_type = 'human_annotation'),
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201) AND task_type = 'audit_level_1'),
 201, 'auditor_level1', 'audit_level_1', 'audit_1', 1,
 'needs_revision', 85.0, 'Slightly imprecise boundaries', TRUE),
-- Review Charlie's task
((SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 103) AND task_type = 'human_annotation'),
 (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201) AND task_type = 'audit_level_1'),
 201, 'auditor_level1', 'audit_level_1', 'audit_1', 1,
 'reject_task', 70.0, 'Incorrect classification - this is a cat, not a dog', TRUE);

-- 🟣 STEP 3: Level 2 Auditor (Senior) reviews Level 1 decisions (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 202 AND allocation_type = 'audit'),
    202, 'audit_level_2', 'audit_2'
);

-- Level 2 reviews Level 1 audit task
INSERT INTO annotation_reviews (
    reviewed_task_id, reviewer_task_id, reviewer_user_id, reviewer_username, 
    review_type, review_phase, audit_level, review_decision, review_score, 
    review_feedback, follows_allocation_audit
) 
VALUES (
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201) AND task_type = 'audit_level_1'),
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 202) AND task_type = 'audit_level_2'),
    202, 'senior_auditor', 'audit_level_2', 'audit_2', 2,
    'approve_task', 95.0, 'Level 1 auditor made correct assessments', TRUE
);

-- 🟠 STEP 4: Level 3 Auditor (Super) final verification (task-specific)
INSERT INTO annotation_work (allocation_id, user_id, task_type, task_phase)
VALUES (
    (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 203 AND allocation_type = 'audit'),
    203, 'audit_level_3', 'audit_3'
);

INSERT INTO annotation_reviews (
    reviewed_task_id, reviewer_task_id, reviewer_user_id, reviewer_username, 
    review_type, review_phase, audit_level, review_decision, review_score, 
    review_feedback, follows_allocation_audit
) 
VALUES (
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 202) AND task_type = 'audit_level_2'),
    (SELECT id FROM annotation_work WHERE allocation_id = (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 203) AND task_type = 'audit_level_3'),
    203, 'super_auditor', 'audit_level_3', 'audit_3', 3,
    'approve_task', 98.0, 'Final verification complete - audit workflow approved', TRUE
);

-- STEP 5: Finalize the deliverable (FILE-LEVEL: aggregates all tasks)
INSERT INTO final_deliverables (
    file_id, selected_annotation_id, final_annotation_data, 
    selection_method, allocation_strategy_used, annotation_mode_delivered,
    selected_by_user_id, selected_by_username, final_quality_score, 
    approval_status, complies_with_allocation_strategy, meets_client_requirements,
    contributing_task_ids, total_tasks_completed, all_reviews_passed
)
VALUES (1, 1, 
    (SELECT annotation_data FROM annotations WHERE id = 1), -- Alice's annotation selected
    'task_aggregation',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
    203, 'super_auditor', 98.0, 'approved', TRUE, TRUE,
    (SELECT json_agg(id) FROM annotation_work WHERE allocation_id IN (SELECT id FROM file_allocations WHERE file_id = 1)),
    (SELECT COUNT(*) FROM annotation_work WHERE allocation_id IN (SELECT id FROM file_allocations WHERE file_id = 1)),
    TRUE
);
```

### **Alternative: Auditor Creates New Annotation (Following Allocation System)**

```sql
-- If Level 1 auditor creates completely new annotation instead of selecting existing one
-- Auditor creates new annotation (unified table with allocation system integration)
INSERT INTO annotations (
    file_id, work_id, allocation_id, user_id, username,
    annotation_data, annotation_mode, follows_allocation_strategy,
    annotation_source, creation_method, parallel_annotation_sequence, file_type
) 
VALUES (1, 
    (SELECT id FROM annotation_work WHERE file_id = 1 AND user_id = 201 AND work_type = 'audit_level_1'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201 AND allocation_type = 'audit'),
    201, 'auditor_level1',
        '{"objects": [{"class":"cat", "bbox":[100,150,200,180]}, {"class":"ball", "bbox":[300,200,50,50]}]}',
    (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    'human', 'auditor_created', 2, (SELECT file_type FROM files_registry WHERE id = 1)
);

-- Review record for new annotation creation
INSERT INTO annotation_reviews (
    annotation_id, file_id, reviewer_work_id, reviewer_allocation_id,
    reviewer_user_id, reviewer_username, review_type, review_phase,
    audit_level, review_decision, review_feedback, creates_new_annotation,
    follows_allocation_audit
) 
VALUES (4, 1,
    (SELECT id FROM annotation_work WHERE file_id = 1 AND user_id = 201 AND work_type = 'audit_level_1'),
    (SELECT id FROM file_allocations WHERE file_id = 1 AND user_id = 201 AND allocation_type = 'audit'),
    201, 'auditor_level1', 'audit_level_1', 'audit_1', 1,
    'create_new', 'Original annotations missed the ball object', TRUE, TRUE
);

-- This new annotation becomes the final one after passing all audit levels
INSERT INTO final_deliverables (
    file_id, selected_annotation_id, final_annotation_data,
    selection_method, allocation_strategy_used, annotation_mode_delivered,
    selected_by_user_id, selected_by_username, selection_reason,
    complies_with_allocation_strategy, meets_client_requirements
)
VALUES (1, 4, 
    (SELECT annotation_data FROM annotations WHERE id = 4), -- Auditor's new annotation
    'auditor_created',
    (SELECT strategy_type FROM allocation_strategies WHERE file_id = 1),
    (SELECT annotation_mode FROM allocation_strategies WHERE file_id = 1),
    201, 'auditor_level1', 'Auditor-created annotation with complete object detection',
    TRUE, TRUE
);
```

---

## **📊 ANNOTATION WORKFLOW QUERIES (Integrated with Allocation System)**

### **Get Final Labels and Audit Status for Files**
```sql
-- Get final verified labels for completed files with allocation system compliance
SELECT 
    fr.file_identifier,
    fr.file_type,
    fd.final_annotation_data, --  FINAL LABELS 
    fd.selection_method,
    fd.allocation_strategy_used,
    fd.annotation_mode_delivered,
    fd.selected_by_username,
    fd.final_quality_score,
    fd.approval_status,
    fd.complies_with_allocation_strategy,
    fd.meets_client_requirements,
    fd.finalized_at,
    
    -- Allocation system context
    as_tbl.strategy_type,
    as_tbl.annotation_mode as original_annotation_mode,
    as_tbl.requires_audit,
    as_tbl.audit_levels
FROM files_registry fr
JOIN final_deliverables fd ON fr.id = fd.file_id
JOIN allocation_strategies as_tbl ON fr.id = as_tbl.file_id
WHERE fd.approval_status = 'approved'
ORDER BY fd.finalized_at;
```

### **Get All Annotations and Audit Trail for a File (Unified Table)**
```sql
-- See complete annotation and audit history for a file with allocation system context
SELECT 
    fr.file_identifier,
    fr.file_type,
    ann.id,
    ann.username,
    ann.annotation_source,
    ann.annotation_mode,
    ann.follows_allocation_strategy,
    ann.parallel_annotation_sequence,
    ann.is_blind_annotation,
    
    -- Annotation content
    ann.annotation_data,
    
    -- Allocation system context
    fa.allocation_role,
    fa.allocation_type,
    fa.workflow_phase,
    fa.is_blind_allocation as allocation_blind,
    
    -- Review information
    ar.review_type,
    ar.review_decision,
    ar.review_score,
    ar.review_feedback,
    ar.is_selected_as_final,
    ar.follows_allocation_audit,
    
    -- Final selection status
    CASE WHEN fd.selected_annotation_id = ann.id THEN 'FINAL_SELECTED' ELSE 'NOT_SELECTED' END as final_status,
    
    ann.created_at,
    ann.submitted_at
FROM files_registry fr
JOIN annotations ann ON fr.id = ann.file_id
LEFT JOIN file_allocations fa ON ann.allocation_id = fa.id
LEFT JOIN annotation_reviews ar ON ann.id = ar.annotation_id
LEFT JOIN final_deliverables fd ON fr.id = fd.file_id
WHERE fr.file_identifier = 'file_001'
ORDER BY ann.parallel_annotation_sequence, ann.created_at;
```

### **Get Files Pending Review at Each Level (With Allocation Context)**
```sql
-- Files waiting for different types of review with allocation system integration
SELECT 
    fr.file_identifier,
    fr.file_type,
    aw.work_type,
    aw.work_phase,
    aw.work_status,
    
    -- Allocation context
    as_tbl.strategy_type,
    as_tbl.annotation_mode,
    as_tbl.requires_audit,
    as_tbl.audit_levels,
    fa.allocation_role,
    fa.workflow_phase as allocation_workflow_phase,
    
    -- Annotation statistics
    COUNT(ann.id) as total_annotations,
    AVG(ann.confidence_score) as avg_confidence,
    COUNT(CASE WHEN ann.annotation_status = 'submitted' THEN 1 END) as submitted_annotations
FROM files_registry fr
JOIN allocation_strategies as_tbl ON fr.id = as_tbl.file_id
JOIN annotation_work aw ON fr.id = aw.file_id
LEFT JOIN file_allocations fa ON aw.allocation_id = fa.id
LEFT JOIN annotations ann ON aw.id = ann.work_id
WHERE aw.work_status IN ('assigned', 'in_progress')
  AND aw.work_type LIKE 'audit%'
GROUP BY fr.id, fr.file_identifier, fr.file_type, aw.work_type, aw.work_phase, aw.work_status,
         as_tbl.strategy_type, as_tbl.annotation_mode, as_tbl.requires_audit, as_tbl.audit_levels,
         fa.allocation_role, fa.workflow_phase
ORDER BY aw.work_type;
```

### **Check Parallel Workflow Status (Allocation System Integration)**
```sql
-- Check if file has completed all required parallel annotations according to allocation strategy
SELECT 
    fr.file_identifier,
    fr.file_type,
    
    -- Allocation requirements
    as_tbl.strategy_type,
    as_tbl.total_required_assignments,
    as_tbl.annotation_mode,
    as_tbl.is_blind_workflow,
    as_tbl.requires_consensus,
    as_tbl.min_consensus_score,
    
    -- Current status
    COUNT(ann.id) as total_annotations,
    COUNT(CASE WHEN ann.annotation_status = 'submitted' THEN 1 END) as submitted_annotations,
    COUNT(CASE WHEN ar.is_selected_as_final = TRUE THEN 1 END) as final_selections,
    AVG(ar.consensus_score) as avg_consensus_score,
    
    -- Completion status
    fd.selection_method,
    fd.approval_status,
    fd.complies_with_allocation_strategy,
    
    -- Progress indicators
    CASE 
        WHEN COUNT(ann.id) >= as_tbl.total_required_assignments THEN 'ANNOTATION_COMPLETE'
        ELSE 'ANNOTATION_PENDING'
    END as annotation_progress,
    
    CASE 
        WHEN as_tbl.requires_consensus AND AVG(ar.consensus_score) >= as_tbl.min_consensus_score THEN 'CONSENSUS_MET'
        WHEN as_tbl.requires_consensus AND AVG(ar.consensus_score) < as_tbl.min_consensus_score THEN 'CONSENSUS_PENDING'
        ELSE 'CONSENSUS_NOT_REQUIRED'
    END as consensus_status
    
FROM files_registry fr
JOIN allocation_strategies as_tbl ON fr.id = as_tbl.file_id
LEFT JOIN annotations ann ON fr.id = ann.file_id
LEFT JOIN annotation_reviews ar ON ann.id = ar.annotation_id
LEFT JOIN final_deliverables fd ON fr.id = fd.file_id
WHERE fr.file_identifier = 'file_001'
GROUP BY fr.id, fr.file_identifier, fr.file_type, as_tbl.strategy_type, as_tbl.total_required_assignments,
         as_tbl.annotation_mode, as_tbl.is_blind_workflow, as_tbl.requires_consensus, as_tbl.min_consensus_score,
         fd.selection_method, fd.approval_status, fd.complies_with_allocation_strategy;
```

### **Get Project Summary with Client Requirements Compliance**
```sql
-- Project-level summary showing compliance with client requirements from allocation system
SELECT 
    pm.project_code,
    pm.annotation_schema,
    pm.label_definitions,
    
    -- File and annotation statistics
    COUNT(DISTINCT fr.id) as total_files,
    COUNT(DISTINCT ann.id) as total_annotations,
    COUNT(DISTINCT fd.id) as completed_deliverables,
    
    -- Allocation strategy distribution
    COUNT(CASE WHEN as_tbl.annotation_mode = 'ai_only' THEN 1 END) as ai_only_files,
    COUNT(CASE WHEN as_tbl.annotation_mode = 'ai_human_hybrid' THEN 1 END) as hybrid_files,
    COUNT(CASE WHEN as_tbl.annotation_mode = 'human_only' THEN 1 END) as human_only_files,
    
    -- Quality metrics
    AVG(fd.final_quality_score) as avg_final_quality,
    COUNT(CASE WHEN fd.meets_client_requirements = TRUE THEN 1 END) as meets_requirements_count,
    COUNT(CASE WHEN fd.complies_with_allocation_strategy = TRUE THEN 1 END) as strategy_compliant_count,
    
    -- Progress indicators
    ROUND((COUNT(DISTINCT fd.id)::DECIMAL / COUNT(DISTINCT fr.id) * 100), 2) as completion_percentage
    
FROM project_metadata pm
LEFT JOIN files_registry fr ON TRUE  -- All files in project
LEFT JOIN allocation_strategies as_tbl ON fr.id = as_tbl.file_id
LEFT JOIN annotations ann ON fr.id = ann.file_id
LEFT JOIN final_deliverables fd ON fr.id = fd.file_id
GROUP BY pm.id, pm.project_code, pm.annotation_schema, pm.label_definitions;
```

---

## **🏷️ ANNOTATION LABELS STORAGE - QUICK REFERENCE (Unified Architecture)**

### ** WHERE ANNOTATION LABELS ARE STORED **

**Primary Storage Location:** `annotations` table (unified) in each project database

**Key Field:** `annotation_data` (JSONB column) - This contains ALL the actual annotation labels and work

**Schema Definition:** Dynamically determined by `project_metadata.annotation_schema` based on client requirements defined during project handover in the allocation system

**Validation:** Content validated against `project_metadata.label_definitions` and `project_metadata.validation_rules`

### **Dynamic Label Structure Based on Client Requirements:**

The annotation data structure is **completely flexible** and defined by the client during project setup in the allocation system. The `project_metadata.annotation_schema` field contains the exact structure required for each project.

#### **Example 1: Image Classification Project (Client-Defined Schema)**
```json
{
  "objects": [
    {
      "class": "person",
      "confidence": 0.95,
      "bounding_box": {"x": 100, "y": 150, "width": 200, "height": 300},
      "attributes": {"age": "adult", "gender": "female", "pose": "standing"}
    }
  ],
  "scene_type": "outdoor",
  "weather": "sunny",
  "custom_labels": {"vehicle_count": 2, "has_traffic_light": true}
}
```

#### **Example 2: Text Classification Project (Client-Defined Schema)**
```json
{
  "sentiment": "positive",
  "entities": [
    {"text": "Apple", "label": "ORGANIZATION", "start": 0, "end": 5},
    {"text": "iPhone", "label": "PRODUCT", "start": 15, "end": 21}
  ],
  "categories": ["technology", "product_review"],
  "intent": "purchase_inquiry",
  "confidence_scores": {"sentiment": 0.87, "intent": 0.92}
}
```

#### **Example 3: Audio Transcription Project (Client-Defined Schema)**
```json
{
  "transcription": "Hello, how can I help you today?",
  "speaker_labels": [
    {"speaker": "Agent", "start_time": 0.0, "end_time": 2.5}
  ],
  "emotions": [
    {"emotion": "friendly", "confidence": 0.82, "timestamp": 1.2}
  ],
  "quality_metrics": {"audio_clarity": "high", "background_noise": "low"}
}
```

#### **Example 4: Custom Multi-Modal Project (Client-Defined Schema)**
```json
{
  "document_analysis": {
    "document_type": "medical_report",
    "key_sections": {
      "diagnosis": "Type 2 Diabetes",
      "medications": ["Metformin", "Insulin"],
      "lab_values": {"glucose": 180, "hba1c": 8.2}
    }
  },
  "image_analysis": {
    "scan_type": "chest_xray",
    "findings": ["clear_lungs", "normal_heart_size"],
    "abnormalities": []
  },
  "client_specific_fields": {
    "priority_level": "high",
    "requires_specialist_review": true,
    "billing_code": "CPT-12345"
  }
}
```

### **Key Integration Points with Allocation System:**

1. **Schema Definition:** `project_metadata.annotation_schema` defines the exact structure
2. **Label Catalog:** `project_metadata.label_definitions` contains all allowed values
3. **Validation Rules:** `project_metadata.validation_rules` ensures data quality
4. **Workflow Configuration:** `allocation_strategies.annotation_mode` determines the processing approach
5. **Quality Requirements:** `allocation_strategies.quality_requirements` sets the standards

---

## **IMPLEMENTATION ADVANTAGES (Unified Architecture with Allocation System Integration)**

### **Benefits of Unified Annotation Workflow Architecture**

1. **Allocation System Integration**: Annotation workflow follows client requirements defined in allocation system during project handover
2. **Unified Data Storage**: Single `annotations` table handles all file types (image, text, audio, video, PDF) based on client schema
3. **Dynamic Schema Support**: Annotation structure completely flexible and defined by `project_metadata.annotation_schema`
4. **Client-Driven Configuration**: All annotation requirements, validation rules, and quality standards defined by client during allocation
5. **Pure Annotation Focus**: Clear separation between work tracking and annotation results while following allocation strategy
6. **Multi-Modal Support**: Single table supports any media type with client-defined annotation structure
7. **Quality Control**: Multi-level review and audit capabilities following allocation system audit configuration
8. **Parallel Workflows**: Support for blind, consensus, and jury selection processes as defined in allocation strategies
9. **AI Integration**: Seamless AI-only, human-only, and hybrid workflows based on allocation system annotation mode
10. **Audit Trail**: Complete tracking of annotation lifecycle and decisions with allocation system compliance
11. **Final Deliverables**: Clear separation of client-ready results that meet allocation system requirements

### **🔧 Unified Workflow Features**

1. **Allocation-Driven Work Tracking**: Annotation work follows allocation system assignments and dependencies
2. **Client Schema Validation**: All annotations validated against client-defined schema and label definitions
3. **Multi-Level Reviews**: Flexible audit levels following allocation system audit configuration
4. **Consensus Mechanisms**: Support for agreement scoring and jury selection as defined in allocation strategies
5. **Version Control**: Annotation versioning and revision tracking with allocation system compliance
6. **Quality Metrics**: Comprehensive quality scoring following client-defined quality requirements
7. **File-Level Storage**: All annotation data stored at file level in unified table regardless of file type
8. **Dynamic Validation**: Real-time validation against project-specific rules defined in allocation system

### **Key Architectural Benefits**

1. **Single Source of Truth**: Allocation system defines all client requirements, annotation workflow executes them
2. **Complete Flexibility**: Can handle any annotation project type through dynamic schema definition
3. **Scalable Design**: Unified table approach scales better than separate tables per project type
4. **Client Compliance**: Ensures annotation workflow always follows exact client requirements from allocation
5. **Maintainable Codebase**: Single annotation table reduces code complexity vs multiple project-type tables
6. **Audit Compliance**: Complete traceability from allocation strategy through annotation to final deliverable

This unified annotation workflow system provides sophisticated annotation management that automatically follows client requirements defined in the allocation system, ensuring perfect compliance with project specifications while maintaining clean data organization and comprehensive quality control mechanisms.