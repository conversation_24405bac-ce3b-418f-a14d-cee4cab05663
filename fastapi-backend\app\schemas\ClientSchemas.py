from pydantic import BaseModel, EmailStr, field_validator, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime

class ProjectDetails(BaseModel):
    """Schema for project details"""
    project_name: str = Field(..., description="Name of the project")
    project_type: str = Field(..., description="Type of project (image, pdf, video, audio, text, csv)")
    project_description: Optional[str] = None

class ClientRegistrationRequest(BaseModel):
    """Schema for client registration with project details"""
    # Client Information
    client_name: str = Field(..., description="Name of the client organization")
    username: str = Field(..., description="Unique username for the client")
    email: str = Field(..., description="Email address for the client")
    contact_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional contact information as JSON (optional)")

    # Project Information - can be either nested or flat
    project: Optional[ProjectDetails] = None
    project_name: Optional[str] = None
    project_type: Optional[str] = None
    project_code: Optional[str] = Field(None, description="Optional custom project code, will be auto-generated if not provided")
    project_description: Optional[str] = None

    # NAS Information (optional)
    nas_url: Optional[str] = None
    nas_username: Optional[str] = None
    nas_password: Optional[str] = None
    nas_type: Optional[str] = "ftp"

    model_config = ConfigDict(from_attributes=True)

class ClientResponse(BaseModel):
    """Schema for client response"""
    id: int
    name: str
    username: str
    email: str
    created_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class ProjectRegistryResponse(BaseModel):
    """Schema for project registry response"""
    id: int
    project_code: str
    project_name: str
    project_type: str
    client_id: int
    folder_path: Optional[str] = None
    batch_size: Optional[int] = None
    total_files: int = 0
    total_batches: int = 0
    completed_files: int = 0

    model_config = ConfigDict(from_attributes=True)

class ClientWithProjectsResponse(BaseModel):
    """Schema for client with projects response"""
    client: ClientResponse
    projects: List[ProjectRegistryResponse] = []
    
    model_config = ConfigDict(from_attributes=True)