["tests/integration/database/test_cache_operations.py::TestRedisCacheOperations::test_basic_cache_operations", "tests/integration/database/test_master_db_operations.py::TestMasterDatabaseOperations::test_client_creation_and_retrieval", "tests/integration/database/test_validation_rules.py::TestPasswordValidation::test_password_length_validation", "tests/services/unit/test_annotator_service_unit.py::TestAnnotatorServiceUnit::test_normalize_image_path_valid_paths", "tests/services/unit/test_auth_service_unit.py::TestAuthService::test_register_user_success", "tests/services/unit/test_csv_batch_service_unit.py::TestCSVBatchServiceUnit::test_validate_csv_structure_valid"]