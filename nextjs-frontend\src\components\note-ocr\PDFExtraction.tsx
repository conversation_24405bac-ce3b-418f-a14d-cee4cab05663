"use client";

import React, { useState, useEffect } from "react";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa";
import { API_BASE_URL } from "@/lib/api";

// Types for documents and page data
export type Document = {
  id: number;
  filename: string;
  upload_time: string;
  page_count: number;
  total_images: number;
};

export type PageData = {
  page_number: number;
  metadata_json?: string;
  text_content?: string;
  images?: {
    image_data: string;
    image_type?: string;
  }[];
};

const API_BASE = API_BASE_URL;

export default function PDFExtraction() {
  const [uploadLoading, setUploadLoading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  const [documents, setDocuments] = useState<Document[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [documentsError, setDocumentsError] = useState("");
  const [selectedDocument, setSelectedDocument] = useState<null | {
    filename: string;
    pages: PageData[];
  }>(null);
  const [selectedFileName, setSelectedFileName] = useState<string>("");
  const [currentPageIndex, setCurrentPageIndex] = useState<number>(0);
  const currentPage = selectedDocument?.pages[currentPageIndex] || null;

  // Load documents on mount
  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    setDocumentsLoading(true);
    setDocumentsError("");
    try {
      const response = await fetch(`${API_BASE}/NoteOCR/documents/`);
      if (!response.ok) throw new Error("Failed to load documents");
      const docs: Document[] = await response.json();
      setDocuments(docs);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setDocumentsError(err.message);
      } else {
        setDocumentsError("An unknown error occurred");
      }
    } finally {
      setDocumentsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFileName(e.target.files[0].name);
    } else {
      setSelectedFileName("");
    }
  };

  const handleUpload = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const form = e.currentTarget;
    const fileInput = form.querySelector<HTMLInputElement>("#pdfFile");
    const file = fileInput?.files?.[0];
    if (!file) return setUploadError("Please select a PDF file");
    if (!file.name.endsWith(".pdf"))
      return setUploadError("File must be a PDF");

    const formData = new FormData();
    formData.append("file", file);

    setUploadLoading(true);
    setUploadError("");
    try {
      const response = await fetch(`${API_BASE}/NoteOCR/documents/`, {
        method: "POST",
        body: formData,
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.detail || "Failed to upload PDF");
      }
      if (fileInput) fileInput.value = "";
      setSelectedFileName("");
      loadDocuments();
    } catch (err: unknown) {
      if (err instanceof Error) {
        setUploadError(err.message);
      } else {
        setUploadError("An unknown error occurred");
      }
    } finally {
      setUploadLoading(false);
    }
  };

  const viewDocument = async (docId: number) => {
    setDocumentsLoading(true);
    setDocumentsError("");
    try {
      const response = await fetch(`${API_BASE}/NoteOCR/documents/${docId}`);
      if (!response.ok) throw new Error("Failed to load document");
      const docData = await response.json();
      setSelectedDocument(docData);
      setCurrentPageIndex(0);
    } catch (err: unknown) {
      if (err instanceof Error) {
        setDocumentsError(err.message);
      } else {
        setDocumentsError("An unknown error occurred");
      }
    } finally {
      setDocumentsLoading(false);
    }
  };

  const deleteDocument = async (docId: number) => {
    if (!confirm("Are you sure you want to delete this document?")) return;
    setDocumentsLoading(true);
    setDocumentsError("");
    try {
      const response = await fetch(`${API_BASE}/NoteOCR/documents/${docId}`, {
        method: "DELETE",
      });
      if (!response.ok) throw new Error("Failed to delete document");
      loadDocuments();
    } catch (err: unknown) {
      if (err instanceof Error) {
        setDocumentsError(err.message);
      } else {
        setDocumentsError("An unknown error occurred");
      }
    } finally {
      setDocumentsLoading(false);
    }
  };

  const prevPage = () => {
    if (currentPageIndex > 0) setCurrentPageIndex((idx) => idx - 1);
  };

  const nextPage = () => {
    if (
      selectedDocument &&
      currentPageIndex < selectedDocument.pages.length - 1
    )
      setCurrentPageIndex((idx) => idx + 1);
  };

  return (
    <>
      <Head>
        <title>PDF Extraction App</title>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>

      <div className="min-h-screen bg-gray-50 py-16 font-sans">
        <div className="max-w-7xl mx-auto px-4">
          {/* Header */}
          <div className="flex items-center justify-center relative mb-8">
            <div className="text-center flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-secondary">
                PDF Extraction App
              </h1>
              <div className="w-20 h-1 bg-[#0D47A1] mx-auto mt-2 rounded"></div>
              <p className="text-gray-500 mt-2">
                Upload and process PDFs to extract pages, images, and text.
              </p>
            </div>
            <Link
              href="/note-ocr"
              className="absolute right-0 inline-flex items-center px-4 py-2 bg-gradient-to-br from-[#0D47A1] to-[#1159B8] rounded-full text-white hover:bg-gray-100 transition no-underline"
            >
              <FaArrowLeft className="mr-2" /> Back to Dashboard
            </Link>
          </div>
          <div className="flex flex-col gap-6">
            {/* Upload Section */}
            <section className="bg-gray-100 p-6 rounded-lg text-center">
              <h2 className="text-2xl font-semibold mb-4 text-gray-800 text-center">
                Upload PDF
              </h2>
              <form
                onSubmit={handleUpload}
                className="flex flex-col items-center space-y-4"
              >
                <input
                  type="file"
                  id="pdfFile"
                  accept=".pdf"
                  required
                  className="hidden"
                  onChange={handleFileChange}
                />
                <label
                  htmlFor="pdfFile"
                  className="w-full max-w-md border-2 border-dashed border-gray-300 rounded-lg py-6 flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors duration-200"
                >
                  <span className="text-gray-600">
                    {selectedFileName || "Click to select a PDF file"}
                  </span>
                </label>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#0D47A1] text-white rounded-full hover:bg-[#1159B8]"
                >
                  Upload &amp; Process
                </button>
              </form>
              {uploadLoading && (
                <p className="mt-2 text-gray-500">
                  Processing PDF, please wait...
                </p>
              )}
              {uploadError && (
                <p className="mt-2 text-red-600">{uploadError}</p>
              )}
            </section>

            {/* Documents List Section */}
            <section className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-semibold text-gray-800">
                  Uploaded Documents
                </h2>
                <button
                  onClick={loadDocuments}
                  className="px-4 py-2 bg-[#0D47A1] text-white rounded hover:bg-[#1159B8]"
                >
                  Refresh List
                </button>
              </div>
              {documentsLoading && (
                <p className="text-gray-500">Loading documents...</p>
              )}
              {documentsError && (
                <p className="text-red-600">{documentsError}</p>
              )}
              <div className="overflow-x-auto">
                <table className="w-full border border-gray-300">
                  <thead className="bg-gray-200">
                    <tr>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        ID
                      </th>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        Filename
                      </th>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        Upload Time
                      </th>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        Pages
                      </th>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        Images
                      </th>
                      <th className="border border-gray-300 px-4 py-2 text-left">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {documents.length === 0 ? (
                      <tr>
                        <td
                          colSpan={6}
                          className="text-center py-4 text-gray-600"
                        >
                          No documents uploaded yet
                        </td>
                      </tr>
                    ) : (
                      documents.map((doc) => (
                        <tr key={doc.id}>
                          <td className="border border-gray-300 px-4 py-2">
                            {doc.id}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {doc.filename}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {new Date(doc.upload_time).toLocaleString()}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {doc.page_count}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {doc.total_images}
                          </td>
                          <td className="border border-gray-300 px-4 py-2 space-x-2">
                            <button
                              onClick={() => viewDocument(doc.id)}
                              className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                            >
                              View
                            </button>
                            <button
                              onClick={() => deleteDocument(doc.id)}
                              className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </section>

            {/* Document Detail View */}
            {selectedDocument && (
              <section className="bg-gray-100 p-6 rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-semibold text-gray-800">
                    Document: {selectedDocument.filename}
                  </h2>
                  <button
                    onClick={() => setSelectedDocument(null)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                  >
                    Back to List
                  </button>
                </div>
                <div>
                  {selectedDocument.pages.length === 0 ? (
                    <p className="text-gray-600">
                      No pages found in this document.
                    </p>
                  ) : (
                    <>
                      <div className="flex justify-center items-center space-x-4 mb-4">
                        <button
                          onClick={prevPage}
                          disabled={currentPageIndex === 0}
                          className="p-2 bg-white border border-gray-300 rounded-full hover:bg-gray-100 disabled:opacity-50"
                        >
                          <FaArrowLeft className="text-gray-600" />
                        </button>
                        <span className="text-gray-800 font-medium">
                          Page {currentPage?.page_number} of{" "}
                          {selectedDocument.pages.length}
                        </span>
                        <button
                          onClick={nextPage}
                          disabled={
                            currentPageIndex ===
                            selectedDocument.pages.length - 1
                          }
                          className="p-2 bg-white border border-gray-300 rounded-full hover:bg-gray-100 disabled:opacity-50"
                        >
                          <FaArrowRight className="text-gray-600" />
                        </button>
                      </div>
                      {currentPage && (
                        <div className="bg-white p-6 rounded-lg shadow">
                          <h3 className="text-xl font-medium text-gray-800 mb-2">
                            Page {currentPage.page_number}
                          </h3>
                          {currentPage.metadata_json && (
                            <div className="bg-gray-100 p-4 rounded mb-4 text-sm text-gray-700">
                              <strong>Metadata:</strong>
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(
                                  JSON.parse(currentPage.metadata_json),
                                  null,
                                  2
                                )}
                              </pre>
                            </div>
                          )}
                          {currentPage.text_content && (
                            <p className="bg-gray-50 p-4 rounded text-gray-700 mb-4 whitespace-pre-line">
                              {currentPage.text_content}
                            </p>
                          )}
                          {currentPage.images &&
                            currentPage.images.length > 0 && (
                              <div>
                                <h4 className="text-lg font-medium text-gray-800 mb-2">
                                  Images ({currentPage.images.length})
                                </h4>
                                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                  {currentPage.images.map((imgData, idx) => {
                                    const base64 = imgData.image_data.replace(
                                      /\s/g,
                                      ""
                                    );
                                    const type =
                                      imgData.image_type?.replace(".", "") ||
                                      "png";
                                    const src = `data:image/${type};base64,${base64}`;
                                    return (
                                      <div
                                        key={idx}
                                        className="border border-gray-300 p-4 rounded"
                                      >
                                        <Image
                                          src={src}
                                          alt={`Page ${
                                            currentPage.page_number
                                          } Image ${idx + 1}`}
                                          width={400}
                                          height={300}
                                          className="w-full object-contain"
                                          unoptimized
                                          onError={(e) => {
                                            // Custom error fallback: replace image with message
                                            const div =
                                              document.createElement("div");
                                            div.className =
                                              "p-4 border-dashed border border-gray-300 text-center text-gray-600";
                                            div.innerHTML = `<p>Failed to load image ${
                                              idx + 1
                                            }</p>`;
                                            e.currentTarget.replaceWith(div);
                                          }}
                                        />
                                        <p className="mt-2 text-sm text-gray-600">
                                          Type: {type}, Size:{" "}
                                          {Math.round(
                                            (base64.length * 0.75) / 1024
                                          )}{" "}
                                          KB
                                        </p>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
 