"""
Integration tests for Schema Migration Database Operations with REAL database operations.
Tests dynamic schema generation, migration patterns, and schema change management.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual schema migration operations without mocks
- Validates dynamic schema generation based on allocation strategies
- Tests schema versioning and backward compatibility patterns
- Covers constraint management and index optimization during migrations
"""
import pytest
import pytest_asyncio
import asyncio
import time
import json
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, MetaData, Table, Column, Integer, String, Boolean, DateTime, inspect
from sqlalchemy.schema import CreateTable, DropTable
from sqlalchemy.exc import OperationalError, ProgrammingError

from app.repositories.project_db_repository import ProjectDBRepository
from app.services.auth_service import AuthService
from app.services.project_batch_service import ProjectBatchService
from app.utils.project_db_manager import ProjectDBManager

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.users import users, UserRole

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def schema_migration_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up environment for schema migration testing."""
    # Create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Create multiple allocation strategies for schema testing
    migration_strategies = []
    strategy_configs = [
        {
            "name": "Simple Sequential",
            "type": StrategyType.SEQUENTIAL,
            "annotators": 1,
            "verification": False,
            "custom_fields": {"simple_mode": True}
        },
        {
            "name": "Complex Parallel",
            "type": StrategyType.PARALLEL,
            "annotators": 3,
            "verification": True,
            "custom_fields": {"quality_threshold": 0.9, "cross_validation": True}
        },
        {
            "name": "Advanced Workflow",
            "type": StrategyType.SEQUENTIAL,
            "annotators": 2,
            "verification": True,
            "custom_fields": {"multi_stage": True, "review_levels": 3, "ai_assistance": True}
        }
    ]
    
    for config in strategy_configs:
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_name=config["name"],
            strategy_type=config["type"],
            num_annotators=config["annotators"],
            requires_verification=config["verification"],
            custom_strategy_config=config["custom_fields"]
        )
        test_master_db.add(strategy)
        migration_strategies.append(strategy)
    
    await test_master_db.commit()
    for strategy in migration_strategies:
        await test_master_db.refresh(strategy)
    
    environment.update({
        "migration_strategies": migration_strategies
    })
    
    return environment


class SchemaMigrationHelper:
    """Utility class for schema migration testing."""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
    
    async def get_table_schema(self, table_name: str) -> Optional[Dict[str, Any]]:
        """Get current schema information for a table."""
        try:
            # Get table structure
            inspector = inspect(self.db_session.bind)
            
            # Get columns
            columns = await self.db_session.run_sync(
                lambda sync_session: inspector.get_columns(table_name)
            )
            
            # Get indexes
            indexes = await self.db_session.run_sync(
                lambda sync_session: inspector.get_indexes(table_name)
            )
            
            # Get foreign keys
            foreign_keys = await self.db_session.run_sync(
                lambda sync_session: inspector.get_foreign_keys(table_name)
            )
            
            return {
                "table_name": table_name,
                "columns": columns,
                "indexes": indexes,
                "foreign_keys": foreign_keys
            }
        
        except Exception as e:
            return None
    
    async def table_exists(self, table_name: str) -> bool:
        """Check if a table exists."""
        try:
            result = await self.db_session.execute(
                text(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table_name}')")
            )
            return result.scalar()
        except Exception:
            return False
    
    async def get_all_tables(self) -> List[str]:
        """Get list of all tables in the database."""
        try:
            result = await self.db_session.execute(
                text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
            )
            return [row[0] for row in result.fetchall()]
        except Exception:
            return []
    
    async def execute_ddl(self, ddl_statement: str) -> bool:
        """Execute DDL statement safely."""
        try:
            await self.db_session.execute(text(ddl_statement))
            await self.db_session.commit()
            return True
        except Exception as e:
            await self.db_session.rollback()
            return False


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.regression       # Suite marker - Schema testing
@pytest.mark.high             # Priority marker - P1 (schema changes are critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestDynamicSchemaGeneration:
    """REGRESSION TEST SUITE: Dynamic schema generation and allocation strategies."""
    
    @pytest.mark.asyncio
    async def test_strategy_based_schema_generation_real_database(
        self,
        schema_migration_environment,
        test_master_db: AsyncSession,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test schema generation based on different allocation strategies."""
        migration_strategies = schema_migration_environment["migration_strategies"]
        client = schema_migration_environment["client"]
        
        schema_helper = SchemaMigrationHelper(test_db)
        
        # Test schema generation for each strategy type
        schema_results = []
        
        for i, strategy in enumerate(migration_strategies):
            # Create project with specific strategy
            project = test_factory.projects.create_project(
                client.id,
                strategy.id,
                project_code=f"SCHEMA_TEST_{i}_{int(time.time())}",
                project_type="image",
                project_status="active"
            )
            test_master_db.add(project)
            await test_master_db.commit()
            await test_master_db.refresh(project)
            
            # Get initial schema state
            initial_tables = await schema_helper.get_all_tables()
            
            # Create batches to trigger schema operations
            batch_data = {
                'batch_identifier': f'SCHEMA_BATCH_{i}_{int(time.time())}',
                'total_files': 10,
                'file_list': [f'schema_file_{j}.jpg' for j in range(10)],
                'annotation_count': strategy.num_annotators,
                'custom_batch_config': {
                    'strategy_specific': True,
                    'verification_required': strategy.requires_verification
                }
            }
            
            repository = ProjectDBRepository()
            
            try:
                created_batch = await repository.create_allocation_batch(
                    project.project_code,
                    batch_data
                )
                
                # Get schema state after batch creation
                final_tables = await schema_helper.get_all_tables()
                
                # Analyze schema for allocation strategy requirements
                allocation_batches_schema = await schema_helper.get_table_schema("allocation_batches")
                files_registry_schema = await schema_helper.get_table_schema("files_registry")
                
                schema_result = {
                    "strategy_id": strategy.id,
                    "strategy_type": strategy.strategy_type.value,
                    "num_annotators": strategy.num_annotators,
                    "requires_verification": strategy.requires_verification,
                    "project_code": project.project_code,
                    "batch_created": created_batch is not None,
                    "initial_table_count": len(initial_tables),
                    "final_table_count": len(final_tables),
                    "allocation_batches_schema": allocation_batches_schema,
                    "files_registry_schema": files_registry_schema
                }
                
                schema_results.append(schema_result)
                
            except Exception as e:
                schema_results.append({
                    "strategy_id": strategy.id,
                    "error": str(e),
                    "batch_created": False
                })
        
        # Verify schema generation results
        successful_schemas = [r for r in schema_results if r.get("batch_created")]
        assert len(successful_schemas) >= 2, f"At least 2 schema generations should succeed, got {len(successful_schemas)}"
        
        # Verify schema consistency across strategies
        for result in successful_schemas:
            assert result["allocation_batches_schema"] is not None, "AllocationBatches schema should exist"
            assert result["files_registry_schema"] is not None, "FilesRegistry schema should exist"
            
            # Verify annotation_count field can accommodate strategy requirements
            batch_schema = result["allocation_batches_schema"]
            annotation_count_column = next((col for col in batch_schema["columns"] if col["name"] == "annotation_count"), None)
            assert annotation_count_column is not None, "annotation_count column should exist"
    
    @pytest.mark.asyncio
    async def test_custom_field_schema_adaptation_real_database(
        self,
        schema_migration_environment,
        test_master_db: AsyncSession,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test schema adaptation for custom fields in allocation strategies."""
        migration_strategies = schema_migration_environment["migration_strategies"]
        client = schema_migration_environment["client"]
        
        # Test custom configuration storage and retrieval
        custom_config_tests = []
        
        for strategy in migration_strategies:
            # Create project with custom strategy configuration
            project = test_factory.projects.create_project(
                client.id,
                strategy.id,
                project_code=f"CUSTOM_CONFIG_{strategy.id}_{int(time.time())}",
                project_type="custom"
            )
            test_master_db.add(project)
            await test_master_db.commit()
            await test_master_db.refresh(project)
            
            # Create batch with strategy-specific custom configuration
            custom_batch_config = {
                'strategy_type': strategy.strategy_type.value,
                'custom_workflow': True,
                'metadata_fields': ["confidence_score", "processing_time", "reviewer_notes"]
            }
            
            if strategy.custom_strategy_config:
                custom_batch_config.update(strategy.custom_strategy_config)
            
            batch_data = {
                'batch_identifier': f'CUSTOM_BATCH_{strategy.id}_{int(time.time())}',
                'total_files': 5,
                'file_list': ['custom_file_1.jpg', 'custom_file_2.jpg'],
                'annotation_count': strategy.num_annotators,
                'custom_batch_config': custom_batch_config
            }
            
            repository = ProjectDBRepository()
            
            try:
                created_batch = await repository.create_allocation_batch(
                    project.project_code,
                    batch_data
                )
                
                # Verify custom configuration was stored
                retrieved_batches = await repository.get_allocation_batches(project.project_code)
                
                custom_batch = next((b for b in retrieved_batches if b['id'] == created_batch['id']), None)
                
                custom_config_test = {
                    "strategy_id": strategy.id,
                    "project_code": project.project_code,
                    "batch_created": True,
                    "custom_config_stored": custom_batch is not None and custom_batch.get('custom_batch_config') is not None,
                    "config_data": custom_batch.get('custom_batch_config') if custom_batch else None
                }
                
                # Verify specific configuration fields
                if custom_config_test["custom_config_stored"]:
                    stored_config = custom_batch['custom_batch_config']
                    if isinstance(stored_config, dict):
                        custom_config_test["strategy_type_preserved"] = stored_config.get('strategy_type') == strategy.strategy_type.value
                        custom_config_test["custom_workflow_preserved"] = stored_config.get('custom_workflow') is True
                        custom_config_test["metadata_fields_preserved"] = 'metadata_fields' in stored_config
                
                custom_config_tests.append(custom_config_test)
                
            except Exception as e:
                custom_config_tests.append({
                    "strategy_id": strategy.id,
                    "error": str(e),
                    "batch_created": False
                })
        
        # Verify custom configuration handling
        successful_configs = [t for t in custom_config_tests if t.get("batch_created")]
        assert len(successful_configs) >= 2, f"At least 2 custom config tests should succeed, got {len(successful_configs)}"
        
        # Verify configuration preservation
        for config_test in successful_configs:
            if config_test.get("custom_config_stored"):
                assert config_test.get("strategy_type_preserved", False), f"Strategy type should be preserved for strategy {config_test['strategy_id']}"
                assert config_test.get("custom_workflow_preserved", False), f"Custom workflow should be preserved for strategy {config_test['strategy_id']}"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.regression       # Suite marker - Migration testing
@pytest.mark.critical         # Priority marker - P0 (migrations are critical)
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Migrations take time
class TestSchemaMigrationPatterns:
    """REGRESSION TEST SUITE: Schema migration and versioning patterns."""
    
    @pytest.mark.asyncio
    async def test_backward_compatible_schema_changes_real_database(
        self,
        schema_migration_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test backward compatible schema changes."""
        schema_helper = SchemaMigrationHelper(test_db)
        project = schema_migration_environment["project"]
        
        # Create initial data with current schema
        repository = ProjectDBRepository()
        
        initial_batch_data = {
            'batch_identifier': f'MIGRATION_TEST_INITIAL_{int(time.time())}',
            'total_files': 10,
            'file_list': [f'migration_file_{i}.jpg' for i in range(10)],
            'annotation_count': 2
        }
        
        initial_batch = await repository.create_allocation_batch(
            project.project_code,
            initial_batch_data
        )
        
        # Get initial schema state
        initial_schema = await schema_helper.get_table_schema("allocation_batches")
        initial_column_count = len(initial_schema["columns"]) if initial_schema else 0
        
        # Simulate schema migration by adding optional columns
        migration_steps = [
            {
                "name": "Add priority_level column",
                "ddl": "ALTER TABLE allocation_batches ADD COLUMN IF NOT EXISTS priority_level INTEGER DEFAULT 1",
                "verification_query": "SELECT priority_level FROM allocation_batches LIMIT 1"
            },
            {
                "name": "Add estimated_duration column",
                "ddl": "ALTER TABLE allocation_batches ADD COLUMN IF NOT EXISTS estimated_duration_minutes INTEGER DEFAULT 60",
                "verification_query": "SELECT estimated_duration_minutes FROM allocation_batches LIMIT 1"
            },
            {
                "name": "Add metadata_json column",
                "ddl": "ALTER TABLE allocation_batches ADD COLUMN IF NOT EXISTS metadata_json JSONB DEFAULT '{}'::jsonb",
                "verification_query": "SELECT metadata_json FROM allocation_batches LIMIT 1"
            }
        ]
        
        migration_results = []
        
        for step in migration_steps:
            try:
                # Execute migration step
                success = await schema_helper.execute_ddl(step["ddl"])
                
                if success:
                    # Verify migration worked
                    verification_result = await test_db.execute(text(step["verification_query"]))
                    verification_success = verification_result.fetchone() is not None
                    
                    # Test that old data still works
                    old_batches = await repository.get_allocation_batches(project.project_code)
                    old_data_accessible = len(old_batches) > 0
                    
                    migration_results.append({
                        "step_name": step["name"],
                        "migration_success": True,
                        "verification_success": verification_success,
                        "old_data_accessible": old_data_accessible
                    })
                else:
                    migration_results.append({
                        "step_name": step["name"],
                        "migration_success": False,
                        "error": "DDL execution failed"
                    })
            
            except Exception as e:
                migration_results.append({
                    "step_name": step["name"],
                    "migration_success": False,
                    "error": str(e)
                })
        
        # Verify migration results
        successful_migrations = [r for r in migration_results if r.get("migration_success")]
        assert len(successful_migrations) >= 2, f"At least 2 migrations should succeed, got {len(successful_migrations)}"
        
        # Verify backward compatibility
        for result in successful_migrations:
            assert result.get("old_data_accessible", False), f"Old data should remain accessible after migration: {result['step_name']}"
        
        # Verify new data can be created with enhanced schema
        enhanced_batch_data = {
            'batch_identifier': f'MIGRATION_TEST_ENHANCED_{int(time.time())}',
            'total_files': 15,
            'file_list': [f'enhanced_file_{i}.jpg' for i in range(15)],
            'annotation_count': 3
        }
        
        enhanced_batch = await repository.create_allocation_batch(
            project.project_code,
            enhanced_batch_data
        )
        
        assert enhanced_batch is not None, "Should be able to create batches with enhanced schema"
        
        # Get final schema state
        final_schema = await schema_helper.get_table_schema("allocation_batches")
        final_column_count = len(final_schema["columns"]) if final_schema else 0
        
        assert final_column_count > initial_column_count, f"Schema should have more columns after migration: {final_column_count} vs {initial_column_count}"
    
    @pytest.mark.asyncio
    async def test_constraint_management_during_migration_real_database(
        self,
        schema_migration_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test constraint management during schema migrations."""
        schema_helper = SchemaMigrationHelper(test_db)
        project = schema_migration_environment["project"]
        
        # Create test data
        repository = ProjectDBRepository()
        
        constraint_test_batch = {
            'batch_identifier': f'CONSTRAINT_TEST_{int(time.time())}',
            'total_files': 5,
            'file_list': ['constraint_file_1.jpg', 'constraint_file_2.jpg'],
            'annotation_count': 2
        }
        
        test_batch = await repository.create_allocation_batch(
            project.project_code,
            constraint_test_batch
        )
        
        # Test constraint modifications
        constraint_operations = [
            {
                "name": "Add check constraint for annotation_count",
                "ddl": "ALTER TABLE allocation_batches ADD CONSTRAINT check_annotation_count_positive CHECK (annotation_count > 0)",
                "test_query": "INSERT INTO allocation_batches (batch_identifier, total_files, annotation_count) VALUES ('test_invalid', 1, 0)",
                "should_fail": True
            },
            {
                "name": "Add unique constraint for batch_identifier",
                "ddl": "ALTER TABLE allocation_batches ADD CONSTRAINT unique_batch_identifier UNIQUE (batch_identifier)",
                "test_query": f"INSERT INTO allocation_batches (batch_identifier, total_files, annotation_count) VALUES ('{constraint_test_batch['batch_identifier']}', 1, 1)",
                "should_fail": True
            }
        ]
        
        constraint_results = []
        
        for operation in constraint_operations:
            try:
                # Add constraint
                constraint_success = await schema_helper.execute_ddl(operation["ddl"])
                
                if constraint_success:
                    # Test constraint enforcement
                    try:
                        await test_db.execute(text(operation["test_query"]))
                        await test_db.commit()
                        
                        # If we get here, the constraint didn't work
                        constraint_enforced = not operation["should_fail"]
                        
                        # Rollback the test data
                        await test_db.rollback()
                        
                    except Exception as constraint_error:
                        # Constraint worked as expected
                        constraint_enforced = operation["should_fail"]
                        await test_db.rollback()
                    
                    constraint_results.append({
                        "operation_name": operation["name"],
                        "constraint_added": True,
                        "constraint_enforced": constraint_enforced
                    })
                else:
                    constraint_results.append({
                        "operation_name": operation["name"],
                        "constraint_added": False,
                        "error": "Failed to add constraint"
                    })
            
            except Exception as e:
                constraint_results.append({
                    "operation_name": operation["name"],
                    "constraint_added": False,
                    "error": str(e)
                })
        
        # Verify constraint operations
        successful_constraints = [r for r in constraint_results if r.get("constraint_added")]
        assert len(successful_constraints) >= 1, f"At least 1 constraint should be added successfully, got {len(successful_constraints)}"
        
        # Verify constraint enforcement
        for result in successful_constraints:
            assert result.get("constraint_enforced", False), f"Constraint should be enforced: {result['operation_name']}"
        
        # Verify existing data still works after constraint addition
        existing_batches = await repository.get_allocation_batches(project.project_code)
        assert len(existing_batches) > 0, "Existing data should remain accessible after constraint changes"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.performance      # Suite marker - Index optimization
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Index operations take time
class TestIndexOptimization:
    """PERFORMANCE TEST SUITE: Index management and optimization during schema changes."""
    
    @pytest.mark.asyncio
    async def test_index_creation_and_optimization_real_database(
        self,
        schema_migration_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test index creation and optimization for performance."""
        schema_helper = SchemaMigrationHelper(test_db)
        project = schema_migration_environment["project"]
        
        # Create multiple batches for index testing
        repository = ProjectDBRepository()
        
        # Create batches to have data for index optimization
        test_batches = []
        for i in range(20):  # Create 20 batches for meaningful index testing
            batch_data = {
                'batch_identifier': f'INDEX_TEST_BATCH_{i}_{int(time.time())}',
                'total_files': random.randint(5, 25),
                'file_list': [f'index_file_{i}_{j}.jpg' for j in range(5)],
                'annotation_count': random.randint(1, 3),
                'is_priority': (i % 5 == 0)  # Every 5th batch is priority
            }
            
            created_batch = await repository.create_allocation_batch(
                project.project_code,
                batch_data
            )
            test_batches.append(created_batch)
        
        # Test index creation operations
        index_operations = [
            {
                "name": "Create index on batch_status",
                "ddl": "CREATE INDEX IF NOT EXISTS idx_allocation_batches_status ON allocation_batches (batch_status)",
                "test_query": "SELECT * FROM allocation_batches WHERE batch_status = 'created' ORDER BY batch_status"
            },
            {
                "name": "Create index on is_priority",
                "ddl": "CREATE INDEX IF NOT EXISTS idx_allocation_batches_priority ON allocation_batches (is_priority)",
                "test_query": "SELECT * FROM allocation_batches WHERE is_priority = true ORDER BY is_priority"
            },
            {
                "name": "Create composite index on assignment fields",
                "ddl": "CREATE INDEX IF NOT EXISTS idx_allocation_batches_assignment ON allocation_batches (assignment_count, annotation_count)",
                "test_query": "SELECT * FROM allocation_batches WHERE assignment_count < annotation_count ORDER BY assignment_count, annotation_count"
            },
            {
                "name": "Create index on created_at for temporal queries",
                "ddl": "CREATE INDEX IF NOT EXISTS idx_allocation_batches_created_at ON allocation_batches (created_at)",
                "test_query": "SELECT * FROM allocation_batches WHERE created_at > NOW() - INTERVAL '1 hour' ORDER BY created_at DESC"
            }
        ]
        
        index_results = []
        
        for operation in index_operations:
            try:
                # Measure query performance before index
                before_start = time.time()
                before_result = await test_db.execute(text(operation["test_query"]))
                before_time = time.time() - before_start
                before_count = len(before_result.fetchall())
                
                # Create index
                index_success = await schema_helper.execute_ddl(operation["ddl"])
                
                if index_success:
                    # Measure query performance after index
                    after_start = time.time()
                    after_result = await test_db.execute(text(operation["test_query"]))
                    after_time = time.time() - after_start
                    after_count = len(after_result.fetchall())
                    
                    # Calculate performance improvement
                    performance_improvement = (before_time - after_time) / before_time if before_time > 0 else 0
                    
                    index_results.append({
                        "operation_name": operation["name"],
                        "index_created": True,
                        "before_time": before_time,
                        "after_time": after_time,
                        "performance_improvement": performance_improvement,
                        "result_count_consistent": before_count == after_count
                    })
                else:
                    index_results.append({
                        "operation_name": operation["name"],
                        "index_created": False,
                        "error": "Failed to create index"
                    })
            
            except Exception as e:
                index_results.append({
                    "operation_name": operation["name"],
                    "index_created": False,
                    "error": str(e)
                })
        
        # Verify index creation results
        successful_indexes = [r for r in index_results if r.get("index_created")]
        assert len(successful_indexes) >= 3, f"At least 3 indexes should be created successfully, got {len(successful_indexes)}"
        
        # Verify result consistency after index creation
        for result in successful_indexes:
            assert result.get("result_count_consistent", False), f"Query results should be consistent after index creation: {result['operation_name']}"
        
        # Note: Performance improvement may not be measurable with small datasets
        # In production, indexes would show more significant improvements
        
        # Verify indexes exist in schema
        final_schema = await schema_helper.get_table_schema("allocation_batches")
        if final_schema:
            indexes = final_schema.get("indexes", [])
            created_index_names = [idx["name"] for idx in indexes if idx["name"].startswith("idx_allocation_batches")]
            assert len(created_index_names) >= 2, f"At least 2 custom indexes should exist, found: {created_index_names}"
    
    @pytest.mark.asyncio
    async def test_schema_rollback_capabilities_real_database(
        self,
        schema_migration_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test schema rollback capabilities for migration safety."""
        schema_helper = SchemaMigrationHelper(test_db)
        project = schema_migration_environment["project"]
        
        # Create initial state
        repository = ProjectDBRepository()
        
        rollback_test_batch = {
            'batch_identifier': f'ROLLBACK_TEST_{int(time.time())}',
            'total_files': 8,
            'file_list': [f'rollback_file_{i}.jpg' for i in range(8)],
            'annotation_count': 2
        }
        
        initial_batch = await repository.create_allocation_batch(
            project.project_code,
            rollback_test_batch
        )
        
        # Get initial schema state
        initial_schema = await schema_helper.get_table_schema("allocation_batches")
        initial_columns = [col["name"] for col in initial_schema["columns"]] if initial_schema else []
        
        # Test rollback scenario - add column then remove it
        rollback_steps = [
            {
                "action": "add_column",
                "name": "Add rollback_test_column",
                "ddl": "ALTER TABLE allocation_batches ADD COLUMN rollback_test_column VARCHAR(255) DEFAULT 'test'",
                "rollback_ddl": "ALTER TABLE allocation_batches DROP COLUMN IF EXISTS rollback_test_column"
            },
            {
                "action": "add_index",
                "name": "Add rollback test index",
                "ddl": "CREATE INDEX rollback_test_idx ON allocation_batches (batch_identifier)",
                "rollback_ddl": "DROP INDEX IF EXISTS rollback_test_idx"
            }
        ]
        
        rollback_results = []
        
        for step in rollback_steps:
            try:
                # Apply migration
                migration_success = await schema_helper.execute_ddl(step["ddl"])
                
                if migration_success:
                    # Verify migration applied
                    if step["action"] == "add_column":
                        verify_query = "SELECT rollback_test_column FROM allocation_batches LIMIT 1"
                    else:  # add_index
                        verify_query = "SELECT * FROM allocation_batches WHERE batch_identifier IS NOT NULL LIMIT 1"
                    
                    try:
                        verification_result = await test_db.execute(text(verify_query))
                        migration_verified = verification_result.fetchone() is not None
                    except Exception:
                        migration_verified = False
                    
                    # Test rollback
                    rollback_success = await schema_helper.execute_ddl(step["rollback_ddl"])
                    
                    if rollback_success:
                        # Verify rollback completed
                        if step["action"] == "add_column":
                            try:
                                # This should fail if column was removed
                                await test_db.execute(text("SELECT rollback_test_column FROM allocation_batches LIMIT 1"))
                                rollback_verified = False  # Column still exists
                            except Exception:
                                rollback_verified = True  # Column successfully removed
                        else:  # remove_index
                            rollback_verified = True  # Index removal is harder to verify
                        
                        # Verify original data still accessible
                        original_batches = await repository.get_allocation_batches(project.project_code)
                        data_preserved = len(original_batches) > 0
                        
                        rollback_results.append({
                            "step_name": step["name"],
                            "migration_success": True,
                            "migration_verified": migration_verified,
                            "rollback_success": True,
                            "rollback_verified": rollback_verified,
                            "data_preserved": data_preserved
                        })
                    else:
                        rollback_results.append({
                            "step_name": step["name"],
                            "migration_success": True,
                            "rollback_success": False,
                            "error": "Rollback DDL failed"
                        })
                else:
                    rollback_results.append({
                        "step_name": step["name"],
                        "migration_success": False,
                        "error": "Migration DDL failed"
                    })
            
            except Exception as e:
                rollback_results.append({
                    "step_name": step["name"],
                    "migration_success": False,
                    "error": str(e)
                })
        
        # Verify rollback capabilities
        successful_rollbacks = [r for r in rollback_results if r.get("rollback_success")]
        assert len(successful_rollbacks) >= 1, f"At least 1 rollback should succeed, got {len(successful_rollbacks)}"
        
        # Verify data preservation during rollback
        for result in successful_rollbacks:
            assert result.get("data_preserved", False), f"Data should be preserved during rollback: {result['step_name']}"
        
        # Verify schema returned to original state
        final_schema = await schema_helper.get_table_schema("allocation_batches")
        final_columns = [col["name"] for col in final_schema["columns"]] if final_schema else []
        
        # Should not have rollback test columns
        assert "rollback_test_column" not in final_columns, "Rollback test column should be removed"
        
        # Original columns should still exist
        for col in initial_columns:
            assert col in final_columns, f"Original column {col} should still exist after rollback"
