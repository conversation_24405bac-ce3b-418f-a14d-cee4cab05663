# routers/allocation_strategies.py (async version)
from fastapi import APIRouter, Depends, HTTPException, status, Query # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select
from typing import List, Optional

from core.session_manager import get_master_db_session
from post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from schemas.allocation_strategy_schemas import AllocationStrategyCreate, AllocationStrategyUpdate, AllocationStrategyOut

router = APIRouter(prefix="/allocation-strategies", tags=["allocation_strategies"])

@router.post("", response_model=AllocationStrategyOut, status_code=status.HTTP_201_CREATED)
async def create_strategy(payload: AllocationStrategyCreate, db: AsyncSession = Depends(get_master_db_session)):
    obj = AllocationStrategies(**payload.dict())
    db.add(obj)
    try:
        await db.commit()
    except IntegrityError as e: # type: ignore
        await db.rollback()
        raise HTTPException(status_code=409, detail="strategy_name already exists") from e
    await db.refresh(obj)
    return obj

@router.get("", response_model=List[AllocationStrategyOut])
async def list_strategies(
    db: AsyncSession = Depends(get_master_db_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=200),
    name_like: Optional[str] = None,
    allocation_status: Optional[str] = Query(None, description="Filter by allocation status (active, inactive, deprecated)"),
):
    stmt = select(AllocationStrategies)
    
    # Add status filter for better performance
    if allocation_status:
        stmt = stmt.where(AllocationStrategies.allocation_status == allocation_status)
    
    if name_like:
        stmt = stmt.where(AllocationStrategies.strategy_name.ilike(f"%{name_like}%"))
    
    # Optimize ordering - use id instead of created_at for better performance
    stmt = stmt.order_by(AllocationStrategies.id.desc()).offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

@router.get("/{strategy_id}", response_model=AllocationStrategyOut)
async def get_strategy(strategy_id: int, db: AsyncSession = Depends(get_master_db_session)):
    obj = await db.get(AllocationStrategies, strategy_id)
    if not obj:
        raise HTTPException(404, "Not found")
    return obj

@router.patch("/{strategy_id}", response_model=AllocationStrategyOut)
async def update_strategy(strategy_id: int, payload: AllocationStrategyUpdate, db: AsyncSession = Depends(get_master_db_session)):
    obj = await db.get(AllocationStrategies, strategy_id)
    if not obj:
        raise HTTPException(404, "Not found")
    for k, v in payload.dict(exclude_unset=True).items():
        setattr(obj, k, v)
    try:
        await db.commit()
    except IntegrityError as e:
        await db.rollback()
        raise HTTPException(409, "strategy_name already exists") from e
    await db.refresh(obj)
    return obj

@router.delete("/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_strategy(strategy_id: int, db: AsyncSession = Depends(get_master_db_session)):
    obj = await db.get(AllocationStrategies, strategy_id)
    if not obj:
        raise HTTPException(404, "Not found")
    await db.delete(obj)
    await db.commit()
