from typing import List, Dict, Optional
from datetime import datetime
from pydantic import BaseModel, Field

class ImageInfo(BaseModel):
    """Schema for image information"""
    name: str
    path: str
    size: Optional[int] = None
    type: str = "file"
    url: Optional[str] = None
    label: Optional[str] = None

class BatchInfo(BaseModel):
    """Schema for batch information"""
    id: int
    batch_name: str
    images: List[str]
    image_count: int
    status: str
    username: Optional[str] = None
    assigned_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None

class SaveLabelsRequest(BaseModel):
    """Schema for save labels request"""
    session_id: str
    labels: Dict[str, str]
    verification_mode: bool = False
    is_complete: bool = False
    request_id: Optional[str] = None
    batch_name: Optional[str] = None

class SaveLabelsResponse(BaseModel):
    """Schema for save labels response"""
    success: bool
    error: Optional[str] = None
    message: Optional[str] = None

class ImageResponse(BaseModel):
    """Schema for image response"""
    images: List[ImageInfo]
    session_id: str
    admin_instructions: Optional[str] = None
    verification_mode: bool = False
    batch_name: Optional[str] = None

class ErrorResponse(BaseModel):
    """Schema for error response"""
    detail: str

class SuccessResponse(BaseModel):
    """Schema for success response"""
    success: bool
    message: str
    data: Optional[Dict] = None

class CropImageRequest(BaseModel):
    """Schema for crop image request"""
    image_path: str

class CropImageResponse(BaseModel):
    """Schema for crop image response"""
    success: bool
    message: Optional[str] = None
    path: Optional[str] = None
    error: Optional[str] = None 