from datetime import datetime
from telethon.tl.types import MessageMediaPhoto, MessageMediaDocument, InputMessagesFilterPhotos
from io import BytesIO
from PIL import Image
from typing import List, Optional, Any, Union
import base64
from cache.fetch_data_cache import get_cached_image_data
import os
class ImageHandler:
    def __init__(self, client):
        self.client = client
        self.supported_extensions = {'jpg', 'jpeg', 'png', 'webp', 'gif', 'tif', 'tiff', 'bmp', 'svg', 'ico', 'heic', 'heif'}
        self.supported_mime_prefixes = {'image/'}
        
    async def get_channel_images(self, channel_id: Union[int, str], date: Optional[datetime] = None, limit: int = 100) -> List[Any]:
        """Get all images from a channel with optional date filtering."""
        try:
            # Resolve the channel entity
            try:
                channel = await self.client.get_entity(channel_id)
            except Exception:
                await self.client.get_dialogs()
                channel = await self.client.get_entity(channel_id)

            image_messages = []
            count = 0
            async for msg in self.client.iter_messages(channel, filter=InputMessagesFilterPhotos):
                if not msg.media:
                    continue
                if date:
                    if msg.date.date() == date:
                        image_messages.append(msg)
                        count += 1
                else:
                    image_messages.append(msg)
                    count += 1
                if count >= limit:
                    break
            return image_messages
        except Exception:
            return []

    async def get_channel_dates(self, channel_id: Union[int, str]) -> List[datetime]:
        """Get all unique dates from channel images."""
        try:
            # Resolve channel entity
            try:
                channel = await self.client.get_entity(channel_id)
            except Exception:
                await self.client.get_dialogs()
                channel = await self.client.get_entity(channel_id)

            dates = set()
            async for msg in self.client.iter_messages(channel, filter=InputMessagesFilterPhotos):
                if msg.media:
                    dates.add(msg.date.date())
            return sorted(list(dates), reverse=True)
        except Exception:
            return []

    async def get_image_preview(self, message: Any) -> Optional[bytes]:
        """Get a preview of an image for display."""
        try:
            if hasattr(message, 'media') and isinstance(message.media, (MessageMediaPhoto, MessageMediaDocument)):
                # Check if image is already cached in channel cache first
                if hasattr(message, 'peer_id') and hasattr(message.peer_id, 'channel_id'):
                    channel_id = message.peer_id.channel_id
                elif hasattr(message, 'to_id') and hasattr(message.to_id, 'channel_id'):
                    channel_id = message.to_id.channel_id
                else:
                    # If we can't get channel_id, fall back to original behavior
                    channel_id = None
                
                if channel_id:
                    # Try to get from channel cache first (more efficient)
                    cached_data = await get_cached_image_data(channel_id, message.id)
                    if cached_data and 'image_data' in cached_data:
                        return base64.b64decode(cached_data['image_data'])
                
                # Download image if not cached
                bytes_io = BytesIO()
                await self.client.download_media(message.media, file=bytes_io)
                bytes_data = bytes_io.getvalue()
                
                if bytes_data:
                    processed_data = self._process_image_data(bytes_data)
                    
                    # DON'T cache individual images here anymore
                    # They will be cached when the full channel is cached
                    # This avoids double caching and reduces Redis operations
                    
                    return processed_data
            return None
        except Exception:
            return None

    def _process_image_data(self, bytes_data: bytes) -> bytes:
        """Process image data to create a thumbnail."""
        try:
            img = Image.open(BytesIO(bytes_data))
            
            # For animated GIFs, just grab the first frame
            if getattr(img, 'is_animated', False):
                img.seek(0)
            
            # Resize the image for preview
            max_size = (800, 800)
            img.thumbnail(max_size, Image.LANCZOS)
            
            # Save as JPEG for consistent preview
            thumb_io = BytesIO()
            
            # Make sure it's in RGB mode for JPEG
            if img.mode in ['RGBA', 'LA', 'P']:
                background = Image.new('RGB', img.size, (255, 255, 255))
                background.paste(img, mask=img.split()[3] if img.mode == 'RGBA' else None)
                background.save(thumb_io, format='JPEG', quality=85)
            else:
                img.save(thumb_io, format='JPEG', quality=85)
                
            return thumb_io.getvalue()
        except Exception:
            return bytes_data



    async def get_image_data(self, message: Any, channel_id: Union[int, str]) -> Optional[bytes]:
        """Get image data as bytes, using channel cache only."""
        try:
            if not message.media:
                return None
                
            # Check channel cache only
            cached_data = await get_cached_image_data(channel_id, message.id)
            if cached_data and 'image_data' in cached_data:
                return base64.b64decode(cached_data['image_data'])
            
            # Download if not in channel cache
            bytes_io = BytesIO()
            await self.client.download_media(message.media, file=bytes_io)
            bytes_data = bytes_io.getvalue()
            
            # NO individual caching - only channel cache is used
            # Images will be cached when the full channel is processed
            
            return bytes_data
            
        except Exception:
            return None



    @staticmethod
    def _get_media_extension(media: Any) -> str:
        """Determine the appropriate file extension for media."""
        if hasattr(media, 'photo'):
            return '.jpg'
        elif hasattr(media, 'document'):
            doc = media.document
            for attr in doc.attributes:
                if hasattr(attr, 'file_name'):
                    _, ext = os.path.splitext(attr.file_name)
                    if ext:
                        return ext
            
            if hasattr(doc, 'mime_type'):
                mime_to_ext = {
                    'image/jpeg': '.jpg',
                    'image/png': '.png',
                    'image/gif': '.gif',
                    'image/webp': '.webp',
                    'image/tiff': '.tiff',
                    'image/bmp': '.bmp',
                    'image/svg+xml': '.svg',
                    'image/x-icon': '.ico',
                    'image/heic': '.heic',
                    'image/heif': '.heif'
                }
                return mime_to_ext.get(doc.mime_type, '.jpg')
        
        return '.jpg'  # Default extension 