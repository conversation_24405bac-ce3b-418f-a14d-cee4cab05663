from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, Foreign<PERSON>ey, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from ..base import Base


class UserProjectAccess(Base):
    """
    Manages fine-grained access control and permissions for users within specific projects.
    Enables project-level role assignment and customized user settings per project.
    """
    __tablename__ = 'user_project_access'

    # Primary Key & Relationships
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, 
                    comment='Reference to global user record')
    project_id = Column(Integer, ForeignKey('projects_registry.id', ondelete='CASCADE'), nullable=False, 
                       comment='Reference to specific project')
    database_name = Column(String(100), nullable=False, 
                          comment='Project database name for connection routing')
    
    # Access Control & Permissions
    project_role = Column(String(50), 
                         comment='Project-specific role (annotator, senior_annotator, reviewer, project_manager)')
    
    # Project-Specific Configuration
    project_specific_skills = Column(JSONB, 
                                   comment='User\'s skills specific to this project\'s requirements (may differ from global skills)')
    
    # Access Status & Activity Tracking
    is_active = Column(Boolean, default=True, 
                      comment='Whether user currently has active access to this project')
    assigned_at = Column(DateTime, default=func.current_timestamp(), 
                        comment='When user was granted access to this project')
    last_activity = Column(DateTime, 
                          comment='Last time user performed any action in this project (for activity monitoring)')

    # Relationships
    user = relationship("users", backref="project_access", foreign_keys=[user_id])
    project = relationship("ProjectsRegistry", backref="user_access")

    # Constraints
    __table_args__ = (
        UniqueConstraint('user_id', 'project_id', name='unique_user_project_access'),
    )

    def __repr__(self):
        return f"<UserProjectAccess(user_id={self.user_id}, project_id={self.project_id}, project_role='{self.project_role}')>" 