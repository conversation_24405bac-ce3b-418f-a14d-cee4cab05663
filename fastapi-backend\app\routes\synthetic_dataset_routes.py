# """
# Synthetic Dataset Routes.
# Routes for generating synthetic datasets using Agno with <PERSON>.
# """

# from fastapi import APIRouter, Depends, HTTPException, status, Body # type: ignore
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy import select
# from sqlalchemy.exc import SQLAlchemyError
# from post_db.connect import get_db_connection as get_db
# from dependencies.auth import get_current_active_user, check_roles
# from post_db.master_models.users import UserRole
# from post_db.models.knowledge_base import KnowledgeEntry
# from typing import List, Optional
# from pydantic import BaseModel
# from synthetic_dataset.reference_agent import (
#     ReferenceBasedGenerator, 
#     ReferenceDataRequest, 
#     ReferenceDataResponse,
#     DatasetType
# )
# from synthetic_dataset.nonref_agents import (
#     GeminiGenerator,
#     SyntheticDataRequest,
#     SyntheticDataResponse
# )
# from schemas.UserSchemas import SuccessResponse, ErrorResponse
# import logging
# import os

# logger = logging.getLogger('synthetic_dataset_routes')

# router = APIRouter(
#     prefix="/synthetic-dataset",
#     tags=["Synthetic Dataset"],
#     dependencies=[Depends(get_current_active_user), Depends(check_roles([UserRole.ADMIN]))],
#     responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
# )

# class ModelInfo(BaseModel):
#     id: str
#     name: str
#     description: str

# class DatasetTypeInfo(BaseModel):
#     id: str
#     name: str
#     description: str

# @router.get("/models", response_model=List[ModelInfo])
# async def get_available_models():
#     """
#     Get a list of available models for synthetic dataset generation.
#     """
#     try:
#         models = [
#             ModelInfo(
#                 id="gemini-2.0-flash",
#                 name="Gemini 2.0 Flash",
#                 description="Fast and efficient model for generating synthetic datasets"
#             ),
#             ModelInfo(
#                 id="gemini-2.0-pro",
#                 name="Gemini 2.0 Pro",
#                 description="Advanced model for high-quality synthetic dataset generation"
#             )
#         ]
#         return models
#     except Exception as e:
#         logger.error(f"Error in get_available_models: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to fetch available models: {str(e)}"
#         )

# @router.get("/dataset-types", response_model=SuccessResponse)
# async def get_dataset_types():
#     """
#     Get available synthetic dataset types.
#     """
#     try:
#         # Return the available dataset types as a dictionary with descriptions
#         types = {
#             DatasetType.QA.value: "Question-Answer pairs dataset",
#             DatasetType.ARTICLES.value: "Article snippets dataset",
#             DatasetType.CONVERSATION.value: "Realistic conversation dataset",
#             DatasetType.CODE_SNIPPETS.value: "Programming code examples dataset"
#         }
#         return SuccessResponse(success=True, message="Dataset types retrieved successfully", data=types)
#     except Exception as e:
#         logger.error(f"Error in get_dataset_types: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to fetch dataset types: {str(e)}"
#         )

# @router.post("/generate", response_model=ReferenceDataResponse)
# async def generate_synthetic_dataset(
#     request: ReferenceDataRequest = Body(...),
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Generate a synthetic dataset based on the provided parameters and knowledge base entry.
    
#     This endpoint supports multiple dataset types:
#     - Question-Answer pairs (qa)
#     - Article snippets (articles)
#     - Conversations (conversation)
#     - Code snippets (code_snippets)
    
#     The generated data will be structured according to the dataset type and will use
#     the specified knowledge base entry as reference material.
#     """
#     try:
#         # Check if GEMINI_API_KEY is set
#         if not os.environ.get("GEMINI_API_KEY"):
#             return ReferenceDataResponse(
#                 success=False,
#                 error="GEMINI_API_KEY environment variable is not set. Please configure your API key."
#             )
        
#         # Validate request parameters
#         if not request.query or request.query.strip() == "":
#             return ReferenceDataResponse(
#                 success=False,
#                 error="Query parameter cannot be empty"
#             )
        
#         if request.num_samples < 1:
#             return ReferenceDataResponse(
#                 success=False,
#                 error="Number of samples must be at least 1"
#             )
        
#         if request.num_samples > 10:
#             logger.warning(f"Large number of samples requested ({request.num_samples}). This may take longer to process.")
        
#         # Get the knowledge base entry
#         stmt = select(KnowledgeEntry).where(KnowledgeEntry.id == request.knowledge_entry_id)
#         result = await db.execute(stmt)
#         knowledge_entry = result.scalar_one_or_none()
        
#         if not knowledge_entry:
#             return ReferenceDataResponse(
#                 success=False,
#                 error=f"Knowledge base entry with ID {request.knowledge_entry_id} not found"
#             )
        
#         # Initialize the generator
#         generator = ReferenceBasedGenerator()
        
#         # Generate the dataset using the knowledge entry content as reference
#         logger.info(f"Generating {request.dataset_type} dataset with query: '{request.query}', samples: {request.num_samples}, using knowledge entry: {knowledge_entry.title}")
#         response = await generator.generate_dataset(request, knowledge_entry.content)
        
#         if not response.success:
#             logger.error(f"Failed to generate dataset: {response.error}")
        
#         return response
#     except ValueError as e:
#         logger.error(f"Validation error in generate_synthetic_dataset: {str(e)}")
#         return ReferenceDataResponse(
#             success=False,
#             error=str(e)
#         )
#     except Exception as e:
#         logger.error(f"Error in generate_synthetic_dataset: {str(e)}")
#         return ReferenceDataResponse(
#             success=False,
#             error=f"Failed to generate synthetic dataset: {str(e)}"
#         )

# @router.post("/generate-nonref", response_model=SyntheticDataResponse)
# async def generate_nonref_synthetic_dataset(
#     request: SyntheticDataRequest = Body(...)
# ):
#     """
#     Generate a synthetic dataset based on the provided parameters without using knowledge base references.
    
#     This endpoint supports multiple dataset types:
#     - Question-Answer pairs (qa)
#     - Article snippets (articles)
#     - Conversations (conversation)
#     - Code snippets (code_snippets)
    
#     The generated data will be structured according to the dataset type and will be created
#     based on the model's existing knowledge without specific reference material.
#     """
#     try:
#         # Check if GEMINI_API_KEY is set
#         if not os.environ.get("GEMINI_API_KEY"):
#             return SyntheticDataResponse(
#                 success=False,
#                 error="GEMINI_API_KEY environment variable is not set. Please configure your API key."
#             )
        
#         # Validate request parameters
#         if not request.query or request.query.strip() == "":
#             return SyntheticDataResponse(
#                 success=False,
#                 error="Query parameter cannot be empty"
#             )
        
#         if request.num_samples < 1:
#             return SyntheticDataResponse(
#                 success=False,
#                 error="Number of samples must be at least 1"
#             )
        
#         if request.num_samples > 10:
#             logger.warning(f"Large number of samples requested ({request.num_samples}). This may take longer to process.")
        
#         # Initialize the generator
#         generator = GeminiGenerator()
        
#         # Generate the dataset without using a reference
#         logger.info(f"Generating non-reference {request.dataset_type} dataset with query: '{request.query}', samples: {request.num_samples}")
#         response = await generator.generate_dataset(request)
        
#         if not response.success:
#             logger.error(f"Failed to generate non-reference dataset: {response.error}")
        
#         return response
#     except ValueError as e:
#         logger.error(f"Validation error in generate_nonref_synthetic_dataset: {str(e)}")
#         return SyntheticDataResponse(
#             success=False,
#             error=str(e)
#         )
#     except Exception as e:
#         logger.error(f"Error in generate_nonref_synthetic_dataset: {str(e)}")
#         return SyntheticDataResponse(
#             success=False,
#             error=f"Failed to generate non-reference synthetic dataset: {str(e)}"
#         )

# @router.get("/knowledge-entries", response_model=List[dict])
# async def get_knowledge_entries_for_dataset(
#     topic: Optional[str] = None,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Get knowledge base entries that can be used for synthetic dataset generation.
#     Optionally filter by topic.
#     """
#     try:
#         stmt = select(KnowledgeEntry)
        
#         if topic:
#             stmt = stmt.where(KnowledgeEntry.topic == topic)
        
#         result = await db.execute(stmt)
#         entries = result.scalars().all()
        
#         # Convert to simplified format for the frontend
#         result_list = []
#         for entry in entries:
#             result_list.append({
#                 "id": entry.id,
#                 "title": entry.title,
#                 "topic": entry.topic,
#                 # Include a preview of the content (first 100 characters)
#                 "contentPreview": entry.content[:100] + "..." if len(entry.content) > 100 else entry.content,
#                 "source": entry.source
#             })
        
#         return result_list
#     except SQLAlchemyError as e:
#         logger.error(f"Database error retrieving knowledge entries: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Database error retrieving knowledge entries"
#         )
#     except Exception as e:
#         logger.error(f"Error retrieving knowledge entries: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to retrieve knowledge entries: {str(e)}"
#         ) 