from fastapi import APIRouter, HTTPException, status, Request, Depends #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from schemas.AdminSettingsSchemas import NASConnectionRequest, MinIOConnectionRequest
from schemas.UserSchemas import SuccessResponse, ErrorResponse
from schemas.FTPConfig import FTPConfig
from core.config import get_settings
from core.nas_connector import parse_ftp_url, create_nas_connector, get_ftp_connector
from core.minio_utils import create_minio_connector, check_minio_connection
from schemas.MinIOConfig import MinIOConfig
from core.project_context import ProjectContext
from dependencies.auth import get_current_active_user, require_admin
from core.session_manager import get_master_db_session
import os
import logging
import asyncio
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select, func, update

logger = logging.getLogger('admin_connection_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin Connections"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
)

# ---------------------- NAS CONNECTION ----------------------

@router.post("/connect-nas",response_model=SuccessResponse)
async def connect_nas(
    nas_req: NASConnectionRequest,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Connect to NAS using provided credentials and store them in project registry.
    Requires project_code to identify which project to update.
    """
    if not nas_req.project_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="project_code is required to identify the project"
        )

    # Get current user's username
    username = current_user.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to identify current user"
        )

    url = nas_req.nas_url if nas_req.nas_url.startswith("ftp://") else f"ftp://{nas_req.nas_url}"
    host, port = parse_ftp_url(url)

    # Clean and validate credentials
    ftp_username = nas_req.nas_username.strip()
    password = nas_req.nas_password.strip()  # Remove any trailing/leading whitespace

    if not ftp_username or not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username and password are required and cannot be empty"
        )

    # Test connection first
    ftp_cfg = FTPConfig(host=host, port=port, user=ftp_username, pwd=password)
    connector = await create_nas_connector(ftp_cfg)

    if not connector or not await connector.authenticate():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="FTP authentication failed. Please check your username and password. Make sure there are no extra spaces in the credentials."
        )

    # Find project directly using project_code
    from post_db.master_models.clients import Clients
    from post_db.master_models.projects_registry import ProjectsRegistry
    from sqlalchemy import select

    project_result = await db.execute(
        select(ProjectsRegistry).where(ProjectsRegistry.project_code == nas_req.project_code)
    )
    project = project_result.scalar_one_or_none()

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with project_code '{nas_req.project_code}' not found."
        )

    # Update project with NAS credentials
    nas_credentials = {
        "nas_url": url,
        "nas_username": nas_req.nas_username,
        "nas_password": nas_req.nas_password,
        "nas_type": nas_req.nas_type
    }

    project.credentials = nas_credentials
    project.connection_type = "NAS-FTP"
    await db.commit()

    # Cache the credentials with user-specific key
    from core.nas_connector import cache_nas_credentials
    cache_success = await cache_nas_credentials(nas_credentials, username)

    logger.info(f"Updated NAS credentials for project {project.project_code} (user: {username})")
    if not cache_success:
        logger.warning(f"Failed to cache NAS credentials for user {username}")

    return SuccessResponse(
        success=True,
        message=f"Successfully connected to NAS and stored credentials for project {project.project_code}",
        data={
            "project_code": project.project_code,
            "username": username,
            "cache_success": cache_success
        }
    )

@router.post("/disconnect-nas",response_model=SuccessResponse)
async def disconnect_nas(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Clear NAS credentials from current user's client's projects.
    """
    from core.project_context import ProjectContext
    from post_db.master_models.projects_registry import ProjectsRegistry
    from sqlalchemy import update

    # Get current user's username
    username = current_user.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to identify current user"
        )

    # Find user's associated client
    client = await ProjectContext.get_user_client(db, username)
    if not client:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No client associated with current user"
        )

    # Clear NAS credentials from all projects for this client
    result = await db.execute(
        update(ProjectsRegistry)
        .where(ProjectsRegistry.client_id == client.id)
        .where(ProjectsRegistry.connection_type == "NAS-FTP")
        .values(credentials=None, connection_type=None)
    )

    await db.commit()

    # Clear user-specific cache
    from core.nas_connector import clear_nas_credentials_cache
    cache_success = await clear_nas_credentials_cache(username)

    logger.info(f"Cleared NAS credentials from {result.rowcount} projects for client ID {client.id} (user: {username})")
    if not cache_success:
        logger.warning(f"Failed to clear NAS credentials cache for user {username}")

    return SuccessResponse(
        success=True,
        message=f"Disconnected from NAS for {result.rowcount} projects",
        data={
            "username": username,
            "client_id": client.id,
            "client_name": client.name,
            "cache_cleared": cache_success
        }
    )

@router.get("/check-nas-connection", response_model=SuccessResponse)
async def check_nas_connection(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Check if NAS connection is configured for current user's client's projects.
    """
    from post_db.master_models.projects_registry import ProjectsRegistry
    from post_db.master_models.clients import Clients
    from post_db.master_models.users import User
    from sqlalchemy import select, func

    # Get current user's username
    username = current_user.get("sub")
    if not username:
        return SuccessResponse(
            success=False,
            message="Unable to identify current user",
            data={"connected": False, "projects_with_credentials": 0}
        )

    # Find user's associated client using cached lookup
    client = await ProjectContext.get_user_client(db, username)
    if not client:
        return SuccessResponse(
            success=True,
            message="No client associated with current user",
            data={"connected": False, "projects_with_credentials": 0, "username": username}
        )

    # Check how many projects have NAS credentials for this client
    result = await db.execute(
        select(func.count(ProjectsRegistry.id))
        .where(ProjectsRegistry.client_id == client.id)
        .where(ProjectsRegistry.connection_type == "NAS-FTP")
        .where(ProjectsRegistry.credentials.isnot(None))
    )

    projects_with_credentials = result.scalar_one()

    return SuccessResponse(
        success=True,
        message="NAS connection status",
        data={
            "connected": projects_with_credentials > 0,
            "projects_with_credentials": projects_with_credentials,
            "client_id": client.id,
            "client_name": client.name,
            "username": username
        }
    )

# ---------------------- MINIO CONNECTION ----------------------

@router.post("/connect-minio", response_model=SuccessResponse)
async def connect_minio(
    minio_req: MinIOConnectionRequest,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Connect to MinIO using provided credentials and store them in project registry.
    Requires project_code to identify which project to update.
    """
    if not minio_req.project_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="project_code is required to identify the project"
        )

    # Get current user's username
    username = current_user.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to identify current user"
        )

    # Clean and validate credentials
    access_key = minio_req.minio_access_key.strip()
    secret_key = minio_req.minio_secret_key.strip()
    bucket_name = minio_req.minio_bucket_name.strip()

    if not all([access_key, secret_key, bucket_name]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Access key, secret key, and bucket name are required and cannot be empty"
        )

    # Test connection first
    minio_config = MinIOConfig(
        endpoint=minio_req.minio_endpoint,
        access_key=access_key,
        secret_key=secret_key,
        bucket_name=bucket_name,
        secure=minio_req.minio_secure,
        region=minio_req.minio_region
    )
    
    connector = await create_minio_connector(minio_config)

    if not connector or not await connector.authenticate():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="MinIO authentication failed. Please check your credentials and endpoint."
        )
    project_result = await db.execute(
        select(ProjectsRegistry).where(ProjectsRegistry.project_code == minio_req.project_code)
    )
    project = project_result.scalar_one_or_none()

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with project_code '{minio_req.project_code}' not found."
        )

    # Update project with MinIO credentials
    minio_credentials = {
        "minio_endpoint": minio_req.minio_endpoint,
        "minio_access_key": minio_req.minio_access_key,
        "minio_secret_key": minio_req.minio_secret_key,
        "minio_bucket_name": minio_req.minio_bucket_name,
        "minio_secure": minio_req.minio_secure,
        "minio_region": minio_req.minio_region
    }

    project.credentials = minio_credentials
    project.connection_type = "MinIO"
    await db.commit()

    # Cache the credentials with user-specific key
    from core.minio_utils import cache_minio_credentials
    cache_success = await cache_minio_credentials(minio_credentials, username)

    logger.info(f"Updated MinIO credentials for project {project.project_code} (user: {username})")
    if not cache_success:
        logger.warning(f"Failed to cache MinIO credentials for user {username}")

    return SuccessResponse(
        success=True,
        message=f"Successfully connected to MinIO and stored credentials for project {project.project_code}",
        data={
            "project_code": project.project_code,
            "username": username,
            "cache_success": cache_success,
            "bucket_name": bucket_name
        }
    )

@router.post("/disconnect-minio", response_model=SuccessResponse)
async def disconnect_minio(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """Disconnect MinIO by clearing credentials from all projects for the current user's client"""
    username = current_user.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to identify current user"
        )

    # Find user's associated client
    client = await ProjectContext.get_user_client(db, username)
    if not client:
        return SuccessResponse(
            success=True,
            message="No client associated with current user",
            data={"disconnected": False, "projects_updated": 0, "username": username}
        )

    # Update all projects to clear MinIO credentials
    update_result = await db.execute(
        update(ProjectsRegistry)
        .where(ProjectsRegistry.client_id == client.id)
        .where(ProjectsRegistry.connection_type == "MinIO")
        .values(credentials=None, connection_type=None)
    )

    await db.commit()

    # Clear cached credentials
    from core.minio_utils import clear_minio_credentials_cache
    await clear_minio_credentials_cache(username)

    logger.info(f"Cleared MinIO credentials for client {client.name} (user: {username})")

    return SuccessResponse(
        success=True,
        message="MinIO disconnected successfully",
        data={
            "disconnected": True,
            "projects_updated": update_result.rowcount,
            "client_id": client.id,
            "client_name": client.name,
            "username": username
        }
    )

@router.get("/minio-status", response_model=SuccessResponse)
async def minio_status(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """Check MinIO connection status for the current user's client"""
    username = current_user.get("sub")
    if not username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to identify current user"
        )

    # Find user's associated client using cached lookup
    client = await ProjectContext.get_user_client(db, username)
    if not client:
        return SuccessResponse(
            success=True,
            message="No client associated with current user",
            data={"connected": False, "projects_with_credentials": 0, "username": username}
        )

    # Check how many projects have MinIO credentials for this client
    

    result = await db.execute(
        select(func.count(ProjectsRegistry.id))
        .where(ProjectsRegistry.client_id == client.id)
        .where(ProjectsRegistry.connection_type == "MinIO")
        .where(ProjectsRegistry.credentials.isnot(None))
    )

    projects_with_credentials = result.scalar_one()

    return SuccessResponse(
        success=True,
        message="MinIO connection status",
        data={
            "connected": projects_with_credentials > 0,
            "projects_with_credentials": projects_with_credentials,
            "client_id": client.id,
            "client_name": client.name,
            "username": username
        }
    )

# ---------------------- STORAGE BROWSING ENDPOINTS ----------------------

@router.post("/browse-nas-directory", response_model=SuccessResponse)
async def browse_nas_directory(
    request: dict,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """Browse NAS directory contents"""
    try:
        project_code = request.get("project_code")
        path = request.get("path", "/")
        
        if not project_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_code is required"
            )
        
        # Get project
        from post_db.master_models.projects_registry import ProjectsRegistry
        from sqlalchemy import select
        
        project_result = await db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
        )
        project = project_result.scalar_one_or_none()
        
        if not project or project.connection_type != "NAS-FTP":
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or not configured for NAS"
            )
        
        # Get FTP connector
        from core.nas_connector import get_ftp_connector_from_credentials
        ftp_connector = await get_ftp_connector_from_credentials(project.credentials)
        
        if not ftp_connector:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to connect to NAS"
            )
        
        # List directory contents
        contents = await ftp_connector.list_directory(path)
        
        return SuccessResponse(
            success=True,
            message="Directory contents retrieved",
            data={"objects": contents}
        )
        
    except Exception as e:
        logger.error(f"Error browsing NAS directory: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/browse-minio-objects", response_model=SuccessResponse)
async def browse_minio_objects(
    request: dict,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user)
):
    """Browse MinIO bucket objects"""
    try:
        project_code = request.get("project_code")
        prefix = request.get("prefix", "")
        
        if not project_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="project_code is required"
            )
        
        # Get project
        from post_db.master_models.projects_registry import ProjectsRegistry
        from sqlalchemy import select
        
        project_result = await db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
        )
        project = project_result.scalar_one_or_none()
        
        if not project or project.connection_type != "MinIO":
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or not configured for MinIO"
            )
        
        # Get MinIO connector
        from core.minio_utils import get_project_minio_connector
        logger.info(f"Getting MinIO connector for project {project.project_code}")
        logger.info(f"Project connection_type: {project.connection_type}")
        logger.info(f"Project has credentials: {bool(project.credentials)}")
        
        minio_connector = await get_project_minio_connector(db, project)
        
        if not minio_connector:
            logger.error(f"Failed to get MinIO connector for project {project.project_code}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to connect to MinIO"
            )
        
        logger.info(f"MinIO connector authenticated: {minio_connector.authenticated}")
        
        # List objects with prefix
        logger.info(f"Listing MinIO objects with prefix: '{prefix}'")
        contents = await minio_connector.list_directory(prefix)
        logger.info(f"Found {len(contents)} objects")
        
        return SuccessResponse(
            success=True,
            message="Objects retrieved",
            data={"objects": contents}
        )
        
    except Exception as e:
        logger.error(f"Error browsing MinIO objects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

