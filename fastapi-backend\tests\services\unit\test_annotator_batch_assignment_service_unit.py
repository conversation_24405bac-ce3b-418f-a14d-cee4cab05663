"""
Unit tests for AnnotatorBatchAssignmentService.
Tests batch assignment business logic when annotators click "Start Annotating".

COVERAGE FOCUS:
- Annotator batch assignment logic
- User active project management
- Batch availability and allocation
- Assignment repository integration  
- Business logic validation
- Concurrent assignment handling
- Error handling and edge cases
- Performance with multiple annotators
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from app.services.annotator_batch_assignment_service import AnnotatorBatchAssignmentService
from app.repositories.batch_assignment_repository import BatchAssignmentRepository
from app.post_db.master_models.allocation_strategies import AllocationStrategies

class TestAnnotatorBatchAssignmentService:
    """Test suite for AnnotatorBatchAssignmentService."""
    
    @pytest.fixture
    def service(self):
        """Create service instance with mocked dependencies."""
        with patch('app.services.annotator_batch_assignment_service.BatchAssignmentRepository') as mock_repo:
            service = AnnotatorBatchAssignmentService()
            service.repository = mock_repo.return_value
            return service

        [
            {
                'batch_id': 'BATCH_002',
                'batch_number': 2, 
                'total_files': 45,
                'assigned_count': 0,  # No annotators assigned yet
                'completion_count': 0,
                'status': 'available',
                'created_at': datetime.now() - timedelta(hours=1),
                'priority_score': 90
            }
            {
                'batch_id': 'BATCH_003',
                'batch_number': 3,
                'total_files': 40,
                'assigned_count': 1,  # 1 of 3 annotators assigned
                'completion_count': 0,
                'status': 'in_progress',
                'created_at': datetime.now() - timedelta(minutes=30),
                'priority_score': 85
            }
        ]
    
    @pytest.fixture
    def assignment_history(self):
        """Sample assignment history for testing."""
        return [
            {
                'assignment_id': 1,
                'user_id': 101,
                'batch_id': 'BATCH_COMPLETED_001',
                'assigned_at': datetime.now() - timedelta(days=2),
                'completed_at': datetime.now() - timedelta(days=1),
                'status': 'completed'
            }
            {
                'assignment_id': 2,
                'user_id': 101,
                'batch_id': 'BATCH_IN_PROGRESS_001',
                'assigned_at': datetime.now() - timedelta(hours=3),
                'completed_at': None,
                'status': 'in_progress'
            }
        ]

    # ==================================================================
    # USER ACTIVE PROJECT TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_success(self, service, user_factory):
        """Test successful retrieval of user's active project."""
        
        with patch('app.services.annotator_batch_assignment_service.MasterSessionLocal') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock user query result
            mock_user = MagicMock()
            mock_user.active_project = sample_user_data['active_project']
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = mock_user
            mock_db.execute.return_value = mock_result
            
            result = await service.get_user_active_project(sample_user_data['user_id'])
            
            assert result == sample_user_data['active_project']
            mock_db.execute.assert_called_once()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_user_not_found(self, service):
        """Test handling when user is not found."""
        
        with patch('app.services.annotator_batch_assignment_service.MasterSessionLocal') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock no user found
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = None
            mock_db.execute.return_value = mock_result
            
            result = await service.get_user_active_project(999)  # Non-existent user
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_active_project_no_active_project(self, service, user_factory):
        """Test handling when user has no active project."""
        
        with patch('app.services.annotator_batch_assignment_service.MasterSessionLocal') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Mock user with no active project
            mock_user = MagicMock()
            mock_user.active_project = None
            mock_result = MagicMock()
            mock_result.scalar_one_or_none.return_value = mock_user
            mock_db.execute.return_value = mock_result
            
            result = await service.get_user_active_project(sample_user_data['user_id'])
            
            assert result is None

    # ==================================================================
    # BATCH ASSIGNMENT LOGIC TESTS  
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_next_available_batch_success(self, service, user_factory, project_factory, batch_factory):
        """Test successful assignment of next available batch."""
        
        # Mock repository methods
        service.repository.get_available_batches = AsyncMock(return_value=available_batches)
        service.repository.assign_batch_to_user = AsyncMock(return_value={
            'success': True,
            'assignment_id': 123,
            'batch_id': batch_factory.create_available_batch()['batch_id'],
            'user_id': sample_user_data['user_id'],
            'assigned_at': datetime.now()
        })
        
        # Mock getting user's active project
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            assert result['success'] is True
            assert result['batch_id'] == batch_factory.create_available_batch()['batch_id']
            assert result['user_id'] == sample_user_data['user_id']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_next_batch_no_available_batches(self, service, user_factory, project_factory):
        """Test assignment when no batches are available."""
        
        # Mock repository returning no available batches
        service.repository.get_available_batches = AsyncMock(return_value=[])
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            assert result['success'] is False
            assert 'No available batches' in result['message']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_next_batch_user_no_active_project(self, service, user_factory):
        """Test assignment when user has no active project."""
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = None  # No active project
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            assert result['success'] is False
            assert 'No active project' in result['message'] or 'project not found' in result['message']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_specific_batch_success(self, service, user_factory, project_factory, batch_factory):
        """Test successful assignment of specific batch."""
        
        target_batch = batch_factory.create_in_progress_batch()  # BATCH_002
        
        # Mock repository methods
        service.repository.get_batch_by_id = AsyncMock(return_value=target_batch)
        service.repository.assign_batch_to_user = AsyncMock(return_value={
            'success': True,
            'assignment_id': 124,
            'batch_id': target_batch['batch_id'],
            'user_id': sample_user_data['user_id'],
            'assigned_at': datetime.now()
        })
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_specific_batch(
                sample_user_data['user_id'],
                target_batch['batch_id']
            )
            
            assert result['success'] is True
            assert result['batch_id'] == target_batch['batch_id']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_specific_batch_not_available(self, service, user_factory, project_factory):
        """Test assignment of specific batch that's not available."""
        
        unavailable_batch_id = 'BATCH_UNAVAILABLE'
        
        # Mock repository returning None (batch not found or not available)
        service.repository.get_batch_by_id = AsyncMock(return_value=None)
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_specific_batch(
                sample_user_data['user_id'],
                unavailable_batch_id
            )
            
            assert result['success'] is False
            assert 'not available' in result['message'] or 'not found' in result['message']

    # ==================================================================
    # BATCH PRIORITY AND SELECTION TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_batch_priority_selection(self, service, user_factory, project_factory, batch_factory):
        """Test that batches are assigned based on priority."""
        
        # Sort available_batches by priority (highest first)
        priority_sorted_batches = sorted(
            available_batches, 
            key=lambda x: x['priority_score'], 
            reverse=True
        )
        
        service.repository.get_available_batches = AsyncMock(return_value=priority_sorted_batches)
        service.repository.assign_batch_to_user = AsyncMock(return_value={
            'success': True,
            'batch_id': priority_sorted_batches[0]['batch_id'],  # Highest priority
            'user_id': sample_user_data['user_id']
        })
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            # Should assign the highest priority batch (BATCH_001 with score 100)
            assert result['success'] is True
            assert result['batch_id'] == 'BATCH_001'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_batch_selection_prefers_partially_assigned(self, service, user_factory, project_factory, batch_factory):
        """Test preference for partially assigned batches to complete them faster."""
        
        # BATCH_001 has 2/3 annotators (needs 1 more)
        # BATCH_002 has 0/3 annotators (needs 3 more)
        # Strategy should prefer BATCH_001 to complete it
        
        service.repository.get_available_batches = AsyncMock(return_value=available_batches)
        
        with patch.object(service, 'select_optimal_batch_for_assignment') as mock_select:
            # Should select BATCH_001 (partially assigned) over BATCH_002 (empty)
            mock_select.return_value = batch_factory.create_available_batch()  # BATCH_001
            
            service.repository.assign_batch_to_user = AsyncMock(return_value={
                'success': True,
                'batch_id': batch_factory.create_available_batch()['batch_id'],
                'user_id': sample_user_data['user_id']
            })
            
            with patch.object(service, 'get_user_active_project') as mock_get_project:
                mock_get_project.return_value = project_factory.create_project()['project_code']
                
                result = await service.assign_next_batch(sample_user_data['user_id'])
                
                assert result['batch_id'] == 'BATCH_001'  # Partially assigned batch

    # ==================================================================
    # USER ASSIGNMENT HISTORY TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_assignment_history(self, service, user_factory, assignment_history):
        """Test retrieval of user's assignment history."""
        
        service.repository.get_user_assignment_history = AsyncMock(return_value=assignment_history)
        
        result = await service.get_user_assignment_history(
            sample_user_data['user_id'],
            limit=10
        )
        
        assert len(result) == len(assignment_history)
        assert result[0]['user_id'] == sample_user_data['user_id']
        assert any(assignment['status'] == 'completed' for assignment in result)
        assert any(assignment['status'] == 'in_progress' for assignment in result)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_user_current_assignments(self, service, user_factory, assignment_history):
        """Test retrieval of user's current (in-progress) assignments."""
        
        current_assignments = [
            assignment for assignment in assignment_history 
            if assignment['status'] == 'in_progress'
        ]
        
        service.repository.get_user_current_assignments = AsyncMock(return_value=current_assignments)
        
        result = await service.get_user_current_assignments(sample_user_data['user_id'])
        
        assert len(result) == 1  # Only one in-progress assignment
        assert result[0]['status'] == 'in_progress'
        assert result[0]['completed_at'] is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_check_user_has_active_assignments(self, service, user_factory, assignment_history):
        """Test checking if user has active assignments before new assignment."""
        
        current_assignments = [
            assignment for assignment in assignment_history 
            if assignment['status'] == 'in_progress'
        ]
        
        service.repository.get_user_current_assignments = AsyncMock(return_value=current_assignments)
        
        result = await service.check_user_has_active_assignments(sample_user_data['user_id'])
        
        assert result['has_active_assignments'] is True
        assert result['active_count'] == 1
        assert len(result['active_batch_ids']) == 1

    # ==================================================================
    # CONCURRENT ASSIGNMENT HANDLING
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_concurrent_batch_assignment(self, service, project_factory, batch_factory):
        """Test handling when multiple annotators try to get the same batch."""
        
        batch = batch_factory.create_available_batch()  # BATCH_001 needs 1 more annotator
        user_ids = [101, 102, 103]  # Three users trying to get the same batch
        
        # Mock repository to simulate race condition
        assignment_results = [
            {'success': True, 'user_id': 101},    # First succeeds
            {'success': False, 'error': 'Batch already full'},  # Second fails  
            {'success': False, 'error': 'Batch already full'}   # Third fails
        ]
        
        service.repository.assign_batch_to_user = AsyncMock(side_effect=assignment_results)
        
        results = []
        for user_id in user_ids:
            with patch.object(service, 'get_user_active_project') as mock_get_project:
                mock_get_project.return_value = project_factory.create_project()['project_code']
                
                result = await service.assign_specific_batch(user_id, batch['batch_id'])
                results.append(result)
        
        # Only one should succeed
        successful_assignments = [r for r in results if r.get('success')]
        failed_assignments = [r for r in results if not r.get('success')]
        
        assert len(successful_assignments) == 1
        assert len(failed_assignments) == 2
        assert successful_assignments[0]['user_id'] == 101

    # ==================================================================
    # ALLOCATION STRATEGY COMPLIANCE TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_assignment_against_strategy(self, service, user_factory, project_factory, batch_factory):
        """Test that assignments comply with project allocation strategy."""
        
        strategy = project_factory.create_project()['allocation_strategy']
        batch = batch_factory.create_available_batch()  # Already has 2 annotators
        
        # Should allow assignment since batch needs 1 more (2 + 1 = 3 required)
        service.repository.get_batch_assignment_count = AsyncMock(return_value=2)
        
        with patch.object(service, 'validate_assignment_compliance') as mock_validate:
            mock_validate.return_value = {
                'compliant': True,
                'required_annotators': strategy['annotators_per_batch'],
                'current_annotators': 2,
                'can_assign': True
            }
            
            result = await service.validate_assignment_compliance(
                batch['batch_id'],
                sample_user_data['user_id'],
                strategy
            )
            
            assert result['compliant'] is True
            assert result['can_assign'] is True

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_reject_assignment_when_batch_full(self, service, user_factory, project_factory):
        """Test rejection of assignment when batch is already full."""
        
        strategy = project_factory.create_project()['allocation_strategy']
        full_batch = {
            'batch_id': 'BATCH_FULL',
            'assigned_count': 3,  # Already has required 3 annotators
            'status': 'assigned_complete'
        }
        
        service.repository.get_batch_assignment_count = AsyncMock(return_value=3)
        
        with patch.object(service, 'validate_assignment_compliance') as mock_validate:
            mock_validate.return_value = {
                'compliant': False,
                'required_annotators': strategy['annotators_per_batch'],
                'current_annotators': 3,
                'can_assign': False,
                'reason': 'Batch already has required number of annotators'
            }
            
            result = await service.validate_assignment_compliance(
                full_batch['batch_id'],
                sample_user_data['user_id'],
                strategy
            )
            
            assert result['compliant'] is False
            assert result['can_assign'] is False

    # ==================================================================
    # PERFORMANCE AND LOAD TESTS  
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assignment_performance_with_many_users(self, service, project_factory):
        """Test assignment performance with many concurrent users."""
        
        # Generate many users requesting assignments
        user_count = 50
        user_ids = list(range(1001, 1001 + user_count))
        
        # Mock available batches
        many_batches = []
        for i in range(20):  # 20 available batches
            many_batches.append({
                'batch_id': f'BATCH_{i:03d}',
                'assigned_count': 0,
                'status': 'available'
            })
        
        service.repository.get_available_batches = AsyncMock(return_value=many_batches)
        
        # Mock successful assignments
        service.repository.assign_batch_to_user = AsyncMock(return_value={'success': True})
        
        import time
        start_time = time.time()
        
        assignment_count = 0
        for user_id in user_ids[:30]:  # Test with 30 users
            with patch.object(service, 'get_user_active_project') as mock_get_project:
                mock_get_project.return_value = project_factory.create_project()['project_code']
                
                result = await service.assign_next_batch(user_id)
                if result.get('success'):
                    assignment_count += 1
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Performance assertions
        assert processing_time < 5.0  # Should handle 30 assignments in under 5 seconds
        assert assignment_count > 0  # Some assignments should succeed

    # ==================================================================
    # ERROR HANDLING AND EDGE CASES
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_repository_errors(self, service, user_factory, project_factory):
        """Test handling of repository errors."""
        
        # Mock repository method to raise exception
        service.repository.get_available_batches = AsyncMock(
            side_effect=Exception("Database connection failed")
        )
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            assert result['success'] is False
            assert 'error' in result or 'failed' in result.get('message', '')

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_invalid_user_id(self, service):
        """Test handling of invalid user IDs."""
        
        invalid_user_ids = [0, -1, None, 'invalid_string', 999999]
        
        for invalid_id in invalid_user_ids:
            with patch.object(service, 'get_user_active_project') as mock_get_project:
                mock_get_project.return_value = None  # Invalid user has no project
                
                result = await service.assign_next_batch(invalid_id)
                
                assert result['success'] is False

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_invalid_batch_id(self, service, user_factory, project_factory):
        """Test handling of invalid batch IDs."""
        
        invalid_batch_ids = ['', None, 'INVALID_BATCH_ID', 'BATCH_' + 'X' * 100]
        
        for invalid_id in invalid_batch_ids:
            service.repository.get_batch_by_id = AsyncMock(return_value=None)
            
            with patch.object(service, 'get_user_active_project') as mock_get_project:
                mock_get_project.return_value = project_factory.create_project()['project_code']
                
                result = await service.assign_specific_batch(
                    sample_user_data['user_id'],
                    invalid_id
                )
                
                assert result['success'] is False

    # ==================================================================
    # INTEGRATION WITH REPOSITORY TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_repository_method_calls(self, service, user_factory, project_factory, batch_factory):
        """Test that repository methods are called with correct parameters."""
        
        service.repository.get_available_batches = AsyncMock(return_value=available_batches)
        service.repository.assign_batch_to_user = AsyncMock(return_value={'success': True})
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            await service.assign_next_batch(sample_user_data['user_id'])
            
            # Verify repository methods were called with correct parameters
            service.repository.get_available_batches.assert_called_once_with(
                project_factory.create_project()['project_code']
            )
            service.repository.assign_batch_to_user.assert_called_once()
            
            # Check the parameters passed to assign_batch_to_user
            call_args = service.repository.assign_batch_to_user.call_args
            assert call_args[0][1] == sample_user_data['user_id']  # user_id parameter

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_batch_assignment_with_metadata(self, service, user_factory, project_factory, batch_factory):
        """Test that batch assignments include proper metadata."""
        
        assignment_metadata = {
            'success': True,
            'assignment_id': 555,
            'batch_id': batch_factory.create_available_batch()['batch_id'],
            'user_id': sample_user_data['user_id'],
            'assigned_at': datetime.now(),
            'project_code': project_factory.create_project()['project_code'],
            'batch_priority': batch_factory.create_available_batch()['priority_score']
        }
        
        service.repository.get_available_batches = AsyncMock(return_value=available_batches)
        service.repository.assign_batch_to_user = AsyncMock(return_value=assignment_metadata)
        
        with patch.object(service, 'get_user_active_project') as mock_get_project:
            mock_get_project.return_value = project_factory.create_project()['project_code']
            
            result = await service.assign_next_batch(sample_user_data['user_id'])
            
            assert result['success'] is True
            assert result['assignment_id'] == assignment_metadata['assignment_id']
            assert result['assigned_at'] == assignment_metadata['assigned_at']
            assert result['project_code'] == assignment_metadata['project_code']
