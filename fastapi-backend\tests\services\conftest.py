"""
Shared pytest configuration and fixtures for service tests.
This file now imports centralized factories and fixtures to avoid duplication.
"""

import pytest
import pytest_asyncio
from unittest.mock import MagicMock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
import tempfile
import os
import shutil
from datetime import datetime, timedelta

# Import centralized factories and fixtures
try:
    from .factories import UserFactory, ProjectFactory, BatchFactory, AllocationStrategyFactory, AuthFactory
    from .fixtures import mock_storage_connectors, temp_file_setup, mock_db_session, mock_master_db_session
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.dirname(__file__))
    from factories import UserFactory, ProjectFactory, BatchFactory, AllocationStrategyFactory, AuthFactory
    from fixtures import mock_storage_connectors, temp_file_setup, mock_db_session, mock_master_db_session


# ==================================================================
# CENTRALIZED FACTORY FIXTURES
# ==================================================================

@pytest.fixture
def user_factory():
    """Provide UserFactory for creating test users."""
    return UserFactory


@pytest.fixture  
def project_factory():
    """Provide ProjectFactory for creating test projects."""
    return ProjectFactory


@pytest.fixture
def batch_factory():
    """Provide BatchFactory for creating test batches."""
    return BatchFactory


@pytest.fixture
def allocation_strategy_factory():
    """Provide AllocationStrategyFactory for creating test strategies."""
    return AllocationStrategyFactory


@pytest.fixture
def auth_factory():
    """Provide AuthFactory for creating test auth data."""
    return AuthFactory


# ==================================================================
# CONVENIENCE FIXTURES (using centralized factories)
# ==================================================================

@pytest.fixture
def sample_user_data(user_factory):
    """Create sample user data using centralized factory."""
    return {
        'annotator': user_factory.create_annotator(username='test_annotator', user_id=123),
        'verifier': user_factory.create_verifier(username='test_verifier', user_id=456),
        'admin': user_factory.create_admin(username='test_admin', user_id=789)
    }


@pytest.fixture
def sample_project_data(project_factory):
    """Create sample project data using centralized factory."""
    return {
        'single_annotator': project_factory.create_nas_project('TEST_SINGLE_001'),
        'dual_annotator': project_factory.create_minio_project('TEST_DUAL_002'),
        'three_annotator': project_factory.create_hybrid_project('TEST_THREE_003')
    }


@pytest.fixture
def sample_batch_data(batch_factory):
    """Create sample batch data using centralized factory."""
    return {
        'available_batch': batch_factory.create_available_batch('BATCH_001'),
        'in_progress_batch': batch_factory.create_in_progress_batch('BATCH_002', assigned_count=2, completion_count=1),
        'completed_batch': batch_factory.create_completed_batch('BATCH_003')
    }


@pytest.fixture
def sample_allocation_strategies(allocation_strategy_factory):
    """Create sample allocation strategies using centralized factory."""
    return allocation_strategy_factory.get_all_strategies()


# ==================================================================
# MOCK SYSTEM FIXTURES  
# ==================================================================

@pytest.fixture
def mock_project_db_manager():
    """Mock ProjectDBManager for testing."""
    mock_manager = MagicMock()
    mock_manager.get_project_session.return_value.__aenter__ = AsyncMock()
    mock_manager.get_project_session.return_value.__aexit__ = AsyncMock()
    return mock_manager


@pytest.fixture
def mock_batch_assignment_repository():
    """Mock BatchAssignmentRepository for testing."""
    mock_repo = MagicMock()
    mock_repo.get_available_batches = AsyncMock(return_value=[])
    mock_repo.assign_batch_to_user = AsyncMock(return_value={'success': True})
    mock_repo.get_user_assignment_history = AsyncMock(return_value=[])
    return mock_repo


# ==================================================================
# TEMPORARY FILE SYSTEM FIXTURES
# ==================================================================

@pytest.fixture
def temp_directory_setup():
    """Create temporary directory structure for testing."""
    temp_dir = tempfile.mkdtemp(prefix='test_dir_')
    
    # Create subdirectories
    subdirs = ['images', 'videos', 'documents', 'processed', 'failed']
    for subdir in subdirs:
        os.makedirs(os.path.join(temp_dir, subdir), exist_ok=True)
    
    # Create some test files
    test_files = []
    for i in range(5):
        file_path = os.path.join(temp_dir, 'images', f'test_image_{i}.jpg')
        with open(file_path, 'w') as f:
            f.write(f'test image content {i}')
        test_files.append(file_path)
    
    yield {
        'base_dir': temp_dir,
        'subdirs': subdirs,
        'test_files': test_files
    }
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def mock_file_system():
    """Mock file system operations for testing."""
    return {
        'folders': ['folder1', 'folder2', 'folder3'],
        'files': ['file1.jpg', 'file2.jpg', 'file3.jpg']
    }


# ==================================================================
# EXTERNAL API MOCKS
# ==================================================================

@pytest.fixture
def mock_external_apis():
    """Mock external API responses."""
    return {
        'ai_processing': {
            'success': True,
            'confidence': 0.95,
            'results': ['annotation1', 'annotation2']
        },
        'storage_service': {
            'upload_success': True,
            'download_url': 'https://storage.test.com/file.jpg'
        },
        'notification_service': {
            'sent': True,
            'message_id': 'msg_12345'
        }
    }


# ==================================================================
# PERFORMANCE TESTING FIXTURES
# ==================================================================

@pytest.fixture
def performance_test_config():
    """Configuration for performance testing."""
    return {
        'concurrent_users': 10,
        'requests_per_user': 5,
        'timeout_seconds': 30,
        'acceptable_response_time': 2.0,  # seconds
        'memory_limit_mb': 512
    }


# ==================================================================
# SECURITY TESTING FIXTURES
# ==================================================================

@pytest.fixture
def security_test_payloads():
    """Security testing payloads for injection attacks."""
    return {
        'sql_injection': [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users--"
        ],
        'xss_payloads': [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<svg onload=alert('xss')>"
        ],
        'path_traversal': [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd"
        ]
    }


# ==================================================================
# INTEGRATION TEST FIXTURES
# ==================================================================

@pytest.fixture
def integration_test_config():
    """Configuration for integration testing with real services."""
    return {
        'database_url': 'postgresql://test_user:test_pass@localhost:5432/integration_test_db',
        'minio_endpoint': 'localhost:9000',
        'minio_access_key': 'test_access_key',
        'minio_secret_key': 'test_secret_key',
        'ftp_host': 'localhost',
        'ftp_port': 21,
        'test_timeout': 30,
        'cleanup_after_tests': True
    }


# ==================================================================
# PYTEST CONFIGURATION
# ==================================================================

def pytest_configure(config):
    """Configure pytest with custom markers and settings."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add 'slow' marker to tests that take more than 5 seconds
        if 'slow' in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Add 'integration' marker to tests in integration directory
        if 'integration' in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Add 'unit' marker to tests in unit directory  
        if 'unit' in item.nodeid:
            item.add_marker(pytest.mark.unit)