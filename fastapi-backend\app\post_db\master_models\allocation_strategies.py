from sqlalchemy import <PERSON>umn, Integer, String, TIMESTAMP, Boolean, func, DECIMAL
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ..base import Base
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum

class StrategyType(str, PyEnum):
    """Allocation strategy type enumeration."""
    SEQUENTIAL = "sequential"  # Renamed from SINGLE_PASS
    PARALLEL = "parallel"      # Kept as is

class AllocationStatus(str, PyEnum):
    """Allocation status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"

class AllocationStrategies(Base):
    """
    Master-level allocation strategy templates that define HOW files should be allocated for annotation work.
    These are reusable strategy templates that projects can reference, not tied to specific files.
    """
    __tablename__ = "allocation_strategies"

    # Primary Identity
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique allocation strategy identifier")
    
    # Strategy Identity
    strategy_name = Column(String(255), unique=True, nullable=False, index=True,
                          comment="Human-readable name for this strategy template")
    strategy_type = Column(CaseInsensitiveEnum(StrategyType), default=StrategyType.SEQUENTIAL, nullable=False,
                          comment="Type of allocation strategy (sequential, parallel)")
    # Strategy Description
    description = Column(String, nullable=True,
                        comment="Human-readable description of this strategy")
    
    # Strategy lifecycle/status
    allocation_status = Column(CaseInsensitiveEnum(AllocationStatus), default=AllocationStatus.ACTIVE, nullable=False, index=True,
                               comment="Current strategy status")
    
    # Strategy Configuration
    num_annotators = Column(Integer, default=1, nullable=False,
                           comment="Number of annotators required for this strategy")
    
    # Verification Configuration
    requires_verification = Column(Boolean, default=False, nullable=False,
                                  comment="Whether files using this strategy require verification after annotation")
    
    # AI Configuration (if applicable)
    requires_ai_preprocessing = Column(Boolean, default=False, nullable=False,
                                      comment="Whether AI preprocessing is required before annotation")
    # Quality & Audit Configuration
    requires_audit = Column(Boolean, default=False, nullable=False,
                           comment="Whether files using this strategy require audit after annotation")
    quality_requirements = Column(JSONB, nullable=True,
                                 comment="Specific quality standards for files using this strategy")
    
    
    # Configuration
    configuration = Column(JSONB, nullable=True,
                          comment="Additional configuration options for this strategy")
    
    # Timeline
    created_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                       comment="When strategy template was created")
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now(), nullable=False,
                       comment="When strategy template was last updated")

    # Relationships
    projects = relationship("ProjectsRegistry", back_populates="allocation_strategy")

    def __repr__(self):
        return f"<AllocationStrategies(id={self.id}, strategy_name='{self.strategy_name}', strategy_type={self.strategy_type})>"