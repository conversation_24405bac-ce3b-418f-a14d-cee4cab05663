"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  FaFileAlt,
  FaTable,
  FaImage,
  FaSuperscript,
  FaUserCheck,
  FaDatabase,
  FaArrowRight,
} from "react-icons/fa";

export default function NoteOcrPage() {
  useEffect(() => {
    // Mobile menu toggle
    const toggle = document.querySelector<HTMLButtonElement>(
      ".mobile-menu-toggle"
    );
    const menu = document.querySelector<HTMLDivElement>(".mobile-menu");
    const onToggle = () => menu?.classList.toggle("hidden");
    toggle?.addEventListener("click", onToggle);
    return () => toggle?.removeEventListener("click", onToggle);
  }, []);

  useEffect(() => {
    // Smooth scrolling & close mobile
    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
      anchor.addEventListener("click", (e) => {
        e.preventDefault();
        const id = (e.currentTarget as HTMLAnchorElement).getAttribute("href");
        const el = id && document.querySelector(id!);
        if (el) el.scrollIntoView({ behavior: "smooth", block: "start" });
        document
          .querySelector<HTMLDivElement>(".mobile-menu")
          ?.classList.add("hidden");
      });
    });
  }, []);

  useEffect(() => {
    // Header scroll shadow
    const header = document.querySelector<HTMLElement>("header");
    const onScroll = () => {
      if (window.scrollY > 50) header?.classList.add("shadow-md");
      else header?.classList.remove("shadow-md");
    };
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  useEffect(() => {
    // Tagline typing animation
    const taglineTyping =
      document.querySelector<HTMLElement>(".tagline-typing");
    if (!taglineTyping) return;
    const taglineOptions = [
      'A<span class="smaller">DVANCED</span> OCR',
      'D<span class="smaller">OCUMENT</span> A<span class="smaller">NALYSIS</span>',
    ];
    let taglineIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let plainText = "";
    const tempDiv = document.createElement("div");
    const typeTagline = () => {
      const currentHTML = taglineOptions[taglineIndex];
      tempDiv.innerHTML = currentHTML;
      const fullText = tempDiv.textContent || tempDiv.innerText || "";
      if (!isDeleting) {
        if (charIndex === 0) plainText = "";
        if (charIndex < fullText.length) {
          plainText += fullText.charAt(charIndex++);
          if (taglineIndex === 0) {
            taglineTyping.innerHTML =
              plainText.length <= 1
                ? plainText
                : 'A<span class="smaller">' +
                  plainText.substring(1) +
                  "</span>";
          } else {
            if (charIndex <= 1) taglineTyping.innerHTML = plainText;
            else if (charIndex <= 8)
              taglineTyping.innerHTML =
                'D<span class="smaller">' + plainText.substring(1) + "</span>";
            else if (charIndex <= 9)
              taglineTyping.innerHTML =
                'D<span class="smaller">OCUMENT</span> ';
            else
              taglineTyping.innerHTML =
                'D<span class="smaller">OCUMENT</span> A<span class="smaller">' +
                plainText.substring(10) +
                "</span>";
          }
          setTimeout(typeTagline, 100);
        } else {
          isDeleting = true;
          setTimeout(typeTagline, 2000);
        }
      } else {
        if (charIndex > 0) {
          charIndex--;
          plainText = fullText.substring(0, charIndex);
          if (taglineIndex === 0) {
            taglineTyping.innerHTML =
              plainText.length <= 1
                ? plainText
                : 'A<span class="smaller">' +
                  plainText.substring(1) +
                  "</span>";
          } else {
            if (charIndex <= 1) taglineTyping.innerHTML = plainText;
            else if (charIndex <= 8)
              taglineTyping.innerHTML =
                'D<span class="smaller">' + plainText.substring(1) + "</span>";
            else if (charIndex <= 9)
              taglineTyping.innerHTML =
                'D<span class="smaller">OCUMENT</span> ';
            else
              taglineTyping.innerHTML =
                'D<span class="smaller">OCUMENT</span> A<span class="smaller">' +
                plainText.substring(10) +
                "</span>";
          }
          setTimeout(typeTagline, 50);
        } else {
          isDeleting = false;
          taglineIndex = (taglineIndex + 1) % taglineOptions.length;
          setTimeout(typeTagline, 500);
        }
      }
    };
    setTimeout(typeTagline, 500);
  }, []);

  useEffect(() => {
    // Rotating messages in typing container
    const typingMessageElement =
      document.querySelector<HTMLElement>(".typing-message");
    if (!typingMessageElement) return;
    const messages = [
      "Extract structured content from complex PDF documents.",
      "Process tables, images, text, and formulas from PDFs.",
      "Transform unstructured PDFs into organized data files.",
      "Intelligent document analysis with precision extraction.",
    ];
    let currentMessageIndex = 0;
    const fadeOutAndIn = () => {
      typingMessageElement.style.opacity = "0";
      setTimeout(() => {
        currentMessageIndex = (currentMessageIndex + 1) % messages.length;
        typingMessageElement.textContent = messages[currentMessageIndex];
        typingMessageElement.style.opacity = "1";
        setTimeout(fadeOutAndIn, 5000);
      }, 100);
    };
    setTimeout(fadeOutAndIn, 4000);
  }, []);

  return (
    <>
      <header className="fixed top-0 left-0 w-full z-50 transition-colors duration-300 bg-white/90">
        <div className="max-w-7xl mx-auto py-4 flex items-center justify-between ">
          <Link href="/" className="flex items-center no-underline">
            <Image
              src="/img/PVlogo-1024x780.png"
              alt="Process Venue Logo"
              width={60}
              height={60}
              className="object-contain"
            />
            <div className="ml-3">
              <span className="block text-lg font-semibold text-gray-800">
                End to End Dataset Solutions
              </span>
              <span className="block text-sm text-gray-800">
                Human-AI Collaboration
              </span>
              <span className="inline-block text-xs font-semibold bg-gradient-to-r from-teal-300 to-blue-600 text-black px-2 py-0.5 rounded-full">
                Coming Soon
              </span>
            </div>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <div className="flex items-center space-x-5 bg-white/80 backdrop-blur-md px-4 py-2 rounded-full shadow">
              <Link
                href="#features"
                className="text-gray-700 hover:text-[#0D47A1] transition no-underline"
              >
                Features
              </Link>
              <Link
                href="/note-ocr/pdfextractor"
                className="text-gray-700 hover:text-[#0D47A1] transition no-underline"
              >
                PDF Processing
              </Link>
              <Link
                href="/note-ocr/imageextracter"
                className="text-gray-700 hover:text-[#0D47A1] transition no-underline"
              >
                Image Extraction
              </Link>
              <Link
                href="#how-it-works"
                className="text-gray-700 hover:text-[#0D47A1] transition no-underline"
              >
                How It Works
              </Link>
            </div>
            <Link
              href="/"
              className="bg-[#0D47A1] text-white px-4 py-2 rounded-full hover:bg-[#1159B8] no-underline hover:no-underline"
            >
              Back to Home
            </Link>
          </nav>
          <button className="md:hidden mobile-menu-toggle text-gray-700 focus:outline-none">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </div>
        <div className="mobile-menu md:hidden hidden bg-white shadow-lg">
          <nav className="flex flex-col space-y-2 p-4">
            <a
              href="#features"
              className="text-gray-700 hover:text-[#0D47A1] no-underline"
            >
              Features
            </a>
            <Link
              href="/note-ocr/pdfextractor"
              className="text-gray-700 hover:text-[#0D47A1] no-underline"
            >
              PDF Processing
            </Link>
            <a
              href="/note-ocr/imageextracter"
              className="text-gray-700 hover:text-[#0D47A1] no-underline"
            >
              Image Extraction
            </a>
            <a
              href="#how-it-works"
              className="text-gray-700 hover:text-[#0D47A1] no-underline"
            >
              How It Works
            </a>
            <Link href="/" className="btn-primary mt-2 ">
              Back to Home
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section with bubbles */}
      <section
        id="hero"
        className="relative py-40 bg-gradient-to-br from-blue-50 to-blue-100"
      >
        {/* Decorative circles */}
        <div className="absolute -top-10 -left-10 w-48 h-48 bg-primary rounded-full opacity-20 animate-float" />
        <div className="absolute top-20 right-10 w-32 h-32 bg-secondary rounded-full opacity-20 animate-float" />
        <div className="relative z-10 container text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-secondary mb-6">
            NoteOCR <span className="text-[#0D47A1]">PDF Extraction</span>
          </h1>
          <div className="flex items-center justify-center mb-10">
            <span className="tagline-typing text-xl text-text-secondary"></span>
            <span className="tagline-cursor text-xl text-[#0D47A1] ml-1">
              |
            </span>
          </div>

          <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-8 mb-12">
            {/* Step 1 */}
            <div className="flex flex-col items-center bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition">
              <FaFileAlt className="text-[#0D47A1] text-3xl mb-4" />
              <h3 className="font-semibold text-[#0D47A1] mb-1">
                Element Extraction
              </h3>
              <p className="text-text-secondary text-sm">
                Text, Tables, Images & Formulas
              </p>
            </div>
            <div>
              <FaArrowRight className="text-[#0D47A1] text-2xl" />
            </div>
            {/* Step 2 */}
            <div className="flex flex-col items-center bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition">
              <FaUserCheck className="text-[#0D47A1] text-3xl mb-4" />
              <h3 className="font-semibold text-[#0D47A1] mb-1">
                Human Validation
              </h3>
              <p className="text-text-secondary text-sm">
                Intelligent Layout Detection
              </p>
            </div>
            <div>
              <FaArrowRight className="text-[#0D47A1] text-2xl" />
            </div>
            {/* Step 3 */}
            <div className="flex flex-col items-center bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition">
              <FaDatabase className="text-[#0D47A1] text-3xl mb-4" />
              <h3 className="font-semibold text-[#0D47A1] mb-1">
                Structured Output
              </h3>
              <p className="text-text-secondary text-sm">
                Organized Data Files
              </p>
            </div>
          </div>

          <div className="typing-container mt-6">
            <span className="typing-message inline-block bg-white/80 text-gray-700 px-6 py-3 rounded-full font-medium transition-opacity duration-500">
              Extract structured content from complex PDF documents.
            </span>
          </div>
        </div>
      </section>

      <section id="features" className="bg-white py-24">
        <div className="container">
          <h2 className="section-title">Features</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Text */}
            <div className="feature-card">
              <div className="feature-icon">
                <FaFileAlt className="text-3xl" />
              </div>
              <h3 className="font-semibold text-secondary mb-2">
                Text Extraction
              </h3>
              <p className="text-text-secondary text-sm">
                Accurately extract and organize text content from PDF documents
                with precise layout preservation.
              </p>
            </div>
            {/* Table */}
            <div className="feature-card">
              <div className="feature-icon">
                <FaTable className="text-3xl" />
              </div>
              <h3 className="font-semibold text-secondary mb-2">
                Table Extraction
              </h3>
              <p className="text-text-secondary text-sm">
                Identify and extract tables from PDFs with structure
                preservation, converting them to usable CSV format.
              </p>
            </div>
            {/* Image */}
            <div className="feature-card">
              <div className="feature-icon">
                <FaImage className="text-3xl" />
              </div>
              <h3 className="font-semibold text-secondary mb-2">
                Image Extraction
              </h3>
              <p className="text-text-secondary text-sm">
                Extract embedded images from PDF documents while maintaining
                quality.
              </p>
            </div>
            {/* Formula */}
            <div className="feature-card">
              <div className="feature-icon">
                <FaSuperscript className="text-3xl" />
              </div>
              <h3 className="font-semibold text-secondary mb-2">
                Formula Extraction
              </h3>
              <p className="text-text-secondary text-sm">
                Identify and extract mathematical formulas from technical
                documents with high precision.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section with bubbles */}
      <section
        id="how-it-works"
        className="relative bg-gradient-to-br from-blue-50 to-blue-100 py-24"
      >
        {/* Decorative circles */}
        <div className="absolute -top-12 -right-12 w-52 h-52 bg-green-100 rounded-full opacity-30" />
        <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-yellow-100 rounded-full opacity-30" />
        <div className="relative z-10 container">
          <h2 className="section-title">How It Works</h2>
          <div className="space-y-8 max-w-xl mx-auto">
            {[
              {
                num: "1",
                title: "PDF Document Input",
                desc: "Place PDF documents in the input directory for batch processing or submit individual files.",
              },
              {
                num: "2",
                title: "Automated Processing",
                desc: "The system analyzes document structure, detects elements, and applies specialized extraction for each element type.",
              },
              {
                num: "3",
                title: "Structured Output",
                desc: "Extracted content organized into dedicated folders for text, tables (CSV), images, and formulas with detailed timing reports.",
              },
            ].map((item) => (
              <div key={item.num} className="flex items-start space-x-4">
                <div className="flex-shrink-0 h-10 w-10 bg-[#0D47A1] text-white rounded-full flex items-center justify-center font-bold">
                  {item.num}
                </div>
                <div>
                  <h3 className="font-semibold text-secondary">{item.title}</h3>
                  <p className="text-text-secondary text-sm mt-1">
                    {item.desc}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <footer className="bg-gradient-to-br from-[#0D47A1] to-[#1159B8] text-white py-8">
        <div className="container text-center">
          <p className="text-sm">
            NOTE-OCR • Document Extraction Service • All Rights Reserved • ©{" "}
            {new Date().getFullYear()}
          </p>
        </div>
      </footer>
    </>
  );
}
