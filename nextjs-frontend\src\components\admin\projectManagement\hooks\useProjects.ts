import { useState, useEffect } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import {
  ProjectRegistryResponse,
  ProjectListResponse,
  ProjectFilters,
  Pagination
} from '../types';
import { API_BASE_URL } from "../../../../lib/api";
import {  buildSearchParams, DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '../utils';

export const useProjects = () => {
  const [projects, setProjects] = useState<ProjectRegistryResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<ProjectFilters>({});
  const [pagination, setPagination] = useState<Pagination>({
    page: DEFAULT_PAGE,
    page_size: DEFAULT_PAGE_SIZE,
    total: 0,
    total_pages: 0,
  });
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);
  const [availableStatuses, setAvailableStatuses] = useState<string[]>([]);

  // Fetch projects with filters and pagination
  const fetchProjects = async (page = 1, pageSize = 10, searchFilters = {}) => {
    try {
      setLoading(true);
      // Fetch more projects to ensure we have enough complete ones after filtering
      const params = buildSearchParams(1, 100, searchFilters);

      const response = await authFetch(`${API_BASE_URL}/admin/projects?${params}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: ProjectListResponse = await response.json();
      console.log(data.projects);
      
      // Filter out incomplete projects (those without provisioned database/batches)
      const completeProjects = data.projects.filter(project => 
        project.total_batches && project.total_batches > 0
      );

      // Apply pagination to filtered results
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedProjects = completeProjects.slice(startIndex, endIndex);
      
      setProjects(paginatedProjects);
      setPagination({
        page: page,
        page_size: pageSize,
        total: completeProjects.length,
        total_pages: Math.ceil(completeProjects.length / pageSize),
      });
    } catch (error) {
      console.error("Error fetching projects:", error);
      showToast.error("Failed to load projects");
    } finally {
      setLoading(false);
    }
  };

  // Fetch available filter options
  const fetchFilterOptions = async () => {
    try {
      const [typesResponse, statusesResponse] = await Promise.all([
        authFetch(`${API_BASE_URL}/admin/projects/types`),
        authFetch(`${API_BASE_URL}/admin/projects/statuses`),
      ]);

      if (typesResponse.ok) {
        const types = await typesResponse.json();
        setAvailableTypes(types);
      }

      if (statusesResponse.ok) {
        const statuses = await statusesResponse.json();
        setAvailableStatuses(statuses);
      }
    } catch (error) {
      console.error("Error fetching filter options:", error);
    }
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<ProjectFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({});
  };

  // Update project in the list (useful after operations like activation/deactivation)
  const updateProjectInList = (projectId: number, updates: Partial<ProjectRegistryResponse>) => {
    setProjects(prevProjects => 
      prevProjects.map(project => 
        project.id === projectId 
          ? { ...project, ...updates }
          : project
      )
    );
  };

  // Initial fetch
  useEffect(() => {
    fetchProjects();
    fetchFilterOptions();
  }, []);

  // Fetch when filters or pagination change
  useEffect(() => {
    fetchProjects(pagination.page, pagination.page_size, filters);
  }, [filters, pagination.page, pagination.page_size]);

  return {
    projects,
    loading,
    filters,
    pagination,
    availableTypes,
    availableStatuses,
    fetchProjects,
    handleFilterChange,
    handlePageChange,
    clearFilters,
    updateProjectInList,
  };
};
