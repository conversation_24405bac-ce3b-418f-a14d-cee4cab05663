from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    formats = (
        "+41 2# ### ## ##",
        "+41 3# ### ## ##",
        "+41 4# ### ## ##",
        "+41 5# ### ## ##",
        "+41 6# ### ## ##",
        "+41 7# ### ## ##",
        "+41 8# ### ## ##",
        "+41 9# ### ## ##",
        "+41 (0)2# ### ## ##",
        "+41 (0)3# ### ## ##",
        "+41 (0)4# ### ## ##",
        "+41 (0)5# ### ## ##",
        "+41 (0)6# ### ## ##",
        "+41 (0)7# ### ## ##",
        "+41 (0)8# ### ## ##",
        "+41 (0)9# ### ## ##",
        "02# ### ## ##",
        "03# ### ## ##",
        "04# ### ## ##",
        "05# ### ## ##",
        "06# ### ## ##",
        "07# ### ## ##",
        "08# ### ## ##",
        "09# ### ## ##",
        # see: http://www.bakom.admin.ch/themen/telekom/00479/00607/index.html
        "084# ### ###",
        "0878 ### ###",
        "0900 ### ###",
        "0901 ### ###",
        "0906 ### ###",
    )
