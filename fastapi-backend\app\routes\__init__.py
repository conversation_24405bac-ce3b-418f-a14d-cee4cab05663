"""
Routes package.
This package contains API routes.
"""
from routes.admin_routes import router as admin_router
from routes.auth_routes import router as auth_router
# from routes.auditor_routes import router as auditor_router
# from routes.NoteOCR_routes import router as NoteOCR_router
# from routes.synthetic_dataset_routes import router as synthetic_dataset_router
from routes.admin_route_modules import admin_user_routes
from routes.admin_route_modules import admin_media_routes
from routes.admin_route_modules import admin_connection_routes
from routes.admin_route_modules import admin_annotator_view
from routes.admin_route_modules import admin_instructions
from routes.admin_route_modules import admin_client_routes
from routes.admin_route_modules import admin_project_routes
from routes.annotator_routes import router as annotator_router
# from routes.annotator_supervision_routes import router as annotator_supervision_router
from routes.allocation_strategy_routes import router as allocation_strategy_router
from routes.project_routes_modules.project_routes import router as project_router
from routes.project_routes_modules.project_creation_routes import router as project_creation_router
from routes.project_routes_modules.project_batch_routes import router as project_batch_router
from routes.project_routes_modules.project_batch_allocations_routes import router as project_batch_allocations_router
from routes.project_routes_modules.project_users_routes import router as project_users_router
from routes.project_routes_modules.project_activation_routes import router as project_activation_router
from routes.assignment_routes import router as assignment_router
from routes.assignment_routes.user_routes import router as user_assignment_routes
from routes.assignment_routes.user_assignment_routes import router as user_assignment_operation_routes
from routes.assignment_routes.batch_allocation_routes import router as batch_allocation_router
from routes.ai_processing import  ai_processing_router
from routes.model_endpoints.ai_models_registry_routes import router as ai_models_registry_router


__all__ = [
    "admin_router",
    "auth_router",
    "auditor_router",
    "admin_user_routes",
    "admin_media_routes",
    "admin_connection_routes",
    "admin_annotator_view",
    "admin_instructions",
    "admin_client_routes",
    "admin_project_routes",
    "annotator_router",
    # "annotator_supervision_router",
    "client_router",
    "allocation_strategy_router",
    "project_router",
    "project_creation_router",
    "project_batch_router",
    "project_batch_allocations_router",
    "project_users_router",
    "project_activation_router",
    "assignment_router",
    "user_assignment_routes",
    "user_assignment_operation_routes",
    "batch_allocation_router",
    "ai_processing_router",
    "ai_models_registry_router",
    # "NoteOCR_router",
    # "synthetic_dataset_router",
]