import React from 'react';
import { FaCog, FaPlay, FaPause, FaCalendarAlt, FaExclamationTriangle, FaCheck, FaStop, FaClock, FaLayerGroup } from 'react-icons/fa';
import { ProjectRegistryResponse, StrategyDetails } from '../types';
import { getStatusColor, formatDate } from '../utils';

interface ProjectActionsProps {
  project: ProjectRegistryResponse;
  strategyDetails: StrategyDetails | null;
  loadingStrategy: boolean;
  activationLoading: boolean;
  deadlineLoading: boolean;
  totalBatches: number;
  onActivateProject: (createBatches: boolean) => void;
  onDeactivateProject: () => void;
  onPauseProject: () => void;
  onCompleteProject: () => void;
  onSetDeadline: () => void;
}

export const ProjectActions: React.FC<ProjectActionsProps> = ({
  project,
  strategyDetails,
  loadingStrategy,
  activationLoading,
  deadlineLoading,
  totalBatches,
  onActivateProject,
  onDeactivateProject,
  onPauseProject,
  onCompleteProject,
  onSetDeadline,
}) => {
  const getStatusBadge = () => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', icon: FaPlay },
      inactive: { bg: 'bg-gray-100', text: 'text-gray-800', icon: FaStop },
      paused: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: FaPause },
      completed: { bg: 'bg-blue-100', text: 'text-blue-800', icon: FaCheck },
    };
    
    const config = statusConfig[project.project_status as keyof typeof statusConfig] || statusConfig.inactive;
    const IconComponent = config.icon;
    
    return (
      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${config.bg} ${config.text}`}>
        <IconComponent className="w-3 h-3 mr-1.5" />
        {project.project_status.charAt(0).toUpperCase() + project.project_status.slice(1)}
      </div>
    );
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <FaCog className="w-5 h-5 text-blue-600" />
            </div>
            Project Actions
          </h3>
          {getStatusBadge()}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-6 py-3">
        <div className="flex flex-wrap gap-3 mb-4">
          {project.project_status === 'inactive' ? (
            <button
              onClick={() => onActivateProject(true)}
              disabled={activationLoading || !strategyDetails}
              className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg hover:from-green-700 hover:to-green-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <FaPlay className="w-4 h-4 mr-2" />
              {activationLoading ? 'Activating...' : 'Activate Project'}
            </button>
          ) : project.project_status === 'active' ? (
            <>
              <button
                onClick={onPauseProject}
                disabled={activationLoading}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-amber-500 to-orange-600 text-white font-medium rounded-lg hover:from-amber-600 hover:to-orange-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaPause className="w-4 h-4 mr-2" />
                {activationLoading ? 'Pausing...' : 'Pause Project'}
              </button>
              <button
                onClick={onCompleteProject}
                disabled={activationLoading}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaCheck className="w-4 h-4 mr-2" />
                {activationLoading ? 'Completing...' : 'Mark Complete'}
              </button>
              <button
                onClick={onDeactivateProject}
                disabled={activationLoading}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg hover:from-red-700 hover:to-red-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaStop className="w-4 h-4 mr-2" />
                {activationLoading ? 'Deactivating...' : 'Deactivate Project'}
              </button>
            </>
          ) : project.project_status === 'paused' ? (
            <>
              <button
                onClick={() => onActivateProject(false)}
                disabled={activationLoading || !strategyDetails}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-green-600 to-green-700 text-white font-medium rounded-lg hover:from-green-700 hover:to-green-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaPlay className="w-4 h-4 mr-2" />
                {activationLoading ? 'Resuming...' : 'Resume Project'}
              </button>
              <button
                onClick={onCompleteProject}
                disabled={activationLoading}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaCheck className="w-4 h-4 mr-2" />
                {activationLoading ? 'Completing...' : 'Mark Complete'}
              </button>
              <button
                onClick={onDeactivateProject}
                disabled={activationLoading}
                className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg hover:from-red-700 hover:to-red-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <FaStop className="w-4 h-4 mr-2" />
                {activationLoading ? 'Deactivating...' : 'Deactivate Project'}
              </button>
            </>
          ) : project.project_status === 'completed' ? (
            <div className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 font-medium rounded-lg border border-emerald-200">
              <FaCheck className="w-4 h-4 mr-2" />
              Project Completed
            </div>
          ) : (
            <button
              onClick={onDeactivateProject}
              disabled={activationLoading}
              className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg hover:from-red-700 hover:to-red-800 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <FaStop className="w-4 h-4 mr-2" />
              {activationLoading ? 'Deactivating...' : 'Deactivate Project'}
            </button>
          )}
          
          {/* Set/Change Deadline Button */}
          {project.project_status !== 'completed' && (
            <button
              onClick={onSetDeadline}
              disabled={deadlineLoading}
              className="inline-flex items-center px-5 py-2.5 bg-white border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 hover:border-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <FaCalendarAlt className="w-4 h-4 mr-2" />
              {deadlineLoading ? 'Saving...' : project.project_deadline ? 'Change Deadline' : 'Set Deadline'}
            </button>
          )}
        </div>

        {/* Project Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Deadline Display */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <FaClock className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Project Deadline</p>
                <p className="text-lg font-semibold text-gray-900">
                  {project.project_deadline ? formatDate(project.project_deadline) : 'Not set'}
                </p>
              </div>
            </div>
          </div>
          
          {/* Total Batches Display */}
          <div className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <FaLayerGroup className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Batches</p>
                <p className="text-lg font-semibold text-gray-900">
                  {project.total_batches || totalBatches || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Strategy Section */}
      <div className="border-t border-gray-200 px-6 py-3">
        <h4 className="text-lg font-semibold text-gray-900 mb-2">Allocation Strategy</h4>
        
        {loadingStrategy ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="ml-3 text-gray-600">Loading strategy details...</p>
          </div>
        ) : !strategyDetails ? (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                <FaExclamationTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h5 className="font-medium text-red-900 mb-1">No Strategy Assigned</h5>
                <p className="text-sm text-red-700">
                  This project does not have an allocation strategy assigned. Please assign a strategy before activating the project.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-br from-emerald-50 to-teal-50 border border-emerald-200 rounded-lg p-5">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                <FaCog className="w-5 h-5 text-emerald-600" />
              </div>
              <div>
                <h5 className="font-semibold text-emerald-900">{strategyDetails?.name || 'N/A'}</h5>
                <p className="text-sm text-emerald-700">Active allocation strategy</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Strategy Type:</span>
                  <span className="text-sm text-gray-900 font-medium">{strategyDetails?.type || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Required Annotations:</span>
                  <span className="text-sm text-gray-900 font-medium">{strategyDetails?.num_annotators || 'N/A'}</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Requires Audit:</span>
                  <span className={`text-sm font-medium ${strategyDetails?.requires_audit ? 'text-green-600' : 'text-gray-500'}`}>
                    {strategyDetails?.requires_audit ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Requires Verification:</span>
                  <span className={`text-sm font-medium ${strategyDetails?.requires_verification ? 'text-green-600' : 'text-gray-500'}`}>
                    {strategyDetails?.requires_verification ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
