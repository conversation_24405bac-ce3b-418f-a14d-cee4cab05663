"""
Batch data factory for creating consistent test batches across all service tests.
"""

import time
from typing import Dict, List, Any
from datetime import datetime, timedelta


class BatchFactory:
    """Factory for creating test batch data."""
    
    @staticmethod
    def create_batch(
        batch_id: str = None,
        batch_number: int = None,
        total_files: int = 50,
        status: str = 'available'
    ) -> Dict[str, Any]:
        """Create basic test batch data."""
        timestamp = int(time.time())
        
        return {
            'batch_id': batch_id or f'BATCH_{timestamp % 10000:04d}',
            'batch_number': batch_number or (timestamp % 100),
            'total_files': total_files,
            'assigned_count': 0,
            'completion_count': 0,
            'verification_count': 0,
            'status': status,
            'created_at': datetime.now() - timedelta(hours=2),
            'updated_at': datetime.now() - timedelta(minutes=30),
            'priority_score': 80,
            'content_type': 'image',
            'assigned_annotators': [],
            'assigned_verifiers': []
        }
    
    @staticmethod
    def create_available_batch(
        batch_id: str = None,
        total_files: int = 50
    ) -> Dict[str, Any]:
        """Create batch available for assignment."""
        batch = BatchFactory.create_batch(
            batch_id=batch_id,
            total_files=total_files,
            status='available'
        )
        batch['assigned_count'] = 0
        batch['completion_count'] = 0
        return batch
    
    @staticmethod
    def create_in_progress_batch(
        batch_id: str = None,
        assigned_count: int = 2,
        completion_count: int = 1,
        total_files: int = 50
    ) -> Dict[str, Any]:
        """Create batch that's in progress."""
        batch = BatchFactory.create_batch(
            batch_id=batch_id,
            total_files=total_files,
            status='in_progress'
        )
        batch['assigned_count'] = assigned_count
        batch['completion_count'] = completion_count
        batch['assigned_annotators'] = [f'annotator_{i+1}' for i in range(assigned_count)]
        return batch
    
    @staticmethod
    def create_completed_batch(
        batch_id: str = None,
        annotation_count: int = 3,
        total_files: int = 50
    ) -> Dict[str, Any]:
        """Create batch that's completed annotation and ready for verification."""
        batch = BatchFactory.create_batch(
            batch_id=batch_id,
            total_files=total_files,
            status='annotation_completed'
        )
        batch['assigned_count'] = annotation_count
        batch['completion_count'] = annotation_count
        batch['annotation_count'] = annotation_count
        batch['verification_count'] = 0
        batch['assigned_annotators'] = [f'annotator_{i+1}' for i in range(annotation_count)]
        batch['completed_at'] = datetime.now() - timedelta(minutes=30)
        return batch
    
    @staticmethod
    def create_verified_batch(
        batch_id: str = None,
        annotation_count: int = 3,
        verification_count: int = 1,
        total_files: int = 50
    ) -> Dict[str, Any]:
        """Create batch that's been verified."""
        batch = BatchFactory.create_batch(
            batch_id=batch_id,
            total_files=total_files,
            status='verification_completed'
        )
        batch['assigned_count'] = annotation_count
        batch['completion_count'] = annotation_count
        batch['annotation_count'] = annotation_count
        batch['verification_count'] = verification_count
        batch['assigned_annotators'] = [f'annotator_{i+1}' for i in range(annotation_count)]
        batch['assigned_verifiers'] = [f'verifier_{i+1}' for i in range(verification_count)]
        batch['completed_at'] = datetime.now() - timedelta(hours=2)
        batch['verified_at'] = datetime.now() - timedelta(minutes=15)
        return batch
    
    @staticmethod
    def create_batch_set(
        count: int = 5,
        project_code: str = 'TEST_PROJECT'
    ) -> List[Dict[str, Any]]:
        """Create a set of batches with different statuses."""
        batches = []
        timestamp = int(time.time())
        
        for i in range(count):
            batch_id = f'{project_code}_BATCH_{i+1:03d}_{timestamp % 1000}'
            
            if i == 0:
                # Available batch
                batch = BatchFactory.create_available_batch(batch_id)
            elif i == 1:
                # In progress batch
                batch = BatchFactory.create_in_progress_batch(batch_id, assigned_count=2)
            elif i == 2:
                # Completed batch (ready for verification)
                batch = BatchFactory.create_completed_batch(batch_id)
            elif i == 3:
                # Verified batch
                batch = BatchFactory.create_verified_batch(batch_id)
            else:
                # Default available batch
                batch = BatchFactory.create_available_batch(batch_id)
            
            batches.append(batch)
        
        return batches
    
    @staticmethod
    def create_assignment_history(
        user_id: int,
        project_code: str = 'TEST_PROJECT',
        history_count: int = 5
    ) -> List[Dict[str, Any]]:
        """Create batch assignment history for a user."""
        history = []
        timestamp = int(time.time())
        
        for i in range(history_count):
            assignment = {
                'assignment_id': 1000 + i,
                'user_id': user_id,
                'batch_id': f'{project_code}_BATCH_{i+1:03d}',
                'assigned_at': datetime.now() - timedelta(days=i+1),
                'completed_at': datetime.now() - timedelta(days=i) if i < 3 else None,
                'status': 'completed' if i < 3 else 'in_progress',
                'annotation_count': 45 if i < 3 else 12,
                'quality_score': 0.85 + (i * 0.02)
            }
            history.append(assignment)
        
        return history
    
    @staticmethod
    def create_batch_with_files(
        batch_id: str = None,
        file_count: int = 50,
        file_type: str = 'image'
    ) -> Dict[str, Any]:
        """Create batch with file list."""
        batch = BatchFactory.create_batch(batch_id=batch_id, total_files=file_count)
        
        # Generate file list
        extension = {
            'image': 'jpg',
            'video': 'mp4',
            'audio': 'wav',
            'document': 'pdf'
        }.get(file_type, 'jpg')
        
        batch['files'] = [
            {
                'file_path': f'/project/files/file_{i+1:03d}.{extension}',
                'file_name': f'file_{i+1:03d}.{extension}',
                'file_size': 1024 * (100 + i),  # Varying file sizes
                'file_type': file_type,
                'checksum': f'md5_{i+1:08d}'
            }
            for i in range(file_count)
        ]
        
        return batch
