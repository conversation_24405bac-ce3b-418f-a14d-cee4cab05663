"use client";

import { useRouter, useSearchParams } from "next/navigation";
import Dashboard from "@/components/admin/Dashboard";

import AnnotatorFormConfig from "@/components/admin/AnnotatorFormConfig";
import UserManagement from "@/components/admin/UserManagement";
import DataSources from "@/components/admin/DataSources";
import SyntheticData from "@/components/admin/SyntheticData";
import DataDelivery from "@/components/admin/DataDelivery";
import ClientOnboarding from "@/components/admin/ClientOnboarding/ClientOnboarding";
import ProjectsManagement from "@/components/admin/projectManagement";

import AIModelsRegistry from "@/components/admin/AIModelsRegistry";
import AllocationStrategy from "@/components/admin/AllocationStrategy";
import AIProcessing from "@/components/admin/ai_processing/AIProcessing";


export default function AdminClient() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Ensure that view is always a string, default to 'dashboard' if it's null
  const view = searchParams.get("view") ?? "dashboard";

  const validViews = [
    "dashboard",
    "userManagement",
    "ocrDirectory",
    "dataSources",
    "syntheticData",
    "dataDelivery",
    "editInstructions",
    "annotatorFormConfig",
    "clientOnboarding",
    "projects",
    "aiModelsRegistry",
    "allocationStrategy",
    "aiProcessing",
  ];
  const validView = validViews.includes(view) ? view : "dashboard"; // Default to 'dashboard' if invalid

  const handleNavigate = (viewName: string) => {
    router.push(`/admin?view=${viewName}`);
  };

  return (
    <>
      {validView === "dashboard" && <Dashboard onNavigate={handleNavigate} />}
      {validView === "userManagement" && <UserManagement />}
      {validView === "dataSources" && <DataSources />}
      {validView === "syntheticData" && <SyntheticData />}
      {validView === "dataDelivery" && <DataDelivery />}
      {/* {validView === "annotatorFormConfig" && <AnnotatorFormConfig />} */}
      {validView === "clientOnboarding" && <ClientOnboarding />}
      {validView === "projects" && <ProjectsManagement />}
      {validView === "annotatorFormConfig" && <AnnotatorFormConfig selectedClient={undefined} projectCode={undefined} onGoToStep={() => {}} isStepCompleted={false} markStepCompleted={() => {}} />}
      {validView === "aiModelsRegistry" && <AIModelsRegistry />}
      {validView === "aiProcessing" && <AIProcessing />}
      {validView === "allocationStrategy" && <AllocationStrategy />}
    </>
  );
}