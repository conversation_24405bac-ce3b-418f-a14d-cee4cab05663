import React from "react";

interface ConnectorCardProps {
  title: string;
  description: string;
  connectButton: React.ReactNode;
  children?: React.ReactNode;
}

export default function ConnectorCard({
  title,
  description,
  connectButton,
  children,
}: ConnectorCardProps) {
  return (
    <div
      className={`
        flex flex-col items-center  rounded-xl 
         transition-all duration-200
        min-h-[120px]
      `}
    >
      <div className="flex items-center space-x-2">
        <span className="text-lg font-semibold text-gray-800">{title}</span>
      </div>
      <p className="text-center text-sm text-gray-500 max-w-xs">{description}</p>
      <div className="flex flex-wrap items-center justify-center gap-2 w-full">
        {connectButton}
        {children}
      </div>
    </div>
  );
}
