export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: 'admin' | 'annotator' | 'auditor';
  status: 'active' | 'suspended';
  lastLogin?: string;
  dateAdded: string;
  annotationMode?: 'manual' | 'verification' | 'supervision';
}

export const users: User[] = [
  { id: 1, username: 'admin_user', email: '<EMAIL>', fullName: 'Admin User', role: 'admin', status: 'active', lastLogin: '2023-05-22 14:30', dateAdded: '2023-01-15' },
  { id: 2, username: 'annotator1', email: '<EMAIL>', fullName: 'John Annotator', role: 'annotator', status: 'active', annotationMode: 'manual', lastLogin: '2023-05-21 09:15', dateAdded: '2023-02-10' },
  { id: 3, username: 'annotator2', email: '<EMAIL>', fullName: '<PERSON>', role: 'annotator', status: 'suspended', annotationMode: 'verification', dateAdded: '2023-03-05' },
  { id: 4, username: 'auditor1', email: '<EMAIL>', fullName: '<PERSON> <PERSON>tor', role: 'auditor', status: 'active', lastLogin: '2023-05-23 11:45', dateAdded: '2023-04-20' },
];

export const countUsersByRole = (users: User[]) => {
  return {
    adminCount: users.filter(user => user.role === 'admin').length,
    annotatorCount: users.filter(user => user.role === 'annotator').length,
    auditorCount: users.filter(user => user.role === 'auditor').length
  };
};

export const filterUsers = (users: User[], searchTerm: string, statusFilter: string) => {
  return users.filter(user => 
    (user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
     user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
     (user.fullName && user.fullName.toLowerCase().includes(searchTerm.toLowerCase())) ||
     user.role.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (statusFilter === 'all' || user.status === statusFilter)
  );
}; 