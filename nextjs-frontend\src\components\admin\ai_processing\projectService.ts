import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { API_BASE_URL } from "@/lib/api";

export interface Project {
  id: number;
  project_code: string;
  project_name: string;
  project_type: string;
  folder_path: string;
  resolved_path: string;
  client_id: number;
  ai_processing: boolean;
  has_storage_credentials: boolean;
}



export const useProjectService = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);


  // Fetch all projects (no caching)
  const fetchProjects = useCallback(async (fileType?: string): Promise<Project[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const url = new URL(`${API_BASE_URL}/ai/projects`);
      if (fileType) {
        url.searchParams.append('file_type', fileType);
      }
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.status}`);
      }

      const data = await response.json();
      const projectList = data.projects || [];
      setProjects(projectList);
      return projectList;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(' Error fetching projects:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load projects: ${errorMessage}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [API_BASE_URL]);

  // Get single project by code
  const getProjectByCode = useCallback(async (projectCode: string): Promise<Project | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/ai/projects/${projectCode}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch project: ${response.status}`);
      }

      const project = await response.json();
      return project;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(` Error fetching project ${projectCode}:`, errorMessage);
      toast.error(`Failed to load project: ${errorMessage}`);
      return null;
    }
  }, [API_BASE_URL]);

  // Refresh projects data
  const refreshProjects = useCallback(async (fileType?: string) => {
    return await fetchProjects(fileType);
  }, [fetchProjects]);

  // Load projects on mount
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return {
    projects,
    isLoading,
    error,
    fetchProjects,
    getProjectByCode,
    refreshProjects,
  };
};

// Custom hook for filtering projects by file type
export const useProjectServiceWithFilter = (fileType?: string) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch projects with file type filter
  const fetchProjects = useCallback(async (): Promise<Project[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const url = new URL(`${API_BASE_URL}/ai/projects`);
      if (fileType) {
        url.searchParams.append('file_type', fileType);
      }
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.status}`);
      }

      const data = await response.json();
      const projectList = data.projects || [];
      setProjects(projectList);
      return projectList;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching projects:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load projects: ${errorMessage}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [fileType]);

  // Refresh projects data
  const refreshProjects = useCallback(async () => {
    return await fetchProjects();
  }, [fetchProjects]);

  // Load projects on mount and when fileType changes
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return {
    projects,
    isLoading,
    error,
    fetchProjects,
    refreshProjects,
  };
};

// Export individual functions for non-hook usage
export const projectService = {
  async fetchProjects(fileType?: string): Promise<Project[]> {

    try {
      const url = new URL(`${API_BASE_URL}/ai/projects`);
      if (fileType) {
        url.searchParams.append('file_type', fileType);
      }
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.status}`);
      }

      const data = await response.json();
      return data.projects || [];

    } catch (err) {
      throw err;
    }
  },

  async getProjectByCode(projectCode: string): Promise<Project | null> {


    try {
      const response = await fetch(`${API_BASE_URL}/ai/projects/${projectCode}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch project: ${response.status}`);
      }

      return await response.json();

    } catch (err) {
      throw err;
    }
  },
};
