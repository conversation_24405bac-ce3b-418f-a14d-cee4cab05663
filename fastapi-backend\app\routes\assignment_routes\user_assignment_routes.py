"""
Routes for assigning users to projects with specific roles.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update, insert
from typing import List, Dict, Any
import logging

from core.session_manager import get_master_db_session, get_project_db_session
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users as Users
from post_db.master_models.user_project_access import UserProjectAccess
from post_db.master_models.allocation_strategies import AllocationStrategies
from dependencies.auth import get_current_active_user, require_admin
from schemas.ProjectAssignmentSchemas import (
    UserAssignmentRequest,
    UserAssignmentResponse,
    ProjectActivationRequest,
    ProjectActivationResponse,
    ProjectAssignmentSummary
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/projects",
    tags=["Project Assignments"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)]
)


async def get_project_or_404(project_id: int, db: AsyncSession) -> ProjectsRegistry:
    """Helper to fetch project or raise 404 if not found."""
    project = await db.get(ProjectsRegistry, project_id)
    if not project:
        logger.warning(f"Project not found with ID: {project_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    return project


async def get_user_or_404(user_id: int, db: AsyncSession) -> Users:
    """Helper to fetch user or raise 404 if not found."""
    user = await db.get(Users, user_id)
    if not user:
        logger.warning(f"User not found with ID: {user_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"User with ID {user_id} not found")
    return user


async def update_project_database(project: ProjectsRegistry, user_id: int, project_role: str) -> bool:
    """
    Update the project-specific database with user assignment.
    
    Args:
        project: Project registry record
        user_id: User ID to assign
        project_role: Role to assign (annotator, auditor, verifier)
        
    Returns:
        bool: Success status
    """
    try:
        logger.info(f"Starting project database update for user {user_id} with role {project_role} in project {project.project_code}")
        
        # Get user details from master database
        async with get_master_db_context() as master_db:
            user_result = await master_db.execute(
                select(Users).where(Users.id == user_id)
            )
            user = user_result.scalar_one_or_none()
            
            if not user:
                logger.error(f"User with ID {user_id} not found in master database")
                return False
            
            logger.info(f"Found user {user.username} (ID: {user_id}) in master database")
            
        project_code = project.project_code
        
        logger.info(f"Connecting to project database {project.database_name} for project {project_code}")
        
        # Connect to project database
        try:
            async with get_project_db_session(project_code) as project_db:
                from post_db.allocation_models.project_users import ProjectUsers
                
                # Check if user already exists in project database
                user_exists_query = select(ProjectUsers).where(
                    ProjectUsers.user_id == user_id
                )
                user_exists_result = await project_db.execute(user_exists_query)
                existing_user = user_exists_result.scalar_one_or_none()
                
                if existing_user:
                    logger.info(f"Updating existing user record for user {user_id} in project database")
                    # Update existing user record
                    await project_db.execute(
                        update(ProjectUsers)
                        .where(ProjectUsers.user_id == user_id)
                        .values(role=project_role)
                    )
                else:
                    logger.info(f"Creating new user record for user {user_id} in project database")
                    # Create new user record
                    await project_db.execute(
                        insert(ProjectUsers)
                        .values(
                            user_id=user_id,
                            username=user.username,
                            role=project_role
                        )
                    )
                
                # Commit the changes
                await project_db.commit()
                
                logger.info(f"Successfully updated project database {project.database_name} for user {user_id} with role {project_role}")
                return True
                
        except Exception as db_error:
            logger.error(f"Database connection/operation error for project {project_code}: {str(db_error)}")
            return False
        
    except Exception as e:
        logger.error(f"Error updating project database for project {project.project_code}, user {user_id}: {str(e)}")
        return False


@router.post("/{project_id}/annotators", response_model=UserAssignmentResponse)
async def assign_annotators(
    project_id: int = Path(..., description="Project ID"),
    request: UserAssignmentRequest = ...,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Assign annotators to a project.
    
    Args:
        project_id: Project ID
        request: User assignment request with user IDs
        
    Returns:
        Assignment response with success/failure details
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project has allocation strategy
        if not project.allocation_strategy_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project must have an allocation strategy before assigning users"
            )
        
        # Process each user
        assigned_users = []
        failed_assignments = []
        
        for user_id in request.user_ids:
            try:
                # Check if user exists and is an annotator
                user = await get_user_or_404(user_id, db)
                if user.role != "annotator":
                    failed_assignments.append({
                        "user_id": user_id,
                        "reason": f"User is not an annotator (current role: {user.role})"
                    })
                    continue
                
                # Check if user is already assigned to this project
                existing_query = select(UserProjectAccess).where(
                    and_(
                        UserProjectAccess.user_id == user_id,
                        UserProjectAccess.project_id == project_id
                    )
                )
                existing_result = await db.execute(existing_query)
                existing_access = existing_result.scalar_one_or_none()
                
                if existing_access:
                    # Update existing access if needed
                    if existing_access.project_role != "annotator":
                        existing_access.project_role = "annotator"
                        existing_access.is_active = True
                            
                        # Update user's active_project field
                        user.active_project = project.project_code
                        
                        await db.commit()
                        
                        # Update project database
                        project_db_success = await update_project_database(project, user_id, "annotator")
                        
                        if project_db_success:
                            assigned_users.append({
                                "user_id": user_id,
                                "username": user.username,
                                "full_name": user.full_name,
                                "status": "updated"
                            })
                        else:
                            failed_assignments.append({
                                "user_id": user_id,
                                "reason": "Failed to update project database"
                            })
                    else:
                        # Already assigned as annotator
                        assigned_users.append({
                            "user_id": user_id,
                            "username": user.username,
                            "full_name": user.full_name,
                            "status": "already_assigned"
                        })
                else:
                    # Create new access record
                    new_access = UserProjectAccess(
                        user_id=user_id,
                        project_id=project_id,
                        database_name=project.database_name,
                        project_role="annotator",
                        is_active=True
                    )
                    db.add(new_access)
                    
                    # Update user's active_project field
                    user.active_project = project.project_code
                    
                    await db.commit()
                    
                    # Update project database
                    project_db_success = await update_project_database(project, user_id, "annotator")
                    
                    if project_db_success:
                        assigned_users.append({
                            "user_id": user_id,
                            "username": user.username,
                            "full_name": user.full_name,
                            "status": "assigned"
                        })
                    else:
                        failed_assignments.append({
                            "user_id": user_id,
                            "reason": "Failed to update project database"
                        })
                    
            except Exception as user_error:
                logger.error(f"Error assigning user {user_id}: {str(user_error)}")
                failed_assignments.append({
                    "user_id": user_id,
                    "reason": str(user_error)
                })
        
        # Update active_annotators count in project
        annotator_count_query = select(UserProjectAccess).where(
            and_(
                UserProjectAccess.project_id == project_id,
                UserProjectAccess.project_role == "annotator",
                UserProjectAccess.is_active == True
            )
        )
        annotator_count_result = await db.execute(annotator_count_query)
        active_annotators = len(annotator_count_result.scalars().all())
        
        # Update project record
        project.active_annotators = active_annotators
        await db.commit()
        
        return UserAssignmentResponse(
            success=len(assigned_users) > 0,
            message=f"Successfully assigned {len(assigned_users)} annotators to project",
            assigned_users=assigned_users,
            failed_assignments=failed_assignments
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning annotators: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning annotators: {str(e)}"
        )





@router.post("/{project_id}/verifiers", response_model=UserAssignmentResponse)
async def assign_verifiers(
    project_id: int = Path(..., description="Project ID"),
    request: UserAssignmentRequest = ...,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Assign verifiers to a project.
    
    Args:
        project_id: Project ID
        request: User assignment request with user IDs
        
    Returns:
        Assignment response with success/failure details
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project has allocation strategy
        if not project.allocation_strategy_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project must have an allocation strategy before assigning users"
            )
        
        # Get allocation strategy to check if verification is required
        strategy = await db.get(AllocationStrategies, project.allocation_strategy_id)
        if not strategy or not strategy.requires_verification:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project's allocation strategy does not require verification"
            )
        
        # Process each user
        assigned_users = []
        failed_assignments = []
        
        for user_id in request.user_ids:
            try:
                # Check if user exists
                user = await get_user_or_404(user_id, db)
                
                # Check if user is already assigned to this project
                existing_query = select(UserProjectAccess).where(
                    and_(
                        UserProjectAccess.user_id == user_id,
                        UserProjectAccess.project_id == project_id
                    )
                )
                existing_result = await db.execute(existing_query)
                existing_access = existing_result.scalar_one_or_none()
                
                if existing_access:
                    # Update existing access if needed
                    if existing_access.project_role != "verifier":
                        existing_access.project_role = "verifier"
                        existing_access.is_active = True
                            
                        # Update user's active_project field
                        user.active_project = project.project_code
                        
                        await db.commit()
                        
                        # Update project database
                        project_db_success = await update_project_database(project, user_id, "verifier")
                        
                        if project_db_success:
                            assigned_users.append({
                                "user_id": user_id,
                                "username": user.username,
                                "full_name": user.full_name,
                                "status": "updated"
                            })
                        else:
                            failed_assignments.append({
                                "user_id": user_id,
                                "reason": "Failed to update project database"
                            })
                    else:
                        # Already assigned as verifier
                        assigned_users.append({
                            "user_id": user_id,
                            "username": user.username,
                            "full_name": user.full_name,
                            "status": "already_assigned"
                        })
                else:
                    # Create new access record
                    new_access = UserProjectAccess(
                        user_id=user_id,
                        project_id=project_id,
                        database_name=project.database_name,
                        project_role="verifier",
                        is_active=True
                    )
                    db.add(new_access)
                    
                    # Update user's active_project field
                    user.active_project = project.project_code
                    
                    await db.commit()
                    
                    # Update project database
                    project_db_success = await update_project_database(project, user_id, "verifier")
                    
                    if project_db_success:
                        assigned_users.append({
                            "user_id": user_id,
                            "username": user.username,
                            "full_name": user.full_name,
                            "status": "assigned"
                        })
                    else:
                        failed_assignments.append({
                            "user_id": user_id,
                            "reason": "Failed to update project database"
                        })
                    
            except Exception as user_error:
                logger.error(f"Error assigning user {user_id}: {str(user_error)}")
                failed_assignments.append({
                    "user_id": user_id,
                    "reason": str(user_error)
                })
        
        return UserAssignmentResponse(
            success=len(assigned_users) > 0,
            message=f"Successfully assigned {len(assigned_users)} verifiers to project",
            assigned_users=assigned_users,
            failed_assignments=failed_assignments
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning verifiers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning verifiers: {str(e)}"
        )


@router.get("/{project_id}/summary", response_model=ProjectAssignmentSummary)
async def get_project_assignment_summary(
    project_id: int = Path(..., description="Project ID"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get summary of all user assignments for a project.
    
    Args:
        project_id: Project ID
        
    Returns:
        Project assignment summary
    """
    try:
        # Get project with allocation strategy
        project_query = select(ProjectsRegistry).where(ProjectsRegistry.id == project_id)
        result = await db.execute(project_query)
        project = result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
            
        # Get allocation strategy
        strategy = None
        if project.allocation_strategy_id:
            strategy_result = await db.get(AllocationStrategies, project.allocation_strategy_id)
            if strategy_result:
                strategy = {
                    "id": strategy_result.id,
                    "name": strategy_result.strategy_name,
                    "type": strategy_result.strategy_type,
                    "requires_audit": strategy_result.requires_audit,
                    "num_annotators": strategy_result.num_annotators
                }
        
        # Get all users assigned to this project
        access_query = select(UserProjectAccess).where(UserProjectAccess.project_id == project_id)
        access_result = await db.execute(access_query)
        all_access = access_result.scalars().all()
        
        # Group users by role
        annotators = []
        verifiers = []
        
        for access in all_access:
            # Get user details
            user = await db.get(Users, access.user_id)
            if not user:
                continue
                
            user_info = {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "is_active": access.is_active,
                "assigned_at": access.assigned_at.isoformat() if access.assigned_at else None,
                "last_activity": access.last_activity.isoformat() if access.last_activity else None
            }
            
            # Add to appropriate list based on role
            if access.project_role == "annotator":
                annotators.append(user_info)
            elif access.project_role == "verifier":
                verifiers.append(user_info)
        
        # Get batch info from project database (placeholder)
        batch_info = {
            "total_batches": project.total_batches,
            "completed_batches": 0,  # This would come from project database
            "in_progress_batches": 0  # This would come from project database
        }
        
        return ProjectAssignmentSummary(
            project_id=project.id,
            project_code=project.project_code,
            project_name=project.project_name,
            project_status=project.project_status,
            allocation_strategy=strategy or {},
            annotators=annotators,
            verifiers=verifiers,
            batch_info=batch_info
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project assignment summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting project assignment summary: {str(e)}"
        )
