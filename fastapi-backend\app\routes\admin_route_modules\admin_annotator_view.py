from fastapi import APIRouter, Depends, HTTPException, status, Query #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from dependencies.auth import get_current_active_user, require_admin
from schemas.AdminSettingsSchemas import (
    AnnotatorFormConfigRequest, 
    AnnotatorFormConfigResponse, 
    GetAnnotatorFormConfigResponse,
    FormFieldConfig
)
from schemas.UserSchemas import SuccessResponse, ErrorResponse
import json
import logging

logger = logging.getLogger('admin_annotator_view')

router = APIRouter(
    prefix="/admin",
    tags=["Admin Annotator View"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
)

async def get_project_or_404(project_code: str, db: AsyncSession) -> ProjectsRegistry:
    """Helper to fetch project or raise 404 if not found."""
    result = await db.execute(select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code))
    project_obj = result.scalar_one_or_none()
    if not project_obj:
        logger.warning(f"Project not found with code: {project_code}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    return project_obj

def parse_annotation_requirements(annotation_requirements: dict | str | None) -> dict | None:
    """Parse annotation_requirements safely, return dict or None if invalid."""
    if annotation_requirements is None:
        return None
    
    # If it's already a dict, return it
    if isinstance(annotation_requirements, dict):
        return annotation_requirements
    
    # If it's a string, try to parse as JSON
    try:
        if isinstance(annotation_requirements, str):
            parsed = json.loads(annotation_requirements)
            if isinstance(parsed, dict):
                return parsed
    except (json.JSONDecodeError, TypeError):
        logger.debug("Annotation requirements field is not valid JSON.")
    return None

@router.get("/annotator-form-config", response_model=GetAnnotatorFormConfigResponse)
async def get_annotator_form_config(
    dataset: str = Query(..., description="Project Code (dataset parameter for frontend compatibility)"),
    mode: str = Query("annotation", description="annotation or verification mode (optional)"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """Get form configuration for annotators for a specific project."""
    try:
        project_obj = await get_project_or_404(dataset, db)

        parsed_requirements = parse_annotation_requirements(project_obj.annotation_requirements)
        form_config = []
        if parsed_requirements and "form_config" in parsed_requirements:
            try:
                form_config = [FormFieldConfig(**field) for field in parsed_requirements["form_config"]]
            except Exception as e:
                logger.warning(f"Invalid form_config structure for project {dataset}: {e}")

        response_data = AnnotatorFormConfigResponse(
            id=project_obj.id,
            mode=mode,
            dataset_id=project_obj.project_code,  # Using project_code (string) to match frontend expectations
            dataset_name=project_obj.project_name or "",
            fields=form_config
        )

        return GetAnnotatorFormConfigResponse(
            success=True,
            data=response_data,
            message="Form configuration retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving form configuration for project {dataset}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve form configuration"
        )

@router.post("/annotator-form-config", response_model=SuccessResponse)
async def save_annotator_form_config(
    config_request: AnnotatorFormConfigRequest,
    db: AsyncSession = Depends(get_master_db_session)
):
    """Save form configuration for annotators for a specific project."""
    try:
        # Use the dataset field which now contains project_code
        project_code = config_request.dataset
        project_obj = await get_project_or_404(project_code, db)

        form_config_data = [field.dict() for field in config_request.fields]

        # Parse existing annotation requirements if any
        existing_requirements = parse_annotation_requirements(project_obj.annotation_requirements) or {}
        
        # Preserve existing requirements and add/update form_config
        existing_requirements["form_config"] = form_config_data

        # Update the annotation_requirements column
        project_obj.annotation_requirements = existing_requirements
        await db.commit()

        return SuccessResponse(
            success=True, 
            message=f"Form configuration saved successfully for project: {project_obj.project_name}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving form configuration for project {config_request.dataset}: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save form configuration"
        )