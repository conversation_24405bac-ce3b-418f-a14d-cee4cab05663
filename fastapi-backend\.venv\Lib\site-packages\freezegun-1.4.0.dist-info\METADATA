Metadata-Version: 2.1
Name: freezegun
Version: 1.4.0
Summary: Let your Python tests travel through time
Home-page: https://github.com/spulec/freezegun
Author: <PERSON>
Author-email: <EMAIL>
License: Apache 2.0
Project-URL: Bug Tracker, https://github.com/spulec/freezegun/issues
Project-URL: Changes, https://github.com/spulec/freezegun/blob/master/CHANGELOG
Project-URL: Documentation, https://github.com/spulec/freezegun/blob/master/README.rst
Project-URL: Source Code, https://github.com/spulec/freezegun
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.7
License-File: LICENSE
License-File: AUTHORS.rst
Requires-Dist: python-dateutil >=2.7

FreezeGun: Let your Python tests travel through time
====================================================

.. image:: https://img.shields.io/pypi/v/freezegun.svg
   :target: https://pypi.python.org/pypi/freezegun/
.. image:: https://github.com/spulec/freezegun/workflows/CI/badge.svg
   :target: https://github.com/spulec/freezegun/actions
.. image:: https://coveralls.io/repos/spulec/freezegun/badge.svg?branch=master
   :target: https://coveralls.io/r/spulec/freezegun

FreezeGun is a library that allows your Python tests to travel through time by mocking the datetime module.

Usage
-----

Once the decorator or context manager have been invoked, all calls to datetime.datetime.now(), datetime.datetime.utcnow(), datetime.date.today(), time.time(), time.localtime(), time.gmtime(), and time.strftime() will return the time that has been frozen. time.monotonic() and time.perf_counter() will also be frozen, but as usual it makes no guarantees about their absolute value, only their changes over time.

Decorator
~~~~~~~~~

.. code-block:: python

    from freezegun import freeze_time
    import datetime
    import unittest

    # Freeze time for a pytest style test:

    @freeze_time("2012-01-14")
    def test():
        assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)

    # Or a unittest TestCase - freezes for every test, and set up and tear down code

    @freeze_time("1955-11-12")
    class MyTests(unittest.TestCase):
        def test_the_class(self):
            assert datetime.datetime.now() == datetime.datetime(1955, 11, 12)

    # Or any other class - freezes around each callable (may not work in every case)

    @freeze_time("2012-01-14")
    class Tester(object):
        def test_the_class(self):
            assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)

    # Or method decorator, might also pass frozen time object as kwarg

    class TestUnitTestMethodDecorator(unittest.TestCase):
        @freeze_time('2013-04-09')
        def test_method_decorator_works_on_unittest(self):
            self.assertEqual(datetime.date(2013, 4, 9), datetime.date.today())

        @freeze_time('2013-04-09', as_kwarg='frozen_time')
        def test_method_decorator_works_on_unittest(self, frozen_time):
            self.assertEqual(datetime.date(2013, 4, 9), datetime.date.today())
            self.assertEqual(datetime.date(2013, 4, 9), frozen_time.time_to_freeze.today())

        @freeze_time('2013-04-09', as_kwarg='hello')
        def test_method_decorator_works_on_unittest(self, **kwargs):
            self.assertEqual(datetime.date(2013, 4, 9), datetime.date.today())
            self.assertEqual(datetime.date(2013, 4, 9), kwargs.get('hello').time_to_freeze.today())

Context manager
~~~~~~~~~~~~~~~

.. code-block:: python

    from freezegun import freeze_time

    def test():
        assert datetime.datetime.now() != datetime.datetime(2012, 1, 14)
        with freeze_time("2012-01-14"):
            assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)
        assert datetime.datetime.now() != datetime.datetime(2012, 1, 14)

Raw use
~~~~~~~

.. code-block:: python

    from freezegun import freeze_time

    freezer = freeze_time("2012-01-14 12:00:01")
    freezer.start()
    assert datetime.datetime.now() == datetime.datetime(2012, 1, 14, 12, 0, 1)
    freezer.stop()

Timezones
~~~~~~~~~

.. code-block:: python

    from freezegun import freeze_time

    @freeze_time("2012-01-14 03:21:34", tz_offset=-4)
    def test():
        assert datetime.datetime.utcnow() == datetime.datetime(2012, 1, 14, 3, 21, 34)
        assert datetime.datetime.now() == datetime.datetime(2012, 1, 13, 23, 21, 34)

        # datetime.date.today() uses local time
        assert datetime.date.today() == datetime.date(2012, 1, 13)

    @freeze_time("2012-01-14 03:21:34", tz_offset=-datetime.timedelta(hours=3, minutes=30))
    def test_timedelta_offset():
        assert datetime.datetime.now() == datetime.datetime(2012, 1, 13, 23, 51, 34)

Nice inputs
~~~~~~~~~~~

FreezeGun uses dateutil behind the scenes so you can have nice-looking datetimes.

.. code-block:: python

    @freeze_time("Jan 14th, 2012")
    def test_nice_datetime():
        assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)

Function and generator objects
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

FreezeGun is able to handle function and generator objects.

.. code-block:: python

    def test_lambda():
        with freeze_time(lambda: datetime.datetime(2012, 1, 14)):
            assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)

    def test_generator():
        datetimes = (datetime.datetime(year, 1, 1) for year in range(2010, 2012))

        with freeze_time(datetimes):
            assert datetime.datetime.now() == datetime.datetime(2010, 1, 1)

        with freeze_time(datetimes):
            assert datetime.datetime.now() == datetime.datetime(2011, 1, 1)

        # The next call to freeze_time(datetimes) would raise a StopIteration exception.

``tick`` argument
~~~~~~~~~~~~~~~~~

FreezeGun has an additional ``tick`` argument which will restart time at the given
value, but then time will keep ticking. This is an alternative to the default
parameters which will keep time stopped.

.. code-block:: python

    @freeze_time("Jan 14th, 2020", tick=True)
    def test_nice_datetime():
        assert datetime.datetime.now() > datetime.datetime(2020, 1, 14)

``auto_tick_seconds`` argument
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

FreezeGun has an additional ``auto_tick_seconds`` argument which will autoincrement the
value every time by the given amount from the start value. This is an alternative to the default
parameters which will keep time stopped. Note that given ``auto_tick_seconds`` the ``tick`` parameter will be ignored.

.. code-block:: python

    @freeze_time("Jan 14th, 2020", auto_tick_seconds=15)
    def test_nice_datetime():
        first_time = datetime.datetime.now()
        auto_incremented_time = datetime.datetime.now()
        assert first_time + datetime.timedelta(seconds=15) == auto_incremented_time


Manual ticks
~~~~~~~~~~~~

FreezeGun allows for the time to be manually forwarded as well.

.. code-block:: python

    def test_manual_tick():
        initial_datetime = datetime.datetime(year=1, month=7, day=12,
                                            hour=15, minute=6, second=3)
        with freeze_time(initial_datetime) as frozen_datetime:
            assert frozen_datetime() == initial_datetime

            frozen_datetime.tick()
            initial_datetime += datetime.timedelta(seconds=1)
            assert frozen_datetime() == initial_datetime

            frozen_datetime.tick(delta=datetime.timedelta(seconds=10))
            initial_datetime += datetime.timedelta(seconds=10)
            assert frozen_datetime() == initial_datetime

.. code-block:: python

    def test_monotonic_manual_tick():
        initial_datetime = datetime.datetime(year=1, month=7, day=12,
                                            hour=15, minute=6, second=3)
        with freeze_time(initial_datetime) as frozen_datetime:
            monotonic_t0 = time.monotonic()
            frozen_datetime.tick(1.0)
            monotonic_t1 = time.monotonic()
            assert monotonic_t1 == monotonic_t0 + 1.0


Moving time to specify datetime
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

FreezeGun allows moving time to specific dates.

.. code-block:: python

    def test_move_to():
        initial_datetime = datetime.datetime(year=1, month=7, day=12,
                                            hour=15, minute=6, second=3)

        other_datetime = datetime.datetime(year=2, month=8, day=13,
                                            hour=14, minute=5, second=0)
        with freeze_time(initial_datetime) as frozen_datetime:
            assert frozen_datetime() == initial_datetime

            frozen_datetime.move_to(other_datetime)
            assert frozen_datetime() == other_datetime

            frozen_datetime.move_to(initial_datetime)
            assert frozen_datetime() == initial_datetime


    @freeze_time("2012-01-14", as_arg=True)
    def test(frozen_time):
        assert datetime.datetime.now() == datetime.datetime(2012, 1, 14)
        frozen_time.move_to("2014-02-12")
        assert datetime.datetime.now() == datetime.datetime(2014, 2, 12)

Parameter for ``move_to`` can be any valid ``freeze_time`` date (string, date, datetime).


Default arguments
~~~~~~~~~~~~~~~~~

Note that FreezeGun will not modify default arguments. The following code will
print the current date. See `here <http://docs.python-guide.org/en/latest/writing/gotchas/#mutable-default-arguments>`_ for why.

.. code-block:: python

    from freezegun import freeze_time
    import datetime as dt

    def test(default=dt.date.today()):
        print(default)

    with freeze_time('2000-1-1'):
        test()


Installation
------------

To install FreezeGun, simply:

.. code-block:: bash

    $ pip install freezegun

On Debian systems:

.. code-block:: bash

    $ sudo apt-get install python-freezegun


Ignore packages
---------------

Sometimes it's desired to ignore FreezeGun behaviour for particular packages (i.e. libraries).
It's possible to ignore them for a single invocation:


.. code-block:: python

    from freezegun import freeze_time

    with freeze_time('2020-10-06', ignore=['threading']):
        # ...


By default FreezeGun ignores following packages:

.. code-block:: python

    [
        'nose.plugins',
        'six.moves',
        'django.utils.six.moves',
        'google.gax',
        'threading',
        'Queue',
        'selenium',
        '_pytest.terminal.',
        '_pytest.runner.',
        'gi',
    ]


It's possible to set your own default ignore list:

.. code-block:: python

    import freezegun

    freezegun.configure(default_ignore_list=['threading', 'tensorflow'])


Please note this will override default ignore list. If you want to extend existing defaults
please use:

.. code-block:: python

    import freezegun

    freezegun.configure(extend_ignore_list=['tensorflow'])
