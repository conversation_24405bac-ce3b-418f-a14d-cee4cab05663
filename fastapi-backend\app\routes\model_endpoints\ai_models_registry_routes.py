from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

from post_db.master_models.ai_models_registry import AIModelsRegistry, DeploymentStatus
from core.session_manager import get_master_db_session

# Define Pydantic models for request/response
class AIModelBase(BaseModel):
    model_name: str
    model_id: str
    supported_file_types: List[str]
    output_format: dict
    deployment_status: str = DeploymentStatus.INACTIVE.value
    description: Optional[str] = None
    input_requirements: Optional[dict] = None

class AIModelCreate(AIModelBase):
    pass

class AIModelUpdate(BaseModel):
    model_name: Optional[str] = None
    supported_file_types: Optional[List[str]] = None
    output_format: Optional[dict] = None
    deployment_status: Optional[str] = None
    description: Optional[str] = None
    input_requirements: Optional[dict] = None

class AIModelResponse(AIModelBase):
    id: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True

# Create router
router = APIRouter(
    prefix="/ai-models",
    tags=["AI Models Registry"],
    responses={404: {"description": "Not found"}},
)

@router.get("/", response_model=List[AIModelResponse])
async def get_all_models(
    active_only: bool = False,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get all AI models from the registry
    If active_only=True, returns only models with deployment_status='active'
    """
    query = select(AIModelsRegistry)
    
    if active_only:
        query = query.where(AIModelsRegistry.deployment_status == DeploymentStatus.ACTIVE.value)
    
    result = await db.execute(query)
    models = result.scalars().all()
    return models

@router.get("/{model_id:path}", response_model=AIModelResponse)
async def get_model(model_id: str, db: AsyncSession = Depends(get_master_db_session)):
    """
    Get a specific AI model by its model_id
    """
    result = await db.execute(
        select(AIModelsRegistry).where(AIModelsRegistry.model_id == model_id)
    )
    model = result.scalar_one_or_none()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Model with ID {model_id} not found"
        )
    return model

@router.post("/", response_model=AIModelResponse, status_code=status.HTTP_201_CREATED)
async def create_model(model: AIModelCreate, db: AsyncSession = Depends(get_master_db_session)):
    """
    Create a new AI model in the registry
    """
    # Check if model_id already exists
    result = await db.execute(
        select(AIModelsRegistry).where(AIModelsRegistry.model_id == model.model_id)
    )
    existing_model = result.scalar_one_or_none()
    
    if existing_model:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Model with ID {model.model_id} already exists"
        )
    
    # Create new model
    db_model = AIModelsRegistry(
        model_name=model.model_name,
        model_id=model.model_id,
        supported_file_types=model.supported_file_types,
        output_format=model.output_format,
        deployment_status=model.deployment_status,
        description=model.description,
        input_requirements=model.input_requirements
    )
    
    db.add(db_model)
    await db.commit()
    await db.refresh(db_model)
    return db_model

@router.put("/{model_id:path}", response_model=AIModelResponse)
async def update_model(
    model_id: str, 
    model_update: AIModelUpdate, 
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Update an existing AI model in the registry
    """
    result = await db.execute(
        select(AIModelsRegistry).where(AIModelsRegistry.model_id == model_id)
    )
    db_model = result.scalar_one_or_none()
    if not db_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Model with ID {model_id} not found"
        )
    
    # Update model fields if provided
    update_data = model_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_model, key, value)
    
    await db.commit()
    await db.refresh(db_model)
    return db_model

@router.delete("/{model_id:path}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_model(model_id: str, db: AsyncSession = Depends(get_master_db_session)):
    """
    Delete an AI model from the registry
    """
    result = await db.execute(
        select(AIModelsRegistry).where(AIModelsRegistry.model_id == model_id)
    )
    db_model = result.scalar_one_or_none()
    if not db_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Model with ID {model_id} not found"
        )
    
    await db.delete(db_model)
    await db.commit()
    return None

class StatusUpdate(BaseModel):
    status: DeploymentStatus


@router.patch("/{model_id:path}/status", response_model=AIModelResponse)
async def update_model_status(
    model_id: str,
    status_update: StatusUpdate,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Update the deployment status of an AI model
    """
    result = await db.execute(
        select(AIModelsRegistry).where(AIModelsRegistry.model_id == model_id)
    )
    db_model = result.scalar_one_or_none()
    if not db_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Model with ID {model_id} not found"
        )
    
    db_model.deployment_status = status_update.status.value
    await db.commit()
    await db.refresh(db_model)
    return db_model