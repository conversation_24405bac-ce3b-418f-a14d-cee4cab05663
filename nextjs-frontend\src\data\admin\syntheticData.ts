export interface SourceItem {
  type: string;
  name: string;
  size?: string;
  content?: string;
  wordCount?: number;
  url?: string;
}

export const supportedFileFormats = ['.pdf', '.txt', '.md'];

export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' bytes';
  else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
  else return (bytes / 1048576).toFixed(1) + ' MB';
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const generateMockPreview = (sources: SourceItem[]): string => {
  return sources.map(source => {
    return `Content from ${source.type}: ${source.name}`;
  }).join('\n\n');
}; 