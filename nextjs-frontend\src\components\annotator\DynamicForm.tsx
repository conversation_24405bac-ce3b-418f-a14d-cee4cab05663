import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>ield, FormFieldConfig } from '@/components/shared/dynamic-fields';
import AISuggestionButton from './AISuggestionButton';

interface DynamicFormProps {
  formConfig: FormFieldConfig[];
  imageKey: string;
  initialData?: Record<string, any>;
  onChange: (imageKey: string, formData: Record<string, any>) => void;
  onValidationChange?: (isValid: boolean, errors: Record<string, string>) => void;
  aiSuggestions?: Record<string, any> | null;
}

export default function DynamicForm({ 
  formConfig, 
  imageKey, 
  initialData, 
  onChange,
  onValidationChange,
  aiSuggestions
}: DynamicFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>(initialData || {});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    } else {
      setFormData({});
    }
    setErrors({});
  }, [initialData, imageKey]);

  // Validate form data
  const validateForm = (data: Record<string, any>) => {
    const newErrors: Record<string, string> = {};
    
    formConfig.forEach(field => {
      const value = data[field.field_name];
      
      if (field.required) {
        if (field.field_type === 'checkboxes') {
          if (!value || !Array.isArray(value) || value.length === 0) {
            newErrors[field.field_name] = `${field.label} is required`;
          }
        } else {
          if (!value || (typeof value === 'string' && value.trim() === '')) {
            newErrors[field.field_name] = `${field.label} is required`;
          }
        }
      }
      
      // Validate max_length for text fields
      if (field.max_length && value && typeof value === 'string') {
        if (value.length > field.max_length) {
          newErrors[field.field_name] = `${field.label} must be ${field.max_length} characters or less`;
        }
      }
    });
    
    return newErrors;
  };

  // Handle field changes
  const handleFieldChange = (fieldName: string, value: any) => {
    const newFormData = {
      ...formData,
      [fieldName]: value
    };
    
    setFormData(newFormData);
    
    // Validate and update errors
    const newErrors = validateForm(newFormData);
    setErrors(newErrors);
    
    // Propagate changes up
    onChange(imageKey, newFormData);
    
    // Notify validation state
    if (onValidationChange) {
      onValidationChange(Object.keys(newErrors).length === 0, newErrors);
    }
  };

  // Handle AI suggestion application
  const handleApplyAISuggestion = (fieldName: string, value: any) => {
    handleFieldChange(fieldName, value);
  };

  // Handle form submission validation
  const validateAndGetErrors = () => {
    const newErrors = validateForm(formData);
    setErrors(newErrors);
    return newErrors;
  };

  // Expose validation method
  const formRef = React.useRef<{
    validate: () => Record<string, string>;
    isValid: () => boolean;
    getData: () => Record<string, any>;
  }>(null);
  React.useImperativeHandle(formRef, () => ({
    validate: validateAndGetErrors,
    isValid: () => Object.keys(validateForm(formData)).length === 0,
    getData: () => formData
  }));

  if (!formConfig || formConfig.length === 0) {
    return (
      <div className="alert alert-info">
        <i className="fas fa-info-circle me-2"></i>
        No dynamic form configured for this dataset. Use the simple label input above.
      </div>
    );
  }

  return (
    <div className="mt-5">
      <div className="border border-gray-300 rounded-lg shadow-md">
        <div className="bg-gray-50 border-b border-gray-300 px-4 py-3">
          <h6 className="mb-0 text-gray-700 font-semibold">
            <i className="fas fa-form me-2"></i>
            Annotation Form
          </h6>
        </div>
        <div className="p-5">
          {formConfig.map((fieldConfig, index) => (
            <div key={`${fieldConfig.field_name}_${index}`} className="mb-5">
              <div className="flex items-center mb-2">
                <label className="font-semibold text-gray-700 mb-0">
                  {fieldConfig.label}
                  {fieldConfig.required && <span className="text-red-600 ml-0.5">*</span>}
                </label>
                {aiSuggestions && (
                  <AISuggestionButton
                    fieldName={fieldConfig.field_name}
                    aiSuggestions={aiSuggestions}
                    onApplySuggestion={handleApplyAISuggestion}
                    className="ml-2"
                    fieldType={fieldConfig.field_type}
                  />
                )}
              </div>
              <DynamicField
                config={{
                  ...fieldConfig,
                  label: '', // Remove label from DynamicField since we're showing it above
                  required: false // Remove required asterisk from DynamicField since we're showing it above
                }}
                value={formData[fieldConfig.field_name]}
                onChange={handleFieldChange}
                error={errors[fieldConfig.field_name]}
              />
            </div>
          ))}
          
          {Object.keys(errors).length > 0 && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-0">
              <i className="fas fa-exclamation-triangle me-2"></i>
              Please fix the errors above before saving.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
