# routers/allocation_strategies.py
from fastapi import APIRouter, Depends, HTTPException, Request # type: ignore
from typing import Dict, Optional, Any
from pydantic import BaseModel
import logging
import json
from sqlalchemy import select

# Core modules
from services.project_batch_service import ProjectBatchService
from dependencies.auth import get_current_user, get_annotator_mode, get_current_active_user, require_annotator
from sqlalchemy.ext.asyncio import AsyncSession
from services.annotator_service import annotator_service
# from post_db.master_models.datasets import Datasets
from post_db.master_models.projects_registry import ProjectsRegistry
from schemas.AdminSettingsSchemas import FormFieldConfig, GetAnnotatorFormConfigResponse, AnnotatorFormConfigResponse
from cache.annotator_cache import (get_cached_batch, cache_batch, get_cached_user_batch_id, 
                                   cache_user_batch,prefetch_batch_images_async, delete_user_batch_cache)
from core.session_manager import get_project_db_session, get_master_db_session
from post_db.allocation_models.project_metadata import ProjectMetadata

logger = logging.getLogger('annotator_routes')

router = APIRouter(
    prefix="/annotator", 
    tags=["Annotator"]
)

class SaveLabelsRequest(BaseModel):
    labels: Dict[str, str]
    form_data: Optional[Dict[str, Dict[str, Any]]] = None  # New dynamic form data
    verification_mode: bool = False
    batch_name: Optional[str] = None

class SaveFormDataRequest(BaseModel):
    image_key: str
    form_data: Dict[str, Any]
    batch_name: Optional[str] = None

def parse_annotation_requirements(annotation_requirements: dict | str | None) -> dict | None:
    """Parse annotation_requirements safely, return dict or None if invalid."""
    if annotation_requirements is None:
        return None
    
    # If it's already a dict, return it
    if isinstance(annotation_requirements, dict):
        return annotation_requirements
    
    # If it's a string, try to parse as JSON
    try:
        if isinstance(annotation_requirements, str):
            parsed = json.loads(annotation_requirements)
            if isinstance(parsed, dict):
                return parsed
    except (json.JSONDecodeError, TypeError):
        logger.debug("Annotation requirements field is not valid JSON.")
    return None

async def get_project_code_from_user(username: str) -> str | None:
    """Get project_code for the user's current allocation."""
    try:
        # Get user's current allocation to find their project_code
        batch_manager = ProjectBatchService()
        user_allocation = await batch_manager.get_user_current_allocation(username)
        
        if not user_allocation or not user_allocation.get('project_code'):
            logger.warning(f"No active allocation found for user: {username}")
            return None
            
        project_code = user_allocation['project_code']
        logger.info(f"Found project code for user {username}: {project_code}")
        return project_code
        
    except Exception as e:
        logger.error(f"Error getting project code for user {username}: {e}")
        return None

async def get_storage_credentials_from_project(username: str) -> dict | None:
    """Get NAS credentials from project metadata for the user's assigned project."""
    try:
        project_code = await get_project_code_from_user(username)
        if not project_code:
            return None
        
        logger.info(f"Getting NAS credentials for project: {project_code}")
        
        # Get project metadata from project database
        async with get_project_db_session(project_code) as project_db_session:
            # Query project metadata
            result = await project_db_session.execute(
                select(ProjectMetadata).where(ProjectMetadata.project_code == project_code)
            )
            project_metadata = result.scalar_one_or_none()
            
            if not project_metadata:
                logger.warning(f"No project metadata found for project: {project_code}")
                return None
                
            credentials = project_metadata.credentials
            if not credentials:
                logger.warning(f"No storage credentials found in project metadata for project: {project_code}")
                return None
                
            logger.info(f"Successfully retrieved storage credentials for project: {project_code}")
            return credentials
            
            
    except Exception as e:
        logger.error(f"Error getting NAS credentials for user {username}: {str(e)}")
        return None

@router.get("/dashboard")
async def annotator_dashboard(
    annotation_mode: str = Depends(get_annotator_mode),
    current_user: dict = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """Get annotator dashboard data"""
    try:
        username = current_user["sub"]
        logger.info(f"Dashboard endpoint called for user: {username}")
        
        # Get user's current allocation to find their project_code
        batch_manager = ProjectBatchService()
        user_allocation = await batch_manager.get_user_current_allocation(username)
        logger.info(f"User allocation result: {user_allocation}")
        
        project_code = None
        project_name = None
        instructions = ""
        
        if user_allocation and user_allocation.get('project_code'):
            project_code = user_allocation['project_code']
            logger.info(f"Found user allocation with project_code: {project_code}")
            
            # Get project details from projects_registry
            result = await master_db.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project_obj = result.scalar_one_or_none()
            if project_obj:
                project_name = project_obj.project_name
                instructions = project_obj.instructions or ""
                logger.info(f"Found project name: {project_name}")
                logger.info(f"Found project instructions: {instructions[:100]}...")
            else:
                logger.warning(f"Project not found in registry: {project_code}")
        else:
            logger.warning(f"No user allocation found for user: {username}")
            
            # EMERGENCY FIX: If no allocation found, try to get any available project
            logger.info("EMERGENCY FIX: Trying to find any available project")
            projects_result = await master_db.execute(select(ProjectsRegistry).limit(1))
            emergency_project = projects_result.scalar_one_or_none()
            if emergency_project:
                project_code = emergency_project.project_code
                project_name = emergency_project.project_name
                instructions = emergency_project.instructions or ""
                logger.info(f"Using emergency project: {project_code}")
            else:
                logger.error("No projects found in database at all!")
        
        return {
            "annotation_mode": annotation_mode,
            "username": username,
            "full_name": current_user.get("full_name", ""),
            "project_code": project_code,
            "project_name": project_name,
            "instructions": instructions,
            "debug_info": {
                "user_allocation": user_allocation,
                "has_user_allocation": user_allocation is not None,
                "project_code_found": project_code is not None
            }
        }
    except Exception as e:
        logger.error(f"Error in dashboard endpoint: {e}")
        return {
            "annotation_mode": annotation_mode,
            "username": current_user.get("sub", ""),
            "full_name": current_user.get("full_name", ""),
            "project_code": None,
            "project_name": None,
            "instructions": ""
        }

@router.get("/debug-db")
async def debug_database(master_db: AsyncSession = Depends(get_master_db_session)):
    """Debug endpoint to check database state"""
    try:
        from post_db.master_models.user_project_access import UserProjectAccess
        
        # Get ALL UserProjectAccess records
        all_access_result = await master_db.execute(select(UserProjectAccess))
        all_accesses = all_access_result.scalars().all()
        
        # Get ALL projects
        projects_result = await master_db.execute(select(ProjectsRegistry))
        all_projects = projects_result.scalars().all()
        
        return {
            "total_user_accesses": len(all_accesses),
            "user_accesses": [
                {
                    "id": access.id,
                    "username": access.username,
                    "project_id": access.project_id,
                    "is_active": access.is_active,
                    "project_role": access.project_role
                } for access in all_accesses
            ],
            "total_projects": len(all_projects),
            "projects": [
                {
                    "id": project.id,
                    "project_code": project.project_code,
                    "project_name": project.project_name
                } for project in all_projects
            ]
        }
    except Exception as e:
        return {"error": str(e)}

@router.get("/form-config", response_model=GetAnnotatorFormConfigResponse)
async def get_annotator_form_config(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_master_db_session),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """Get form configuration for current annotator based on their allocated project."""
    try:
        username = current_user["sub"]
        
        # FIXED: Get the user's current allocated project from user_allocations
        logger.info(f"Getting form config for user: {username}")
        
        # Get user's current allocation to find their project_code
        batch_manager = ProjectBatchService()
        user_allocation = await batch_manager.get_user_current_allocation(username)
        
        if not user_allocation or not user_allocation.get('project_code'):
            logger.warning(f"No active allocation found for user: {username}")
            return GetAnnotatorFormConfigResponse(
                success=True,
                data=AnnotatorFormConfigResponse(
                    id=0,
                    mode="annotation",
                    dataset_id="",
                    dataset_name="",
                    fields=[]
                ),
                message="No batch allocation found for user"
            )
        
        project_code = user_allocation['project_code']
        logger.info(f"Found user's allocated project: {project_code}")
        
        # Get the project from projects_registry using the project_code from allocation
        result = await master_db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
        )
        project_obj = result.scalar_one_or_none()
        
        if not project_obj:
            logger.warning(f"Project '{project_code}' not found in projects_registry")
            return GetAnnotatorFormConfigResponse(
                success=True,
                data=AnnotatorFormConfigResponse(
                    id=0,
                    mode="annotation",
                    dataset_id=project_code,
                    dataset_name=project_code,
                    fields=[]
                ),
                message=f"Project '{project_code}' not found"
            )
        
        logger.info(f"Found allocated project: {project_obj.project_name} ({project_obj.project_code})")
        
        # Parse form configuration
        logger.info(f"Raw annotation_requirements: {project_obj.annotation_requirements}")
        parsed_requirements = parse_annotation_requirements(project_obj.annotation_requirements)
        logger.info(f"Parsed requirements: {parsed_requirements}")
        
        form_config = []
        if parsed_requirements and "form_config" in parsed_requirements:
            try:
                logger.info(f"Found form_config with {len(parsed_requirements['form_config'])} fields")
                form_config = [FormFieldConfig(**field) for field in parsed_requirements["form_config"]]
                logger.info(f"Successfully parsed {len(form_config)} form fields")
            except Exception as e:
                logger.warning(f"Invalid form_config structure for project {project_obj.project_code}: {e}")
        else:
            logger.warning(f"No 'form_config' found in annotation_requirements for project {project_obj.project_code}")
        
        response_data = AnnotatorFormConfigResponse(
            id=project_obj.id,
            mode="annotation",  # Default mode since annotation_mode doesn't exist in ProjectsRegistry
            dataset_id=project_obj.project_code,
            dataset_name=project_obj.project_name or "",
            fields=form_config
        )
        
        return GetAnnotatorFormConfigResponse(
            success=True,
            data=response_data,
            message="Form configuration retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error retrieving form configuration for user {username}: {e}")
        return GetAnnotatorFormConfigResponse(
            success=False,
            data=None,
            message="Failed to retrieve form configuration"
        )

@router.get("/annotate")
async def annotate_route(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_user),
    mode: str = Depends(get_annotator_mode)
):
    """Get annotation data for the current user with caching support"""
    username = current_user["sub"]
    
    # Try to get cached batch first
    cache_mode = "annotation" if mode == "annotation" else "verification"
    cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
    if cached_batch_id:
        cached_batch = await get_cached_batch(cached_batch_id)
        if cached_batch:
            # Convert cached image paths to ImageInfo objects
            from schemas.annotation_schemas import ImageInfo
            cached_images = []
            for img_path in cached_batch['images']:
                cached_images.append(ImageInfo(
                    name=img_path.split('/')[-1],
                    path=img_path,
                    size=0,
                    type='file'
                ))
            
            # Get labels from cache
            cached_labels = cached_batch.get('labels', {})
            
            return {
                "images": annotator_service.prepare_image_data(cached_images, cached_labels),
                "user": current_user,
                "mode": mode,
                "batch_name": cached_batch['batch_name'],
                "instructions": cached_batch.get('instructions', '')
            }
    
    # If no cached batch, redirect to next-set to get a new batch
    raise HTTPException(status_code=204, detail="No cached batch available. Please call /next-set to get a new batch.")

@router.get("/next-set")
async def get_next_image_set(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_user),
    mode: str = Depends(get_annotator_mode)
):
    """Get the next set of images for annotation with caching support"""
    username = current_user["sub"]
    batch_manager = ProjectBatchService()
    cache_mode = "annotation" if mode == "annotation" else "verification"

    # NEW: Use allocation_batches with admin-controlled active project
    success, message, batch = await batch_manager.assign_allocation_batch_to_user_from_active_project(username, mode=mode)

    if not success:
        return {
            "images": [],
            "status": "no_batches",
            "message": f'No new batches available: {message}'
        }

    # NEW: Handle allocation_batches format
    # Extract file list from allocation_batches schema
    file_list = batch.get('file_list', [])
    if isinstance(file_list, str):
        try:
            file_list = json.loads(file_list)
        except json.JSONDecodeError:
            file_list = []
    
    batch_identifier = batch.get('batch_identifier', '')
    total_files = batch.get('total_files', len(file_list))
    
    # For now, we'll use a simple approach for instructions
    # TODO: Get instructions from project metadata or admin settings
    instructions = ""
    
    # Load labels for verification mode (simplified for now)
    batch_labels = {}
    # TODO: Implement label loading for verification mode with new schema
    
    batch_data = {
        'id': f"{username}_{mode}_{batch_identifier}",
        'batch_name': batch_identifier,  # Use batch_identifier as batch_name for compatibility
        'batch_identifier': batch_identifier,  # New field
        'images': file_list,  # File list from allocation_batches
        'image_count': total_files,  # Total files from allocation_batches
        'username': username,
        'mode': mode,
        'instructions': instructions,
        'labels': batch_labels  # Store labels in cache
    }
    await cache_batch(batch_data['id'], batch_data)
    await cache_user_batch(username, batch_data['id'], cache_mode)
    
    logger.info(f"Starting prefetch for newly assigned batch {batch_data['id']}")
    prefetch_batch_images_async(batch_data)

    return {
        "status": "success",
        "message": f'New batch assigned: {message}',
        "batch": batch
}

@router.post("/save-individual-form-data")
async def save_form_data(
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Save individual form data to Redis cache"""
    try:
        # Parse JSON manually to avoid Pydantic validation issues
        body = await request.json()
        logger.info(f"Received request body: {body}")
        logger.info(f"Body type: {type(body)}")
        logger.info(f"Body keys: {list(body.keys()) if isinstance(body, dict) else 'Not a dict'}")
        
        username = current_user["sub"]
        image_key = body.get("image_key")
        form_data = body.get("form_data")
        batch_name = body.get("batch_name")
        
        logger.info(f"Extracted - image_key: {image_key}, form_data: {form_data}, batch_name: {batch_name}")
        
        if not image_key or not form_data:
            raise HTTPException(status_code=400, detail="image_key and form_data are required")
        
        logger.info(f"Saving form data for user {username}, image {image_key}")
        
        # Get cached batch to update form data
        cache_mode = "annotation"  # Default to annotation mode
        cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
        
        if not cached_batch_id:
            # Create a temporary cache entry using batch_name
            if batch_name:
                cached_batch_id = f"{username}_{cache_mode}_{batch_name}"
                # Try to get existing cache first
                cached_batch = await get_cached_batch(cached_batch_id)
                if not cached_batch:
                    cached_batch = {
                        'id': cached_batch_id,
                        'batch_name': batch_name,
                        'username': username,
                        'mode': cache_mode,
                        'form_data': {}
                    }
            else:
                raise HTTPException(status_code=404, detail="No active batch found in cache and no batch_name provided")
        else:
            cached_batch = await get_cached_batch(cached_batch_id)
            if not cached_batch:
                raise HTTPException(status_code=404, detail="Batch data not found in cache")
        
        # Initialize form_data in cache if it doesn't exist
        if 'form_data' not in cached_batch:
            cached_batch['form_data'] = {}
        
       
        if image_key.startswith('http') and '/api/media/image/' in image_key:
            normalized_key = image_key.split('/api/media/image/')[-1]
            if not normalized_key.startswith('/'):
                normalized_key = '/' + normalized_key
        elif image_key.startswith('http') and '/api/annotator/image/' in image_key:
            # Legacy support for old URLs
            normalized_key = image_key.split('/api/annotator/image/')[-1]
            if not normalized_key.startswith('/'):
                normalized_key = '/' + normalized_key
        else:
            normalized_key = image_key
            
        logger.info(f"Normalized image key from '{image_key}' to '{normalized_key}'")
        
        # Update form data for this specific image using normalized key
        cached_batch['form_data'][normalized_key] = form_data
        
        logger.info(f"About to cache batch with ID: {cached_batch_id}")
        logger.info(f"Cached batch form_data now has {len(cached_batch['form_data'])} entries")
        logger.info(f"Form data keys: {list(cached_batch['form_data'].keys())}")
        
        # Update the cache
        await cache_batch(cached_batch_id, cached_batch)
        
        logger.info(f"Successfully saved form data for image {image_key}")
        return {"success": True, "message": "Form data saved to cache"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving form data: {str(e)}")
        raise HTTPException(status_code=500, detail=f'Failed to save form data: {str(e)}')


@router.post("/save-labels")
async def save_labels(
    data: SaveLabelsRequest,
    db: AsyncSession = Depends(get_master_db_session    ),
    current_user: dict = Depends(get_current_user)
):
    """Save labels and form data for a session and clean up cache"""
    try:
        username = current_user["sub"]
        labels = data.labels
        form_data = data.form_data
        is_verification_mode = data.verification_mode
        batch_name = data.batch_name

        logger.info(f"Starting save_labels for user {username}, batch {batch_name}")
        
        # Get cached batch to retrieve any form data saved individually
        cache_mode = "annotation" if not is_verification_mode else "verification"
        cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
        logger.info(f"Looking for cached batch ID: {cached_batch_id}")
        
        # Also try the batch_name based cache ID format we use for individual saves
        if not cached_batch_id and batch_name:
            cached_batch_id = f"{username}_{cache_mode}_{batch_name}"
            logger.info(f"Trying batch_name based cache ID: {cached_batch_id}")
        
        cached_form_data = {}
        
        if cached_batch_id:
            cached_batch = await get_cached_batch(cached_batch_id)
            logger.info(f"Retrieved cached batch: {cached_batch}")
            if cached_batch and 'form_data' in cached_batch:
                cached_form_data = cached_batch['form_data']
                logger.info(f"Found cached form data for {len(cached_form_data)} images")
                logger.info(f"Cached form data keys: {list(cached_form_data.keys())}")
                logger.info(f"Sample cached form data: {dict(list(cached_form_data.items())[:2])}")  # Show first 2 items
            else:
                logger.info(f"No form_data found in cached batch or batch is None")

        # Merge form data from request with cached form data (request takes priority)
        final_form_data = cached_form_data.copy()
        if form_data:
            final_form_data.update(form_data)
            
        logger.info(f"Labels from request: {labels}")
        logger.info(f"Form data from request: {form_data}")
        logger.info(f"Final merged form data: {len(final_form_data)} entries")
        logger.info(f"Final form data keys: {list(final_form_data.keys())[:3]}...")

        # Combine labels with form data if available
        combined_labels = {}
        
        # First, add all labels (even if empty)
        for image_key, label_value in labels.items():
            if final_form_data and image_key in final_form_data:
                # Combine simple label with form data
                combined_labels[image_key] = {
                    "label": label_value,
                    "form_data": final_form_data[image_key]
                }
            else:
                # Keep backward compatibility - simple string label
                combined_labels[image_key] = label_value
        
        # For dynamic forms, also add entries that only have form data (no labels)
        if len(final_form_data) > 0:
            for image_key, form_data_entry in final_form_data.items():
                if image_key not in combined_labels:
                    # Create entry with form data only
                    combined_labels[image_key] = {
                        "label": "completed",  # Default label for form-only entries
                        "form_data": form_data_entry
                    }
                    logger.info(f"Added form-only entry for: {image_key}")

        logger.info(f"Combined labels for {len(combined_labels)} images, {len([k for k, v in combined_labels.items() if isinstance(v, dict) and 'form_data' in v])} with form data")

        # Save labels via batch service
        batch_manager = ProjectBatchService()
        mode = "verification" if is_verification_mode else "annotation"
        success, msg = await batch_manager.save_batch_labels(db, combined_labels, username, batch_name, mode)
        
        if success:
            # Clean up cache after successful completion
            await delete_user_batch_cache(username, cache_mode)
            logger.info(f"Cleaned up cache for completed batch with form data: {bool(final_form_data)}")
            
            return {"success": True, "form_data_saved": bool(final_form_data)}
        
        raise HTTPException(status_code=500, detail=f"Failed to save {mode} labels: {msg}")

    except Exception as e:
        logger.error(f"Error in save_labels: {str(e)}")
        raise HTTPException(status_code=500, detail=f'Failed to save labels: {str(e)}')