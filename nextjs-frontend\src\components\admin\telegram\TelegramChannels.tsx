"use client";

import React, { useEffect, useState, useCallback } from "react";
import {
  FaTelegram,
  FaSyncAlt,
  FaDownload,
  FaImages,
  FaChartBar,
  FaSignOutAlt,
} from "react-icons/fa";
import {
  BASE_URL,
  TelegramChannel,
  AnalyticsData,
  TelegramChannelsProps,
  fetchTelegramChannels,
} from "./types";

export default function TelegramChannels({
  selectedChannelId,
  onSelectChannel,
  onShowImages,
  onDisconnect,
}: TelegramChannelsProps) {
  const [channels, setChannels] = useState<TelegramChannel[]>([]);
  const [loading, setLoading] = useState(true);
  const selectedChannel =
    channels.find((ch) => ch.id === selectedChannelId) || null;
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [days, setDays] = useState<number>(7);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [exportError, setExportError] = useState<string | null>(null);
  const [abortController, setAbortController] =
    useState<AbortController | null>(null);

  const fetchChannels = async (forceRefresh: boolean = false) => {
    setLoading(true);
    try {
      const channelData = await fetchTelegramChannels(forceRefresh);
      setChannels(channelData);
    } catch (err) {
      console.error("Error fetching channels:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  useEffect(() => {
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, [abortController]);

  // Use useCallback for fetchAnalytics to avoid missing dependency warnings
  const fetchAnalytics = useCallback(async () => {
    if (!selectedChannelId) return;

    if (abortController) {
      abortController.abort();
    }

    const controller = new AbortController();
    setAbortController(controller);
    setAnalyticsLoading(true);

    try {
      const url = `${BASE_URL}/telegram/analytics?channel_id=${selectedChannelId}${
        days ? `&days=${days}` : ""
      }`;
      const res = await fetch(url, {
        credentials: "include",
        signal: controller.signal,
      });

      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();
      setAnalytics(data);
    } catch (err: unknown) {
      if (err instanceof Error && err.name === "AbortError") {
        console.log("Analytics request was cancelled");
        setAnalytics({
          total_images: 0,
          error: "Analysis was cancelled",
        });
      } else {
        console.error("Error fetching analytics:", err);
        setAnalytics({
          total_images: 0,
          error: "Failed to load analytics",
        });
      }
    } finally {
      setAnalyticsLoading(false);
      setAbortController(null);
    }
    // We intentionally do NOT add abortController to deps to avoid infinite loop
  }, [selectedChannelId, abortController, days]);

  useEffect(() => {
    if (selectedChannelId) {
      fetchAnalytics();
    }
  }, [selectedChannelId, fetchAnalytics]);

  const handleDisconnect = async () => {
    try {
      await fetch(`${BASE_URL}/telegram/disconnect`, {
        method: "POST",
        credentials: "include",
      });
      onDisconnect?.();
    } catch (err) {
      console.error(err);
    }
  };

  const handleExport = () => {
    if (!selectedChannel || !analytics) {
      setExportError("No channel selected or analytics data available");
      return;
    }

    setExportLoading(true);
    setExportError(null);

    try {
      const filename = `${selectedChannel.title
        .replace(/[^a-z0-9]/gi, "_")
        .toLowerCase()}_analytics.csv`;
      const csvContent = generateCSVContent(selectedChannel, analytics);

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export failed:", error);
      setExportError("Failed to export analytics. Please try again.");
    } finally {
      setExportLoading(false);
    }
  };

  const generateCSVContent = (
    channel: TelegramChannel,
    analyticsData: AnalyticsData
  ) => {
    let csvContent = "Metric,Value\n";
    csvContent += `Channel Name,${channel.title}\n`;
    csvContent += `Channel Username,${channel.username || "N/A"}\n`;
    csvContent += `Total Images,${analyticsData.total_images}\n`;
    csvContent += `Active Days,${analyticsData.dates?.length || 0}\n`;
    csvContent += `Date Range,${analyticsData.first_date || "N/A"} to ${
      analyticsData.last_date || "N/A"
    }\n`;
    csvContent += `Days Filter Applied,${days}\n`;
    csvContent += `Export Date,${new Date().toISOString().split("T")[0]}\n`;

    return csvContent;
  };

  const handleStopAnalysis = () => {
    if (abortController) {
      abortController.abort();
    }
  };

  const handleAnalysisClick = (id: number) => {
    if (analyticsLoading) return;

    onSelectChannel(id);
    if (id === selectedChannelId) {
      fetchAnalytics();
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-600 to-purple-400 rounded-xl p-6 text-white">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center">
              <FaTelegram className="text-white text-2xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">
                Telegram Channels
              </h2>
              <p className="text-blue-100 text-sm">
                {channels.length} channel{channels.length !== 1 ? "s" : ""}{" "}
                available
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => fetchChannels(true)}
              className="px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 hover:shadow-lg rounded-lg text-black transition-all duration-200 flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transform hover:scale-105"
            >
              <FaSyncAlt className={`${loading ? "animate-spin" : ""}`} />
              <span>Refresh</span>
            </button>
            <button
              onClick={handleDisconnect}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 hover:shadow-lg rounded-lg text-white transition-all duration-200 flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-red-400 transform hover:scale-105"
            >
              <FaSignOutAlt />
              <span>Disconnect</span>
            </button>
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row lg:space-x-6">
        <div className="flex-1 space-y-4">
          {loading ? (
            <div className="flex justify-center py-10">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
            </div>
          ) : channels.length === 0 ? (
            <p className="text-center text-gray-500 py-10">
              No channels found.
            </p>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {channels.map((ch) => (
                <div
                  key={ch.id}
                  className={`group relative flex flex-col h-full bg-white rounded-xl shadow-md overflow-hidden ${
                    selectedChannel?.id === ch.id
                      ? "ring-2 ring-blue-500 shadow-lg"
                      : ""
                  }`}
                >
                  <div className="p-6 flex-1">
                    <div className="flex items-start space-x-3 mb-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <FaTelegram className="text-white text-xl" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3
                          className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2 leading-tight"
                          title={ch.title}
                        >
                          {ch.title}
                        </h3>
                        {ch.username && (
                          <p className="text-sm text-gray-500">
                            @{ch.username}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 mb-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-xs text-gray-600 font-medium">
                        Active Channel
                      </span>
                    </div>
                  </div>

                  <div className="p-4 bg-gray-50 border-t border-gray-100">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleAnalysisClick(ch.id)}
                        disabled={analyticsLoading}
                        className={`flex-1 flex items-center justify-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform ${
                          analyticsLoading
                            ? "bg-gray-400 cursor-not-allowed text-gray-200"
                            : "bg-blue-600 hover:bg-blue-700 hover:shadow-lg text-white hover:scale-105"
                        }`}
                      >
                        {analyticsLoading && selectedChannel?.id === ch.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                            Loading...
                          </>
                        ) : (
                          <>
                            <FaChartBar className="mr-2" />
                            Analysis
                          </>
                        )}
                      </button>
                      <button
                        onClick={() => onShowImages(ch.id)}
                        disabled={analyticsLoading}
                        className={`flex-1 flex items-center justify-center px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transform ${
                          analyticsLoading
                            ? "bg-gray-400 cursor-not-allowed text-gray-200"
                            : "bg-green-600 hover:bg-green-700 hover:shadow-lg text-white hover:scale-105"
                        }`}
                      >
                        <FaImages className="mr-2" />
                        Images
                      </button>
                    </div>
                  </div>

                  {selectedChannel?.id === ch.id && (
                    <div className="absolute top-3 right-3">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="w-full lg:w-1/3 space-y-4 mt-6 lg:mt-0">
          <div className="bg-white shadow-lg rounded-xl overflow-hidden relative">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-400 p-5 text-white">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <div className="w-9 h-9 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center">
                    <FaChartBar className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      Channel Analysis
                    </h3>
                    {selectedChannel && (
                      <p
                        className="text-indigo-100 text-sm truncate max-w-48"
                        title={selectedChannel.title}
                      >
                        {selectedChannel.title}
                      </p>
                    )}
                  </div>
                </div>
                <button
                  onClick={handleExport}
                  disabled={
                    exportLoading ||
                    !selectedChannel ||
                    !analytics ||
                    !!analytics.error
                  }
                  className={`p-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 ${
                    exportLoading ||
                    !selectedChannel ||
                    !analytics ||
                    !!analytics.error
                      ? "bg-gray-500 bg-opacity-20 cursor-not-allowed"
                      : "bg-blue-500 bg-opacity-20 hover:bg-opacity-30"
                  }`}
                  title={
                    exportLoading
                      ? "Exporting..."
                      : !selectedChannel
                      ? "Select a channel first"
                      : !analytics
                      ? "Run analysis first"
                      : analytics.error
                      ? "Fix analysis errors first"
                      : "Export Analytics"
                  }
                >
                  {exportLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                  ) : (
                    <FaDownload className="text-white" />
                  )}
                </button>
              </div>

              {exportError && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{exportError}</p>
                  <button
                    onClick={() => setExportError(null)}
                    className="text-red-500 hover:text-red-700 text-xs underline mt-1"
                  >
                    Dismiss
                  </button>
                </div>
              )}
            </div>

            <div className="p-5 space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time Range Filter
                </label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <input
                      id="days"
                      type="number"
                      min={1}
                      value={days}
                      onChange={(e) => setDays(Number(e.target.value))}
                      disabled={analyticsLoading}
                      className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        analyticsLoading ? "bg-gray-100 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter number of days"
                    />
                  </div>
                  {analyticsLoading ? (
                    <button
                      onClick={handleStopAnalysis}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center space-x-2"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Stop</span>
                    </button>
                  ) : (
                    <button
                      onClick={fetchAnalytics}
                      disabled={!selectedChannelId}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 ${
                        !selectedChannelId
                          ? "bg-gray-400 cursor-not-allowed text-gray-200"
                          : "bg-blue-600 hover:bg-blue-700 text-white"
                      }`}
                    >
                      <span>Apply</span>
                    </button>
                  )}
                </div>
              </div>

              {selectedChannel ? (
                analytics ? (
                  analytics.error ? (
                    <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <svg
                            className="h-5 w-5 text-red-400"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-red-800">
                            Error loading analytics
                          </h3>
                          <p className="text-sm text-red-700 mt-1">
                            {analytics.error}
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-blue-50 rounded-lg p-4 text-center">
                        <div className="text-2xl font-bold text-blue-600">
                          {analytics.total_images || 0}
                        </div>
                        <div className="text-sm text-blue-800">
                          Total Images
                        </div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-4 text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {analytics.dates?.length || 0}
                        </div>
                        <div className="text-sm text-green-800">
                          Active Days
                        </div>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-4 text-center col-span-2">
                        <div className="text-sm text-purple-800 mb-1">
                          Date Range
                        </div>
                        <div className="text-xs text-purple-600">
                          {analytics.first_date || "N/A"} →{" "}
                          {analytics.last_date || "N/A"}
                        </div>
                      </div>
                    </div>
                  )
                ) : analyticsLoading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-2 border-indigo-500 border-t-transparent"></div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FaChartBar className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <p>Loading analytics...</p>
                  </div>
                )
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FaChartBar className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <p>Select a channel to view analytics</p>
                </div>
              )}

              {selectedChannel && (
                <button
                  onClick={() => onShowImages(selectedChannel.id)}
                  className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                >
                  <FaImages />
                  <span>View Images</span>
                </button>
              )}
            </div>

            {analyticsLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10 rounded-xl">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto mb-4"></div>
                  <p className="text-gray-600 font-medium">
                    Analyzing channel data...
                  </p>
                  <p className="text-gray-500 text-sm mt-1">
                    This may take a while for channels with lots of data
                  </p>
                  <button
                    onClick={handleStopAnalysis}
                    className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center space-x-2 mx-auto"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span>Stop Analysis</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
