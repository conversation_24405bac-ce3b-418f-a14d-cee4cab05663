"""
Service for fetching and managing verification data.
Handles retrieving batch files with their annotator reviews for verification.
"""

import logging
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from sqlalchemy.sql import text
import json

from core.session_manager import get_project_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.users import users
from post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from core.session_manager import get_master_db_context

logger = logging.getLogger(__name__)


class VerifierDataService:
    """
    Service for managing verifier data operations.
    Fetches batch files with annotator reviews for verification workflow.
    """

    def __init__(self):
        pass

    async def get_user_active_project(self, user_id: int) -> Optional[str]:
        """Get the user's active project from master database."""
        try:
            async with get_master_db_context() as session:
                result = await session.execute(
                    select(users).where(users.id == user_id)
                )
                user = result.scalar_one_or_none()
                if user:
                    return user.active_project
                return None
        except Exception as e:
            logger.error(f"Error getting user active project: {str(e)}")
            return None

    async def get_project_allocation_strategy(self, project_code: str) -> Optional[AllocationStrategies]:
        """Get the allocation strategy for a project."""
        try:
            async with get_master_db_context() as session:
                # Get project info
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    logger.warning(f"No allocation strategy found for project {project_code}")
                    return None
                
                # Get strategy info
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                return strategy
                
        except Exception as e:
            logger.error(f"Error getting project allocation strategy: {str(e)}")
            return None

    async def get_verifier_assigned_batch(self, project_code: str, verifier_id: int) -> Optional[Dict[str, Any]]:
        """Get the batch assigned to this verifier from project_users.current_batch."""
        try:
            async with get_project_db_session(project_code) as session:
                # First, get the current batch ID from project_users table
                user_batch_query = text("""
                    SELECT current_batch
                    FROM project_users 
                    WHERE user_id = :verifier_id
                """)
                user_result = await session.execute(user_batch_query, {"verifier_id": verifier_id})
                user_row = user_result.fetchone()
                
                if not user_row or not user_row[0]:
                    logger.info(f"No current batch found in project_users for verifier {verifier_id}")
                    return None
                
                batch_id = user_row[0]
                logger.info(f"Found current_batch in project_users: {batch_id} for verifier {verifier_id}")
                
                # Now get the batch details using the batch_id
                batch_query = text("""
                    SELECT id, batch_identifier, annotation_count, completion_count, total_files
                    FROM allocation_batches 
                    WHERE id = :batch_id
                """)
                batch_result = await session.execute(batch_query, {"batch_id": batch_id})
                batch_row = batch_result.fetchone()
                
                if batch_row:
                    return {
                        "batch_id": batch_row[0],
                        "batch_identifier": batch_row[1],
                        "annotation_count": batch_row[2],
                        "completion_count": batch_row[3],
                        "total_files": batch_row[4]
                    }
                
                logger.warning(f"Batch {batch_id} not found in allocation_batches table")
                return None
        except Exception as e:
            logger.error(f"Error getting verifier assigned batch: {str(e)}")
            return None

    async def get_verification_batch_data(self, project_code: str, batch_id: int, annotation_count: int, strategy_type: str = "sequential") -> Dict[str, Any]:
        """
        Get all files in the batch with their annotator reviews for verification.
        
        Args:
            project_code: Project code
            batch_id: ID of the batch to verify
            annotation_count: Number of annotators per file
            strategy_type: Type of strategy (sequential or parallel)
            
        Returns:
            Dictionary containing files and their reviews
        """
        try:
            async with get_project_db_session(project_code) as session:
                if strategy_type == "sequential":
                    # For sequential, only fetch annotator_1_review
                    annotator_columns_str = "fa.annotator_1, fa.annotator_1_review"
                else:
                    # For parallel, fetch all annotator reviews (original logic)
                    annotator_columns = []
                    for i in range(1, annotation_count + 1):
                        annotator_columns.extend([
                            f"fa.annotator_{i}",
                            f"fa.annotator_{i}_review"
                        ])
                    annotator_columns_str = ", ".join(annotator_columns)
                
                # Query to get all files in the batch with their reviews
                query = text(f"""
                    SELECT 
                        fr.id as file_id,
                        fr.file_identifier,
                        fr.original_filename,
                        fr.file_type,
                        fr.file_extension,
                        fr.storage_location,
                        fr.file_size_bytes,
                        fa.id as allocation_id,
                        fa.processing_status,
                        fa.completion_count,
                        {annotator_columns_str}
                    FROM files_registry fr
                    JOIN file_allocations fa ON fr.id = fa.file_id
                    WHERE fa.batch_id = :batch_id
                    ORDER BY fr.sequence_order, fr.id
                """)
                
                result = await session.execute(query, {"batch_id": batch_id})
                rows = result.fetchall()
                
                files_data = []
                logger.info(f"Found {len(rows)} files in batch {batch_id} for verification")
                
                for row in rows:
                    # Extract file info
                    file_info = {
                        "file_id": row[0],
                        "file_identifier": row[1],
                        "original_filename": row[2] or row[1].split('/')[-1] if row[1] else "Unknown",
                        "file_type": row[3],
                        "file_extension": row[4],
                        "storage_location": row[5],
                        "file_size_bytes": row[6],
                        "allocation_id": row[7],
                        "processing_status": row[8],
                        "completion_count": row[9]
                    }
                    
                    logger.info(f"Processing file {row[0]} ({file_info['original_filename']}), completion_count: {row[9]}, row length: {len(row)}")
                    
                    # Extract annotator reviews
                    reviews = []
                    col_index = 10  # Start after the basic file info columns
                    
                    logger.debug(f"Extracting reviews for file {row[0]}, annotation_count: {annotation_count}, row length: {len(row)}")
                    
                    for i in range(1, annotation_count + 1):
                        annotator_id = row[col_index] if col_index < len(row) else None
                        annotator_review = row[col_index + 1] if col_index + 1 < len(row) else None
                        
                        logger.info(f"File {row[0]} Annotator {i}: ID={annotator_id}, Review exists: {annotator_review is not None}, Review length: {len(str(annotator_review)) if annotator_review else 0}")
                        
                        if annotator_id and annotator_review:
                            # Parse JSON review data
                            review_data = annotator_review
                            if isinstance(annotator_review, str):
                                try:
                                    review_data = json.loads(annotator_review)
                                    logger.info(f"Successfully parsed JSON review for annotator {annotator_id}")
                                except json.JSONDecodeError:
                                    logger.warning(f"Failed to parse JSON review for annotator {annotator_id}")
                                    review_data = {"raw_data": annotator_review}
                            
                            reviews.append({
                                "annotator_id": annotator_id,
                                "annotator_number": i,
                                "review_data": review_data,
                                "submitted_at": "2024-01-01"  # Add default timestamp
                            })
                            logger.info(f"Added review for annotator {annotator_id} (#{i})")
                        else:
                            logger.warning(f"Missing review data for annotator {i} on file {row[0]}")
                        
                        col_index += 2  # Move to next annotator columns
                    
                    # Ensure reviews is always set (even if empty)
                    file_info["reviews"] = reviews
                    logger.info(f"File {row[0]} ({file_info['original_filename']}) has {len(reviews)} reviews")
                    files_data.append(file_info)
                
                total_reviews = sum(len(file["reviews"]) for file in files_data)
                logger.info(f"Total reviews found for batch {batch_id}: {total_reviews} across {len(files_data)} files")
                
                return {
                    "success": True,
                    "batch_id": batch_id,
                    "files": files_data,
                    "total_files": len(files_data),
                    "annotation_count": annotation_count
                }
                
                
        except Exception as e:
            logger.error(f"Error getting verification batch data: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to get verification data: {str(e)}"
            }

    async def get_verifier_verification_data(self, verifier_id: int) -> Dict[str, Any]:
        """
        Get verification data for a verifier's assigned batch.
        This is the main method called when verifier needs to start verification.
        
        Args:
            verifier_id: ID of the verifier
            
        Returns:
            Dictionary containing batch and files data for verification
        """
        try:
            logger.info(f"Getting verification data for verifier {verifier_id}")
            
            # 1. Get verifier's active project
            project_code = await self.get_user_active_project(verifier_id)
            if not project_code:
                return {
                    "success": False,
                    "error": "Verifier has no active project assigned",
                    "error_code": "NO_ACTIVE_PROJECT"
                }
            
            # 2. Get project allocation strategy
            strategy = await self.get_project_allocation_strategy(project_code)
            strategy_type = "sequential"  # Default
            if strategy and strategy.strategy_type:
                strategy_type = strategy.strategy_type.value
            
            logger.info(f"Project strategy type: {strategy_type}")
            
            # 3. Get the batch assigned to this verifier
            batch_info = await self.get_verifier_assigned_batch(project_code, verifier_id)
            if not batch_info:
                return {
                    "success": False,
                    "error": "No batch assigned to verifier",
                    "error_code": "NO_ASSIGNED_BATCH"
                }
            
            # 4. Get all files and reviews for this batch
            verification_data = await self.get_verification_batch_data(
                project_code,
                batch_info["batch_id"],
                batch_info["annotation_count"],
                strategy_type
            )
            
            if not verification_data["success"]:
                return verification_data
            
            # 5. Add batch metadata to the response
            verification_data.update({
                "batch_info": batch_info,
                "project_code": project_code,
                "strategy_type": strategy_type
            })
            
            logger.info(f"Successfully retrieved verification data for batch {batch_info['batch_identifier']}")
            return verification_data
            
        except Exception as e:
            logger.error(f"Error getting verifier verification data: {str(e)}")
            return {
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "error_code": "INTERNAL_ERROR"
            }

    async def prepare_verification_files_for_cache(self, verification_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Prepare verification files data in the format expected by the frontend cache.
        Review data is cached separately for each file to allow file-by-file fetching.
        
        Args:
            verification_data: Raw verification data from database
            
        Returns:
            List of file objects formatted for frontend consumption
        """
        try:
            from cache.annotator_cache import cache_file_review_data
            
            files_for_cache = []
            batch_id = verification_data.get("batch_id")
            
            for file_data in verification_data.get("files", []):
                file_id = file_data["file_id"]
                
                # Cache review data separately for this file
                if file_data.get("reviews") and batch_id:
                    review_cache_data = {
                        "file_id": file_id,
                        "batch_id": batch_id,
                        "reviews": file_data.get("reviews", [])
                    }
                    await cache_file_review_data(file_id, batch_id, review_cache_data)
                    logger.info(f"Cached review data for file {file_id} in batch {batch_id}")
                
                # Extract file path for media serving
                file_path = file_data.get("file_identifier", "")
                if not file_path and file_data.get("storage_location"):
                    storage_loc = file_data["storage_location"]
                    if isinstance(storage_loc, dict) and "path" in storage_loc:
                        file_path = storage_loc["path"]
                    elif isinstance(storage_loc, str):
                        try:
                            parsed_loc = json.loads(storage_loc)
                            file_path = parsed_loc.get("path", storage_loc)
                        except json.JSONDecodeError:
                            file_path = storage_loc
                
                # Get reviews data, ensure it's always a list
                reviews_data = file_data.get("reviews", [])
                if not isinstance(reviews_data, list):
                    logger.warning(f"Reviews for file {file_id} is not a list: {type(reviews_data)}")
                    reviews_data = []
                
                logger.info(f"File {file_id} prepared with {len(reviews_data)} reviews for parallel verification")
                
                # Check if this is a CSV project
                file_type = file_data.get("file_type")
                is_csv_file = (file_type == 'csv' or 
                              (hasattr(file_type, '_value_') and file_type._value_ == 'csv') or
                              (isinstance(file_type, str) and 'csv' in file_type.lower()))
                
                # For CSV projects, use file_identifier as content, not as file path
                if is_csv_file:
                    # For CSV files, the file_identifier contains the actual content
                    csv_content = file_data.get("file_identifier", "")
                    cache_file = {
                        "file_id": file_id,
                        "filename": file_data["original_filename"] or f"Row {file_id}",
                        "file_path": csv_content,  # Content for CSV viewer
                        "file_type": "csv",  # Ensure consistent string type
                        "file_extension": "csv",
                        "processing_status": file_data.get("processing_status"),
                        "completion_count": file_data.get("completion_count"),
                        "reviews": reviews_data,
                        "annotator_reviews": reviews_data,
                        "batch_id": batch_id,
                        "url": csv_content,  # Pass content directly for CSV viewer
                        "is_csv": True  # Flag for frontend detection
                    }
                else:
                    # Original logic for non-CSV files
                    cache_file = {
                        "file_id": file_id,
                        "filename": file_data["original_filename"],
                        "file_path": file_path,
                        "file_type": file_data.get("file_type"),
                        "file_extension": file_data.get("file_extension"),
                        "processing_status": file_data.get("processing_status"),
                        "completion_count": file_data.get("completion_count"),
                        "reviews": reviews_data,
                        "annotator_reviews": reviews_data,
                        "batch_id": batch_id,
                        "url": f"/api/annotator/image{file_path}" if file_path else None
                    }
                
                files_for_cache.append(cache_file)
            
            total_reviews_cached = sum(len(f.get("annotator_reviews", [])) for f in files_for_cache)
            logger.info(f"Prepared {len(files_for_cache)} files with {total_reviews_cached} total reviews for parallel verification cache")
            
            return files_for_cache
            
        except Exception as e:
            logger.error(f"Error preparing verification files for cache: {str(e)}")
            return []

    async def save_verifier_review(self, project_code: str, file_id: int, verifier_id: int, review_data: Dict[str, Any]) -> bool:
        """
        Save verifier review for a specific file.
        
        Args:
            project_code: Project code
            file_id: ID of the file being reviewed
            verifier_id: ID of the verifier
            review_data: Review data including decision and comments
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Update file_allocations with verifier review
                update_query = text("""
                    UPDATE file_allocations 
                    SET verifier_review = :review_data
                    WHERE file_id = :file_id AND verifier = :verifier_id
                """)
                
                await session.execute(update_query, {
                    "review_data": json.dumps(review_data),
                    "file_id": file_id,
                    "verifier_id": verifier_id
                })
                
                await session.commit()
                logger.info(f"Saved verifier review for file {file_id} by verifier {verifier_id}")
                return True
                
                
        except Exception as e:
            logger.error(f"Error saving verifier review: {str(e)}")
            return False

    async def get_verifier_review(self, project_code: str, file_id: int, verifier_id: int) -> Optional[Dict[str, Any]]:
        """
        Get verifier review for a specific file.
        
        Args:
            project_code: Project code
            file_id: ID of the file
            verifier_id: ID of the verifier
            
        Returns:
            Optional[Dict]: Review data if found, None otherwise
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Get verifier review from file_allocations
                query = text("""
                    SELECT verifier_review
                    FROM file_allocations 
                    WHERE file_id = :file_id AND verifier = :verifier_id
                """)
                
                result = await session.execute(query, {
                    "file_id": file_id,
                    "verifier_id": verifier_id
                })
                row = result.fetchone()
                
                if row and row[0]:
                    try:
                        return json.loads(row[0])
                    except json.JSONDecodeError:
                        logger.warning(f"Invalid JSON in verifier review for file {file_id}")
                        return None
                
                return None
                
                
        except Exception as e:
            logger.error(f"Error getting verifier review: {str(e)}")
            return None
