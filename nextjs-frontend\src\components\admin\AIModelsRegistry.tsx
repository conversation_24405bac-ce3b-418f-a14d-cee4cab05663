"use client";

import { useState, useEffect } from "react";
import { FaPlus, FaEdit, FaTrash, FaCheck, FaTimes, FaMinus } from "react-icons/fa";
import toast from "react-hot-toast";

import { API_BASE_URL } from "@/lib/api";

interface AIModel {
  id?: number;
  model_name: string;
  model_id: string;
  supported_file_types: string[];
  input_requirements?: Record<string, unknown>;
  output_format: Record<string, unknown>;
  deployment_status: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  description?: string;
}

interface AIModelFormProps {
  model?: AIModel;
  onClose: () => void;
  onSuccess: () => void;
}

function AIModelForm({ model, onClose, onSuccess }: AIModelFormProps) {
  const isEditing = !!model;
  
  const [formData, setFormData] = useState<AIModel>({
    model_name: model?.model_name || "",
    model_id: model?.model_id || "",
    supported_file_types: model?.supported_file_types || [],
    input_requirements: model?.input_requirements || {},
    output_format: model?.output_format || {},
    deployment_status: model?.deployment_status || "inactive",
    description: model?.description || "",
  });
  
  const [fileType, setFileType] = useState("");
  const [jsonError, setJsonError] = useState({
    input: false,
    output: false,
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleJsonChange = (field: "input_requirements" | "output_format", value: string) => {
    try {
      const parsedJson = value.trim() ? JSON.parse(value) : {};
      setFormData({ ...formData, [field]: parsedJson });
      setJsonError({ ...jsonError, [field === "input_requirements" ? "input" : "output"]: false });
    } catch {
      setJsonError({ ...jsonError, [field === "input_requirements" ? "input" : "output"]: true });
    }
  };

  const addFileType = () => {
    if (fileType && !formData.supported_file_types.includes(fileType)) {
      setFormData({
        ...formData,
        supported_file_types: [...formData.supported_file_types, fileType],
      });
      setFileType("");
    }
  };

  const removeFileType = (index: number) => {
    const newTypes = [...formData.supported_file_types];
    newTypes.splice(index, 1);
    setFormData({ ...formData, supported_file_types: newTypes });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (jsonError.input || jsonError.output) {
      toast.error("Please fix JSON format errors before submitting");
      return;
    }
    
    if (!formData.model_name || !formData.model_id || formData.supported_file_types.length === 0) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    setLoading(true);
    
    try {
      const url = isEditing 
        ? `${API_BASE_URL}/ai-models/${model.model_id}`
        : `${API_BASE_URL}/ai-models/`;
      
      const method = isEditing ? "PUT" : "POST";
      
      const res = await fetch(url, {
        method,
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
      
      if (!res.ok) {
        // Handle duplicate model_id gracefully
        if (res.status === 409) {
          const data = await res.json().catch(() => ({ detail: "Model with this ID already exists" }));
          toast.error(data.detail || "Model with this ID already exists");
          return;
        }
        // Attempt to read error message from backend, otherwise generic
        const data = await res.json().catch(() => ({}));
        const message = data.detail || `Request failed with status ${res.status}`;
        toast.error(message);
        return;
      }
      
      toast.success(`Model ${isEditing ? "updated" : "created"} successfully`);
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error saving model:", error);
      toast.error(`Failed to ${isEditing ? "update" : "create"} model`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Model Name *
          </label>
          <input
            type="text"
            name="model_name"
            value={formData.model_name}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., GPT-4 Vision"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Model ID *
          </label>
          <input
            type="text"
            name="model_id"
            value={formData.model_id}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., gpt-4-vision"
            required
            disabled={isEditing}
          />
          {isEditing && (
            <p className="text-xs text-gray-500 mt-1">Model ID cannot be changed after creation</p>
          )}
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          name="description"
          value={formData.description || ""}
          onChange={handleChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Describe the model's capabilities and use cases"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Supported File Types *
        </label>
        <div className="flex flex-wrap gap-2 mb-2">
          {formData.supported_file_types.map((type, index) => (
            <div 
              key={index} 
              className="flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full"
            >
              <span>{type}</span>
              <button
                type="button"
                onClick={() => removeFileType(index)}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                <FaMinus size={10} />
              </button>
            </div>
          ))}
        </div>
        <div className="flex">
          <input
            type="text"
            value={fileType}
            onChange={(e) => setFileType(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., image, pdf, audio"
          />
          <button
            type="button"
            onClick={addFileType}
            className="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600"
          >
            <FaPlus />
          </button>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Input Requirements (JSON)
        </label>
        <textarea
          value={JSON.stringify(formData.input_requirements, null, 2)}
          onChange={(e) => handleJsonChange("input_requirements", e.target.value)}
          rows={5}
          className={`w-full px-3 py-2 border rounded-md font-mono text-sm ${
            jsonError.input 
              ? "border-red-500 focus:ring-red-500" 
              : "border-gray-300 focus:ring-blue-500"
          } focus:outline-none focus:ring-2`}
          placeholder='{"format": "base64", "max_size_mb": 10}'
        />
        {jsonError.input && (
          <p className="text-red-500 text-xs mt-1">Invalid JSON format</p>
        )}
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Output Format (JSON)
        </label>
        <textarea
          value={JSON.stringify(formData.output_format, null, 2)}
          onChange={(e) => handleJsonChange("output_format", e.target.value)}
          rows={5}
          className={`w-full px-3 py-2 border rounded-md font-mono text-sm ${
            jsonError.output 
              ? "border-red-500 focus:ring-red-500" 
              : "border-gray-300 focus:ring-blue-500"
          } focus:outline-none focus:ring-2`}
          placeholder='{"text": "string", "confidence": "number"}'
        />
        {jsonError.output && (
          <p className="text-red-500 text-xs mt-1">Invalid JSON format</p>
        )}
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Deployment Status
        </label>
        <select
          name="deployment_status"
          value={formData.deployment_status}
          onChange={handleChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="inactive">Inactive</option>
          <option value="active">Active</option>
        </select>
      </div>
      
      <div className="flex justify-end gap-2">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100"
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-blue-300"
          disabled={loading || jsonError.input || jsonError.output}
        >
          {loading ? (
            <span className="flex items-center gap-2">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              {isEditing ? "Updating..." : "Creating..."}
            </span>
          ) : (
            <>{isEditing ? "Update" : "Create"} Model</>
          )}
        </button>
      </div>
    </form>
  );
}

export default function AIModelsRegistry() {
  const [models, setModels] = useState<AIModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingModel, setEditingModel] = useState<AIModel | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<number | null>(null);

  useEffect(() => {
    fetchModels();
  }, []);

  const fetchModels = async () => {
    setLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/ai-models/`, {
        credentials: "include",
      });
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      const data = await res.json();
      setModels(data);
    } catch (error) {
      console.error("Error fetching AI models:", error);
      toast.error("Failed to load AI models");
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (model: AIModel) => {
    const newStatus = model.deployment_status === "active" ? "inactive" : "active";
    
    try {
      const res = await fetch(`${API_BASE_URL}/ai-models/${model.model_id}/status`, {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      await fetchModels();
      toast.success(`Model ${model.model_name} is now ${newStatus}`);
    } catch (error) {
      console.error("Error updating model status:", error);
      toast.error("Failed to update model status");
    }
  };

  const handleDelete = async (id: number) => {
    if (confirmDelete !== id) {
      setConfirmDelete(id);
      return;
    }
    
    try {
      const modelToDelete = models.find(m => m.id === id);
      if (!modelToDelete) return;
      
      const res = await fetch(`${API_BASE_URL}/ai-models/${modelToDelete.model_id}`, {
        method: "DELETE",
        credentials: "include",
      });
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      await fetchModels();
      toast.success("Model deleted successfully");
    } catch (error) {
      console.error("Error deleting model:", error);
      toast.error("Failed to delete model");
    } finally {
      setConfirmDelete(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <>
      <div className="py-6 flex flex-col items-center">
        <div className="flex items-center gap-2 mb-1">
          <h1 className="text-6xl bold uppercase tracking-tight text-gray-900">
            AI Models
          </h1>
        </div>
        <div className="w-96 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-fuchsia-500 rounded-full shadow-lg" />
      </div>

      <div className="rounded-3xl p-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Available Models</h2>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-2 rounded-lg font-semibold shadow hover:from-blue-600 hover:to-blue-800 focus:outline-none"
          >
            <FaPlus />
            Add New Model
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500"></div>
          </div>
        ) : models.length === 0 ? (
          <div className="bg-gray-50 rounded-xl p-10 text-center">
            <p className="text-gray-500 text-lg">No AI models registered yet</p>
            <button
              onClick={() => setShowAddModal(true)}
              className="mt-4 bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 focus:outline-none"
            >
              Register Your First Model
            </button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white rounded-xl shadow-md">
              <thead className="bg-gray-100">
                <tr>
                  <th className="py-3 px-4 text-left">Name</th>
                  <th className="py-3 px-4 text-left">Model ID</th>
                  <th className="py-3 px-4 text-left">File Types</th>
                  <th className="py-3 px-4 text-left">Status</th>
                  <th className="py-3 px-4 text-left">Created</th>
                  <th className="py-3 px-4 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {models.map((model) => (
                  <tr key={model.id} className="border-t hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="font-medium">{model.model_name}</div>
                      <div className="text-xs text-gray-500">{model.description || "No description"}</div>
                    </td>
                    <td className="py-3 px-4 font-mono text-sm">{model.model_id}</td>
                    <td className="py-3 px-4">
                      <div className="flex flex-wrap gap-1">
                        {model.supported_file_types.map((type, idx) => (
                          <span
                            key={idx}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {type}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          model.deployment_status === "active"
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {model.deployment_status}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-sm">
                      {formatDate(model.created_at || "")}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleToggleStatus(model)}
                          className={`p-2 rounded-full ${
                            model.deployment_status === "active"
                              ? "bg-red-100 text-red-600 hover:bg-red-200"
                              : "bg-green-100 text-green-600 hover:bg-green-200"
                          }`}
                          title={model.deployment_status === "active" ? "Deactivate" : "Activate"}
                        >
                          {model.deployment_status === "active" ? <FaTimes /> : <FaCheck />}
                        </button>
                        <button
                          onClick={() => setEditingModel(model)}
                          className="p-2 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200"
                          title="Edit"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDelete(model.id as number)}
                          className={`p-2 rounded-full ${
                            confirmDelete === model.id
                              ? "bg-red-500 text-white"
                              : "bg-red-100 text-red-600 hover:bg-red-200"
                          }`}
                          title={confirmDelete === model.id ? "Confirm Delete" : "Delete"}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Modal with AIModelForm component */}
      {(showAddModal || editingModel) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-2xl font-bold mb-4">
              {editingModel ? "Edit Model" : "Add New Model"}
            </h2>
            
            <AIModelForm
              model={editingModel || undefined}
              onClose={() => {
                setShowAddModal(false);
                setEditingModel(null);
              }}
              onSuccess={fetchModels}
            />
          </div>
        </div>
      )}
    </>
  );
}