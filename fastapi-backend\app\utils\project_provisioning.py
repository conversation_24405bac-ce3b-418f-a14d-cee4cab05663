"""
Database provisioning utilities for creating new project databases.
"""

import asyncio
import subprocess
import os
import asyncpg # type: ignore
from typing import Optional, Dict
import logging
import uuid
import datetime
from dotenv import load_dotenv
load_dotenv()

import logging
from typing import Dict
from sqlalchemy.exc import SQLAlchemyError
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from core.session_manager import get_project_db_session
from sqlalchemy import select

logger = logging.getLogger(__name__)

class ProjectDatabaseProvisioner:
    def __init__(self):
        # Database connection settings - all on port 5432
        self.admin_host = "***********"
        self.admin_port = 5432  # Standard PostgreSQL port
        self.admin_user = os.getenv("DB_ADMIN_USER")
        self.admin_password = os.getenv("DB_ADMIN_PASSWORD")
        self.app_user = "mansi"  # The user that will access project databases
        self.pgadmin_enabled = True  # Set to True if you want pgAdmin setup
        
    async def create_project_database_with_strategy(self, project_code: str, custom_db_name: str = None, strategy = None) -> dict:
        """
        Create a new project database with dynamic schema based on allocation strategy.
        
        Args:
            project_code: The project code from projects_registry
            custom_db_name: Optional custom database name
            strategy: AllocationStrategies object defining the schema requirements
            
        Returns:
            dict: Result with success status and details
        """
        database_name = custom_db_name if custom_db_name else f"project_{project_code}"
        
        try:
            # Step 1: Create the database
            await self._create_database(database_name)
            logger.info(f"Database {database_name} created successfully")
            
            # Step 2: Generate and run dynamic migrations based on strategy
            from utils.dynamic_schema_generator import DynamicSchemaGenerator
            schema_generator = DynamicSchemaGenerator(strategy)
            await self._run_dynamic_migrations(database_name, schema_generator)
            logger.info(f"Dynamic migrations completed for {database_name} using strategy {strategy.strategy_name}")
            
            # Step 3: Verify database
            table_count = await self._verify_database(database_name)
            logger.info(f"Database {database_name} verified with {table_count} tables")
            
            # Step 4: Setup pgAdmin access (if enabled)
            pgadmin_info = None
            if self.pgadmin_enabled:
                pgadmin_info = await self._setup_pgadmin_access(database_name)
            
            return {
                "success": True,
                "database_name": database_name,
                "table_count": table_count,
                "strategy_name": strategy.strategy_name,
                "strategy_id": strategy.id,
                "connection_info": {
                    "host": self.admin_host,
                    "port": self.admin_port,
                    "database": database_name,
                    "username": self.app_user,
                    "connection_string": f"postgresql://{self.app_user}:pass123@{self.admin_host}:{self.admin_port}/{database_name}"
                },
                "pgadmin_info": pgadmin_info,
                "message": f"Project database {database_name} created successfully with strategy {strategy.strategy_name}"
            }
            
        except Exception as e:
            logger.error(f"Failed to create project database {database_name} with strategy: {str(e)}")
            
            # Attempt cleanup
            try:
                await self._cleanup_failed_database(database_name)
            except Exception as cleanup_error:
                logger.error(f"Cleanup failed: {cleanup_error}")
            
            return {
                "success": False,
                "database_name": database_name,
                "error": str(e),
                "message": f"Failed to create project database {database_name} with strategy"
            }
        
    async def create_project_database(self, project_code: str, custom_db_name: str = None) -> dict:
        """
        Create a new project database with the given project code.
        
        Args:
            project_code: The project code from projects_registry
            custom_db_name: Optional custom database name
            
        Returns:
            dict: Result with success status and details
        """
        database_name = custom_db_name if custom_db_name else f"project_{project_code}"
        
        try:
            # Step 1: Create the database
            await self._create_database(database_name)
            logger.info(f"Database {database_name} created successfully")
            
            # Step 2: Run migrations
            await self._run_migrations(database_name)
            logger.info(f"Migrations completed for {database_name}")
            
            # Step 3: Verify database
            table_count = await self._verify_database(database_name)
            logger.info(f"Database {database_name} verified with {table_count} tables")
            
            # Step 4: Setup pgAdmin access (if enabled)
            pgadmin_info = None
            if self.pgadmin_enabled:
                pgadmin_info = await self._setup_pgadmin_access(database_name)
            
            return {
                "success": True,
                "database_name": database_name,
                "table_count": table_count,
                "connection_info": {
                    "host": self.admin_host,
                    "port": self.admin_port,
                    "database": database_name,
                    "username": self.app_user,
                    "connection_string": f"postgresql://{self.app_user}:pass123@{self.admin_host}:{self.admin_port}/{database_name}"
                },
                "pgadmin_info": pgadmin_info,
                "message": f"Project database {database_name} created successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to create project database {database_name}: {str(e)}")
            
            # Attempt cleanup
            try:
                await self._cleanup_failed_database(database_name)
            except Exception as cleanup_error:
                logger.error(f"Cleanup failed: {cleanup_error}")
            
            return {
                "success": False,
                "database_name": database_name,
                "error": str(e),
                "message": f"Failed to create project database {database_name}"
            }
    
    async def _create_database(self, database_name: str):
        """Create the database using the session manager."""
        from core.session_manager import get_master_db_context
        from sqlalchemy import text
        
        try:
            # Check if database already exists
            async with get_master_db_context() as session:
                result = await session.execute(
                    text("SELECT 1 FROM pg_database WHERE datname = :database_name"),
                    {"database_name": database_name}
                )
                exists = result.scalar()
            
            if exists:
                raise Exception(f"Database {database_name} already exists")
            
            # Create the database - use raw connection for DDL since we need admin privileges
            # Note: Database creation requires a direct connection to postgres database
            conn = None
            try:
                conn = await asyncpg.connect(
                    host=self.admin_host,
                    port=self.admin_port,
                    user=self.admin_user,
                    password=self.admin_password,
                    database="postgres"  # Connect to default postgres database
                )
                
                # Create the database (DDL operations require autocommit)
                await conn.execute(f'CREATE DATABASE "{database_name}"')
                
                # Grant permissions to app user
                await conn.execute(f'GRANT ALL PRIVILEGES ON DATABASE "{database_name}" TO {self.app_user}')
                
            finally:
                if conn:
                    await conn.close()
            
        except Exception as e:
            logger.error(f"Database creation failed: {e}")
            raise
    
    async def _setup_pgadmin_access(self, database_name: str) -> dict:
        """
        Prepare pgAdmin connection information and optionally auto-register.
        """
        try:
            connection_info = {
                "server_name": f"Project DB - {database_name}",
                "host": self.admin_host,
                "port": self.admin_port,
                "database": database_name,
                "username": self.app_user,
                "password": "pass123",  # You might want to handle this more securely
                "connection_url": f"postgresql://{self.app_user}:pass123@{self.admin_host}:{self.admin_port}/{database_name}",
                "pgadmin_instructions": [
                    "1. Open pgAdmin",
                    "2. Right-click 'Servers' → Create → Server",
                    f"3. General Tab: Name = 'Project DB - {database_name}'",
                    f"4. Connection Tab: Host = '{self.admin_host}', Port = '{self.admin_port}'",
                    f"5. Database = '{database_name}', Username = '{self.app_user}'",
                    "6. Save and connect"
                ]
            }
            
            logger.info(f"pgAdmin connection info prepared for {database_name}")
            return connection_info
            
        except Exception as e:
            logger.error(f"Failed to setup pgAdmin access: {str(e)}")
            return {"error": str(e)}
    
    async def get_pgadmin_servers_config(self, project_databases: list) -> dict:
        """
        Generate pgAdmin servers.json configuration for multiple project databases.
        This can be imported into pgAdmin for bulk server setup.
        """
        servers_config = {
            "Servers": {}
        }
        
        for i, db_name in enumerate(project_databases, 1):
            servers_config["Servers"][str(i)] = {
                "Name": f"Project DB - {db_name}",
                "Group": "Project Databases",
                "Host": self.admin_host,
                "Port": self.admin_port,
                "MaintenanceDB": db_name,
                "Username": self.app_user,
                "SSLMode": "prefer",
                "Comment": f"Auto-generated config for {db_name}"
            }
        
        return servers_config
    
    async def _run_migrations(self, database_name: str):
        """Run Alembic migrations for the project database."""
        import tempfile
        import shutil
        from pathlib import Path
        
        try:
            # Create the database URL for this specific project database
            db_url = f"postgresql+asyncpg://{self.app_user}:pass123@{self.admin_host}:{self.admin_port}/{database_name}"
            
            # Create a temporary directory for this specific migration
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create a fresh migration structure instead of copying old ones
                dest_migrations = temp_path / "project_db"
                dest_migrations.mkdir()
                
                # Create alembic.ini with the correct database URL
                alembic_ini_path = dest_migrations / "alembic.ini"
                alembic_content = f"""[alembic]
script_location = {dest_migrations}
sqlalchemy.url = {db_url.replace('postgresql+asyncpg://', 'postgresql+psycopg2://')}
"""
                with open(alembic_ini_path, 'w', encoding='utf-8') as f:
                    f.write(alembic_content)
                
                # Create env.py that imports from project_base
                env_py_path = dest_migrations / "env.py"
                env_py_content = '''"""
Alembic environment for project database
"""
import sys
import os
from pathlib import Path
from sqlalchemy import engine_from_config, pool
from alembic import context

# Robust path resolution for finding the app directory
def find_app_directory():
    """Find the app directory containing post_db module"""
    # Start with current file location
    current_dir = os.path.dirname(os.path.abspath(__file__))
    search_dir = current_dir
    max_depth = 20
    
    # Search up the directory tree
    for _ in range(max_depth):
        if os.path.exists(os.path.join(search_dir, 'post_db')):
            return search_dir
        parent = os.path.dirname(search_dir)
        if parent == search_dir:  # Reached root directory
            break
        search_dir = parent
    
    # Fallback strategies
    fallback_paths = [
        os.getcwd(),  # Current working directory
        os.path.join(os.getcwd(), 'app'),  # app subdirectory
        os.path.join(os.getcwd(), 'fastapi-backend', 'app'),  # common structure
    ]
    
    for path in fallback_paths:
        if os.path.exists(os.path.join(path, 'post_db')):
            return path
    
    # Last resort: try to find via __file__ from any imported module
    try:
        import post_db
        post_db_file = getattr(post_db, '__file__', None)
        if post_db_file:
            return os.path.dirname(os.path.dirname(post_db_file))
    except ImportError:
        pass
    
    # If all else fails, return None and let the import fail with a clear error
    return None

# Set up the path
app_dir = find_app_directory()
if app_dir:
    sys.path.insert(0, app_dir)
    print(f"Added to Python path: {app_dir}")
else:
    print("Warning: Could not find app directory containing post_db module")

config = context.config

# Import the ProjectBase with all project models
try:
    from post_db.project_base import ProjectBase
    target_metadata = ProjectBase.metadata
    print(f"Successfully loaded {len(target_metadata.tables)} tables for migration")
except ImportError as e:
    print(f"Failed to import ProjectBase: {e}")
    if app_dir:
        print(f"App directory: {app_dir}")
        print(f"Directory contents: {os.listdir(app_dir) if os.path.exists(app_dir) else 'Not found'}")
    else:
        print("App directory not found")
    raise

def run_migrations_offline():
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )
    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata
        )
        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''
                with open(env_py_path, 'w', encoding='utf-8') as f:
                    f.write(env_py_content)
                
                # Create versions directory
                versions_dir = dest_migrations / "versions"
                versions_dir.mkdir()
                
                # Create a fresh initial migration that creates all tables
                migration_file = versions_dir / "001_initial_create_all_tables.py"
                migration_content = '''"""initial create all tables

Revision ID: 001_initial
Revises: 
Create Date: 2025-08-29 13:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

revision = '001_initial'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """Create all project tables"""
    # Create project_metadata table
    op.create_table('project_metadata',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('project_code', sa.String(length=50), nullable=False),
        sa.Column('master_db_project_id', sa.Integer(), nullable=False),
        sa.Column('annotation_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('validation_rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('allocation_strategy', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('credentials', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('connection_type', sa.String(length=50), nullable=True),
        sa.Column('folder_path', sa.String(length=500), nullable=True),
        sa.Column('instructions', sa.Text(), nullable=True),
        sa.Column('batch_size', sa.Integer(), nullable=True),
        sa.Column('supported_file_types', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('file_processing_pipeline', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('quality_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('last_sync_with_master', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create allocation_batches table
    op.create_table('allocation_batches',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('batch_identifier', sa.String(length=100), nullable=False),
        sa.Column('batch_status', sa.String(length=50), default='created', nullable=False),
        sa.Column('total_files', sa.Integer(), nullable=False),
        sa.Column('file_list', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('skill_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('allocation_criteria', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('is_priority', sa.Boolean(), default=False, nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('deadline', sa.TIMESTAMP(), nullable=True),
        sa.Column('annotation_count', sa.Integer(), default=0, nullable=False),
        sa.Column('assignment_count', sa.Integer(), default=0, nullable=False),
        sa.Column('completion_count', sa.Integer(), default=0, nullable=False),
        sa.Column('custom_batch_config', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('batch_identifier', name='_batch_identifier_uc')
    )
    
    # Create files_registry table
    op.create_table('files_registry',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('batch_id', sa.Integer(), nullable=False),
        sa.Column('file_identifier', sa.String(length=255), nullable=False),
        sa.Column('original_filename', sa.String(length=500), nullable=True),
        sa.Column('file_type', sa.String(length=50), nullable=True),
        sa.Column('file_extension', sa.String(length=10), nullable=True),
        sa.Column('storage_location', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('file_size_bytes', sa.BigInteger(), nullable=True),
        sa.Column('file_hash', sa.String(length=64), nullable=True),
        sa.Column('processing_status', sa.String(length=50), default='pending', nullable=False),
        sa.Column('processed_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('preprocessing_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('processing_priority', sa.Integer(), default=1, nullable=False),
        sa.Column('client_priority', sa.Integer(), default=1, nullable=False),
        sa.Column('sequence_order', sa.Integer(), nullable=True),
        sa.Column('uploaded_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE')
    )
    
    # Create user_allocations table
    op.create_table('user_allocations',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('batch_id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('files_completed', sa.Integer(), default=0, nullable=False),
        sa.Column('total_files', sa.Integer(), nullable=True),
        sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('allocation_role', sa.String(length=50), default='annotator', nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=False),
        sa.Column('allocated_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('activation_deadline', sa.TIMESTAMP(), nullable=True),
        sa.Column('completion_deadline', sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE')
    )
    
    # Create file_allocations table
    op.create_table('file_allocations',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('file_id', sa.Integer(), nullable=False),
        sa.Column('batch_id', sa.Integer(), nullable=False),
        sa.Column('allocation_sequence', sa.Integer(), default=1, nullable=False),
        sa.Column('workflow_phase', sa.String(length=50), default='annotation', nullable=False),
        sa.Column('processing_status', sa.String(length=50), default='pending', nullable=False),
        sa.Column('processed_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('preprocessing_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('allocated_at', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('activation_deadline', sa.TIMESTAMP(), nullable=True),
        sa.Column('completion_deadline', sa.TIMESTAMP(), nullable=True),
        sa.Column('assignment_count', sa.Integer(), default=0, nullable=False),
        sa.Column('completion_count', sa.Integer(), default=0, nullable=False),
        sa.Column('allocation_rules', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('annotator_1', sa.Integer(), nullable=True, comment='References user_id in project_users table'),
        sa.Column('annotator_1_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for annotator 1'),
        sa.Column('annotator_2', sa.Integer(), nullable=True, comment='References user_id in project_users table'),
        sa.Column('annotator_2_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for annotator 2'),
        sa.Column('annotator_3', sa.Integer(), nullable=True, comment='References user_id in project_users table'),
        sa.Column('annotator_3_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for annotator 3'),
        sa.Column('verifier', sa.Integer(), nullable=True, comment='References user_id in project_users table'),
        sa.Column('verifier_review', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='Review data for verifier'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['file_id'], ['files_registry.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('file_id', 'allocation_sequence', name='_file_allocation_seq_uc')
    )
    

    

    

    
    # Create model_execution_logs table
    op.create_table('model_execution_logs',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('model_name', sa.String(length=255), nullable=False),
        sa.Column('batch_id', sa.Integer(), nullable=True),
        sa.Column('file_id', sa.Integer(), nullable=True),
        sa.Column('user_prompt', sa.Text(), nullable=True),
        sa.Column('system_prompt', sa.Text(), nullable=True),
        sa.Column('execution_start_time', sa.TIMESTAMP(), default=sa.func.now(), nullable=False),
        sa.Column('execution_end_time', sa.TIMESTAMP(), nullable=True),
        sa.Column('execution_duration_ms', sa.Integer(), nullable=True),
        sa.Column('execution_status', sa.String(length=50), nullable=False),
        sa.Column('input_data_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('output_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('confidence_scores', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('model_config_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('error_code', sa.String(length=50), nullable=True),
        sa.Column('retry_count', sa.Integer(), default=0, nullable=False),
        sa.Column('triggered_by', sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['batch_id'], ['allocation_batches.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['file_id'], ['files_registry.id'], ondelete='CASCADE')
    )
    
    # Create project_users table if it doesn't exist
    op.create_table('project_users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('role', sa.String(length=255), nullable=False),
        sa.Column('current_batch', sa.Integer(), nullable=True),
        sa.Column('completed_batches', postgresql.ARRAY(sa.Integer()), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'username', name='_user_id_username_uc')
    )

def downgrade():
    """Drop all project tables"""
    op.drop_table('model_execution_logs')
    op.drop_table('annotation_reviews')
    op.drop_table('annotations')
    op.drop_table('annotation_work')
    op.drop_table('file_allocations')
    op.drop_table('user_allocations')
    op.drop_table('files_registry')
    op.drop_table('allocation_batches')
    op.drop_table('project_metadata')
    op.drop_table('project_users')
'''
                with open(migration_file, 'w', encoding='utf-8') as f:
                    f.write(migration_content)
                
                # Set environment variables for this migration run
                env = os.environ.copy()
                env['PROJECT_DB_DATABASE_URL'] = db_url
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
                # Add the app directory to PYTHONPATH so imports work from temp directory
                app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Go up to app directory
                if 'PYTHONPATH' in env:
                    env['PYTHONPATH'] = f"{app_dir}{os.pathsep}{env['PYTHONPATH']}"
                else:
                    env['PYTHONPATH'] = app_dir
                
                # Run alembic with the fresh migration
                cmd = [
                    "alembic", "-c", str(alembic_ini_path), "upgrade", "head"
                ]
                
                logger.info(f"Running fresh migration command: {' '.join(cmd)}")
                logger.info(f"Target database: {database_name}")
                
                result = subprocess.run(
                    cmd,
                    env=env,
                    capture_output=True,
                    text=True,
                    check=False,
                    encoding='utf-8',
                    errors='replace',
                    cwd=str(dest_migrations)  # Run alembic from the migrations directory
                )
                
                # Log all output for debugging
                if result.stdout:
                    logger.info(f"Migration stdout: {result.stdout}")
                
                if result.stderr:
                    logger.warning(f"Migration stderr: {result.stderr}")
                
                # Check if migration actually succeeded
                if result.returncode != 0:
                    error_msg = f"Migration failed with return code {result.returncode}"
                    if result.stderr:
                        error_msg += f": {result.stderr}"
                    if result.stdout:
                        error_msg += f" (stdout: {result.stdout})"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                
                logger.info(f"Fresh migration completed successfully for {database_name}")
                
        except Exception as e:
            logger.error(f"Migration error for {database_name}: {str(e)}")
            raise
    
    async def _run_dynamic_migrations(self, database_name: str, schema_generator):
        """Run dynamic migrations based on the schema generator."""
        import tempfile
        import shutil
        from pathlib import Path
        
        try:
            # Create the database URL for this specific project database
            db_url = f"postgresql+asyncpg://{self.app_user}:pass123@{self.admin_host}:{self.admin_port}/{database_name}"
            
            # Create a temporary directory for this specific migration
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create a fresh migration structure
                dest_migrations = temp_path / "project_db"
                dest_migrations.mkdir()
                
                # Create alembic.ini with the correct database URL
                alembic_ini_path = dest_migrations / "alembic.ini"
                alembic_content = f"""[alembic]
script_location = {dest_migrations}
sqlalchemy.url = {db_url.replace('postgresql+asyncpg://', 'postgresql+psycopg2://')}
"""
                with open(alembic_ini_path, 'w', encoding='utf-8') as f:
                    f.write(alembic_content)
                
                # Create env.py that imports from project_base
                env_py_path = dest_migrations / "env.py"
                env_py_content = '''"""\nAlembic environment for project database\n"""\nimport sys\nfrom pathlib import Path\nfrom sqlalchemy import engine_from_config, pool\nfrom alembic import context\n\n# Add the app directory to Python path\nsys.path.insert(0, r"C:\\Users\\<USER>\\Desktop\\DADP\\DADP-Prod-FN\\fastapi-backend\\app")\n\nconfig = context.config\n\n# Import the ProjectBase with all project models\nfrom post_db.project_base import ProjectBase\ntarget_metadata = ProjectBase.metadata\n\ndef run_migrations_offline():\n    url = config.get_main_option("sqlalchemy.url")\n    context.configure(\n        url=url,\n        target_metadata=target_metadata,\n        literal_binds=True,\n        dialect_opts={"paramstyle": "named"},\n    )\n    with context.begin_transaction():\n        context.run_migrations()\n\ndef run_migrations_online():\n    connectable = engine_from_config(\n        config.get_section(config.config_ini_section),\n        prefix="sqlalchemy.",\n        poolclass=pool.NullPool,\n    )\n    with connectable.connect() as connection:\n        context.configure(\n            connection=connection, \n            target_metadata=target_metadata\n        )\n        with context.begin_transaction():\n            context.run_migrations()\n\nif context.is_offline_mode():\n    run_migrations_offline()\nelse:\n    run_migrations_online()\n'''
                with open(env_py_path, 'w', encoding='utf-8') as f:
                    f.write(env_py_content)
                
                # Create versions directory
                versions_dir = dest_migrations / "versions"
                versions_dir.mkdir()
                
                # Generate a unique revision ID
                revision_id = f"{uuid.uuid4().hex[:12]}"
                current_date = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
                
                # Generate the dynamic migration content
                migration_content = schema_generator.generate_migration_script()
                migration_content = migration_content.replace('{{REVISION_ID}}', revision_id)
                migration_content = migration_content.replace('{{CREATE_DATE}}', current_date)
                migration_content = migration_content.replace('{{STRATEGY_NAME}}', schema_generator.strategy.strategy_name)
                
                # Write the migration file
                migration_file = versions_dir / f"{revision_id}_dynamic_schema.py"
                with open(migration_file, 'w', encoding='utf-8') as f:
                    f.write(migration_content)
                
                # Set environment variables for this migration run
                env = os.environ.copy()
                env['PROJECT_DB_DATABASE_URL'] = db_url
                env['PYTHONIOENCODING'] = 'utf-8'
                env['PYTHONLEGACYWINDOWSFSENCODING'] = '0'
                # Add the app directory to PYTHONPATH so imports work from temp directory
                app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Go up to app directory
                if 'PYTHONPATH' in env:
                    env['PYTHONPATH'] = f"{app_dir}{os.pathsep}{env['PYTHONPATH']}"
                else:
                    env['PYTHONPATH'] = app_dir
                
                # Run alembic with the dynamic migration
                cmd = [
                    "alembic", "-c", str(alembic_ini_path), "upgrade", "head"
                ]
                
                logger.info(f"Running dynamic migration command: {' '.join(cmd)}")
                logger.info(f"Target database: {database_name}")
                logger.info(f"Using strategy: {schema_generator.strategy.strategy_name}")
                
                result = subprocess.run(
                    cmd,
                    env=env,
                    capture_output=True,
                    text=True,
                    check=False,
                    encoding='utf-8',
                    errors='replace',
                    cwd=str(dest_migrations)  # Run alembic from the migrations directory
                )
                
                # Log all output for debugging
                if result.stdout:
                    logger.info(f"Migration stdout: {result.stdout}")
                
                if result.stderr:
                    logger.warning(f"Migration stderr: {result.stderr}")
                
                # Check if migration actually succeeded
                if result.returncode != 0:
                    error_msg = f"Dynamic migration failed with return code {result.returncode}"
                    if result.stderr:
                        error_msg += f": {result.stderr}"
                    if result.stdout:
                        error_msg += f" (stdout: {result.stdout})"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                
                logger.info(f"Dynamic migration completed successfully for {database_name}")
                
        except Exception as e:
            logger.error(f"Dynamic migration error for {database_name}: {str(e)}")
            raise
    
    async def _verify_database(self, database_name: str) -> int:
        """Verify the database was created correctly by counting tables."""
        conn = None
        try:
            conn = await asyncpg.connect(
                host=self.admin_host,
                port=self.admin_port,
                user=self.app_user,
                password="pass123",  # Fixed password
                database=database_name
            )
            
            # Count tables in the database
            table_count = await conn.fetchval("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            
            # Expected tables from project_base.py
            expected_tables = [
                'project_metadata', 'files_registry', 'allocation_batches', 
                'user_allocations', 'file_allocations', 'project_users', 
                'model_execution_logs'
            ]
            
            if table_count < len(expected_tables):
                logger.warning(f"Expected {len(expected_tables)} tables, found {table_count}. This may be normal for new databases.")
                # Don't raise exception, just log warning
            
            return table_count
            
        finally:
            if conn:
                await conn.close()
    
    async def _cleanup_failed_database(self, database_name: str):
        """Clean up a failed database creation."""
        conn = None
        try:
            conn = await asyncpg.connect(
                host=self.admin_host,
                port=self.admin_port,
                user=self.admin_user,
                password=self.admin_password,
                database="postgres"
            )
            
            # Terminate any connections to the database
            await conn.execute(f"""
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = '{database_name}'
                AND pid <> pg_backend_pid()
            """)
            
            # Drop the database
            await conn.execute(f'DROP DATABASE IF EXISTS "{database_name}"')
            logger.info(f"Cleaned up failed database: {database_name}")
            
        finally:
            if conn:
                await conn.close()

# Convenience function for easy import
async def provision_project_database(project_code: str, custom_db_name: str = None, strategy_id: int = None) -> Dict[str, str]:
    """
    Ensure project DB exists and base schema is applied. Idempotent.
    
    Args:
        project_code: The project code from projects_registry
        custom_db_name: Optional custom database name
        strategy_id: Optional allocation strategy ID to use for schema generation
        
    Returns:
        Dict: Result with success status and details
    """
    try:
        # Fetch project info
        async with get_master_db_context() as session:
            result = await session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project = result.scalar_one_or_none()
            if not project:
                return {"success": False, "error": "Project not found"}
                
            # If strategy_id is provided, use it; otherwise use the one from the project
            if strategy_id is None and project.allocation_strategy_id is not None:
                strategy_id = project.allocation_strategy_id
            
            # If we have a strategy_id, fetch the strategy details
            strategy = None
            if strategy_id is not None:
                from post_db.master_models.allocation_strategies import AllocationStrategies
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                if not strategy:
                    return {"success": False, "error": f"Allocation strategy with ID {strategy_id} not found"}

        # Use the database_name from the registry, or custom_db_name if provided
        database_name = custom_db_name if custom_db_name else project.database_name
        
        # Create the database using the provisioner
        provisioner = ProjectDatabaseProvisioner()
        
        # Actually create the database with dynamic schema if strategy is provided
        if strategy:
            result = await provisioner.create_project_database_with_strategy(project_code, database_name, strategy)
        else:
            # Fall back to default schema if no strategy is provided
            result = await provisioner.create_project_database(project_code, database_name)
            
        return result
        
    except SQLAlchemyError as e:
        logger.error(f"Provisioning failed: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return {"success": False, "error": str(e)}