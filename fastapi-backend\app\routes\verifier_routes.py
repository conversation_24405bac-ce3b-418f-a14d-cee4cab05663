from fastapi import APIRouter, Depends, HTTPException # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.sql import text
from typing import Dict, Any
import logging
import json
from datetime import datetime

from dependencies.auth import get_current_active_user, get_current_user, require_verifier
from core.session_manager import get_master_db_session
from post_db.master_models.users import users
from post_db.master_models.projects_registry import ProjectsRegistry
from services.verifier_batch_assignment_service import VerifierBatchAssignmentService
from services.verifier_data_service import VerifierDataService
from core.session_manager import get_project_db_session
from cache.annotator_cache import (get_cached_batch, cache_batch, get_cached_user_batch_id, 
                                   cache_user_batch, prefetch_batch_images_async,
                                   cache_file_review_data, get_cached_file_review_data, delete_user_batch_cache)

logger = logging.getLogger("verifier_routes")

router = APIRouter(
    prefix="/verifier",
    tags=["Verifier"],
    dependencies=[Depends(get_current_active_user), Depends(require_verifier)]
)

@router.get("/dashboard")
async def verifier_dashboard(
    current_user: dict = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """Return basic dashboard data for verifier"""
    username = current_user["sub"]
    try:
        # Fetch user to get active_project
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        project_code = None
        project_name = None
        if user_obj and user_obj.active_project:
            project_code = user_obj.active_project
            # Fetch project name
            proj_res = await master_db.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            proj_obj = proj_res.scalar_one_or_none()
            if proj_obj:
                project_name = proj_obj.project_name
        return {
            "username": username,
            "full_name": current_user.get("full_name", ""),
            "project_code": project_code,
            "project_name": project_name
        }
    except Exception as e:
        logger.error(f"Error in verifier dashboard: {e}")
        return {
            "username": username,
            "full_name": current_user.get("full_name", ""),
            "project_code": None,
            "project_name": None
        }


@router.post("/start-verification", response_model=Dict[str, Any])
async def start_verification(
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Assign the current verifier to the next available completed batch.
    
    This endpoint is called when a verifier clicks "Start Verifying" button.
    It will:
    1. Check if verifier already has an active batch assigned
    2. If not, find the next available completed batch (completion_count == annotation_count, verifier IS NULL)
    3. Assign the batch to the verifier by updating both allocation_batches and file_allocations tables
    4. Return batch details and appropriate messages
    
    Returns:
        Dict: Assignment result with batch details and status messages
    """
    try:
        logger.info(f"START VERIFICATION endpoint called for user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        logger.info(f"Processing start verification for user_id: {user_id}")
        
        # Initialize verifier service
        verifier_service = VerifierBatchAssignmentService()
        
        # Call the main assignment logic
        result = await verifier_service.assign_verifier_to_next_batch(user_id)
        
        logger.info(f"Verifier batch assignment result: {result}")
        
        # Handle different response scenarios
        if result["success"]:
            if result.get("already_assigned"):
                return {
                    "success": True,
                    "message": result["message"],
                    "batch": result["batch"],
                    "status": "already_assigned"
                }
            elif result.get("newly_assigned"):
                return {
                    "success": True,
                    "message": result["message"],
                    "batch": result["batch"],
                    "status": "newly_assigned"
                }
            else:
                return {
                    "success": True,
                    "message": result["message"]
                }
        else:
            # Handle different error scenarios with appropriate HTTP status codes
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            
            if error_code == "NO_ACTIVE_PROJECT":
                raise HTTPException(
                    status_code=400,
                    detail=result["error"]
                )
            elif error_code == "NO_COMPLETED_BATCHES":
                raise HTTPException(
                    status_code=404,
                    detail=result["error"]
                )
            elif error_code == "ALL_BATCHES_ASSIGNED":
                raise HTTPException(
                    status_code=409,
                    detail=result["error"]
                )
            elif error_code == "NO_AVAILABLE_BATCHES":
                raise HTTPException(
                    status_code=404,
                    detail=result["error"]
                )
            elif error_code == "NO_BATCHES_FOUND":
                raise HTTPException(
                    status_code=404,
                    detail=result["error"]
                )
            elif error_code == "ASSIGNMENT_FAILED":
                raise HTTPException(
                    status_code=500,
                    detail=result["error"]
                )
            elif error_code == "INTERNAL_ERROR":
                raise HTTPException(
                    status_code=500,
                    detail=result["error"]
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=result["error"]
                )
                
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected error in start_verification: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/verify")
async def verify_route(
    current_user: dict = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """Get verification data for the current verifier with caching support"""
    username = current_user["sub"]
    
    # Try to get cached batch first
    cache_mode = "verification"
    cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
    if cached_batch_id:
        cached_batch = await get_cached_batch(cached_batch_id)
        if cached_batch:
            # Get strategy type for cached data
            cached_project_code = cached_batch.get('project_code')
            cached_strategy_type = "sequential"
            cached_form_config = []
            
            if cached_project_code:
                try:
                    verifier_data_service = VerifierDataService()
                    strategy = await verifier_data_service.get_project_allocation_strategy(cached_project_code)
                    if strategy and strategy.strategy_type:
                        cached_strategy_type = strategy.strategy_type.value
                    
                    # Get form config for cached data
                    project_result = await master_db.execute(
                        select(ProjectsRegistry).where(ProjectsRegistry.project_code == cached_project_code)
                    )
                    project = project_result.scalar_one_or_none()
                    if project and project.annotation_requirements:
                        try:
                            requirements = json.loads(project.annotation_requirements) if isinstance(project.annotation_requirements, str) else project.annotation_requirements
                            cached_form_config = requirements.get("form_config", [])
                        except:
                            cached_form_config = []
                except:
                    cached_strategy_type = "sequential"
            
            return {
                "files": cached_batch.get('files', []),
                "user": current_user,
                "mode": "verification",
                "batch_name": cached_batch['batch_name'],
                "batch_info": cached_batch.get('batch_info', {}),
                "total_files": cached_batch.get('total_files', 0),
                "strategy_type": cached_strategy_type,
                "form_config": cached_form_config,
                "project_code": cached_project_code
            }
    
    # If no cached batch, try to load and cache verification data automatically
    logger.info(f"No cached verification data for {username}, attempting to load and cache...")
    
    try:
        # Get user ID from master database
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(status_code=404, detail="User not found in database")
        
        # Initialize verifier data service
        verifier_data_service = VerifierDataService()
        
        # Get verification data
        verification_result = await verifier_data_service.get_verifier_verification_data(user_obj.id)
        
        if not verification_result["success"]:
            logger.error(f"Failed to get verification data: {verification_result.get('error')}")
            error_code = verification_result.get("error_code", "UNKNOWN_ERROR")
            
            if error_code == "NO_ASSIGNED_BATCH":
                raise HTTPException(status_code=204, detail="No batch assigned for verification. Please contact administrator.")
            elif error_code == "NO_ACTIVE_PROJECT":
                raise HTTPException(status_code=400, detail="No active project assigned. Please contact administrator.")
            else:
                raise HTTPException(status_code=500, detail=verification_result.get("error", "Failed to get verification data"))
        
        # Prepare files for caching
        files_for_cache = await verifier_data_service.prepare_verification_files_for_cache(verification_result)
        
        if not files_for_cache:
            logger.warning(f"No files found for verification batch")
            raise HTTPException(status_code=204, detail="No files found in assigned batch")
        
        # Cache the verification data
        batch_info = verification_result.get("batch_info", {})
        batch_identifier = batch_info.get("batch_identifier", "verification_batch")
        
        batch_data = {
            'id': f"{username}_verification_{batch_identifier}",
            'batch_name': batch_identifier,
            'batch_identifier': batch_identifier,
            'files': files_for_cache,
            'file_count': len(files_for_cache),
            'username': username,
            'mode': 'verification',
            'batch_info': batch_info,
            'total_files': verification_result.get("total_files", len(files_for_cache)),
            'project_code': verification_result.get("project_code")
        }
        
        # Store in cache
        await cache_batch(batch_data['id'], batch_data)
        await cache_user_batch(username, batch_data['id'], "verification")
        
        logger.info(f"Successfully auto-cached verification batch {batch_data['id']} with {len(files_for_cache)} files")
        
        # Start prefetching images in background
        prefetch_batch_images_async(batch_data)
        
        # Get project strategy information for frontend interface selection
        strategy_type = "sequential"  # default
        form_config = []
        project_code = verification_result.get("project_code")
        
        if project_code:
            try:
                # Get allocation strategy
                strategy = await verifier_data_service.get_project_allocation_strategy(project_code)
                if strategy and strategy.strategy_type:
                    strategy_type = strategy.strategy_type.value
                    logger.info(f"Project {project_code} uses {strategy_type} strategy for verification")
                
                # Get form config from project (needed for parallel verification)
                project_result = await master_db.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = project_result.scalar_one_or_none()
                if project and project.annotation_requirements:
                    try:
                        requirements = json.loads(project.annotation_requirements) if isinstance(project.annotation_requirements, str) else project.annotation_requirements
                        form_config = requirements.get("form_config", [])
                        logger.info(f"Loaded {len(form_config)} form fields for {strategy_type} verification")
                    except Exception as e:
                        logger.warning(f"Failed to parse annotation requirements: {str(e)}")
                        
            except Exception as e:
                logger.error(f"Error getting project strategy for verification: {str(e)}")
        
        # Return the verification data
        return {
            "files": files_for_cache,
            "user": current_user,
            "mode": "verification", 
            "batch_name": batch_identifier,
            "batch_info": batch_info,
            "total_files": verification_result.get("total_files", len(files_for_cache)),
            "strategy_type": strategy_type,
            "form_config": form_config,
            "project_code": project_code
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error auto-caching verification data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/next-set")
async def get_next_verification_set(
    current_user: dict = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """Get the verification data for verifier's assigned batch"""
    try:
        username = current_user["sub"]
        logger.info(f"Getting verification data for user: {username}")
        
        # Get user ID from master database
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            return {
                "files": [],
                "status": "error",
                "message": "User not found in database"
            }
        
        # Initialize verifier data service
        verifier_data_service = VerifierDataService()
        
        # Get verification data
        verification_result = await verifier_data_service.get_verifier_verification_data(user_obj.id)
        
        if not verification_result["success"]:
            logger.error(f"Failed to get verification data: {verification_result.get('error')}")
            error_code = verification_result.get("error_code", "UNKNOWN_ERROR")
            
            if error_code == "NO_ASSIGNED_BATCH":
                return {
                    "files": [],
                    "status": "no_batch",
                    "message": "No batch assigned for verification. Please contact administrator."
                }
            elif error_code == "NO_ACTIVE_PROJECT":
                return {
                    "files": [],
                    "status": "no_project",
                    "message": "No active project assigned. Please contact administrator."
                }
            else:
                return {
                    "files": [],
                    "status": "error",
                    "message": verification_result.get("error", "Failed to get verification data")
                }
        
        # Prepare files for caching
        files_for_cache = await verifier_data_service.prepare_verification_files_for_cache(verification_result)
        
        if not files_for_cache:
            logger.warning(f"No files found for verification batch")
            return {
                "files": [],
                "status": "no_files",
                "message": "No files found in assigned batch"
            }
        
        # Cache the verification data
        batch_info = verification_result.get("batch_info", {})
        batch_identifier = batch_info.get("batch_identifier", "verification_batch")
        
        batch_data = {
            'id': f"{username}_verification_{batch_identifier}",
            'batch_name': batch_identifier,
            'batch_identifier': batch_identifier,
            'files': files_for_cache,
            'file_count': len(files_for_cache),
            'username': username,
            'mode': 'verification',
            'batch_info': batch_info,
            'total_files': verification_result.get("total_files", len(files_for_cache)),
            'project_code': verification_result.get("project_code")
        }
        
        # Store in cache
        await cache_batch(batch_data['id'], batch_data)
        await cache_user_batch(username, batch_data['id'], "verification")
        
        logger.info(f"Successfully cached verification batch {batch_data['id']} with {len(files_for_cache)} files")
        
        # Start prefetching images in background
        prefetch_batch_images_async(batch_data)
        
        return {
            "status": "success",
            "message": f"Verification batch loaded: {batch_identifier}",
            "files": files_for_cache,
            "batch_info": batch_info,
            "total_files": len(files_for_cache)
        }
        
    except Exception as e:
        logger.error(f"Error getting verification data: {str(e)}")
        return {
            "files": [],
            "status": "error",
            "message": f"Internal server error: {str(e)}"
        }


@router.get("/current-batch")
async def get_current_verification_batch(
    current_user: dict = Depends(get_current_user)
):
    """Get current verification batch details for verifier"""
    try:
        username = current_user["sub"]
        user_id = current_user.get("user_id")
        
        # First, check the actual database state to see if user has a current batch
        verifier_data_service = VerifierDataService()
        project_code = await verifier_data_service.get_user_active_project(user_id)
        
        if project_code:
            # Check if user actually has a current batch in project_users table
            batch_info = await verifier_data_service.get_verifier_assigned_batch(project_code, user_id)
            
            if not batch_info:
                # No current batch in database, clear any stale cache
                logger.info(f"No current batch in database for user {username}, clearing cache")
                await delete_user_batch_cache(username, "verification")
                return {
                    "success": False,
                    "error": "No current verification batch found",
                    "error_code": "NO_CURRENT_BATCH"
                }
        
        # Try to get cached batch only if database confirms there's an active batch
        cache_mode = "verification"
        cached_batch_id = await get_cached_user_batch_id(username, cache_mode)
        
        if cached_batch_id:
            cached_batch = await get_cached_batch(cached_batch_id)
            if cached_batch:
                return {
                    "success": True,
                    "batch": {
                        "batch_identifier": cached_batch.get("batch_identifier"),
                        "files": cached_batch.get("files", []),
                        "total_files": cached_batch.get("total_files", 0),
                        "batch_info": cached_batch.get("batch_info", {}),
                        "project_code": cached_batch.get("project_code")
                    }
                }
        
        # No cached batch found or cache doesn't match database state
        return {
            "success": False,
            "error": "No current verification batch found",
            "error_code": "NO_CURRENT_BATCH"
        }
        
    except Exception as e:
        logger.error(f"Error getting current verification batch: {str(e)}")
        return {
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "error_code": "INTERNAL_ERROR"
        }


@router.get("/file-reviews/{file_id}")
async def get_file_reviews(
    file_id: int,
    batch_id: int,
    current_user: dict = Depends(get_current_user)
):
    """
    Get review data for a specific file from cache.
    This endpoint allows fetching annotator review data file by file.
    
    Args:
        file_id: ID of the file
        batch_id: ID of the batch
        
    Returns:
        Dict containing review data for the file
    """
    try:
        logger.info(f"Fetching review data for file {file_id} in batch {batch_id}")
        
        # Get cached review data for this file
        review_data = await get_cached_file_review_data(file_id, batch_id)
        
        if not review_data:
            logger.warning(f"No review data found for file {file_id} in batch {batch_id}")
            raise HTTPException(
                status_code=404, 
                detail=f"No review data found for file {file_id} in batch {batch_id}"
            )
        
        return {
            "success": True,
            "file_id": file_id,
            "batch_id": batch_id,
            "reviews": review_data.get("reviews", []),
            "cached_at": review_data.get("cached_at")
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting file reviews: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/complete-verification", response_model=Dict[str, Any])
async def complete_verification(
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Complete the current verification batch for the verifier.
    
    This endpoint is called when a verifier finishes verifying a batch.
    It will:
    1. Mark the user_allocations record as completed
    2. Update project_users to clear current_batch and add to completed_batches
    3. Clear verifier assignment from allocation_batches and file_allocations
    
    Returns:
        Dict: Completion result with status messages
    """
    try:
        logger.info(f"COMPLETE VERIFICATION endpoint called for user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        logger.info(f"Processing complete verification for user_id: {user_id}")
        
        # Initialize verifier service
        verifier_service = VerifierBatchAssignmentService()
        
        # Get user's active project
        project_code = await verifier_service.get_user_active_project(user_id)
        if not project_code:
            logger.error(f"No active project for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="Verifier has no active project assigned"
            )
        
        logger.info(f"Verifier {user_id} active project: {project_code}")
        
        # Check if verifier has an active batch
        current_batch = await verifier_service.check_verifier_has_active_batch(project_code, user_id)
        logger.info(f"Current batch check result for verifier {user_id}: {current_batch}")
        
        if not current_batch:
            logger.warning(f"No active batch found for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="No active batch found to complete"
            )
        
        # Check if batch is already completed
        if current_batch.get("verification_completed", False):
            logger.info(f"Batch {current_batch['batch_id']} is already completed for verifier {user_id}, clearing assignment")
            # Clear the assignment since it's already completed
            success = await verifier_service.complete_verifier_batch(
                project_code, 
                current_batch["batch_id"], 
                user_id
            )
            if success:
                # Clear verifier cache after clearing completed assignment
                try:
                    await delete_user_batch_cache(username, "verification")
                    logger.info(f"Cleared verification cache for user {username} after clearing completed assignment")
                except Exception as cache_error:
                    logger.warning(f"Failed to clear verification cache for user {username}: {cache_error}")
                
                return {
                    "success": True,
                    "message": f"Cleared completed verification assignment for batch {current_batch['batch_identifier']}",
                    "batch": current_batch
                }
            else:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to clear completed verification assignment"
                )
        
        # Complete the batch
        success = await verifier_service.complete_verifier_batch(
            project_code, 
            current_batch["batch_id"], 
            user_id
        )
        
        if success:
            logger.info(f"Successfully completed batch {current_batch['batch_id']} for verifier {user_id}")
            
            # Clear verifier cache after successful completion to reset state
            try:
                await delete_user_batch_cache(username, "verification")
                logger.info(f"Cleared verification cache for user {username} after batch completion")
            except Exception as cache_error:
                logger.warning(f"Failed to clear verification cache for user {username}: {cache_error}")
            
            return {
                "success": True,
                "message": f"Successfully completed verification of batch {current_batch['batch_identifier']}",
                "batch": current_batch
            }
        else:
            logger.error(f"Failed to complete batch for verifier {user_id}")
            raise HTTPException(
                status_code=500,
                detail="Failed to complete verification batch"
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error completing verification: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/remove-assignment", response_model=Dict[str, Any])
async def remove_verifier_assignment(
    batch_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Remove a verifier's assignment from a specific batch.
    
    This endpoint is called by administrators to remove a verifier's assignment.
    It will:
    1. Mark the user_allocations record as inactive
    2. Update project_users to clear current_batch
    3. Clear verifier assignment from allocation_batches and file_allocations
    
    Args:
        batch_id: ID of the batch to remove assignment from
        
    Returns:
        Dict: Removal result with status messages
    """
    try:
        logger.info(f"REMOVE ASSIGNMENT endpoint called for batch {batch_id} by user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        logger.info(f"Processing remove assignment for user_id: {user_id}, batch_id: {batch_id}")
        
        # Initialize verifier service
        verifier_service = VerifierBatchAssignmentService()
        
        # Get user's active project
        project_code = await verifier_service.get_user_active_project(user_id)
        if not project_code:
            logger.error(f"No active project for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="Verifier has no active project assigned"
            )
        
        # Remove the assignment
        success = await verifier_service.remove_verifier_assignment(
            project_code, 
            batch_id, 
            user_id
        )
        
        if success:
            logger.info(f"Successfully removed assignment for batch {batch_id} from verifier {user_id}")
            return {
                "success": True,
                "message": f"Successfully removed assignment from batch {batch_id}",
                "batch_id": batch_id
            }
        else:
            logger.error(f"Failed to remove assignment for batch {batch_id} from verifier {user_id}")
            raise HTTPException(
                status_code=500,
                detail="Failed to remove verifier assignment"
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error removing assignment: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/save-review")
async def save_verifier_review(
    data: dict,
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Save a verifier's review for a specific file.
    
    Expected data format:
    {
        "file_id": int,
        "decision": "approved" | "rejected",
        "review_data": {
            "label": "completed",
            "form_data": {
                "question": "answer",
                ...
            }
        }
    }
    
    Stored format:
    {
        "decision": "approved",
        "reviewed_at": "NOW()",
        "verifier_id": int,
        "label": "completed", 
        "form_data": {...}
    }
    
    Returns:
        Dict: Save result with status
    """
    try:
        logger.info(f"SAVE REVIEW endpoint called by user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        
        # Validate required fields
        file_id = data.get("file_id")
        decision = data.get("decision")
        
        if not file_id or not decision:
            raise HTTPException(
                status_code=400,
                detail="file_id and decision are required fields"
            )
        
        if decision not in ["approved", "rejected"]:
            raise HTTPException(
                status_code=400,
                detail="decision must be either 'approved' or 'rejected'"
            )
        
        # Initialize verifier data service
        verifier_service = VerifierDataService()
        
        # Get user's active project
        project_code = await verifier_service.get_user_active_project(user_id)
        if not project_code:
            logger.error(f"No active project for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="Verifier has no active project assigned"
            )
        
        # Prepare review data with flat structure
        review_data_from_request = data.get("review_data", {})
        
        review_data = {
            "decision": decision,
            "reviewed_at": "NOW()",
            "verifier_id": user_id,
            "label": review_data_from_request.get("label", "completed"),
            "form_data": review_data_from_request.get("form_data", {})
        }
        
        # Save the review
        success = await verifier_service.save_verifier_review(
            project_code, 
            file_id, 
            user_id, 
            review_data
        )
        
        if success:
            logger.info(f"Successfully saved review for file {file_id} by verifier {user_id}")
            return {
                "success": True,
                "message": f"Review saved successfully for file {file_id}",
                "file_id": file_id,
                "decision": decision
            }
        else:
            logger.error(f"Failed to save review for file {file_id}")
            raise HTTPException(
                status_code=500,
                detail="Failed to save review"
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error saving review: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/save-parallel-review")
async def save_parallel_verification_review(
    data: dict,
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Save parallel verification review decisions for multiple files.
    
    Expected data format:
    {
        "batch_name": str,
        "decisions": [
            {
                "file_id": int,
                "verification_type": "parallel",
                "field_decisions": {
                    "field_name": {
                        "chosen_annotator_id": int,
                        "custom_response": any,
                        "decision_type": "annotator_choice" | "custom_input",
                        "final_value": any
                    }
                },
                "completed": bool
            }
        ]
    }
    
    Returns:
        Dict: Save result with status
    """
    try:
        logger.info(f"SAVE PARALLEL REVIEW endpoint called by user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        
        # Validate required fields
        batch_name = data.get("batch_name")
        decisions = data.get("decisions", [])
        
        if not batch_name:
            raise HTTPException(
                status_code=400,
                detail="batch_name is required"
            )
        
        if not decisions:
            raise HTTPException(
                status_code=400,
                detail="decisions array is required"
            )
        
        logger.info(f"Processing {len(decisions)} parallel verification decisions for batch: {batch_name}")
        
        # Initialize verifier services
        verifier_data_service = VerifierDataService()
        verifier_batch_service = VerifierBatchAssignmentService()
        
        # Get user's active project
        project_code = await verifier_data_service.get_user_active_project(user_id)
        if not project_code:
            logger.error(f"No active project for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="Verifier has no active project assigned"
            )
        
        # Get field mapping once for all decisions to avoid duplicate calls
        from services.project_batch_service import ProjectBatchService
        batch_service = ProjectBatchService()
        field_mapping = await batch_service.get_field_name_to_label_mapping(project_code)
        
        # Save each decision
        saved_count = 0
        for decision in decisions:
            try:
                file_id = decision.get("file_id")
                field_decisions = decision.get("field_decisions", {})
                completed = decision.get("completed", False)
                
                if not file_id:
                    logger.warning(f"Skipping decision without file_id: {decision}")
                    continue
                
                # Transform field_decisions keys from field_name to label
                transformed_field_decisions = {}
                for field_name, field_data in field_decisions.items():
                    label = field_mapping.get(field_name, field_name)  # fallback to field_name if mapping not found
                    transformed_field_decisions[label] = field_data
                    logger.debug(f"Transformed field '{field_name}' -> '{label}'")
                
                # Prepare the review data in JSON format
                review_data = {
                    "verification_type": "parallel",
                    "field_decisions": transformed_field_decisions,
                    "completed": completed,
                    "decision": "approved" if completed else "in_progress",
                    "comments": f"Parallel verification with {len(field_decisions)} field decisions",
                    "reviewed_at": datetime.now().isoformat()  # Current timestamp
                }
                
                # Save the review using the verifier data service
                await verifier_data_service.save_verifier_review(
                    project_code=project_code,
                    file_id=file_id,
                    verifier_id=user_id,
                    review_data=review_data
                )
                
                saved_count += 1
                logger.info(f"Saved parallel review for file {file_id}")
                
            except Exception as e:
                logger.error(f"Error saving decision for file {decision.get('file_id', 'unknown')}: {str(e)}")
                # Continue processing other decisions even if one fails
                continue
        
        logger.info(f"Successfully saved {saved_count}/{len(decisions)} parallel verification decisions")
        
        # Handle batch completion - clear current_batch and add to completed_batches
        if saved_count > 0:
            try:
                logger.info(f"Starting batch completion logic for verifier {user_id}, project {project_code}, batch {batch_name}")
                
                # Get the batch_id from the project database using batch_name
                async with get_project_db_session(project_code) as session:
                    batch_query = text("""
                        SELECT id FROM allocation_batches 
                        WHERE batch_identifier = :batch_name
                        LIMIT 1
                    """)
                    batch_result = await session.execute(batch_query, {"batch_name": batch_name})
                    batch_row = batch_result.fetchone()
                    
                    logger.info(f"Batch lookup result for '{batch_name}': {batch_row}")
                    
                    if batch_row:
                        batch_id = batch_row[0]
                        logger.info(f"Found batch_id: {batch_id} for batch_name: {batch_name}")
                        
                        # Use the existing complete_verifier_batch method instead of manual logic
                        completion_success = await verifier_batch_service.complete_verifier_batch(
                            project_code=project_code,
                            batch_id=batch_id,
                            verifier_id=user_id
                        )
                        
                        if completion_success:
                            logger.info(f"Successfully completed batch {batch_id} for verifier {user_id}")
                            
                            # Clear verifier cache after successful completion to reset state
                            try:
                                await delete_user_batch_cache(username, "verification")
                                logger.info(f"Cleared verification cache for user {username} after batch completion")
                            except Exception as cache_error:
                                logger.warning(f"Failed to clear verification cache for user {username}: {cache_error}")
                        else:
                            logger.error(f"Failed to complete batch {batch_id} for verifier {user_id}")
                    else:
                        logger.warning(f"Batch not found for batch_name: {batch_name}")
                    
            except Exception as e:
                logger.error(f"Error handling batch completion for verifier: {str(e)}")
                import traceback
                logger.error(f"Full traceback: {traceback.format_exc()}")
                # Don't fail the entire request if batch completion fails
        
        return {
            "success": True,
            "message": f"Successfully saved {saved_count} parallel verification decisions",
            "saved_count": saved_count,
            "total_count": len(decisions),
            "batch_name": batch_name
        }
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error saving parallel review: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/get-review/{file_id}")
async def get_verifier_review(
    file_id: int,
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get a verifier's review for a specific file.
    
    Args:
        file_id: ID of the file
        
    Returns:
        Dict: Review data if found
    """
    try:
        logger.info(f"GET REVIEW endpoint called for file {file_id} by user: {current_user.get('sub')}")
        
        # Get user ID from master database
        username = current_user["sub"]
        result = await master_db.execute(select(users).where(users.username == username))
        user_obj = result.scalar_one_or_none()
        
        if not user_obj:
            logger.error(f"User not found in master database: {username}")
            raise HTTPException(
                status_code=404,
                detail="User not found in database"
            )
        
        user_id = user_obj.id
        
        # Initialize verifier data service
        verifier_service = VerifierDataService()
        
        # Get user's active project
        project_code = await verifier_service.get_user_active_project(user_id)
        if not project_code:
            logger.error(f"No active project for verifier {user_id}")
            raise HTTPException(
                status_code=400,
                detail="Verifier has no active project assigned"
            )
        
        # Get the review
        review_data = await verifier_service.get_verifier_review(
            project_code, 
            file_id, 
            user_id
        )
        
        if review_data:
            logger.info(f"Found review for file {file_id} by verifier {user_id}")
            return {
                "success": True,
                "file_id": file_id,
                "review": review_data
            }
        else:
            return {
                "success": False,
                "message": f"No review found for file {file_id}",
                "file_id": file_id
            }
            
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting review: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )
