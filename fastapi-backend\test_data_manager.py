#!/usr/bin/env python3
"""
🗃️ Database Test Data Manager
Comprehensive test data creation, management, and cleanup utilities.

This module provides utilities for:
- Creating consistent test data across different test suites
- Managing test database state and cleanup
- Generating realistic test scenarios
- Performance testing data sets
"""

import asyncio
import sys
import os
import json
import random
import string
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path

# Add app path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "app")))

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select, delete, text
import sqlalchemy as sa

from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.user_project_access import UserProjectAccess
from app.schemas.UserSchemas import UserRegisterRequest

@dataclass
class TestDataConfig:
    """Configuration for test data generation."""
    num_users: int = 10
    num_projects: int = 3
    num_clients: int = 2
    num_batches_per_project: int = 5
    num_files_per_batch: int = 20
    include_performance_data: bool = False
    performance_scale_factor: int = 1  # Multiplier for performance testing

@dataclass
class TestUser:
    """Test user data structure."""
    username: str
    email: str
    password: str
    role: UserRole
    is_active: bool = True

@dataclass
class TestProject:
    """Test project data structure."""
    project_code: str
    project_name: str
    project_type: str
    database_name: str
    client_id: int
    allocation_strategy_id: int
    is_active: bool = True

@dataclass
class TestBatch:
    """Test batch data structure."""
    batch_identifier: str
    batch_status: str
    total_files: int
    is_priority: bool
    allocation_criteria: Dict[str, Any]
    skill_requirements: Dict[str, Any]

class DatabaseTestDataManager:
    """Manages test data creation, cleanup, and state management."""
    
    def __init__(self):
        """Initialize data manager with database connections."""
        self.project_engine = None
        self.master_engine = None
        self.project_session = None
        self.master_session = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
        
    async def connect(self):
        """Establish database connections."""
        project_db_url = os.getenv("DATABASE_URL", "postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/test_project_db")
        master_db_url = os.getenv("MASTER_DATABASE_URL", "postgresql+asyncpg://kanwar_raj:dadpdev123@10.10.10.30:5432/test_master_db")
        
        self.project_engine = create_async_engine(project_db_url, echo=False, pool_pre_ping=True)
        self.master_engine = create_async_engine(master_db_url, echo=False, pool_pre_ping=True)
        
        ProjectSession = async_sessionmaker(bind=self.project_engine, class_=AsyncSession)
        MasterSession = async_sessionmaker(bind=self.master_engine, class_=AsyncSession)
        
        self.project_session = ProjectSession()
        self.master_session = MasterSession()
        
    async def disconnect(self):
        """Close database connections."""
        if self.project_session:
            await self.project_session.close()
        if self.master_session:
            await self.master_session.close()
        if self.project_engine:
            await self.project_engine.dispose()
        if self.master_engine:
            await self.master_engine.dispose()
            
    def generate_test_users(self, config: TestDataConfig) -> List[TestUser]:
        """Generate test user data."""
        users = []
        roles = [UserRole.ADMIN, UserRole.ANNOTATOR, UserRole.VERIFIER, UserRole.AUDITOR]
        
        # Always create at least one admin
        users.append(TestUser(
            username="test_admin",
            email="<EMAIL>",
            password="TestPass123!",
            role=UserRole.ADMIN
        ))
        
        # Generate additional users
        for i in range(config.num_users - 1):
            username = f"test_user_{i+1}"
            email = f"user{i+1}@test.com"
            role = random.choice(roles)
            
            users.append(TestUser(
                username=username,
                email=email,
                password=f"TestPass{i+1}!",
                role=role
            ))
            
        return users
        
    def generate_test_clients(self, config: TestDataConfig) -> List[Dict[str, Any]]:
        """Generate test client data."""
        clients = []
        
        for i in range(config.num_clients):
            clients.append({
                "client_name": f"Test Client {i+1}",
                "client_code": f"TC{i+1:03d}",
                "contact_email": f"contact{i+1}@testclient.com",
                "is_active": True,
                "created_at": datetime.utcnow() - timedelta(days=random.randint(1, 365))
            })
            
        return clients
        
    def generate_allocation_strategies(self, config: TestDataConfig) -> List[Dict[str, Any]]:
        """Generate test allocation strategies."""
        strategies = []
        strategy_types = [StrategyType.SEQUENTIAL, StrategyType.PARALLEL, StrategyType.ROUND_ROBIN]
        
        for i, strategy_type in enumerate(strategy_types):
            strategies.append({
                "strategy_name": f"test_strategy_{strategy_type.value}_{i+1}",
                "strategy_type": strategy_type,
                "description": f"Test {strategy_type.value} allocation strategy",
                "num_annotators": random.randint(2, 4),
                "requires_verification": random.choice([True, False]),
                "requires_ai_preprocessing": random.choice([True, False]),
                "requires_audit": random.choice([True, False])
            })
            
        return strategies
        
    def generate_test_projects(self, config: TestDataConfig, client_ids: List[int], strategy_ids: List[int]) -> List[TestProject]:
        """Generate test project data."""
        projects = []
        project_types = ["image", "document", "audio", "video"]
        
        for i in range(config.num_projects):
            project_code = f"PROJ_{i+1:03d}"
            projects.append(TestProject(
                project_code=project_code,
                project_name=f"Test Project {i+1}",
                project_type=random.choice(project_types),
                database_name=f"test_project_db_{i+1}",
                client_id=random.choice(client_ids),
                allocation_strategy_id=random.choice(strategy_ids)
            ))
            
        return projects
        
    def generate_test_batches(self, config: TestDataConfig, project_ids: List[int]) -> List[TestBatch]:
        """Generate test batch data."""
        batches = []
        batch_statuses = ["created", "assigned", "in_progress", "completed", "verified"]
        
        for project_id in project_ids:
            for i in range(config.num_batches_per_project):
                batch_identifier = f"BATCH_{project_id}_{i+1:03d}"
                
                batches.append(TestBatch(
                    batch_identifier=batch_identifier,
                    batch_status=random.choice(batch_statuses),
                    total_files=config.num_files_per_batch,
                    is_priority=random.choice([True, False]),
                    allocation_criteria={
                        "min_experience": random.randint(1, 12),
                        "skill_level": random.choice(["beginner", "intermediate", "advanced"]),
                        "availability_hours": random.randint(4, 8)
                    },
                    skill_requirements={
                        "image_annotation": random.choice([True, False]),
                        "text_classification": random.choice([True, False]),
                        "quality_control": random.choice([True, False])
                    }
                ))
                
        return batches
        
    def generate_performance_data(self, config: TestDataConfig) -> Dict[str, List[Dict[str, Any]]]:
        """Generate large datasets for performance testing."""
        if not config.include_performance_data:
            return {}
            
        scale = config.performance_scale_factor
        
        performance_data = {
            "large_batches": [],
            "bulk_files": [],
            "mass_allocations": []
        }
        
        # Generate large batch scenarios
        for i in range(10 * scale):
            performance_data["large_batches"].append({
                "batch_identifier": f"PERF_BATCH_{i+1:06d}",
                "batch_status": "created",
                "total_files": random.randint(1000, 10000),
                "is_priority": False,
                "allocation_criteria": {"performance_test": True}
            })
            
        # Generate bulk file data
        for batch_idx in range(5 * scale):
            files_in_batch = random.randint(5000, 20000)
            for file_idx in range(files_in_batch):
                performance_data["bulk_files"].append({
                    "batch_id": batch_idx + 1,
                    "file_identifier": f"PERF_FILE_{batch_idx+1:03d}_{file_idx+1:06d}",
                    "original_filename": f"test_file_{file_idx+1:06d}.jpg",
                    "file_type": "image",
                    "file_extension": ".jpg",
                    "file_size_bytes": random.randint(100000, 5000000),
                    "sequence_order": file_idx + 1
                })
                
        return performance_data
        
    async def create_master_db_test_data(self, config: TestDataConfig) -> Dict[str, List[int]]:
        """Create test data in master database."""
        print("🔧 Creating master database test data...")
        
        # Generate test data
        test_users = self.generate_test_users(config)
        test_clients = self.generate_test_clients(config)
        test_strategies = self.generate_allocation_strategies(config)
        
        created_ids = {
            "user_ids": [],
            "client_ids": [],
            "strategy_ids": [],
            "project_ids": []
        }
        
        try:
            # Create users
            from app.services.auth_service import AuthService
            for user in test_users:
                user_request = UserRegisterRequest(
                    username=user.username,
                    email=user.email,
                    password=user.password,
                    role=user.role.value
                )
                success, result = await AuthService.register_user(self.master_session, user_request)
                if success and hasattr(result, 'id'):
                    created_ids["user_ids"].append(result.id)
                    
            # Create clients
            for client_data in test_clients:
                client = Clients(**client_data)
                self.master_session.add(client)
                await self.master_session.flush()
                created_ids["client_ids"].append(client.id)
                
            # Create allocation strategies
            for strategy_data in test_strategies:
                strategy = AllocationStrategies(**strategy_data)
                self.master_session.add(strategy)
                await self.master_session.flush()
                created_ids["strategy_ids"].append(strategy.id)
                
            # Create projects
            test_projects = self.generate_test_projects(
                config, created_ids["client_ids"], created_ids["strategy_ids"]
            )
            
            for project in test_projects:
                project_registry = ProjectsRegistry(
                    project_code=project.project_code,
                    project_name=project.project_name,
                    project_type=project.project_type,
                    database_name=project.database_name,
                    client_id=project.client_id,
                    allocation_strategy_id=project.allocation_strategy_id,
                    is_active=project.is_active
                )
                self.master_session.add(project_registry)
                await self.master_session.flush()
                created_ids["project_ids"].append(project_registry.id)
                
            # Create user-project access relationships
            for user_id in created_ids["user_ids"]:
                for project_id in random.sample(created_ids["project_ids"], 
                                               random.randint(1, len(created_ids["project_ids"]))):
                    access = UserProjectAccess(
                        user_id=user_id,
                        project_id=project_id,
                        access_level="read_write",
                        granted_at=datetime.utcnow()
                    )
                    self.master_session.add(access)
                    
            await self.master_session.commit()
            print(f"✅ Created {len(created_ids['user_ids'])} users, {len(created_ids['client_ids'])} clients, "
                  f"{len(created_ids['strategy_ids'])} strategies, {len(created_ids['project_ids'])} projects")
                  
        except Exception as e:
            await self.master_session.rollback()
            print(f"❌ Error creating master DB test data: {e}")
            raise
            
        return created_ids
        
    async def create_project_db_test_data(self, config: TestDataConfig, project_ids: List[int]) -> Dict[str, int]:
        """Create test data in project database."""
        print("🔧 Creating project database test data...")
        
        created_counts = {
            "batches": 0,
            "files": 0,
            "allocations": 0
        }
        
        try:
            # Generate and create batches
            test_batches = self.generate_test_batches(config, project_ids)
            batch_ids = []
            
            for batch in test_batches:
                # Create batch record
                insert_batch = sa.text("""
                    INSERT INTO allocation_batches 
                    (batch_identifier, batch_status, total_files, is_priority, 
                     allocation_criteria, skill_requirements, created_at)
                    VALUES (:batch_identifier, :batch_status, :total_files, :is_priority,
                            :allocation_criteria, :skill_requirements, :created_at)
                    RETURNING id
                """)
                
                result = await self.project_session.execute(insert_batch, {
                    "batch_identifier": batch.batch_identifier,
                    "batch_status": batch.batch_status, 
                    "total_files": batch.total_files,
                    "is_priority": batch.is_priority,
                    "allocation_criteria": json.dumps(batch.allocation_criteria),
                    "skill_requirements": json.dumps(batch.skill_requirements),
                    "created_at": datetime.utcnow()
                })
                
                batch_id = result.scalar()
                batch_ids.append(batch_id)
                created_counts["batches"] += 1
                
                # Create files for batch
                for file_idx in range(batch.total_files):
                    insert_file = sa.text("""
                        INSERT INTO files_registry
                        (batch_id, file_identifier, original_filename, file_type, 
                         file_extension, file_size_bytes, sequence_order, uploaded_at)
                        VALUES (:batch_id, :file_identifier, :original_filename, :file_type,
                                :file_extension, :file_size_bytes, :sequence_order, :uploaded_at)
                        RETURNING id
                    """)
                    
                    file_result = await self.project_session.execute(insert_file, {
                        "batch_id": batch_id,
                        "file_identifier": f"{batch.batch_identifier}_FILE_{file_idx+1:04d}",
                        "original_filename": f"test_file_{file_idx+1:04d}.jpg",
                        "file_type": "image",
                        "file_extension": ".jpg", 
                        "file_size_bytes": random.randint(100000, 2000000),
                        "sequence_order": file_idx + 1,
                        "uploaded_at": datetime.utcnow()
                    })
                    
                    file_id = file_result.scalar()
                    created_counts["files"] += 1
                    
                    # Create file allocation
                    insert_allocation = sa.text("""
                        INSERT INTO file_allocations
                        (file_id, batch_id, allocation_sequence, workflow_phase, 
                         processing_status, allocated_at)
                        VALUES (:file_id, :batch_id, :allocation_sequence, :workflow_phase,
                                :processing_status, :allocated_at)
                    """)
                    
                    await self.project_session.execute(insert_allocation, {
                        "file_id": file_id,
                        "batch_id": batch_id,
                        "allocation_sequence": 1,
                        "workflow_phase": "annotation", 
                        "processing_status": "pending",
                        "allocated_at": datetime.utcnow()
                    })
                    
                    created_counts["allocations"] += 1
                    
            # Create performance data if requested
            if config.include_performance_data:
                performance_data = self.generate_performance_data(config)
                for batch_data in performance_data.get("large_batches", []):
                    insert_batch = sa.text("""
                        INSERT INTO allocation_batches 
                        (batch_identifier, batch_status, total_files, is_priority, 
                         allocation_criteria, created_at)
                        VALUES (:batch_identifier, :batch_status, :total_files, :is_priority,
                                :allocation_criteria, :created_at)
                    """)
                    
                    await self.project_session.execute(insert_batch, {
                        **batch_data,
                        "allocation_criteria": json.dumps(batch_data["allocation_criteria"]),
                        "created_at": datetime.utcnow()
                    })
                    
                    created_counts["batches"] += 1
                    
            await self.project_session.commit()
            print(f"✅ Created {created_counts['batches']} batches, "
                  f"{created_counts['files']} files, {created_counts['allocations']} allocations")
                  
        except Exception as e:
            await self.project_session.rollback()
            print(f"❌ Error creating project DB test data: {e}")
            raise
            
        return created_counts
        
    async def cleanup_all_test_data(self):
        """Clean up all test data from both databases."""
        print("🧹 Cleaning up all test data...")
        
        try:
            # Clean project database
            await self.project_session.execute(sa.text("TRUNCATE TABLE file_allocations CASCADE"))
            await self.project_session.execute(sa.text("TRUNCATE TABLE user_allocations CASCADE"))
            await self.project_session.execute(sa.text("TRUNCATE TABLE files_registry CASCADE"))
            await self.project_session.execute(sa.text("TRUNCATE TABLE allocation_batches CASCADE"))
            await self.project_session.execute(sa.text("TRUNCATE TABLE project_users CASCADE"))
            await self.project_session.execute(sa.text("TRUNCATE TABLE project_metadata CASCADE"))
            await self.project_session.commit()
            
            # Clean master database
            await self.master_session.execute(delete(UserProjectAccess))
            await self.master_session.execute(delete(ProjectsRegistry))
            await self.master_session.execute(delete(AllocationStrategies))
            await self.master_session.execute(delete(Clients))
            await self.master_session.execute(delete(users).where(users.username.like("test_%")))
            await self.master_session.commit()
            
            print("✅ All test data cleaned up successfully")
            
        except Exception as e:
            await self.project_session.rollback()
            await self.master_session.rollback()
            print(f"❌ Error cleaning up test data: {e}")
            raise
            
    async def create_specific_test_scenario(self, scenario_name: str) -> Dict[str, Any]:
        """Create specific test scenarios for different test suites."""
        scenarios = {
            "smoke_test": TestDataConfig(
                num_users=3,
                num_projects=1,
                num_clients=1,
                num_batches_per_project=2,
                num_files_per_batch=5
            ),
            "core_test": TestDataConfig(
                num_users=10,
                num_projects=3,
                num_clients=2,
                num_batches_per_project=5,
                num_files_per_batch=20
            ),
            "performance_test": TestDataConfig(
                num_users=20,
                num_projects=5,
                num_clients=3,
                num_batches_per_project=10,
                num_files_per_batch=100,
                include_performance_data=True,
                performance_scale_factor=2
            )
        }
        
        config = scenarios.get(scenario_name, scenarios["core_test"])
        
        print(f"🎯 Creating '{scenario_name}' test scenario...")
        
        # Clean existing data first
        await self.cleanup_all_test_data()
        
        # Create new test data
        master_ids = await self.create_master_db_test_data(config)
        project_counts = await self.create_project_db_test_data(config, master_ids["project_ids"])
        
        return {
            "scenario": scenario_name,
            "config": asdict(config),
            "created_master_ids": master_ids,
            "created_project_counts": project_counts,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    async def export_test_data_snapshot(self, filepath: str):
        """Export current test data state to a file for reproducibility."""
        print(f"📄 Exporting test data snapshot to {filepath}...")
        
        snapshot = {
            "export_timestamp": datetime.utcnow().isoformat(),
            "master_db_data": {},
            "project_db_data": {}
        }
        
        try:
            # Export master database data
            users_result = await self.master_session.execute(select(users))
            snapshot["master_db_data"]["users"] = [
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value,
                    "is_active": user.is_active
                }
                for user in users_result.scalars().all()
            ]
            
            clients_result = await self.master_session.execute(select(Clients))
            snapshot["master_db_data"]["clients"] = [
                {
                    "id": client.id,
                    "client_name": client.client_name,
                    "client_code": client.client_code,
                    "is_active": client.is_active
                }
                for client in clients_result.scalars().all()
            ]
            
            # Export project database summary
            batch_count = await self.project_session.execute(sa.text("SELECT COUNT(*) FROM allocation_batches"))
            file_count = await self.project_session.execute(sa.text("SELECT COUNT(*) FROM files_registry"))
            allocation_count = await self.project_session.execute(sa.text("SELECT COUNT(*) FROM file_allocations"))
            
            snapshot["project_db_data"]["summary"] = {
                "total_batches": batch_count.scalar(),
                "total_files": file_count.scalar(),
                "total_allocations": allocation_count.scalar()
            }
            
            # Write to file
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            with open(filepath, 'w') as f:
                json.dump(snapshot, f, indent=2, default=str)
                
            print(f"✅ Test data snapshot exported successfully")
            return snapshot
            
        except Exception as e:
            print(f"❌ Error exporting test data snapshot: {e}")
            raise

async def main():
    """Main function for CLI usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Database Test Data Manager")
    parser.add_argument("action", choices=["create", "cleanup", "export", "scenario"], 
                       help="Action to perform")
    parser.add_argument("--scenario", choices=["smoke_test", "core_test", "performance_test"],
                       default="core_test", help="Test scenario to create")
    parser.add_argument("--export-file", default="test_data_snapshot.json",
                       help="File to export test data snapshot")
    
    args = parser.parse_args()
    
    async with DatabaseTestDataManager() as manager:
        if args.action == "create":
            config = TestDataConfig()
            master_ids = await manager.create_master_db_test_data(config)
            await manager.create_project_db_test_data(config, master_ids["project_ids"])
            
        elif args.action == "cleanup":
            await manager.cleanup_all_test_data()
            
        elif args.action == "export":
            await manager.export_test_data_snapshot(args.export_file)
            
        elif args.action == "scenario":
            await manager.create_specific_test_scenario(args.scenario)
            
    print("✅ Test data management operation completed!")

if __name__ == "__main__":
    asyncio.run(main())
