# """
# Image Annotation Service for Object Detection with YoloX & Yolo11

# This module provides async services for:
# - Object detection using YoloX and Yolo11 models
# - Bounding box extraction and parsing  
# - Database storage and retrieval of annotation results
# - Interactive annotation queries

# Example usage:
#     service = get_image_annotation_service()
#     result = await service.detect_objects(image_data, ModelType.YOLO11)
# """
# import io
# import base64
# import json
# import asyncio
# from datetime import datetime, timedelta
# from fastapi import HTTPException, status
# from typing import Dict, Any, Optional, Union, BinaryIO, List
# from pydantic import BaseModel
# from enum import Enum
# import logging
# from core.config import settings
# from core.session_manager import get_project_db_session
# from post_db.models.image_annotation import ImageAnnotation
# from .model_utils import async_exception_handler, make_api_request, prepare_files, validate_id

# # Configure logging
# logger = logging.getLogger('image_annotation_service')


# class BoundingBox(BaseModel):
#     """Model for bounding box coordinates"""
#     x_min: float
#     y_min: float
#     x_max: float
#     y_max: float
#     confidence: float
#     class_name: str
#     class_id: int


# class DetectionResponse(BaseModel):
#     """Model for object detection API responses"""
#     success: bool
#     detections: List[BoundingBox]
#     image_width: int
#     image_height: int
#     model_used: str


# class AnnotationUploadResponse(BaseModel):
#     """Model for annotation upload responses"""
#     annotation_id: str
#     filename: str
#     detection_count: int


# class ModelType(str, Enum):
#     """Enum for supported YOLO model types"""
#     YOLOX = "yolox"
#     YOLO11 = "yolo11"


# class ImageAnnotationService:
#     """
#     Class for object detection and annotation using YoloX & Yolo11 models
#     """
#     def __init__(self):
#         # Using OCR settings as base - can add annotation API specific settings here
#         self.api_url = settings.api_settings.url or settings.api_settings.ocr_url
#         self.api_key = settings.api_settings.key or settings.api_settings.ocr_key
#         self.api_headers = settings.api_settings.headers if settings.api_settings.key else settings.api_settings.ocr_headers


#     def _validate_model_version(self, model_type: ModelType, model_version: str) -> None:
#         """
#         Validate that the model version string matches the model type prefix.
#         """
#         expected_prefix = model_type.value
#         if not model_version.startswith(expected_prefix):
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail=f"Invalid model version '{model_version}' for {model_type.name}. "
#                        f"Use versions starting with '{expected_prefix}'."
#             )



#     def _parse_detection_response(self, response_text: str) -> Dict[str, Any]:
#         """
#         Parse detection response and extract bounding box information.
#         Only supports JSON format responses with 'detections' array.
#         """
#         try:
#             data = json.loads(response_text)
#             if not isinstance(data, dict) or "detections" not in data:
#                 logger.error(f"Invalid JSON structure: missing 'detections' key in response")
#                 raise HTTPException(
#                     status_code=status.HTTP_502_BAD_GATEWAY,
#                     detail="Invalid response format: missing 'detections' array"
#                 )
            
#             # Parse detections into bounding boxes
#             detections = []
#             for det in data.get("detections", []):
#                 try:
#                     bbox = BoundingBox(
#                         x_min=det["x_min"],
#                         y_min=det["y_min"],
#                         x_max=det["x_max"],
#                         y_max=det["y_max"],
#                         confidence=det["confidence"],
#                         class_name=det["class_name"],
#                         class_id=det.get("class_id", 0)
#                     )
#                     detections.append(bbox)
#                 except Exception as e:
#                     logger.warning(f"Skipping invalid detection entry: {det} due to {e}")
#                     continue

#             return {
#                 "detections": detections,
#                 "image_width": data.get("image_width", 0),
#                 "image_height": data.get("image_height", 0)
#             }
            
#         except json.JSONDecodeError as e:
#             logger.error(f"Failed to parse JSON response: {e}")
#             raise HTTPException(
#                 status_code=status.HTTP_502_BAD_GATEWAY,
#                 detail="Invalid response format: expected JSON with 'detections' array"
#             )



#     @async_exception_handler
#     async def process_image(self, image_file: BinaryIO, project_code: str) -> AnnotationUploadResponse:
#         """
#         Process and store an image file for annotation
#         """
#         original_filename = getattr(image_file, 'filename', 'image.jpg')
        
#         # Handle different types of file objects
#         if hasattr(image_file, 'read'):
#             if asyncio.iscoroutinefunction(image_file.read):
#                 image_bytes = await image_file.read()
#             else:
#                 image_bytes = image_file.read()
#         else:
#             image_bytes = image_file.file.read()

#         encoded_data = base64.b64encode(image_bytes).decode('utf-8')

#         async with get_project_db_session(project_code) as session:
#             record = ImageAnnotation(
#                 dataset_name=f"annotation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
#                 images=original_filename,
#                 label_file_path="",  # Will be set after annotation
#                 image_count=1,
#                 assigned_at=datetime.now(),
#                 annotation_status='processing'
#             )
#             session.add(record)
#             await session.commit()
#             await session.refresh(record)
#             annotation_id = str(record.id)


#         return AnnotationUploadResponse(
#             annotation_id=annotation_id,
#             filename=original_filename,
#             detection_count=0
#         )

#     @async_exception_handler
#     async def get_stored_annotation(self, annotation_id: str, project_code: str) -> Dict[str, Any]:
#         """
#         Get stored annotation data by ID
#         """
#         annotation_int = validate_id(annotation_id, "annotation ID")

#         async with get_project_db_session(project_code) as session:
#             record = await session.get(ImageAnnotation, annotation_int)
#             if not record:
#                 error_msg = f"Annotation not found for ID: {annotation_id}"
#                 logger.error(error_msg)
#                 raise HTTPException(
#                     status_code=status.HTTP_404_NOT_FOUND,
#                     detail=error_msg
#                 )

#             return {
#                 'annotation_id': str(record.id),
#                 'filename': record.images,
#                 'dataset_name': record.dataset_name,
#                 'status': record.annotation_status,
#                 'assigned_at': record.assigned_at.isoformat() if record.assigned_at else None
#             }

#     @async_exception_handler
#     async def detect_objects(
#         self,
#         image_data: Union[bytes, BinaryIO],
#         model_type: ModelType = ModelType.YOLO11,
#         confidence_threshold: float = 0.5,
#         model_version: Optional[str] = None
#     ) -> DetectionResponse:
#         """
#         Detect objects in an image using specified YOLO model
#         """
#         # Set default model versions if not provided
#         if model_version is None:
#             model_version = "yolox_s" if model_type == ModelType.YOLOX else "yolo11n"

#         self._validate_model_version(model_type, model_version)

#         model_display_name = model_type.name.capitalize()

#         detection_prompt = (
#             f"Detect all objects in this image using {model_display_name} {model_version} model. "
#             f"Return bounding box coordinates (x_min, y_min, x_max, y_max), confidence scores, "
#             f"and class labels for all detected objects with confidence > {confidence_threshold}. "
#             f"Format the response as JSON with detections array."
#         )

#         files = prepare_files(
#             image_data,
#             question=detection_prompt,
#             model=model_type.value,
#             confidence=str(confidence_threshold)
#         )

#         result = await make_api_request(
#             self.api_url, self.api_headers, "/infer/detect", f"{model_display_name} Detection",
#             files=files, timeout=30.0
#         )

#         detections_data = self._parse_detection_response(result.get("response", ""))

#         return DetectionResponse(
#             success=True,
#             detections=detections_data["detections"],
#             image_width=detections_data.get("image_width", 0),
#             image_height=detections_data.get("image_height", 0),
#             model_used=f"{model_type.value}_{model_version}"
#         )

#     @async_exception_handler
#     async def save_annotation_results(
#         self,
#         annotation_id: str,
#         detections: List[BoundingBox],
#         model_used: str,
#         project_code: str
#     ) -> None:
#         """
#         Save annotation results to database
#         """
#         annotation_int = validate_id(annotation_id, "annotation ID")

#         # Create label file content in YOLO format
#         label_content = []
#         for detection in detections:
#             label_content.append(
#                 f"{detection.class_id} {detection.x_min} {detection.y_min} "
#                 f"{detection.x_max} {detection.y_max} {detection.confidence}"
#             )
        

#         async with get_project_db_session(project_code) as session:
#             record = await session.get(ImageAnnotation, annotation_int)
#             if record:
#                 record.label_file_path = f"annotations/{annotation_id}_labels.txt"
#                 record.annotation_status = 'completed'
#                 record.processed_at = datetime.now()
#                 record.audit_comments = f"Detected {len(detections)} objects using {model_used}"
#                 await session.commit()


# /
# # Singleton instance
# _image_annotation_service = ImageAnnotationService()

# def get_image_annotation_service() -> ImageAnnotationService:
#     """Get the singleton image annotation service instance"""
#     return _image_annotation_service