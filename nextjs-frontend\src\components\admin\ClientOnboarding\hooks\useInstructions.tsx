// hooks/useInstructions.tsx - Updated to work with project_code
import { useState, useEffect } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "@/lib/api";

type Payload = {
  instructions: string;
  project_code: string;
};

export const useInstructions = () => {
  const [instructions, setInstructions] = useState("");
  const [savingInstructions, setSavingInstructions] = useState(false);
  const [selectedProjectCode, setSelectedProjectCode] = useState<string>('');
  const [projectsList, setProjectsList] = useState<any[]>([]);
  const [loadingProjects, setLoadingProjects] = useState(false);
  const [currentProjectData, setCurrentProjectData] = useState<any>(null);

  // Fetch all projects
  const fetchProjects = async (clientId?: string) => {
    setLoadingProjects(true);
    try {
      let url = `${API_BASE_URL}/admin/get-datasets`;
      if (clientId) {
        url += `?client_id=${clientId}`;
      }
      
      const res = await authFetch(url, { credentials: 'include' });
      if (!res.ok) throw new Error(`HTTP ${res.status}`);
      
      const json = await res.json();
      if (json.success && json.data?.datasets) {
        setProjectsList(json.data.datasets);
      } else {
        setProjectsList([]);
      }
    } catch (err) {
      console.error('Error fetching projects:', err);
      setProjectsList([]);
    } finally {
      setLoadingProjects(false);
    }
  };

  // Fetch specific project data when project_code changes
  useEffect(() => {
    if (!selectedProjectCode) {
      setCurrentProjectData(null);
      setInstructions('');
      return;
    }
    
    setLoadingProjects(true);
    authFetch(`${API_BASE_URL}/admin/get-datasets?project_code=${selectedProjectCode}`, { credentials: 'include' })
      .then(res => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then(json => {
        if (json.success && json.data?.datasets && json.data.datasets.length > 0) {
          const projectData = json.data.datasets[0];
          setCurrentProjectData(projectData);
          setInstructions(projectData.instructions || '');
        } else {
          setCurrentProjectData(null);
          setInstructions('');
        }
      })
      .catch(err => {
        console.error('Error fetching project details:', err);
        setCurrentProjectData(null);
        setInstructions('');
      })
      .finally(() => setLoadingProjects(false));
  }, [selectedProjectCode]);

  const handleSaveInstructions = async () => {
    if (!selectedProjectCode) {
      showToast.error("Please select a project first");
      return { success: false };
    }

    setSavingInstructions(true);
    try {
      const payload: Payload = {
        project_code: selectedProjectCode,
        instructions: instructions
      };

      const res = await authFetch(`${API_BASE_URL}/admin/edit-project-instructions`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      if (!res.ok) throw new Error("Failed to save instructions");

      showToast.success("Instructions saved successfully!");
      return { success: true };
    } catch (error: unknown) {
      if (error instanceof Error) {
        showToast.error(`Failed to save instructions: ${error.message}`);
      } else {
        showToast.error("Failed to save instructions");
      }
      return { success: false };
    } finally {
      setSavingInstructions(false);
    }
  };

  const handleProjectChange = (projectCode: string) => {
    setSelectedProjectCode(projectCode);
  };
  
  return {
    instructions,
    setInstructions,
    savingInstructions,
    selectedProjectCode,
    projectsList,
    loadingProjects,
    currentProjectData,
    fetchProjects,
    handleSaveInstructions,
    handleProjectChange,
  };

};