"""
Performance tests for CSVBatchService.
Tests throughput, memory usage, and scalability limits.
"""

import pytest
import pytest_asyncio
import time
import psutil
import os
import csv
import tempfile
from concurrent.futures import ThreadPoolExecutor
import asyncio

from app.services.csv_batch_service import CSVBatchService

class TestCSVBatchServicePerformance:
    """Performance tests for CSVBatchService."""
    
    @pytest.fixture
    def performance_csv_data(self):
        """Generate large CSV datasets for performance testing."""
        return {
            'small': self._generate_csv_data(1000),      # 1K records
            'medium': self._generate_csv_data(10000),    # 10K records  
            'large': self._generate_csv_data(100000),    # 100K records
            'xl': self._generate_csv_data(500000),       # 500K records
        }
    
    def _generate_csv_data(self, num_records):
        """Generate CSV data with specified number of records."""
        header = ['image_path', 'annotation_data', 'metadata', 'confidence']
        data = []
        for i in range(num_records):
            row = [
                f'/images/perf_test/img_{i:06d}.jpg',
                f'{{"label": "class_{i % 10}", "bbox": [{i % 100}, {i % 100}, 50, 50]}}',
                f'{{"quality": {0.7 + (i % 30) * 0.01}, "source": "perf_test"}}',
                f'{0.8 + (i % 20) * 0.01}'
            ]
            data.append(row)
        return [header] + data
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_csv_processing_throughput(self, performance_csv_data, performance_monitor):
        """Test CSV processing throughput with different data sizes."""
        service = CSVBatchService()
        
        for size_name, csv_data in performance_csv_data.items():
            if size_name == 'xl':  # Skip XL for regular tests
                continue
                
            # Create temporary CSV file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                writer = csv.writer(f)
                writer.writerows(csv_data)
                csv_file_path = f.name
            
            try:
                performance_monitor.start()
                
                # Process CSV data
                with open(csv_file_path, 'r') as f:
                    processed_data = list(csv.reader(f))
                
                # Simulate validation and processing
                header = processed_data[0]
                data_rows = processed_data[1:]
                
                valid_rows = 0
                for row in data_rows:
                    if len(row) == len(header) and all(cell.strip() for cell in row):
                        valid_rows += 1
                
                performance_monitor.stop()
                
                execution_time = performance_monitor.get_execution_time()
                throughput = len(data_rows) / execution_time  # records per second
                
                print(f"\n{size_name.upper()} CSV Performance:")
                print(f"  Records: {len(data_rows):,}")
                print(f"  Time: {execution_time:.2f}s")
                print(f"  Throughput: {throughput:,.0f} records/sec")
                print(f"  Valid rows: {valid_rows:,}")
                
                # Performance assertions
                if size_name == 'small':
                    assert throughput > 1000, f"Small CSV throughput {throughput:.0f} too low"
                elif size_name == 'medium':
                    assert throughput > 500, f"Medium CSV throughput {throughput:.0f} too low"
                elif size_name == 'large':
                    assert throughput > 100, f"Large CSV throughput {throughput:.0f} too low"
                
            finally:
                os.unlink(csv_file_path)
    
    @pytest.mark.performance
    @pytest.mark.memory_intensive
    def test_memory_usage_large_csv(self, performance_csv_data, service_performance_data):
        """Test memory usage with large CSV processing."""
        service = CSVBatchService()
        large_csv_data = performance_csv_data['large']  # 100K records
        
        # Monitor memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process large CSV
        header = large_csv_data[0]
        data_rows = large_csv_data[1:]
        
        # Simulate processing steps
        processed_batches = []
        batch_size = 1000
        
        for i in range(0, len(data_rows), batch_size):
            batch = data_rows[i:i + batch_size]
            
            # Simulate batch processing
            batch_data = []
            for row in batch:
                if len(row) == len(header):
                    processed_row = {
                        'image_path': row[0],
                        'annotation_data': row[1],
                        'metadata': row[2],
                        'confidence': float(row[3])
                    }
                    batch_data.append(processed_row)
            
            processed_batches.append({
                'batch_id': f'PERF_BATCH_{i // batch_size + 1:03d}',
                'data': batch_data
            })
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = final_memory - initial_memory
        
        print(f"\nMemory Usage Analysis:")
        print(f"  Initial memory: {initial_memory:.1f} MB")
        print(f"  Final memory: {final_memory:.1f} MB")
        print(f"  Memory used: {memory_used:.1f} MB")
        print(f"  Records processed: {len(data_rows):,}")
        print(f"  Memory per record: {memory_used / len(data_rows) * 1024:.2f} KB")
        
        # Memory usage should be reasonable
        max_memory = service_performance_data['memory_limits']['csv_processing']
        assert memory_used < max_memory, f"Memory usage {memory_used:.1f}MB exceeds limit {max_memory}MB"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_csv_processing(self, mock_db_session, performance_monitor):
        """Test concurrent CSV processing performance."""
        service = CSVBatchService()
        
        # Create multiple concurrent processing tasks
        project_codes = [f'CONCURRENT_PERF_{i:03d}' for i in range(10)]
        
        async def process_project(project_code):
            """Simulate CSV processing for a project."""
            await asyncio.sleep(0.1)  # Simulate processing time
            return {
                'project_code': project_code,
                'batches_created': 5,
                'records_processed': 1000,
                'success': True
            }
        
        performance_monitor.start()
        
        # Run concurrent processing
        tasks = [process_project(code) for code in project_codes]
        results = await asyncio.gather(*tasks)
        
        performance_monitor.stop()
        
        execution_time = performance_monitor.get_execution_time()
        
        # Verify all completed successfully
        successful_results = [r for r in results if r['success']]
        assert len(successful_results) == len(project_codes)
        
        # Concurrent processing should be faster than sequential
        total_records = sum(r['records_processed'] for r in successful_results)
        throughput = total_records / execution_time
        
        print(f"\nConcurrent Processing Performance:")
        print(f"  Projects: {len(project_codes)}")
        print(f"  Total records: {total_records:,}")
        print(f"  Time: {execution_time:.2f}s")
        print(f"  Concurrent throughput: {throughput:,.0f} records/sec")
        
        # Should process multiple projects efficiently
        assert throughput > 5000, f"Concurrent throughput {throughput:.0f} too low"
    
    @pytest.mark.performance
    @pytest.mark.stress
    def test_stress_large_csv_files(self, service_performance_data):
        """Stress test with very large CSV files."""
        service = CSVBatchService()
        
        # Generate stress test data (1M records)
        stress_data_size = 1000000
        
        print(f"\nStress Test - Processing {stress_data_size:,} records")
        
        start_time = time.time()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # Simulate processing large dataset in chunks
        chunk_size = 10000
        processed_chunks = 0
        
        for chunk_start in range(0, stress_data_size, chunk_size):
            chunk_end = min(chunk_start + chunk_size, stress_data_size)
            chunk_size_actual = chunk_end - chunk_start
            
            # Simulate chunk processing
            chunk_data = []
            for i in range(chunk_start, chunk_end):
                chunk_data.append({
                    'image_path': f'/stress/img_{i:07d}.jpg',
                    'data': f'annotation_data_{i}',
                    'processed': True
                })
            
            processed_chunks += 1
            
            # Memory cleanup simulation
            if processed_chunks % 10 == 0:  # Every 100K records
                chunk_data = []  # Clear memory
        
        end_time = time.time()
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        execution_time = end_time - start_time
        throughput = stress_data_size / execution_time
        memory_used = final_memory - initial_memory
        
        print(f"  Processing time: {execution_time:.2f}s")
        print(f"  Throughput: {throughput:,.0f} records/sec")
        print(f"  Memory used: {memory_used:.1f} MB")
        print(f"  Chunks processed: {processed_chunks}")
        
        # Stress test assertions
        assert throughput > 50000, f"Stress test throughput {throughput:.0f} too low"
        assert memory_used < 500, f"Stress test memory {memory_used:.1f}MB too high"
        assert execution_time < 30, f"Stress test time {execution_time:.2f}s too high"
    
    @pytest.mark.performance
    def test_csv_format_parsing_performance(self, performance_monitor):
        """Test performance of different CSV format parsing."""
        formats = {
            'comma': ',',
            'semicolon': ';',
            'tab': '\t',
            'pipe': '|'
        }
        
        record_count = 10000
        results = {}
        
        for format_name, delimiter in formats.items():
            # Generate CSV data with specific delimiter
            csv_content = f'path{delimiter}label{delimiter}confidence\n'
            for i in range(record_count):
                csv_content += f'/img_{i}.jpg{delimiter}class_{i % 5}{delimiter}{0.8 + (i % 20) * 0.01}\n'
            
            performance_monitor.start()
            
            # Parse CSV content
            lines = csv_content.strip().split('\n')
            header = lines[0].split(delimiter)
            data_rows = []
            
            for line in lines[1:]:
                row = line.split(delimiter)
                if len(row) == len(header):
                    data_rows.append(row)
            
            performance_monitor.stop()
            
            execution_time = performance_monitor.get_execution_time()
            throughput = len(data_rows) / execution_time
            
            results[format_name] = {
                'time': execution_time,
                'throughput': throughput,
                'records': len(data_rows)
            }
        
        print(f"\nCSV Format Parsing Performance:")
        for format_name, result in results.items():
            print(f"  {format_name.upper()}: {result['throughput']:,.0f} records/sec")
        
        # All formats should parse efficiently
        for format_name, result in results.items():
            assert result['throughput'] > 1000, f"{format_name} format too slow: {result['throughput']:.0f}"
