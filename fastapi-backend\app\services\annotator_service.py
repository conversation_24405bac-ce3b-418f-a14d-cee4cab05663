"""
Combined annotation and verification functionality for the annotation app.
Contains all functions related to both manual image labeling and verification.
"""
import os
import json
import logging
from datetime import datetime
from typing import Tuple, List, Dict, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.config import settings
from core.nas_connector import get_ftp_connector
# from post_db.image_annotation import ImageAnnotation
# from post_db.image_verification import ImageVerification
# from post_db.datasets import Datasets
from schemas.annotation_schemas import ImageInfo
from services.project_batch_service import ProjectBatchService

logger = logging.getLogger('annotator_service')

class AnnotatorService:
    """Service class for both annotation and verification operations"""


    @staticmethod
    def _normalize_image_path(path: str) -> str:
        """Normalize image path for consistent handling"""
        normalized_path = path.replace('\\', '/')
        if not normalized_path.startswith('/'):
            normalized_path = '/' + normalized_path
        return normalized_path.replace('//', '/')

    @staticmethod
    async def get_batch_for_user(db: AsyncSession, username: str, mode: str = 'annotation') -> Tuple[List[ImageInfo], Dict, str, Optional[str]]:
        """Get the current batch of images for a user in specified mode"""
        try:
            Model, status_field, user_field = AnnotatorService._get_model_config(mode)
            
            # Try to get existing assigned batch
            # TODO: replace with async select
            result = await db.execute(select(Model).where(
                getattr(Model, user_field) == username,
                getattr(Model, status_field) == 'assigned'
            ).limit(1))
            batch = result.scalar_one_or_none()
            
            # If no batch, assign a new one
            if not batch:
                batch_manager = ProjectBatchService()
                success, message, assigned = await batch_manager.assign_allocation_batch_to_user_from_active_project(username, mode=mode)
                if not success or not assigned:
                    logger.info(f"No batches available to assign for {username} in {mode} mode: {message}")
                    return [], {}, "", None
                
                # Use the assigned batch data directly from the new allocation system
                # Convert allocation_batches data to a compatible format for processing
                batch = type('MockBatch', (), {
                    'id': assigned['id'],
                    'dataset_batch_name': assigned.get('batch_identifier', ''),
                    'dataset_name': assigned.get('batch_identifier', ''),  # Use batch_identifier as dataset name for now
                    'images': assigned.get('file_list', []),  # allocation_batches uses 'file_list' (JSONB)
                    'image_count': assigned.get('total_files', 0),
                    'assigned_at': assigned.get('created_at'),
                    'processed_at': None,
                    'label_file_path': None
                })()
            # Get FTP connector
            connector = await get_ftp_connector()
            if not connector:
                logger.error("Failed to get FTP connector")
                return [], {}, "", None

            # Process images
            selected_images = await AnnotatorService._process_batch_images(batch, connector)
            
            # Load labels for verification mode
            image_labels = await AnnotatorService._load_labels_for_mode(db, batch, connector, mode)

            # Reset batch if no images found
            if not selected_images:
                setattr(batch, status_field, 'available')
                setattr(batch, user_field, None)
                batch.assigned_at = None
                db.add(batch)
                await db.commit()
                return [], {}, "", None

            return selected_images, image_labels, batch.dataset_batch_name, batch.dataset_name

        except Exception as e:
            logger.error(f"Error getting batch for user: {str(e)}")
            return [], {}, "", None

    @staticmethod
    async def _process_batch_images(batch, connector) -> List[ImageInfo]:
        """Process batch images and return ImageInfo objects"""
        selected_images = []
        image_paths = json.loads(batch.images) if isinstance(batch.images, str) else batch.images

        for img_path in image_paths:
            try:
                file_info = await connector.get_file_info(img_path)
                if file_info:
                    normalized_path = AnnotatorService._normalize_image_path(img_path)
                    selected_images.append(ImageInfo(
                        name=os.path.basename(normalized_path),
                        path=normalized_path,
                        size=file_info.get('size', 0),
                        type='file'
                    ))
            except Exception as e:
                logger.error(f"Error getting file info for {img_path}: {str(e)}")
        
        return selected_images

    # @staticmethod
    # async def _load_labels_for_mode(db: AsyncSession, batch, connector, mode: str) -> Dict[str, str]:
    #     """Load labels based on mode"""
    #     image_labels = {}
        
    #     if mode == 'verification':
    #         # Get the dataset to find the original label folder path
    #         dataset_obj = await db.execute(select(Datasets).where(Datasets.dataset_name == batch.dataset_name).limit(1))
    #         dataset_obj = dataset_obj.scalar_one_or_none()
    #         if dataset_obj and dataset_obj.label_folder_path:
    #             original_label_path = dataset_obj.label_folder_path
    #             logger.info(f"Attempting to load original labels from: {original_label_path}")
    #             try:
    #                 file_bytes = await connector.get_file_content(original_label_path)
    #                 if file_bytes:
    #                     image_labels = json.loads(file_bytes.decode('utf-8'))
    #                     logger.info(f"Successfully loaded {len(image_labels)} original labels from dataset")
    #                 else:
    #                     logger.warning(f"No content found in original label file {original_label_path}")
    #             except Exception as e:
    #                 logger.error(f"Error loading original labels from {original_label_path}: {e}")
    #         else:
    #             logger.warning(f"No label_folder_path found in dataset {batch.dataset_name}")
        
    #     return image_labels

    @staticmethod
    def prepare_image_data(selected_images: List[ImageInfo], image_labels: Dict[str, str] = None) -> List[ImageInfo]:
        """Prepare image data for response"""
        image_data = []
        for img in selected_images:
            label = image_labels.get(img.name, "") if image_labels else ""
            image_url = f"{settings.api_prefix}/annotator/image/{img.path.lstrip('/')}"
            
            image_data.append(ImageInfo(
                name=img.name,
                path=img.path,
                url=image_url,
                label=label,
                size=img.size,
                type=img.type
            ))
        
        return image_data

annotator_service = AnnotatorService() 