"use client";

import { useState, useCallback } from "react";
import { authFetch } from "@/lib/authFetch";
import { showToast } from "@/lib/toast";
import { MediaType, detectMediaType, getMediaEndpoint } from "../../shared/media/types";

import { API_BASE_URL } from "@/lib/api";

const API_BASE = API_BASE_URL;

export interface AnnotationFile {
  url: string;
  name: string;
  type: MediaType;
  path: string;
  ai_suggestions?: Record<string, any> | null;
  processing_status?: string;
  // CSV-specific properties
  csvBatchId?: number;
  projectCode?: string;
}

export interface BatchData {
  files: AnnotationFile[];
  instructions: string;
  batchName: string;
}

export function useAnnotationData() {
  const [fetchedFiles, setFetchedFiles] = useState<AnnotationFile[]>([]);
  const [fetchedInstructions, setFetchedInstructions] = useState<string>("");
  const [batchName, setBatchName] = useState<string>("");
  const [noTasks, setNoTasks] = useState(false);

  const loadBatch = useCallback(async (folder?: string) => {
    setNoTasks(false);
    
    try {
      console.log("Loading current batch data...");
      
      // First, try to get current batch details from the new assignment system
      const currentBatchResp = await authFetch(`${API_BASE}/annotator/current-batch`, {
        credentials: "include",
      });
      
      if (currentBatchResp.ok) {
        const batchData = await currentBatchResp.json();
        console.log("Current batch data:", batchData);
        console.log("Batch files:", batchData.batch?.files);
        console.log("First file type:", batchData.batch?.files?.[0]?.file_type);
        console.log("File type details:", {
          type: typeof batchData.batch?.files?.[0]?.file_type,
          value: batchData.batch?.files?.[0]?.file_type,
          toString: batchData.batch?.files?.[0]?.file_type?.toString?.()
        });
        
        if (batchData.success && batchData.batch && batchData.batch.files && batchData.batch.files.length > 0) {
          // Check if this is a CSV project by looking at file types
          const isCSVProject = batchData.batch.files && 
                              batchData.batch.files.length > 0 && 
                              batchData.batch.files.every((file: any) => {
                                // Handle both string 'csv' and enum object {FileType.CSV: 'csv'}
                                const fileType = file.file_type;
                                return fileType === 'csv' || 
                                       (typeof fileType === 'object' && fileType && fileType.toString().includes('csv')) ||
                                       (fileType && fileType._value_ === 'csv');
                              });
          
          console.log("Is CSV project:", isCSVProject);
          
          let annotationFiles: AnnotationFile[] = [];
          
          if (isCSVProject) {
            // For CSV projects, create individual files for each CSV row
            annotationFiles = batchData.batch.files.map((file: any, index: number) => ({
              url: `csv_row_${file.id || index}`, // Use a unique identifier for saving
              name: `Row ${index + 1}`,
              type: 'csv' as MediaType,
              path: file.file_identifier, // Put the text content in path
              ai_suggestions: file.ai_suggestions || null,
              processing_status: file.processing_status || 'ready'
            }));
            
            setFetchedFiles(annotationFiles);
            setFetchedInstructions(batchData.batch.instructions || "");
            setBatchName(batchData.batch.batch_identifier || "CSV Batch");
            
            return { 
              success: true, 
              data: { 
                files: annotationFiles, 
                instructions: batchData.batch.instructions || "", 
                batchName: batchData.batch.batch_identifier || "CSV Batch"
              } 
            };
          } else {
            // Convert batch files to annotation files (existing logic for non-CSV projects)
            annotationFiles = batchData.batch.files.map((file: any) => {
              // Extract file path from storage_location
              let filePath = "";
              
              if (file.storage_location) {
                if (typeof file.storage_location === 'object' && file.storage_location.path) {
                  filePath = file.storage_location.path;
                } else if (typeof file.storage_location === 'string') {
                  try {
                    const parsed = JSON.parse(file.storage_location);
                    if (parsed && parsed.path) {
                      filePath = parsed.path;
                    } else {
                      filePath = file.storage_location;
                    }
                  } catch {
                    filePath = file.storage_location;
                  }
                }
              } else if (file.file_identifier) {
                filePath = file.file_identifier;
              } else if (file.filename) {
                filePath = file.filename;
              }
              
              // Detect media type and get proper URL
              const mediaType = detectMediaType(filePath);
              const url = getMediaEndpoint(mediaType, filePath);
              
              return {
                url,
                name: file.filename || filePath.split('/').pop() || 'Unknown',
                type: mediaType,
                path: filePath,
                ai_suggestions: file.ai_suggestions || null,
                processing_status: file.processing_status || 'pending'
              };
            }).filter(Boolean);
          }
          
          if (annotationFiles.length > 0) {
            setFetchedFiles(annotationFiles);
            setFetchedInstructions(batchData.batch.instructions || "");
            setBatchName(batchData.batch.batch_identifier || folder || "");
            console.log("Loaded batch with", annotationFiles.length, "files");
            return { success: true, data: { files: annotationFiles, instructions: batchData.batch.instructions || "", batchName: batchData.batch.batch_identifier || folder || "" } };
          } else {
            console.warn("No valid file URLs could be constructed from batch files");
          }
        }
      } else if (currentBatchResp.status === 404) {
        console.log("No current batch assigned to user");
        setNoTasks(true);
        return { success: false, error: "No tasks available" };
      } else {
        console.warn("Failed to get current batch:", currentBatchResp.status, currentBatchResp.statusText);
      }
      
      // Fallback: try the old system if no current batch
      console.log("No current batch found, trying fallback...");
      let resp = await authFetch(`${API_BASE}/annotator/annotate`, {
        credentials: "include",
      });
      
      if (resp.status === 204) {
        setNoTasks(true);
        return { success: false, error: "No tasks available" };
      }
      
      let data = await resp.json();
      if (!data.images || data.images.length === 0) {
        const nextResp = await authFetch(`${API_BASE}/annotator/next-set`, {
          credentials: "include",
        });
        const nextData = await nextResp.json();
        if (nextData.status !== "success") {
          setNoTasks(true);
          return { success: false, error: "No tasks available" };
        }
        resp = await authFetch(`${API_BASE}/annotator/annotate`, {
          credentials: "include",
        });
        if (resp.status === 204) {
          setNoTasks(true);
          return { success: false, error: "No tasks available" };
        }
        data = await resp.json();
      }
      
      // Convert old format to new format
      const annotationFiles: AnnotationFile[] = data.images.map((img: { url: string }) => {
        let path = img.url;
        if (!path.startsWith("http")) {
          if (path.startsWith("/api")) path = path.replace(/^\/api/, "");
          path = `${API_BASE}${path}`;
        }
        
        const mediaType = detectMediaType(path);
        return {
          url: path,
          name: path.split('/').pop() || 'Unknown',
          type: mediaType,
          path: path,
          ai_suggestions: null,
          processing_status: 'pending'
        };
      });
      
      setFetchedFiles(annotationFiles);
      setFetchedInstructions(data.instructions || "");
      setBatchName(data.batch_name || folder || "");
      
      return { 
        success: true, 
        data: { 
          files: annotationFiles, 
          instructions: data.instructions || "", 
          batchName: data.batch_name || folder || "" 
        } 
      };
      
    } catch (err: unknown) {
      console.error("Error loading batch:", err);
      showToast.error("Failed to load batch");
      setNoTasks(true);
      return { success: false, error: "Failed to load batch" };
    }
  }, []);

  return {
    fetchedFiles,
    fetchedInstructions,
    batchName,
    noTasks,
    loadBatch,
    setFetchedFiles,
    setFetchedInstructions,
    setBatchName,
    setNoTasks
  };
}
