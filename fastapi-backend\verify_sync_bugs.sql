-- DATABASE SYNCHRONIZATION BUGS VERIFICATION SCRIPT
-- Run this on your master database to check for sync issues

-- ===================================================
-- BUG 1 & 2: Batch Creation Sync Issues  
-- ===================================================
SELECT 
    'Batch Creation Sync Issues' as bug_type,
    project_code,
    project_type,
    total_batches as master_batches,
    total_files as master_files,
    CASE 
        WHEN total_batches = 0 AND total_files = 0 THEN 'LIKELY BUG'
        ELSE 'OK'
    END as status
FROM projects_registry 
WHERE project_type IN ('csv', 'image', 'video', 'audio', 'pdf')
AND created_at > NOW() - INTERVAL '30 days'  -- Recent projects
ORDER BY created_at DESC;

-- ===================================================  
-- BUG 3: User Removal Counter Issues
-- ===================================================
SELECT 
    'User Assignment Counter Issues' as bug_type,
    p.project_code,
    p.active_annotators as master_count,
    COUNT(upa.user_id) as actual_active_count,
    (p.active_annotators - COUNT(upa.user_id)) as difference,
    CASE 
        WHEN p.active_annotators != COUNT(upa.user_id) THEN 'LIKELY BUG'
        ELSE 'OK' 
    END as status
FROM projects_registry p
LEFT JOIN user_project_access upa ON p.id = upa.project_id 
    AND upa.project_role = 'annotator' 
    AND upa.is_active = true
WHERE p.project_status = 'active'
GROUP BY p.id, p.project_code, p.active_annotators
ORDER BY difference DESC;

-- ===================================================
-- BUG 4: Project Deactivation Counter Issues  
-- ===================================================
SELECT 
    'Deactivation Counter Issues' as bug_type,
    project_code,
    project_status,
    active_annotators,
    CASE 
        WHEN project_status = 'inactive' AND active_annotators > 0 THEN 'CONFIRMED BUG'
        ELSE 'OK'
    END as status
FROM projects_registry 
WHERE project_status = 'inactive'
AND active_annotators > 0;

-- ===================================================
-- BUG 5: Completion Sync Issues (Needs Project DB Check)
-- ===================================================  
SELECT 
    'Completion Sync Check Needed' as bug_type,
    project_code,
    completed_files,
    total_files,
    last_sync_at,
    CASE 
        WHEN last_sync_at IS NULL THEN 'NEVER SYNCED'
        WHEN last_sync_at < NOW() - INTERVAL '1 day' THEN 'STALE SYNC'
        ELSE 'RECENT SYNC'
    END as sync_status
FROM projects_registry 
WHERE total_files > 0
ORDER BY last_sync_at ASC NULLS FIRST;

-- ===================================================
-- SUMMARY: Projects Likely Affected by Bugs
-- ===================================================
SELECT 
    COUNT(*) as total_projects,
    COUNT(CASE WHEN total_batches = 0 AND total_files = 0 THEN 1 END) as likely_batch_bugs,
    COUNT(CASE WHEN project_status = 'inactive' AND active_annotators > 0 THEN 1 END) as deactivation_bugs,
    COUNT(CASE WHEN last_sync_at IS NULL THEN 1 END) as never_synced
FROM projects_registry;
