import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface CheckFieldsProps {
  data: Record<string, string>;
  onChange: (name: string, value: string) => void;
}

export default function CheckFields({ data, onChange }: CheckFieldsProps) {
  return (
    <div className="space-y-6">
      {/* Link to File */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Link to File</label>
        <input
          type="text"
          name="Link to File"
          value={data['Link to File'] || ''}
          onChange={e => onChange('Link to File', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>

      {/* Pic Date / Download Date */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Pic Date</label>
          <DatePicker
            selected={data['Pic Date'] ? new Date(data['Pic Date']) : null}
            onChange={date => onChange('Pic Date', date ? (date as Date).toISOString().split('T')[0] : '')}
            dateFormat="yyyy-MM-dd"
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            placeholderText="Select date"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Download Date</label>
          <DatePicker
            selected={data['Download Date'] ? new Date(data['Download Date']) : null}
            onChange={date => onChange('Download Date', date ? (date as Date).toISOString().split('T')[0] : '')}
            dateFormat="yyyy-MM-dd"
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            placeholderText="Select date"
          />
        </div>
      </div>

      {/* Bank Name / Check Type */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Bank Name</label>
          <input
            type="text"
            name="Bank Name"
            value={data['Bank Name'] || ''}
            onChange={e => onChange('Bank Name', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Check Type</label>
          <select
            name="Check Type"
            value={data['Check Type'] || ''}
            onChange={e => onChange('Check Type', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          >
            <option value="">Select Check Type</option>
            <option value="Business">Business</option>
            <option value="Government">Government</option>
            <option value="Cashier">Cashier</option>
            <option value="Personal">Personal</option>
            <option value="Money Order">Money Order</option>
          </select>
        </div>
      </div>

      {/* Payor Information */}
      <div>
        <h6 className="text-sm font-medium text-gray-700 mb-2">Payor Information</h6>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">1st Payor First Name</label>
            <input
              type="text"
              name="1st Payor First Name"
              value={data['1st Payor First Name'] || ''}
              onChange={e => onChange('1st Payor First Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">1st Payor Family Name</label>
            <input
              type="text"
              name="1st Payor Family Name"
              value={data['1st Payor Family Name'] || ''}
              onChange={e => onChange('1st Payor Family Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Payor Street Address</label>
            <input
              type="text"
              name="Payor Street Address"
              value={data['Payor Street Address'] || ''}
              onChange={e => onChange('Payor Street Address', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Payor City</label>
            <input
              type="text"
              name="Payor City"
              value={data['Payor City'] || ''}
              onChange={e => onChange('Payor City', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Payor State</label>
            <input
              type="text"
              name="Payor State"
              value={data['Payor State'] || ''}
              onChange={e => onChange('Payor State', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Payor Zip code</label>
            <input
              type="text"
              name="Payor Zip code"
              value={data['Payor Zip code'] || ''}
              onChange={e => onChange('Payor Zip code', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
      </div>

      {/* Payee Information */}
      <div>
        <h6 className="text-sm font-medium text-gray-700 mb-2">Payee Information</h6>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">1st Payee First Name</label>
            <input
              type="text"
              name="1st Payee First Name"
              value={data['1st Payee First Name'] || data['Payee Name'] || ''}
              onChange={e => onChange('1st Payee First Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">1st Payee Family Name</label>
            <input
              type="text"
              name="1st Payee Family Name"
              value={data['1st Payee Family Name'] || ''}
              onChange={e => onChange('1st Payee Family Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        {/* 2nd Payee Names */}
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">2nd Payee First Name</label>
            <input
              type="text"
              name="2nd Payee First Name"
              value={data['2nd Payee First Name'] || ''}
              onChange={e => onChange('2nd Payee First Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">2nd Payee Family Name</label>
            <input
              type="text"
              name="2nd Payee Family Name"
              value={data['2nd Payee Family Name'] || ''}
              onChange={e => onChange('2nd Payee Family Name', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        {/* Payee Street Address */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700">Payee Street Address</label>
          <input
            type="text"
            name="Payee Street Address"
            value={data['Payee Street Address'] || data['Payee Address'] || ''}
            onChange={e => onChange('Payee Street Address', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        {/* Payee City/State/Zip */}
        <div className="grid grid-cols-3 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Payee City</label>
            <input
              type="text"
              name="Payee City"
              value={data['Payee City'] || ''}
              onChange={e => onChange('Payee City', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Payee State</label>
            <input
              type="text"
              name="Payee State"
              value={data['Payee State'] || ''}
              onChange={e => onChange('Payee State', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Payee Zip Code</label>
            <input
              type="text"
              name="Payee Zip Code"
              value={data['Payee Zip Code'] || ''}
              onChange={e => onChange('Payee Zip Code', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div>
        <h6 className="text-sm font-medium text-gray-700 mb-2">Additional Information</h6>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Account Number</label>
            <input
              type="text"
              name="Account Number"
              value={data['Account Number'] || ''}
              onChange={e => onChange('Account Number', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Routing Number</label>
            <input
              type="text"
              name="Routing Number"
              value={data['Routing Number'] || ''}
              onChange={e => onChange('Routing Number', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Check Amount</label>
            <input
              type="text"
              name="Check Amount"
              value={data['Check Amount'] || ''}
              onChange={e => onChange('Check Amount', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Check Number</label>
            <input
              type="text"
              name="Check Number"
              value={data['Check Number'] || ''}
              onChange={e => onChange('Check Number', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Payee Type</label>
            <input
              type="text"
              name="Payee Type"
              value={data['Payee Type'] || ''}
              onChange={e => onChange('Payee Type', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Market</label>
            <input
              type="text"
              name="Market"
              value={data['Market'] || ''}
              onChange={e => onChange('Market', e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            />
          </div>
        </div>
      </div>
    </div>
  );
} 