"""
Shared database base for all models.
This module contains the unified declarative base that both regular models 
and allocation models will inherit from.
"""

from sqlalchemy.orm import declarative_base

# Unified Base for all models (both regular and allocation models)
Base = declarative_base()

# Note: Master models are imported where needed (routes, services, init_db.py)
# SQLAlchemy will register them with the Base metadata automatically when imported
# Importing them here creates circular import issues since models import Base