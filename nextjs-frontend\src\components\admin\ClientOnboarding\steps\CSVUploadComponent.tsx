// steps/CSVUploadComponent.tsx
import React, { useState, useRef, useCallback } from 'react';
import { FaUpload, <PERSON>a<PERSON><PERSON><PERSON>, FaCheckCircle, FaTimesCircle, FaFileAlt, FaTrash } from 'react-icons/fa';

interface CSVMetadata {
  columns: string[];
  total_columns: number;
  sample_rows: number;
  encoding: string;
  column_types: Record<string, string>;
  file_extension: string;
  error?: string;
}

interface CSVUploadComponentProps {
  onUploadComplete: (uploadData: { fileName: string; filePath: string; fileSize: number; csvMetadata?: CSVMetadata }) => void;
  onUploadError: (error: string) => void;
  isUploading?: boolean;
}

interface UploadedFile {
  name: string;
  size: number;
  uploadedAt: Date;
  path: string;
  csvMetadata?: CSVMetadata;
}

export const CSVUploadComponent: React.FC<CSVUploadComponentProps> = ({
  onUploadComplete,
  onUploadError,
  isUploading = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Validate file type and size
  const validateFile = (file: File): string | null => {
    const allowedTypes = ['.csv', '.xlsx', '.xls'];
    const maxSize = 50 * 1024 * 1024; // 50MB
    
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
      return 'Please upload a CSV or Excel file (.csv, .xlsx, .xls)';
    }
    
    if (file.size > maxSize) {
      return 'File size must be less than 50MB';
    }
    
    return null;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    if (uploadedFile) {
      onUploadError('Please remove the current file before uploading a new one');
      return;
    }

    const validation = validateFile(file);
    if (validation) {
      onUploadError(validation);
      return;
    }

    // MinIO credentials are now handled server-side via environment variables

    try {
      setUploadProgress(0);
      
      // Get project code from localStorage
      const projectData = localStorage.getItem('currentProject');
      if (!projectData) {
        throw new Error('No project data found. Please ensure a project is selected.');
      }
      
      const project = JSON.parse(projectData);
      const projectCode = project.project_code;
      
      if (!projectCode) {
        throw new Error('No project code found. Please ensure a project is selected.');
      }

      // Create FormData for file upload (credentials come from project)
      const formData = new FormData();
      formData.append('file', file);
      
      // Upload to MinIO via API using project code
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000';
      const response = await fetch(`${backendUrl}/api/admin/upload-csv/${projectCode}`, {
        method: 'POST',
        body: formData,
        credentials: 'include', // Include cookies for authentication
      });

      if (!response.ok) {
        let errorMessage = 'Upload failed';
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch (e) {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        console.error('Upload failed:', errorMessage);
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      const uploadedFileData: UploadedFile = {
        name: file.name,
        size: file.size,
        uploadedAt: new Date(),
        path: result.file_path || `csv-uploads/${projectCode}/${file.name}`,
        csvMetadata: result.csv_metadata
      };

      setUploadedFile(uploadedFileData);
      setUploadProgress(100);

      // Notify parent component
      onUploadComplete({
        fileName: file.name,
        filePath: uploadedFileData.path,
        fileSize: file.size,
        csvMetadata: result.csv_metadata
      });

    } catch (error: any) {
      console.error('Upload error:', error);
      onUploadError(error.message || 'Upload failed. Please try again.');
      setUploadProgress(0);
    }
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, [uploadedFile]);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  // Remove uploaded file
  const handleRemoveFile = () => {
    setUploadedFile(null);
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Open file dialog
  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      {!uploadedFile && (
        <div
          className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv,.xlsx,.xls"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={isUploading}
          />
          
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-gray-400">
              {isUploading ? (
                <FaSpinner className="w-full h-full animate-spin text-blue-500" />
              ) : (
                <FaUpload className="w-full h-full" />
              )}
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-700">
                {isUploading ? 'Uploading...' : 'Upload CSV File'}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Drag and drop your CSV or Excel file here, or click to browse
              </p>
              <p className="text-xs text-gray-400 mt-2">
                Supported formats: .csv, .xlsx, .xls (max 50MB)
              </p>
            </div>
            
            {!isUploading && (
              <button
                onClick={openFileDialog}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Choose File
              </button>
            )}
          </div>

          {/* Upload Progress */}
          {isUploading && uploadProgress > 0 && (
            <div className="mt-4">
              <div className="bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-600 mt-1">{uploadProgress}% uploaded</p>
            </div>
          )}
        </div>
      )}

      {/* Uploaded File Display */}
      {uploadedFile && (
        <div className="border border-green-200 bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <FaFileAlt className="text-green-600 text-xl" />
              <div>
                <p className="font-medium text-green-800">{uploadedFile.name}</p>
                <p className="text-sm text-green-600">
                  {formatFileSize(uploadedFile.size)} • Uploaded {uploadedFile.uploadedAt.toLocaleTimeString()}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FaCheckCircle className="text-green-600" />
              <button
                onClick={handleRemoveFile}
                className="text-red-600 hover:text-red-800 p-1"
                title="Remove file"
              >
                <FaTrash className="text-sm" />
              </button>
            </div>
          </div>
          
          {/* CSV Metadata Display */}
          {uploadedFile.csvMetadata && (
            <div className="border-t border-green-200 pt-3">
              <h4 className="text-sm font-medium text-green-800 mb-2">File Information</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-700 font-medium">Columns:</span>
                  <span className="text-green-600 ml-1">{uploadedFile.csvMetadata.total_columns}</span>
                </div>
                <div>
                  <span className="text-green-700 font-medium">Sample Rows:</span>
                  <span className="text-green-600 ml-1">{uploadedFile.csvMetadata.sample_rows}</span>
                </div>
                <div>
                  <span className="text-green-700 font-medium">Encoding:</span>
                  <span className="text-green-600 ml-1">{uploadedFile.csvMetadata.encoding}</span>
                </div>
                <div>
                  <span className="text-green-700 font-medium">Format:</span>
                  <span className="text-green-600 ml-1">{uploadedFile.csvMetadata.file_extension.toUpperCase()}</span>
                </div>
              </div>
              
              {uploadedFile.csvMetadata.columns.length > 0 && (
                <div className="mt-3">
                  <span className="text-green-700 font-medium text-sm">Available Columns:</span>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {uploadedFile.csvMetadata.columns.slice(0, 8).map((column, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                        {column}
                      </span>
                    ))}
                    {uploadedFile.csvMetadata.columns.length > 8 && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">
                        +{uploadedFile.csvMetadata.columns.length - 8} more
                      </span>
                    )}
                  </div>
                </div>
              )}
              
              {uploadedFile.csvMetadata.error && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-yellow-800 text-xs">
                    <strong>Note:</strong> {uploadedFile.csvMetadata.error}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      
    </div>
  );
};
