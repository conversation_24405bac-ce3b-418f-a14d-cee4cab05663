"""
Redis connector module for caching.
This module provides the core Redis functionality for all cache modules.
"""

import redis.asyncio as redis
import json
import datetime
from core.config import settings

# Set up logging
import logging
logger = logging.getLogger('redis_connector')

# Redis connection singleton
_redis_client: redis.Redis | None = None
_redis_enabled = True

def set_redis_client(client):
    """
    Set the Redis client from outside (e.g., from app.py)

    Args:
        client: Redis client instance
    """
    global _redis_client
    _redis_client = client

def set_redis_enabled(enabled):
    """
    Enable or disable Redis caching globally

    Args:
        enabled: Boolean indicating whether Redis should be used
    """
    global _redis_enabled
    _redis_enabled = enabled
    logger.info(f"Redis caching {'enabled' if enabled else 'disabled'}")

def is_redis_enabled():
    """
    Check if Redis caching is enabled

    Returns:
        bool: True if Redis caching is enabled
    """
    return _redis_enabled

async def create_redis_client() -> redis.Redis | None:
    """
    Create an async Redis client using configuration.

    Returns:
        Redis async client instance or None if connection fails
    """
    try:
        cfg = settings.redis_settings
        client = redis.Redis(
            host=cfg.host,
            port=cfg.port,
            db=cfg.db,
            password=cfg.password,
            decode_responses=False
        )
        # Test connection asynchronously
        await client.ping()
        logger.info(f"Successfully connected to Redis at {cfg.host}:{cfg.port}")
        return client
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        return None

def get_redis_client() -> redis.Redis | None:
    """
    Get the initialized Redis client instance.

    Returns:
        Redis client instance or None if disabled or not initialized
    """
    if not _redis_enabled:
        return None
    if _redis_client is None:
        logger.warning("Redis client is not initialized. Call set_redis_client() first.")
        return None
    return _redis_client

def serialize_datetime(obj):
    """JSON serializer for datetime objects"""
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

async def cache_set(key, value, expire_seconds=None, ttl=None) -> bool:
    """
    Set a value in Redis cache

    Args:
        key: Cache key
        value: Value to cache (can be string, dict, list, or bytes)
        expire_seconds: Optional expiration time in seconds
        ttl: Optional expiration time in seconds (alias for expire_seconds)

    Returns:
        bool: Success status
    """
    # If Redis is disabled, skip caching
    if not is_redis_enabled():
        logger.debug(f"Redis caching disabled, skipping cache_set for key: {key}")
        return False

    client = get_redis_client()
    if not client:
        logger.warning(f"Redis client not available, skipping cache_set for key: {key}")
        return False

    # Check for None value - Redis doesn't accept None values
    if value is None:
        logger.warning(f"Attempted to cache None value for key: {key}. Skipping cache operation.")
        return False

    # Handle both ttl and expire_seconds parameters (ttl takes precedence for backward compatibility)
    expiration_time = ttl if ttl is not None else expire_seconds

    try:
        # Convert dict/list to JSON string if needed
        if isinstance(value, (dict, list)):
            value = json.dumps(value, default=serialize_datetime)

        # Set the value in Redis
        if expiration_time:
            await client.setex(key, expiration_time, value)
        else:
            await client.set(key, value)

        logger.debug(f"Successfully cached data for key: {key}")
        return True
    except Exception as e:
        logger.error(f"Error setting cache for key {key}: {e}")
        return False

async def cache_get(key, json_decode=False):
    """
    Get a value from Redis cache

    Args:
        key: Cache key
        json_decode: Whether to decode JSON string to dict/list

    Returns:
        Cached value or None if not found
    """
    # If Redis is disabled, return None
    if not is_redis_enabled():
        logger.debug(f"Redis caching disabled, skipping cache_get for key: {key}")
        return None

    client = get_redis_client()
    if not client:
        logger.warning(f"Redis client not available, skipping cache_get for key: {key}")
        return None

    try:
        # Get the value from Redis
        value = await client.get(key)

        if value is None:
            return None

        # Decode JSON if requested
        if json_decode and isinstance(value, (bytes, str)):
            decoded = value.decode('utf-8', errors='replace') if isinstance(value, bytes) else value
            try:
                return json.loads(decoded)
            except Exception:
                logger.warning(f"Failed to decode JSON for key {key}")
                return value

        return value
    except Exception as e:
        logger.error(f"Error getting cache for key {key}: {str(e)}")
        return None

async def cache_delete(key) -> bool:
    """
    Delete a value from Redis cache

    Args:
        key: Cache key

    Returns:
        bool: Success status
    """
    # If Redis is disabled, return False
    if not is_redis_enabled():
        logger.debug(f"Redis caching disabled, skipping cache_delete for key: {key}")
        return False

    client = get_redis_client()
    if not client:
        logger.warning(f"Redis client not available, skipping cache_delete for key: {key}")
        return False

    try:
        await client.delete(key)
        logger.debug(f"Successfully deleted cache for key: {key}")
        return True
    except Exception as e:
        logger.error(f"Error deleting cache for key {key}: {e}")
        return False

async def cache_exists(key) -> bool:
    """
    Check if a key exists in Redis cache

    Args:
        key: Cache key

    Returns:
        bool: True if key exists, False otherwise
    """
    # If Redis is disabled, return False
    if not is_redis_enabled():
        logger.debug(f"Redis caching disabled, skipping cache_exists for key: {key}")
        return False

    client = get_redis_client()
    if not client:
        logger.warning(f"Redis client not available, skipping cache_exists for key: {key}")
        return False

    try:
        exists = await client.exists(key)
        return exists > 0
    except Exception as e:
        logger.error(f"Error checking cache existence for key {key}: {e}")
        return False

async def flush_cache() -> bool:
    """
    Flush the entire Redis cache database

    Returns:
        bool: Success status
    """
    # If Redis is disabled, return False
    if not is_redis_enabled():
        logger.debug("Redis caching disabled, skipping flush_cache")
        return False

    client = get_redis_client()
    if not client:
        logger.warning("Redis client not available, skipping flush_cache")
        return False

    try:
        await client.flushdb()
        logger.info("Successfully flushed Redis cache database")
        return True
    except Exception as e:
        logger.error(f"Error flushing Redis cache database: {e}")
        return False

async def delete_keys_by_pattern(pattern: str) -> bool:
    """
    Delete all keys matching a Redis pattern using SCAN and UNLINK (or DELETE).

    Args:
        pattern: Glob-style Redis key pattern (e.g. 'admin:*')

    Returns:
        bool: Success status
    """
    # If Redis is disabled, skip operation
    if not is_redis_enabled():
        logger.debug(f"Redis caching disabled, skipping delete_keys_by_pattern for pattern: {pattern}")
        return False

    client = get_redis_client()
    if not client:
        logger.warning(f"Redis client not available, skipping delete_keys_by_pattern for pattern: {pattern}")
        return False

    try:
        cursor = 0
        while True:
            cursor, keys = await client.scan(cursor=cursor, match=pattern, count=100)
            if keys:
                try:
                    await client.unlink(*keys)
                except AttributeError:
                    await client.delete(*keys)
            if cursor == 0:
                break
        logger.info(f"Deleted keys matching pattern {pattern}")
        return True
    except Exception as e:
        logger.error(f"Error deleting keys by pattern {pattern}: {e}")
        return False