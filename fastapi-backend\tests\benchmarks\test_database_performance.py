"""
Database Performance Benchmarks
Compares performance across different data volumes using test_data_manager.py scenarios.

USAGE:
1. Run benchmarks: pytest tests/benchmarks/test_database_performance.py -v -s
2. Results will show performance across smoke_test, core_test, and performance_test scenarios
3. Use -k to run specific benchmarks: pytest tests/benchmarks/ -k batch_query
"""
import pytest
import time
import asyncio
import statistics
from typing import Dict, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.allocation_strategies import AllocationStrategies

# Import test factory and data manager
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


class BenchmarkRunner:
    """Utility class for running and recording performance benchmarks."""
    
    def __init__(self):
        self.results: Dict[str, Dict[str, float]] = {}
    
    async def run_benchmark(self, name: str, scenario: str, benchmark_func, *args, **kwargs) -> float:
        """Run a benchmark function and record the result."""
        times = []
        
        # Run multiple times for more accurate measurement
        for i in range(3):
            start_time = time.time()
            await benchmark_func(*args, **kwargs)
            end_time = time.time()
            times.append(end_time - start_time)
            
            # Small delay between runs
            await asyncio.sleep(0.1)
        
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        if name not in self.results:
            self.results[name] = {}
        
        self.results[name][scenario] = {
            'avg': avg_time,
            'min': min_time, 
            'max': max_time,
            'runs': times
        }
        
        print(f"🏁 {name} ({scenario}): {avg_time:.4f}s avg (min: {min_time:.4f}s, max: {max_time:.4f}s)")
        return avg_time
    
    def print_summary(self):
        """Print benchmark summary comparison."""
        print("\n" + "=" * 80)
        print("📊 BENCHMARK SUMMARY")
        print("=" * 80)
        
        for benchmark_name, scenarios in self.results.items():
            print(f"\n🎯 {benchmark_name}")
            print("-" * 60)
            
            for scenario_name, metrics in scenarios.items():
                print(f"   {scenario_name:15} | {metrics['avg']:.4f}s avg | {metrics['min']:.4f}s min | {metrics['max']:.4f}s max")
            
            # Show performance ratios
            if len(scenarios) >= 2:
                scenarios_list = list(scenarios.items())
                if len(scenarios_list) >= 2:
                    base_scenario = scenarios_list[0][1]['avg']
                    for scenario_name, metrics in scenarios_list[1:]:
                        ratio = metrics['avg'] / base_scenario if base_scenario > 0 else 0
                        print(f"   {scenario_name:15} | {ratio:.1f}x slower than {scenarios_list[0][0]}")


@pytest.fixture(scope="class")
def benchmark_runner():
    """Provide benchmark runner instance."""
    return BenchmarkRunner()


@pytest.mark.benchmark
@pytest.mark.integration
@pytest.mark.database
class TestDatabaseBenchmarksAcrossVolumes:
    """Database performance benchmarks across different data volumes."""
    
    @pytest.fixture(scope="class", autouse=True)
    async def setup_benchmark_data(self, benchmark_runner):
        """Setup different data volumes for benchmarking."""
        try:
            from test_data_manager import DatabaseTestDataManager
            
            async with DatabaseTestDataManager() as manager:
                print("\nSetting up benchmark data scenarios...")
                
                # Test each scenario
                scenarios = ["smoke_test", "core_test", "performance_test"]
                
                for scenario in scenarios:
                    print(f"\nSetting up {scenario}...")
                    await manager.create_specific_test_scenario(scenario)
                    
                    # Give some time for data to settle
                    await asyncio.sleep(1)
                    
                    # Run benchmarks for this scenario
                    yield scenario
                    
                print("\n🧹 Cleaning up benchmark data...")
                await manager.cleanup_all_test_data()
                
        except ImportError:
            print("⚠️ test_data_manager not available - skipping benchmark setup")
            yield None
        except Exception as e:
            print(f"❌ Error setting up benchmark data: {e}")
            yield None
    
    async def _benchmark_simple_batch_query(self, test_db: AsyncSession):
        """Benchmark function: simple batch query."""
        stmt = select(AllocationBatches).limit(50)
        result = await test_db.execute(stmt)
        batches = result.scalars().all()
        return len(batches)
    
    async def _benchmark_complex_batch_join(self, test_db: AsyncSession):
        """Benchmark function: complex batch join query."""
        stmt = select(
            AllocationBatches.batch_identifier,
            func.count(FilesRegistry.id).label('file_count')
        ).join(
            FilesRegistry, AllocationBatches.id == FilesRegistry.batch_id
        ).group_by(
            AllocationBatches.id, AllocationBatches.batch_identifier
        ).limit(20)
        
        result = await test_db.execute(stmt)
        results = result.all()
        return len(results)
    
    async def _benchmark_aggregation_query(self, test_db: AsyncSession):
        """Benchmark function: aggregation query."""
        stmt = select(
            func.count(AllocationBatches.id).label('total_batches'),
            func.sum(AllocationBatches.total_files).label('total_files'),
            func.avg(AllocationBatches.total_files).label('avg_files'),
            AllocationBatches.batch_status
        ).group_by(AllocationBatches.batch_status)
        
        result = await test_db.execute(stmt)
        aggregations = result.all()
        return len(aggregations)
    
    async def _benchmark_master_db_join(self, test_master_db: AsyncSession):
        """Benchmark function: master database join."""
        stmt = select(
            ProjectsRegistry.project_code,
            Clients.client_name, 
            AllocationStrategies.strategy_name
        ).join(
            Clients, ProjectsRegistry.client_id == Clients.id
        ).outerjoin(
            AllocationStrategies, ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id
        )
        
        result = await test_master_db.execute(stmt)
        projects = result.all()
        return len(projects)
    
    @pytest.mark.parametrize("scenario", ["smoke_test", "core_test", "performance_test"])
    @pytest.mark.asyncio
    async def test_batch_query_benchmarks(self, test_db: AsyncSession, benchmark_runner: BenchmarkRunner, scenario: str, setup_benchmark_data):
        """Benchmark batch queries across different data volumes."""
        if setup_benchmark_data != scenario:
            pytest.skip(f"Scenario {scenario} not currently active")
        
        print(f"\n🎯 Running batch query benchmarks for {scenario}")
        
        # Simple query benchmark
        await benchmark_runner.run_benchmark(
            "Simple Batch Query",
            scenario, 
            self._benchmark_simple_batch_query,
            test_db
        )
        
        # Complex join benchmark  
        await benchmark_runner.run_benchmark(
            "Complex Batch Join",
            scenario,
            self._benchmark_complex_batch_join, 
            test_db
        )
        
        # Aggregation benchmark
        await benchmark_runner.run_benchmark(
            "Batch Aggregation",
            scenario,
            self._benchmark_aggregation_query,
            test_db
        )
    
    @pytest.mark.parametrize("scenario", ["smoke_test", "core_test", "performance_test"])
    @pytest.mark.asyncio 
    async def test_master_db_benchmarks(self, test_master_db: AsyncSession, benchmark_runner: BenchmarkRunner, scenario: str, setup_benchmark_data):
        """Benchmark master database queries across different data volumes."""
        if setup_benchmark_data != scenario:
            pytest.skip(f"Scenario {scenario} not currently active")
            
        print(f"\n Running master DB benchmarks for {scenario}")
        
        # Master database join benchmark
        await benchmark_runner.run_benchmark(
            "Master DB Join",
            scenario,
            self._benchmark_master_db_join,
            test_master_db
        )
    
    @pytest.mark.asyncio
    async def test_print_benchmark_summary(self, benchmark_runner: BenchmarkRunner):
        """Print final benchmark summary."""
        benchmark_runner.print_summary()
        
        # Performance assertions
        for benchmark_name, scenarios in benchmark_runner.results.items():
            for scenario_name, metrics in scenarios.items():
                # Basic performance thresholds
                if scenario_name == "smoke_test":
                    assert metrics['avg'] < 0.5, f"{benchmark_name} too slow for smoke test: {metrics['avg']}s"
                elif scenario_name == "core_test":
                    assert metrics['avg'] < 2.0, f"{benchmark_name} too slow for core test: {metrics['avg']}s" 
                elif scenario_name == "performance_test":
                    assert metrics['avg'] < 5.0, f"{benchmark_name} too slow for performance test: {metrics['avg']}s"


@pytest.mark.benchmark
@pytest.mark.integration
class TestSpecificPerformanceScenarios:
    """Specific performance scenarios using bulk data."""
    
    @pytest.mark.asyncio
    async def test_large_file_processing_scenario(self, test_db: AsyncSession):
        """Test performance scenario for processing large numbers of files."""
        print("\nTesting large file processing scenario...")
        
        # Simulate batch processing workflow
        start_time = time.time()
        
        # 1. Get batches ready for processing
        ready_batches_query = select(AllocationBatches).where(
            AllocationBatches.batch_status == BatchStatus.CREATED
        ).limit(10)
        
        batches_result = await test_db.execute(ready_batches_query)
        ready_batches = batches_result.scalars().all()
        
        # 2. For each batch, get its files
        total_files_processed = 0
        for batch in ready_batches:
            files_query = select(FilesRegistry).where(
                FilesRegistry.batch_id == batch.id
            )
            files_result = await test_db.execute(files_query)
            files = files_result.scalars().all()
            total_files_processed += len(files)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"   Processed {total_files_processed} files from {len(ready_batches)} batches")
        print(f"   Processing time: {processing_time:.4f}s")
        print(f"   Files per second: {total_files_processed/processing_time:.1f}")
        
        assert processing_time < 10.0, f"File processing took too long: {processing_time}s"
        assert total_files_processed > 0, "No files were processed"
    
    @pytest.mark.asyncio
    async def test_user_workload_analysis_scenario(self, test_master_db: AsyncSession):
        """Test performance scenario for analyzing user workloads."""
        print("\n Testing user workload analysis scenario...")
        
        start_time = time.time()
        
        # Complex query analyzing user distribution and projects
        user_workload_query = select(
            users.username,
            users.role,
            func.count(ProjectsRegistry.id).label('accessible_projects')
        ).outerjoin(
            # Note: This would need UserProjectAccess table in real scenario
            # Using clients as proxy for demonstration
            ProjectsRegistry, users.active_project == ProjectsRegistry.project_code
        ).group_by(
            users.id, users.username, users.role
        ).having(
            func.count(ProjectsRegistry.id) >= 0
        )
        
        result = await test_master_db.execute(user_workload_query)
        user_workloads = result.all()
        
        end_time = time.time()
        analysis_time = end_time - start_time
        
        print(f"    Analyzed {len(user_workloads)} users in {analysis_time:.4f}s")
        
        # Show sample results
        for workload in user_workloads[:5]:
            print(f"      {workload.username} ({workload.role.value}): {workload.accessible_projects} projects")
        
        assert analysis_time < 3.0, f"User analysis took too long: {analysis_time}s"
        assert len(user_workloads) > 0, "No user workloads analyzed"


# Performance test utilities
def measure_query_performance(query_name: str):
    """Decorator for measuring query performance."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            result = await func(*args, **kwargs)
            end_time = time.time()
            
            print(f" {query_name}: {end_time - start_time:.4f}s")
            return result
        return wrapper
    return decorator


@measure_query_performance("Batch Count Query")
async def get_batch_count(db_session: AsyncSession) -> int:
    """Utility function to get batch count with performance measurement."""
    result = await db_session.execute(select(func.count()).select_from(AllocationBatches))
    return result.scalar()
