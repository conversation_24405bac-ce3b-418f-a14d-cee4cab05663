// components/ImageBrowser.tsx
import React from 'react';
import { FaArrowLeft, FaSync } from 'react-icons/fa';
import { ImageItem } from '../types';

interface ImageBrowserProps {
  images: ImageItem[];
  folder: string;
  page: number;
  totalPages: number;
  loading: boolean;
  onBack: () => void;
  onRefresh: () => void;
  onPageChange: (page: number) => void;
}

export const ImageBrowser: React.FC<ImageBrowserProps> = ({
  images,
  folder,
  page,
  totalPages,
  loading,
  onBack,
  onRefresh,
  onPageChange,
}) => {
  return (
    <div className="image-browser-modal">
      <div className="modal-header d-flex justify-content-between align-items-center mb-3">
        <div className="d-flex align-items-center">
          <button className="btn btn-secondary me-2" onClick={onBack}>
            <FaArrowLeft className="me-1" /> Back
          </button>
          <h5 className="mb-0">Browse Images: {folder}</h5>
        </div>
        <button 
          className="btn btn-outline-primary" 
          onClick={onRefresh}
          disabled={loading}
        >
          <FaSync className={loading ? 'fa-spin' : ''} /> Refresh
        </button>
      </div>

      {loading ? (
        <div className="text-center">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : (
        <>
          <div className="row">
            {images.map((image, index) => (
              <div key={index} className="col-md-3 col-sm-4 col-6 mb-3">
                <div className="card">
                  <img 
                    src={image.path} 
                    alt={image.name}
                    className="card-img-top h-[150px] object-cover cursor-pointer"
                    onClick={() => window.open(image.path, '_blank')}
                  />
                  <div className="card-body p-2">
                    <small className="text-muted" title={image.name}>
                      {image.name.length > 20 
                        ? `${image.name.substring(0, 20)}...` 
                        : image.name
                      }
                    </small>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {totalPages > 1 && (
            <nav>
              <ul className="pagination justify-content-center">
                <li className={`page-item ${page === 1 ? 'disabled' : ''}`}>
                  <button 
                    className="page-link" 
                    onClick={() => onPageChange(page - 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </button>
                </li>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(totalPages, page - 2 + i));
                  return (
                    <li key={pageNum} className={`page-item ${page === pageNum ? 'active' : ''}`}>
                      <button 
                        className="page-link" 
                        onClick={() => onPageChange(pageNum)}
                      >
                        {pageNum}
                      </button>
                    </li>
                  );
                })}
                
                <li className={`page-item ${page === totalPages ? 'disabled' : ''}`}>
                  <button 
                    className="page-link" 
                    onClick={() => onPageChange(page + 1)}
                    disabled={page === totalPages}
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </>
      )}
    </div>
  );
};