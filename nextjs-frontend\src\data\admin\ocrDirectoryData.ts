export interface Dataset {
  id: string;  // Changed from number to string (project_code)
  name: string;
  total_batches: number;
  completed_batches: number;
  progress_percentage: number;
  folder_path?: string;
  label_file?: string;
  is_active?: boolean;  // NEW: Indicates if this is the currently active dataset
}

export interface Directory {
  name: string;
  path: string;
  type: 'directory' | 'file';
}

export const manualDatasets: Dataset[] = [
  { id: "PROJ_MEDICAL_001", name: 'Medical Records', total_batches: 100, completed_batches: 75, progress_percentage: 75, folder_path: '/data/medical' },
  { id: "PROJ_FINANCIAL_001", name: 'Financial Documents', total_batches: 80, completed_batches: 80, progress_percentage: 100, folder_path: '/data/financial' },
  { id: "PROJ_LEGAL_001", name: 'Legal Contracts', total_batches: 120, completed_batches: 40, progress_percentage: 33, folder_path: '/data/legal' },
];

export const verificationDatasets: Dataset[] = [
  { id: "PROJ_MEDICAL_VER_001", name: 'Verified Medical Records', total_batches: 75, completed_batches: 30, progress_percentage: 40, folder_path: '/data/medical_verification', label_file: '/data/labels/medical_labels.json' },
  { id: "PROJ_FINANCIAL_VER_001", name: 'Verified Financial Documents', total_batches: 60, completed_batches: 60, progress_percentage: 100, folder_path: '/data/financial_verification', label_file: '/data/labels/financial_labels.json' },
];

export const mockRootDirectories: Directory[] = [
  { name: 'data', path: '/data', type: 'directory' },
  { name: 'documents', path: '/documents', type: 'directory' },
  { name: 'images', path: '/images', type: 'directory' },
  { name: 'labels', path: '/labels', type: 'directory' },
  { name: 'config.json', path: '/config.json', type: 'file' },
];

export const mockDataDirectories: Directory[] = [
  { name: 'medical', path: '/data/medical', type: 'directory' },
  { name: 'financial', path: '/data/financial', type: 'directory' },
  { name: 'legal', path: '/data/legal', type: 'directory' },
  { name: 'medical_verification', path: '/data/medical_verification', type: 'directory' },
  { name: 'financial_verification', path: '/data/financial_verification', type: 'directory' },
];

export const mockLabelDirectories: Directory[] = [
  { name: 'medical_labels.json', path: '/labels/medical_labels.json', type: 'file' },
  { name: 'financial_labels.json', path: '/labels/financial_labels.json', type: 'file' },
  { name: 'legal_labels.json', path: '/labels/legal_labels.json', type: 'file' },
];

export const getCompletionStatus = (dataset: Dataset) => {
  if (dataset.total_batches === 0) return 'empty';
  if (dataset.completed_batches >= dataset.total_batches) return 'completed';
  return 'in-progress';
};

export const generateMockDirectories = (path: string): Directory[] => {
  if (path === '/') {
    return mockRootDirectories;
  } else if (path === '/data') {
    return mockDataDirectories;
  } else if (path === '/labels') {
    return mockLabelDirectories;
  } else {
    // Generate some mock files for other directories
    const dirs = Array.from({ length: 3 }, (_, i) => ({
      name: `folder_${i + 1}`,
      path: `${path}/folder_${i + 1}`,
      type: 'directory' as const,
    }));
    
    const files = Array.from({ length: 5 }, (_, i) => ({
      name: `file_${i + 1}${path.includes('labels') ? '.json' : path.includes('images') ? '.jpg' : '.txt'}`,
      path: `${path}/file_${i + 1}${path.includes('labels') ? '.json' : path.includes('images') ? '.jpg' : '.txt'}`,
      type: 'file' as const,
    }));
    
    return [...dirs, ...files];
  }
}; 