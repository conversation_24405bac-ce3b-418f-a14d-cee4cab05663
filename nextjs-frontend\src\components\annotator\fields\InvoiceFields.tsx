import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface InvoiceFieldsProps {
  data: Record<string, string>;
  onChange: (name: string, value: string) => void;
}

export default function InvoiceFields({ data, onChange }: InvoiceFieldsProps) {
  return (
    <div className="space-y-6">
      {/* Invoice Number / Invoice Date */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Invoice Number</label>
          <input
            type="text"
            name="Invoice Number"
            value={data['Invoice Number'] || ''}
            onChange={e => onChange('Invoice Number', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Invoice Date</label>
          <DatePicker
            selected={data['Invoice Date'] ? new Date(data['Invoice Date']) : null}
            onChange={date => onChange('Invoice Date', date ? (date as Date).toISOString().split('T')[0] : '')}
            dateFormat="yyyy-MM-dd"
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            placeholderText="Select date"
          />
        </div>
      </div>

      {/* Due Date / Payment Terms */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Due Date</label>
          <DatePicker
            selected={data['Due Date'] ? new Date(data['Due Date']) : null}
            onChange={date => onChange('Due Date', date ? (date as Date).toISOString().split('T')[0] : '')}
            dateFormat="yyyy-MM-dd"
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
            placeholderText="Select date"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Payment Terms</label>
          <input
            type="text"
            name="Payment Terms"
            value={data['Payment Terms'] || ''}
            onChange={e => onChange('Payment Terms', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
      </div>

      {/* Vendor/Seller */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Vendor/Seller</label>
        <input
          type="text"
          name="Vendor/Seller"
          value={data['Vendor/Seller'] || ''}
          onChange={e => onChange('Vendor/Seller', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>

      {/* Customer */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Customer</label>
        <input
          type="text"
          name="Customer"
          value={data['Customer'] || ''}
          onChange={e => onChange('Customer', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>

      {/* Total Amount */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Total Amount</label>
        <input
          type="text"
          name="Total Amount"
          value={data['Total Amount'] || ''}
          onChange={e => onChange('Total Amount', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>
    </div>
  );
} 