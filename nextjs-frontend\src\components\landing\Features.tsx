"use client";
import React from "react";

import { FC  } from "react";
import { motion, Variants } from "framer-motion";
import { Lightbulb, ShieldCheck, Users, FolderOpen } from "lucide-react";

interface Feature {
  id: number;
  icon: JSX.Element;
  title: string;
  description: string;
  iconColor: string;
  shadowColor: string; // Neumorphism shadow tint color
}

const features: Feature[] = [
  {
    id: 1,
    icon: <Lightbulb className="w-10 h-10" />,
    title: "Efficient Annotation",
    description:
      "Accelerate your workflow with fast, accurate, and intuitive data annotation.",
    iconColor: "text-yellow-500",
    shadowColor: "rgba(251,191,36,0.25)", // yellow glow shadow
  },
  {
    id: 2,
    icon: <ShieldCheck className="w-10 h-10" />,
    title: "Auditing Mode",
    description:
      "Ensure quality and compliance with seamless, real-time auditing capabilities.",
    iconColor: "text-green-500",
    shadowColor: "rgba(34,197,94,0.25)", // green glow shadow
  },
  {
    id: 3,
    icon: <Users className="w-10 h-10" />,
    title: "Team Collaboration",
    description:
      "Collaborate with your team in real-time, share annotations, and track progress.",
    iconColor: "text-indigo-500",
    shadowColor: "rgba(99,102,241,0.25)", // indigo glow shadow
  },
  {
    id: 4,
    icon: <FolderOpen className="w-10 h-10" />,
    title: "File Management",
    description: "Organize and manage your data with ease.",
    iconColor: "text-pink-500",
    shadowColor: "rgba(236,72,153,0.25)", // pink glow shadow
  },
];

// Container animation: stagger children
const containerVariants: Variants = {
  hidden: {},
  visible: { transition: { staggerChildren: 0.2 } },
};

// Card animation: fade + slide + subtle float on hover
const cardVariants: Variants = {
  hidden: { opacity: 0, y: 40 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.7, ease: "easeOut" } },
  hover: {
    scale: 1.05,
    y: -5,
    boxShadow: "0 20px 30px rgba(0,0,0,0.1)",
    transition: { duration: 0.3 },
  },
};

const Features: FC = () => {
  return (
    <section
      id="features"
      className="py-24 bg-gradient-to-b from-white to-blue-50 relative"
      aria-labelledby="features-title"
    >
      <div className="container mx-auto px-6 max-w-6xl">
        <motion.h2
          id="features-title"
          className="section-title text-4xl font-extrabold text-[var(--primary-color)] text-center mb-20 select-none"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, ease: "easeOut" }}
        >
          Platform Features
        </motion.h2>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={containerVariants}
        >
          {features.map(
            ({ id, icon, title, description, iconColor, shadowColor }) => (
              <motion.article
                key={id}
                className="feature-card cursor-default select-none rounded-3xl bg-[#f0f0f3] p-8 flex flex-col items-center text-center
                         shadow-neumorph transition-shadow duration-300"
                variants={cardVariants}
                role="region"
                aria-labelledby={`feature-title-${id}`}
                whileHover="hover"
                style={{
                  // Custom neumorphic shadow for each card
                  boxShadow: `
                  8px 8px 15px #c1c1c1,
                  -8px -8px 15px #ffffff
                `,
                }}
              >
                <div
                  aria-hidden="true"
                  className={`flex items-center justify-center w-16 h-16 mb-7 rounded-full bg-[#e0e0e3]`}
                  style={{
                    boxShadow: `
                    inset 4px 4px 6px #bebebe,
                    inset -4px -4px 6px #ffffff,
                    0 0 15px ${shadowColor}
                  `,
                  }}
                >
                  {React.cloneElement(icon, {
                    className: `${iconColor} w-10 h-10`,
                  })}
                </div>

                <h3
                  id={`feature-title-${id}`}
                  className="text-xl font-semibold text-[var(--heading-color)] mb-3 select-text"
                >
                  {title}
                </h3>
                <p className="text-[var(--text-secondary)] text-base leading-relaxed select-text">
                  {description}
                </p>
              </motion.article>
            )
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
