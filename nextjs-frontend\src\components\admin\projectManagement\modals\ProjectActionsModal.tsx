import React from 'react';
import { FaTimes } from 'react-icons/fa';
import { ProjectRegistryResponse, StrategyDetails } from '../types';
import { getStatusColor } from '../utils';
import {
  ProjectActions,
  UserAssignmentSection,
  BatchAllocationSection,
} from '../components';
import {
  UserInfo,
  AssignedUserInfo,
  BatchAllocationInfo,
  AssignmentProgress,
} from '../types';

interface ProjectActionsModalProps {
  project: ProjectRegistryResponse;
  isOpen: boolean;
  onClose: () => void;
  // Strategy
  strategyDetails: StrategyDetails | null;
  loadingStrategy: boolean;
  // User Assignment
  availableAnnotators: UserInfo[];
  availableVerifiers: UserInfo[];
  loadingUsers: boolean;
  selectedAnnotators: number[];
  selectedVerifiers: number[];
  assignedAnnotators: AssignedUserInfo[];
  assignedVerifiers: AssignedUserInfo[];
  loadingAssignedUsers: boolean;
  assignmentLoading: boolean;
  // Batch Allocations
  batchAllocations: BatchAllocationInfo[];
  assignmentProgress: AssignmentProgress | null;
  loadingBatchAllocations: boolean;
  syncingBatchAllocations: boolean;
  onSyncBatchAllocations: () => void;
  // Loading states
  activationLoading: boolean;
  deadlineLoading: boolean;
  // Actions
  onActivateProject: (createBatches: boolean) => void;
  onDeactivateProject: () => void;
  onPauseProject: () => void;
  onCompleteProject: () => void;
  onSetDeadline: () => void;
  onToggleUserSelection: (role: 'annotators' | 'verifiers', userId: number) => void;
  onAssignUsers: (role: 'annotators' | 'verifiers', userIds: number[]) => void;
  onRemoveUser: (role: 'annotators' | 'verifiers', userId: number) => void;
}

export const ProjectActionsModal: React.FC<ProjectActionsModalProps> = ({
  project,
  isOpen,
  onClose,
  strategyDetails,
  loadingStrategy,
  availableAnnotators,
  availableVerifiers,
  loadingUsers,
  selectedAnnotators,
  selectedVerifiers,
  assignedAnnotators,
  assignedVerifiers,
  loadingAssignedUsers,
  assignmentLoading,
  batchAllocations,
  assignmentProgress,
  loadingBatchAllocations,
  syncingBatchAllocations,
  onSyncBatchAllocations,
  activationLoading,
  deadlineLoading,
  onActivateProject,
  onDeactivateProject,
  onPauseProject,
  onCompleteProject,
  onSetDeadline,
  onToggleUserSelection,
  onAssignUsers,
  onRemoveUser,
}) => {
  if (!isOpen || !project) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-5xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                Manage Project: {project.project_name}
              </h2>
              <p className="text-gray-600">
                Project Code: {project.project_code} | Status:{' '}
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.project_status)}`}>
                  {project.project_status}
                </span>
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FaTimes className="text-xl" />
            </button>
          </div>

          {/* Project Actions */}
          <ProjectActions
            project={project}
            strategyDetails={strategyDetails}
            loadingStrategy={loadingStrategy}
            activationLoading={activationLoading}
            deadlineLoading={deadlineLoading}
            totalBatches={batchAllocations.length}
            onActivateProject={onActivateProject}
            onDeactivateProject={onDeactivateProject}
            onPauseProject={onPauseProject}
            onCompleteProject={onCompleteProject}
            onSetDeadline={onSetDeadline}
          />

          {/* User Assignment Section */}
          <UserAssignmentSection
            project={project}
            strategyDetails={strategyDetails}
            availableAnnotators={availableAnnotators}
            assignedAnnotators={assignedAnnotators}
            selectedAnnotators={selectedAnnotators}
            availableVerifiers={availableVerifiers}
            assignedVerifiers={assignedVerifiers}
            selectedVerifiers={selectedVerifiers}
            loadingUsers={loadingUsers}
            loadingAssignedUsers={loadingAssignedUsers}
            assignmentLoading={assignmentLoading}
            onToggleUserSelection={onToggleUserSelection}
            onAssignUsers={onAssignUsers}
            onRemoveUser={onRemoveUser}
          />

          {/* Batch Allocation Section */}
          <BatchAllocationSection
            strategyDetails={strategyDetails}
            batchAllocations={batchAllocations}
            assignmentProgress={assignmentProgress}
            loadingBatchAllocations={loadingBatchAllocations}
            syncingBatchAllocations={syncingBatchAllocations}
            onSyncBatchAllocations={onSyncBatchAllocations}
          />
        </div>
      </div>
    </div>
  );
};
