"""
Routes for data export functionality.
"""

import logging
import json
import csv
import io
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query # type: ignore
from fastapi.responses import StreamingResponse # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from dependencies.auth import get_current_user
from core.session_manager import get_project_db_session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/export", tags=["export"])

@router.get("/csv/{project_code}")
async def export_project_csv(
    project_code: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    master_db: AsyncSession = Depends(get_master_db_session)
):
    """
    Export project annotation data as CSV.
    
    This endpoint:
    1. Checks the project's allocation strategy to determine if verification is required
    2. If no verification: exports annotator_1_review data from file_allocations
    3. If verification required: exports verifier_review data from file_allocations
    4. Returns CSV file with file metadata and form data
    
    Args:
        project_code: Project code to export data for
        
    Returns:
        StreamingResponse: CSV file download
    """
    try:
        logger.info(f"Starting CSV export for project {project_code} by user {current_user.get('username')}")
        
        # 1. Get project information
        project_result = await master_db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
        )
        project = project_result.scalar_one_or_none()
        
        if not project:
            raise HTTPException(status_code=404, detail=f"Project {project_code} not found")
        
        if not project.allocation_strategy_id:
            raise HTTPException(status_code=400, detail=f"Project {project_code} has no allocation strategy configured")
        
        # 2. Get allocation strategy to determine verification requirement
        strategy_result = await master_db.execute(
            select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
        )
        strategy = strategy_result.scalar_one_or_none()
        
        if not strategy:
            raise HTTPException(status_code=400, detail=f"Allocation strategy not found for project {project_code}")
        
        requires_verification = strategy.requires_verification
        logger.info(f"Project {project_code} requires verification: {requires_verification}")
        
        # 3. Get project database session
        async with get_project_db_session(project_code) as session:
            # 4. Query appropriate review data based on verification requirement
            if requires_verification:
                # Export verifier_review data
                query = text("""
                    SELECT 
                        fa.file_id,
                        fr.original_filename,
                        fr.file_identifier as path,
                        fa.verifier_review
                    FROM file_allocations fa
                    JOIN files_registry fr ON fa.file_id = fr.id
                    WHERE fa.verifier_review IS NOT NULL
                    ORDER BY fa.file_id
                """)
                review_column = "verifier_review"
            else:
                # Export annotator_1_review data
                query = text("""
                    SELECT 
                        fa.file_id,
                        fr.original_filename,
                        fr.file_identifier as path,
                        fa.annotator_1_review
                    FROM file_allocations fa
                    JOIN files_registry fr ON fa.file_id = fr.id
                    WHERE fa.annotator_1_review IS NOT NULL
                    ORDER BY fa.file_id
                """)
                review_column = "annotator_1_review"
            
            result = await session.execute(query)
            rows = result.fetchall()
            
            if not rows:
                raise HTTPException(status_code=404, detail=f"No review data found for project {project_code}")
            
            logger.info(f"Found {len(rows)} files with review data for project {project_code}")
            
            # 5. Process data and create CSV
            csv_data = []
            all_form_fields = set()
            
            # First pass: collect all form fields to create consistent headers
            for row in rows:
                review_data_raw = row[3]  # The review data column
                if review_data_raw:
                    try:
                        # Handle both dict and string cases
                        if isinstance(review_data_raw, dict):
                            review_data = review_data_raw
                        else:
                            review_data = json.loads(review_data_raw)
                        
                        if requires_verification:
                            # For verifier data, check both new flat structure and old field_decisions structure
                            form_data = review_data.get("form_data", {})
                            field_decisions = review_data.get("field_decisions", {})
                            
                            if form_data:
                                # New flat structure: form_data contains question-answer pairs
                                all_form_fields.update(form_data.keys())
                            elif field_decisions:
                                # Old structure: field_decisions keys
                                all_form_fields.update(field_decisions.keys())
                        else:
                            # For annotator data, extract form_data keys
                            form_data = review_data.get("form_data", {})
                            all_form_fields.update(form_data.keys())
                            
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Invalid review data for file {row[0]}: {str(e)}")
                        continue
            
            # Sort form fields for consistent column ordering
            sorted_form_fields = sorted(all_form_fields)
            
            # Create CSV headers
            headers = ["id", "original_filename", "path"] + sorted_form_fields
            
            # Second pass: extract data for CSV
            for row in rows:
                file_id = row[0]
                original_filename = row[1]
                path = row[2]
                review_data_raw = row[3]
                
                # Start with basic file info
                csv_row = {
                    "id": file_id,
                    "original_filename": original_filename,
                    "path": path
                }
                
                # Initialize all form fields with empty values
                for field in sorted_form_fields:
                    csv_row[field] = ""
                
                # Fill in actual form data
                if review_data_raw:
                    try:
                        # Handle both dict and string cases
                        if isinstance(review_data_raw, dict):
                            review_data = review_data_raw
                        else:
                            review_data = json.loads(review_data_raw)
                        
                        if requires_verification:
                            # For verifier data, check both new flat structure and old field_decisions structure
                            form_data = review_data.get("form_data", {})
                            field_decisions = review_data.get("field_decisions", {})
                            
                            if form_data:
                                # New flat structure: form_data contains question-answer pairs
                                for field, value in form_data.items():
                                    if field in sorted_form_fields:
                                        # Handle list values by joining them
                                        if isinstance(value, list):
                                            csv_row[field] = ", ".join(str(v) for v in value)
                                        else:
                                            csv_row[field] = str(value) if value is not None else ""
                            elif field_decisions:
                                # Old structure: field_decisions with final_value
                                for field, decision_data in field_decisions.items():
                                    if field in sorted_form_fields:
                                        final_value = decision_data.get("final_value", "")
                                        # Handle list values by joining them
                                        if isinstance(final_value, list):
                                            csv_row[field] = ", ".join(str(v) for v in final_value)
                                        else:
                                            csv_row[field] = str(final_value) if final_value is not None else ""
                        else:
                            # For annotator data, extract values from form_data
                            form_data = review_data.get("form_data", {})
                            for field, value in form_data.items():
                                if field in sorted_form_fields:
                                    # Handle list values by joining them
                                    if isinstance(value, list):
                                        csv_row[field] = ", ".join(str(v) for v in value)
                                    else:
                                        csv_row[field] = str(value) if value is not None else ""
                                        
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Invalid review data for file {file_id}: {str(e)}")
                
                csv_data.append(csv_row)
            
            # 6. Generate CSV content
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=headers)
            writer.writeheader()
            writer.writerows(csv_data)
            csv_content = output.getvalue()
            output.close()
            
            # 7. Create filename
            data_type = "verifier_reviews" if requires_verification else "annotator_reviews"
            filename = f"{project_code}_{data_type}_export.csv"
            
            logger.info(f"Successfully generated CSV export for project {project_code} with {len(csv_data)} rows")
            
            # 8. Return CSV as downloadable file
            def iter_csv():
                yield csv_content.encode('utf-8')
            
            return StreamingResponse(
                iter_csv(),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={filename}"}
            )
            
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting CSV for project {project_code}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to export CSV: {str(e)}")
