'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FaDatabase, FaUser, FaKey, FaChevronDown, FaSignOutAlt } from 'react-icons/fa';
// import LoginModal from '@/components/auth/LoginModal';
import PasswordModal from '@/components/auth/PasswordModal';
import { useAuth } from '@/contexts/AuthContext';
import { ProjectProvider, useProject } from '@/contexts/ProjectContext';
import RoleGuard from '@/components/auth/RoleGuard';

import { API_BASE_URL } from "@/lib/api";


function AnnotatorLayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { setProjectCode } = useProject();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [projectCode, setProjectCodeLocal] = useState<string | null>(null);
  const openPasswordModal = () => setIsPasswordModalOpen(true);
  const closePasswordModal = () => setIsPasswordModalOpen(false);

  const displayName = user?.full_name || user?.username || '';

  // Fetch project code
  useEffect(() => {
    const fetchProjectCode = async () => {
      try {
        console.log('Fetching project status...');
        const response = await fetch(`${API_BASE_URL}/annotator/dashboard`, {
          credentials: 'include'
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('Project status data:', data);
          console.log('Project code from API:', data.project_code);
          console.log('Debug info:', data.debug_info);
          setProjectCodeLocal(data.project_code);
          setProjectCode(data.project_code);
        } else {
          console.error('Failed to fetch project status:', response.status, response.statusText);
          const errorText = await response.text();
          console.error('Error response:', errorText);
        }
      } catch (error) {
        console.error('Error fetching project code:', error);
      }
    };

    fetchProjectCode();
  }, [setProjectCode]);

  const handleLogout = async () => {
    await logout();
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <RoleGuard allowedRoles={['annotator']}>
      <div className="flex flex-col h-screen overflow-auto">
        {/* Header */}
        <header className="bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-md">
          <div className="max-w-[1400px] mx-auto flex items-center justify-between px-8 py-3">
            <div className="flex items-center space-x-4">
              <FaDatabase className="text-2xl" />
              <span className="font-bold text-2xl">DADP</span>
              {projectCode && (
                <div className="bg-white/20 text-white px-3 py-1 rounded-lg font-medium">
                  {projectCode}
                </div>
              )}
            </div>
            <div ref={dropdownRef} className="relative flex items-center space-x-4">
              <FaUser />
              <button
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="font-medium flex items-center space-x-1 text-white focus:outline-none"
              >
                <span>{displayName || 'User'}</span>
                <FaChevronDown className={`transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              {isDropdownOpen && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white text-black rounded-md shadow-lg z-50 overflow-hidden">
                  <div className="px-4 py-1">{displayName}</div>
                  <hr className="border-gray-600 my-1" />
                  <button
                    onClick={() => { openPasswordModal(); setIsDropdownOpen(false); }}
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 rounded-t-md"
                  >
                    <FaKey className="inline mr-2" /> Change Password
                  </button>
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 rounded-b-md"
                  >
                    <FaSignOutAlt className="inline mr-2" /> Logout
                  </button>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 bg-gray-100 overflow-auto">{children}</main>
        {/* Change Password Modal */}
        <PasswordModal isOpen={isPasswordModalOpen} onClose={closePasswordModal} />
      </div>
    </RoleGuard>
  );
}

export default function AnnotatorLayout({ children }: { children: React.ReactNode }) {
  return (
    <ProjectProvider>
      <AnnotatorLayoutContent>{children}</AnnotatorLayoutContent>
    </ProjectProvider>
  );
} 