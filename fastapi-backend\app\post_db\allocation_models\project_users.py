from sqlalchemy import Column, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship
from ..project_base import ProjectBase

class ProjectUsers(ProjectBase):
    """
    Manages users in a project.
    """
    __tablename__ = "project_users"
    
    __table_args__ = (
        UniqueConstraint('user_id', 'username', name='_user_id_username_uc'),
    )

    # Primary Identity & Relationships
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique user identifier within this project database")
    user_id = Column(Integer,  nullable=False, index=True,
                    comment="Reference to users.id in master database (optional for now)")
    username = Column(String(255), nullable=False, index=True,
                     comment="Username for quick reference")
    role = Column(String(255), nullable=False, index=True,
                     comment="Role for quick reference")
    
    # Batch Tracking
    current_batch = Column(Integer, nullable=True, index=True,
                          comment="ID of the current batch the user is working on")
    completed_batches = Column(ARRAY(Integer), nullable=True, default=list,
                              comment="List of batch IDs that the user has completed")
    
    # Relationships
    allocations = relationship("UserAllocations", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ProjectUsers(id={self.id}, user_id={self.user_id}, username={self.username}, role={self.role})>"