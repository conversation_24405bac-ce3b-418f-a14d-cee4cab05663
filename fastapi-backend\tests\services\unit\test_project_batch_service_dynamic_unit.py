"""
Comprehensive unit tests for DynamicProjectBatchService.
Tests dynamic schema handling, allocation strategies, and project batch operations.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
import json

from app.services.project_batch_service_dynamic import DynamicProjectBatchService
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

class TestDynamicProjectBatchServiceUnit:
    """Unit tests for DynamicProjectBatchService with mocked dependencies."""
    
    @pytest.fixture
    def dynamic_service(self):
        """DynamicProjectBatchService instance for testing."""
        return DynamicProjectBatchService()
    
    @pytest.fixture
    def mock_allocation_strategies(self):
        """Mock allocation strategies for testing."""
        return {
            'single_annotator': {
                'id': 1,
                'strategy_type': StrategyType.SEQUENTIAL,
                'num_annotators': 1,
                'requires_verification': False,
                'configuration': {'batch_size': 20}
            },
            'multi_annotator': {
                'id': 2, 
                'strategy_type': StrategyType.PARALLEL,
                'num_annotators': 3,
                'requires_verification': True,
                'configuration': {'batch_size': 15, 'overlap_percentage': 10}
            },
            'complex_strategy': {
                'id': 3,
                'strategy_type': StrategyType.SEQUENTIAL,
                'num_annotators': 5,
                'requires_verification': True,
                'configuration': {
                    'batch_size': 10,
                    'quality_threshold': 0.95,
                    'requires_consensus': True
                }
            }
        }
    
    @pytest.fixture
    def mock_project_schemas(self):
        """Mock dynamic schema configurations."""
        return {
            'schema_1_annotator': {
                'allocation_batches': ['id', 'batch_identifier', 'total_files', 'annotator_1', 'status'],
                'user_allocations': ['id', 'user_id', 'batch_id', 'allocation_role', 'assigned_at']
            },
            'schema_3_annotators': {
                'allocation_batches': [
                    'id', 'batch_identifier', 'total_files', 
                    'annotator_1', 'annotator_2', 'annotator_3', 'verifier', 'status'
                ],
                'user_allocations': ['id', 'user_id', 'batch_id', 'allocation_role', 'assigned_at']
            },
            'schema_5_annotators': {
                'allocation_batches': [
                    'id', 'batch_identifier', 'total_files',
                    'annotator_1', 'annotator_2', 'annotator_3', 'annotator_4', 'annotator_5',
                    'verifier', 'auditor', 'status'
                ],
                'user_allocations': ['id', 'user_id', 'batch_id', 'allocation_role', 'assigned_at']
            }
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_success(self, dynamic_service, mock_allocation_strategies):
        """Test successful project strategy retrieval."""
        project_code = 'DYNAMIC_001'
        
        # Mock project query
        mock_project = MagicMock()
        mock_project.project_code = project_code
        mock_project.allocation_strategy_id = 2
        
        mock_project_result = AsyncMock()
        mock_project_result.scalar_one_or_none.return_value = mock_project
        
        # Mock strategy query
        mock_strategy = MagicMock()
        strategy_data = mock_allocation_strategies['multi_annotator']
        for key, value in strategy_data.items():
            setattr(mock_strategy, key, value)
        
        mock_strategy_result = AsyncMock()
        mock_strategy_result.scalar_one_or_none.return_value = mock_strategy
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.side_effect = [mock_project_result, mock_strategy_result]
            
            result = await dynamic_service.get_project_strategy(project_code)
            
            assert result is not None
            assert result.num_annotators == 3
            assert result.requires_verification is True
            assert result.strategy_type == StrategyType.PARALLEL

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_no_project(self, dynamic_service):
        """Test strategy retrieval for non-existent project."""
        project_code = 'NONEXISTENT_001'
        
        # Mock project not found
        mock_project_result = AsyncMock()
        mock_project_result.scalar_one_or_none.return_value = None
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.return_value = mock_project_result
            
            result = await dynamic_service.get_project_strategy(project_code)
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_strategy_no_strategy(self, dynamic_service):
        """Test strategy retrieval when project has no allocation strategy."""
        project_code = 'NO_STRATEGY_001'
        
        # Mock project without strategy
        mock_project = MagicMock()
        mock_project.project_code = project_code
        mock_project.allocation_strategy_id = None
        
        mock_project_result = AsyncMock()
        mock_project_result.scalar_one_or_none.return_value = mock_project
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            mock_session.execute.return_value = mock_project_result
            
            result = await dynamic_service.get_project_strategy(project_code)
            
            assert result is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_dynamic_schema_structure_single_annotator(self, dynamic_service, 
                                                                 mock_allocation_strategies,
                                                                 mock_project_schemas):
        """Test dynamic schema structure for single annotator strategy."""
        strategy = mock_allocation_strategies['single_annotator']
        
        with patch.object(dynamic_service, '_generate_schema_columns') as mock_generate:
            mock_generate.return_value = mock_project_schemas['schema_1_annotator']
            
            schema = await dynamic_service.get_dynamic_schema_structure(strategy)
            
            assert 'allocation_batches' in schema
            assert 'annotator_1' in schema['allocation_batches']
            assert 'verifier' not in schema['allocation_batches']  # No verification required
            assert mock_generate.called

    @pytest.mark.unit 
    @pytest.mark.asyncio
    async def test_get_dynamic_schema_structure_multi_annotator(self, dynamic_service,
                                                                mock_allocation_strategies,
                                                                mock_project_schemas):
        """Test dynamic schema structure for multi annotator strategy."""
        strategy = mock_allocation_strategies['multi_annotator']
        
        with patch.object(dynamic_service, '_generate_schema_columns') as mock_generate:
            mock_generate.return_value = mock_project_schemas['schema_3_annotators']
            
            schema = await dynamic_service.get_dynamic_schema_structure(strategy)
            
            assert 'allocation_batches' in schema
            assert 'annotator_1' in schema['allocation_batches']
            assert 'annotator_2' in schema['allocation_batches']
            assert 'annotator_3' in schema['allocation_batches']
            assert 'verifier' in schema['allocation_batches']  # Verification required

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_dynamic_batch_single_annotator(self, dynamic_service, mock_allocation_strategies):
        """Test dynamic batch creation for single annotator strategy."""
        project_code = 'SINGLE_BATCH_001'
        strategy = mock_allocation_strategies['single_annotator']
        
        batch_data = {
            'batch_identifier': 'DYNAMIC_BATCH_001',
            'total_files': 20,
            'file_list': [f'file_{i}.jpg' for i in range(1, 21)],
            'metadata': {'content_type': 'image'}
        }
        
        with patch.object(dynamic_service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = MagicMock(**strategy)
            
            with patch.object(dynamic_service, '_create_batch_with_dynamic_columns') as mock_create:
                mock_create.return_value = {
                    'success': True,
                    'batch_id': 'DYNAMIC_BATCH_001',
                    'columns_created': ['annotator_1']
                }
                
                result = await dynamic_service.create_dynamic_batch(project_code, batch_data)
                
                assert result['success'] is True
                assert 'columns_created' in result
                assert 'annotator_1' in result['columns_created']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_dynamic_batch_multi_annotator(self, dynamic_service, mock_allocation_strategies):
        """Test dynamic batch creation for multi annotator strategy."""
        project_code = 'MULTI_BATCH_001'  
        strategy = mock_allocation_strategies['multi_annotator']
        
        batch_data = {
            'batch_identifier': 'DYNAMIC_BATCH_002',
            'total_files': 15,
            'file_list': [f'video_{i}.mp4' for i in range(1, 16)],
            'annotation_count': 3
        }
        
        with patch.object(dynamic_service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = MagicMock(**strategy)
            
            with patch.object(dynamic_service, '_create_batch_with_dynamic_columns') as mock_create:
                mock_create.return_value = {
                    'success': True,
                    'batch_id': 'DYNAMIC_BATCH_002',
                    'columns_created': ['annotator_1', 'annotator_2', 'annotator_3', 'verifier']
                }
                
                result = await dynamic_service.create_dynamic_batch(project_code, batch_data)
                
                assert result['success'] is True
                assert len(result['columns_created']) == 4  # 3 annotators + 1 verifier

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_user_to_dynamic_batch_annotator(self, dynamic_service):
        """Test assigning user to dynamic batch as annotator."""
        project_code = 'ASSIGN_001'
        batch_identifier = 'BATCH_ASSIGN_001'
        user_id = 123
        role = 'annotator'
        
        with patch.object(dynamic_service, '_get_next_available_annotator_column') as mock_get_column:
            mock_get_column.return_value = 'annotator_2'  # Second annotator slot
            
            with patch.object(dynamic_service, '_update_batch_assignment') as mock_update:
                mock_update.return_value = {
                    'success': True,
                    'assigned_column': 'annotator_2',
                    'user_id': user_id
                }
                
                result = await dynamic_service.assign_user_to_dynamic_batch(
                    project_code, batch_identifier, user_id, role
                )
                
                assert result['success'] is True
                assert result['assigned_column'] == 'annotator_2'
                assert result['user_id'] == user_id

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_user_to_dynamic_batch_verifier(self, dynamic_service):
        """Test assigning user to dynamic batch as verifier."""
        project_code = 'ASSIGN_VER_001'
        batch_identifier = 'BATCH_VERIFY_001'
        user_id = 456
        role = 'verifier'
        
        with patch.object(dynamic_service, '_check_batch_ready_for_verification') as mock_check:
            mock_check.return_value = True  # All annotations complete
            
            with patch.object(dynamic_service, '_update_batch_assignment') as mock_update:
                mock_update.return_value = {
                    'success': True,
                    'assigned_column': 'verifier',
                    'user_id': user_id
                }
                
                result = await dynamic_service.assign_user_to_dynamic_batch(
                    project_code, batch_identifier, user_id, role
                )
                
                assert result['success'] is True
                assert result['assigned_column'] == 'verifier'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_user_batch_full(self, dynamic_service):
        """Test assigning user to dynamic batch when all slots are full."""
        project_code = 'FULL_BATCH_001'
        batch_identifier = 'BATCH_FULL_001'
        user_id = 789
        role = 'annotator'
        
        with patch.object(dynamic_service, '_get_next_available_annotator_column') as mock_get_column:
            mock_get_column.return_value = None  # No available slots
            
            result = await dynamic_service.assign_user_to_dynamic_batch(
                project_code, batch_identifier, user_id, role
            )
            
            assert result['success'] is False
            assert 'no available slots' in result['error'].lower()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_assignment_status_comprehensive(self, dynamic_service):
        """Test comprehensive batch assignment status retrieval."""
        project_code = 'STATUS_001'
        batch_identifier = 'BATCH_STATUS_001'
        
        mock_batch_status = {
            'batch_identifier': batch_identifier,
            'total_files': 25,
            'annotator_1': 101,  # User ID
            'annotator_2': 102,
            'annotator_3': None,  # Not assigned
            'verifier': None,     # Not assigned
            'annotation_progress': {
                'annotator_1': {'completed': 20, 'assigned': 25},
                'annotator_2': {'completed': 15, 'assigned': 25}
            },
            'overall_completion': 70.0  # (20+15)/(25+25) * 100
        }
        
        with patch.object(dynamic_service, '_query_batch_status') as mock_query:
            mock_query.return_value = mock_batch_status
            
            result = await dynamic_service.get_batch_assignment_status(project_code, batch_identifier)
            
            assert result['batch_identifier'] == batch_identifier
            assert result['overall_completion'] == 70.0
            assert result['annotator_1'] == 101
            assert result['annotator_3'] is None

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_update_dynamic_schema_add_annotators(self, dynamic_service):
        """Test updating dynamic schema to add more annotators."""
        project_code = 'SCHEMA_UPDATE_001'
        current_annotators = 2
        new_annotators = 4
        
        with patch.object(dynamic_service, '_get_current_schema_info') as mock_get_info:
            mock_get_info.return_value = {
                'current_annotators': current_annotators,
                'has_verifier': True,
                'has_auditor': False
            }
            
            with patch.object(dynamic_service, '_add_annotator_columns') as mock_add_columns:
                mock_add_columns.return_value = {
                    'success': True,
                    'columns_added': ['annotator_3', 'annotator_4'],
                    'schema_updated': True
                }
                
                result = await dynamic_service.update_dynamic_schema(
                    project_code, new_annotators, add_verifier=True
                )
                
                assert result['success'] is True
                assert len(result['columns_added']) == 2  # Added 2 new annotator columns

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_schema_consistency_valid(self, dynamic_service):
        """Test schema consistency validation with valid configuration."""
        project_code = 'VALID_SCHEMA_001'
        
        mock_schema_info = {
            'allocation_batches_columns': ['annotator_1', 'annotator_2', 'verifier'],
            'user_allocations_structure': 'consistent',
            'data_integrity': 'valid'
        }
        
        with patch.object(dynamic_service, '_analyze_schema_structure') as mock_analyze:
            mock_analyze.return_value = mock_schema_info
            
            with patch.object(dynamic_service, '_validate_data_integrity') as mock_validate:
                mock_validate.return_value = {'valid': True, 'issues': []}
                
                result = await dynamic_service.validate_schema_consistency(project_code)
                
                assert result['valid'] is True
                assert len(result['issues']) == 0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_schema_consistency_issues(self, dynamic_service):
        """Test schema consistency validation with issues."""
        project_code = 'INVALID_SCHEMA_001'
        
        with patch.object(dynamic_service, '_analyze_schema_structure') as mock_analyze:
            mock_analyze.return_value = {
                'allocation_batches_columns': ['annotator_1'],  # Missing annotator_2
                'user_allocations_structure': 'inconsistent'
            }
            
            with patch.object(dynamic_service, '_validate_data_integrity') as mock_validate:
                mock_validate.return_value = {
                    'valid': False,
                    'issues': [
                        'Missing annotator_2 column in allocation_batches',
                        'User allocation records don\'t match batch assignments'
                    ]
                }
                
                result = await dynamic_service.validate_schema_consistency(project_code)
                
                assert result['valid'] is False
                assert len(result['issues']) == 2

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_performance_complex_schema_operations(self, dynamic_service, performance_monitor,
                                                         service_performance_data):
        """Test performance with complex schema operations."""
        project_code = 'PERF_COMPLEX_001'
        
        # Mock complex strategy with many annotators
        complex_strategy = {
            'num_annotators': 10,
            'requires_verification': True,
            'requires_audit': True,
            'configuration': {'batch_size': 5}
        }
        
        with patch.object(dynamic_service, 'get_project_strategy') as mock_get_strategy:
            mock_get_strategy.return_value = MagicMock(**complex_strategy)
            
            with patch.object(dynamic_service, '_create_batch_with_dynamic_columns') as mock_create:
                performance_monitor.start()
                
                mock_create.return_value = {
                    'success': True,
                    'batch_id': 'PERF_BATCH_001',
                    'columns_created': [f'annotator_{i}' for i in range(1, 11)] + ['verifier', 'auditor']
                }
                
                batch_data = {
                    'batch_identifier': 'PERF_BATCH_001',
                    'total_files': 100,
                    'file_list': [f'file_{i}.jpg' for i in range(100)]
                }
                
                result = await dynamic_service.create_dynamic_batch(project_code, batch_data)
                
                performance_monitor.stop()
                
                execution_time = performance_monitor.get_execution_time()
                # Complex schema operations should still be reasonable fast
                assert execution_time < 1.0, f"Complex schema operation took {execution_time}s"
                assert result['success'] is True

    @pytest.mark.unit
    def test_memory_usage_large_schema(self, dynamic_service, service_performance_data):
        """Test memory usage with large schema configurations."""
        import sys
        
        # Simulate large schema with many columns
        large_schema = {}
        
        # Generate allocation_batches table with many annotator columns
        batch_columns = ['id', 'batch_identifier', 'total_files', 'status']
        batch_columns.extend([f'annotator_{i}' for i in range(1, 101)])  # 100 annotators
        batch_columns.extend(['verifier', 'auditor'])
        large_schema['allocation_batches'] = batch_columns
        
        # Generate user_allocations with many relationships
        allocation_columns = ['id', 'user_id', 'batch_id', 'allocation_role', 'assigned_at']
        large_schema['user_allocations'] = allocation_columns
        
        initial_size = sys.getsizeof(large_schema)
        
        # Simulate schema processing
        processed_schema = {}
        for table, columns in large_schema.items():
            processed_schema[table] = {
                'column_count': len(columns),
                'column_definitions': {col: 'INTEGER' if 'id' in col else 'VARCHAR' for col in columns},
                'indexes': [col for col in columns if 'id' in col or col == 'batch_identifier']
            }
        
        final_size = sys.getsizeof(processed_schema)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['batch_processing']
        assert memory_increase < max_memory, f"Schema memory usage {memory_increase}MB exceeds limit"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_schema_modifications(self, dynamic_service):
        """Test concurrent schema modifications on different projects."""
        import asyncio
        
        project_codes = ['CONCURRENT_SCHEMA_001', 'CONCURRENT_SCHEMA_002', 'CONCURRENT_SCHEMA_003']
        
        with patch.object(dynamic_service, '_get_current_schema_info') as mock_get_info:
            mock_get_info.return_value = {'current_annotators': 1, 'has_verifier': False}
            
            with patch.object(dynamic_service, '_add_annotator_columns') as mock_add_columns:
                mock_add_columns.return_value = {
                    'success': True,
                    'columns_added': ['annotator_2', 'annotator_3'],
                    'schema_updated': True
                }
                
                # Create concurrent schema update tasks
                tasks = []
                for project_code in project_codes:
                    task = dynamic_service.update_dynamic_schema(project_code, 3, add_verifier=True)
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # All schema updates should complete successfully
                exceptions = [r for r in results if isinstance(r, Exception)]
                assert len(exceptions) == 0
                
                successful_results = [r for r in results if r.get('success', False)]
                assert len(successful_results) == len(project_codes)
