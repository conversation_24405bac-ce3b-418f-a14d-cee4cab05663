import io
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from ..model_endpoints.image_caption import (
    get_image_caption_service,
    ImageCaptionService
)
from services.ai_processing_service import get_ai_processing_service, AIProcessingService

router = APIRouter()
logger = logging.getLogger(__name__)
@router.post("/batch/project", response_model=Dict[str, Any])
async def batch_caption_project_folder(
    project_code: str = Form(...),
    model_name: str = Form(...),
    custom_prompt: Optional[str] = Form(None),
    ai_service: AIProcessingService = Depends(get_ai_processing_service),
    service: ImageCaptionService = Depends(get_image_caption_service),
    db: AsyncSession = Depends(get_master_db_session)
) -> Dict[str, Any]:
    try:
        project = await _get_project_by_code(db, project_code)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project '{project_code}' not found or inactive"
            )
        if model_name and model_name.startswith("AI-Processing/"):
            model_name = model_name[len("AI-Processing/"):]
            
        folder_path = project.folder_path or ""
            
        # Use empty file identifiers list - let the AI service handle file discovery
        file_identifiers = []
        
        # Use the new integrated AI processing service
        result = await ai_service.process_files_for_project(
            project_code=project_code,
            file_identifiers=file_identifiers,
            model_name=model_name,
            processing_type="caption",
            user_prompt=custom_prompt,
            system_prompt="Generate a descriptive caption for this image."
        )
        
        
        # Add project information for backward compatibility
        result["project_name"] = project.project_name
        result["folder_path"] = folder_path
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error processing project folder: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process project folder: {str(e)}"
        )


async def _get_project_by_code(db: AsyncSession, project_code: str) -> Optional[ProjectsRegistry]:

    stmt = select(ProjectsRegistry).where(
        ProjectsRegistry.project_code == project_code,
        ProjectsRegistry.project_status == 'active'
    )
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

    