// steps/DataConnectorSetup.tsx
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aCheckCircle, FaArrowRight, FaArrowLeft, FaKey, FaRedo, FaDatabase, FaFileAlt } from 'react-icons/fa';
import { Client, ConnectorStatus, NasFormData, DriveFormData, MinIOFormData } from '../types';
import { CSVUploadComponent } from './CSVUploadComponent';

interface DataConnectorSetupProps {
  selectedClient: Client | null;
  connectorStatus: ConnectorStatus;
  nasForm: NasFormData;
  setNasForm: React.Dispatch<React.SetStateAction<NasFormData>>;
  driveForm: DriveFormData;
  setDriveForm: React.Dispatch<React.SetStateAction<DriveFormData>>;
  minioForm: MinIOFormData;
  setMinioForm: React.Dispatch<React.SetStateAction<MinIOFormData>>;
  connectingNas: boolean;
  connectingDrive: boolean;
  connectingMinio: boolean;
  onConnectNas: (projectCode: string, overrideCredentials?: any) => Promise<{ success: boolean }>;
  onConnectDrive: (projectCode: string) => Promise<{ success: boolean }>;
  onConnectMinio: (projectCode: string) => Promise<{ success: boolean }>;
  onGoToStep: (step: number) => void;
  markStepCompleted: (stepId: number) => void;
}

export const DataConnectorSetup: React.FC<DataConnectorSetupProps> = ({
  selectedClient,
  connectorStatus,
  nasForm,
  setNasForm,
  driveForm,
  setDriveForm,
  minioForm,
  setMinioForm,
  connectingNas,
  connectingDrive,
  connectingMinio,
  onConnectNas,
  onConnectDrive,
  onConnectMinio,
  onGoToStep,
  markStepCompleted,
}) => {
  const [existingNasCredentials, setExistingNasCredentials] = useState<any>(null);
  const [useExistingCredentials, setUseExistingCredentials] = useState(true);
  const [projectType, setProjectType] = useState<string>('');
  const [csvUploadData, setCsvUploadData] = useState<{ fileName: string; filePath: string; fileSize: number; csvMetadata?: any } | null>(null);
  const [csvUploadError, setCsvUploadError] = useState<string>('');

  // Helper function to get current project code from localStorage
  const getCurrentProjectCode = (): string | null => {
    try {
      const projectData = localStorage.getItem('currentProject');
      if (projectData) {
        const project = JSON.parse(projectData);
        return project.project_code || null;
      }
    } catch (error) {
      console.error('Error getting project code from localStorage:', error);
    }
    return null;
  };

  // Helper function to get current project data
  const getCurrentProjectData = () => {
    try {
      const projectData = localStorage.getItem('currentProject');
      if (projectData) {
        return JSON.parse(projectData);
      }
    } catch (error) {
      console.error('Error getting project data from localStorage:', error);
    }
    return null;
  };

  // Load existing credentials and project type from localStorage on component mount
  useEffect(() => {
    try {
      const projectData = localStorage.getItem('currentProject');
      if (projectData) {
        const project = JSON.parse(projectData);
        
        // Set project type
        setProjectType(project.project_type || '');
        
        if (project.credentials && project.connection_type === 'NAS-FTP') {
          setExistingNasCredentials(project.credentials);
          console.log('Found existing NAS credentials:', project.credentials);

          // Pre-populate the form with existing credentials
          setNasForm({
            nasType: project.credentials.nas_type || 'ftp',
            url: project.credentials.nas_url || '',
            username: project.credentials.nas_username || '',
            password: project.credentials.nas_password || '',
          });
        }
      }
    } catch (error) {
      console.error('Error loading existing credentials:', error);
    }
  }, [setNasForm]);

  // Handler for continuing to next step after connection is established
  const handleContinueToDatasetSelection = () => {
    markStepCompleted(2); // Mark step 2 as completed
    onGoToStep(3); // Navigate to step 3
  };

  // Handler for using existing credentials
  const handleUseExistingCredentials = async () => {
    const projectCode = getCurrentProjectCode();
    if (existingNasCredentials && projectCode) {
      // Prepare credentials in the format expected by the API
      const credentials = {
        nasType: existingNasCredentials.nas_type || 'ftp',
        url: existingNasCredentials.nas_url || '',
        username: existingNasCredentials.nas_username || '',
        password: existingNasCredentials.nas_password || '',
      };

      // Pass credentials directly to the connect function
      await onConnectNas(projectCode, credentials);
    } else {
      console.error('No project code found for existing credentials');
      alert('No project code found. Please ensure a project is selected.');
    }
  };

  // Handler for resetting to enter new credentials
  const handleResetCredentials = () => {
    setUseExistingCredentials(false);
    setNasForm({
      nasType: 'ftp',
      url: '',
      username: '',
      password: '',
    });
  };

  // Handler for CSV upload completion
  const handleCsvUploadComplete = (uploadData: { fileName: string; filePath: string; fileSize: number; csvMetadata?: any }) => {
    setCsvUploadData(uploadData);
    setCsvUploadError('');
    
    // Update localStorage with uploaded file info
    try {
      const projectData = getCurrentProjectData();
      if (projectData) {
        projectData.csv_file = uploadData;
        localStorage.setItem('currentProject', JSON.stringify(projectData));
      }
    } catch (error) {
      console.error('Error saving CSV upload data:', error);
    }
  };

  // Handler for CSV upload error
  const handleCsvUploadError = (error: string) => {
    setCsvUploadError(error);
    setCsvUploadData(null);
  };

  // Handler for continuing after CSV upload
  const handleContinueAfterCsvUpload = () => {
    if (csvUploadData) {
      markStepCompleted(2);
      onGoToStep(3);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4 flex items-center">
        <FaServer className="mr-2 text-green-500" />
        Data Connector Setup
      </h3>

      {selectedClient && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm text-blue-800">
            <strong>Selected Client:</strong> {selectedClient.full_name} (@
            {selectedClient.username})
          </div>
        </div>
      )}

      {/* CSV Project Type - Show CSV Upload Component */}
      {projectType === 'csv' ? (
        <div className="space-y-6">
          <div className="border rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium flex items-center">
                <FaFileAlt className="mr-2 text-blue-600" />
                CSV File Upload
              </h4>
              {csvUploadData && (
                <span className="text-green-600 text-sm flex items-center">
                  <FaCheckCircle className="mr-1" /> File Uploaded
                </span>
              )}
            </div>

            {/* CSV Upload Component */}
            <CSVUploadComponent
              onUploadComplete={handleCsvUploadComplete}
              onUploadError={handleCsvUploadError}
              isUploading={false}
            />

          

            {/* Continue Button */}
            {csvUploadData && (
              <button
                onClick={handleContinueAfterCsvUpload}
                className="w-full mt-6 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center justify-center"
              >
                <FaArrowRight className="mr-2" />
                Continue to Next Step
              </button>
            )}
          </div>
         
        </div>
      ) : (
        /* Regular Project Types - Show Storage Options */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* NAS Connection */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-medium flex items-center">
              <FaServer className="mr-2 text-gray-600" />
              NAS Storage
            </h4>
            {connectorStatus.nas && (
              <span className="text-green-600 text-sm flex items-center">
                <FaCheckCircle className="mr-1" /> Connected
              </span>
            )}
          </div>

          {!connectorStatus.nas && (
            <>
              {/* Show existing credentials option if available */}
              {existingNasCredentials && useExistingCredentials ? (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="text-sm font-medium text-green-800 flex items-center">
                      Existing NAS Credentials Found
                    </h5>
                    <button
                      onClick={handleResetCredentials}
                      className="text-xs text-green-600 hover:text-green-800 underline"
                    >
                      Enter New Credentials
                    </button>
                  </div>
                  <div className="text-xs text-green-700 space-y-1">
                    <div><strong>URL:</strong> {existingNasCredentials.nas_url}</div>
                    <div><strong>Username:</strong> {existingNasCredentials.nas_username}</div>
                  </div>
                  <button
                    onClick={handleUseExistingCredentials}
                    disabled={connectingNas || !selectedClient}
                    className="w-full mt-3 px-3 py-2 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    {connectingNas ? (
                      <FaSpinner className="animate-spin mr-2" />
                    ) : (
                      <FaCheckCircle className="mr-2" />
                    )}
                    Use Existing Credentials
                  </button>
                </div>
              ) : (
                <>
                  {/* Show reset option if we have existing credentials but user chose to enter new ones */}
                  {existingNasCredentials && (
                    <div className="mb-3 flex justify-end">
                      <button
                        onClick={() => setUseExistingCredentials(true)}
                        className="text-xs text-blue-600 hover:text-blue-800 underline flex items-center"
                      >
                        <FaRedo className="mr-1" />
                        Use Existing Credentials Instead
                      </button>
                    </div>
                  )}

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        NAS Type
                      </label>
                      <select
                        value={nasForm.nasType}
                        onChange={(e) =>
                          setNasForm((prev) => ({
                            ...prev,
                            nasType: e.target.value,
                          }))
                        }
                        className="w-full border border-gray-300 rounded px-3 py-2"
                      >
                        <option value="ftp">FTP</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        NAS URL
                      </label>
                      <input
                        type="text"
                        value={nasForm.url}
                        onChange={(e) =>
                          setNasForm((prev) => ({ ...prev, url: e.target.value }))
                        }
                        className="w-full border border-gray-300 rounded px-3 py-2"
                        placeholder="10.10.10.199:6001"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Username
                      </label>
                      <input
                        type="text"
                        value={nasForm.username}
                        onChange={(e) =>
                          setNasForm((prev) => ({
                            ...prev,
                            username: e.target.value.trim(),
                          }))
                        }
                        className="w-full border border-gray-300 rounded px-3 py-2"
                        placeholder="Enter FTP username"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Password
                      </label>
                      <input
                        type="password"
                        value={nasForm.password}
                        onChange={(e) =>
                          setNasForm((prev) => ({
                            ...prev,
                            password: e.target.value.trim(),
                          }))
                        }
                        className="w-full border border-gray-300 rounded px-3 py-2"
                        placeholder="Enter FTP password"
                      />
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      const projectCode = getCurrentProjectCode();
                      if (!projectCode) {
                        alert('No project code found. Please ensure a project is selected.');
                        return;
                      }

                      // Validate credentials before submitting
                      if (!nasForm.username.trim() || !nasForm.password.trim()) {
                        alert('Please enter both username and password');
                        return;
                      }

                      onConnectNas(projectCode);
                    }}
                    disabled={connectingNas || !getCurrentProjectCode() || !nasForm.username.trim() || !nasForm.password.trim()}
                    className="w-full mt-4 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 flex items-center justify-center"
                  >
                    {connectingNas ? (
                      <FaSpinner className="animate-spin mr-2" />
                    ) : (
                      <FaServer className="mr-2" />
                    )}
                    Connect NAS
                  </button>
                </>
              )}
            </>
          )}

          {connectorStatus.nas && (
            <button
              onClick={handleContinueToDatasetSelection}
              className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center justify-center"
            >
              <FaArrowRight className="mr-2" />
              Continue to Dataset Selection
            </button>
          )}
        </div>

        {/* Google Drive Connection */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-medium flex items-center">
              <FaGoogle className="mr-2 text-blue-600" />
              Google Drive
            </h4>
            {connectorStatus.drive && (
              <span className="text-green-600 text-sm flex items-center">
                <FaCheckCircle className="mr-1" /> Connected
              </span>
            )}
          </div>

          {!connectorStatus.drive && (
            <>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Client ID
                  </label>
                  <input
                    type="text"
                    value={driveForm.clientId}
                    onChange={(e) =>
                      setDriveForm((prev) => ({
                        ...prev,
                        clientId: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="Google OAuth Client ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Client Secret
                  </label>
                  <input
                    type="password"
                    value={driveForm.clientSecret}
                    onChange={(e) =>
                      setDriveForm((prev) => ({
                        ...prev,
                        clientSecret: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="Google OAuth Client Secret"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Folder ID (Optional)
                  </label>
                  <input
                    type="text"
                    value={driveForm.folderId}
                    onChange={(e) =>
                      setDriveForm((prev) => ({
                        ...prev,
                        folderId: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="Google Drive Folder ID"
                  />
                </div>
              </div>

              <button
                onClick={() => {
                  const projectCode = getCurrentProjectCode();
                  if (projectCode) {
                    onConnectDrive(projectCode);
                  } else {
                    alert('No project code found. Please ensure a project is selected.');
                  }
                }}
                disabled={connectingDrive || !getCurrentProjectCode()}
                className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {connectingDrive ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : (
                  <FaGoogle className="mr-2" />
                )}
                Connect Google Drive
              </button>
            </>
          )}

          {connectorStatus.drive && (
            <button
              onClick={handleContinueToDatasetSelection}
              className="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center justify-center"
            >
              <FaArrowRight className="mr-2" />
              Continue to Dataset Selection
            </button>
          )}
        </div>

        {/* MinIO Connection */}
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-medium flex items-center">
              <FaDatabase className="mr-2 text-pink-600" />
              MinIO Storage
            </h4>
            {connectorStatus.minio && (
              <span className="text-green-600 text-sm flex items-center">
                <FaCheckCircle className="mr-1" /> Connected
              </span>
            )}
          </div>

          {!connectorStatus.minio && (
            <>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Endpoint
                  </label>
                  <input
                    type="text"
                    value={minioForm.endpoint}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        endpoint: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="localhost:9000"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Access Key
                  </label>
                  <input
                    type="text"
                    value={minioForm.accessKey}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        accessKey: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="MinIO Access Key"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Secret Key
                  </label>
                  <input
                    type="password"
                    value={minioForm.secretKey}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        secretKey: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="MinIO Secret Key"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Bucket Name
                  </label>
                  <input
                    type="text"
                    value={minioForm.bucketName}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        bucketName: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="my-bucket"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Region (Optional)
                  </label>
                  <input
                    type="text"
                    value={minioForm.region}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        region: e.target.value,
                      }))
                    }
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="us-east-1"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="minio-secure"
                    checked={minioForm.secure}
                    onChange={(e) =>
                      setMinioForm((prev) => ({
                        ...prev,
                        secure: e.target.checked,
                      }))
                    }
                    className="mr-2"
                  />
                  <label htmlFor="minio-secure" className="text-sm text-gray-700">
                    Use HTTPS (Secure)
                  </label>
                </div>
              </div>

              <button
                onClick={() => {
                  const projectCode = getCurrentProjectCode();
                  if (projectCode) {
                    onConnectMinio(projectCode);
                  } else {
                    alert('No project code found. Please ensure a project is selected.');
                  }
                }}
                disabled={connectingMinio || !getCurrentProjectCode() || !minioForm.endpoint.trim() || !minioForm.accessKey.trim() || !minioForm.secretKey.trim() || !minioForm.bucketName.trim()}
                className="w-full mt-4 px-4 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 flex items-center justify-center"
              >
                {connectingMinio ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : (
                  <FaDatabase className="mr-2" />
                )}
                Connect MinIO
              </button>
            </>
          )}

          {connectorStatus.minio && (
            <button
              onClick={handleContinueToDatasetSelection}
              className="w-full mt-4 px-4 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 flex items-center justify-center"
            >
              <FaArrowRight className="mr-2" />
              Continue to Dataset Selection
            </button>
          )}
        </div>
        </div>
      )}

      <div className="flex justify-between mt-6">
        <button
          onClick={() => onGoToStep(1)}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Back to Registration
        </button>
      </div>
    </div>
  );
};