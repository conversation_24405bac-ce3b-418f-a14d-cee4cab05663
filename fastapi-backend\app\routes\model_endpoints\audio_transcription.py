import logging
from typing import Dict, Any, Optional
import httpx
from core.config import settings
from .model_utils import async_exception_handler

# Configure logging
logger = logging.getLogger('audio_transcription_service')

class AudioTranscriptionService:
    def __init__(self):
        self.api_url = settings.api_settings.audio_url
        self.api_key = settings.api_settings.audio_key
        self.timeout = 120.0
    
    def create_client(self) -> httpx.AsyncClient:
        headers = {"Authorization": f"Bearer {self.api_key}"} if self.api_key else {}
        return httpx.AsyncClient(
            base_url=self.api_url.rstrip('/'),
            headers=headers,
            timeout=self.timeout,
            follow_redirects=True
        )

    @async_exception_handler
    async def transcribe_audio(
        self,
        audio_data,
        model_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        original_filename = getattr(audio_data, 'filename', 'audio.wav')
        
        try:
            # Forward the file directly to audio service without bytes conversion
            async with self.create_client() as client:
                # Reset file position to beginning if available
                if hasattr(audio_data, 'seek'):
                    await audio_data.seek(0)
                
                # Handle FileWrapper file property
                if hasattr(audio_data, 'file') and hasattr(audio_data.file, '__await__'):
                    file_stream = await audio_data.file
                else:
                    file_stream = audio_data.file if hasattr(audio_data, 'file') else audio_data
                
                files = {
                    "file": (original_filename, file_stream, 
                            getattr(audio_data, 'content_type', 'audio/wav'))
                }
                
                if model_name is not None and str(model_name).lower() == "string":
                    model_name = None
                if custom_prompt is not None and str(custom_prompt).lower() == "string":
                    custom_prompt = None
                    
                data = {
                    "model": model_name or "whisper-medium",
                    "language": "en",
                    "response_format": "json"
                }

                response = await client.post(
                    "/v1/audio/transcriptions",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                result = response.json()
            
            return {
                "file_name": original_filename,
                "transcription": result.get("text", ""),
                "status": "success"
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error forwarding to audio service: {error_msg}", exc_info=True)
            return {
                "file_name": original_filename,
                "error": error_msg,
                "status": "failed"
            }

def get_audio_transcription_service() -> AudioTranscriptionService:
    """Get the audio transcription service instance"""
    return AudioTranscriptionService()