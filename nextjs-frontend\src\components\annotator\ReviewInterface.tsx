'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FaSearchMinus, FaSearchPlus, FaUndo, FaDownload, FaCheck, FaExpand, FaArrowRight, FaArrowLeft, FaFile, FaExternalLinkAlt, FaSpinner, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import PassportFields from './fields/PassportFields';
import CheckFields from './fields/CheckFields';
import InvoiceFields from './fields/InvoiceFields';
import Link from 'next/link';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import Image from "next/image";

// Consolidated interfaces
interface DocumentFile {
  id: string;
  name: string;
  model_type: 'standard' | 'enhanced' | 'premium';
  status: 'processing' | 'completed' | 'error' | 'saved';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  fields?: Record<string, any>;
  imageUrl?: string;
  drive_link?: string;
}

interface ReviewInterfaceProps {
  documents?: DocumentFile[];
  documentType: 'passport' | 'check' | 'invoice';
  source?: 'local' | 'drive';
  resetToUpload?: () => void;
}

// Constants
const API_BASE = `${process.env.NEXT_PUBLIC_API_URL}/supervision`;
const FILTER_OPTIONS = ['all', 'processing', 'completed'] as const;

const StatusBadge = ({ status }: { status: DocumentFile['status'] }) => {
  const statusConfig = {
    completed: { bg: 'bg-green-100', text: 'text-green-800', label: 'Completed', icon: FaCheckCircle },
    error: { bg: 'bg-red-100', text: 'text-red-800', label: 'Error', icon: FaExclamationTriangle },
    saved: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Saved', icon: FaCheck },
    processing: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Processing', icon: FaSpinner }
  };
  
  const config = statusConfig[status];
  const Icon = config.icon;
  
  return (
    <span className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${config.bg} ${config.text}`}>
      <Icon className={`mr-1 h-3 w-3 ${status === 'processing' ? 'animate-spin' : ''}`} />
      {config.label}
    </span>
  );
};

const LoadingSpinner = ({ message }: { message: string }) => (
  <div className="flex flex-col items-center justify-center py-12">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
    <h5 className="text-lg font-medium text-gray-900 mb-2">{message}</h5>
    <p className="text-gray-500 text-center">Please wait while we process your documents</p>
  </div>
);

const CompletionScreen = ({ resetToUpload }: { resetToUpload?: () => void }) => (
  <div className="flex flex-col items-center justify-center min-h-screen p-4">
    <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
      <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
        <FaCheckCircle className="h-8 w-8 text-green-600" />
      </div>
      <h3 className="text-2xl font-semibold text-gray-900 mb-4">All Documents Processed!</h3>
      <p className="text-gray-600 mb-8">You have successfully processed and reviewed all documents.</p>
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <button
          className="w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          onClick={resetToUpload}
        >
          Process More Documents
        </button>
        <Link href="/annotator/dashboard">
          <button className="w-full sm:w-auto px-6 py-3 bg-gray-100 text-gray-800 rounded-lg hover:bg-gray-200 transition-colors font-medium">
            Dashboard
          </button>
        </Link>
      </div>
    </div>
  </div>
);

export default function ReviewInterface({ documents, documentType, source = 'local', resetToUpload }: ReviewInterfaceProps) {
  // State management
  const [files, setFiles] = useState<DocumentFile[]>(documents || []);
  const [currentDocumentId, setCurrentDocumentId] = useState<string | null>(null);
  const [fieldData, setFieldData] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'processing' | 'completed' | 'all'>('all');
  const [isDownloadOpen, setIsDownloadOpen] = useState(false);
  const [isBatchSaving, setIsBatchSaving] = useState(false);
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0, logs: [] as string[] });
  const [isAllCompleted, setIsAllCompleted] = useState(false);
  
  const pollingRef = useRef<number | null>(null);
  const filesRef = useRef(files);
  
  // Update files ref
  useEffect(() => { filesRef.current = files; }, [files]);
  
  // Memoized computed values
  const filteredFiles = useMemo(() => 
    viewMode === 'all' ? files : files.filter(file => file.status === viewMode),
    [files, viewMode]
  );
  
  const currentDocument = useMemo(() => 
    files.find(file => file.id === currentDocumentId),
    [files, currentDocumentId]
  );
  
  const isCurrentDocumentCompleted = currentDocument?.status === 'completed';
  
  const currentDocumentIndex = useMemo(() => 
    filteredFiles.findIndex(f => f.id === currentDocumentId),
    [filteredFiles, currentDocumentId]
  );

  // Initialize component
  useEffect(() => {
    if (documents && documents.length > 0) {
      setFiles(documents);
      const completedDoc = documents.find(file => file.status === 'completed') || documents[0];
      setCurrentDocumentId(completedDoc.id);
      setFieldData(completedDoc.fields as Record<string, string> || {});
    }
    
    const timer = setTimeout(() => setIsLoading(false), 1500);
    return () => clearTimeout(timer);
  }, [documents]);
  
  // Auto-update field data when document completes
  useEffect(() => {
    if (currentDocument?.status === 'completed' && currentDocument.fields) {
      setFieldData(currentDocument.fields as Record<string, string>);
    }
  }, [currentDocument]);
  
  // Document polling
  useEffect(() => {
    const poll = async () => {
      for (const file of filesRef.current) {
        if (file.status === 'processing') {
          const res = await authFetch(`${API_BASE}/check-status/${file.id}`);
          const json = await res.json();
          
          if (json.status === 'completed') {
            setFiles(prev => prev.map(f => f.id === file.id ? { 
              ...f, 
              status: 'completed',
              fields: json.data || {}
            } : f));
            
            if (file.id === currentDocumentId) {
              setFieldData(json.data || {});
              setIsLoading(false);
            }
          } else if (json.status === 'error') {
            setFiles(prev => prev.map(f => f.id === file.id ? { ...f, status: 'error' } : f));
          }
        }
      }
      pollingRef.current = window.setTimeout(poll, 2000);
    };
    
    if (!isLoading) poll();
    return () => {
      if (pollingRef.current) clearTimeout(pollingRef.current);
    };
  }, [isLoading, currentDocumentId]);
  
  // Handlers
  const handleDocumentSelect = useCallback((docId: string) => {
    setCurrentDocumentId(docId);
    const doc = files.find(d => d.id === docId);
    setFieldData(doc?.fields as Record<string, string> || {});
  }, [files]);
  
  const handleNext = useCallback(() => {
    if (currentDocumentIndex < filteredFiles.length - 1) {
      handleDocumentSelect(filteredFiles[currentDocumentIndex + 1].id);
    }
  }, [currentDocumentIndex, filteredFiles, handleDocumentSelect]);
  
  const handlePrevious = useCallback(() => {
    if (currentDocumentIndex > 0) {
      handleDocumentSelect(filteredFiles[currentDocumentIndex - 1].id);
    }
  }, [currentDocumentIndex, filteredFiles, handleDocumentSelect]);
  
  const handleSaveNext = useCallback(async () => {
    if (!currentDocumentId) return;
    
    const response = await authFetch(`${API_BASE}/save-document`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        image_id: currentDocumentId,
        data: fieldData,
        corrections: null,
        verified: true
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Server error (${response.status}): ${errorText}`);
    }
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'Save failed');
    }
    
    setFiles(prev => prev.map(f => 
      f.id === currentDocumentId ? { ...f, status: 'saved' } : f
    ));
    
    if (currentDocumentIndex < filteredFiles.length - 1) {
      handleNext();
    } else {
      setIsAllCompleted(true);
    }
  }, [currentDocumentId, fieldData, currentDocumentIndex, filteredFiles.length, handleNext]);
  
  const handleDownloadCsv = useCallback(async () => {
    if (!currentDocumentId) {
      showToast.warning('No document selected');
      return;
    }
    
    const timestamp = new Date().toISOString().replace(/[-:\.TZ]/g, '').slice(0, 14);
    const doc = files.find(f => f.id === currentDocumentId);
    const baseName = doc?.name.replace(/[^a-zA-Z0-9_\-.]/g, '_') || 'document';
    const downloadFilename = `${documentType}_${baseName}_${timestamp}.csv`;
    
    const response = await authFetch(`${API_BASE}/download_csv`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        edited_data: fieldData,
        document_type: documentType,
        document_id: currentDocumentId,
        filename: downloadFilename
      })
    });
    
    if (!response.ok) throw new Error('Network response was not ok');
    
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = downloadFilename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [currentDocumentId, fieldData, documentType, files]);
  
  const handleDownloadTxt = useCallback(() => {
    if (!currentDocumentId) {
      showToast.warning('No document selected');
      return;
    }
    window.location.href = `${API_BASE}/download_txt/${currentDocumentId}`;
  }, [currentDocumentId]);
  
  const handleSaveAll = useCallback(async () => {
    const docsToSave = files.filter(f => f.status === 'completed');
    setBatchProgress({ current: 0, total: docsToSave.length, logs: [] });
    setIsBatchSaving(true);
    
    for (const [index, file] of docsToSave.entries()) {
      const response = await authFetch(`${API_BASE}/save-document`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          image_id: file.id, 
          data: file.fields || fieldData, 
          corrections: null, 
          verified: true 
        })
      });
      
      const result = await response.json();
      if (result.success) {
        setFiles(prev => prev.map(f => f.id === file.id ? { ...f, status: 'saved' } : f));
        setBatchProgress(prev => ({ 
          ...prev, 
          current: index + 1, 
          logs: [...prev.logs, `✓ Saved ${file.name}`] 
        }));
      } else {
        setBatchProgress(prev => ({ 
          ...prev, 
          current: index + 1, 
          logs: [...prev.logs, `✗ Failed ${file.name}`] 
        }));
      }
    }
    
    setTimeout(() => {
      setIsBatchSaving(false);
      setIsAllCompleted(true);
    }, 1000);
  }, [files, fieldData]);
  
  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') handleNext();
      if (e.key === 'ArrowLeft') handlePrevious();
      if ((e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S')) {
        e.preventDefault();
        handleSaveNext();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleNext, handlePrevious, handleSaveNext]);
  
  // Render completion screen
  if (isAllCompleted) {
    return <CompletionScreen resetToUpload={resetToUpload} />;
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto p-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm mb-6 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Document Review - {source === 'local' ? 'Local Files' : 'Google Drive'}
              </h1>
              <p className="text-gray-600 mt-1">
                Processing {documentType} documents with {currentDocument?.model_type || 'standard'} model
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Document {currentDocumentIndex + 1} of {filteredFiles.length}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={handlePrevious}
                  disabled={currentDocumentIndex === 0}
                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FaArrowLeft />
                </button>
                <button
                  onClick={handleNext}
                  disabled={currentDocumentIndex === filteredFiles.length - 1}
                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <FaArrowRight />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-12 gap-6">
          {/* Document Queue Sidebar */}
          <div className="col-span-2">
            <div className="bg-white rounded-lg shadow-sm h-full">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Document Queue</h3>
                <div className="flex space-x-1">
                  {FILTER_OPTIONS.map(mode => (
                    <button
                      key={mode}
                      onClick={() => setViewMode(mode)}
                      className={`px-2 py-1 text-xs rounded-full transition-colors ${
                        viewMode === mode 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {mode.charAt(0).toUpperCase() + mode.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
              <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 12rem)' }}>
                {filteredFiles.map(file => (
                  <div
                    key={file.id}
                    onClick={() => handleDocumentSelect(file.id)}
                    className={`p-3 border-b border-gray-100 cursor-pointer transition-colors hover:bg-gray-50 ${
                      file.id === currentDocumentId ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center mb-1">
                          <FaFile className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                          <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                        </div>
                        {source === 'drive' && file.drive_link && (
                          <a 
                            href={file.drive_link} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 mt-1"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaExternalLinkAlt className="h-3 w-3 mr-1" />
                            View in Drive
                          </a>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          <StatusBadge status={file.status} />
                          <span className="text-xs text-gray-500 capitalize">
                            {file.model_type}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="col-span-10">
            {isLoading ? (
              <div className="bg-white rounded-lg shadow-sm p-8">
                <LoadingSpinner message="Processing documents..." />
              </div>
            ) : (
              <div className="grid grid-cols-12 gap-6 h-full">
                {/* Document Image */}
                <div className="col-span-7">
                  <div className="bg-white rounded-lg shadow-sm h-full flex flex-col">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900">Document Image</h3>
                    </div>
                    <div className="flex-1 p-4" style={{ minHeight: '700px' }}>
                      {currentDocument ? (
                        <TransformWrapper 
                          initialScale={1} 
                          minScale={0.3} 
                          maxScale={5} 
                          centerOnInit={true}
                          wheel={{ step: 0.1 }}
                          pinch={{ step: 5 }}
                        >
                          {({ zoomIn, zoomOut, resetTransform, centerView, state }:
                           // eslint-disable-next-line @typescript-eslint/no-explicit-any
                           any) => (
                            <div className="h-full flex flex-col">
                              <div className="flex items-center justify-center space-x-2 mb-4">
                                <button 
                                  onClick={zoomIn}
                                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
                                >
                                  <FaSearchPlus />
                                </button>
                                <span className="min-w-[60px] text-center text-sm font-medium">
                                  {Math.round((state?.scale ?? 1) * 100)}%
                                </span>
                                <button 
                                  onClick={zoomOut}
                                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
                                >
                                  <FaSearchMinus />
                                </button>
                                <button 
                                  onClick={resetTransform}
                                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
                                >
                                  <FaUndo />
                                </button>
                                <button 
                                  onClick={() => { resetTransform(); centerView(); }}
                                  className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
                                >
                                  <FaExpand />
                                </button>
                              </div>
                              <div className="flex-1 flex justify-center items-center overflow-hidden rounded-lg border border-gray-200 bg-gray-50">
                                <TransformComponent wrapperStyle={{ width: '100%', height: '100%' }}>
                                  <div className="w-full h-full flex items-center justify-center">
                                  <Image
    src={`${process.env.NEXT_PUBLIC_API_URL?.replace(/\/$/, '')}${currentDocument.imageUrl}`}
    alt="Document"
    width={400}
    height={500}
    unoptimized
    className="max-w-full max-h-full object-contain shadow-lg"
    style={{
      minWidth: '300px',
      minHeight: '400px',
      borderRadius: '4px'
    }}
  />
                                  </div>
                                </TransformComponent>
                              </div>
                            </div>
                          )} 
                        </TransformWrapper>
                      ) : (
                        <div className="h-full flex items-center justify-center text-gray-500">
                          <LoadingSpinner message="Loading image..." />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Extracted Data */}
                <div className="col-span-5">
                  <div className="bg-white rounded-lg shadow-sm h-full flex flex-col">
                    <div className="px-6 py-4 border-b border-gray-200">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">Extracted Data</h3>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={handleSaveNext}
                            disabled={!isCurrentDocumentCompleted}
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                          >
                            <FaCheck className="mr-2 h-4 w-4" />
                            Save & Next
                          </button>
                          <div className="relative">
                            <button
                              onClick={() => setIsDownloadOpen(!isDownloadOpen)}
                              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                            >
                              <FaDownload className="mr-2 h-4 w-4" />
                              Download
                            </button>
                            {isDownloadOpen && (
                              <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                                <button
                                  onClick={handleDownloadCsv}
                                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-t-lg"
                                >
                                  Download CSV
                                </button>
                                <button
                                  onClick={handleDownloadTxt}
                                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-b-lg"
                                >
                                  Download TXT
                                </button>
                              </div>
                            )}
                          </div>
                          <button
                            onClick={handleSaveAll}
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-yellow-600 hover:bg-yellow-700 transition-colors"
                          >
                            Save All
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="flex-1 overflow-y-auto p-6" style={{ minHeight: '700px' }}>
                      {isCurrentDocumentCompleted ? (
                        <div>
                          {documentType === 'passport' && (
                            <PassportFields 
                              data={fieldData} 
                              onChange={(name, value) => setFieldData(prev => ({ ...prev, [name]: value }))} 
                            />
                          )}
                          {documentType === 'check' && (
                            <CheckFields 
                              data={fieldData} 
                              onChange={(name, value) => setFieldData(prev => ({ ...prev, [name]: value }))} 
                            />
                          )}
                          {documentType === 'invoice' && (
                            <InvoiceFields 
                              data={fieldData} 
                              onChange={(name, value) => setFieldData(prev => ({ ...prev, [name]: value }))} 
                            />
                          )}
                        </div>
                      ) : (
                        <LoadingSpinner message="Processing document..." />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Batch Save Modal */}
      {isBatchSaving && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Saving All Documents</h4>
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${batchProgress.total ? (batchProgress.current / batchProgress.total) * 100 : 0}%` }}
              />
            </div>
            <p className="text-sm text-gray-600 mb-4">
              {batchProgress.current} of {batchProgress.total} processed
            </p>
            <div className="max-h-40 overflow-y-auto text-xs border border-gray-200 rounded-lg p-3 bg-gray-50">
              {batchProgress.logs.map((log, idx) => (
                <div key={idx} className="py-1">{log}</div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 