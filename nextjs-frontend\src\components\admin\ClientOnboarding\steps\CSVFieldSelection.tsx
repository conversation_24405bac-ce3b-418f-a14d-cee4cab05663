// steps/CSVFieldSelection.tsx
import React, { useState, useEffect } from 'react';
import {
  FaDatabase,
  FaArrowRight,
  FaArrowLeft,
  FaCheckCircle,
  FaFileAlt,
  FaColumns,
  FaHashtag
} from 'react-icons/fa';
import { Client } from '../types';

interface CSVMetadata {
  columns: string[];
  total_columns: number;
  sample_rows: number;
  encoding: string;
  column_types: Record<string, string>;
  file_extension: string;
  error?: string;
}

interface CurrentProject {
  id: number;
  project_code: string;
  project_name: string;
  project_type: string;
  csv_file?: {
    fileName: string;
    filePath: string;
    fileSize: number;
    csvMetadata?: CSVMetadata;
  };
}

interface CSVFieldSelectionProps {
  selectedClient: Client | null;
  onGoToStep: (step: number, projectCode?: string) => void;
  onSetCSVConfiguration: (config: { selectedColumn: string; recordsPerBatch: number }) => Promise<{ success: boolean; data?: any }>;
  isConfiguring: boolean;
}

export const CSVFieldSelection: React.FC<CSVFieldSelectionProps> = ({
  selectedClient,
  onGoToStep,
  onSetCSVConfiguration,
  isConfiguring
}) => {
  const [currentProject, setCurrentProject] = useState<CurrentProject | null>(null);
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const [recordsPerBatch, setRecordsPerBatch] = useState<number>(50);
  const [configurationComplete, setConfigurationComplete] = useState(false);

  // Load the project that was created in step 1
  useEffect(() => {
    const loadCurrentProject = () => {
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          setCurrentProject(project);
          console.log('Loaded CSV project from localStorage:', project);
          
          // Pre-select the first column if available
          if (project.csv_file?.csvMetadata?.columns?.length > 0) {
            setSelectedColumn(project.csv_file.csvMetadata.columns[0]);
          }
        } else {
          console.warn('No project data found in localStorage');
        }
      } catch (error) {
        console.error('Error loading project from localStorage:', error);
      }
    };

    loadCurrentProject();
  }, []);

  const handleConfigureCSV = async () => {
    if (!selectedColumn || !currentProject) return;

    const config = {
      selectedColumn,
      recordsPerBatch
    };

    console.log('Configuring CSV with:', config);
    
    const result = await onSetCSVConfiguration(config);
    
    if (result.success) {
      setConfigurationComplete(true);
      
      // Update localStorage with configuration
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          project.csv_configuration = config;
          localStorage.setItem('currentProject', JSON.stringify(project));
        }
      } catch (error) {
        console.error('Error saving CSV configuration:', error);
      }
    } else {
      // Show error to user
      alert('Configuration failed. Please try again.');
    }
  };

  const handleContinueToNextStep = () => {
    if (currentProject?.project_code) {
      onGoToStep(4, currentProject.project_code);
    }
  };

  const csvMetadata = currentProject?.csv_file?.csvMetadata;
  const hasValidMetadata = csvMetadata && csvMetadata.columns.length > 0;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4 flex items-center">
        <FaColumns className="mr-2 text-blue-500" />
        Field Selection
      </h3>

      {selectedClient && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm text-blue-800">
            <strong>Selected Client:</strong> {selectedClient.full_name} (@{selectedClient.username})
          </div>
        </div>
      )}

      {currentProject && (
        <div className="mb-6">
          <div className="bg-blue-50 border border-blue-200 text-blue-800 px-3 py-2 mb-4 rounded-md">
            <small className="text-blue-800">
              <strong>Configuring:</strong> {currentProject.project_name}
            </small>
          </div>

          {/* CSV File Information */}
          {currentProject.csv_file && (
            <div className="border border-gray-200 rounded-lg p-4 mb-6">
              <div className="flex items-center mb-3">
                <FaFileAlt className="text-green-600 text-lg mr-2" />
                <div>
                  <h4 className="font-medium text-gray-800">{currentProject.csv_file.fileName}</h4>
                  <p className="text-sm text-gray-600">
                    {csvMetadata ? `${csvMetadata.total_columns} columns, ${csvMetadata.sample_rows} sample rows` : 'Processing file...'}
                  </p>
                </div>
              </div>
              
            </div>
          )}

          {/* Configuration Form */}
          {hasValidMetadata ? (
            <div className="space-y-6">
              {/* Column Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Target Column for Annotation
                </label>
                <select
                  value={selectedColumn}
                  onChange={(e) => setSelectedColumn(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">-- Select a column --</option>
                  {csvMetadata!.columns.map((column, index) => (
                    <option key={index} value={column}>
                      {column}
                      {csvMetadata!.column_types[column] && 
                        ` (${csvMetadata!.column_types[column]})`
                      }
                    </option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  This column will be used as the target for annotation tasks
                </p>
              </div>

              {/* Records per Batch */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Records per Batch
                  <small className="text-gray-500 ml-1">(optional)</small>
                </label>
                <input
                  type="number"
                  min="1"
                  max="1000"
                  value={recordsPerBatch}
                  onChange={(e) => setRecordsPerBatch(parseInt(e.target.value) || 25)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="25"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Each batch will contain {recordsPerBatch} records for annotation
                </p>
              </div>

              {/* Configuration Button */}
              <div className="pt-4">
                <button
                  onClick={handleConfigureCSV}
                  disabled={!selectedColumn || isConfiguring}
                  className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isConfiguring ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                      Configuring...
                    </>
                  ) : (
                    <>
                      <FaCheckCircle className="mr-2" />
                      Configure CSV Project
                    </>
                  )}
                </button>
              </div>

              {/* Success Message */}
              {configurationComplete && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <FaCheckCircle className="text-green-600 mr-2" />
                    <div>
                      <h4 className="text-green-800 font-medium">Configuration Complete!</h4>
                      <p className="text-green-700 text-sm mt-1">
                        Column "{selectedColumn}" configured with {recordsPerBatch} records per batch
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <FaDatabase className="mx-auto text-4xl text-gray-400 mb-4" />
              <h4 className="text-lg font-medium text-gray-600 mb-2">
                {csvMetadata?.error ? 'Error Reading CSV File' : 'Processing CSV File...'}
              </h4>
              <p className="text-gray-500">
                {csvMetadata?.error 
                  ? `Unable to extract column information: ${csvMetadata.error}`
                  : 'Please wait while we analyze your CSV file structure...'
                }
              </p>
            </div>
          )}
        </div>
      )}

      {/* Navigation Footer */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200 mt-8">
        <button
          onClick={() => onGoToStep(2, currentProject?.project_code)}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Back to Data Connectors
        </button>

        {configurationComplete && (
          <button
            onClick={handleContinueToNextStep}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
          >
            Continue to Instructions
            <FaArrowRight className="ml-2" />
          </button>
        )}
      </div>
    </div>
  );
};
