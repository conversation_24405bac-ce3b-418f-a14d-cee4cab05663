"""
Telegram service module.Separate FastAPI service for Telegram routes.
"""
import sys
import os

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, os.pardir, os.pardir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware

# Import routes and dependencies
from routes.telegram_fetch_data_routes import router as telegram_router
from documind.telegram_service import TelegramServiceSettings
from core.config import settings
from cache.redis_connector import create_redis_client, set_redis_client, set_redis_enabled

# Configure logging
from core.logging import configure_app_logging
logger = configure_app_logging(
    console_for_root=True,
    console_for_modules=True
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup event for application resources (e.g., caching)"""
    
    if settings.redis_settings.enabled:
        try:
            client = await create_redis_client()
            if client:
                set_redis_client(client)
                logger.info("Async Redis client initialized for Telegram service caching")
            else:
                set_redis_enabled(False)
                logger.warning("Async Redis client not available, caching disabled for Telegram service")
        except Exception as e:
            set_redis_enabled(False)
            logger.error(f"Error initializing async Redis client for Telegram service: {e}")
    yield

tg_settings = TelegramServiceSettings()

app = FastAPI(
    title="Telegram Service",
    version=tg_settings.version,
    description="Separate service for Telegram-related operations",
    docs_url=tg_settings.docs_url,
    redoc_url=tg_settings.redoc_url,
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=tg_settings.cors_settings.allow_origins,
    allow_credentials=tg_settings.cors_settings.allow_credentials,
    allow_methods=tg_settings.cors_settings.allow_methods,
    allow_headers=tg_settings.cors_settings.allow_headers,
)

# Add session middleware for authentication
app.add_middleware(SessionMiddleware, secret_key=settings.jwt_settings.secret_key)

# Include telegram routes
app.include_router(telegram_router)

@app.get("/", tags=["Root"])
async def root():
    """Root endpoint - Telegram service information"""
    return {
        "service_name": "Telegram Service",
        "version": tg_settings.version,
        "docs_url": tg_settings.docs_url,
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting Telegram service on {tg_settings.host}:{tg_settings.port}")
    uvicorn.run(
        "documind_service:app",
        host=tg_settings.host,
        port=tg_settings.port,
        reload=True,
        log_level="info",
        access_log=True
    ) 