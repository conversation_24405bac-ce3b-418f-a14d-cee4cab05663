from sqlalchemy import Column, Integer, Text, String
from ..base import Base

class AdminSettings(Base):
    """Model for storing admin settings."""
    __tablename__ = "admin_settings"

    id = Column("Id", Integer, primary_key=True)
    annotation_dataset_name = Column("Annotation_Dataset_Name", String)
    verification_dataset_name = Column("Verification_Dataset_Name", String)
    supervision_instructions = Column("Supervision_Instructions", Text)
    
    # Active project settings for allocation_batches workflow
    active_annotation_project_code = Column("Active_Annotation_Project_Code", String, 
                                           comment="Currently active project code for annotation workflow")
    active_verification_project_code = Column("Active_Verification_Project_Code", String,
                                             comment="Currently active project code for verification workflow")