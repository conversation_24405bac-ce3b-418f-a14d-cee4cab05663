// types/index.ts
export interface Client {
  id: number;
  username: string;
  full_name: string;
  email: string;
  role?: string; // Optional role field for compatibility
  is_active?: boolean;
  created_at?: string;
  project_code?: string; // Optional project code for the client's active project
}

export interface LocalDataset {
  id: number;
  name: string;
  path: string;
  status: string;
}

export interface Dataset {
  id: string;
  name: string;
  total_batches: number;
  completed_batches: number;
  progress_percentage: number;
  folder_path: string;
  project_code?: string;
  label_file?: string;
}

export interface OnboardingStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
}

export interface ClientFormData {
  username: string;
  fullName: string;
  email: string;
  password: string;
  confirmPassword: string;
  
  // Project details
  projectName: string;
  projectType: string;
  projectDescription?: string;
}

export interface ConnectorStatus {
  nas: boolean;
  drive: boolean;
  minio: boolean;
}

export interface NasFormData {
  nasType: string;
  url: string;
  username: string;
  password: string;
}

export interface DriveFormData {
  clientId: string;
  clientSecret: string;
  folderId: string;
}

export interface MinIOFormData {
  endpoint: string;
  accessKey: string;
  secretKey: string;
  bucketName: string;
  secure: boolean;
  region: string;
}

export interface Directory {
  name: string;
  path: string;
  type: 'file' | 'directory';
}

export interface ImageItem {
  path: string;
  name: string;
}

export type ActiveTab = "annotation" | "verification";

export type SelectionTarget = 
  | "manual-folder"
  | "verification-image-folder"
  | "verification-label-file"
  | null;

export type CompletionStatus = 'completed' | 'in-progress' | 'empty';