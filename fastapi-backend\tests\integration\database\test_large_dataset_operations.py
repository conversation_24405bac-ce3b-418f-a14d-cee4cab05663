"""
Integration tests for Large Dataset Database Operations with REAL database operations.
Tests big data performance, bulk operations, and scalability patterns.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual large-scale database operations without mocks
- Validates performance characteristics under high data volumes
- Tests bulk operation efficiency and memory usage patterns
- Covers pagination, chunking, and query optimization scenarios
"""
import pytest
import pytest_asyncio
import asyncio
import time
import random
import gc
import psutil
import os
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func, and_
from datetime import datetime, timedelta

from app.repositories.batch_assignment_repository import BatchAssignmentRepository
from app.repositories.project_db_repository import ProjectDBRepository
from app.services.auth_service import AuthService
from app.services.project_batch_service import ProjectBatchService

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.allocation_models.project_users import ProjectUsers
from app.post_db.master_models.users import users, UserRole

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def large_dataset_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up environment for large dataset testing."""
    # Create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Create many users for large scale testing
    large_dataset_users = []
    for i in range(50):  # 50 users for bulk operations
        user_data = test_factory.users.create_user_register_request(role="annotator")
        success, user = await AuthService.register_user(test_master_db, user_data)
        assert success
        large_dataset_users.append(user)
    
    # Note: We'll create large batches and files in individual tests to control memory usage
    
    environment.update({
        "large_dataset_users": large_dataset_users
    })
    
    return environment


class PerformanceMonitor:
    """Utility class for monitoring test performance."""
    
    def __init__(self):
        self.process = psutil.Process()
        self.start_time = None
        self.start_memory = None
    
    def start_monitoring(self):
        """Start performance monitoring."""
        self.start_time = time.time()
        self.start_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        gc.collect()  # Clean up before test
    
    def get_metrics(self):
        """Get current performance metrics."""
        current_time = time.time()
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        return {
            "elapsed_time": current_time - self.start_time if self.start_time else 0,
            "memory_used_mb": current_memory - self.start_memory if self.start_memory else 0,
            "current_memory_mb": current_memory
        }


@pytest.mark.integration
@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Takes time
class TestLargeBatchOperations:
    """PERFORMANCE TEST SUITE: Large batch dataset operations."""
    
    @pytest.mark.asyncio
    async def test_bulk_batch_creation_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of creating many batches."""
        repository = ProjectDBRepository()
        project = large_dataset_environment["project"]
        monitor = PerformanceMonitor()
        
        monitor.start_monitoring()
        
        # Create large number of batches
        batch_count = 100
        created_batches = []
        
        # Batch creation in chunks to manage memory
        chunk_size = 20
        
        for chunk_start in range(0, batch_count, chunk_size):
            chunk_end = min(chunk_start + chunk_size, batch_count)
            chunk_batches = []
            
            for i in range(chunk_start, chunk_end):
                batch_data = {
                    'batch_identifier': f'BULK_BATCH_{i}_{int(time.time())}',
                    'total_files': random.randint(10, 50),
                    'file_list': [f'bulk_file_{i}_{j}.jpg' for j in range(10)],
                    'annotation_count': random.randint(1, 3),
                    'is_priority': (i % 10 == 0)  # Every 10th batch is priority
                }
                
                created_batch = await repository.create_allocation_batch(
                    project.project_code,
                    batch_data
                )
                
                chunk_batches.append(created_batch)
            
            created_batches.extend(chunk_batches)
            
            # Monitor progress
            metrics = monitor.get_metrics()
            print(f"Created {len(created_batches)} batches, Memory: {metrics['current_memory_mb']:.2f}MB, Time: {metrics['elapsed_time']:.2f}s")
        
        final_metrics = monitor.get_metrics()
        
        # Verify all batches were created
        assert len(created_batches) == batch_count
        
        # Performance assertions
        assert final_metrics["elapsed_time"] < 60.0, f"Bulk batch creation took too long: {final_metrics['elapsed_time']:.2f}s"
        assert final_metrics["memory_used_mb"] < 500, f"Excessive memory usage: {final_metrics['memory_used_mb']:.2f}MB"
        
        # Verify database state
        all_batches = await repository.get_allocation_batches(project.project_code)
        assert len(all_batches) >= batch_count, f"Expected at least {batch_count} batches, got {len(all_batches)}"
        
        # Test batch retrieval performance
        retrieval_start = time.time()
        retrieved_batches = await repository.get_allocation_batches(project.project_code)
        retrieval_time = time.time() - retrieval_start
        
        assert retrieval_time < 5.0, f"Batch retrieval took too long: {retrieval_time:.2f}s"
        assert len(retrieved_batches) >= batch_count
    
    @pytest.mark.asyncio
    async def test_large_file_registration_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of registering large numbers of files."""
        repository = ProjectDBRepository()
        project = large_dataset_environment["project"]
        monitor = PerformanceMonitor()
        
        # Create a large batch for file testing
        large_batch_data = {
            'batch_identifier': f'LARGE_FILE_BATCH_{int(time.time())}',
            'total_files': 1000,  # Large number of files
            'file_list': [f'large_file_{i}.jpg' for i in range(1000)],
            'annotation_count': 1
        }
        
        created_batch = await repository.create_allocation_batch(
            project.project_code,
            large_batch_data
        )
        
        monitor.start_monitoring()
        
        # Register files in chunks
        files_per_chunk = 100
        total_files = 500  # Register 500 files for performance testing
        registered_files_count = 0
        
        for chunk_start in range(0, total_files, files_per_chunk):
            chunk_end = min(chunk_start + files_per_chunk, total_files)
            
            # Prepare chunk of files
            files_data = []
            for i in range(chunk_start, chunk_end):
                file_data = {
                    'file_identifier': f'large_file_{i}_{int(time.time())}.jpg',
                    'original_filename': f'large_test_{i}.jpg',
                    'file_type': 'image',
                    'file_extension': '.jpg',
                    'storage_location': {
                        'type': 'test',
                        'path': f'/large_test/{i}.jpg'
                    },
                    'file_size_bytes': random.randint(100000, 5000000),  # 100KB to 5MB
                    'file_hash': f'hash_{i}_{int(time.time())}',
                    'sequence_order': i + 1
                }
                files_data.append(file_data)
            
            # Register chunk
            chunk_start_time = time.time()
            registered_files = await repository.register_files(
                project.project_code,
                created_batch['id'],
                files_data
            )
            chunk_time = time.time() - chunk_start_time
            
            registered_files_count += len(registered_files)
            
            # Monitor chunk performance
            metrics = monitor.get_metrics()
            print(f"Registered {registered_files_count} files, Chunk time: {chunk_time:.2f}s, Total time: {metrics['elapsed_time']:.2f}s, Memory: {metrics['current_memory_mb']:.2f}MB")
        
        final_metrics = monitor.get_metrics()
        
        # Performance assertions
        assert final_metrics["elapsed_time"] < 120.0, f"File registration took too long: {final_metrics['elapsed_time']:.2f}s"
        assert final_metrics["memory_used_mb"] < 200, f"Excessive memory usage during file registration: {final_metrics['memory_used_mb']:.2f}MB"
        assert registered_files_count == total_files
        
        # Test file retrieval performance
        retrieval_start = time.time()
        batch_files = await repository.get_files_by_batch(
            project.project_code,
            created_batch['id']
        )
        retrieval_time = time.time() - retrieval_start
        
        assert retrieval_time < 10.0, f"File retrieval took too long: {retrieval_time:.2f}s"
        assert len(batch_files) >= total_files
    
    @pytest.mark.asyncio
    async def test_batch_pagination_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of paginated batch operations."""
        repository = BatchAssignmentRepository()
        project = large_dataset_environment["project"]
        
        # First create many batches for pagination testing
        batch_count = 200
        created_batches = []
        
        for i in range(batch_count):
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"PAGINATION_BATCH_{i}_{int(time.time())}",
                total_files=random.randint(5, 20),
                annotation_count=random.randint(1, 3),
                assignment_count=0,
                is_priority=(i % 20 == 0)
            )
            test_db.add(batch)
            created_batches.append(batch)
            
            # Commit in chunks to avoid memory issues
            if (i + 1) % 50 == 0:
                await test_db.commit()
                for batch in created_batches[-50:]:
                    await test_db.refresh(batch)
        
        await test_db.commit()
        for batch in created_batches[-50:]:
            await test_db.refresh(batch)
        
        # Test paginated retrieval
        monitor = PerformanceMonitor()
        monitor.start_monitoring()
        
        page_size = 20
        total_retrieved = 0
        page_times = []
        
        # Simulate pagination through all batches
        available_batches = await repository.get_available_batches(project.project_code)
        
        # Test chunked processing of available batches
        for chunk_start in range(0, len(available_batches), page_size):
            chunk_start_time = time.time()
            
            chunk_end = min(chunk_start + page_size, len(available_batches))
            chunk_batches = available_batches[chunk_start:chunk_end]
            
            # Process each batch in chunk (simulate detailed operations)
            for batch in chunk_batches:
                batch_files = await repository.get_batch_files(
                    project.project_code,
                    batch.id
                )
                total_retrieved += 1
            
            chunk_time = time.time() - chunk_start_time
            page_times.append(chunk_time)
        
        final_metrics = monitor.get_metrics()
        
        # Performance assertions
        avg_page_time = sum(page_times) / len(page_times) if page_times else 0
        max_page_time = max(page_times) if page_times else 0
        
        assert final_metrics["elapsed_time"] < 30.0, f"Paginated processing took too long: {final_metrics['elapsed_time']:.2f}s"
        assert avg_page_time < 2.0, f"Average page processing time too slow: {avg_page_time:.2f}s"
        assert max_page_time < 5.0, f"Slowest page took too long: {max_page_time:.2f}s"
        assert total_retrieved >= len(available_batches)


@pytest.mark.integration
@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Takes time
class TestLargeUserOperations:
    """PERFORMANCE TEST SUITE: Large user dataset operations."""
    
    @pytest.mark.asyncio
    async def test_bulk_user_assignment_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of assigning many users to batches."""
        repository = BatchAssignmentRepository()
        project = large_dataset_environment["project"]
        large_users = large_dataset_environment["large_dataset_users"]
        monitor = PerformanceMonitor()
        
        # Create batches with many slots for bulk assignment testing
        bulk_batches = []
        for i in range(10):  # 10 batches
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"BULK_ASSIGN_BATCH_{i}_{int(time.time())}",
                total_files=20,
                annotation_count=5,  # 5 slots per batch
                assignment_count=0
            )
            test_db.add(batch)
            bulk_batches.append(batch)
        
        await test_db.commit()
        for batch in bulk_batches:
            await test_db.refresh(batch)
        
        # Add users to project
        for user in large_users[:30]:  # Use 30 users for testing
            project_user = test_factory.users.create_project_user(
                role="annotator",
                user_id=user.id,
                username=user.username
            )
            test_db.add(project_user)
        
        await test_db.commit()
        
        monitor.start_monitoring()
        
        # Perform bulk assignments
        assignment_results = []
        users_to_assign = large_users[:30]
        
        # Assign users in parallel batches
        concurrent_assignments = 5  # 5 concurrent assignments at a time
        
        for user_chunk_start in range(0, len(users_to_assign), concurrent_assignments):
            user_chunk_end = min(user_chunk_start + concurrent_assignments, len(users_to_assign))
            user_chunk = users_to_assign[user_chunk_start:user_chunk_end]
            
            # Create assignment tasks for this chunk
            assignment_tasks = []
            
            for i, user in enumerate(user_chunk):
                batch_index = (user_chunk_start + i) % len(bulk_batches)
                target_batch = bulk_batches[batch_index]
                slot = ((user_chunk_start + i) % target_batch.annotation_count) + 1
                
                async def assign_user(u, b, s):
                    try:
                        success = await repository.assign_user_to_batch(
                            project.project_code,
                            u.id,
                            u.username,
                            b.id,
                            s,
                            b.total_files
                        )
                        return {"user_id": u.id, "batch_id": b.id, "success": success}
                    except Exception as e:
                        return {"user_id": u.id, "batch_id": b.id, "success": False, "error": str(e)}
                
                task = assign_user(user, target_batch, slot)
                assignment_tasks.append(task)
            
            # Execute chunk assignments concurrently
            chunk_results = await asyncio.gather(*assignment_tasks, return_exceptions=True)
            assignment_results.extend(chunk_results)
            
            # Monitor progress
            metrics = monitor.get_metrics()
            successful_assignments = sum(1 for r in assignment_results if isinstance(r, dict) and r.get("success"))
            print(f"Assigned {successful_assignments} users, Time: {metrics['elapsed_time']:.2f}s, Memory: {metrics['current_memory_mb']:.2f}MB")
        
        final_metrics = monitor.get_metrics()
        
        # Analyze results
        successful_assignments = [r for r in assignment_results if isinstance(r, dict) and r.get("success")]
        failed_assignments = [r for r in assignment_results if isinstance(r, dict) and not r.get("success")]
        
        # Performance assertions
        assert final_metrics["elapsed_time"] < 60.0, f"Bulk user assignment took too long: {final_metrics['elapsed_time']:.2f}s"
        assert len(successful_assignments) >= 20, f"Too few successful assignments: {len(successful_assignments)}"
        
        # Verify database consistency
        for batch in bulk_batches:
            batch_state = await repository.get_batch_with_files(
                project.project_code,
                batch.id
            )
            
            if batch_state and batch_state.get("assignment_count", 0) > 0:
                assert batch_state["assignment_count"] <= batch.annotation_count, \
                    f"Batch {batch.id} over-assigned: {batch_state['assignment_count']} > {batch.annotation_count}"
    
    @pytest.mark.asyncio
    async def test_user_workload_calculation_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of calculating workloads for many users."""
        repository = BatchAssignmentRepository()
        project = large_dataset_environment["project"]
        large_users = large_dataset_environment["large_dataset_users"]
        monitor = PerformanceMonitor()
        
        # Create user allocations for workload testing
        workload_batches = []
        for i in range(20):  # 20 batches for workload testing
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"WORKLOAD_BATCH_{i}_{int(time.time())}",
                total_files=random.randint(10, 30),
                annotation_count=3,
                assignment_count=0
            )
            test_db.add(batch)
            workload_batches.append(batch)
        
        await test_db.commit()
        for batch in workload_batches:
            await test_db.refresh(batch)
        
        # Create user allocations
        test_users = large_users[:25]  # 25 users
        for user in test_users:
            project_user = test_factory.users.create_project_user(
                role="annotator",
                user_id=user.id,
                username=user.username
            )
            test_db.add(project_user)
        
        await test_db.commit()
        
        # Assign users to batches for workload calculation
        for i, user in enumerate(test_users):
            batch = workload_batches[i % len(workload_batches)]
            slot = (i % batch.annotation_count) + 1
            
            await repository.assign_user_to_batch(
                project.project_code,
                user.id,
                user.username,
                batch.id,
                slot,
                batch.total_files
            )
        
        monitor.start_monitoring()
        
        # Calculate workloads for all users
        workload_results = []
        
        # Process users in chunks
        chunk_size = 10
        for chunk_start in range(0, len(test_users), chunk_size):
            chunk_end = min(chunk_start + chunk_size, len(test_users))
            user_chunk = test_users[chunk_start:chunk_end]
            
            chunk_workloads = []
            for user in user_chunk:
                # Get user's current batch
                user_batch = await repository.get_user_current_batch(
                    project.project_code,
                    user.id
                )
                
                workload_info = {
                    "user_id": user.id,
                    "current_batch": user_batch,
                    "has_assignment": user_batch is not None
                }
                
                # If user has assignment, get batch details
                if user_batch:
                    batch_files = await repository.get_batch_files(
                        project.project_code,
                        user_batch
                    )
                    workload_info["file_count"] = len(batch_files)
                
                chunk_workloads.append(workload_info)
            
            workload_results.extend(chunk_workloads)
            
            # Monitor progress
            metrics = monitor.get_metrics()
            print(f"Calculated workload for {len(workload_results)} users, Time: {metrics['elapsed_time']:.2f}s")
        
        final_metrics = monitor.get_metrics()
        
        # Performance assertions
        assert final_metrics["elapsed_time"] < 30.0, f"Workload calculation took too long: {final_metrics['elapsed_time']:.2f}s"
        assert len(workload_results) == len(test_users)
        
        # Verify workload data
        assigned_users = [w for w in workload_results if w["has_assignment"]]
        assert len(assigned_users) >= len(test_users) * 0.8, "Most users should have assignments"
        
        # Calculate average workload processing time
        avg_processing_time = final_metrics["elapsed_time"] / len(test_users)
        assert avg_processing_time < 1.0, f"Average workload processing time too slow: {avg_processing_time:.3f}s per user"


@pytest.mark.integration
@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Takes time
class TestQueryOptimization:
    """PERFORMANCE TEST SUITE: Query optimization for large datasets."""
    
    @pytest.mark.asyncio
    async def test_complex_query_performance_real_database(
        self,
        large_dataset_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test performance of complex queries on large datasets."""
        project = large_dataset_environment["project"]
        monitor = PerformanceMonitor()
        
        # Create large dataset for query testing
        query_batches = []
        for i in range(50):
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"QUERY_BATCH_{i}_{int(time.time())}",
                total_files=random.randint(20, 100),
                annotation_count=random.randint(1, 5),
                assignment_count=random.randint(0, 3),
                is_priority=(i % 10 == 0),
                batch_status=random.choice([BatchStatus.CREATED, BatchStatus.ALLOCATED, BatchStatus.IN_PROGRESS])
            )
            test_db.add(batch)
            query_batches.append(batch)
        
        await test_db.commit()
        for batch in query_batches:
            await test_db.refresh(batch)
        
        monitor.start_monitoring()
        
        # Test various complex queries
        query_results = {}
        
        # Query 1: Batches with available slots
        query1_start = time.time()
        available_batches = await test_db.execute(
            select(AllocationBatches)
            .where(AllocationBatches.assignment_count < AllocationBatches.annotation_count)
            .order_by(AllocationBatches.created_at.desc())
            .limit(20)
        )
        available_batch_results = available_batches.scalars().all()
        query_results["available_batches"] = {
            "time": time.time() - query1_start,
            "count": len(available_batch_results)
        }
        
        # Query 2: Priority batches with specific status
        query2_start = time.time()
        priority_batches = await test_db.execute(
            select(AllocationBatches)
            .where(
                and_(
                    AllocationBatches.is_priority == True,
                    AllocationBatches.batch_status.in_([BatchStatus.CREATED, BatchStatus.ALLOCATED])
                )
            )
            .order_by(AllocationBatches.created_at)
        )
        priority_batch_results = priority_batches.scalars().all()
        query_results["priority_batches"] = {
            "time": time.time() - query2_start,
            "count": len(priority_batch_results)
        }
        
        # Query 3: Aggregate statistics
        query3_start = time.time()
        stats_query = await test_db.execute(
            select(
                func.count(AllocationBatches.id).label("total_batches"),
                func.sum(AllocationBatches.total_files).label("total_files"),
                func.avg(AllocationBatches.annotation_count).label("avg_annotation_count"),
                func.count().filter(AllocationBatches.is_priority == True).label("priority_count")
            )
        )
        stats_result = stats_query.first()
        query_results["aggregate_stats"] = {
            "time": time.time() - query3_start,
            "stats": dict(stats_result._mapping) if stats_result else {}
        }
        
        # Query 4: Complex join with files (simulated)
        query4_start = time.time()
        batch_file_counts = await test_db.execute(
            select(
                AllocationBatches.id,
                AllocationBatches.batch_identifier,
                func.count(FilesRegistry.id).label("actual_file_count")
            )
            .outerjoin(FilesRegistry, AllocationBatches.id == FilesRegistry.batch_id)
            .group_by(AllocationBatches.id, AllocationBatches.batch_identifier)
            .having(func.count(FilesRegistry.id) > 0)
            .limit(20)
        )
        batch_file_results = batch_file_counts.all()
        query_results["batch_file_counts"] = {
            "time": time.time() - query4_start,
            "count": len(batch_file_results)
        }
        
        final_metrics = monitor.get_metrics()
        
        # Performance assertions for each query
        assert query_results["available_batches"]["time"] < 1.0, f"Available batches query too slow: {query_results['available_batches']['time']:.3f}s"
        assert query_results["priority_batches"]["time"] < 1.0, f"Priority batches query too slow: {query_results['priority_batches']['time']:.3f}s"
        assert query_results["aggregate_stats"]["time"] < 2.0, f"Aggregate stats query too slow: {query_results['aggregate_stats']['time']:.3f}s"
        assert query_results["batch_file_counts"]["time"] < 3.0, f"Batch file counts query too slow: {query_results['batch_file_counts']['time']:.3f}s"
        
        # Overall performance
        assert final_metrics["elapsed_time"] < 10.0, f"All complex queries took too long: {final_metrics['elapsed_time']:.2f}s"
        
        # Verify query results
        assert query_results["available_batches"]["count"] >= 0
        assert query_results["priority_batches"]["count"] >= 0
        
        stats = query_results["aggregate_stats"]["stats"]
        if stats:
            assert stats["total_batches"] >= 50
            assert stats["total_files"] > 0
    
    @pytest.mark.asyncio
    async def test_database_connection_pooling_under_load_real_database(
        self,
        large_dataset_environment,
        setup_test_database
    ):
        """Test database connection pooling under heavy load."""
        repository = ProjectDBRepository()
        project = large_dataset_environment["project"]
        monitor = PerformanceMonitor()
        
        monitor.start_monitoring()
        
        # Simulate high concurrent load
        concurrent_operations = 20
        operations_per_worker = 10
        
        async def database_worker(worker_id):
            """Worker that performs multiple database operations."""
            worker_results = []
            
            for op_id in range(operations_per_worker):
                try:
                    # Mix of operations to test connection pooling
                    if op_id % 3 == 0:
                        # Create batch
                        batch_data = {
                            'batch_identifier': f'POOL_TEST_W{worker_id}_OP{op_id}_{int(time.time())}',
                            'total_files': random.randint(5, 15),
                            'file_list': [f'pool_file_{i}.jpg' for i in range(5)],
                            'annotation_count': 1
                        }
                        result = await repository.create_allocation_batch(
                            project.project_code,
                            batch_data
                        )
                        worker_results.append({"operation": "create_batch", "success": True, "batch_id": result['id']})
                    
                    elif op_id % 3 == 1:
                        # Get all batches
                        batches = await repository.get_allocation_batches(project.project_code)
                        worker_results.append({"operation": "get_batches", "success": True, "count": len(batches)})
                    
                    else:
                        # Register files (if we have batches)
                        all_batches = await repository.get_allocation_batches(project.project_code)
                        if all_batches:
                            target_batch = random.choice(all_batches)
                            files_data = [{
                                'file_identifier': f'pool_worker_{worker_id}_op_{op_id}_file.jpg',
                                'original_filename': f'pool_test.jpg',
                                'file_type': 'image',
                                'file_size_bytes': 1024,
                                'storage_location': {'type': 'test'}
                            }]
                            
                            files = await repository.register_files(
                                project.project_code,
                                target_batch['id'],
                                files_data
                            )
                            worker_results.append({"operation": "register_files", "success": True, "files": len(files)})
                        else:
                            worker_results.append({"operation": "register_files", "success": False, "reason": "no_batches"})
                
                except Exception as e:
                    worker_results.append({"operation": f"op_{op_id}", "success": False, "error": str(e)})
                
                # Small delay between operations
                await asyncio.sleep(0.01)
            
            return {"worker_id": worker_id, "results": worker_results}
        
        # Start all workers concurrently
        worker_tasks = []
        for worker_id in range(concurrent_operations):
            task = database_worker(worker_id)
            worker_tasks.append(task)
        
        # Execute all workers
        worker_results = await asyncio.gather(*worker_tasks, return_exceptions=True)
        
        final_metrics = monitor.get_metrics()
        
        # Analyze results
        successful_workers = [r for r in worker_results if isinstance(r, dict) and "worker_id" in r]
        failed_workers = [r for r in worker_results if not isinstance(r, dict) or "worker_id" not in r]
        
        total_operations = 0
        successful_operations = 0
        
        for worker_result in successful_workers:
            worker_ops = worker_result.get("results", [])
            total_operations += len(worker_ops)
            successful_operations += sum(1 for op in worker_ops if op.get("success"))
        
        # Performance assertions
        assert final_metrics["elapsed_time"] < 30.0, f"Connection pooling test took too long: {final_metrics['elapsed_time']:.2f}s"
        assert len(successful_workers) >= concurrent_operations * 0.9, f"Too many workers failed: {len(failed_workers)} out of {concurrent_operations}"
        
        success_rate = successful_operations / total_operations if total_operations > 0 else 0
        assert success_rate >= 0.8, f"Operation success rate too low: {success_rate:.2%}"
        
        # Performance per operation
        ops_per_second = total_operations / final_metrics["elapsed_time"] if final_metrics["elapsed_time"] > 0 else 0
        assert ops_per_second >= 10, f"Operations per second too low: {ops_per_second:.2f}"
        
        print(f"Connection pooling test completed: {total_operations} operations, {ops_per_second:.2f} ops/sec, {success_rate:.2%} success rate")
