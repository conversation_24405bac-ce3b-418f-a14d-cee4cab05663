"""
Unified Media Routes

Handles media serving for both annotator and verifier roles.
Supports streaming for MinIO and direct serving for other storage types.
"""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Request #type:ignore
from fastapi.responses import Response #type:ignore
from sqlalchemy import text

from dependencies.auth import get_current_user
from services.media_streaming_service import media_streaming_service
from services.project_batch_service import ProjectBatchService
from core.session_manager import get_project_db_session
from cache.redis_connector import cache_get, cache_set

logger = logging.getLogger('media_routes')

router = APIRouter(
    prefix="/media",
    tags=["Media"]
)


async def get_user_project_code(username: str) -> Optional[str]:
    """Get project_code for the user's current allocation."""
    try:
        batch_manager = ProjectBatchService()
        user_allocation = await batch_manager.get_user_current_allocation(username)
        
        if not user_allocation or not user_allocation.get('project_code'):
            logger.warning(f"No active allocation found for user: {username}")
            return None
            
        project_code = user_allocation['project_code']
        logger.info(f"Found project code for user {username}: {project_code}")
        return project_code
        
    except Exception as e:
        logger.error(f"Error getting project code for user {username}: {e}")
        return None


@router.get("/stream-url/{media_type}/{media_path:path}")
async def get_media_streaming_url(
    media_type: str, 
    media_path: str, 
    current_user: dict = Depends(get_current_user),
    expires_in: int = 3600
):
    """
    Get optimized streaming URL for media files.
    For MinIO: Returns presigned URL for direct streaming
    For other storage: Returns API endpoint URL
    
    Supported media types: video, audio
    """
    try:
        username = current_user["sub"]
        
        # Validate media type
        if media_type not in ['video', 'audio']:
            raise HTTPException(status_code=400, detail=f"Unsupported media type: {media_type}")
        
        # Get user's project code
        project_code = await get_user_project_code(username)
        if not project_code:
            raise HTTPException(status_code=404, detail="No active project found for user")
        
        # Get streaming URL
        streaming_info = await media_streaming_service.get_streaming_url(
            media_path=media_path,
            media_type=media_type,
            project_code=project_code,
            expires_in=expires_in
        )
        
        return streaming_info
        
    except Exception as e:
        logger.error(f"Error getting streaming URL for {media_type}/{media_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/image/{media_path:path}")
async def serve_image(
    media_path: str, 
    current_user: dict = Depends(get_current_user)
):
    """Serve an image file"""
    return await _serve_media("image", media_path, current_user)


@router.get("/video/{media_path:path}")
async def serve_video(
    media_path: str, 
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Serve a video file with streaming support"""
    return await _serve_media("video", media_path, current_user, request)


@router.get("/audio/{media_path:path}")
async def serve_audio(
    media_path: str, 
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Serve an audio file with streaming support"""
    return await _serve_media("audio", media_path, current_user, request)


@router.get("/pdf/{media_path:path}")
@router.head("/pdf/{media_path:path}")
async def serve_pdf(
    media_path: str, 
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Serve a PDF file"""
    return await _serve_media("pdf", media_path, current_user, request)


@router.get("/text/{media_path:path}")
@router.head("/text/{media_path:path}")
async def serve_text(
    media_path: str, 
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Serve a text file"""
    return await _serve_media("text", media_path, current_user, request)


@router.get("/csv/{media_path:path}")
@router.head("/csv/{media_path:path}")
async def serve_csv(
    media_path: str, 
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Serve a CSV file"""
    return await _serve_media("csv", media_path, current_user, request)


@router.get("/csv-batch/{project_code}/{batch_id}")
async def get_csv_batch_data(
    project_code: str,
    batch_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get CSV data for a specific batch from files_registry"""
    try:
        username = current_user["sub"]
        
        # Verify user has access to this project
        batch_manager = ProjectBatchService()
        user_allocation = await batch_manager.get_user_current_allocation(username)
        
        if not user_allocation or user_allocation.get('project_code') != project_code:
            raise HTTPException(status_code=403, detail="Access denied to this project")
        
        # Get CSV data from batch
        csv_data = await _get_csv_batch_data(project_code, batch_id)
        
        return csv_data
        
    except Exception as e:
        logger.error(f"Error getting CSV batch data for {project_code}/{batch_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def _serve_media(
    media_type: str, 
    media_path: str, 
    current_user: dict, 
    request: Optional[Request] = None
) -> Response:
    """Internal helper to serve media files"""
    try:
        username = current_user["sub"]
        
        # Get user's project code
        project_code = await get_user_project_code(username)
        if not project_code:
            raise HTTPException(status_code=404, detail="No active project found for user")
        
        # Stream media using the service
        return await media_streaming_service.stream_media(
            media_path=media_path,
            media_type=media_type,
            project_code=project_code,
            request=request
        )
        
    except Exception as e:
        logger.error(f"Error serving {media_type} {media_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def _get_csv_batch_data(project_code: str, batch_id: int) -> Dict[str, Any]:
    """Get CSV data for a batch from files_registry table"""
    
    # Check cache first
    cache_key = f"csv_batch_data:{project_code}:{batch_id}"
    cached_data = await cache_get(cache_key)
    if cached_data:
        logger.info(f"Serving CSV batch data from cache: {project_code}/{batch_id}")
        return cached_data
    
    try:
        # Get project database connection
        async with get_project_db_session(project_code) as session:
            # Query files_registry for this batch
            query = """
                SELECT file_identifier, original_filename, sequence_order
                FROM files_registry 
                WHERE batch_id = :batch_id 
                ORDER BY sequence_order ASC, id ASC
            """
            
            result = await session.execute(
                text(query), 
                {"batch_id": batch_id}
            )
            files = result.fetchall()
            
            if not files:
                raise HTTPException(status_code=404, detail=f"No files found for batch {batch_id}")
            
            # For CSV projects, file_identifier contains the actual CSV row data as plain text
            headers = []
            rows = []
            
            for i, file_record in enumerate(files):
                file_identifier = file_record.file_identifier
                
                # Parse the CSV row data from file_identifier
                # Assuming it's stored as comma-separated values
                row_data = [cell.strip() for cell in file_identifier.split(',')]
                
                if i == 0:
                    # First row is headers
                    headers = row_data
                else:
                    rows.append(row_data)
            
            csv_data = {
                "headers": headers,
                "rows": rows,
                "totalRows": len(rows)
            }
            
            # Cache the result for 1 hour
            await cache_set(cache_key, csv_data, expire_seconds=3600)
            logger.info(f"Cached CSV batch data: {project_code}/{batch_id}")
            
            return csv_data
            
    except Exception as e:
        logger.error(f"Error getting CSV batch data for {project_code}/{batch_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get CSV batch data: {str(e)}")
