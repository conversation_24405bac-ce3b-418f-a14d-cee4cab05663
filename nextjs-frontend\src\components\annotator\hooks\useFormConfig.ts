"use client";

import { useState, useCallback } from "react";
import { authFetch } from "@/lib/authFetch";
import { FormFieldConfig } from "@/components/shared/dynamic-fields";

import { API_BASE_URL } from "@/lib/api";

const API_BASE = API_BASE_URL;

export function useFormConfig() {
  const [formConfig, setFormConfig] = useState<FormFieldConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadFormConfig = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("Loading form configuration...");
      const response = await authFetch(`${API_BASE}/annotator/form-config`, {
        credentials: "include",
      });
      
      console.log("Form config response status:", response.status);
      
      if (!response.ok) {
        console.error("Form config API error:", response.status, response.statusText);
        setFormConfig([]);
        return { success: false, error: `API error: ${response.status}` };
      }
      
      const data = await response.json();
      console.log("Form config API response:", data);
      
      if (data.success && data.data && data.data.fields) {
        console.log("Setting form config with", data.data.fields.length, "fields:", data.data.fields);
        setFormConfig(data.data.fields);
        return { success: true, data: data.data.fields };
      } else {
        console.log("No form config found or invalid response structure");
        setFormConfig([]);
        return { success: true, data: [] };
      }
    } catch (err: unknown) {
      console.error("Error loading form configuration:", err);
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      setFormConfig([]);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    formConfig,
    isLoading,
    error,
    loadFormConfig,
    setFormConfig
  };
}
