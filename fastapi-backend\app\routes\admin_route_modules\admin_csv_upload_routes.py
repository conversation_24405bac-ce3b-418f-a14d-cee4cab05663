import logging
import os
import tempfile
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form #type:ignore
from fastapi.responses import JSONResponse #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from dependencies.auth import get_current_active_user, require_admin
from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select, update
from minio import Minio  #type:ignore
from datetime import datetime
import uuid
import pandas as pd
import io
from cache.redis_connector import cache_set, cache_get
from core.config import get_settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/admin",
    tags=["Admin - CSV Upload"],
)

class CSVConfigurationRequest(BaseModel):
    selectedColumn: str
    recordsPerBatch: int

def extract_csv_metadata_from_minio(minio_client: Minio, bucket_name: str, object_path: str, filename: str) -> Dict[str, Any]:
    """
    Extract metadata from CSV file stored in MinIO bucket by streaming.
    
    Args:
        minio_client: MinIO client instance
        bucket_name: Name of the MinIO bucket
        object_path: Path to the object in the bucket
        filename: Original filename to determine file type
        
    Returns:
        Dict containing column names, row count, and other metadata
    """
    try:
        file_extension = os.path.splitext(filename)[1].lower()
        
        # Stream the file from MinIO bucket without loading into memory
        response = minio_client.get_object(bucket_name, object_path)
        
        # Read based on file type - stream directly from MinIO response
        try:
            if file_extension == '.csv':
                # Try different encodings for CSV files
                encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
                df = None
                used_encoding = None
                
                for encoding in encodings:
                    try:
                        # Read directly from streaming response, only first 1000 rows
                        df = pd.read_csv(response, encoding=encoding, nrows=1000)
                        used_encoding = encoding
                        break
                    except (UnicodeDecodeError, pd.errors.ParserError):
                        # Reset the stream for next encoding attempt
                        response.close()
                        response = minio_client.get_object(bucket_name, object_path)
                        continue
                        
                if df is None:
                    raise Exception("Unable to read CSV file with any supported encoding")
                    
            elif file_extension in ['.xlsx', '.xls']:
                # For Excel files, we unfortunately need to buffer since pandas doesn't support streaming Excel
                file_buffer = io.BytesIO(response.read())
                df = pd.read_excel(file_buffer, nrows=1000)  # Read first 1000 rows for metadata
                used_encoding = 'excel'
            else:
                raise Exception(f"Unsupported file type: {file_extension}")
                
        finally:
            # Always close the response stream
            response.close()
        
        # Extract metadata
        columns = df.columns.tolist()
        
        # Clean column names (remove extra whitespace, handle unnamed columns)
        cleaned_columns = []
        for i, col in enumerate(columns):
            if pd.isna(col) or str(col).startswith('Unnamed:'):
                cleaned_columns.append(f"Column_{i+1}")
            else:
                cleaned_columns.append(str(col).strip())
        
        # Get basic statistics
        total_rows = len(df)
        
        # Sample data types
        dtypes_info = {}
        for col in cleaned_columns[:10]:  # Limit to first 10 columns for performance
            if col in df.columns or col.replace('Column_', 'Unnamed: ') in df.columns:
                try:
                    original_col = col if col in df.columns else f"Unnamed: {cleaned_columns.index(col)}"
                    dtype = str(df[original_col].dtype)
                    dtypes_info[col] = dtype
                except:
                    dtypes_info[col] = 'object'
        
        return {
            "columns": cleaned_columns,
            "total_columns": len(cleaned_columns),
            "sample_rows": total_rows,
            "encoding": used_encoding,
            "column_types": dtypes_info,
            "file_extension": file_extension
        }
        
    except Exception as e:
        logger.error(f"Error extracting CSV metadata: {str(e)}")
        # Return basic metadata even if extraction fails
        return {
            "columns": [],
            "total_columns": 0,
            "sample_rows": 0,
            "encoding": "unknown",
            "column_types": {},
            "file_extension": os.path.splitext(filename)[1].lower(),
            "error": str(e)
        }

@router.post("/upload-csv/{project_code}")
async def upload_csv_file(
    project_code: str,
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Upload a CSV file directly to MinIO storage using project's stored credentials.
    
    Args:
        project_code: Project code to get MinIO credentials from
        file: The CSV/Excel file to upload
        
    Returns:
        Dict with upload success status and file information
    """
    try:
        logger.info(f"CSV upload request received for project: {project_code}")
        logger.info(f"File: {file.filename}, Content type: {file.content_type}")
        
        # Validate file type
        allowed_extensions = ['.csv', '.xlsx', '.xls']
        file_extension = os.path.splitext(file.filename or '')[1].lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file type. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        # Validate file size (50MB limit) - read content once for size check
        max_size = 50 * 1024 * 1024  # 50MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail="File size exceeds 50MB limit"
            )
        
        # Get project information to validate it's a CSV project
        try:
            query = select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            result = await db.execute(query)
            project_info = result.scalar_one_or_none()
            
            if not project_info:
                raise HTTPException(
                    status_code=404,
                    detail=f"Project not found: {project_code}"
                )
            
            if project_info.project_type != 'csv':
                raise HTTPException(
                    status_code=400,
                    detail=f"Project {project_code} is not a CSV project type"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching project info: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve project information"
            )
        
        # Get MinIO configuration from centralized config
        settings = get_settings()
        minio_config = settings.minio_settings
        
        endpoint = minio_config.endpoint
        access_key = minio_config.access_key
        secret_key = minio_config.secret_key
        bucket_name = minio_config.csv_bucket_name
        region = minio_config.region
        secure = minio_config.secure
        
        try:
            
            minio_client = Minio(
                endpoint=endpoint,
                access_key=access_key,
                secret_key=secret_key,
                secure=secure,
                region=region if region else None
            )
            
            logger.info(f"Created MinIO client for endpoint: {endpoint}")
            
        except ImportError as e:
            logger.error(f"MinIO library not installed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="MinIO library not available. Please install minio package."
            )
        except Exception as e:
            logger.error(f"Failed to create MinIO client: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to connect to MinIO storage: {str(e)}"
            )
                   
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        base_name = os.path.splitext(file.filename or 'file')[0]
        safe_filename = f"{base_name}_{timestamp}_{unique_id}{file_extension}"
        
        # Construct full object path (fixed folder for CSV uploads)
        folder_path = "csv-uploads" + "/" + project_code
        object_path = f"{folder_path}/{safe_filename}"
        
        # Upload file to MinIO first
        try:
            # Create a temporary file to upload
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            # Upload to MinIO
            minio_client.fput_object(
                bucket_name=bucket_name,
                object_name=object_path,
                file_path=temp_file_path,
                content_type=file.content_type or 'application/octet-stream'
            )
            
            # Clean up temporary file
            os.unlink(temp_file_path)
            
            logger.info(f"Successfully uploaded {file.filename} to {bucket_name}/{object_path}")
            
        except Exception as e:
            logger.error(f"Failed to upload file to MinIO: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload file: {str(e)}"
            )
        
        # Now extract CSV metadata by streaming from MinIO bucket
        try:
            csv_metadata = extract_csv_metadata_from_minio(
                minio_client, bucket_name, object_path, file.filename or 'unknown.csv'
            )
            logger.info(f"Extracted CSV metadata: {csv_metadata.get('total_columns', 0)} columns, {csv_metadata.get('sample_rows', 0)} rows")
            
        except Exception as e:
            logger.error(f"Failed to extract CSV metadata: {str(e)}")
            # Try to clean up the uploaded file since metadata extraction failed
            try:
                minio_client.remove_object(bucket_name, object_path)
                logger.info(f"Cleaned up uploaded file {object_path} after metadata extraction failure")
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup uploaded file after metadata extraction failure: {str(cleanup_error)}")
            
            raise HTTPException(
                status_code=500,
                detail=f"Failed to process uploaded file: {str(e)}"
            )
        
        # Update project registry with CSV file path
        try:
            await db.execute(
                update(ProjectsRegistry)
                .where(ProjectsRegistry.project_code == project_code)
                .values(folder_path=object_path)
            )
            await db.commit()
            logger.info(f"Updated project {project_code} folder_path to: {object_path}")
        except Exception as e:
            logger.error(f"Failed to update project registry with CSV file path: {str(e)}")
            # Continue anyway - the file was uploaded successfully
        
        # Return success response with CSV metadata
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "File uploaded successfully",
                "file_info": {
                    "original_filename": file.filename,
                    "uploaded_filename": safe_filename,
                    "file_path": object_path,
                    "bucket_name": bucket_name,
                    "file_size": len(file_content),
                    "content_type": file.content_type,
                    "upload_timestamp": datetime.now().isoformat()
                },
                "csv_metadata": csv_metadata
            }
        )
        
    except HTTPException as he:
        logger.error(f"HTTP Exception in CSV upload: {he.status_code} - {he.detail}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in CSV upload: {str(e)}")
        logger.exception("Full traceback:")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred during file upload: {str(e)}"
        )

@router.post("/configure-csv/{project_code}")
async def configure_csv_project(
    project_code: str,
    config: CSVConfigurationRequest,
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Configure CSV project with selected column and batch size.
    Stores configuration in Redis for later use during project provisioning.
    
    Args:
        project_code: Project code to configure
        config: CSV configuration with selected column and batch size
        
    Returns:
        Dict with configuration success status
    """
    try:
        logger.info(f"CSV configuration request for project: {project_code}")
        logger.info(f"Selected column: {config.selectedColumn}, Batch size: {config.recordsPerBatch}")
        
        # Validate project exists and is CSV type
        try:
            query = select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            result = await db.execute(query)
            project_info = result.scalar_one_or_none()
            
            if not project_info:
                raise HTTPException(
                    status_code=404,
                    detail=f"Project not found: {project_code}"
                )
            
            if project_info.project_type != 'csv':
                raise HTTPException(
                    status_code=400,
                    detail=f"Project {project_code} is not a CSV project type"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error fetching project info: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve project information"
            )
        
        # Store configuration in Redis with a reasonable TTL (24 hours)
        redis_key = f"csv_config:{project_code}"
        csv_config = {
            "project_code": project_code,
            "selected_column": config.selectedColumn,
            "records_per_batch": config.recordsPerBatch,
            "configured_at": datetime.now().isoformat(),
            "configured_by": current_user.get("username", "unknown")
        }
        
        # Store in Redis with 24 hour expiration
        success = await cache_set(redis_key, csv_config, expire_seconds=86400)
        
        if not success:
            logger.warning(f"Failed to store CSV configuration in Redis for project {project_code}")
            # Continue anyway - configuration can still work without Redis
        
        logger.info(f"CSV configuration stored for project {project_code}: column={config.selectedColumn}, batch_size={config.recordsPerBatch}")
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "CSV project configured successfully",
                "configuration": {
                    "project_code": project_code,
                    "selected_column": config.selectedColumn,
                    "records_per_batch": config.recordsPerBatch
                }
            }
        )
        
    except HTTPException as he:
        logger.error(f"HTTP Exception in CSV configuration: {he.status_code} - {he.detail}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in CSV configuration: {str(e)}")
        logger.exception("Full traceback:")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred during CSV configuration: {str(e)}"
        )

