"""
Comprehensive storage tests for AIProcessingService.
Tests AI processing with MinIO, NAS-FTP storage backends and file content retrieval.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List, Optional
import io
import time
from datetime import datetime

from app.services.ai_processing_service import AIProcessingService

class TestAIProcessingServiceStorage:
    """Storage-focused tests for AIProcessingService."""
    
    @pytest.fixture
    def ai_service(self):
        """AIProcessingService instance for testing."""
        return AIProcessingService()
    
    @pytest.fixture
    def storage_file_data(self):
        """Mock file data for different storage types."""
        return {
            'minio_image': {
                'file_identifier': '/ai/minio/image_analysis.jpg',
                'original_filename': 'analysis_image.jpg',
                'file_size': 5 * 1024 * 1024,  # 5MB
                'storage_info': {
                    'connector_type': 'MinIO',
                    'bucket': 'ai-processing',
                    'connector': MagicMock()
                },
                'content_type': 'image/jpeg'
            },
            'nas_video': {
                'file_identifier': '/ai/nas/video_process.mp4',
                'original_filename': 'process_video.mp4',
                'file_size': 100 * 1024 * 1024,  # 100MB
                'storage_info': {
                    'connector_type': 'NAS-FTP',
                    'base_path': '/nas/ai-files',
                    'connector': MagicMock()
                },
                'content_type': 'video/mp4'
            },
            'large_dataset': {
                'file_identifier': '/ai/large/dataset.zip',
                'original_filename': 'ml_dataset.zip',
                'file_size': 1024 * 1024 * 1024,  # 1GB
                'storage_info': {
                    'connector_type': 'MinIO',
                    'bucket': 'large-datasets',
                    'connector': MagicMock()
                },
                'content_type': 'application/zip'
            }
        }
    
    @pytest.fixture
    def mock_ai_models(self):
        """Mock AI model configurations."""
        return {
            'image_classification': {
                'model_name': 'resnet50',
                'input_types': ['image/jpeg', 'image/png'],
                'max_file_size': 50 * 1024 * 1024,  # 50MB
                'processing_time_estimate': 2.5  # seconds
            },
            'video_analysis': {
                'model_name': 'video_cnn',
                'input_types': ['video/mp4', 'video/avi'],
                'max_file_size': 500 * 1024 * 1024,  # 500MB
                'processing_time_estimate': 30.0  # seconds
            },
            'document_analysis': {
                'model_name': 'bert_doc',
                'input_types': ['application/pdf', 'text/plain'],
                'max_file_size': 10 * 1024 * 1024,  # 10MB
                'processing_time_estimate': 5.0  # seconds
            }
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_process_single_file_minio_storage(self, ai_service, storage_file_data, mock_ai_models):
        """Test AI processing of single file from MinIO storage."""
        file_data = storage_file_data['minio_image']
        model_config = mock_ai_models['image_classification']
        
        # Mock file content retrieval from MinIO
        mock_file_content = b'mock_image_data' * 1000  # Simulate image data
        file_data['storage_info']['connector'].get_file_content.return_value = mock_file_content
        
        with patch.object(ai_service, '_process_ai_service_call') as mock_ai_call:
            mock_ai_call.return_value = {
                'success': True,
                'results': {'classification': 'cat', 'confidence': 0.95},
                'processing_time': model_config['processing_time_estimate'],
                'model_used': model_config['model_name']
            }
            
            with patch.object(ai_service, '_store_processing_results') as mock_store:
                mock_store.return_value = {'stored': True, 'result_id': 'AI_RES_001'}
                
                result = await ai_service._process_single_file(
                    'MINIO_AI_001',
                    file_data,
                    model_config['model_name'],
                    'image_classification',
                    user_prompt=None,
                    system_prompt=None
                )
                
                assert result['success'] is True
                assert result['storage_type'] == 'MinIO'
                assert result['file_retrieved_successfully'] is True
                assert result['processing_results']['classification'] == 'cat'
                assert file_data['storage_info']['connector'].get_file_content.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_process_single_file_nas_storage(self, ai_service, storage_file_data, mock_ai_models):
        """Test AI processing of single file from NAS-FTP storage."""
        file_data = storage_file_data['nas_video']
        model_config = mock_ai_models['video_analysis']
        
        # Mock file content retrieval from NAS-FTP (slower than MinIO)
        mock_file_content = b'mock_video_data' * 10000  # Simulate larger video data
        file_data['storage_info']['connector'].get_file_content.return_value = mock_file_content
        
        # Simulate slower NAS retrieval
        async def slow_get_content(file_path):
            await asyncio.sleep(0.5)  # Simulate network delay
            return mock_file_content
        
        file_data['storage_info']['connector'].get_file_content.side_effect = slow_get_content
        
        with patch.object(ai_service, '_process_ai_service_call') as mock_ai_call:
            mock_ai_call.return_value = {
                'success': True,
                'results': {'objects_detected': ['person', 'car'], 'frame_count': 1800},
                'processing_time': model_config['processing_time_estimate'],
                'model_used': model_config['model_name']
            }
            
            start_time = time.time()
            result = await ai_service._process_single_file(
                'NAS_AI_001',
                file_data,
                model_config['model_name'],
                'video_analysis'
            )
            end_time = time.time()
            
            assert result['success'] is True
            assert result['storage_type'] == 'NAS-FTP'
            assert result['file_retrieved_successfully'] is True
            # Should take longer due to NAS retrieval time
            assert (end_time - start_time) > 0.4

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_failure_handling(self, ai_service, storage_file_data):
        """Test handling of storage failures during AI processing."""
        file_data = storage_file_data['minio_image']
        
        storage_failures = [
            {'error': 'ConnectionTimeout', 'retry_count': 3, 'recoverable': True},
            {'error': 'FileNotFound', 'retry_count': 1, 'recoverable': False},
            {'error': 'PermissionDenied', 'retry_count': 1, 'recoverable': False},
            {'error': 'StorageQuotaExceeded', 'retry_count': 0, 'recoverable': False}
        ]
        
        for failure in storage_failures:
            # Reset mock for each test
            file_data['storage_info']['connector'].reset_mock()
            
            if failure['recoverable']:
                # Fail first few attempts, then succeed
                side_effects = [Exception(failure['error'])] * failure['retry_count']
                side_effects.append(b'mock_file_content')
                file_data['storage_info']['connector'].get_file_content.side_effect = side_effects
            else:
                # Always fail
                file_data['storage_info']['connector'].get_file_content.side_effect = Exception(failure['error'])
            
            with patch.object(ai_service, '_handle_storage_error') as mock_handle:
                mock_handle.return_value = {
                    'error_handled': True,
                    'retry_attempted': failure['recoverable'],
                    'final_success': failure['recoverable']
                }
                
                try:
                    result = await ai_service._process_single_file(
                        'STORAGE_FAIL_001',
                        file_data,
                        'test_model',
                        'test_processing'
                    )
                    
                    if failure['recoverable']:
                        assert result['success'] is True
                        assert result['recovery_attempted'] is True
                    
                except Exception as e:
                    if not failure['recoverable']:
                        assert failure['error'] in str(e)
                    else:
                        pytest.fail(f"Recoverable failure {failure['error']} should not raise exception")

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_large_file_processing_performance(self, ai_service, storage_file_data, performance_monitor):
        """Test performance of AI processing with large files from different storage types."""
        large_file = storage_file_data['large_dataset']
        
        # Test with different storage types
        storage_types = ['MinIO', 'NAS-FTP']
        performance_results = {}
        
        for storage_type in storage_types:
            large_file['storage_info']['connector_type'] = storage_type
            
            # Mock different retrieval speeds
            mock_content = b'large_file_content' * 100000  # Simulate 1GB+ content
            
            if storage_type == 'MinIO':
                # MinIO - faster retrieval
                large_file['storage_info']['connector'].get_file_content.return_value = mock_content
            else:
                # NAS-FTP - slower retrieval
                async def slow_nas_retrieval(file_path):
                    await asyncio.sleep(1.0)  # Simulate slower network
                    return mock_content
                
                large_file['storage_info']['connector'].get_file_content.side_effect = slow_nas_retrieval
            
            performance_monitor.start()
            
            with patch.object(ai_service, '_process_ai_service_call') as mock_ai_call:
                mock_ai_call.return_value = {
                    'success': True,
                    'results': {'processed': True},
                    'processing_time': 10.0
                }
                
                result = await ai_service._process_single_file(
                    f'PERF_{storage_type}_001',
                    large_file,
                    'large_model',
                    'large_processing'
                )
                
                performance_monitor.stop()
                execution_time = performance_monitor.get_execution_time()
                
                performance_results[storage_type] = {
                    'execution_time': execution_time,
                    'success': result['success'],
                    'file_size': large_file['file_size']
                }
        
        # MinIO should be faster for large file retrieval
        if 'MinIO' in performance_results and 'NAS-FTP' in performance_results:
            minio_time = performance_results['MinIO']['execution_time']
            nas_time = performance_results['NAS-FTP']['execution_time']
            
            assert minio_time < nas_time, f"MinIO ({minio_time}s) should be faster than NAS ({nas_time}s)"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_ai_processing_mixed_storage(self, ai_service, storage_file_data):
        """Test concurrent AI processing with files from different storage types."""
        concurrent_files = [
            (storage_file_data['minio_image'], 'image_classification'),
            (storage_file_data['nas_video'], 'video_analysis'),
            (storage_file_data['minio_image'].copy(), 'image_classification'),  # Another MinIO file
        ]
        
        # Mock storage connectors for concurrent access
        for file_data, _ in concurrent_files:
            mock_content = b'mock_content_for_' + file_data['file_identifier'].encode()
            file_data['storage_info']['connector'].get_file_content.return_value = mock_content
        
        async def process_concurrent_file(file_data, processing_type):
            """Process a single file concurrently."""
            with patch.object(ai_service, '_process_ai_service_call') as mock_ai_call:
                mock_ai_call.return_value = {
                    'success': True,
                    'results': {'processed': True, 'file_id': file_data['file_identifier']},
                    'processing_time': 2.0
                }
                
                return await ai_service._process_single_file(
                    'CONCURRENT_001',
                    file_data,
                    'concurrent_model',
                    processing_type
                )
        
        # Execute all processing tasks concurrently
        tasks = [
            process_concurrent_file(file_data, proc_type) 
            for file_data, proc_type in concurrent_files
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify no exceptions occurred
        exceptions = [r for r in results if isinstance(r, Exception)]
        assert len(exceptions) == 0, f"Found exceptions: {exceptions}"
        
        # Verify all files processed successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(concurrent_files)
        
        for result in successful_results:
            assert result['success'] is True
            assert result['file_retrieved_successfully'] is True

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_connector_caching(self, ai_service, storage_file_data):
        """Test storage connector caching and reuse across AI processing operations."""
        # Multiple files from same storage project
        files_same_storage = [
            storage_file_data['minio_image'].copy(),
            storage_file_data['minio_image'].copy(),
            storage_file_data['minio_image'].copy()
        ]
        
        # Modify file identifiers to make them unique
        for i, file_data in enumerate(files_same_storage):
            file_data['file_identifier'] = f'/ai/minio/cached_file_{i}.jpg'
            file_data['original_filename'] = f'cached_file_{i}.jpg'
        
        connector_creation_count = 0
        
        def mock_connector_factory(*args, **kwargs):
            nonlocal connector_creation_count
            connector_creation_count += 1
            mock_connector = MagicMock()
            mock_connector.get_file_content.return_value = b'cached_content'
            return mock_connector
        
        with patch.object(ai_service, '_get_or_create_cached_connector') as mock_cached:
            mock_cached.side_effect = mock_connector_factory
            
            # Process all files
            for file_data in files_same_storage:
                with patch.object(ai_service, '_process_ai_service_call') as mock_ai_call:
                    mock_ai_call.return_value = {'success': True, 'results': {}}
                    
                    await ai_service._process_single_file(
                        'CACHE_TEST_001',
                        file_data,
                        'cached_model',
                        'cached_processing'
                    )
            
            # Connector should be created only once due to caching
            assert connector_creation_count <= 1, f"Expected 1 connector creation, got {connector_creation_count}"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_file_format_validation_by_storage_type(self, ai_service, storage_file_data):
        """Test file format validation specific to storage types."""
        validation_scenarios = [
            {
                'file_data': storage_file_data['minio_image'],
                'expected_formats': ['image/jpeg', 'image/png', 'image/gif'],
                'processing_type': 'image_classification',
                'should_validate': True
            },
            {
                'file_data': storage_file_data['nas_video'],
                'expected_formats': ['video/mp4', 'video/avi', 'video/mov'],
                'processing_type': 'video_analysis',
                'should_validate': True
            },
            {
                'file_data': storage_file_data['minio_image'].copy(),
                'expected_formats': ['application/pdf'],  # Wrong format for image file
                'processing_type': 'document_analysis',
                'should_validate': False
            }
        ]
        
        for scenario in validation_scenarios:
            with patch.object(ai_service, '_validate_file_format_for_storage') as mock_validate:
                mock_validate.return_value = {
                    'valid': scenario['should_validate'],
                    'detected_format': scenario['file_data']['content_type'],
                    'expected_formats': scenario['expected_formats'],
                    'storage_type': scenario['file_data']['storage_info']['connector_type']
                }
                
                validation_result = ai_service._validate_file_format_for_storage(
                    scenario['file_data'], 
                    scenario['processing_type']
                )
                
                assert validation_result['valid'] == scenario['should_validate']
                assert validation_result['detected_format'] == scenario['file_data']['content_type']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_cost_tracking(self, ai_service, storage_file_data):
        """Test cost tracking for AI processing across different storage types."""
        cost_configs = {
            'MinIO': {'per_gb_retrieval': 0.05, 'per_operation': 0.001},
            'NAS-FTP': {'per_gb_retrieval': 0.08, 'per_operation': 0.002}
        }
        
        processing_operations = [
            {'file': storage_file_data['minio_image'], 'operations': 5},
            {'file': storage_file_data['nas_video'], 'operations': 2},
            {'file': storage_file_data['large_dataset'], 'operations': 1}
        ]
        
        total_cost = 0
        cost_breakdown = {}
        
        for operation in processing_operations:
            file_data = operation['file']
            storage_type = file_data['storage_info']['connector_type']
            file_size_gb = file_data['file_size'] / (1024 * 1024 * 1024)
            
            with patch.object(ai_service, '_calculate_processing_cost') as mock_cost:
                # Calculate cost based on storage type and file size
                retrieval_cost = file_size_gb * cost_configs[storage_type]['per_gb_retrieval']
                operation_cost = operation['operations'] * cost_configs[storage_type]['per_operation']
                total_file_cost = retrieval_cost + operation_cost
                
                mock_cost.return_value = {
                    'storage_type': storage_type,
                    'file_size_gb': file_size_gb,
                    'retrieval_cost': retrieval_cost,
                    'operation_cost': operation_cost,
                    'total_cost': total_file_cost
                }
                
                cost_result = ai_service._calculate_processing_cost(
                    file_data, operation['operations'], cost_configs
                )
                
                cost_breakdown[file_data['file_identifier']] = cost_result
                total_cost += cost_result['total_cost']
        
        # Verify cost calculations
        assert total_cost > 0
        assert len(cost_breakdown) == len(processing_operations)
        
        # Large dataset should have highest cost due to size
        large_dataset_cost = cost_breakdown[storage_file_data['large_dataset']['file_identifier']]
        minio_image_cost = cost_breakdown[storage_file_data['minio_image']['file_identifier']]
        
        assert large_dataset_cost['total_cost'] > minio_image_cost['total_cost']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_security_validation(self, ai_service, storage_file_data, security_test_data):
        """Test security validation for files from different storage types."""
        security_scenarios = [
            {
                'file_path': '/safe/path/image.jpg',
                'storage_type': 'MinIO',
                'expected_safe': True
            },
            {
                'file_path': security_test_data['malicious_inputs'][2],  # Path traversal attempt
                'storage_type': 'NAS-FTP',
                'expected_safe': False
            },
            {
                'file_path': '/normal/file.pdf',
                'storage_type': 'MinIO',
                'expected_safe': True
            }
        ]
        
        for scenario in security_scenarios:
            test_file = storage_file_data['minio_image'].copy()
            test_file['file_identifier'] = scenario['file_path']
            test_file['storage_info']['connector_type'] = scenario['storage_type']
            
            with patch.object(ai_service, '_validate_file_security') as mock_security:
                mock_security.return_value = {
                    'safe': scenario['expected_safe'],
                    'security_issues': [] if scenario['expected_safe'] else ['path_traversal_detected'],
                    'storage_type': scenario['storage_type'],
                    'file_path': scenario['file_path']
                }
                
                security_result = ai_service._validate_file_security(test_file)
                
                assert security_result['safe'] == scenario['expected_safe']
                if not scenario['expected_safe']:
                    assert len(security_result['security_issues']) > 0

    @pytest.mark.unit
    def test_memory_usage_large_file_processing(self, ai_service, service_performance_data):
        """Test memory usage during large file AI processing."""
        import sys
        
        # Simulate processing multiple large files
        large_files = []
        for i in range(10):  # 10 files
            file_content = b'large_file_content' * 10000  # ~160KB per file
            file_metadata = {
                'file_id': f'large_file_{i}',
                'content': file_content,
                'size': len(file_content),
                'processing_results': {'ai_output': f'result_{i}' * 1000}  # Large AI results
            }
            large_files.append(file_metadata)
        
        initial_size = sys.getsizeof(large_files)
        
        # Simulate AI processing and result storage
        processed_results = {}
        for file_data in large_files:
            # Simulate AI processing creating additional data
            processed_results[file_data['file_id']] = {
                'original_size': file_data['size'],
                'processed_content': file_data['content'],  # Keep reference to original
                'ai_results': file_data['processing_results'],
                'metadata': {
                    'processing_time': 5.5,
                    'model_version': '1.0',
                    'confidence_scores': [0.95] * 100  # Large confidence array
                }
            }
        
        final_size = sys.getsizeof(processed_results)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['file_operations']
        assert memory_increase < max_memory, f"Memory usage {memory_increase}MB exceeds limit {max_memory}MB"
