export async function authFetch(input: RequestInfo, init: RequestInit = {}) {
  const headers = new Headers(init.headers || {});

  // Attach JWT from localStorage if present and header not already set
  if (!headers.has("Authorization")) {
    const token = (typeof window !== "undefined") ? localStorage.getItem("access_token") : null;
    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
  }
  
  return fetch(input, {
    ...init,
    headers,
    credentials: 'include', // Important: needed for cookies
  });
}