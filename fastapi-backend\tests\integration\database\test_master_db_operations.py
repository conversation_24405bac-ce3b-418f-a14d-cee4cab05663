"""
Integration tests for Master Database operations.
Tests user management, project registry, and cross-project functionality.

IMPORTANT: Master DB uses standard SQLAlchemy metadata creation (not DynamicSchemaGenerator).
Project DB operations in these tests should align with production architecture:
- user_id relationships managed by business logic, not database constraints
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from httpx import AsyncClient

from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.user_project_access import UserProjectAccess
from app.services.auth_service import AuthService
from app.schemas.UserSchemas import UserRegisterRequest
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.core_features    # Feature marker - Core database
@pytest.mark.smoke            # Suite marker - Core functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestMasterDatabaseOperations:
    """SMOKE TEST SUITE: Critical master database CRUD operations."""
    
    # REMOVED: Duplicate user management tests
    # → test_user_registration_and_retrieval() moved to test_admin_routes_database_integration.py
    # → test_duplicate_user_prevention() moved to test_api_database_integration.py
    
    @pytest.mark.asyncio
    async def test_client_creation_and_retrieval(self, test_master_db: AsyncSession, setup_test_database):
        """Test client creation and retrieval."""
        # Create client
        client = test_factory.projects.create_client()
        
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Verify client was created
        assert client.id is not None
        assert client.name == "Test Client Corp"
        assert client.username == "testclient"
        
        # Test retrieval
        stmt = select(Clients).where(Clients.username == "testclient")
        result = await test_master_db.execute(stmt)
        retrieved_client = result.scalar_one_or_none()
        
        assert retrieved_client is not None
        assert retrieved_client.name == "Test Client Corp"
        assert retrieved_client.contact_info["phone"] == "************"
    
    @pytest.mark.asyncio
    async def test_project_registry_operations(self, test_master_db: AsyncSession, sample_project_data, setup_test_database):
        """Test project registry CRUD operations."""
        # First create a client
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create project
        project = test_factory.projects.create_project(
            client.id,
            None,  # No allocation strategy
            project_code=sample_project_data["project_code"],
            project_name=sample_project_data["project_name"],
            project_type=sample_project_data["project_type"]
        )
        
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Verify project creation
        assert project.id is not None
        assert project.project_code == sample_project_data["project_code"]
        assert project.client_id == client.id
        
        # Test project retrieval
        stmt = select(ProjectsRegistry).where(
            ProjectsRegistry.project_code == sample_project_data["project_code"]
        )
        result = await test_master_db.execute(stmt)
        retrieved_project = result.scalar_one_or_none()
        
        assert retrieved_project is not None
        assert retrieved_project.project_name == sample_project_data["project_name"]
    
    @pytest.mark.asyncio
    async def test_user_project_access_management(self, test_master_db: AsyncSession, setup_test_database):
        """Test user-project access control."""
        # Create test user
        user_data = test_factory.users.create_user_register_request(role="annotator")
        
        success, user_result = await AuthService.register_user(test_master_db, user_data)
        assert success
        
        # Create client and project
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create allocation strategy first
        strategy = test_factory.projects.create_allocation_strategy()
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        project = test_factory.projects.create_project(client.id, strategy.id)
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Grant user access to project
        access = UserProjectAccess(
            user_id=user_result.id,
            project_id=project.id,
            project_role="annotator",  #  FIXED: Use correct field name
            database_name=test_factory.config.get_database_config()["database_name"],  #  REQUIRED FIELD ADDED
            is_active=True
        )
        test_master_db.add(access)
        await test_master_db.commit()
        
        # Verify access was granted
        stmt = select(UserProjectAccess).where(
            UserProjectAccess.user_id == user_result.id,
            UserProjectAccess.project_id == project.id
        )
        result = await test_master_db.execute(stmt)
        user_access = result.scalar_one_or_none()
        
        assert user_access is not None
        assert user_access.project_role == "annotator"  #  FIXED: Use correct field name
        assert user_access.is_active is True


# REMOVED: TestMasterDatabaseAPI class
# → Duplicate API tests moved to specialized test files:
# → test_user_creation_api() moved to test_admin_routes_database_integration.py
# → test_user_login_api() moved to test_auth_middleware_database_integration.py
# → test_protected_endpoint_access() moved to test_auth_middleware_database_integration.py
