from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Optional
from pydantic import BaseModel, EmailStr, ConfigDict

from post_db.master_models.clients import Clients
from core.session_manager import get_master_db_context

router = APIRouter(prefix="/clients", tags=["Client Onboarding"])

class ClientCreateRequest(BaseModel):
    name: str
    username: str
    email: EmailStr
    contact_phone: Optional[str] = None
    address: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

@router.post("/", status_code=201)
async def create_client(req: ClientCreateRequest):
    # Check if client username or email already exists
    async with get_master_db_context() as session:  # type: AsyncSession
        # Check for existing username
        username_result = await session.execute(
            select(Clients).where(Clients.username == req.username)
        )
        if username_result.scalar_one_or_none():
            raise HTTPException(status_code=409, detail="Client with this username already exists")
        
        # Check for existing email
        email_result = await session.execute(
            select(Clients).where(Clients.email == req.email)
        )
        if email_result.scalar_one_or_none():
            raise HTTPException(status_code=409, detail="Client with this email already exists")

        new_client = Clients(
            name=req.name,
            username=req.username,
            email=req.email
        )
        session.add(new_client)
        try:
            await session.commit()
            return {"success": True, "client_id": str(new_client.id)}
        except SQLAlchemyError as e:
            await session.rollback()
            raise HTTPException(status_code=500, detail=str(e))


