"""
Comprehensive unit tests for AnnotatorService.
Tests all methods in isolation with mocked dependencies.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Tuple, Optional

from app.services.annotator_service import AnnotatorService
from app.schemas.annotation_schemas import ImageInfo

class TestAnnotatorServiceUnit:
    """Unit tests for AnnotatorService with mocked dependencies."""
    
    @pytest.mark.unit
    def test_normalize_image_path_valid_paths(self):
        """Test path normalization with valid inputs."""
        test_cases = [
            ('folder/image.jpg', '/folder/image.jpg'),
            ('/already/normalized.jpg', '/already/normalized.jpg'),
            ('folder\\windows\\path.jpg', '/folder/windows/path.jpg'),
            ('//double//slash.jpg', '/double/slash.jpg'),
            ('folder/subfolder/image.png', '/folder/subfolder/image.png')
        ]
        
        for input_path, expected in test_cases:
            result = AnnotatorService._normalize_image_path(input_path)
            assert result == expected, f"Failed for input: {input_path}"
    
    @pytest.mark.unit  
    def test_normalize_image_path_edge_cases(self, edge_case_data):
        """Test path normalization with edge cases."""
        # Empty path
        result = AnnotatorService._normalize_image_path('')
        assert result == '/'
        
        # Only slashes
        result = AnnotatorService._normalize_image_path('///')  
        assert result == '/'
        
        # Unicode characters
        unicode_path = 'фолдер/изображение.jpg'
        result = AnnotatorService._normalize_image_path(unicode_path)
        assert result.startswith('/')
        assert 'изображение.jpg' in result

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_for_user_annotation_mode(self, mock_db_session):
        """Test getting batch for user in annotation mode."""
        # Mock database response for existing assigned batch
        mock_batch = MagicMock()
        mock_batch.batch_identifier = 'BATCH_001'
        mock_batch.file_list = ['image1.jpg', 'image2.jpg']
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_batch
        mock_db_session.execute.return_value = mock_result
        
        # Test getting existing batch
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
            
            result = await AnnotatorService.get_batch_for_user(
                mock_db_session, 
                'test_user', 
                'annotation'
            )
            
            assert isinstance(result, tuple)
            assert len(result) == 4  # images, batch_info, batch_id, error
            
            # Verify database query was called
            assert mock_db_session.execute.called
    
    @pytest.mark.unit
    @pytest.mark.asyncio 
    async def test_get_batch_for_user_no_existing_batch(self, mock_db_session):
        """Test getting batch when no existing batch assigned."""
        # Mock no existing batch
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
            
            # Mock new batch assignment
            with patch.object(AnnotatorService, '_assign_new_batch') as mock_assign:
                mock_assign.return_value = (['image1.jpg'], {'batch_id': 'NEW_001'}, 'NEW_001', None)
                
                result = await AnnotatorService.get_batch_for_user(
                    mock_db_session,
                    'test_user', 
                    'annotation'
                )
                
                # Verify new batch was assigned
                assert mock_assign.called
                assert result[2] == 'NEW_001'  # batch_id
    
    @pytest.mark.unit 
    @pytest.mark.asyncio
    async def test_get_batch_for_user_verification_mode(self, mock_db_session):
        """Test getting batch for user in verification mode."""
        mock_batch = MagicMock()
        mock_batch.batch_identifier = 'VERIFY_001'
        mock_batch.file_list = ['verified1.jpg', 'verified2.jpg']
        
        mock_result = AsyncMock() 
        mock_result.scalar_one_or_none.return_value = mock_batch
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'verification_status', 'verifier_id')
            
            result = await AnnotatorService.get_batch_for_user(
                mock_db_session,
                'verifier_user',
                'verification'
            )
            
            assert result[1]['batch_id'] == 'VERIFY_001'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_batch_for_user_database_error(self, mock_db_session):
        """Test handling database errors during batch retrieval."""
        # Mock database exception
        mock_db_session.execute.side_effect = Exception("Database connection failed")
        
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
            
            result = await AnnotatorService.get_batch_for_user(
                mock_db_session,
                'test_user', 
                'annotation'
            )
            
            # Should return error in tuple
            assert result[3] is not None  # error message
            assert "error" in str(result[3]).lower()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_save_annotation_success(self, mock_db_session):
        """Test successful annotation saving."""
        annotation_data = {
            'image_path': '/test/image.jpg',
            'annotations': [{'x': 100, 'y': 150, 'label': 'test'}],
            'batch_id': 'BATCH_001',
            'user_id': 'user123'
        }
        
        with patch.object(AnnotatorService, '_validate_annotation_data') as mock_validate:
            mock_validate.return_value = True
            
            with patch.object(AnnotatorService, '_save_to_database') as mock_save:
                mock_save.return_value = True
                
                result = await AnnotatorService.save_annotation(
                    mock_db_session,
                    annotation_data
                )
                
                assert result['success'] is True
                assert mock_validate.called
                assert mock_save.called

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_save_annotation_invalid_data(self, mock_db_session, security_test_data):
        """Test annotation saving with invalid data."""
        # Test with malicious input
        malicious_annotation = {
            'image_path': security_test_data['malicious_inputs'][0],  # SQL injection attempt
            'annotations': [{'x': -1, 'y': -1, 'label': ''}],  # Invalid coordinates
            'batch_id': '',  # Empty batch ID
            'user_id': None  # Null user ID
        }
        
        with patch.object(AnnotatorService, '_validate_annotation_data') as mock_validate:
            mock_validate.return_value = False
            
            result = await AnnotatorService.save_annotation(
                mock_db_session,
                malicious_annotation  
            )
            
            assert result['success'] is False
            assert 'error' in result
            assert mock_validate.called

    @pytest.mark.unit
    def test_validate_annotation_data_valid(self):
        """Test annotation data validation with valid inputs."""
        valid_data = {
            'image_path': '/valid/path/image.jpg',
            'annotations': [
                {'x': 100, 'y': 150, 'width': 50, 'height': 75, 'label': 'cat'},
                {'x': 200, 'y': 250, 'width': 60, 'height': 80, 'label': 'dog'}
            ],
            'batch_id': 'BATCH_001',
            'user_id': 'user123',
            'confidence': 0.95
        }
        
        with patch.object(AnnotatorService, '_validate_annotation_data') as mock_method:
            mock_method.return_value = True
            result = AnnotatorService._validate_annotation_data(valid_data)
            assert result is True

    @pytest.mark.unit
    def test_validate_annotation_data_invalid(self, security_test_data):
        """Test annotation data validation with invalid inputs."""  
        invalid_cases = [
            # Missing required fields
            {'image_path': '/path.jpg'},
            # Invalid coordinates
            {'image_path': '/path.jpg', 'annotations': [{'x': -1, 'y': -1}]},
            # SQL injection attempt
            {'image_path': security_test_data['malicious_inputs'][0], 'annotations': []},
            # XSS attempt
            {'image_path': '/path.jpg', 'annotations': [{'label': security_test_data['malicious_inputs'][1]}]}
        ]
        
        with patch.object(AnnotatorService, '_validate_annotation_data') as mock_method:
            for invalid_data in invalid_cases:
                mock_method.return_value = False
                result = AnnotatorService._validate_annotation_data(invalid_data)
                assert result is False

    @pytest.mark.unit  
    @pytest.mark.asyncio
    async def test_get_model_config_annotation_mode(self):
        """Test _get_model_config for annotation mode."""
        with patch.object(AnnotatorService, '_get_model_config') as mock_method:
            mock_method.return_value = (MagicMock(), 'annotation_status', 'annotator_id') 
            
            model, status_field, user_field = AnnotatorService._get_model_config('annotation')
            
            assert status_field == 'annotation_status'
            assert user_field == 'annotator_id'

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_get_model_config_verification_mode(self):
        """Test _get_model_config for verification mode."""
        with patch.object(AnnotatorService, '_get_model_config') as mock_method:
            mock_method.return_value = (MagicMock(), 'verification_status', 'verifier_id')
            
            model, status_field, user_field = AnnotatorService._get_model_config('verification') 
            
            assert status_field == 'verification_status'
            assert user_field == 'verifier_id'

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_new_batch_success(self, mock_db_session):
        """Test successful new batch assignment."""
        with patch.object(AnnotatorService, '_find_available_batch') as mock_find:
            mock_batch = MagicMock()
            mock_batch.batch_identifier = 'NEW_BATCH_001'
            mock_batch.file_list = ['new1.jpg', 'new2.jpg']
            mock_find.return_value = mock_batch
            
            with patch.object(AnnotatorService, '_assign_batch_to_user') as mock_assign:
                mock_assign.return_value = True
                
                result = await AnnotatorService._assign_new_batch(
                    mock_db_session,
                    'test_user',
                    'annotation'
                )
                
                assert result[2] == 'NEW_BATCH_001'  # batch_id
                assert len(result[0]) == 2  # image list
                assert result[3] is None  # no error

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_assign_new_batch_no_available(self, mock_db_session):
        """Test new batch assignment when no batches available."""
        with patch.object(AnnotatorService, '_find_available_batch') as mock_find:
            mock_find.return_value = None
            
            result = await AnnotatorService._assign_new_batch(
                mock_db_session,
                'test_user', 
                'annotation'
            )
            
            assert result[0] == []  # empty image list
            assert result[3] is not None  # error message
            assert "no available batches" in str(result[3]).lower()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_performance_batch_retrieval(self, mock_db_session, performance_monitor, service_performance_data):
        """Test performance of batch retrieval operation."""
        # Mock quick database response
        mock_batch = MagicMock()
        mock_batch.batch_identifier = 'PERF_BATCH'
        mock_batch.file_list = ['img1.jpg'] * 100  # Large batch
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_batch
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
            
            performance_monitor.start()
            
            result = await AnnotatorService.get_batch_for_user(
                mock_db_session,
                'perf_user', 
                'annotation'
            )
            
            performance_monitor.stop()
            
            execution_time = performance_monitor.get_execution_time()
            acceptable_time = service_performance_data['acceptable_response_times']['get_batch_for_user']
            
            assert execution_time < acceptable_time, f"Execution time {execution_time}s exceeds acceptable {acceptable_time}s"
            assert len(result[0]) == 100  # All images retrieved

    @pytest.mark.unit
    @pytest.mark.asyncio 
    async def test_concurrent_batch_access(self, mock_db_session):
        """Test concurrent access to batch retrieval."""
        import asyncio
        
        mock_batch = MagicMock()
        mock_batch.batch_identifier = 'CONCURRENT_BATCH'
        mock_batch.file_list = ['conc1.jpg', 'conc2.jpg']
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_batch
        mock_db_session.execute.return_value = mock_result
        
        with patch.object(AnnotatorService, '_get_model_config') as mock_config:
            mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
            
            # Simulate 10 concurrent requests
            tasks = []
            for i in range(10):
                task = AnnotatorService.get_batch_for_user(
                    mock_db_session,
                    f'user_{i}',
                    'annotation'
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All requests should complete without exceptions
            exceptions = [r for r in results if isinstance(r, Exception)]
            assert len(exceptions) == 0, f"Found {len(exceptions)} exceptions in concurrent access"
            
            # All should return valid results
            valid_results = [r for r in results if not isinstance(r, Exception)]
            assert len(valid_results) == 10

    @pytest.mark.unit
    def test_memory_usage_large_batch(self, test_batch_data, service_performance_data):
        """Test memory usage with large batch processing."""
        import sys
        
        large_batch = test_batch_data['large_batch']
        
        # Mock processing large file list
        file_list = [f"large_file_{i}.jpg" for i in range(10000)]
        
        initial_size = sys.getsizeof(file_list)
        
        # Simulate path normalization on large list
        normalized_paths = [AnnotatorService._normalize_image_path(path) for path in file_list]
        
        final_size = sys.getsizeof(normalized_paths)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['batch_processing'] 
        
        assert memory_increase < max_memory, f"Memory usage {memory_increase}MB exceeds limit {max_memory}MB"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_error_handling_comprehensive(self, mock_db_session):
        """Test comprehensive error handling scenarios."""
        error_scenarios = [
            # Database connection error
            Exception("Database connection lost"),
            # SQL execution error
            Exception("SQL syntax error"),
            # Timeout error
            Exception("Operation timed out"),
            # Permission error
            Exception("Permission denied"),
            # Memory error
            MemoryError("Insufficient memory")
        ]
        
        for error in error_scenarios:
            mock_db_session.execute.side_effect = error
            
            with patch.object(AnnotatorService, '_get_model_config') as mock_config:
                mock_config.return_value = (MagicMock(), 'status', 'annotator_id')
                
                result = await AnnotatorService.get_batch_for_user(
                    mock_db_session,
                    'error_test_user',
                    'annotation'
                )
                
                # Should handle error gracefully
                assert result[3] is not None  # Error message present
                assert isinstance(result[3], str)  # Error is string
                assert len(result[0]) == 0  # Empty image list on error
