"""
Shared schemas for AI Models system - Simplified for Actual Use Case.
Only includes common patterns that are actually used in the annotation workflow.
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime

# Import enums from AI model database models
from post_db.master_models.ai_models_registry import DeploymentStatus
from post_db.allocation_models.model_execution_logs import ExecutionStatus

# Type alias for JSON data
JsonDict = Dict[str, Any]


class PaginationInfo(BaseModel):
    """Pagination information for list responses."""
    page: int = Field(..., ge=1, description="Current page number (1-based)")
    limit: int = Field(..., ge=1, le=100, description="Items per page (max 100)")
    total_count: int = Field(..., ge=0, description="Total number of items")
    total_pages: int = Field(..., ge=0, description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")
    
    model_config = ConfigDict(from_attributes=True)


class SuccessResponse(BaseModel):
    """Standard success response format."""
    success: bool = True
    message: Optional[str] = Field(None, description="Success message")
    data: Optional[JsonDict] = Field(None, description="Additional response data")
    
    model_config = ConfigDict(from_attributes=True)


class ErrorResponse(BaseModel):
    """Standard error response format."""
    success: bool = False
    error: str = Field(..., description="Error message")
    error_code: Optional[str] = Field(None, description="Error code for categorization")
    
    model_config = ConfigDict(from_attributes=True)


# Export all components
__all__ = [
    "PaginationInfo",
    "SuccessResponse",
    "ErrorResponse",
    "JsonDict",
    # Enums from database models
    "DeploymentStatus",
    "ExecutionStatus"
]