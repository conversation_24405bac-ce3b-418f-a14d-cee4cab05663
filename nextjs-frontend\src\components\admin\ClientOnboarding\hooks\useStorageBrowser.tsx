// hooks/useStorageBrowser.tsx
import { useState, useEffect } from 'react';
import { Directory, SelectionTarget } from '../types';
import { StorageType } from '../components/StorageBrowserModal';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "@/lib/api";

export const useStorageBrowser = () => {
  const [isStorageBrowserOpen, setIsStorageBrowserOpen] = useState(false);
  const [storageType, setStorageType] = useState<StorageType>('NAS-FTP');
  const [bucketName, setBucketName] = useState<string>('');
  const [currentSelectionTarget, setCurrentSelectionTarget] = useState<SelectionTarget>(null);
  const [currentBrowsePath, setCurrentBrowsePath] = useState('/');
  const [currentSelection, setCurrentSelection] = useState('');
  const [isSelectingFile, setIsSelectingFile] = useState(false);
  const [directoryContents, setDirectoryContents] = useState<Directory[]>([]);
  const [isLoadingDirectory, setIsLoadingDirectory] = useState(false);

  // Project state
  const [currentProject, setCurrentProject] = useState<any>(null);

  // Load current project and determine storage type
  useEffect(() => {
    const loadCurrentProject = () => {
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          setCurrentProject(project);
          
          console.log('useStorageBrowser: Loaded project data:', {
            connection_type: project.connection_type,
            credentials: project.credentials,
            has_credentials: !!project.credentials
          });
          
          // Determine storage type based on project connection_type or credentials
          let detectedStorageType = 'NAS-FTP'; // Default
          
          if (project.connection_type) {
            detectedStorageType = project.connection_type;
            setStorageType(project.connection_type);
            
            // Set bucket name for MinIO
            if (project.connection_type === 'MinIO' && project.credentials) {
              setBucketName(project.credentials.minio_bucket_name || 'bucket');
            }
          } else if (project.minio_credentials) {
            // Legacy support for old minio_credentials field
            detectedStorageType = 'MinIO';
            setStorageType('MinIO');
            setBucketName(project.minio_credentials.minio_bucket_name || 'bucket');
          } else if (project.credentials) {
            // Try to detect storage type from credentials structure
            if (project.credentials.minio_endpoint || project.credentials.minio_bucket_name) {
              detectedStorageType = 'MinIO';
              setStorageType('MinIO');
              setBucketName(project.credentials.minio_bucket_name || 'bucket');
            } else if (project.credentials.nas_url || project.credentials.nas_username) {
              detectedStorageType = 'NAS-FTP';
              setStorageType('NAS-FTP');
            } else {
              detectedStorageType = 'NAS-FTP';
              setStorageType('NAS-FTP'); // Default
            }
          } else {
            setStorageType('NAS-FTP'); // Default
          }
          
          console.log('useStorageBrowser: Storage type detected and set to:', detectedStorageType);
        }
      } catch (error) {
        console.error('Error loading project from localStorage:', error);
      }
    };

    loadCurrentProject();
  }, []);

  const openStorageBrowser = (target: SelectionTarget) => {
    // Reload project data to get the latest connection info
    const loadCurrentProject = () => {
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          setCurrentProject(project);
          
          console.log('useStorageBrowser: Reloaded project data for storage browser:', {
            connection_type: project.connection_type,
            credentials: project.credentials,
            has_credentials: !!project.credentials
          });
          
          // Determine storage type based on project connection_type or credentials
          let detectedStorageType = 'NAS-FTP'; // Default
          
          if (project.connection_type) {
            detectedStorageType = project.connection_type;
            setStorageType(project.connection_type);
            
            // Set bucket name for MinIO
            if (project.connection_type === 'MinIO' && project.credentials) {
              setBucketName(project.credentials.minio_bucket_name || 'bucket');
            }
          } else if (project.minio_credentials) {
            // Legacy support for old minio_credentials field
            detectedStorageType = 'MinIO';
            setStorageType('MinIO');
            setBucketName(project.minio_credentials.minio_bucket_name || 'bucket');
          } else if (project.credentials) {
            // Try to detect storage type from credentials structure
            if (project.credentials.minio_endpoint || project.credentials.minio_bucket_name) {
              detectedStorageType = 'MinIO';
              setStorageType('MinIO');
              setBucketName(project.credentials.minio_bucket_name || 'bucket');
            } else if (project.credentials.nas_url || project.credentials.nas_username) {
              detectedStorageType = 'NAS-FTP';
              setStorageType('NAS-FTP');
            } else {
              detectedStorageType = 'NAS-FTP';
              setStorageType('NAS-FTP'); // Default
            }
          } else {
            setStorageType('NAS-FTP'); // Default
          }
          
          console.log('useStorageBrowser: Storage type detected and set to:', detectedStorageType);
        }
      } catch (error) {
        console.error('Error loading project from localStorage:', error);
      }
    };

    // Reload project data first
    loadCurrentProject();
    
    setCurrentSelectionTarget(target);
    setIsSelectingFile(target === "verification-label-file");
    setCurrentBrowsePath('/');
    setCurrentSelection('');
    setIsStorageBrowserOpen(true);
    
    // Load initial directory
    setTimeout(() => loadDirectory('/'), 100); // Small delay to ensure state is updated
  };

  const loadDirectory = async (path: string) => {
    if (!currentProject) return;

    setIsLoadingDirectory(true);
    try {
      let endpoint = '';
      let requestBody: any = {};

      switch (storageType) {
        case 'NAS-FTP':
          endpoint = `${API_BASE_URL}/admin/browse-nas-directory`;
          requestBody = {
            project_code: currentProject.project_code,
            path: path
          };
          break;
        
        case 'MinIO':
          endpoint = `${API_BASE_URL}/admin/browse-minio-objects`;
          requestBody = {
            project_code: currentProject.project_code,
            prefix: path === '/' ? '' : path
          };
          break;
        
        case 'GoogleDrive':
          // Future implementation
          showToast.error('Google Drive browser not yet implemented');
          return;
        
        default:
          showToast.error('Unknown storage type');
          return;
      }

      const response = await authFetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Failed to load directory: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Transform the response based on storage type
      let contents: Directory[] = [];
      if (data.data) {
        // Handle different response formats
        let items = [];
        if (Array.isArray(data.data)) {
          // Direct array (NAS format)
          items = data.data;
        } else if (data.data.objects && Array.isArray(data.data.objects)) {
          // Wrapped in objects property (MinIO format)
          items = data.data.objects;
        }
        
        contents = items.map((item: any) => ({
          name: item.name,
          path: item.path,
          type: item.type === 'directory' ? 'directory' : 'file'
        }));
      }

      setDirectoryContents(contents);
    } catch (error) {
      console.error('Error loading directory:', error);
      showToast.error(`Failed to load directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setDirectoryContents([]);
    } finally {
      setIsLoadingDirectory(false);
    }
  };

  const selectItem = (item: Directory) => {
    if (item.type === 'directory') {
      // Navigate into directory
      setCurrentBrowsePath(item.path);
      loadDirectory(item.path);
    } else if (isSelectingFile) {
      // Select file
      setCurrentSelection(item.path);
    }
  };

  const breadcrumbClick = (path: string) => {
    setCurrentBrowsePath(path);
    loadDirectory(path);
  };

  const selectPath = () => {
    const selectedPath = isSelectingFile ? currentSelection : currentBrowsePath;
    
    if (!selectedPath && isSelectingFile) {
      showToast.error('Please select a file');
      return;
    }

    // Update the appropriate field based on selection target
    switch (currentSelectionTarget) {
      case "manual-folder":
        // This will be handled by the parent component
        break;
      case "verification-image-folder":
        // This will be handled by the parent component
        break;
      case "verification-label-file":
        // This will be handled by the parent component
        break;
    }

    setIsStorageBrowserOpen(false);
    
    // Notify parent component about the selection
    if (window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('storage-path-selected', {
        detail: {
          target: currentSelectionTarget,
          path: selectedPath,
          storageType: storageType
        }
      }));
    }
  };

  return {
    // Modal state
    isStorageBrowserOpen,
    setIsStorageBrowserOpen,
    storageType,
    bucketName,
    
    // Navigation state
    currentSelectionTarget,
    currentBrowsePath,
    currentSelection,
    isSelectingFile,
    directoryContents,
    isLoadingDirectory,
    
    // Actions
    openStorageBrowser,
    selectItem,
    breadcrumbClick,
    selectPath,
    loadDirectory,
    
    // Project info
    currentProject
  };
};
