"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  FaRocket,
  FaChevronDown,
  FaArrowUp,
  FaFileAlt,
  FaCogs,
  FaDownload,
  FaCompressArrowsAlt,
  FaChartBar,
  FaMagic,
  FaShareAlt,
  FaLayerGroup,
  FaProjectDiagram,
  FaRobot,
  FaMicrochip,
  FaBrain,
  FaServer,
  FaQuestionCircle,
  FaTag,
  FaBook,
  FaCompressAlt,
  FaListOl,
  FaBalanceScale,
} from "react-icons/fa";

export default function Home() {
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);

  useEffect(() => {
    // Smooth scroll for anchor links
    const links = Array.from(
      document.querySelectorAll<HTMLAnchorElement>('a[href^="#"]')
    );
    const handleClick = (e: Event) => {
      e.preventDefault();
      const targetId = (e.currentTarget as HTMLAnchorElement).getAttribute(
        "href"
      );
      if (targetId) {
        const target = document.querySelector<HTMLElement>(targetId);
        if (target)
          target.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    };
    links.forEach((link) => link.addEventListener("click", handleClick));

    // Back-to-top visibility
    const onScroll = () => setShowBackToTop(window.pageYOffset > 300);
    window.addEventListener("scroll", onScroll);

    return () => {
      links.forEach((link) => link.removeEventListener("click", handleClick));
      window.removeEventListener("scroll", onScroll);
    };
  }, []);

  return (
    <>
      <div className="font-poppins ">
        {/* Header */}
        <header className="fixed top-0 left-0 w-full z-50 transition-colors duration-300 bg-white/90">
          <div className="max-w-7xl mx-auto py-4 flex items-center justify-between ">
            <Link href="/" className="flex items-center no-underline">
              <Image
                src="/img/PVlogo-1024x780.png"
                alt="Process Venue Logo"
                width={60}
                height={60}
                className="object-contain"
              />
              <div className="ml-3">
                <span className="block text-lg font-semibold text-gray-800">
                  End to End Synthetic Dataset Solutions
                </span>
                <span className="block text-sm text-gray-800">
                  Human-AI Collaboration
                </span>
                <span className="inline-block text-xs font-semibold bg-gradient-to-r from-teal-300 to-blue-600 text-black px-2 py-0.5 rounded-full">
                  BETA VERSION
                </span>
              </div>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <div className="flex items-center space-x-5 bg-white/80 backdrop-blur-md px-4 py-2 rounded-full shadow">
                <a
                  href="#top"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  Home
                </a>
                <a
                  href="#features"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  Features
                </a>
                <a
                  href="#how-it-works"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  How It Works
                </a>
                <a
                  href="#dataset-types"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  Datasets
                </a>
                <a
                  href="#model-distillation"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  Distillation
                </a>
                <a
                  href="#use-cases"
                  className="no-underline text-gray-700 hover:text-[#0D47A1] font-semibold"
                >
                  Use Cases
                </a>
              </div>
              <Link
                href="/"
                className="no-underline inline-flex items-center bg-[#0D47A1] text-white px-4 py-2 rounded-full hover:bg-[#1159B8] transition"
              >
                Back to Home
              </Link>
            </nav>
            <button
              className="md:hidden text-gray-700"
              onClick={() => setMenuOpen(!menuOpen)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
          {menuOpen && (
            <div className="md:hidden bg-white/90 backdrop-blur-md p-4 space-y-4">
              <a href="#top" className="block no-underline text-gray-700">
                Home
              </a>
              <a href="#features" className="block no-underline text-gray-700">
                Features
              </a>
              <a
                href="#how-it-works"
                className="block no-underline text-gray-700"
              >
                How It Works
              </a>
              <a
                href="#dataset-types"
                className="block no-underline text-gray-700"
              >
                Datasets
              </a>
              <a
                href="#model-distillation"
                className="block no-underline text-gray-700"
              >
                Distillation
              </a>
              <a href="#use-cases" className="block no-underline text-gray-700">
                Use Cases
              </a>
              <Link
                href="/"
                className="block no-underline text-center bg-[#0D47A1] text-white px-4 py-2 rounded-lg hover:bg-[#1159B8] transition"
              >
                Back to Home
              </Link>
            </div>
          )}
        </header>

        {/* Hero Section */}
        <section
          id="top"
          className="relative py-24 bg-gradient-to-br from-blue-50 to-blue-100"
        >
          <div className="absolute -top-10 -left-10 w-48 h-48 bg-primary rounded-full opacity-20 animate-float" />
          <div className="absolute top-20 right-10 w-32 h-32 bg-secondary rounded-full opacity-20 animate-float" />
          <div className="relative z-10 container mx-auto px-6 py-20 text-center">
            <h1 className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#0D47A1] to-[#1159B8]">
              SynGround
            </h1>
            <p className="mt-4 text-xl text-gray-700">
              Accelerate model training with high-quality synthetic data and
              efficient knowledge transfer
            </p>
            <div className="mt-8 space-y-2">
              <p className="text-lg text-gray-600 animate-fade-in opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-100">
                Generate synthetic datasets from any source
              </p>
              <p className="text-lg text-gray-600 animate-fade-in opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-300">
                Train and optimize AI models efficiently
              </p>
              <p className="text-lg text-gray-600 animate-fade-in opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-500">
                Transfer knowledge between models seamlessly
              </p>
            </div>
            <div className="mt-8">
              <Link
                href="/login"
                className="inline-flex items-center bg-[#0D47A1] text-white px-6 py-3 rounded-full hover:bg-[#1159B8] transition no-underline hover:no-underline"
              >
                <FaRocket className="mr-2" /> Get Started
              </Link>
            </div>
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-fade-in opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-700">
              <a
                href="#features"
                className="no-underline text-gray-600 hover:text-blue-600 text-2xl"
              >
                <FaChevronDown />
              </a>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Dataset Generation Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-100 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaFileAlt />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Source Analysis
                  </h3>
                  <p className="text-gray-600">
                    Upload PDFs, text files, and other documents like web page
                    URLs to generate comprehensive datasets.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-300 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaCogs />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Customizable Settings
                  </h3>
                  <p className="text-gray-600">
                    Configure number of QA pairs, difficulty levels, and query
                    (optional).
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-500 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaDownload />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Export Options</h3>
                  <p className="text-gray-600">
                    Download your generated datasets in multiple formats for
                    training or evaluation.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-700 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaCompressArrowsAlt />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Model Distillation
                  </h3>
                  <p className="text-gray-600">
                    Create compact datasets for transferring knowledge from
                    large to smaller models.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section
          id="how-it-works"
          className="relative bg-gradient-to-br from-blue-50 to-blue-100 py-20"
        >
          {/* Decorative circles */}
          <div className="absolute -top-12 -right-12 w-52 h-52 bg-green-100 rounded-full opacity-30"></div>
          <div className="absolute -bottom-16 right-1/4 w-72 h-72 bg-yellow-100 rounded-full opacity-30"></div>
          <div className="relative z-10 container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              How Dataset Generation Works
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  num: "1",
                  title: "Select Dataset Types",
                  desc: "Choose from multiple dataset types based on your specific needs.",
                },
                {
                  num: "2",
                  title: "Upload Sources",
                  desc: "Add documents or web pages to generate datasets from.",
                },
                {
                  num: "3",
                  title: "Add Keywords (Optional)",
                  desc: "Provide specific keywords as queries to focus the dataset generation.",
                },
                {
                  num: "4",
                  title: "Select AI Model",
                  desc: "Choose from OpenAI, Claude, Gemini, or Ollama based on your requirements.",
                },
                {
                  num: "5",
                  title: "Configure Settings",
                  desc: "Set the number of items to generate and difficulty level.",
                },
                {
                  num: "6",
                  title: "Generate Dataset",
                  desc: "Create your custom dataset using the selected parameters.",
                },
                {
                  num: "7",
                  title: "Download Results",
                  desc: "Export your dataset in various formats including JSON, CSV, TXT.",
                },
              ].map((step, idx) => (
                <div
                  key={step.num}
                  className={`opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-${
                    (idx + 1) * 100
                  } animate-fade-in`}
                >
                  <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                    <div className="text-3xl font-bold mb-4 text-gray-700">
                      {step.num}
                    </div>
                    <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                    <p className="text-gray-600">{step.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Models Available Section */}
        <section id="models" className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Models Available
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
              {[
                {
                  icon: <FaRobot className="text-[#0D47A1] text-3xl mb-4" />,
                  title: "OpenAI",
                  desc: "Advanced GPT models for generating high-quality, contextual question-answer pairs.",
                },
                {
                  icon: (
                    <FaMicrochip className="text-[#0D47A1] text-3xl mb-4" />
                  ),
                  title: "Claude",
                  desc: "Specialized in detailed and nuanced responses for complex domain knowledge.",
                },
                {
                  icon: <FaBrain className="text-[#0D47A1] text-3xl mb-4" />,
                  title: "Gemini",
                  desc: "Google's multimodal model with strong reasoning and content generation capabilities.",
                },
                {
                  icon: <FaServer className="text-[#0D47A1] text-3xl mb-4" />,
                  title: "Deepseek r1",
                  desc: "Run various open-source models locally using Ollama with customizable parameters.",
                },
              ].map((m, idx) => (
                <div
                  key={m.title}
                  className={`opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-${
                    (idx + 1) * 100
                  } animate-fade-in`}
                >
                  <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                    {m.icon}
                    <h3 className="text-lg font-semibold mb-2">{m.title}</h3>
                    <p className="text-gray-600">{m.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Types of Datasets Section (Colored) */}
        <section
          id="dataset-types"
          className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 py-20"
        >
          {/* Decorative circles */}
          <div className="absolute -top-12 -left-12 w-52 h-52 bg-green-100 rounded-full opacity-30"></div>
          <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-yellow-100 rounded-full opacity-30"></div>
          <div className="relative z-10 container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Types of Datasets
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[
                {
                  icon: (
                    <FaQuestionCircle className="text-[#0D47A1] text-2xl mb-4" />
                  ),
                  title: "Question-Answer Pairs",
                  desc: "Standard format with questions and corresponding correct answers.",
                },
                {
                  icon: <FaTag className="text-[#0D47A1] text-2xl mb-4" />,
                  title: "Entity Extraction",
                  desc: "Identify and extract named entities, terms, and key information points.",
                },
                {
                  icon: <FaBook className="text-[#0D47A1] text-2xl mb-4" />,
                  title: "Concept Definitions",
                  desc: "Explanations and definitions of key terms and concepts.",
                },
                {
                  icon: (
                    <FaCompressAlt className="text-[#0D47A1] text-2xl mb-4" />
                  ),
                  title: "Summarization",
                  desc: "Condensed versions of longer texts capturing key information.",
                },
                {
                  icon: <FaListOl className="text-[#0D47A1] text-2xl mb-4" />,
                  title: "Procedures",
                  desc: "Step-by-step instructions for completing specific tasks.",
                },
                {
                  icon: (
                    <FaBalanceScale className="text-[#0D47A1] text-2xl mb-4" />
                  ),
                  title: "Comparisons",
                  desc: "Comparing different datasets to identify similarities and differences.",
                },
              ].map((ds, idx) => (
                <div
                  key={ds.title}
                  className={`opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-${
                    (idx + 1) * 100
                  } animate-fade-in`}
                >
                  <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                    {ds.icon}
                    <h3 className="text-lg font-semibold mb-2">{ds.title}</h3>
                    <p className="text-gray-600">{ds.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Model Distillation Section */}
        <section id="model-distillation" className="py-20 bg-white">
          <div className="container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Model Distillation
            </h2>
            <p className="text-gray-600 text-center max-w-3xl mx-auto mb-8">
              Model Distillation is a compression technique where a large
              Teacher Model transfers its knowledge to a smaller Student Model
              while keeping performance high. The goal is to make AI models
              smaller, faster, and more efficient for real-world applications.
            </p>
            <h3 className="text-2xl font-semibold text-gray-800 mb-4 text-center">
              When no real data available:
            </h3>
            <div className="mb-8 flex justify-center">
              <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg max-w-4xl w-full">
                <div className="text-[#0D47A1] text-3xl mb-4 flex justify-center">
                  <FaMagic />
                </div>
                <h3 className="text-lg font-semibold mb-2 text-center">
                  Data-Free Knowledge Distillation
                </h3>
                <p className="text-gray-600">
                  Transfer knowledge from a teacher model to a student model
                  without relying on original training data by generating
                  synthetic data to guide the student&apos;s learning.
                </p>
              </div>
            </div>
            <h3 className="text-2xl font-semibold text-gray-800 mb-4 text-center">
              When real data is available:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-100 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaShareAlt />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Response Based Knowledge Distillation
                  </h3>
                  <p className="text-gray-600">
                    Transfers information from the final output layer of the
                    teacher model by training the student model to output logits
                    that match the teacher model&apos;s predictions.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-200 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaLayerGroup />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Feature Based Knowledge Distillation
                  </h3>
                  <p className="text-gray-600">
                    Focuses on information conveyed in intermediate layers where
                    neural networks perform feature extraction, training the
                    student to learn the same features as the teacher.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-300 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaProjectDiagram />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Relation Based Knowledge Distillation
                  </h3>
                  <p className="text-gray-600">
                    Focuses on the relationships between different layers or
                    between feature maps representing the activations at
                    different layers or locations of the teacher model.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Use Cases Section (Colored) */}
        <section
          id="use-cases"
          className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 py-20"
        >
          {/* Decorative circles */}
          <div className="absolute -top-16 right-0 w-48 h-48 bg-green-100 rounded-full opacity-30"></div>
          <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-yellow-100 rounded-full opacity-30"></div>
          <div className="relative z-10 container mx-auto px-6">
            <h2 className="text-3xl font-semibold text-black mb-8 text-center relative after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Dataset Use Cases
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-100 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaBrain />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    AI Model Training
                  </h3>
                  <p className="text-gray-600">
                    Create training datasets to improve AI model performance on
                    domain-specific knowledge.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-200 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaChartBar />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Performance Evaluation
                  </h3>
                  <p className="text-gray-600">
                    Generate test sets to evaluate how well AI models understand
                    your content.
                  </p>
                </div>
              </div>
              <div className="opacity-0 translate-y-4  transition-transform duration-700 ease-out delay-300 animate-fade-in">
                <div className="p-6 bg-white rounded-lg shadow hover:shadow-lg">
                  <div className="text-[#0D47A1] text-3xl mb-4">
                    <FaBook />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    Educational Resources
                  </h3>
                  <p className="text-gray-600">
                    Create Q&A materials for educational purposes or knowledge
                    assessment.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <section
          id="footer"
          className="relative overflow-hidden bg-gradient-to-br from-[#0D47A1] to-[#1159B8] py-12"
        >
          <div className="relative z-10 max-w-7xl mx-auto px-4 text-center text-sm text-white">
            SynGround • Synthetic Data Platform • All Rights Reserved • © 2025
          </div>
        </section>

        {/* Back-to-Top Button */}
        {showBackToTop && (
          <button
            onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
            className="fixed bottom-8 right-8 p-3 rounded-full bg-[#0D47A1] text-white shadow-lg hover:bg-[#1159B8] transition"
          >
            <FaArrowUp />
          </button>
        )}
      </div>
    </>
  );
}
