// utils/helpers.ts
import { Dataset, ClientFormData, CompletionStatus } from '../types';

export const validateClientForm = (clientForm: ClientFormData): Record<string, string> => {
  const errors: Record<string, string> = {};

  // User account validation
  if (!clientForm.username.trim()) errors.username = "Username is required";
  if (!clientForm.fullName.trim()) errors.fullName = "Full name is required";
  if (!clientForm.email.trim()) errors.email = "Email is required";
  if (!clientForm.password) errors.password = "Password is required";
  if (!clientForm.confirmPassword)
    errors.confirmPassword = "Please confirm password";

  if (clientForm.username && !/^[a-zA-Z0-9_]+$/.test(clientForm.username)) {
    errors.username =
      "Username can only contain letters, numbers, and underscores";
  }

  if (
    clientForm.email &&
    !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(clientForm.email)
  ) {
    errors.email = "Please enter a valid email address";
  }

  if (clientForm.password && clientForm.password.length < 8) {
    errors.password = "Password must be at least 8 characters";
  }

  if (clientForm.password !== clientForm.confirmPassword) {
    errors.confirmPassword = "Passwords do not match";
  }



  // Project validation
  if (!clientForm.projectName?.trim()) {
    errors.projectName = "Project name is required";
  }

  if (!clientForm.projectType) {
    errors.projectType = "Project type is required";
  }

  return errors;
};

export const getCompletionStatus = (dataset: Dataset): CompletionStatus => {
  if (dataset.total_batches === 0) return 'empty';
  if (dataset.completed_batches === dataset.total_batches) return 'completed';
  return 'in-progress';
};

export const generateBreadcrumbs = (path: string) => {
  const parts = path.split('/').filter(p => p);
  let currentPath = '';
  
  return [
    { name: 'Root', path: '/' },
    ...parts.map(part => {
      currentPath += '/' + part;
      return { name: part, path: currentPath };
    })
  ];
};