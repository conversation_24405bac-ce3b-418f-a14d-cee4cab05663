# Master Database Schema

## **ARCHITECTURE OVERVIEW**

### **Core Concept: Centralized Master Database**
- **Master Database**: Central management and coordination hub
- **Purpose**: User management, project registry, cross-project analytics
- **Role**: Coordinates multiple project databases while maintaining isolation
- **Global Operations**: Authentication, authorization, resource allocation

---

## **🏢 MASTER DATABASE SCHEMA**

### **Purpose**: Central coordination, user management, and project orchestration

### 1. **projects_registry** (Central project catalog)
**Role**: Master registry of all annotation projects across the platform. Acts as the central directory for project discovery, access control, and high-level project management.

```sql
CREATE TABLE projects_registry (
    -- Primary Key & Identification
    id SERIAL PRIMARY KEY,                           -- Unique internal project identifier for system references
    project_code VARCHAR(50) NOT NULL UNIQUE,       -- Human-readable project code (e.g., "PROJ_CLIENT_001") for client/admin reference
    project_name VARCHAR(255) NOT NULL,             -- Descriptive project name displayed in UI and reports
    project_type VARCHAR(50) NOT NULL,              -- Media type being annotated ('image', 'pdf', 'video', 'audio', 'text') - determines annotation interface
    
    -- Client Information & Ownership
    client_id VARCHAR(100) NOT NULL,                -- Client's unique identifier for multi-tenant organization
    client_name VARCHAR(255) NOT NULL,              -- Client organization name for display and billing purposes
    client_contact_info JSONB,                      -- Client contact details (email, phone, address) stored as flexible JSON
    
    -- Database Connection & Isolation
    database_name VARCHAR(100) NOT NULL UNIQUE,     -- Name of dedicated project database (e.g., "proj_client_001_db")
    database_host VARCHAR(255) DEFAULT 'localhost', -- Database server hostname for distributed database deployment
    database_port INTEGER DEFAULT 5432,             -- Database port for connection routing
    database_connection_params JSONB,               -- Additional connection parameters (SSL, timeout, pool size) as JSON
    
    -- Project Configuration & Schema Definition
    annotation_requirements JSONB,                  -- Client-specific annotation requirements and business rules
    label_schema_definition JSONB,                  -- Dynamic label structure definition for this project's annotation types
    custom_table_definitions JSONB,                 -- Custom table schemas beyond standard template for specialized workflows
    
    -- Annotation Mode Configuration (AI/Human workflow support)
    annotation_mode VARCHAR(50) DEFAULT 'human_only', -- 'ai_only', 'ai_human_hybrid', 'human_only'
    ai_model_config JSONB,                          -- AI model configuration and parameters for automated annotation
    requires_human_verification BOOLEAN DEFAULT FALSE, -- Whether AI annotations need human verification
    verification_sampling_rate DECIMAL(5,2) DEFAULT 100.0, -- Percentage of AI annotations to verify (0-100)
    
    -- Project Status & Management
    project_status VARCHAR(50) DEFAULT 'active',    -- Current operational status ('active', 'paused', 'completed', 'archived')
    priority_level INTEGER DEFAULT 1,               -- Project priority for resource allocation (1=highest, 10=lowest)
    
    -- Performance Statistics (cached from project database for dashboard)
    total_files INTEGER DEFAULT 0,                  -- Total number of files in project (cached for quick dashboard display)
    total_batches INTEGER DEFAULT 0,                -- Total number of annotation batches created
    completed_files INTEGER DEFAULT 0,              -- Number of fully annotated and approved files
    active_annotators INTEGER DEFAULT 0,            -- Current number of users actively working on this project
    
    -- Timeline & Deadlines
    project_deadline TIMESTAMP,                     -- Client deadline for project completion (for scheduling and prioritization)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Project creation timestamp for audit trail
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Last modification timestamp for change tracking
    last_sync_at TIMESTAMP,                         -- Last synchronization with project database for data consistency monitoring
    
    -- Data Validation
    CHECK (project_type IN ('image', 'pdf', 'video', 'audio', 'text')) -- Ensures only supported annotation types
);
```

### 2. **users** (Global user management)
**Role**: Centralized user registry for all platform users. Manages authentication, authorization, skills, and cross-project performance tracking for annotators, reviewers, and administrators.

```sql
CREATE TABLE users (
    -- Primary Identity
    id SERIAL PRIMARY KEY,                          -- Unique user identifier used across all project databases
    username VARCHAR(255) NOT NULL UNIQUE,         -- Unique username for login and user references across system
    email VARCHAR(255) NOT NULL UNIQUE,            -- Primary email for authentication and notifications
    full_name VARCHAR(255),                        -- Display name for UI and reporting purposes
    user_role VARCHAR(50) NOT NULL,                -- Global role ('admin', 'annotator', 'reviewer', 'client') determining base permissions
    
    -- Authentication & Access Control
    password_hash VARCHAR(255),                    -- Hashed password for secure authentication
    is_active BOOLEAN DEFAULT TRUE,                -- Account status for enabling/disabling user access globally
    last_login TIMESTAMP,                          -- Last login timestamp for security monitoring and session management
    
    -- Skills & Qualification Management
    annotation_skills JSONB,                       -- User's annotation capabilities by media type (e.g., {"image": "expert", "pdf": "intermediate"})
    max_concurrent_projects INTEGER DEFAULT 3,     -- Maximum number of projects user can work on simultaneously (workload management)
    max_concurrent_batches INTEGER DEFAULT 5,      -- Maximum number of annotation batches user can handle at once
    
    -- Cross-Project Performance Tracking
    overall_quality_score DECIMAL(5,2),            -- Aggregated quality score across all projects (0-100) for assignment optimization
    total_completed_assignments INTEGER DEFAULT 0,  -- Total number of completed annotation assignments across all projects
    
    -- Audit Trail
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Account creation timestamp for user lifecycle tracking
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- Last profile update timestamp for change monitoring
);
```

### 3. **user_project_access** (Project-specific access control)
**Role**: Manages fine-grained access control and permissions for users within specific projects. Enables project-level role assignment and customized user settings per project.

```sql
CREATE TABLE user_project_access (
    -- Primary Key & Relationships
    id SERIAL PRIMARY KEY,                                     -- Unique access record identifier
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,   -- Reference to global user record
    project_id INTEGER REFERENCES projects_registry(id) ON DELETE CASCADE, -- Reference to specific project
    database_name VARCHAR(100) NOT NULL,                      -- Project database name for connection routing
    
    -- Access Control & Permissions
    project_role VARCHAR(50),                                 -- Project-specific role ('annotator', 'senior_annotator', 'reviewer', 'project_manager')
    
    -- Project-Specific Configuration
    project_specific_skills JSONB,                            -- User's skills specific to this project's requirements (may differ from global skills)
    max_batches_in_project INTEGER DEFAULT 1,                 -- Maximum concurrent batches this user can handle in this specific project
    preferred_batch_size INTEGER DEFAULT 20,                  -- User's preferred number of files per batch for this project type
    
    -- Access Status & Activity Tracking
    is_active BOOLEAN DEFAULT TRUE,                           -- Whether user currently has active access to this project
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,          -- When user was granted access to this project
    last_activity TIMESTAMP,                                  -- Last time user performed any action in this project (for activity monitoring)
    
    -- Data Integrity
    UNIQUE(user_id, project_id)                               -- Ensures one access record per user per project
);
```

### 4. **global_workload_summary** (Cross-project workload tracking)
**Role**: Real-time aggregated view of each user's workload across all projects. Used for intelligent task assignment, capacity planning, and preventing user overload.

```sql
CREATE TABLE global_workload_summary (
    -- Primary Identity
    id SERIAL PRIMARY KEY,                          -- Unique workload summary record identifier
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- Reference to user record for relationship integrity
    username VARCHAR(255) NOT NULL,                -- Username for quick reference and display (denormalized for performance)
    
    -- Current Cross-Project Workload Status
    total_active_projects INTEGER DEFAULT 0,       -- Number of projects user is currently working on (for project assignment limits)
    total_active_batches INTEGER DEFAULT 0,        -- Number of annotation batches currently assigned across all projects
    total_pending_files INTEGER DEFAULT 0,         -- Total files waiting for annotation across all user's assignments
    
    -- Capacity Management & Resource Allocation
    max_concurrent_projects INTEGER DEFAULT 3,     -- Maximum projects this user can handle simultaneously (copied from users table for performance)
    max_concurrent_batches INTEGER DEFAULT 5,      -- Maximum batches this user can work on at once (copied from users table)
    current_capacity_percentage DECIMAL(5,2) DEFAULT 0, -- Current workload as percentage of maximum capacity (0-100%)
    
    -- User Availability & Status
    is_available BOOLEAN DEFAULT TRUE,             -- Whether user is available for new assignments
    availability_reason VARCHAR(255),              -- Reason for unavailability ("On vacation", "Training", "Medical leave", etc.)
    timezone VARCHAR(50) DEFAULT 'UTC',           -- User's timezone for scheduling and deadline management
    
    -- Data Freshness
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- When this summary was last recalculated (for cache invalidation)
    
    -- Data Integrity
    UNIQUE(username)                               -- Ensures one workload summary per user
);
```

### 5. **cross_project_analytics** (Aggregated analytics)
**Role**: Time-series analytics data aggregated across all projects for executive reporting, performance monitoring, and business intelligence. Provides platform-wide insights for management decisions.

```sql
CREATE TABLE cross_project_analytics (
    -- Primary Key
    id SERIAL PRIMARY KEY,                          -- Unique analytics record identifier
    
    -- Analytics Time Scope & Granularity
    analytics_date DATE DEFAULT CURRENT_DATE,      -- Date of the analytics snapshot for time-series analysis
    analytics_type VARCHAR(50) NOT NULL,           -- Time granularity ('daily', 'weekly', 'monthly') for different reporting levels
    
    -- Project Portfolio Metrics
    total_active_projects INTEGER DEFAULT 0,       -- Number of projects with active annotation work (for portfolio management)
    total_files_processed INTEGER DEFAULT 0,       -- Total files completed across all projects (for throughput measurement)
    total_annotations_completed INTEGER DEFAULT 0, -- Total annotation tasks completed platform-wide (for productivity tracking)
    
    -- Human Resource Metrics
    total_active_users INTEGER DEFAULT 0,          -- Number of users who performed annotation work during this period
    avg_user_utilization DECIMAL(5,2),            -- Average user capacity utilization percentage across platform (for resource planning)
    
    -- Quality & Performance Metrics
    overall_quality_score DECIMAL(5,2),           -- Platform-wide quality score aggregated from all projects (for quality monitoring)
    avg_annotation_time_seconds INTEGER,          -- Average time to complete annotation across all media types (for efficiency analysis)
    
    -- Business Performance & Trend Analysis
    daily_throughput INTEGER,                     -- Number of files completed per day (for capacity planning and client estimates)
    weekly_trend JSONB,                          -- Trend analysis data (e.g., {"direction": "up", "percentage": 15}) for executive dashboards
    
    -- Data Integrity
    UNIQUE(analytics_date, analytics_type)        -- Ensures one analytics record per date per granularity type
);
```

---

## **📊 SYNCHRONIZATION STRATEGY**

### **Master-Project Database Sync**
**Role**: Maintains consistency between master database and individual project databases through automated synchronization triggers and procedures. Ensures centralized reporting and cross-project analytics remain accurate.

```sql
-- Stored procedure for syncing project statistics from individual project databases to master database
CREATE OR REPLACE FUNCTION sync_project_stats_to_master()
RETURNS TRIGGER AS $$
BEGIN
    -- Automatically updates master database with current project statistics whenever project data changes
    -- Triggered on file completion, user assignment changes, batch status updates, etc.
    -- Maintains real-time synchronization between distributed project databases and central coordination system
    -- This would be called via database links, foreign data wrappers, or application-level sync mechanisms
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### **Cross-Database Query Service**
**Role**: Provides unified query interface for accessing data across multiple project databases. Enables cross-project analytics, user workload management, and centralized reporting while maintaining database isolation.

```python
class CrossProjectQueryService:
    """
    Service for executing queries across multiple project databases and aggregating results.
    Manages connection pools to all project databases and provides unified data access.
    """
    
    def __init__(self):
        self.master_db = MasterDatabaseConnection()              # Connection to central master database
        self.project_connections = {}                            # Cache of connections to individual project databases
    
    def get_user_workload_across_projects(self, username):
        """
        Aggregates user's workload across all projects they have access to.
        
        Args:
            username: Username to get workload summary for
            
        Returns:
            dict: Aggregated workload metrics across all projects
        """
        projects = self.master_db.get_user_projects(username)    # Get list of projects user has access to
        
        total_workload = {                                       # Initialize workload aggregation structure
            'active_batches': 0,                                # Total batches user is working on across all projects
            'pending_files': 0,                                 # Total files waiting for user's annotation
            'in_progress_files': 0                              # Total files user is currently annotating
        }
        
        for project in projects:                                 # Iterate through each project user has access to
            project_db = self.get_project_connection(project['database_name'])  # Get connection to project database
            workload = project_db.get_user_workload(username)                   # Query user's workload in this project
            
            # Aggregate workload metrics across all projects for comprehensive view
            total_workload['active_batches'] += workload['active_batches']      # Sum active batches across projects
            total_workload['pending_files'] += workload['pending_files']        # Sum pending files across projects  
            total_workload['in_progress_files'] += workload['in_progress_files'] # Sum in-progress files across projects
        
        return total_workload                                    # Return comprehensive cross-project workload summary
```

---

## **DATABASE MANAGEMENT SYSTEM**

### **Database Creation Strategy**
**Role**: Automated database provisioning system that creates dedicated project databases from templates. Ensures consistent schema deployment while allowing project-specific customizations.

```sql
-- Template for creating new project database with standardized configuration
CREATE DATABASE ${project_database_name}                    -- Dynamic database name (e.g., "proj_acme_imgclass_001")
    WITH OWNER = ${db_owner}                                -- Database owner for access control management
    ENCODING = 'UTF8'                                       -- Standard encoding for internationalization support
    LC_COLLATE = 'en_US.UTF-8'                            -- Locale collation for consistent text sorting
    LC_CTYPE = 'en_US.UTF-8'                              -- Character classification for text processing
    TEMPLATE = template_project_db;                         -- Template database containing base schema structure
```

### **Dynamic Schema Creation**
**Role**: Python service that automates the creation and configuration of project-specific databases with customized schemas, ensuring each project gets the exact table structure needed for its annotation requirements.

```python
# Python service for dynamic schema creation and project database management
class ProjectDatabaseManager:
    def create_project_database(self, project_config):
        """
        Creates a new database for a project with custom schema tailored to client requirements.
        
        Args:
            project_config: Dictionary containing project specifications, client requirements, 
                          annotation schema, and workflow configuration
        
        Returns:
            str: Database name of the newly created project database
        """
        
        # 1. Generate unique database name following naming convention
        db_name = f"proj_{project_config['client_id']}_{project_config['project_code']}"
        self.create_database(db_name)                       # Create empty database instance
        
        # 2. Apply standardized base schema (common tables for all projects)
        self.apply_base_schema(db_name)                     # Deploy project_metadata, files_registry, batches, assignments
        
        # 3. Create project-specific annotation tables based on media type and requirements
        self.create_custom_annotation_tables(db_name, project_config)  # Deploy annotations_image, annotations_text, etc.
        
        # 4. Optimize database for project-specific workload patterns
        self.create_project_indexes(db_name, project_config)           # Create indexes for expected query patterns
        
        # 5. Register new project database in master coordination system
        self.register_project_in_master(project_config, db_name)       # Update projects_registry in master DB
        
        return db_name
    
    def create_custom_annotation_tables(self, db_name, project_config):
        """
        Creates annotation tables customized for specific project requirements and media types.
        
        Args:
            db_name: Target project database name
            project_config: Project configuration including annotation schema and label definitions
        """
        
        annotation_schema = project_config['annotation_schema']         # Client-specific annotation requirements
        
        if project_config['project_type'] == 'image':                 # Create image-specific annotation tables
            self.create_image_annotation_table(db_name, annotation_schema)
        elif project_config['project_type'] == 'text':                # Create text-specific annotation tables
            self.create_text_annotation_table(db_name, annotation_schema)
        # Additional media types: video, audio, pdf, etc.
```

---

## **IMPLEMENTATION ADVANTAGES**

### **Benefits of Master Database Architecture**

1. **Centralized Control**: Single point for user management and project coordination
2. **Global Insights**: Cross-project analytics and reporting capabilities
3. **Resource Optimization**: Intelligent workload distribution across projects
4. **Security**: Centralized authentication with project-level authorization
5. **Scalability**: Can manage hundreds of isolated project databases
6. **Compliance**: Central audit trail and access control

### **🔧 Management Features**

1. **Central User Management**: Users managed centrally but access controlled per-project
2. **Cross-Project Analytics**: Aggregated insights across all projects
3. **Resource Allocation**: Intelligent distribution of annotators across projects
4. **Workload Balancing**: Prevent overloading users across multiple projects
5. **Performance Tracking**: Individual and aggregate performance metrics

### **📈 Enterprise Ready**

- **Multi-tenancy**: True database-level isolation with central coordination
- **Horizontal Scaling**: Projects distributed across multiple database servers
- **Global Monitoring**: Platform-wide monitoring and alerting
- **Business Intelligence**: Executive dashboards and analytics
- **Compliance**: Centralized audit and governance capabilities