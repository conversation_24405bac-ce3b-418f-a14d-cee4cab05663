"""
FastAPI routes for project users management with dynamic roles.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Body #type:ignore
from fastapi.responses import JSONResponse #type:ignore
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import logging
from services.project_users_service import ProjectUsersService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/project-users", tags=["project-users"])

# Request and response models
class AddUserRequest(BaseModel):
    user_id: int
    username: str
    role: str

# Service dependency
def get_users_service():
    return ProjectUsersService()

@router.get("/{project_code}/available-roles", response_model=Dict[str, Any])
async def get_available_roles(
    project_code: str,
    users_service: ProjectUsersService = Depends(get_users_service)
):
    """
    Get available roles for a project based on its allocation strategy.
    
    Args:
        project_code: The project code
        
    Returns:
        Dict: Available roles information
    """
    try:
        result = await users_service.get_available_roles(project_code)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting available roles: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting available roles: {str(e)}")

@router.post("/{project_code}/users", response_model=Dict[str, Any])
async def add_user_to_project(
    project_code: str,
    request: AddUserRequest,
    users_service: ProjectUsersService = Depends(get_users_service)
):
    """
    Add a user to a project with a specific role.
    
    Args:
        project_code: The project code
        request: User addition request
        
    Returns:
        Dict: Result of user addition
    """
    try:
        result = await users_service.add_user_to_project(
            project_code=project_code,
            user_id=request.user_id,
            username=request.username,
            role=request.role
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding user to project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding user to project: {str(e)}")

@router.get("/{project_code}/users", response_model=Dict[str, Any])
async def get_project_users(
    project_code: str,
    users_service: ProjectUsersService = Depends(get_users_service)
):
    """
    Get all users in a project.
    
    Args:
        project_code: The project code
        
    Returns:
        Dict: Project users information
    """
    try:
        result = await users_service.get_project_users(project_code)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project users: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting project users: {str(e)}")

@router.delete("/{project_code}/users/{user_id}", response_model=Dict[str, Any])
async def remove_user_from_project(
    project_code: str,
    user_id: int,
    users_service: ProjectUsersService = Depends(get_users_service)
):
    """
    Remove a user from a project.
    
    Args:
        project_code: The project code
        user_id: The user ID
        
    Returns:
        Dict: Result of user removal
    """
    try:
        result = await users_service.remove_user_from_project(
            project_code=project_code,
            user_id=user_id
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing user from project: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error removing user from project: {str(e)}")
