# routes/telegram_fetch_data_routes.py
from fastapi import APIRouter, HTTPException, Query, Body, Depends, Response, Cookie
from fastapi.responses import StreamingResponse, JSONResponse
from typing import List, Optional, Dict, Any
from io import BytesIO, <PERSON><PERSON>
from pydantic import BaseModel
import logging
from dependencies.auth import get_current_active_user, require_admin
from documind.telegram_service import (
    DriveUploader,
    connect_to_telegram,
    verify_code,
    verify_password,
    check_auth,
    get_user_channels,
    disconnect,
    reset_session,
    get_channel_images,
    get_channel_dates,
    download_image,
    get_channel_analytics
)
from cache.fetch_data_cache import (
    cache_channels,
    get_cached_channels,
    cache_channel_images,
    get_cached_channel_images,
    cache_auth_status,
    get_cached_auth_status,
    invalidate_auth_status_cache,
    cache_channel_analytics,
    get_cached_channel_analytics,
    invalidate_channels_cache,
    generate_channels_key,
    CHANNELS_TTL
)

# Initialize logger
logger = logging.getLogger(__name__)

# Cookie settings for telegram session
TELEGRAM_COOKIE_SETTINGS = {
    "httponly": True,
    "secure": False,  # Set to True in production with HTTPS
    "samesite": "lax",
    "max_age": 60 * 60 * 24 * 30,  # 30 days
    "path": "/",
}

router = APIRouter(
    prefix="/telegram",
    tags=["telegram"],
  dependencies=[Depends(get_current_active_user), Depends(require_admin())],
  responses={401: {"description": "Unauthorized"}}
)

# --- Request Models ---
class ConnectRequest(BaseModel):
    api_id: int
    api_hash: str
    phone: Optional[str] = None

class VerifyCodeRequest(BaseModel):
    code: str

class VerifyPasswordRequest(BaseModel):
    password: str

class DownloadImageRequest(BaseModel):
    message_id: int
    channel_id: int
    filename: Optional[str] = None

class DownloadMultipleRequest(BaseModel):
    images: List[Dict[str, int]]

class UploadToDriveRequest(BaseModel):
    images: Optional[List[Dict[str, Any]]] = None
    channel_name: Optional[str] = None

# --- Endpoints ---
@router.post("/connect")
async def telegram_connect(
    data: ConnectRequest, 
    response: Response,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    try:
        result = await connect_to_telegram(
            data.api_id,
            data.api_hash,
            data.phone,
            tg_session
        )
        
        # Set telegram session cookie if session_str is returned
        if result.get('session_str'):
            response.set_cookie(
                key="tg_session",
                value=result['session_str'],
                **TELEGRAM_COOKIE_SETTINGS
            )
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-code")
async def telegram_verify_code(
    data: VerifyCodeRequest, 
    response: Response,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    try:
        result = await verify_code(data.code, tg_session)
        
        # Update telegram session cookie if session_str is returned
        if result.get('session_str'):
            response.set_cookie(
                key="tg_session",
                value=result['session_str'],
                **TELEGRAM_COOKIE_SETTINGS
            )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-password")
async def telegram_verify_password(
    data: VerifyPasswordRequest, 
    response: Response,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    try:
        result = await verify_password(data.password, tg_session)
        
        # Update telegram session cookie if session_str is returned
        if result.get('session_str'):
            response.set_cookie(
                key="tg_session",
                value=result['session_str'],
                **TELEGRAM_COOKIE_SETTINGS
            )
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/check-auth")
async def telegram_check_auth(
    api_id: Optional[int] = Query(None),
    api_hash: Optional[str] = Query(None),
    phone: Optional[str] = Query(None),
    refresh: bool = Query(False),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not refresh:
        cached = await get_cached_auth_status()
        if cached and cached.get('authenticated'):
            return cached

    session_str = tg_session
    if api_id and api_hash:
        try:
            res = await connect_to_telegram(api_id, api_hash, phone, session_str)
            session_str = res.get('session_str', session_str)
        except HTTPException:
            pass

    try:
        result = await check_auth(session_str)
        if result.get('authenticated', False):
            await cache_auth_status(result)
        else:
           await invalidate_auth_status_cache()
        return result
    except Exception as e:
        await invalidate_auth_status_cache()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/channels")
async def telegram_get_channels(
    refresh: bool = Query(False),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    if not refresh:
        cached = await get_cached_channels()
        if cached is not None:
            return JSONResponse(content={'channels': cached})

    try:
        channels = await get_user_channels(tg_session)
        await cache_channels(channels)
        return JSONResponse(content={'channels': channels})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/disconnect")
async def telegram_disconnect(
    response: Response,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    try:
        result = await disconnect(tg_session)
        await reset_session(tg_session)
        invalidate_auth_status_cache()
        # Note: invalidate_channels_cache was removed as unused
        
        # Clear telegram session cookie
        response.delete_cookie(key="tg_session", path="/")
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/images")
async def telegram_get_channel_images(
    channel_id: int = Query(...),
    date: Optional[str] = Query(None),
    refresh: bool = Query(False),
    limit: int = Query(50),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    if not refresh:
        cached = await get_cached_channel_images(channel_id, date)
        if cached is not None:
            return {'images': cached.get('images', [])}
    
    try:
        images = await get_channel_images(channel_id, date, tg_session, limit)
        
        # Cache the results for future use
        if images:
            # Get channel title for caching
            from documind.telegram_service.telegram_fetcher import get_or_create_client
            client = await get_or_create_client(tg_session)
            if client and client.is_connected():
                try:
                    channel = await client.get_entity(channel_id)
                    channel_title = getattr(channel, 'title', f'Channel {channel_id}')
                except:
                    channel_title = f'Channel {channel_id}'
            else:
                channel_title = f'Channel {channel_id}'
                
            await cache_channel_images(channel_id, date, images, channel_title)
        
        return {'images': images}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dates")
async def telegram_get_channel_dates(
    channel_id: int = Query(...),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    try:
        dates = await get_channel_dates(channel_id, tg_session)
        return {'dates': dates}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download-image")
async def telegram_download_image(
    data: DownloadImageRequest,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    try:
        result = await download_image(
            data.message_id,
            data.channel_id,
            data.filename,
            tg_session
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download-multiple")
async def telegram_download_multiple_images(
    data: DownloadMultipleRequest,
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    try:
        downloaded = []
        for img in data.images:
            res = await download_image(img['id'], img['channel_id'], None, tg_session)
            if res.get('image_data'):
                downloaded.append({
                    'message_id': img['id'],
                    'channel_id': img['channel_id'],
                    'image_data': res['image_data'],
                    'metadata': res.get('metadata', {}),
                    'status': res.get('status', 'downloaded')
                })
        return {'downloaded': downloaded}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics")
async def telegram_get_channel_analytics(
    channel_id: int = Query(...),
    days: Optional[int] = Query(None),
    refresh: bool = Query(False),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    if days is None and not refresh:
        cached = await get_cached_channel_analytics(channel_id)
        if cached is not None:
            return cached

    try:
        analytics = await get_channel_analytics(channel_id, tg_session, days)
        await cache_channel_analytics(channel_id, analytics)
        return analytics
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload-to-drive")
async def telegram_upload_to_drive(
    data: UploadToDriveRequest = Body(...),
    tg_session: Optional[str] = Cookie(None)
) -> Dict[str, Any]:
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    if not data.images:
        raise HTTPException(status_code=400, detail="No images provided")
    
    import base64
    
    try:
        # Prepare image data list for upload
        image_data_list = []
        crop_counter = 1
        
        for i, img in enumerate(data.images):
            # Check if this is base64 image data (for cropped images)
            if 'image_data' in img:
                # Handle base64 image data with numbered filenames
                original_filename = img.get('original_filename')
                filename = f"{original_filename}_{crop_counter}.jpg" if original_filename else f"crop_{crop_counter}.jpg"
                
                image_data_list.append({
                    'image_data': img['image_data'],
                    'original_filename': filename,
                    'mime_type': 'image/jpeg',
                    'file_extension': '.jpg'
                })
                crop_counter += 1
            else:
                # Handle regular Telegram image IDs - get from cache
                image_result = await download_image(img['id'], img['channel_id'], None, tg_session)
                if image_result.get('image_data'):
                    metadata = image_result.get('metadata', {})
                    file_ext = metadata.get('file_extension', '.jpg')
                    
                    image_data_list.append({
                        'image_data': image_result['image_data'],
                        'message_id': img['id'],
                        'channel_id': img['channel_id'],
                        'file_extension': file_ext,
                        'mime_type': metadata.get('mime_type') or ('image/jpeg' if file_ext == '.jpg' else 'image/png'),
                        'original_filename': f"msg_{img['id']}{file_ext}"
                    })
        
        if not image_data_list:
            raise HTTPException(status_code=400, detail="No valid images to upload")
        
        # Upload to Google Drive using binary data directly
        uploader = DriveUploader()
        result = uploader.upload_telegram_images_from_cache(image_data_list, data.channel_name or 'Telegram')
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.get("/export-analytics-csv")
async def telegram_export_analytics_csv(
    tg_session: Optional[str] = Cookie(None)
):
    if not tg_session:
        raise HTTPException(status_code=400, detail="No telegram session found")
        
    try:
        channels = await get_user_channels(tg_session)
        csv_buffer = StringIO()
        import csv
        writer = csv.writer(csv_buffer)
        writer.writerow(['Channel ID','Name','Username','Participants','Total Images','Days','First Date','Last Date'])
        for ch in channels:
            stats = await get_channel_analytics(ch['id'], tg_session)
            ci = stats['channel']
            writer.writerow([
                ch['id'],
                ci['title'],
                ci.get('username',''),
                ci.get('participants_count',0),
                stats['total_images'],
                len(stats['dates']),
                stats.get('first_date'),
                stats.get('last_date')
            ])
        csv_buffer.seek(0)
        output = BytesIO(csv_buffer.getvalue().encode('utf-8'))
        return StreamingResponse(
            output,
            media_type='text/csv',
            headers={'Content-Disposition': 'attachment; filename="telegram_analytics.csv"'}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
