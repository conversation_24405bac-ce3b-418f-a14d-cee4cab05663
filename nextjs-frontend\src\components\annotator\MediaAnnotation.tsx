"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaArrowLeft,
  FaArrowRight,
  FaSave,
} from "react-icons/fa";
import { useRouter } from "next/navigation";
import { showToast } from "@/lib/toast";
import DynamicForm from "./DynamicForm";
import AISuggestionButton from "./AISuggestionButton";
import AISuggestionDialog from "./AISuggestionDialog";
import { MediaViewer } from "../shared/media";
import { 
  useAnnotationData, 
  useAnnotationState, 
  useFormConfig, 
  useSaveAnnotations,
  type AnnotationFile 
} from "./hooks";
import { API_BASE_URL } from "@/lib/api";

interface MediaAnnotationProps {
  files?: AnnotationFile[];
  adminInstructions?: string;
  folder?: string;
}

export default function MediaAnnotation({
  files = [],
  adminInstructions = "",
  folder = "",
}: MediaAnnotationProps) {
  console.log('MediaAnnotation component rendered');
  
  const router = useRouter();
  
  // Custom hooks
  const { 
    fetchedFiles, 
    fetchedInstructions, 
    batchName, 
    noTasks, 
    loadBatch 
  } = useAnnotationData();
  
  const {
    currentFileIndex,
    label,
    labels,
    formData,
    isFormValid,
    zoomLevel,
    allChangesSaved,
    batchCompleted,
    hasReviewed,
    currentFileSaved,
    setCurrentFileIndex,
    setLabel,
    setLabels,
    setAllChangesSaved,
    setBatchCompleted,
    setHasReviewed,
    setCurrentFileSaved,
    handlePrevious,
    handleNext,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleFormDataChange,
    handleFormValidationChange,
    resetState,
    calculateCompletedCount
  } = useAnnotationState();
  
  const { formConfig, loadFormConfig } = useFormConfig();
  const { saveIndividualAnnotation, saveAllAnnotations } = useSaveAnnotations();

  // Load batch and form config on mount
  useEffect(() => {
    console.log("MediaAnnotation useEffect triggered - loading batch and form config");
    
    const initializeComponent = async () => {
      resetState();
      
      try {
        await loadBatch(folder);
        await loadFormConfig();
      } catch (error) {
        console.error("Failed to initialize component:", error);
      }
    };
    
    initializeComponent();
  }, [loadBatch, loadFormConfig, folder, resetState]);

  // Use fetched files if present, otherwise props.files
  const fileArray = fetchedFiles.length > 0 ? fetchedFiles : files;
  const instructions = fetchedInstructions || adminInstructions;

  // Reset currentFileSaved when navigating to a new file
  useEffect(() => {
    const currentFile = fileArray.length > 0 ? fileArray[currentFileIndex] : null;
    
    if (currentFile) {
      // Always reset to false when changing files - user must save each file explicitly
      setCurrentFileSaved(false);
    }
  }, [currentFileIndex, fileArray, setCurrentFileSaved]);

  // Handle saving individual annotation
  const handleSaveAnnotation = useCallback(async () => {
    const currentFile = fileArray[currentFileIndex];
    if (!currentFile) return;
    
    // Check if there's data to save
    const hasData = formConfig.length > 0 
      ? formData[currentFile.url] && Object.keys(formData[currentFile.url]).length > 0
      : label.trim() !== "";
    
    if (!hasData) return;
    
    try {
      // Save individual annotation
      const result = await saveIndividualAnnotation(
        currentFile.url,
        label,
        formData[currentFile.url] || null,
        batchName,
        formConfig
      );
      
      if (result.success) {
        // Save simple label if it exists (for backward compatibility)
        if (label.trim()) {
          const newLabels = { ...labels, [currentFile.url]: label };
          setLabels(newLabels);
          localStorage.setItem("savedLabels", JSON.stringify(newLabels));
        }
        
        // Mark current file as saved
        setCurrentFileSaved(true);

        // Check if all files are now completed
        const completedCount = calculateCompletedCount(fileArray, formConfig);
        
        // If we just completed all files and haven't already triggered all changes saved state
        if (completedCount === fileArray.length && !allChangesSaved) {
          console.log("All files completed! Triggering all changes saved state");
          setAllChangesSaved(true);
          if (formConfig.length > 0) {
            showToast.success("All files completed! Please review your responses.");
            return; // Don't auto-advance for dynamic forms
          } else {
            showToast.success("All files labeled! Please review your responses.");
          }
        }

        // Auto-advance to next image after successful save (for both simple labels and dynamic forms)
        if (currentFileIndex < fileArray.length - 1) {
          handleNext(fileArray, formConfig);
        }
      }
    } catch (error) {
      console.error("Error saving annotation:", error);
      showToast.error("Failed to save annotation");
    }
  }, [
    fileArray, 
    currentFileIndex, 
    formConfig, 
    formData, 
    label, 
    batchName, 
    labels, 
    allChangesSaved,
    saveIndividualAnnotation,
    setLabels,
    setCurrentFileSaved,
    setAllChangesSaved,
    calculateCompletedCount,
    handleNext
  ]);

  const handleSaveAll = useCallback(async () => {
    try {
      const result = await saveAllAnnotations(labels, formData, batchName, formConfig);
      
      if (result.success) {
        setBatchCompleted(true);
      }
    } catch (error) {
      console.error("Error saving all annotations:", error);
    }
  }, [labels, formData, batchName, formConfig, saveAllAnnotations, setBatchCompleted]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (batchCompleted || hasReviewed) return;
      
      if (e.key === "ArrowLeft") handlePrevious(fileArray, formConfig);
      else if (e.key === "ArrowRight") {
        // Only allow navigation if current file is saved
        if (currentFileSaved) {
          handleNext(fileArray, formConfig);
        }
      }
      else if (e.key === "Enter") {
        // In dynamic form mode we do NOT want Enter to auto-save & advance
        if (formConfig.length === 0) {
          handleSaveAnnotation();
        }
      }
      else if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        handleSaveAll();
      }
    };
    
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    handlePrevious, 
    handleNext, 
    handleSaveAnnotation, 
    handleSaveAll, 
    formConfig, 
    batchCompleted, 
    hasReviewed,
    fileArray,
    currentFileSaved
  ]);

  const handleGetNextBatch = useCallback(async () => {
    console.log('Start Annotating logic triggered after batch completion!');
    
    // Store the current batch name to check if we get assigned the same batch
    const currentBatchName = batchName;
    console.log('Current batch name before assignment:', currentBatchName);
    
    try {
      // Try to assign a new batch using the start-annotation endpoint
      console.log('Attempting to assign next batch...');
      
      const startResponse = await fetch(`${API_BASE_URL}/annotator/start-annotation`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (startResponse.ok) {
        const result = await startResponse.json();
        console.log('Batch assignment result:', result);
        
        if (result.success) {
          // Check if this is an existing batch (user already has an active batch)
          if (result.is_existing_batch && result.message && result.message.includes('already has an active batch')) {
            console.log('User already has active batch - no new batch assignment needed');
            showToast.warning("No more batches available. You're already working on the last batch.");
            return;
          }
          
          // Reset state and reload the new batch
          resetState();
          const loadResult = await loadBatch();
          await loadFormConfig();
          
          // Verify we got a new batch
          if (loadResult && loadResult.success && loadResult.data) {
            const newBatchName = loadResult.data.batchName;
            console.log('Successfully assigned new batch:', newBatchName);
            showToast.success("New batch assigned! Loading...");
          } else {
            console.log('Batch loaded successfully');
            showToast.success("New batch assigned! Loading...");
          }
          
          return;
        } else {
          // Handle specific error codes from backend
          if (result.error_code === "ALL_BATCHES_COMPLETED") {
            console.log('User has completed all available batches');
            showToast.info("Congratulations! You've completed all available batches. Great work!");
          } else if (result.error_code === "NO_AVAILABLE_BATCHES") {
            console.log('No batches available for assignment');
            showToast.info("No batches are currently available for assignment.");
          } else {
            console.error('Failed to assign batch:', result.error);
            showToast.error(result.error || "No more batches available");
          }
        }
      } else if (startResponse.status === 404) {
        console.log('No batches available for assignment');
        showToast.info("No more batches available. Great work!");
      } else {
        console.error('Failed to start annotation:', startResponse.status);
        showToast.error("Failed to get next batch");
      }
    } catch (error) {
      console.error('Error getting next batch:', error);
      showToast.error("Error getting next batch");
    }

    // Fallback: navigate to dashboard if batch assignment fails
    console.log("Falling back to dashboard navigation...");
    showToast.info("Navigating to dashboard...");
    router.push("/annotator?view=dashboard");
  }, [router, resetState, loadBatch, loadFormConfig, batchName]);

  // Calculate progress
  const actualCompletedCount = calculateCompletedCount(fileArray, formConfig);
  const progress = fileArray.length > 0 ? Math.round((actualCompletedCount / fileArray.length) * 100) : 0;
  const isCompleted = actualCompletedCount === fileArray.length || allChangesSaved;

  const currentFile = fileArray.length > 0 ? fileArray[currentFileIndex] : null;

  // Handle AI suggestion application
  const handleAISuggestionApply = useCallback((fieldName: string, value: any) => {
    if (!currentFile) return;
    
    console.log('Applying AI suggestion:', fieldName, value);
    
    // Get current form data for this file
    const currentFormData = formData[currentFile.url] || {};
    
    // Update the specific field
    const updatedFormData = {
      ...currentFormData,
      [fieldName]: value
    };
    
    console.log('Updated form data:', updatedFormData);
    
    // Apply the change using the proper handler
    handleFormDataChange(currentFile.url, updatedFormData);
  }, [currentFile, formData, handleFormDataChange]);

  if (noTasks) {
    console.log("No tasks available - showing no tasks message");
    return (
      <div className="card mt-4">
        <div className="card-header bg-info text-white">No Tasks Available</div>
        <div className="card-body">
          <div className="alert alert-info">
            There are currently no annotation tasks available for you.
          </div>
          <p>You are currently in annotation mode.</p>
          <p>You can:</p>
          <ul>
            <li>Wait for an administrator to assign new tasks</li>
            <li>Contact your administrator for assistance</li>
          </ul>
          <button
            className="btn btn-primary"
            onClick={() => router.push("/annotator?view=dashboard")}
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  console.log("Rendering main annotation layout");
  return (
      <div className="grid grid-cols-[350px_1fr_260px] gap-4 mt-5">
      {/* Left sidebar: Task Guidelines */}
      <div className="guidelines-sidebar">
        <div className="card rounded-lg shadow-md">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h5 className="mb-0">Task Guidelines</h5>
          </div>
          <div className="card-body p-3">
            {instructions ? (
              <div
                className="instructions-content"
                dangerouslySetInnerHTML={{ __html: instructions }}
              />
            ) : (
              <p className="text-muted fst-italic">
                No specific instructions have been provided for this dataset.
              </p>
            )}
            <div className="best-practices mt-4">
              <h6 className="fw-bold text-primary mb-2">Best Practices</h6>
              <ul className="list-unstyled">
                <li>
                  <FaCheck className="text-success me-2" />
                  Be consistent with your labeling approach
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Take breaks to avoid fatigue
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Use keyboard shortcuts for efficiency
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Refer to guidelines if unsure
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        {/* AI Suggestion Dialog */}
        {formConfig.length > 0 && currentFile?.ai_suggestions && (
          <div className="card mt-3">
            <div className="card-header">
              <h6 className="mb-0">
                <i className="fas fa-robot me-2"></i>
                AI Assistance
              </h6>
            </div>
            <div className="card-body p-2">
              <AISuggestionDialog
                formConfig={formConfig}
                onApplySuggestion={handleAISuggestionApply}
                currentAISuggestions={currentFile?.ai_suggestions}
              />
            </div>
          </div>
        )}
      </div>

      {/* Main annotation area */}
      <div className="annotation-container">
        <div className="annotation-main">
          <div id="mediaDisplay" className="relative bg-gray-50 rounded-lg shadow-md h-96 flex justify-center items-center overflow-hidden">
            {currentFile && (
              <MediaViewer
                mediaPath={currentFile.path}
                mediaType={currentFile.type}
                zoomLevel={zoomLevel}
                isLabeled={!!labels[currentFile.url]}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetZoom={handleResetZoom}
                batchId={currentFile.csvBatchId}
                projectCode={currentFile.projectCode}
              />
            )}
            
            <div
              className="progress absolute bottom-0 left-0 right-0 m-0 rounded-none h-1.5"
            >
              <div
                className="progress-bar"
                role="progressbar"
                style={{ width: `${progress}%` }}
                aria-valuenow={progress}
                aria-valuemin={0}
                aria-valuemax={100}
              ></div>
            </div>
          </div>

          <div className="text-center mt-4 mb-5 text-base font-medium text-gray-700 py-2">
            <span>{actualCompletedCount}</span> of <span>{fileArray.length}</span>{" "}
            files {formConfig.length > 0 ? 'annotated' : 'labeled'} (<span>{progress}%</span>)
          </div>

          <div className="mt-5">
            {/* Dynamic Form or Simple Label Input */}
            {!batchCompleted && !hasReviewed && currentFile && (
              formConfig.length > 0 ? (
                <DynamicForm
                  formConfig={formConfig}
                  imageKey={currentFile.url}
                  initialData={formData[currentFile.url]}
                  onChange={handleFormDataChange}
                  onValidationChange={handleFormValidationChange}
                  aiSuggestions={currentFile?.ai_suggestions || null}
                />
              ) : (
                <div className="mb-3">
                  <div className="label-with-ai-suggestion">
                    <label htmlFor="labelInput" className="form-label">
                      Label <span className="text-danger">*</span>
                    </label>
                    {currentFile?.ai_suggestions && (
                      <AISuggestionButton
                        fieldName="label"
                        aiSuggestions={currentFile.ai_suggestions}
                        onApplySuggestion={(fieldName, value) => setLabel(String(value))}
                        className="ml-2"
                      />
                    )}
                  </div>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      id="labelInput"
                      placeholder="Enter label for this file"
                      value={label}
                      onChange={(e) => setLabel(e.target.value)}
                      required
                    />
                    <button
                      className="btn btn-primary"
                      onClick={handleSaveAnnotation}
                      title="Save Label (Enter)"
                      disabled={!label.trim()}
                      type="button"
                    >
                      <FaCheck /> Save
                    </button>
                  </div>
                  <div className="form-text text-muted">
                    Label is required before proceeding to the next file
                  </div>
                </div>
              )
            )}
            
            {/* Quick Save Button for Dynamic Forms */}
            {formConfig.length > 0 && !batchCompleted && !hasReviewed && currentFile && (
              <div className="mb-3 mt-1">
                <button
                  className="btn btn-success"
                  onClick={handleSaveAnnotation}
                  title="Save Form Data"
                  disabled={!isFormValid || !formData[currentFile.url] || Object.keys(formData[currentFile.url] || {}).length === 0}
                  type="button"
                >
                   Save Form Data
                </button>
              </div>
            )}

            {!batchCompleted && !hasReviewed && (
              <div className="flex justify-between items-center my-5">
                <button
                  className="btn btn-outline-primary min-w-25 flex items-center justify-center gap-2"
                  onClick={() => handlePrevious(fileArray, formConfig)}
                  disabled={currentFileIndex === 0}
                  type="button"
                >
                  <FaArrowLeft /> Previous
                </button>

                <span>
                  File {currentFileIndex + 1} of {fileArray.length}
                </span>

                <button
                  className="btn btn-outline-primary min-w-25 flex items-center justify-center gap-2"
                  onClick={() => handleNext(fileArray, formConfig)}
                  disabled={currentFileIndex === fileArray.length - 1 || !currentFileSaved}
                  title={!currentFileSaved ? "Please save data for current file first" : "Next file"}
                  type="button"
                >
                  Next <FaArrowRight />
                </button>
              </div>
            )}
            
            {batchCompleted && (
              <div className="alert alert-info mb-3 text-center">
                <div className="d-flex align-items-center justify-content-center mb-2">
                  <FaCheck className="me-2" />
                  <strong>Batch Completed Successfully!</strong>
                </div>
                <div className="text-center">
                  All your responses have been saved. You can now proceed to get the next set of files.
                </div>
              </div>
            )}

            <div className="mt-8">
              {allChangesSaved && !batchCompleted && (
                <div className="alert alert-success mb-2 py-1 px-2">
                  All files labeled! Ready to save.
                </div>
              )}
              
              {allChangesSaved && !batchCompleted && (
                <div className="review-confirmation mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="reviewConfirmation"
                      checked={hasReviewed}
                      onChange={(e) => setHasReviewed(e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="reviewConfirmation">
                      I have reviewed all my responses and they are correct
                    </label>
                  </div>
                </div>
              )}
              
              <div className="d-flex justify-content-center items-center space-x-4 mb-4">
                {!batchCompleted ? (
                  <button
                    className="btn btn-success min-w-38 flex items-center justify-center gap-2"
                    onClick={handleSaveAll}
                    disabled={!allChangesSaved || !hasReviewed}
                    title={
                      !allChangesSaved 
                        ? "Complete labeling all files to enable this button"
                        : !hasReviewed
                        ? "Please review your responses first"
                        : "Save all labels and complete this batch"
                    }
                    type="button"
                  >
                     Save All Labels
                  </button>
                ) : (
                  <button
                    className="btn btn-primary"
                    onClick={handleGetNextBatch}
                    title="Get next batch of files"
                    type="button"
                  >
                    Get Next Set of Files
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side: Keyboard Shortcuts */}
      <div className="shortcuts-sidebar">
        <div className="card">
          <div className="card-header">
            <h5 className="mb-0">Keyboard Shortcuts</h5>
          </div>
          <div className="card-body p-0">
            <div className="shortcut-list">
              <div className="shortcut-item">
                <span className="shortcut-text">Save label</span>
                <span className="key">↵ Enter</span>
              </div>
              <div className="shortcut-item">
                <span className="shortcut-text">Previous file</span>
                <span className="key">← Left</span>
              </div>
              <div className="shortcut-item">
                <span className="shortcut-text">Next file</span>
                <span className="key">→ Right</span>
              </div>
              <div className="shortcut-item">
                <span className="shortcut-text">Save all labels</span>
                <span className="key">Ctrl+S</span>
              </div>
              <div className="shortcut-item">
                <span className="shortcut-text">Zoom in</span>
                <span className="key">+</span>
              </div>
              <div className="shortcut-item">
                <span className="shortcut-text">Zoom out</span>
                <span className="key">-</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
