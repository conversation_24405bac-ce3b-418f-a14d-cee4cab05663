import React from 'react';
import { FaTimes, FaCalendarAlt } from 'react-icons/fa';
import { ProjectRegistryResponse } from '../types';
import { formatDate } from '../utils';

interface DeadlineModalProps {
  project: ProjectRegistryResponse;
  isOpen: boolean;
  onClose: () => void;
  selectedDeadline: string;
  onDeadlineChange: (deadline: string) => void;
  onSave: () => void;
  loading: boolean;
}

export const DeadlineModal: React.FC<DeadlineModalProps> = ({
  project,
  isOpen,
  onClose,
  selectedDeadline,
  onDeadlineChange,
  onSave,
  loading,
}) => {
  if (!isOpen || !project) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <FaCalendarAlt className="mr-2" />
                {project.project_deadline ? 'Change Project Deadline' : 'Set Project Deadline'}
              </h2>
              <p className="text-gray-600 mt-1">Project: {project.project_name}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FaTimes className="text-xl" />
            </button>
          </div>

          <div className="space-y-4">
            {/* Calendar Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Deadline Date
              </label>
              <input
                type="date"
                min={new Date().toISOString().split('T')[0]}
                value={selectedDeadline}
                onChange={(e) => onDeadlineChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Selected Date Display */}
            {selectedDeadline && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="text-sm">
                  <span className="font-medium text-blue-800">Selected Deadline:</span>
                  <div className="text-blue-700 mt-1">{formatDate(selectedDeadline)}</div>
                </div>
              </div>
            )}

            {/* Current Deadline Display */}
            {project.project_deadline && (
              <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
                <div className="text-sm">
                  <span className="font-medium text-gray-700">Current Deadline:</span>
                  <div className="text-gray-600 mt-1">{formatDate(project.project_deadline)}</div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={onSave}
                disabled={!selectedDeadline || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {loading ? 'Saving...' : project.project_deadline ? 'Update Deadline' : 'Set Deadline'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
