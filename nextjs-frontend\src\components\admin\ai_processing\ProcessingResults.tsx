"use client";
import React, { useState, useEffect } from "react";
import { FaSpinner } from "react-icons/fa";
import toast from "react-hot-toast";
import { FileAllocationResult, useFileAllocationService } from "./fileAllocationService";

export interface ProcessingResultsProps {
  projectCode?: string;
  processingType?: string;
  limit?: number;
  onSelectResult?: (result: FileAllocationResult) => void;
}

export const ProcessingResults: React.FC<ProcessingResultsProps> = ({
  projectCode,
  processingType,
  limit = 100, // Increased limit to show more results
  onSelectResult
}) => {
  const [results, setResults] = useState<FileAllocationResult[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedResult, setSelectedResult] = useState<FileAllocationResult | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalResults, setTotalResults] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(false);

  // Use the file allocation service
  const fileAllocationService = useFileAllocationService();

  useEffect(() => {
    setCurrentPage(1); // Reset to first page when project or processing type changes
    fetchProcessingResults(1);
    // Remove automatic polling - only refresh when user manually clicks refresh
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectCode, processingType]);

  const fetchProcessingResults = async (page: number = currentPage, append: boolean = false) => {
    setIsLoading(true);
    setError(null);

    try {
      // Calculate offset based on page number and ensure it's a valid number
      const pageNum = typeof page === 'number' && !isNaN(page) ? page : 1;
      const offsetNum = (pageNum - 1) * limit;
      
      // Fetch results from file_allocation table
      const data = await fileAllocationService.fetchFileAllocationResults(
        projectCode,
        processingType,
        limit,
        offsetNum
      );
      
      const newResults = data || [];
      
      // Update state based on whether we're appending or replacing
      if (append) {
        setResults(prev => [...prev, ...newResults]);
      } else {
        setResults(newResults);
      }
      
      // Update pagination info
      setCurrentPage(page);
      setTotalResults(data.length || newResults.length);
      setHasMore(newResults.length === limit); // If we got a full page, there might be more
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching processing results:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load processing results: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };
  

  const handleSelectResult = (result: FileAllocationResult) => {
    setSelectedResult(result);
    if (onSelectResult) {
      onSelectResult(result);
    }
  };

  const getProcessingTypeLabel = (type: string) => {
    switch (type.toLowerCase()) {
      case "caption":
        return "Caption";
      case "ocr":
        return "OCR";
      case "vqa":
        return "VQA";
      case "transcription":
        return "Transcription";
    }
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "success":
      case "completed":
        return "text-green-600 bg-green-50";
      case "failed":
      case "error":
        return "text-red-600 bg-red-50";
      case "warning":
        return "text-yellow-600 bg-yellow-50";
      default:
        return "text-blue-600 bg-blue-50";
    }
  };

  const renderResultDetails = (result: FileAllocationResult) => {
    if (!result) return null;

    // Extract the appropriate content based on processing type
    let content: React.ReactNode = null;
    
    // Safely extract result data, handling various formats
    let resultData: Record<string, unknown>;
    try {
      if (!result.preprocessing_results) {
        resultData = {};
      } else if (typeof result.preprocessing_results === 'string') {
        try {
          // Try to parse if it's a JSON string
          resultData = JSON.parse(result.preprocessing_results);
        } catch {
          // If parsing fails, use as a plain string
          resultData = { text: result.preprocessing_results };
        }
      } else {
        // Use as is if it's already an object
        resultData = result.preprocessing_results as Record<string, unknown>;
      }
    } catch (error) {
      console.error("Error processing result data:", error);
      resultData = {};
    }

    // Handle different result types based on processing_type
    if (result.processing_type === "caption") {
      // For caption results - check multiple possible locations
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const caption = (resultData as any)?.model_response?.caption || 
                     (resultData as any)?.caption || 
                     (typeof resultData === 'string' ? resultData : null) ||
                     // eslint-disable-next-line @typescript-eslint/no-explicit-any
                     ((resultData as any)?.result?.caption) || 
                     "No caption available";
      
      content = (
        <div className="mt-2 p-3 bg-blue-50 rounded">
          <span className="font-medium text-blue-700">Caption:</span>
          <p className="mt-1 text-gray-700">{String(caption)}</p>
        </div>
      );
    } else if (result.processing_type === "ocr") {
      // For OCR results - check multiple possible locations
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const extractedText = (resultData as any)?.model_response?.extracted_text || 
                           (resultData as any)?.extracted_text || 
                           // eslint-disable-next-line @typescript-eslint/no-explicit-any
                           (resultData as any)?.text || 
                           (typeof resultData === 'string' ? resultData : null) ||
                           "No text extracted";
      
      content = (
        <div className="mt-2 p-3 bg-green-50 rounded">
          <span className="font-medium text-green-700">Extracted Text:</span>
          <p className="mt-1 text-gray-700 max-h-60 overflow-y-auto">{String(extractedText)}</p>
        </div>
      );
    } else if (result.processing_type === "vqa") {
      // For VQA results - check multiple possible locations
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const answer = (resultData as any)?.model_response?.answer || 
                    (resultData as any)?.answer || 
                    (typeof resultData === 'string' ? resultData : null) ||
                    "No answer available";
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const question = (resultData as any)?.model_response?.question || 
                      (resultData as any)?.question || 
                      (resultData as any)?.prompt || "";
      
      content = (
        <div className="mt-2 p-3 bg-purple-50 rounded">
          <span className="font-medium text-purple-700">Answer:</span>
          <p className="mt-1 text-gray-700">{String(answer)}</p>
          {question && (
            <p className="mt-1 text-sm text-gray-500">
              <span className="font-medium">Question:</span> {String(question)}
            </p>
          )}
        </div>
      );
    } else if (result.processing_type === "transcription") {
      // For transcription results - check multiple possible locations
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const transcription = (resultData as any)?.model_response?.transcription || 
                          (resultData as any)?.transcription || 
                          (typeof resultData === 'string' ? resultData : null) ||
                          "No transcription available";
      
      content = (
        <div className="mt-2 p-3 bg-orange-50 rounded">
          <span className="font-medium text-orange-700">Transcription:</span>
          <p className="mt-1 text-gray-700 max-h-80 overflow-y-auto">{String(transcription)}</p>
        </div>
      );
    } else if (resultData?.annotation_results && typeof resultData.annotation_results === 'object') {
      // Handle annotation-based results
      try {
        const annotationResults = resultData.annotation_results as Record<string, {question?: string; answer?: string}>;
        content = (
          <div className="mt-2 p-3 bg-indigo-50 rounded">
            <span className="font-medium text-indigo-700">Annotation Results:</span>
            {Object.entries(annotationResults).map(([key, value]) => {
                const question = value?.question || 'Question';
                const answer = value?.answer || 'No answer';
                return (
                  <div key={key} className="mt-2 border-t border-indigo-100 pt-2">
                    <p className="font-medium text-indigo-600">{question}</p>
                    <p className="text-gray-700">{answer}</p>
                  </div>
                );
              })}
          </div>
        );
      } catch (error) {
        console.error("Error rendering annotation results:", error);
        content = (
          <div className="mt-2 p-3 bg-red-50 rounded">
            <span className="font-medium text-red-700">Error displaying annotation results</span>
          </div>
        );
      }
    } else {
      // Generic result display
      content = (
        <div className="mt-2 p-3 bg-gray-50 rounded">
          <span className="font-medium text-gray-700">Result:</span>
          <pre className="mt-1 text-sm text-gray-700 whitespace-pre-wrap overflow-y-auto max-h-80">
            {JSON.stringify(resultData, null, 2)}
          </pre>
        </div>
      );
    }

    return (
      <div className="mt-4 space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            {result.original_filename || result.file_identifier}
          </h3>
          <div className="flex items-center space-x-2">
            <span className={`text-sm px-2 py-1 rounded-full ${getStatusColor(result.processing_status)}`}>
              {result.processing_status}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
          <div>
            <span className="font-medium">Project:</span> {result.project_code}
          </div>
          <div>
            <span className="font-medium">Model:</span> {result.model_name || "N/A"}
          </div>
          <div>
            <span className="font-medium">Processing Type:</span> {result.processing_type}
          </div>
          <div>
            <span className="font-medium">Processed At:</span> {formatDate(result.processed_at)}
          </div>
          <div>
            <span className="font-medium">File Type:</span> {result.processed_metadata?.file_type || "N/A"}
          </div>
        </div>

        {content}
        
        <div className="text-xs text-gray-500 mt-2">
          File Identifier: {result.file_identifier}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <FaSpinner className="animate-spin text-blue-500 text-2xl mb-2" />
        <p className="text-gray-600">Loading processing results...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-4 rounded-md">
        <p className="text-red-700">Error: {error}</p>
          <button 
            onClick={() => void fetchProcessingResults()}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800"
          >
          Try again
        </button>
      </div>
    );
  }

  if (!projectCode) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Please select a project to view results</p>
        <p className="text-sm text-gray-400 mt-1">Processing results are stored by project</p>
      </div>
    );
  }
  
  if (results.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No processing results found</p>
        {projectCode && <p className="text-sm text-gray-400 mt-1">No results for project: {projectCode}</p>}
        {processingType && <p className="text-sm text-gray-400 mt-1">No results for type: {processingType}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Processing Results</h2>
        <button 
          onClick={() => fetchProcessingResults(1, false)}
          className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
        >
          <FaSpinner className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {selectedResult ? (
        <div>
          <button
            onClick={() => setSelectedResult(null)}
            className="mb-4 text-sm text-blue-600 hover:text-blue-800"
          >
            ← Back to results list
          </button>
          <div className="bg-white p-4 rounded-md border border-gray-200">
            {renderResultDetails(selectedResult)}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-md border border-gray-200 overflow-hidden">
          <ul className="divide-y divide-gray-200">
            {results.map((result, index) => {
              // Extract a preview of the result based on processing type
              let resultPreview = "";
              try {
                const resultData = result.preprocessing_results as Record<string, unknown>;
                // Check multiple possible locations for the data
                if (result.processing_type === "caption") {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const caption = (resultData as any)?.model_response?.caption || 
                                 (resultData as any)?.caption;
                  if (caption && typeof caption === 'string') {
                    resultPreview = caption.substring(0, 50) + (caption.length > 50 ? "..." : "");
                  }
                } else if (result.processing_type === "ocr") {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const text = (resultData as any)?.model_response?.extracted_text || 
                              (resultData as any)?.extracted_text;
                  if (text && typeof text === 'string') {
                    resultPreview = text.substring(0, 50) + (text.length > 50 ? "..." : "");
                  }
                } else if (result.processing_type === "vqa") {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const answer = (resultData as any)?.model_response?.answer || 
                                (resultData as any)?.answer;
                  if (answer && typeof answer === 'string') {
                    resultPreview = answer.substring(0, 50) + (answer.length > 50 ? "..." : "");
                  }
                } else if (result.processing_type === "transcription") {
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  const transcription = (resultData as any)?.model_response?.transcription || 
                                       (resultData as any)?.transcription;
                  if (transcription && typeof transcription === 'string') {
                    resultPreview = transcription.substring(0, 50) + (transcription.length > 50 ? "..." : "");
                  }
                }
              } catch (error) {
                console.error("Error generating result preview:", error);
              }
              
              return (
                <li 
                  key={`${result.id}-${result.file_identifier}-${index}`}
                  className="p-4 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleSelectResult(result)}
                >
                  <div className="flex items-start">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">
                          {result.original_filename || result.file_identifier || `Result ${result.id}`}
                        </p>
                        <div className="flex items-center">
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(result.processing_status)}`}>
                            {result.processing_status}
                          </span>
                        </div>
                      </div>
                      <p className="text-xs text-gray-500">
                        Project: {result.project_code} | Model: {result.model_name || "N/A"}
                      </p>
                      <p className="text-xs text-gray-500">
                        Type: {result.processing_type} | Processed: {formatDate(result.processed_at)}
                      </p>
                      {resultPreview && (
                        <p className="text-xs text-gray-600 mt-1 italic">
                          {resultPreview}
                        </p>
                      )}
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
          
          {hasMore && (
            <div className="p-4 border-t border-gray-200 flex justify-center">
              <button
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.preventDefault();
                  const nextPage = currentPage + 1;
                  if (!isLoading && hasMore) {
                    void fetchProcessingResults(nextPage, true);
                  }
                }}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center disabled:opacity-50"
              >
                {isLoading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" /> Loading...
                  </>
                ) : (
                  "Load More Results"
                )}
              </button>
            </div>
          )}
          
          {results.length > 0 && (
            <div className="p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 text-center">
              Showing {results.length} of {totalResults} results
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProcessingResults;