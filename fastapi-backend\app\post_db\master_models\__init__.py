# Master Database Models Package
# Contains models for the centralized master database that coordinates multiple project databases

# Import only the essential models to avoid circular imports
from .users import users, UserRole
from .projects_registry import ProjectsRegistry
from .user_project_access import UserProjectAccess
from .global_workload_summary import GlobalWorkloadSummary
from .cross_project_analytics import CrossProjectAnalytics
from .allocation_strategies import AllocationStrategies
from .clients import Clients

__all__ = [
    'ProjectsRegistry',
    'users',
    'UserProjectAccess',    
    'GlobalWorkloadSummary',
    'CrossProjectAnalytics',
    'UserRole',
    'AllocationStrategies',
    'Clients'
]