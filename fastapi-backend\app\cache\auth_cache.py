"""
Authentication-related caching functionality.
"""
from cache.redis_connector import cache_set, cache_get, cache_delete, cache_exists
from cache.base import serialize_for_cache
import logging
from core.config import settings

logger = logging.getLogger('auth_cache')

# Cache key prefixes
USER_ROLE_PREFIX = "user:role:"
USER_DATA_PREFIX = "user:data:"

# Cache TTL values (in seconds) - loaded from configuration
USER_ROLE_TTL = settings.redis_settings.auth_user_role_ttl
USER_DATA_TTL = settings.redis_settings.auth_user_data_ttl

def generate_user_role_key(username):
    """
    Generate a cache key for a user's role

    Args:
        username: Username

    Returns:
        str: Cache key
    """
    return f"{USER_ROLE_PREFIX}{username}"

def generate_user_data_key(username):
    """
    Generate a cache key for a user's data

    Args:
        username: Username

    Returns:
        str: Cache key
    """
    return f"{USER_DATA_PREFIX}{username}"

def cache_user_role(username, role):
    """
    Cache a user's role

    Args:
        username: Username
        role: User role

    Returns:
        bool: Success status
    """
    key = generate_user_role_key(username)
    ttl = USER_ROLE_TTL

    logger.info(f"Caching role for user {username}: {role}")
    return cache_set(key, {'role': role}, ttl)

def get_cached_user_role(username):
    """
    Get a cached user's role

    Args:
        username: Username

    Returns:
        str: User role or None if not found
    """
    key = generate_user_role_key(username)
    logger.debug(f"Getting cached role for user {username}")
    data = cache_get(key, json_decode=True)
    return data.get('role') if data else None

def cache_user_data(username, user_data):
    """
    Cache a user's data

    Args:
        username: Username
        user_data: User data dictionary

    Returns:
        bool: Success status
    """
    key = generate_user_data_key(username)
    ttl = USER_DATA_TTL

    # Serialize datetime objects to strings
    serialized_user_data = serialize_for_cache(user_data)

    logger.info(f"Caching data for user {username}")
    return cache_set(key, serialized_user_data, ttl)

def get_cached_user_data(username):
    """
    Get cached user data

    Args:
        username: Username

    Returns:
        dict: User data or None if not found
    """
    key = generate_user_data_key(username)
    logger.debug(f"Getting cached data for user {username}")
    return cache_get(key, json_decode=True)

def invalidate_user_role_cache(username):
    """
    Invalidate a user's role cache

    Args:
        username: Username

    Returns:
        bool: Success status
    """
    key = generate_user_role_key(username)
    logger.info(f"Invalidating role cache for user {username}")
    return cache_delete(key)

def invalidate_user_data_cache(username):
    """
    Invalidate a user's data cache

    Args:
        username: Username

    Returns:
        bool: Success status
    """
    key = generate_user_data_key(username)
    logger.info(f"Invalidating data cache for user {username}")
    return cache_delete(key)

def invalidate_all_user_caches(username):
    """
    Invalidate all caches for a user

    Args:
        username: Username

    Returns:
        bool: Success status
    """
    role_cache_deleted = invalidate_user_role_cache(username)
    data_cache_deleted = invalidate_user_data_cache(username)
    logger.info(f"Invalidated all caches for user {username}")
    return role_cache_deleted and data_cache_deleted
