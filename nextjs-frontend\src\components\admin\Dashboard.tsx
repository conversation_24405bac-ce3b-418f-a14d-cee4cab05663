"use client";

import { useState, useEffect } from "react";
import {
  Fa<PERSON>sers,
  FaUserPlus,
  FaMicrochip,
  FaBalanceScale,
  FaClock,
  FaProjectDiagram,
} from "react-icons/fa";
import toast from "react-hot-toast";
import ConnectorCard from "@/components/admin/ConnectorCard";
import ConnectGoogleDriveModal from "@/components/admin/ConnectGoogleDriveModal";

import { API_BASE_URL } from "@/lib/api";

interface DashboardProps {
  onNavigate: (component: string) => void;
}

export default function Dashboard({ onNavigate }: DashboardProps) {
  const [googleDriveConnected, setGoogleDriveConnected] = useState(false);
  const [showDriveModal, setShowDriveModal] = useState(false);
  const [driveLoading, setDriveLoading] = useState(false);

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const res = await fetch(`${API_BASE_URL}/admin/dashboard`, {
          credentials: "include",
        });
        const json = await res.json();
        if (json.data?.config) {
          const cfg = json.data.config;
          setGoogleDriveConnected(cfg.drive_connected);
        }
      } catch (e) {
        console.error(e);
      }
    };
    fetchStatus();
    checkGoogleDriveConnection();
  }, []);

  useEffect(() => {
    const handleMessage = (e: MessageEvent) => {
      if (e.data === "google-auth-success") {
        toast.success("Google Drive connected successfully!");
        checkGoogleDriveConnection();
      } else if (e.data === "google-auth-error") {
        toast.error("Google Drive connection failed. Please try again.");
      }
    };
    window.addEventListener("message", handleMessage);
    return () => window.removeEventListener("message", handleMessage);
  }, []);

  

  const checkGoogleDriveConnection = async () => {
    try {
      const res = await fetch(
        `${API_BASE_URL}/admin/check-google-drive-connection`,
        { credentials: "include" }
      );
      const json = await res.json();
      if (json.success) {
        setGoogleDriveConnected(json.data?.connected);
      }
    } catch (e) {
      console.error(e);
    }
  };



  const handleConnectDrive = async (data: {
    clientId: string;
    clientSecret: string;
    folderId?: string;
  }) => {
    setDriveLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/admin/configure-google-drive`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          client_id: data.clientId,
          client_secret: data.clientSecret,
          folder_id: data.folderId,
        }),
      });
      const json = await res.json();
      if (json.data?.auth_url) {
        window.open(
          json.data.auth_url,
          "GoogleDriveAuth",
          "width=600,height=700"
        );
        toast("Continue in Google popup to finish authentication.", {
          icon: "🔗",
        });
      }
    } catch (e) {
      console.error(e);
      toast.error("Failed to start Google Drive authentication.");
    } finally {
      setDriveLoading(false);
      setShowDriveModal(false);
    }
  };

  const handleMinioConfigure = () => {
    onNavigate("clientOnboarding");
  };

  return (
    <>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's an overview of your system.</p>
      </div>
      {/* Quick Actions */}
              <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-200 mb-2">
        <h2 className="text-lg font-bold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          <button
            onClick={() => onNavigate('clientOnboarding')}
            className="flex flex-col items-center justify-center p-2 rounded-md border border-gray-300 hover:border-blue-500 hover:bg-blue-50 transition-colors text-center"
          >
            <FaUserPlus className="text-blue-500 text-lg mb-1" />
            <span className="text-xs font-medium text-gray-700">Client Onboarding</span>
          </button>

          <button
            onClick={() => onNavigate('userManagement')}
            className="flex flex-col items-center justify-center p-2 rounded-md border border-gray-300 hover:border-green-500 hover:bg-green-50 transition-colors text-center"
          >
            <FaUsers className="text-green-500 text-lg mb-1" />
            <span className="text-xs font-medium text-gray-700">Manage Users</span>
          </button>

          <button
            onClick={() => onNavigate('projects')}
            className="flex flex-col items-center justify-center p-2 rounded-md border border-gray-300 hover:border-green-500 hover:bg-green-50 transition-colors text-center"
          >
            <FaProjectDiagram className="text-blue-500 text-lg mb-1" />
            <span className="text-xs font-medium text-gray-700">Manage Projects</span>
          </button>

          <button
            onClick={() => onNavigate('aiModelsRegistry')}
            className="flex flex-col items-center justify-center p-2 rounded-md border border-gray-300 hover:border-purple-500 hover:bg-purple-50 transition-colors text-center"
          >
            <FaMicrochip className="text-purple-500 text-lg mb-1" />
            <span className="text-xs font-medium text-gray-700">AI Models</span>
          </button>

          <button
            onClick={() => onNavigate('allocationStrategy')}
            className="flex flex-col items-center justify-center p-2 rounded-md border border-gray-300 hover:border-orange-500 hover:bg-orange-50 transition-colors text-center"
          >
            <FaBalanceScale className="text-orange-500 text-lg mb-1" />
            <span className="text-xs font-medium text-gray-700">Allocation Strategy</span>
          </button>
        </div>
      </div>

      {/* Data Connectors */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="mb-2">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">Data Connectors</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* <ConnectorCard
              title="Google Drive"
              description="Connect to manage datasets stored on Google Drive."
              connectButton={
                // <button
                //   onClick={() => setShowDriveModal(true)}
                //   className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-4 py-2 rounded-lg font-semibold shadow hover:from-blue-600 hover:to-blue-800 focus:outline-none"
                // >
                //   Configure
                // </button>
                <span className="inline-block px-4 py-2 rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 text-gray-500 font-semibold cursor-not-allowed opacity-70 shadow">
                  <FaClock className="inline mr-2" />
                  Coming Soon
                </span>
              }
            /> */}
            <ConnectorCard
              title="Processvenue's Internal Bucket Storage"
              description="Connect to access and manage datasets stored on Processvenue's Intern Bucket Storage."
              connectButton={
                <button
                  onClick={handleMinioConfigure}
                  className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-2 rounded-lg font-semibold shadow hover:from-pink-600 hover:to-pink-800 focus:outline-none"
                >
                  Configure
                </button>
              }
            />
            <ConnectorCard
              title="NAS Storage"
              description="Connect to access and manage datasets stored on Network Attached Storage."
              connectButton={
                <button
                  onClick={() => onNavigate("clientOnboarding")}
                  className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-700 text-white px-4 py-2 rounded-lg font-semibold shadow hover:from-green-600 hover:to-green-800 focus:outline-none"
                >
                  Configure
                </button>
              }
            >
            </ConnectorCard>

            <ConnectorCard
              title="Azure Storage"
              description="Connect to access and manage image datasets stored on Microsoft Azure."
              connectButton={
                <span className="inline-block px-4 py-2 rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 text-gray-500 font-semibold cursor-not-allowed opacity-70 shadow">
                  <FaClock className="inline mr-2" />
                  Coming Soon
                </span>
              }
            />
            <ConnectorCard
              title="AWS S3 Storage"
              description="Connect to access and manage image datasets stored on Amazon Web Services."
              connectButton={
                <span className="inline-block px-4 py-2 rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 text-gray-500 font-semibold cursor-not-allowed opacity-70 shadow">
                  <FaClock className="inline mr-2" />
                  Coming Soon
                </span>
              }
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      <ConnectGoogleDriveModal
        show={showDriveModal}
        onClose={() => setShowDriveModal(false)}
        onSubmit={handleConnectDrive}
        isLoading={driveLoading}
      />
    </>
  );
}
