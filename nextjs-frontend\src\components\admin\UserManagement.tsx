"use client";

import React, { useState, useEffect } from "react";
import {
  FaEdit,
  FaTrash,
  FaPlus,
  FaSearch,
  FaShieldAlt,
  FaUserTag,
  FaEye,
  FaUsers,
  FaPauseCircle,
  FaPlayCircle,
  FaInfoCircle,
  FaChartPie,
  FaCheckCircle,
  FaTimes,
} from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/lib/toast";

import { API_BASE_URL } from "@/lib/api";

// Define User interface matching backend
interface User {
  id: number;
  username: string;
  full_name: string;
  email: string;
  role: "admin" | "annotator" | "auditor" | "client";
  is_active: boolean;
  created_at: string;
  last_login?: string;
  annotator_mode?: "annotation" | "verification" | "supervision";
}

type UserRole = User["role"];
type AnnotatorMode = "annotation" | "verification" | "supervision";

export default function UserManagement() {
  const { user: currentAuthUser } = useAuth();

  // Validation errors for registration form
  interface NewUserErrors {
    username?: string;
    password?: string;
    confirmPassword?: string;
    email?: string;
    fullName?: string;
  }
  const [errors, setErrors] = useState<NewUserErrors>({});

  // State for users
  const [users, setUsers] = useState<User[]>([]);

  // Helper for email validation
  const isValidEmail = (email: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Helper to format ISO datetime strings into a readable local format
  const formatDateTime = (iso: string) => {
    const date = new Date(iso);
    return date.toLocaleString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  // UI state
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState<UserRole | "all">("all");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<{
    username: string;
    password: string;
    confirmPassword: string;
    fullName: string;
    email: string;
    role: UserRole;
    status: "active" | "suspended";
    annotationMode: AnnotatorMode;
  }>({
    username: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    email: "",
    role: "auditor",
    status: "active",
    annotationMode: "annotation",
  });
  const [loggedInUsername, setLoggedInUsername] = useState<string | null>(null);

  // Loading and suspend modal state
  const [suspendUser, setSuspendUser] = useState<User | null>(null);
  const [isFlushModalOpen, setIsFlushModalOpen] = useState(false);

  // Count users by role
  const adminCount = users.filter((u) => u.role === "admin").length;
  const annotatorCount = users.filter((u) => u.role === "annotator").length;
  const auditorCount = users.filter((u) => u.role === "auditor").length;
  const clientCount = users.filter((u) => u.role === "client").length;

  // Filter users based on search term and status filter
  const filteredUsers = users.filter((user) => {
    const text =
      `${user.username} ${user.full_name} ${user.email} ${user.role}`.toLowerCase();
    const matchesSearch = text.includes(debouncedSearchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "active" ? user.is_active : !user.is_active);
    const matchesRole = roleFilter === "all" || user.role === roleFilter;
    return matchesSearch && matchesStatus && matchesRole;
  });

  // Fetch users on mount
  useEffect(() => {
    setLoggedInUsername(currentAuthUser?.username || null);

    const fetchUsers = async () => {
      try {
        const res = await authFetch(`${API_BASE_URL}/admin/users`);
        const json = await res.json();
        // API may return plain array or envelope { success, message, data }
        const list: User[] = Array.isArray(json)
          ? (json as User[])
          : Array.isArray(json.data)
          ? (json.data as User[])
          : [];
        if (Array.isArray(list)) {
          setUsers(list);
        } else {
          console.error("Unexpected users response format", json);
          setUsers([]);
        }
      } catch (err) {
        console.error("Failed to fetch users", err);
        setUsers([]);
      }
    };
    fetchUsers();
  }, [currentAuthUser]);

  // Flush DB handler
  const handleFlushDb = async () => {
    try {
      const res = await authFetch(`${API_BASE_URL}/admin/flush-db`, {
        method: "POST",
        credentials: "include",
      });
      const json = await res.json();
      showToast.success(json.message || "Database flushed successfully");
    } catch (err) {
      console.error("Flush DB failed", err);
      showToast.error("Failed to flush database");
    } finally {
      setIsFlushModalOpen(false);
    }
  };

  // Handle user edit
  const handleEditUser = (user: User) => {
    setCurrentUser(user);
    setIsEditModalOpen(true);
  };

  // Handle user delete/suspend
  const handleSuspendUser = (user: User) => {
    setSuspendUser(user);
  };

  // Handle user reactivation
  const handleReactivateUser = async (user: User) => {
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/users/${user.username}/suspend`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ action: "unsuspend" }),
        }
      );
      if (!res.ok) throw new Error(`Failed to reactivate user: ${res.status}`);
      setUsers(
        users.map((u) =>
          u.username === user.username ? { ...u, is_active: true } : u
        )
      );
    } catch (err) {
      console.error("Reactivate user failed", err);
      showToast.error("Failed to reactivate user");
    }
  };

  // Confirm suspension in modal
  const handleConfirmSuspend = async (user: User) => {
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/users/${user.username}/suspend`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ action: "suspend" }),
        }
      );
      if (!res.ok) throw new Error(`Failed to suspend user: ${res.status}`);
      setUsers(
        users.map((u) =>
          u.username === user.username ? { ...u, is_active: false } : u
        )
      );
      setSuspendUser(null);
    } catch (err) {
      console.error("Suspend user failed", err);
      showToast.error("Failed to suspend user");
    }
  };

  // Handle form submission for adding a new user
  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const res = await authFetch(`${API_BASE_URL}/admin/add-user`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: newUser.username,
          full_name: newUser.fullName,
          email: newUser.email,
          role: newUser.role,
          is_active: newUser.status === "active",
          password: newUser.password,
          annotation_mode: newUser.annotationMode,
        }),
      });
      if (!res.ok) throw new Error(`Failed to add user: ${res.status}`);
      const json = await res.json();
      const created: User = Array.isArray(json)
        ? (json[0] as User)
        : json.data?.user
        ? (json.data.user as User)
        : (json.data as User) || (json as User);
      setUsers([...users, created]);
      setNewUser({
        username: "",
        fullName: "",
        email: "",
        password: "",
        confirmPassword: "",
        role: "annotator",
        status: "active",
        annotationMode: "annotation",
      });
      setIsAddModalOpen(false);
    } catch (err) {
      console.error("Add user failed", err);
      showToast.error("Failed to add user");
    }
  };

  // Handle form submission for editing a user
  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/users/${currentUser.username}`,
        {
          method: "PUT",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            username: currentUser.username,
            full_name: currentUser.full_name,
            email: currentUser.email,
            role: currentUser.role,
            is_active: currentUser.is_active,
            annotator_mode: currentUser.annotator_mode,
          }),
        }
      );
      if (!res.ok) throw new Error(`Failed to update user: ${res.status}`);
      const json = await res.json();
      const updated: User = json.data || json;
      setUsers(users.map((user) => (user.id === updated.id ? updated : user)));
      setIsEditModalOpen(false);
    } catch (err) {
      console.error("Update user failed", err);
      showToast.error("Failed to update user");
    }
  };

  // Registration form submit with validation
  const handleRegisterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors: NewUserErrors = {};
    // Required field checks
    if (!newUser.username.trim()) {
      validationErrors.username = "Username is required.";
    }
    if (!newUser.fullName.trim()) {
      validationErrors.fullName = "Full name is required.";
    }
    if (!newUser.password) {
      validationErrors.password = "Password is required.";
    }
    if (!newUser.confirmPassword) {
      validationErrors.confirmPassword = "Please confirm your password.";
    }
    if (!newUser.email.trim()) {
      validationErrors.email = "Email is required.";
    }
    const usernamePattern = /^[a-zA-Z0-9_]+$/;
    // Format and match validations
    if (newUser.username && !usernamePattern.test(newUser.username)) {
      validationErrors.username =
        validationErrors.username || "Invalid username format.";
    }
    if (newUser.password && newUser.password.length < 8) {
      validationErrors.password =
        validationErrors.password || "Password must be at least 8 characters.";
    }
    if (
      newUser.password &&
      newUser.confirmPassword &&
      newUser.password !== newUser.confirmPassword
    ) {
      validationErrors.confirmPassword = "Passwords do not match.";
    }
    if (newUser.email && !isValidEmail(newUser.email)) {
      validationErrors.email = "Please enter a valid email address.";
    }
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setErrors({});
    handleAddUser(e);
  };

  useEffect(() => {
    const handler = setTimeout(() => setDebouncedSearchTerm(searchTerm), 300);
    return () => clearTimeout(handler);
  }, [searchTerm]);

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* Page header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-semibold text-gray-800">
          User Management
        </h1>
      </div>

      {/* Role Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div
          onClick={() =>
            setRoleFilter(roleFilter === "admin" ? "all" : "admin")
          }
          className={`cursor-pointer flex items-center p-4 rounded-lg border-l-4 transition ${
            roleFilter === "admin"
              ? "border-red-500 bg-red-50"
              : "border-transparent bg-white hover:shadow"
          }`}
        >
          <div className="p-3 bg-red-100 rounded-full text-red-500 mr-4">
            <FaShieldAlt size={20} />
          </div>
          <div>
            <h2 className="text-lg font-medium text-gray-700">
              Administrators
            </h2>
            <p className="text-2xl font-bold text-red-600">{adminCount}</p>
            <p className="text-sm text-gray-500">System administrators</p>
          </div>
        </div>

        <div
          onClick={() =>
            setRoleFilter(roleFilter === "annotator" ? "all" : "annotator")
          }
          className={`cursor-pointer flex items-center p-4 rounded-lg border-l-4 transition ${
            roleFilter === "annotator"
              ? "border-blue-500 bg-blue-50"
              : "border-transparent bg-white hover:shadow"
          }`}
        >
          <div className="p-3 bg-blue-100 rounded-full text-blue-500 mr-4">
            <FaUserTag size={20} />
          </div>
          <div>
            <h2 className="text-lg font-medium text-gray-700">Annotators</h2>
            <p className="text-2xl font-bold text-blue-600">{annotatorCount}</p>
            <p className="text-sm text-gray-500">
              Data annotation/verification
            </p>
          </div>
        </div>

        <div
          onClick={() =>
            setRoleFilter(roleFilter === "auditor" ? "all" : "auditor")
          }
          className={`cursor-pointer flex items-center p-4 rounded-lg border-l-4 transition ${
            roleFilter === "auditor"
              ? "border-yellow-500 bg-yellow-50"
              : "border-transparent bg-white hover:shadow"
          }`}
        >
          <div className="p-3 bg-yellow-100 rounded-full text-yellow-500 mr-4">
            <FaEye size={20} />
          </div>
          <div>
            <h2 className="text-lg font-medium text-gray-700">Auditors</h2>
            <p className="text-2xl font-bold text-yellow-600">{auditorCount}</p>
            <p className="text-sm text-gray-500">Quality control users</p>
          </div>
        </div>

        {/* <div
          onClick={() =>
            setRoleFilter(roleFilter === "client" ? "all" : "client")
          }
          className={`cursor-pointer flex items-center p-4 rounded-lg border-l-4 transition ${
            roleFilter === "client"
              ? "border-green-500 bg-green-50"
              : "border-transparent bg-white hover:shadow"
          }`}
        >
          <div className="p-3 bg-green-100 rounded-full text-green-500 mr-4">
            <FaUsers size={20} />
          </div>
          <div>
            <h2 className="text-lg font-medium text-gray-700">Clients</h2>
            <p className="text-2xl font-bold text-green-600">{clientCount}</p>
            <p className="text-sm text-gray-500">Client users</p>
          </div>
        </div> */}
      </div>

      {/* Users Table Card */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="flex items-center justify-between bg-blue-600 px-6 py-4">
          <div className="flex items-center">
            <h2 className="text-white font-medium">Registered Users</h2>
            <span className="ml-3 inline-flex items-center px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full">
              {filteredUsers.length === users.length
                ? `${users.length} Total`
                : `${filteredUsers.length} of ${users.length}`}
            </span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setIsFlushModalOpen(true)}
              className="flex items-center bg-red-500 hover:bg-red-600 text-white text-sm px-3 py-1 rounded"
            >
              <FaTrash className="mr-1" /> Flush DB
            </button>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center bg-green-500 hover:bg-green-600 text-white text-sm px-3 py-1 rounded"
            >
              <FaPlus className="mr-1" /> Add New User
            </button>
          </div>
        </div>

        <div className="p-6 relative">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 space-y-4 md:space-y-0">
            <div className="relative text-gray-600">
              <FaSearch className="absolute left-3 top-3" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by username, name, email, role..."
                className="pl-10 pr-4 py-2 border border-gray-300 rounded w-full md:w-64 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                >
                  <i className="bi bi-x-lg"></i>
                </button>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setStatusFilter("all")}
                className={`px-3 py-1 rounded text-sm ${
                  statusFilter === "all"
                    ? "bg-gray-200 text-gray-800"
                    : "bg-white text-gray-600 border border-gray-300"
                }`}
              >
                All Status
              </button>
              <button
                onClick={() => setStatusFilter("active")}
                className={`px-3 py-1 rounded text-sm ${
                  statusFilter === "active"
                    ? "bg-green-200 text-green-800"
                    : "bg-white text-gray-600 border border-gray-300"
                }`}
              >
                Active
              </button>
              <button
                onClick={() => setStatusFilter("suspended")}
                className={`px-3 py-1 rounded text-sm ${
                  statusFilter === "suspended"
                    ? "bg-red-200 text-red-800"
                    : "bg-white text-gray-600 border border-gray-300"
                }`}
              >
                Suspended
              </button>
            </div>
          </div>

          {/* Users Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {[
                    "Username",
                    "Full Name",
                    "Role",
                    "Email",
                    "Mode",
                    "Last Login",
                    "Status",
                    "Actions",
                  ].map((header) => (
                    <th
                      key={header}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.username}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {user.full_name || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          user.role === "admin"
                            ? "bg-red-100 text-red-800"
                            : user.role === "annotator"
                            ? "bg-blue-100 text-blue-800"
                            : user.role === "auditor"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {user.email || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.role === "annotator" ? (
                        user.annotator_mode === "supervision" ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                            <i className="bi bi-eye me-1"></i>Supervision
                          </span>
                        ) : user.annotator_mode === "verification" ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                            <i className="bi bi-check2-square me-1"></i>
                            Verification
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800">
                            <i className="bi bi-pencil-square me-1"></i>
                            Annotation
                          </span>
                        )
                      ) : (
                        <span className="text-sm text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {user.last_login ? (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-gray-100 text-gray-800">
                          {formatDateTime(user.last_login)}
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full bg-gray-200 text-gray-600">
                          Never
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                          user.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {user.is_active ? "Active" : "Suspended"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-blue-500 hover:text-blue-700"
                        title="Edit User"
                      >
                        <FaEdit />
                      </button>
                      {/* Only allow suspend/reactivate if not current user */}
                      {user.username !== loggedInUsername &&
                        (user.is_active ? (
                          <button
                            onClick={() => handleSuspendUser(user)}
                            className="text-red-500 hover:text-red-700"
                            title="Suspend User"
                          >
                            <FaPauseCircle />
                          </button>
                        ) : (
                          <button
                            onClick={() => handleReactivateUser(user)}
                            className="text-green-500 hover:text-green-700"
                            title="Reactivate User"
                          >
                            <FaPlayCircle />
                          </button>
                        ))}
                    </td>
                  </tr>
                ))}
                {filteredUsers.length === 0 && (
                  <tr>
                    <td
                      colSpan={8}
                      className="px-6 py-8 text-center text-gray-500"
                    >
                      No users match your search criteria.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {users.length === 0 && (
            <div className="text-center p-8">
              <FaUsers className="mx-auto text-4xl text-gray-400" />
              <p className="mt-4 text-lg text-gray-600">
                No registered users found.
              </p>
              <button
                onClick={() => setIsAddModalOpen(true)}
                className="mt-4 inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                <FaPlus className="mr-2" /> Add First User
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Add User Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
            <div className="px-6 py-4 border-b">
              <h4 className="text-lg font-semibold flex items-center">
                <FaPlus className="mr-2 text-xl" /> Register New User
              </h4>
            </div>
            <div className="px-6 py-4">
              <form
                onSubmit={handleRegisterSubmit}
                noValidate
                className="space-y-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Username
                    </label>
                    <input
                      type="text"
                      value={newUser.username}
                      onChange={(e) =>
                        setNewUser({ ...newUser, username: e.target.value })
                      }
                      required
                      pattern="[a-zA-Z0-9_]+"
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                        errors.username
                          ? "border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:ring-blue-500"
                      }`}
                    />
                    <p className="text-xs text-gray-500">
                      Letters, numbers, & underscores only.
                    </p>
                    {errors.username && (
                      <p className="text-red-600 text-sm mt-1">
                        {errors.username}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={newUser.fullName}
                      onChange={(e) =>
                        setNewUser({ ...newUser, fullName: e.target.value })
                      }
                      required
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                        errors.fullName
                          ? "border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:ring-blue-500"
                      }`}
                    />
                    {errors.fullName && (
                      <p className="text-red-600 text-sm mt-1">
                        {errors.fullName}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Password
                    </label>
                    <input
                      type="password"
                      value={newUser.password}
                      onChange={(e) =>
                        setNewUser({ ...newUser, password: e.target.value })
                      }
                      required
                      minLength={8}
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                        errors.password
                          ? "border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:ring-blue-500"
                      }`}
                    />
                    {errors.password && (
                      <p className="text-red-600 text-sm mt-1">
                        {errors.password}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      Minimum 8 characters.
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Confirm Password
                    </label>
                    <input
                      type="password"
                      value={newUser.confirmPassword}
                      onChange={(e) =>
                        setNewUser({
                          ...newUser,
                          confirmPassword: e.target.value,
                        })
                      }
                      required
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                        errors.confirmPassword
                          ? "border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:ring-blue-500"
                      }`}
                    />
                    {errors.confirmPassword && (
                      <p className="text-red-600 text-sm mt-1">
                        {errors.confirmPassword}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Email Address
                    </label>
                    <input
                      type="email"
                      value={newUser.email}
                      onChange={(e) =>
                        setNewUser({ ...newUser, email: e.target.value })
                      }
                      required
                      className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                        errors.email
                          ? "border-red-500 focus:ring-red-500"
                          : "border-gray-300 focus:ring-blue-500"
                      }`}
                    />
                    {errors.email && (
                      <p className="text-red-600 text-sm mt-1">
                        {errors.email}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Assign Role
                    </label>
                    <select
                      value={newUser.role}
                      onChange={(e) =>
                        setNewUser({
                          ...newUser,
                          role: e.target.value as UserRole,
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="annotator">Annotator</option>
                      <option value="auditor">Auditor</option>
                      <option value="admin">Administrator</option>
                      <option value="client">Client</option>
                    </select>
                    <p className="text-xs text-gray-500">
                      Select the user&#39;s role in the system.
                    </p>
                  </div>
                </div>
                {newUser.role === "annotator" && (
                  <div className="border-l-4 border-blue-500 pl-4 mt-4">
                    <p className="mb-2 font-semibold text-blue-600 flex items-center">
                      <FaUserTag className="mr-1" /> Annotator Settings
                    </p>
                    <label className="block text-sm font-medium mb-1">
                      Default Annotation Mode
                    </label>
                    <select
                      value={newUser.annotationMode}
                      onChange={(e) =>
                        setNewUser({
                          ...newUser,
                          annotationMode: e.target.value as AnnotatorMode,
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="annotation">Annotation</option>
                      <option value="verification">Label Verification</option>
                      <option value="supervision">Supervision</option>
                    </select>
                  </div>
                )}
                <div className="flex justify-end space-x-2 mt-4 pt-3 border-t">
                  <button
                    type="button"
                    onClick={() => setIsAddModalOpen(false)}
                    className="px-4 py-2 bg-gray-200 rounded"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded"
                  >
                    <FaPlus className="mr-2" /> Register User
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {isEditModalOpen && currentUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl">
            <div className="flex items-center justify-between px-6 py-4 border-b">
              <h4 className="text-lg font-semibold flex items-center">
                <FaEdit className="mr-2 text-xl" /> Edit User:{" "}
                {currentUser.username}
              </h4>
              <div className="flex items-center space-x-4">
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    currentUser.is_active
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  {currentUser.is_active ? (
                    <>
                      <FaCheckCircle className="mr-1" /> Active
                    </>
                  ) : (
                    <>
                      <FaTimes className="mr-1" /> Suspended
                    </>
                  )}
                </span>
                <button
                  onClick={() => setIsEditModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FaTimes />
                </button>
              </div>
            </div>
            <div className="px-6 py-4 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h5 className="font-medium mb-3 flex items-center border-b pb-2">
                    <FaInfoCircle className="mr-2" /> User Information
                  </h5>
                  <div className="space-y-2">
                    <div className="flex">
                      <div className="w-32 font-semibold">Username:</div>
                      <div>{currentUser.username}</div>
                    </div>
                    <div className="flex">
                      <div className="w-32 font-semibold">Role:</div>
                      <div>
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            currentUser.role === "admin"
                              ? "bg-red-100 text-red-800"
                              : currentUser.role === "auditor"
                              ? "bg-yellow-100 text-yellow-800"
                              : currentUser.role === "annotator"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-green-100 text-green-800"
                          }`}
                        >
                          {currentUser.role.charAt(0).toUpperCase() +
                            currentUser.role.slice(1)}
                        </span>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="w-32 font-semibold">Last Login:</div>
                      <div>
                        {currentUser.last_login
                          ? formatDateTime(currentUser.last_login)
                          : "Never"}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="p-4 border rounded-lg">
                  <h5 className="font-medium mb-3 flex items-center border-b pb-2">
                    <FaChartPie className="mr-2" /> User Activity
                  </h5>
                  <div className="space-y-2">
                    <div className="flex">
                      <div className="w-32 font-semibold">Status:</div>
                      <div>
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            currentUser.is_active
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-200 text-gray-600"
                          }`}
                        >
                          {currentUser.is_active ? "Active" : "Suspended"}
                        </span>
                      </div>
                    </div>
                    <div className="flex">
                      <div className="w-32 font-semibold">Account Type:</div>
                      <div>Custom User</div>
                    </div>
                    {currentUser.role === "annotator" &&
                      currentUser.annotator_mode && (
                        <div className="flex">
                          <div className="w-32 font-semibold">Mode:</div>
                          <div>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {currentUser.annotator_mode
                                .charAt(0)
                                .toUpperCase() +
                                currentUser.annotator_mode.slice(1)}
                            </span>
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              </div>
              <div>
                <h5 className="font-medium mb-3 flex items-center">
                  <FaEdit className="mr-2" /> Edit User Details
                </h5>
                <form onSubmit={handleUpdateUser} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      value={currentUser.full_name || ""}
                      onChange={(e) =>
                        setCurrentUser({
                          ...currentUser,
                          full_name: e.target.value,
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Email
                    </label>
                    <input
                      type="email"
                      value={currentUser.email}
                      onChange={(e) =>
                        setCurrentUser({
                          ...currentUser,
                          email: e.target.value,
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Role
                    </label>
                    <select
                      value={currentUser.role}
                      onChange={(e) =>
                        setCurrentUser({
                          ...currentUser,
                          role: e.target.value as UserRole,
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="admin">Admin</option>
                      <option value="annotator">Annotator</option>
                      <option value="auditor">Auditor</option>
                      <option value="client">Client</option>
                    </select>
                  </div>
                  {currentUser.role === "annotator" && (
                    <div>
                      <label className="block text-sm font-medium mb-1">
                        Annotation Mode
                      </label>
                      <select
                        value={currentUser.annotator_mode || "annotation"}
                        onChange={(e) =>
                          setCurrentUser({
                            ...currentUser,
                            annotator_mode: e.target.value as AnnotatorMode,
                          })
                        }
                        className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="annotation">Annotation</option>
                        <option value="verification">Verification</option>
                        <option value="supervision">Supervision</option>
                      </select>
                    </div>
                  )}
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Status
                    </label>
                    <select
                      value={currentUser.is_active ? "true" : "false"}
                      onChange={(e) =>
                        setCurrentUser({
                          ...currentUser,
                          is_active: e.target.value === "true",
                        })
                      }
                      className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="true">Active</option>
                      <option value="false">Suspended</option>
                    </select>
                  </div>
                  <div className="flex justify-end space-x-2 mt-4">
                    <button
                      type="button"
                      onClick={() => setIsEditModalOpen(false)}
                      className="px-4 py-2 bg-gray-200 rounded"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded"
                    >
                      Save Changes
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Suspend Confirmation Modal */}
      {suspendUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="px-6 py-4 border-b">
              <h5 className="text-lg font-bold text-red-600">
                Confirm Suspension
              </h5>
            </div>
            <div className="px-6 py-4">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-red-100 text-red-600 rounded-full mr-4">
                  <FaPauseCircle size={24} />
                </div>
                <div>
                  <h6 className="font-medium">
                    Suspend User: {suspendUser.username}
                  </h6>
                  <p className="text-sm text-gray-600">
                    {suspendUser.full_name || "No name provided"}
                  </p>
                </div>
              </div>
              <div className="text-gray-700">
                The user will no longer be able to log in, but their account
                information will be preserved.
              </div>
            </div>
            <div className="px-6 py-4 border-t flex justify-end space-x-2">
              <button
                onClick={() => setSuspendUser(null)}
                className="px-4 py-2 bg-gray-200 rounded"
              >
                Cancel
              </button>
              <button
                onClick={() => handleConfirmSuspend(suspendUser)}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Suspend User
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Flush Database and Cache Confirmation Modal */}
      {isFlushModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-lg">
            <div className="px-6 py-4 bg-red-600 rounded-t-lg">
              <h4 className="text-white font-bold">
                Flush Database and Cache
              </h4>
            </div>
            <div className="px-6 py-4">
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                <h5 className="font-semibold mb-2">
                  Warning: This action cannot be undone!
                </h5>
                <p>
                  You are about to delete all records from the following tables:
                </p>
                <ul className="list-disc list-inside mb-2 space-y-1">
                  <li>Annotation Records (image_annotation)</li>
                  <li>Verification Records (image_verification)</li>
                  <li>Audit History (audit_history)</li>
                  <li>Admin Settings (admin_settings)</li>
                  <li>Datasets (datasets)</li>
                  <li>Supervision (supervision)</li>
                </ul>
                <p>
                  Additionally, the Redis cache database will be completely
                  flushed.
                </p>
                <hr className="my-4" />
                <p>
                  All data in these tables and the cache will be permanently
                  removed. This action cannot be reversed.
                </p>
              </div>
            </div>
            <div className="px-6 py-4 border-t flex justify-end space-x-2">
              <button
                onClick={() => setIsFlushModalOpen(false)}
                className="px-4 py-2 bg-gray-200 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleFlushDb}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Confirm Database and Cache Flush
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}