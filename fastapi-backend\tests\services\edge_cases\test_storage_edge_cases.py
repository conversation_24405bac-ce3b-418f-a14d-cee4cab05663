"""
Edge case tests for storage operations across all services.
Tests boundary conditions, error scenarios, and unusual usage patterns.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List, Optional
import asyncio
import tempfile
import os
from datetime import datetime, timedelta

from app.services.media_streaming_service import MediaStreamingService
from app.services.project_batch_service import ProjectBatchService
from app.services.ai_processing_service import AIProcessingService

class TestStorageEdgeCases:
    """Edge case tests for storage operations."""
    
    @pytest.fixture
    def edge_case_scenarios(self):
        """Edge case scenarios for testing."""
        return {
            'empty_data': {
                'files': [],
                'file_sizes': [],
                'batch_sizes': [0, None, -1],
                'paths': ['', None, '   '],
                'descriptions': ['Empty lists', 'Zero values', 'Invalid inputs']
            },
            'boundary_values': {
                'max_file_size': 5 * 1024 * 1024 * 1024,  # 5GB
                'max_files_per_batch': 10000,
                'max_concurrent_connections': 1000,
                'max_path_length': 4096,
                'min_values': [1, 0.001, 1e-10]
            },
            'invalid_data': {
                'malformed_paths': [
                    '../../etc/passwd',
                    '/dev/null',
                    'CON', 'PRN', 'AUX',  # Windows reserved names
                    'file with spaces',
                    'file\x00null.txt',  # Null byte
                    'very_long_filename_' + 'x' * 1000 + '.txt'
                ],
                'invalid_sizes': [-1, float('inf'), float('nan'), 2**64],
                'malformed_metadata': [None, {}, {'invalid': 'structure'}]
            },
            'unicode_edge_cases': {
                'filenames': [
                    'файл.txt',  # Cyrillic
                    '文件.jpg',   # Chinese
                    'ファイル.pdf',  # Japanese
                    'الملف.doc',  # Arabic
                    '🚀🎯📁.gif',  # Emojis
                    'نام\u200cفایل.txt',  # Zero-width characters
                    'file\u0001\u001f.txt'  # Control characters
                ],
                'paths': [
                    '/پوشه/فایل.txt',
                    '/папка/файл.doc',
                    '/フォルダ/ファイル.jpg'
                ]
            }
        }

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_empty_file_operations(self, edge_case_scenarios):
        """Test operations with empty files and data."""
        service = ProjectBatchService()
        empty_scenarios = edge_case_scenarios['empty_data']
        
        # Test empty file list
        with patch.object(service, '_process_file_list') as mock_process:
            mock_process.return_value = {
                'files_processed': 0,
                'batches_created': 0,
                'warnings': ['No files found to process']
            }
            
            result = await service.create_batches_from_folder(
                'EMPTY_TEST_001',
                '/empty/folder',
                files_per_batch=10
            )
            
            success, message, num_batches = result
            assert success is False or num_batches == 0
            assert 'empty' in message.lower() or 'no files' in message.lower()
        
        # Test zero batch size
        with patch.object(service, '_validate_batch_parameters') as mock_validate:
            mock_validate.return_value = {
                'valid': False,
                'errors': ['Batch size must be greater than 0']
            }
            
            for invalid_batch_size in empty_scenarios['batch_sizes']:
                try:
                    result = await service.create_batches_from_folder(
                        'EMPTY_BATCH_TEST',
                        '/test/folder',
                        files_per_batch=invalid_batch_size
                    )
                    
                    success, message, num_batches = result
                    assert success is False, f"Should fail with batch size {invalid_batch_size}"
                except Exception as e:
                    assert 'batch' in str(e).lower() or 'size' in str(e).lower()

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_maximum_boundary_values(self, edge_case_scenarios):
        """Test operations at maximum boundary values."""
        boundary_values = edge_case_scenarios['boundary_values']
        
        # Test maximum file size
        ai_service = AIProcessingService()
        
        large_file_data = {
            'file_identifier': '/ai/huge_file.zip',
            'file_size': boundary_values['max_file_size'],
            'storage_info': {
                'connector_type': 'MinIO',
                'connector': MagicMock()
            }
        }
        
        with patch.object(ai_service, '_validate_file_size_limits') as mock_validate:
            mock_validate.return_value = {
                'valid': False,
                'error': f"File size {boundary_values['max_file_size']} exceeds maximum limit",
                'max_allowed': 1024 * 1024 * 1024  # 1GB limit
            }
            
            try:
                result = await ai_service._process_single_file(
                    'MAX_SIZE_TEST',
                    large_file_data,
                    'test_model',
                    'test_processing'
                )
                
                assert result['success'] is False
                assert 'size' in result.get('error', '').lower()
                
            except Exception as e:
                assert 'size' in str(e).lower() or 'limit' in str(e).lower()
        
        # Test maximum files per batch
        batch_service = ProjectBatchService()
        
        with patch.object(batch_service, '_validate_batch_size_limits') as mock_validate_batch:
            max_files = boundary_values['max_files_per_batch']
            mock_validate_batch.return_value = {
                'valid': max_files <= 5000,  # Reasonable limit
                'recommended_max': 5000,
                'performance_warning': max_files > 1000
            }
            
            validation_result = batch_service._validate_batch_size_limits(max_files)
            
            if max_files > 5000:
                assert validation_result['valid'] is False
            else:
                assert validation_result['valid'] is True
                if max_files > 1000:
                    assert validation_result['performance_warning'] is True

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_invalid_path_handling(self, edge_case_scenarios, security_test_data):
        """Test handling of invalid and malicious file paths."""
        media_service = MediaStreamingService()
        invalid_data = edge_case_scenarios['invalid_data']
        
        malicious_paths = invalid_data['malformed_paths'] + security_test_data['malicious_inputs'][:2]
        
        for malicious_path in malicious_paths:
            with patch.object(media_service, '_validate_file_path_security') as mock_security:
                is_safe = not any(dangerous in str(malicious_path) for dangerous in ['../', '/dev/', '\x00', 'CON'])
                
                mock_security.return_value = {
                    'safe': is_safe,
                    'issues': [] if is_safe else ['Path contains dangerous patterns'],
                    'normalized_path': malicious_path if is_safe else None
                }
                
                try:
                    result = await media_service.get_streaming_url(
                        malicious_path,
                        'video',
                        'SECURITY_TEST_001'
                    )
                    
                    if not is_safe:
                        pytest.fail(f"Malicious path {malicious_path} should have been rejected")
                        
                except Exception as e:
                    # Should raise exception for unsafe paths
                    if is_safe:
                        pytest.fail(f"Safe path {malicious_path} should not raise exception: {e}")
                    else:
                        assert any(keyword in str(e).lower() for keyword in ['invalid', 'path', 'security', 'forbidden'])

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_unicode_filename_handling(self, edge_case_scenarios):
        """Test handling of Unicode filenames and paths."""
        batch_service = ProjectBatchService()
        unicode_cases = edge_case_scenarios['unicode_edge_cases']
        
        for filename in unicode_cases['filenames']:
            with patch.object(batch_service, '_process_unicode_filename') as mock_unicode:
                # Determine if filename should be supported
                has_control_chars = any(ord(c) < 32 for c in filename)
                has_null_chars = '\x00' in filename
                is_valid = not (has_control_chars or has_null_chars)
                
                mock_unicode.return_value = {
                    'valid': is_valid,
                    'normalized_filename': filename.encode('utf-8', errors='replace').decode('utf-8') if is_valid else None,
                    'encoding_issues': not is_valid
                }
                
                unicode_result = batch_service._process_unicode_filename(filename)
                
                if has_control_chars or has_null_chars:
                    assert unicode_result['valid'] is False
                    assert unicode_result['encoding_issues'] is True
                else:
                    # Should handle standard Unicode characters
                    assert unicode_result['valid'] is True
                    assert unicode_result['normalized_filename'] is not None

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_network_timeout_edge_cases(self, edge_case_scenarios):
        """Test network timeout and connection edge cases."""
        ai_service = AIProcessingService()
        
        timeout_scenarios = [
            {'timeout_seconds': 0.001, 'expected_failure': True},   # Very short timeout
            {'timeout_seconds': 3600, 'expected_failure': False},   # Very long timeout  
            {'timeout_seconds': -1, 'expected_failure': True},      # Invalid negative timeout
            {'timeout_seconds': float('inf'), 'expected_failure': True},  # Infinite timeout
        ]
        
        for scenario in timeout_scenarios:
            file_data = {
                'file_identifier': '/timeout_test/file.jpg',
                'storage_info': {
                    'connector_type': 'NAS-FTP',  # More prone to timeouts
                    'connector': MagicMock(),
                    'timeout_seconds': scenario['timeout_seconds']
                }
            }
            
            with patch.object(ai_service, '_handle_connection_timeout') as mock_timeout:
                if scenario['expected_failure']:
                    mock_timeout.side_effect = Exception(f"Timeout after {scenario['timeout_seconds']} seconds")
                else:
                    mock_timeout.return_value = {'success': True, 'timeout_handled': True}
                
                try:
                    result = await ai_service._process_single_file(
                        'TIMEOUT_TEST',
                        file_data,
                        'test_model',
                        'test_processing'
                    )
                    
                    if scenario['expected_failure']:
                        pytest.fail(f"Timeout scenario {scenario['timeout_seconds']}s should have failed")
                    else:
                        assert result['success'] is True
                        
                except Exception as e:
                    if not scenario['expected_failure']:
                        pytest.fail(f"Valid timeout {scenario['timeout_seconds']}s should not fail: {e}")
                    else:
                        assert 'timeout' in str(e).lower()

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_storage_capacity_edge_cases(self, edge_case_scenarios):
        """Test storage capacity edge cases and quota limits."""
        batch_service = ProjectBatchService()
        
        capacity_scenarios = [
            {'available_space': 0, 'file_size': 1024, 'should_fail': True},
            {'available_space': 1023, 'file_size': 1024, 'should_fail': True},  # Just under required
            {'available_space': 1024, 'file_size': 1024, 'should_fail': False},  # Exactly enough
            {'available_space': -1, 'file_size': 1024, 'should_fail': True},  # Negative space (error condition)
        ]
        
        for scenario in capacity_scenarios:
            with patch.object(batch_service, '_check_storage_capacity') as mock_capacity:
                mock_capacity.return_value = {
                    'available_bytes': scenario['available_space'],
                    'required_bytes': scenario['file_size'],
                    'sufficient_space': scenario['available_space'] >= scenario['file_size'],
                    'quota_exceeded': scenario['available_space'] < 0
                }
                
                try:
                    result = await batch_service._validate_storage_capacity(
                        scenario['file_size'], 'CAPACITY_TEST_001'
                    )
                    
                    if scenario['should_fail']:
                        assert result['sufficient_space'] is False
                    else:
                        assert result['sufficient_space'] is True
                        
                except Exception as e:
                    if not scenario['should_fail']:
                        pytest.fail(f"Capacity scenario should not fail: {e}")
                    assert any(keyword in str(e).lower() for keyword in ['space', 'capacity', 'quota'])

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_concurrent_connection_limits(self, edge_case_scenarios):
        """Test concurrent connection limit edge cases."""
        media_service = MediaStreamingService()
        boundary_values = edge_case_scenarios['boundary_values']
        
        max_connections = boundary_values['max_concurrent_connections']
        
        # Test exceeding connection limits
        with patch.object(media_service, '_manage_connection_pool') as mock_pool:
            mock_pool.return_value = {
                'current_connections': max_connections + 1,
                'max_allowed': max_connections,
                'connection_rejected': True,
                'queue_position': 1
            }
            
            pool_result = media_service._manage_connection_pool('CONN_LIMIT_TEST')
            
            assert pool_result['connection_rejected'] is True
            assert pool_result['current_connections'] > pool_result['max_allowed']
        
        # Test connection pool exhaustion recovery
        with patch.object(media_service, '_handle_connection_pool_exhaustion') as mock_recovery:
            mock_recovery.return_value = {
                'recovery_attempted': True,
                'connections_freed': 50,
                'new_connection_available': True,
                'wait_time_seconds': 5.0
            }
            
            recovery_result = media_service._handle_connection_pool_exhaustion()
            
            assert recovery_result['recovery_attempted'] is True
            assert recovery_result['connections_freed'] > 0

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_file_corruption_detection(self, edge_case_scenarios):
        """Test file corruption detection and handling."""
        ai_service = AIProcessingService()
        
        corruption_scenarios = [
            {'corruption_type': 'partial_read', 'recoverable': True},
            {'corruption_type': 'checksum_mismatch', 'recoverable': False},
            {'corruption_type': 'truncated_file', 'recoverable': False},
            {'corruption_type': 'binary_corruption', 'recoverable': False}
        ]
        
        for scenario in corruption_scenarios:
            file_data = {
                'file_identifier': f'/corrupted/{scenario["corruption_type"]}.jpg',
                'expected_checksum': 'abc123def456',
                'storage_info': {
                    'connector_type': 'MinIO',
                    'connector': MagicMock()
                }
            }
            
            with patch.object(ai_service, '_detect_file_corruption') as mock_detect:
                mock_detect.return_value = {
                    'corrupted': True,
                    'corruption_type': scenario['corruption_type'],
                    'recoverable': scenario['recoverable'],
                    'integrity_check_failed': True
                }
                
                with patch.object(ai_service, '_handle_file_corruption') as mock_handle:
                    if scenario['recoverable']:
                        mock_handle.return_value = {
                            'recovery_successful': True,
                            'file_recovered': True,
                            'method_used': 'retry_download'
                        }
                    else:
                        mock_handle.return_value = {
                            'recovery_successful': False,
                            'file_quarantined': True,
                            'manual_intervention_required': True
                        }
                    
                    corruption_result = ai_service._detect_file_corruption(file_data)
                    assert corruption_result['corrupted'] is True
                    
                    recovery_result = ai_service._handle_file_corruption(file_data, corruption_result)
                    
                    if scenario['recoverable']:
                        assert recovery_result['recovery_successful'] is True
                    else:
                        assert recovery_result['recovery_successful'] is False
                        assert recovery_result['manual_intervention_required'] is True

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_storage_migration_edge_cases(self, edge_case_scenarios):
        """Test edge cases in storage migration operations."""
        batch_service = ProjectBatchService()
        
        migration_edge_cases = [
            {'scenario': 'source_file_deleted_during_migration', 'should_handle': True},
            {'scenario': 'target_storage_full_during_migration', 'should_handle': True},
            {'scenario': 'network_failure_mid_migration', 'should_handle': True},
            {'scenario': 'duplicate_file_at_target', 'should_handle': True},
            {'scenario': 'permission_denied_target', 'should_handle': False}
        ]
        
        for edge_case in migration_edge_cases:
            migration_data = {
                'source_file': '/source/test_file.mp4',
                'target_storage': 'MinIO',
                'source_storage': 'NAS-FTP',
                'file_size': 100 * 1024 * 1024  # 100MB
            }
            
            with patch.object(batch_service, '_execute_migration_with_error_handling') as mock_migrate:
                if edge_case['should_handle']:
                    mock_migrate.return_value = {
                        'migration_successful': True,
                        'error_encountered': edge_case['scenario'],
                        'error_handled': True,
                        'recovery_action': 'retry_with_fallback'
                    }
                else:
                    mock_migrate.side_effect = Exception(f"Unrecoverable error: {edge_case['scenario']}")
                
                try:
                    result = batch_service._execute_migration_with_error_handling(
                        migration_data, edge_case['scenario']
                    )
                    
                    if edge_case['should_handle']:
                        assert result['migration_successful'] is True
                        assert result['error_handled'] is True
                    else:
                        pytest.fail(f"Unhandled edge case {edge_case['scenario']} should raise exception")
                        
                except Exception as e:
                    if edge_case['should_handle']:
                        pytest.fail(f"Handleable edge case {edge_case['scenario']} should not raise exception")
                    else:
                        assert edge_case['scenario'] in str(e)

    @pytest.mark.edge_case
    @pytest.mark.asyncio
    async def test_metadata_edge_cases(self, edge_case_scenarios):
        """Test metadata handling edge cases."""
        batch_service = ProjectBatchService()
        invalid_data = edge_case_scenarios['invalid_data']
        
        for malformed_metadata in invalid_data['malformed_metadata']:
            file_data = {
                'file_path': '/test/metadata_edge_case.jpg',
                'file_size': 1024 * 1024,
                'metadata': malformed_metadata
            }
            
            with patch.object(batch_service, '_process_file_metadata') as mock_metadata:
                try:
                    # Attempt to process malformed metadata
                    if malformed_metadata is None:
                        mock_metadata.return_value = {
                            'metadata_valid': False,
                            'error': 'Metadata is None',
                            'default_metadata_used': True
                        }
                    elif isinstance(malformed_metadata, dict) and not malformed_metadata:
                        mock_metadata.return_value = {
                            'metadata_valid': True,
                            'empty_metadata_handled': True,
                            'default_values_applied': True
                        }
                    else:
                        mock_metadata.return_value = {
                            'metadata_valid': False,
                            'error': 'Invalid metadata structure',
                            'sanitized_metadata': {}
                        }
                    
                    result = batch_service._process_file_metadata(file_data)
                    
                    # Should handle malformed metadata gracefully
                    if malformed_metadata is None:
                        assert result['metadata_valid'] is False
                        assert result['default_metadata_used'] is True
                    elif isinstance(malformed_metadata, dict) and not malformed_metadata:
                        assert result['empty_metadata_handled'] is True
                    
                except Exception as e:
                    # Should not crash on malformed metadata
                    pytest.fail(f"Metadata processing should handle malformed input gracefully: {e}")

    @pytest.mark.edge_case
    def test_memory_pressure_edge_cases(self, edge_case_scenarios, service_performance_data):
        """Test behavior under extreme memory pressure."""
        import gc
        
        # Simulate memory pressure scenarios
        memory_pressure_scenarios = [
            {'available_memory_mb': 10, 'operation': 'large_file_processing'},
            {'available_memory_mb': 100, 'operation': 'concurrent_operations'},
            {'available_memory_mb': 1, 'operation': 'metadata_processing'}
        ]
        
        for scenario in memory_pressure_scenarios:
            # Simulate low memory condition
            large_memory_consumer = []
            try:
                # Don't actually consume memory in tests, just simulate the check
                simulated_available_memory = scenario['available_memory_mb']
                min_required_memory = service_performance_data['memory_limits']['file_operations']
                
                if simulated_available_memory < min_required_memory:
                    # Should handle low memory condition gracefully
                    memory_handling_result = {
                        'operation_modified': True,
                        'memory_optimization_applied': True,
                        'reduced_concurrent_operations': True,
                        'fallback_mode_enabled': True
                    }
                    
                    assert memory_handling_result['operation_modified'] is True
                    assert memory_handling_result['fallback_mode_enabled'] is True
                    
            finally:
                # Cleanup
                del large_memory_consumer
                gc.collect()
