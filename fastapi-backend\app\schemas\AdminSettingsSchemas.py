from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class AdminSetting(BaseModel):
    key: str
    value: str
    updated_at: datetime

    class Config:
        from_attributes = True

class AdminInstruction(BaseModel):
    id: int
    mode: str
    instructions: str

    class Config:
        from_attributes = True
        
# Admin Request Models
class NASConnectionRequest(BaseModel):
    nas_type: str
    nas_url: str
    nas_username: str
    nas_password: str
    project_code: str  # Project code for NAS connection (e.g., PROJ_CLIENTNAME_0001)
    redirect_after: bool = False

class MinIOConnectionRequest(BaseModel):
    minio_endpoint: str  # MinIO server endpoint (e.g., "localhost:9000")
    minio_access_key: str  # MinIO access key
    minio_secret_key: str  # MinIO secret key
    minio_bucket_name: str  # Bucket name
    minio_secure: bool = False  # Use HTTPS (True) or HTTP (False)
    minio_region: Optional[str] = None  # AWS region (optional)
    project_code: str  # Project code for MinIO connection
    redirect_after: bool = False

class InstructionsRequest(BaseModel):
    mode: str
    instructions: str
    dataset: Optional[str] = None  # Project code string in format PROJ_{project_name}_{client_id}
    
class ActionRequest(BaseModel):
    action: str

class SelectAnnotationFolderRequest(BaseModel):
    folder_path: str
    files_per_batch: Optional[int] = None  # New field for dynamic batch sizing
    project_code: str  # Project code to update (required)
    client_id: Optional[int] = None  # Client ID to associate with the project

class SelectVerificationFoldersRequest(BaseModel):
    image_folder: str
    label_file: str

class SelectDatasetRequest(BaseModel):
    dataset_id: str  # Project code string in format PROJ_{project_name}_{client_id}
    mode: str

class ConfigureGoogleDriveRequest(BaseModel):
    client_id: str
    client_secret: str
    folder_id: Optional[str] = None
    project_code: str  # Project code for Google Drive connection

class MergeDatasetRequest(BaseModel):
    dataset_name: str
    storage_destination: Optional[str] = "current"

# Annotator Form Configuration Schemas
class FormFieldConfig(BaseModel):
    field_name: str
    field_type: str  # 'short_answer', 'long_answer', 'multiple_choice', 'checkboxes'
    label: str
    required: bool = False
    options: Optional[List[str]] = None  # For choice-based fields (multiple_choice, checkboxes)
    placeholder: Optional[str] = None
    max_length: Optional[int] = None

class AnnotatorFormConfigRequest(BaseModel):
    mode: Optional[str] = "annotation"  # Default to annotation mode if not provided
    dataset: str  # Project code string in format PROJ_{project_name}_{client_id}
    fields: List[FormFieldConfig]

class AnnotatorFormConfigResponse(BaseModel):
    id: int
    mode: str
    dataset_id: str  # Project code string in format PROJ_{project_name}_{client_id}
    dataset_name: str
    fields: List[FormFieldConfig]

class GetAnnotatorFormConfigResponse(BaseModel):
    success: bool
    data: Optional[AnnotatorFormConfigResponse] = None
    message: str = ""