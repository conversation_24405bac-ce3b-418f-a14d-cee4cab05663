// src/app/admin/page.tsx

'use client';

import { Suspense, lazy } from 'react';

const AdminClient = lazy(() => import('./AdminClient'));

export default function AdminPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
        </div>
      }
    >
      <AdminClient />
    </Suspense>
  );
}
