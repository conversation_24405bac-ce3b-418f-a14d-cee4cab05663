'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import VerificationInterface from '../../../components/verifier/VerificationInterface';
import ParallelVerificationInterface from '../../../components/verifier/ParallelVerificationInterface';

import { API_BASE_URL } from "@/lib/api";

interface VerificationData {
  files: any[];
  user: any;
  mode: string;
  batch_name: string;
  batch_info: any;
  total_files: number;
  strategy_type?: 'parallel' | 'sequential';
  form_config?: any[];
}

export default function VerifyPage() {
  const { user } = useAuth();
  const [verificationData, setVerificationData] = useState<VerificationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVerificationData = async () => {
      try {
        console.log('Fetching verification data...');
        
        const response = await fetch(`${API_BASE_URL}/verifier/verify`, { 
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          // Check if response has content before parsing JSON
          const text = await response.text();
          if (text.trim()) {
            const data = JSON.parse(text);
            console.log('Verification data:', data);
            setVerificationData(data);
          } else {
            setError('Empty response from server');
          }
        } else if (response.status === 401) {
          // Authentication error
          setError('Authentication required. Please log in again.');
          setTimeout(() => {
            window.location.href = '/';
          }, 2000);
        } else if (response.status === 204) {
          // No batch assigned for verification
          setError('No verification batches available. Please contact your administrator to assign verification tasks.');
        } else if (response.status === 400) {
          // No active project
          setError('No active project assigned. Please contact your administrator.');
        } else {
          try {
            const errorData = await response.json();
            setError(errorData.detail || 'Failed to fetch verification data');
          } catch {
            setError(`Failed to fetch verification data (Status: ${response.status})`);
          }
        }
      } catch (err) {
        console.error('Error fetching verification data:', err);
        setError('Network error occurred while fetching verification data');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchVerificationData();
    } else {
      setLoading(false);
      setError('User not authenticated');
    }
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading verification data...</h2>
          <p className="text-gray-500">Please wait while we prepare your verification batch</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center max-w-md">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h2 className="text-xl font-semibold mb-2">Error</h2>
            <p>{error}</p>
          </div>
          <button
            onClick={() => window.location.href = '/verifier'}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  if (!verificationData) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">No verification data available</h2>
          <button
            onClick={() => window.location.href = '/verifier'}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Determine which verification interface to use based on strategy type
  const isParallelStrategy = verificationData.strategy_type === 'parallel';
  
  if (isParallelStrategy) {
    return (
      <ParallelVerificationInterface
        files={verificationData.files}
        batchName={verificationData.batch_name}
        batchInfo={verificationData.batch_info}
        totalFiles={verificationData.total_files}
        formConfig={verificationData.form_config || []}
      />
    );
  }

  return (
    <VerificationInterface
      files={verificationData.files}
      batchName={verificationData.batch_name}
      batchInfo={verificationData.batch_info}
      totalFiles={verificationData.total_files}
      formConfig={verificationData.form_config || []}
    />
  );
}
