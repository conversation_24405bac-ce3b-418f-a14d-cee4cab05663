"use client";

import React, { useState } from "react";

type ConnectGoogleDriveModalProps = {
  show: boolean;
  onClose: () => void;
  onSubmit: (data: {
    clientId: string;
    clientSecret: string;
    folderId?: string;
  }) => void;
  isLoading: boolean;
};

const ConnectGoogleDriveModal: React.FC<ConnectGoogleDriveModalProps> = ({
  show,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [clientId, setClientId] = useState("");
  const [clientSecret, setClientSecret] = useState("");
  const [folderId, setFolderId] = useState("");

  if (!show) return null;

  return (
    <>
      <div
        className="modal show block"
        tabIndex={-1}
        role="dialog"
      >
        <div className="modal-dialog" role="document">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Connect to Google Drive</h5>
              <button
                type="button"
                className="btn-close"
                aria-label="Close"
                onClick={onClose}
              ></button>
            </div>
            <div className="modal-body">
              <div className="mb-3">
                <label className="form-label">Client ID</label>
                <input
                  type="text"
                  className="form-control"
                  value={clientId}
                  onChange={(e) => setClientId(e.target.value)}
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Client Secret</label>
                <input
                  type="text"
                  className="form-control"
                  value={clientSecret}
                  onChange={(e) => setClientSecret(e.target.value)}
                />
              </div>
              <div className="mb-3">
                <label className="form-label">Root Folder ID (optional)</label>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Google Drive Folder ID"
                  value={folderId}
                  onChange={(e) => setFolderId(e.target.value)}
                />
              </div>
            </div>
            <div className="modal-footer">
              <button
                type="button"
                className="btn btn-secondary"
                onClick={onClose}
                disabled={isLoading}
              >
                Close
              </button>
              <button
                type="button"
                className="btn btn-primary"
                disabled={isLoading}
                onClick={() =>
                  onSubmit({
                    clientId,
                    clientSecret,
                    folderId: folderId || undefined,
                  })
                }
              >
                {isLoading ? "Connecting..." : "Configure & Connect"}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40"></div>
    </>
  );
};

export default ConnectGoogleDriveModal;
