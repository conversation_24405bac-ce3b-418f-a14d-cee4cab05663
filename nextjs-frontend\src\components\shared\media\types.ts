// Media types and interfaces for annotation components

import { API_BASE_URL } from "@/lib/api";

export type MediaType = 'image' | 'video' | 'audio' | 'pdf' | 'text' | 'csv';

export interface MediaFile {
  url: string;
  name: string;
  type: MediaType;
  size?: number;
}

export interface MediaViewerProps {
  mediaUrl: string;
  mediaType: MediaType;
  zoomLevel?: number;
  onLoad?: () => void;
  onError?: (error: string) => void;
}

export interface ZoomControlsProps {
  zoomLevel: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  disabled?: boolean;
}

// Utility function to detect media type from file extension
export function detectMediaType(filename: string): MediaType {
  const ext = filename.toLowerCase().split('.').pop() || '';
  
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'];
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp', 'ogv'];
  const audioExts = ['mp3', 'wav', 'flac', 'ogg', 'm4a', 'aac', 'wma', 'opus'];
  const pdfExts = ['pdf'];
  const textExts = ['txt', 'md', 'csv', 'json', 'xml', 'log', 'yaml', 'yml'];
  const csvExts = ['csv', 'xlsx', 'xls'];
  
  if (imageExts.includes(ext)) return 'image';
  if (videoExts.includes(ext)) return 'video';
  if (audioExts.includes(ext)) return 'audio';
  if (pdfExts.includes(ext)) return 'pdf';
  if (csvExts.includes(ext)) return 'csv';
  if (textExts.includes(ext)) return 'text';
  
  return 'image'; // Default fallback
}

// Utility function to get the correct API endpoint for media type
export function getMediaEndpoint(mediaType: MediaType, mediaPath: string): string {
  const API_BASE = API_BASE_URL;
  
  // Clean up the path and construct the proper URL
  const normalizedPath = mediaPath.startsWith('/') ? mediaPath : `/${mediaPath}`;
  const encodedPath = encodeURI(normalizedPath);
  
  // Use unified media endpoints that work for both annotator and verifier
  switch (mediaType) {
    case 'image':
      return `${API_BASE}/media/image${encodedPath}`;
    case 'video':
      return `${API_BASE}/media/video${encodedPath}`;
    case 'audio':
      return `${API_BASE}/media/audio${encodedPath}`;
    case 'pdf':
      return `${API_BASE}/media/pdf${encodedPath}`;
    case 'text':
      return `${API_BASE}/media/text${encodedPath}`;
    case 'csv':
      return `${API_BASE}/media/csv${encodedPath}`;
    default:
      return `${API_BASE}/media/image${encodedPath}`;
  }
}

// Utility function to get streaming URL endpoint for video/audio
export function getStreamingUrlEndpoint(mediaType: MediaType, mediaPath: string): string {
  const API_BASE = API_BASE_URL;
  
  // Clean up the path and construct the proper URL
  const normalizedPath = mediaPath.startsWith('/') ? mediaPath : `/${mediaPath}`;
  const encodedPath = encodeURI(normalizedPath);
  
  if (mediaType === 'video' || mediaType === 'audio') {
    return `${API_BASE}/media/stream-url/${mediaType}${encodedPath}`;
  }
  
  // Fallback to regular endpoint for non-streaming types
  return getMediaEndpoint(mediaType, mediaPath);
}
