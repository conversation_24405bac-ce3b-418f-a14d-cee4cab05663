"use client";

import { useCallback } from "react";
import { authFetch } from "@/lib/authFetch";
import { showToast } from "@/lib/toast";
import { FormFieldConfig } from "@/components/shared/dynamic-fields";
import { AnnotationFile } from "./useAnnotationData";

import { API_BASE_URL } from "@/lib/api";

const API_BASE = API_BASE_URL;

export function useSaveAnnotations() {
  
  const saveIndividualAnnotation = useCallback(async (
    fileUrl: string,
    label: string,
    formData: Record<string, any> | null,
    batchName: string,
    formConfig: FormFieldConfig[]
  ) => {
    try {
      // For dynamic forms, save form data to Redis
      if (formConfig.length > 0 && formData && Object.keys(formData).length > 0) {
        console.log("Saving form data to Redis for file:", fileUrl);
        const response = await authFetch(`${API_BASE}/annotator/save-individual-form-data`, {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            image_key: fileUrl, // Keep as image_key for backend compatibility
            form_data: formData,
            batch_name: batchName
          }),
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log("Form data saved to Redis successfully");
            showToast.success("Form data saved!");
            return { success: true };
          } else {
            console.error("Failed to save form data to Redis");
            showToast.error("Failed to save form data");
            return { success: false, error: "Failed to save form data" };
          }
        } else {
          console.error("API error saving form data:", response.status);
          showToast.error("Failed to save form data");
          return { success: false, error: `API error: ${response.status}` };
        }
      }
      
      // For simple labels, save to localStorage (for backward compatibility)
      if (label.trim()) {
        // This will be handled by the calling component
        return { success: true };
      }
      
      return { success: true };
    } catch (error) {
      console.error("Error saving annotation:", error);
      showToast.error("Failed to save annotation");
      return { success: false, error: "Failed to save annotation" };
    }
  }, []);

  const saveAllAnnotations = useCallback(async (
    labels: Record<string, string>,
    formData: Record<string, Record<string, any>>,
    batchName: string,
    formConfig: FormFieldConfig[]
  ) => {
    try {
      const payload: any = {
        labels,
        verification_mode: false,
        batch_name: batchName,
      };
      
      // For dynamic forms, create labels from form data
      if (formConfig.length > 0) {
        const formLabels: Record<string, string> = {};
        Object.keys(formData).forEach(fileKey => {
          if (formData[fileKey] && Object.keys(formData[fileKey]).length > 0) {
            formLabels[fileKey] = "completed"; // Dummy label to indicate completion
          }
        });
        
        // Merge with existing labels
        payload.labels = { ...labels, ...formLabels };
        console.log("Created labels from form data:", formLabels);
      }
      
      // Include form data if available
      if (Object.keys(formData).length > 0) {
        payload.form_data = formData;
      }
      
      const response = await authFetch(`${API_BASE}/annotator/save-labels`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      
      const result = await response.json();
      
      if (result.success) {
        const message = result.form_data_saved 
          ? "All labels and form data saved successfully!" 
          : "All labels saved successfully!";
        showToast.success(message);
        return { success: true, message };
      } else {
        showToast.error("Error saving labels");
        return { success: false, error: "Error saving labels" };
      }
    } catch (error: unknown) {
      console.error("Error saving labels:", error);
      showToast.error("Error saving labels");
      return { success: false, error: "Error saving labels" };
    }
  }, []);

  return {
    saveIndividualAnnotation,
    saveAllAnnotations
  };
}
