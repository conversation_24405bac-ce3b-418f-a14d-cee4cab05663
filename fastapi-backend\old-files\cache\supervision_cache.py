"""
Supervision-related caching functionality.
"""
from cache.redis_connector import cache_set, cache_get, cache_delete
from cache.base import serialize_for_cache
import logging
from core.config import settings

logger = logging.getLogger('supervision_cache')


# Cache key prefixes
DOCUMENT_STATUS_PREFIX = "document:status:"
DRIVE_FILES_PREFIX = "drive:files:"
DRIVE_FOLDERS_PREFIX = "drive:folders"
FOLDER_CONTENTS_PREFIX = "drive:folder_contents:"

# Cache TTL values (in seconds) - loaded from configuration
DOCUMENT_STATUS_TTL = settings.redis_settings.supervision_document_status_ttl
DRIVE_FILES_TTL = settings.redis_settings.supervision_drive_files_ttl
DRIVE_FOLDERS_TTL = settings.redis_settings.supervision_drive_folders_ttl
FOLDER_CONTENTS_TTL = settings.redis_settings.supervision_folder_contents_ttl

def generate_document_status_key(image_id):
    """
    Generate a cache key for document processing status

    Args:
        image_id: Document ID

    Returns:
        str: Cache key
    """
    return f"{DOCUMENT_STATUS_PREFIX}{image_id}"

def generate_drive_files_key(document_type):
    """
    Generate a cache key for Google Drive files

    Args:
        document_type: Document type

    Returns:
        str: Cache key
    """
    return f"{DRIVE_FILES_PREFIX}{document_type}"

def generate_drive_folders_key():
    """
    Generate a cache key for Google Drive folders

    Returns:
        str: Cache key
    """
    return DRIVE_FOLDERS_PREFIX

def generate_folder_contents_key(folder_id):
    """
    Generate a cache key for Google Drive folder contents

    Args:
        folder_id: Folder ID

    Returns:
        str: Cache key
    """
    return f"{FOLDER_CONTENTS_PREFIX}{folder_id}"

def cache_document_status(image_id, status_data):
    """
    Cache document processing status

    Args:
        image_id: Document ID
        status_data: Status data dictionary

    Returns:
        bool: Success status
    """
    key = generate_document_status_key(image_id)
    ttl = DOCUMENT_STATUS_TTL

    # Serialize datetime objects to strings
    serialized_status_data = serialize_for_cache(status_data)

    logger.info(f"Caching status for document {image_id}")
    return cache_set(key, serialized_status_data, ttl)

def get_cached_document_status(image_id):
    """
    Get cached document processing status

    Args:
        image_id: Document ID

    Returns:
        dict: Status data or None if not found
    """
    key = generate_document_status_key(image_id)
    logger.debug(f"Getting cached status for document {image_id}")
    return cache_get(key, json_decode=True)

def cache_drive_files(document_type, files_data):
    """
    Cache Google Drive files

    Args:
        document_type: Document type
        files_data: Files data dictionary

    Returns:
        bool: Success status
    """
    key = generate_drive_files_key(document_type)
    ttl = DRIVE_FILES_TTL

    # Serialize datetime objects to strings
    serialized_files_data = serialize_for_cache(files_data)

    logger.info(f"Caching Drive files for document type {document_type}")
    return cache_set(key, serialized_files_data, ttl)

def get_cached_drive_files(document_type):
    """
    Get cached Google Drive files

    Args:
        document_type: Document type

    Returns:
        dict: Files data or None if not found
    """
    key = generate_drive_files_key(document_type)
    logger.debug(f"Getting cached Drive files for document type {document_type}")
    return cache_get(key, json_decode=True)

def cache_drive_folders(folders_data):
    """
    Cache Google Drive folders

    Args:
        folders_data: Folders data dictionary

    Returns:
        bool: Success status
    """
    key = generate_drive_folders_key()
    ttl = DRIVE_FOLDERS_TTL

    # Serialize datetime objects to strings
    serialized_folders_data = serialize_for_cache(folders_data)

    logger.info(f"Caching Drive folders")
    return cache_set(key, serialized_folders_data, ttl)

def get_cached_drive_folders():
    """
    Get cached Google Drive folders

    Returns:
        dict: Folders data or None if not found
    """
    key = generate_drive_folders_key()
    logger.debug(f"Getting cached Drive folders")
    return cache_get(key, json_decode=True)

def cache_folder_contents(folder_id, contents_data):
    """
    Cache Google Drive folder contents

    Args:
        folder_id: Folder ID
        contents_data: Contents data dictionary

    Returns:
        bool: Success status
    """
    key = generate_folder_contents_key(folder_id)
    ttl = FOLDER_CONTENTS_TTL

    # Serialize datetime objects to strings
    serialized_contents_data = serialize_for_cache(contents_data)

    logger.info(f"Caching contents for folder {folder_id}")
    return cache_set(key, serialized_contents_data, ttl)

def get_cached_folder_contents(folder_id):
    """
    Get cached Google Drive folder contents

    Args:
        folder_id: Folder ID

    Returns:
        dict: Contents data or None if not found
    """
    key = generate_folder_contents_key(folder_id)
    logger.debug(f"Getting cached contents for folder {folder_id}")
    return cache_get(key, json_decode=True)

def invalidate_document_status_cache(image_id):
    """
    Invalidate document processing status cache

    Args:
        image_id: Document ID

    Returns:
        bool: Success status
    """
    key = generate_document_status_key(image_id)
    logger.info(f"Invalidating status cache for document {image_id}")
    return cache_delete(key)

def invalidate_drive_files_cache(document_type):
    """
    Invalidate Google Drive files cache

    Args:
        document_type: Document type

    Returns:
        bool: Success status
    """
    key = generate_drive_files_key(document_type)
    logger.info(f"Invalidating Drive files cache for document type {document_type}")
    return cache_delete(key)

def invalidate_drive_folders_cache():
    """
    Invalidate Google Drive folders cache

    Returns:
        bool: Success status
    """
    key = generate_drive_folders_key()
    logger.info(f"Invalidating Drive folders cache")
    return cache_delete(key)

def invalidate_folder_contents_cache(folder_id):
    """
    Invalidate Google Drive folder contents cache

    Args:
        folder_id: Folder ID

    Returns:
        bool: Success status
    """
    key = generate_folder_contents_key(folder_id)
    logger.info(f"Invalidating contents cache for folder {folder_id}")
    return cache_delete(key)
