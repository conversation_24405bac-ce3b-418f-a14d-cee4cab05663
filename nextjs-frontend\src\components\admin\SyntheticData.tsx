'use client';

import { useState, useEffect } from 'react';
import {  FaSync, FaTimes, FaCloudDownloadAlt, FaSave, FaBook, FaLightbulb } from 'react-icons/fa';
import { authFetch } from '@/lib/authFetch';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import ToggleSwitch from '@/components/common/ToggleSwitch';
import ReferenceSyntheticDataForm from './ReferenceSyntheticDataForm';
import NonReferenceSyntheticDataForm from './NonReferenceSyntheticDataForm';

import { API_BASE_URL } from "@/lib/api"; 

interface ModelInfo {
  id: string;
  name: string;
  description: string;
}

interface DatasetType {
  [key: string]: string;
}

interface Conversation {
  scenario: string;
  participants: string[];
  exchanges: { speaker: string; text: string }[];
}

interface CodeSnippet {
  title: string;
  language: string;
  code: string;
  explanation: string;
  concepts: string[];
}

// interface KnowledgeEntry {
//   id: number;
//   title: string;
//   topic: string;
//   contentPreview: string;
//   source?: string;
// }

type StorageOption = 'amazon_s3' | 'azure_blob' | 'nas' | 'google_drive';

interface StorageOptions {
  id: StorageOption;
  name: string;
  description: string;
}

export default function SyntheticData() {
  // Shared state between both modes
  const [userQuery, setUserQuery] = useState('');
  const [datasetType, setDatasetType] = useState('qa');
  const [numSamples, setNumSamples] = useState(3);
  const [selectedModel, setSelectedModel] = useState('gemini-2.0-flash');
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [datasetTypes, setDatasetTypes] = useState<DatasetType>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [generatedData, setGeneratedData] = useState<any[] | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Mode selection
  const [isReferenceMode, setIsReferenceMode] = useState(true);
  
  // Reference-specific state
  const [selectedKnowledgeEntry, setSelectedKnowledgeEntry] = useState<number | null>(null);
  
  // Storage options
  const [selectedStorage, setSelectedStorage] = useState<StorageOption>('amazon_s3');
  
  const storageOptions: StorageOptions[] = [
    {
      id: 'amazon_s3',
      name: 'Amazon S3',
      description: 'Store in Amazon S3 cloud storage'
    },
    {
      id: 'azure_blob',
      name: 'Azure Blob',
      description: 'Store in Microsoft Azure Blob storage'
    },
    {
      id: 'nas',
      name: 'NAS',
      description: 'Store in Network Attached Storage'
    },
    {
      id: 'google_drive',
      name: 'Google Drive',
      description: 'Store in Google Drive cloud storage'
    }
  ];
  
  // Reset form data when switching modes
  useEffect(() => {
    // Reset generation-specific data
    setGeneratedData(null);
    
    // Reset error messages
    setError(null);
    
    // If switching away from reference mode, no need to maintain knowledge entry selection
    if (!isReferenceMode) {
      setSelectedKnowledgeEntry(null);
    }
  }, [isReferenceMode]);
  
  // Fetch available models and dataset types on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // Fetch available models
        const modelsResponse = await authFetch(`${API_BASE_URL}/synthetic-dataset/models`, { credentials: 'include' });
        if (modelsResponse.ok) {
          const models = await modelsResponse.json();
          setAvailableModels(models);
          // Set default model if available
          if (models.length > 0) {
            setSelectedModel(models[0].id);
          }
        } else {
          console.error('Failed to fetch models:', modelsResponse.status);
          // If API fails, provide default models
          setAvailableModels([
            {
              id: "gemini-2.0-flash",
              name: "Gemini 2.0 Flash",
              description: "Fast and efficient model for generating synthetic datasets"
            },
            {
              id: "gemini-2.0-pro",
              name: "Gemini 2.0 Pro",
              description: "Advanced model for high-quality synthetic dataset generation"
            }
          ]);
        }
        
        // Fetch dataset types
        const typesResponse = await authFetch(`${API_BASE_URL}/synthetic-dataset/dataset-types`, { credentials: 'include' });
        if (typesResponse.ok) {
          const result = await typesResponse.json();
          if (result.success && result.data) {
            setDatasetTypes(result.data);
            // Set default dataset type if available
            if (Object.keys(result.data).length > 0) {
              setDatasetType(Object.keys(result.data)[0]);
            }
          }
        } else {
          console.error('Failed to fetch dataset types:', typesResponse.status);
          // If API fails, provide default dataset types
          setDatasetTypes({
            "qa": "Question-Answer pairs dataset",
            "articles": "Article snippets dataset",
            "conversation": "Realistic conversation dataset",
            "code_snippets": "Programming code examples dataset"
          });
        }
      } catch (error) {
        console.error('Error fetching initial data:', error);
        setError('Failed to fetch initial data. Please try again later.');
      }
    };
    
    fetchInitialData();
  }, []);
  
  // Clear any error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);
  
  // Get the icon for the dataset type
  // const getDatasetTypeIcon = (type: string) => {
  //   switch (type) {
  //     case 'qa':
  //       return <FaQuestion className="mr-2 text-blue-500" />;
  //     case 'articles':
  //       return <FaNewspaper className="mr-2 text-green-500" />;
  //     case 'conversation':
  //       return <FaComments className="mr-2 text-purple-500" />;
  //     case 'code_snippets':
  //       return <FaCode className="mr-2 text-amber-500" />;
  //     default:
  //       return <FaDatabase className="mr-2 text-gray-500" />;
  //   }
  // };
  
  const handleGenerateDataset = async () => {
    if (!userQuery) {
      setError('Please enter a query to generate data');
      return;
    }
    
    if (isReferenceMode && !selectedKnowledgeEntry) {
      setError('Please select a knowledge base entry');
      return;
    }
    
    setIsGenerating(true);
    setError(null);
    setGeneratedData(null);
    
    try {
      const endpoint = isReferenceMode 
        ? `${API_BASE_URL}/synthetic-dataset/generate` 
        : `${API_BASE_URL}/synthetic-dataset/generate-nonref`;
      
      const requestBody = isReferenceMode 
        ? {
            query: userQuery,
            dataset_type: datasetType,
            num_samples: numSamples,
            model: selectedModel,
            knowledge_entry_id: selectedKnowledgeEntry
          }
        : {
            query: userQuery,
            dataset_type: datasetType,
            num_samples: numSamples,
            model: selectedModel
          };
      
      const response = await authFetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody),
      });
      
      if (response.ok) {
        const result = await response.json();
        
        if (result.success && result.data) {
          setGeneratedData(result.data);
        } else {
          setError(result.error || 'Failed to generate dataset');
        }
      } else {
        setError(`Server returned status ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error generating dataset:', error);
      setError('An error occurred while generating the dataset');
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleSaveToStorage = async () => {
    if (!generatedData) {
      setError('No data to save');
      return;
    }
    
    setIsSaving(true);
    
    try {
      // This would be implemented when backend is ready
      // For now, just simulate a successful save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setError(null);
      // Show success message
      setError(`Dataset successfully saved to ${storageOptions.find(opt => opt.id === selectedStorage)?.name}`);
    } catch (error) {
      console.error('Error saving dataset:', error);
      setError('An error occurred while saving the dataset');
    } finally {
      setIsSaving(false);
    }
  };
  
  const downloadDataset = () => {
    if (!generatedData) return;
    
    const dataStr = JSON.stringify(generatedData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', `${datasetType}-dataset.json`);
    document.body.appendChild(linkElement);
    linkElement.click();
    document.body.removeChild(linkElement);
  };
  
  const renderDatasetPreview = () => {
    if (!generatedData) return null;
    
    if (datasetType === 'qa') {
      return (
        <div className="mt-6 space-y-4">
          {generatedData.map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors duration-200">
              <div className="font-semibold text-lg mb-2 text-blue-700">Q: {item.question}</div>
              <div className="text-gray-700 whitespace-pre-line">A: {item.answer}</div>
            </div>
          ))}
        </div>
      );
    } else if (datasetType === 'articles') {
      return (
        <div className="mt-6 space-y-6">
          {generatedData.map((item, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors duration-200">
              <h3 className="font-bold text-xl mb-2 text-green-700">{item.title}</h3>
              <div className="text-gray-700 mb-3 whitespace-pre-line">{item.content}</div>
              <div className="flex flex-wrap gap-2">
                {item.keywords.map((keyword: string, idx: number) => (
                  <span key={idx} className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      );
    } else if (datasetType === 'conversation') {
      return (
        <div className="mt-6 space-y-6">
          {generatedData.map((item: Conversation, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors duration-200">
              <div className="bg-purple-50 p-3 rounded-lg mb-4">
                <h3 className="font-bold text-lg mb-1 text-purple-700">Scenario:</h3>
                <p className="text-gray-700">{item.scenario}</p>
                <div className="mt-2 flex flex-wrap gap-2">
                  <span className="text-sm text-gray-500">Participants: </span>
                  {item.participants.map((participant, idx) => (
                    <span key={idx} className="bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full text-sm">
                      {participant}
                    </span>
                  ))}
                </div>
              </div>
              <div className="space-y-3">
                {item.exchanges.map((exchange, idx) => {
                  const isEven = idx % 2 === 0;
                  return (
                    <div key={idx} className={`flex ${isEven ? 'justify-start' : 'justify-end'}`}>
                      <div className={`max-w-[80%] rounded-lg p-3 ${isEven ? 'bg-purple-100 text-purple-900' : 'bg-gray-100 text-gray-900'}`}>
                        <div className="font-semibold mb-1">{exchange.speaker}</div>
                        <div>{exchange.text}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      );
    } else if (datasetType === 'code_snippets') {
      return (
        <div className="mt-6 space-y-6">
          {generatedData.map((item: CodeSnippet, index: number) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden hover:border-amber-300 transition-colors duration-200">
              <div className="bg-gray-800 text-white p-4 flex justify-between items-center">
                <h3 className="font-bold text-lg">{item.title}</h3>
                <span className="bg-amber-200 text-amber-800 px-2 py-1 rounded text-sm">
                  {item.language}
                </span>
              </div>
              <div className="overflow-auto max-h-96">
                <SyntaxHighlighter 
                  language={item.language.toLowerCase()} 
                  style={vscDarkPlus}
                  showLineNumbers
                  wrapLongLines
                >
                  {item.code}
                </SyntaxHighlighter>
              </div>
              <div className="p-4 bg-gray-50">
                <div className="mb-3">
                  <h4 className="font-semibold text-gray-700 mb-2">Explanation:</h4>
                  <p className="text-gray-600">{item.explanation}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Key Concepts:</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.concepts.map((concept, idx) => (
                      <span key={idx} className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-sm">
                        {concept}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      );
    }
    
    return (
      <div className="mt-6 border border-gray-200 rounded-lg p-4">
        <pre className="whitespace-pre-wrap overflow-auto max-h-96">{JSON.stringify(generatedData, null, 2)}</pre>
      </div>
    );
  };
  
  return (
    <div className="bg-white">
      <div className="max-w-6xl mx-auto px-8 py-12">
        <div className="text-center mb-8">

          <h1 className="text-4xl font-bold text-primary inline-block">Synthetic Dataset Generator</h1>
          <div className="mt-2 mx-auto h-1 w-20 bg-primary"></div>
          <p className="mt-4 text-gray-600">Generate high-quality synthetic datasets with or without knowledge base references</p>
        </div>
        
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden p-8">
          {/* Error Alert */}
          {error && (
            <div className={`mb-6 p-4 ${error.includes('successfully') ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700'} border rounded flex justify-between items-center`}>
              <span>{error}</span>
              <button onClick={() => setError(null)} className={error.includes('successfully') ? "text-green-500" : "text-red-500"}>
                <FaTimes />
              </button>
            </div>
          )}
          
          {/* Toggle between reference and non-reference mode */}
          <div className="mb-8">
            <ToggleSwitch
              leftOption="With Reference"
              rightOption="Without Reference"
              leftIcon={<FaBook />}
              rightIcon={<FaLightbulb />}
              isRightActive={!isReferenceMode}
              onChange={(isRight) => setIsReferenceMode(!isRight)}
            />
            <p className="text-center mt-3 text-sm text-gray-500">
              {isReferenceMode 
                ? "Generate datasets based on knowledge base entries for higher accuracy and consistency." 
                : "Generate datasets without specific references for more creative and diverse outputs."}
            </p>
          </div>
          
          {/* Form Section */}
          {isReferenceMode ? (
            <ReferenceSyntheticDataForm
              userQuery={userQuery}
              setUserQuery={setUserQuery}
              datasetType={datasetType}
              setDatasetType={setDatasetType}
              numSamples={numSamples}
              setNumSamples={setNumSamples}
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              availableModels={availableModels}
              datasetTypes={datasetTypes}
              onGenerate={handleGenerateDataset}
              isGenerating={isGenerating}
              selectedKnowledgeEntry={selectedKnowledgeEntry}
              setSelectedKnowledgeEntry={setSelectedKnowledgeEntry}
            />
          ) : (
            <NonReferenceSyntheticDataForm
              userQuery={userQuery}
              setUserQuery={setUserQuery}
              datasetType={datasetType}
              setDatasetType={setDatasetType}
              numSamples={numSamples}
              setNumSamples={setNumSamples}
              selectedModel={selectedModel}
              setSelectedModel={setSelectedModel}
              availableModels={availableModels}
              datasetTypes={datasetTypes}
              onGenerate={handleGenerateDataset}
              isGenerating={isGenerating}
            />
          )}
          
          {/* Storage Selection */}
          {generatedData && (
            <div className="mt-6 border-t pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">Storage Option</label>
                  <div className="relative">
                    <select
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                      value={selectedStorage}
                      onChange={(e) => setSelectedStorage(e.target.value as StorageOption)}
                    >
                      {storageOptions.map((option) => (
                        <option key={option.id} value={option.id}>
                          {option.name} - {option.description}
                        </option>
                      ))}
                    </select>
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      
                    </div>
                  </div>
                </div>
                <div className="flex items-end">
                  <div className="flex space-x-3 w-full">
                    <button 
                      className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed flex-1"
                      onClick={handleSaveToStorage}
                      disabled={isSaving || !generatedData}
                    >
                      {isSaving ? (
                        <>
                          <FaSync className="animate-spin mr-2" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <FaSave className="mr-2" />
                          Save to {storageOptions.find(opt => opt.id === selectedStorage)?.name}
                        </>
                      )}
                    </button>
                    <button 
                      className="flex items-center justify-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex-1"
                      onClick={downloadDataset}
                    >
                      <FaCloudDownloadAlt className="mr-2" />
                      Download JSON
                    </button>

                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Results Section */}
          {generatedData && (
            <div className="mt-10 border-t border-gray-200 pt-6">
              <div className="mb-4">
                <h2 className="text-2xl font-bold text-gray-800">Generated Dataset</h2>
                <p className="text-gray-600">
                  {isReferenceMode 
                    ? "Generated with reference-based approach for higher accuracy" 
                    : "Generated without specific references for more creative outputs"}
                </p>
              </div>
              
              {renderDatasetPreview()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 