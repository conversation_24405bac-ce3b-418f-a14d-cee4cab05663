"""
Project Context Manager

Provides utilities to get current project context and credentials
without relying on environment variables.
"""

import logging
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.clients import Clients
from cache import cache_get, cache_set

logger = logging.getLogger(__name__)

class ProjectContext:
    """Manages project context and credentials"""

    @staticmethod
    async def get_user_client(db: AsyncSession, username: str) -> Optional[Clients]:
        """
        Get client associated with a user using cached lookup for efficiency.

        Args:
            db: Database session
            username: Username to find client for

        Returns:
            Clients object or None if not found
        """
        if not username:
            return None

        # Try cache first
        cache_key = f"user_client:{username}"
        cached_client_id = await cache_get(cache_key)

        if cached_client_id:
            # Get client by ID from cache
            result = await db.execute(
                select(Clients).where(Clients.id == int(cached_client_id))
            )
            client = result.scalar_one_or_none()
            if client:
                return client

        # Cache miss - do database lookup
        from post_db.master_models.users import User

        user_result = await db.execute(
            select(User).where(User.username == username)
        )
        user = user_result.scalar_one_or_none()

        if not user:
            return None

        # Try to find client by matching user's contact info instead of name
        # This prevents issues with duplicate client names
        client = None
        
        # Try by email if user has email field
        if hasattr(user, 'email') and user.email:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.email == user.email)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by email: {e}")
        
        # Try by username if no client found yet
        if not client and user.username:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.username == user.username)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by username: {e}")
        
        # Fallback to name matching if no client found (for backward compatibility)
        if not client:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.name == user.full_name)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by full_name: {e}")
        
        if not client and user.username:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.name == user.username)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by username as name: {e}")

        # Cache the result for future lookups
        if client:
            await cache_set(cache_key, str(client.id), ttl=3600)  # Cache for 1 hour
            logger.debug(f"Cached user-client relationship: {username} -> {client.id}")

        return client

    @staticmethod
    async def invalidate_user_client_cache(username: str) -> bool:
        """
        Invalidate cached user-client relationship for a specific user.

        Args:
            username: Username to invalidate cache for

        Returns:
            bool: Success status
        """
        from cache import cache_delete
        if not username:
            return False

        cache_key = f"user_client:{username}"
        success = await cache_delete(cache_key)
        if success:
            logger.debug(f"Invalidated user-client cache for {username}")
        return success

    @staticmethod
    async def clear_all_user_client_cache() -> int:
        """
        Clear all user-client cache entries (admin function).

        Returns:
            int: Number of cache entries cleared
        """
        from cache import delete_keys_by_pattern
        # Delete all keys matching the user_client pattern
        deleted_count = await delete_keys_by_pattern("user_client:*")
        logger.info(f"Cleared {deleted_count} user-client cache entries")
        return deleted_count

    @staticmethod
    async def get_current_project(
        db: AsyncSession,
        project_code: Optional[str] = None,
        client_id: Optional[int] = None,
        username: Optional[str] = None
    ) -> Optional[ProjectsRegistry]:
        """
        Get current project context

        Args:
            db: Database session
            project_code: Specific project code to fetch
            client_id: Client ID to get most recent project for
            username: Username to find associated client and project

        Returns:
            ProjectsRegistry object or None if not found
        """
        try:
            if project_code:
                # Get specific project by code
                result = await db.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                return result.scalar_one_or_none()

            elif client_id:
                # Get most recent project for client
                result = await db.execute(
                    select(ProjectsRegistry)
                    .where(ProjectsRegistry.client_id == client_id)
                    .order_by(desc(ProjectsRegistry.created_at))
                    .limit(1)
                )
                return result.scalar_one_or_none()

            elif username:
                # Get project for user's associated client using cached lookup
                client = await ProjectContext.get_user_client(db, username)
                if not client:
                    logger.warning(f"No client found for user {username}")
                    return None

                # Get most recent active project for this client
                result = await db.execute(
                    select(ProjectsRegistry)
                    .where(ProjectsRegistry.client_id == client.id)
                    # .where(ProjectsRegistry.project_status == 'active')
                    .order_by(desc(ProjectsRegistry.created_at))
                    .limit(1)
                )
                return result.scalar_one_or_none()

            else:
                # Get most recent active project as fallback
                result = await db.execute(
                    select(ProjectsRegistry)
                    # .where(ProjectsRegistry.project_status == 'active')
                    .order_by(desc(ProjectsRegistry.created_at))
                    .limit(1)
                )
                return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Error getting current project: {e}")
            return None

    @staticmethod
    async def get_project_credentials(db: AsyncSession, project: ProjectsRegistry) -> Optional[Dict[str, Any]]:
        """
        Get storage credentials for a project

        Args:
            db: Database session
            project: ProjectsRegistry object

        Returns:
            Dictionary with storage credentials or None if not found
        """
        try:
            # If project has credentials, return it
            if project and project.credentials:
                return project.credentials

            # If credentials is None/empty, try to refresh the project from database
            # This handles cases where the field wasn't loaded properly in the initial query
            if project:
                logger.debug(f"Refreshing project {project.project_code} to load credentials")
                await db.refresh(project)
                if project.credentials:
                    logger.debug(f"Found credentials after refresh for project {project.project_code}")
                    return project.credentials

            # Fallback: try to get from related client projects with explicit field loading
            result = await db.execute(
                select(ProjectsRegistry)
                .where(ProjectsRegistry.client_id == project.client_id)
                .where(ProjectsRegistry.credentials.isnot(None))
                .order_by(desc(ProjectsRegistry.created_at))
                .limit(1)
            )           

            return None

        except Exception as e:
            logger.error(f"Error getting project credentials: {e}")
            return None

    @staticmethod
    async def get_project_connection_type(db: AsyncSession, project: ProjectsRegistry) -> Optional[str]:
        """
        Get connection type for a project

        Args:
            db: Database session
            project: ProjectsRegistry object

        Returns:
            Connection type string or None if not found
        """
        try:
            if project and project.connection_type:
                return project.connection_type

            # Try to refresh the project from database
            if project:
                await db.refresh(project)
                if project.connection_type:
                    return project.connection_type
            return None

        except Exception as e:
            logger.error(f"Error getting project connection type: {e}")
            return None

    @staticmethod
    async def get_client_from_project(db: AsyncSession, project: ProjectsRegistry) -> Optional[Clients]:
        """
        Get client information for a project

        Args:
            db: Database session
            project: ProjectsRegistry object

        Returns:
            Clients object or None if not found
        """
        try:
            if project and project.client_id:
                result = await db.execute(
                    select(Clients).where(Clients.id == project.client_id)
                )
                return result.scalar_one_or_none()

            return None

        except Exception as e:
            logger.error(f"Error getting client from project: {e}")
            return None
