"use client";
import React, { useState } from "react";
import { useProjectServiceWithFilter } from "../projectService";
import { useModelService } from "../modelService";
import { FaSpinner } from "react-icons/fa";
import toast from "react-hot-toast";
import { CaptionProps, CaptionFormData } from "./caption";
import { API_BASE_URL } from "../../../../lib/api";

export const Caption: React.FC<CaptionProps> = ({ onResultReceived }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<CaptionFormData>({
    folderPath: "",
    projectCode: "",
    customPrompt: "",
    processingMode: "project",
    modelId: "",
  });

  // Use filtered project service for image projects only and model service
  const { projects, isLoading: isLoadingProjects } = useProjectServiceWithFilter('image');
  const { models, isLoading: isLoadingModels, getModelsByFileType } = useModelService();
  
  // Filter models that support image processing, fallback to all models if none found
  const imageModels = getModelsByFileType('image').length > 0 
    ? getModelsByFileType('image') 
    : models;

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.projectCode) {
      toast.error("Please select a project");
      return;
    }

    if (!formData.modelId) {
      toast.error("Please select an AI model");
      return;
    }

    setIsLoading(true);

    try {
      const formDataToSend = new FormData();

      formDataToSend.append("project_code", formData.projectCode!);
      formDataToSend.append("model_name", formData.modelId!);

      if (formData.customPrompt) {
        formDataToSend.append("custom_prompt", formData.customPrompt);
      }

      const response = await fetch(`${API_BASE_URL}/ai/caption/batch/project`, {
        method: "POST",
        credentials: "include",
        body: formDataToSend,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to process project");
      }

      const result = await response.json();
      toast.success("Project processed successfully");
      onResultReceived(result);
    } catch (error) {
      console.error("Error processing:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process");
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="space-y-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Batch Caption Generation</h3>
        <p className="text-sm text-gray-600">Generate captions for images from selected projects</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Project Selection */}
        <div>
          <label htmlFor="projectCode" className="block text-sm font-medium text-gray-700 mb-1">
            Select Project
          </label>
          <div className="flex">
            <div className="relative flex-grow">
              <select
                id="projectCode"
                name="projectCode"
                value={formData.projectCode || ""}
                onChange={handleFormChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
                disabled={isLoadingProjects}
              >
                <option value="">
                  {isLoadingProjects ? "Loading projects..." : "Select a project"}
                </option>
                {projects.map((project) => (
                  <option key={project.id} value={project.project_code}>
                    {project.project_code} - {project.project_name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Choose a project to process images from its configured folder path
          </p>
        </div>

        {/* Model Selection */}
        <div>
          <label htmlFor="modelId" className="block text-sm font-medium text-gray-700 mb-1">
            Select AI Model
          </label>
          <div className="flex">
            <div className="relative flex-grow">
              <select
                id="modelId"
                name="modelId"
                value={formData.modelId || ""}
                onChange={handleFormChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
                disabled={isLoadingModels}
              >
                <option value="">
                  {isLoadingModels ? "Loading models..." : "Select an AI model"}
                </option>
                {imageModels.map((model) => (
                  <option key={model.id} value={model.model_id}>
                    {model.model_name} ({model.model_id})
                  </option>
                ))}
              </select>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Choose an AI model that supports image captioning
          </p>
        </div>

        <div>
            <label htmlFor="customPrompt" className="block text-sm font-medium text-gray-700 mb-1">
              Custom Prompt (Optional)
            </label>
            <textarea
              id="customPrompt"
              name="customPrompt"
              value={formData.customPrompt || ""}
              onChange={handleFormChange}
              rows={2}
              className="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe this image in detail..."
            />
            <p className="mt-1 text-xs text-gray-500">
              Custom instruction to guide the caption generation
            </p>
          </div>

        <div className="pt-2">
          <button
            type="submit"
            disabled={isLoading || (formData.processingMode === "project" && isLoadingProjects) || isLoadingModels}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300"
          >
            {isLoading ? (
              <>
                <FaSpinner className="animate-spin mr-2" /> Processing...
              </>
            ) : formData.processingMode === "project" ? (
              "Generate Captions from Project"
            ) : (
              "Generate Captions from Folder"
            )}
          </button>
        </div>
        </form>
    </div>
  );
};
