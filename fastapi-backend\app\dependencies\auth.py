"""
Authentication dependencies module.
Contains reusable dependencies for authentication and authorization.
"""
import logging
from typing import Dict, Optional, Any, List, Callable
from fastapi import Depends, HTTPException, status, Request, Cookie # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timezone
from jose import JWTError # type: ignore

from core.config import settings
from core.security import verify_token
from core.session_manager import get_master_db_session
from post_db.master_models.users import users
from post_db.master_models.users import UserRole
from schemas.UserSchemas import UserCreate, UserUpdate, UserResponse

# Configure logging
logger = logging.getLogger('auth')

# Common exceptions
CREDENTIALS_EXCEPTION = HTTPException(
    status_code=status.HTTP_401_UNAUTHORIZED,
    detail="Could not validate credentials",
    headers={"WWW-Authenticate": "Bearer"},
)
INACTIVE_USER_EXCEPTION = HTTPException(
    status_code=status.HTTP_403_FORBIDDEN,
    detail="Inactive user",
)
PERMISSION_DENIED_EXCEPTION = HTTPException(
    status_code=status.HTTP_403_FORBIDDEN,
    detail="Not enough permissions",
)

class UserService:
    """Service class for user-related operations"""

    def __init__(self, db: AsyncSession):
        self.db: AsyncSession = db

    async def get_user_by_username(self, username: str) -> Optional[users]:
        try:
            result = await self.db.execute(select(users).where(users.username == username))
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_user_by_username: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error occurred"
            )

    async def get_user_by_email(self, email: str) -> Optional[users]:
        try:
            result = await self.db.execute(select(users).where(users.email == email))
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_user_by_email: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error occurred"
            )

    async def create_user(self, user_data: UserCreate) -> users:
        try:
            user = users(**user_data.model_dump())
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            logger.info(f"Created new user: {user.username}")
            return user
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error in create_user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating user"
            )

    async def update_user(self, user: users, user_data: UserUpdate) -> users:
        try:
            for field, value in user_data.model_dump(exclude_unset=True).items():
                setattr(user, field, value)
            await self.db.commit()
            await self.db.refresh(user)
            logger.info(f"Updated user: {user.username}")
            return user
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error in update_user: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating user"
            )

    async def update_user_password(self, user: users, password_hash: str) -> users:
        try:
            user.password_hash = password_hash
            await self.db.commit()
            await self.db.refresh(user)
            logger.info(f"Updated password for user: {user.username}")
            return user
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error in update_user_password: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating password"
            )

    async def update_last_login(self, user: users) -> users:
        try:
            user.last_login = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(user)
            return user
        except SQLAlchemyError as e:
            await self.db.rollback()
            logger.error(f"Database error in update_last_login: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating last login"
            )

    async def get_users_by_role(self, role: UserRole, skip: int = 0, limit: int = 100) -> List[users]:
        try:
            result = await self.db.execute(
                select(users).where(users.role == role).offset(skip).limit(limit)
            )
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_users_by_role: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error fetching users"
            )

    async def get_annotator_mode(self, username: str) -> Optional[str]:
        user = await self.get_user_by_username(username)
        if user:
            # Handle case where annotator_mode might not exist
            if hasattr(user, 'annotator_mode') and user.annotator_mode:
                return user.annotator_mode.value if hasattr(user.annotator_mode, 'value') else str(user.annotator_mode)
            # Default to annotation mode if not set
            return "annotation"
        return None

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[users]:
        """Get all users with pagination"""
        try:
            result = await self.db.execute(
                select(users).offset(skip).limit(limit)
            )
            return result.scalars().all()
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_all: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error fetching users"
            )


def get_user_service(db: AsyncSession = Depends(get_master_db_session)) -> UserService:
    return UserService(db)


def create_user_response(user: users) -> UserResponse:
    return UserResponse.model_validate(user)


async def get_current_user(
    request: Request,
    access_token: Optional[str] = Cookie(None),
    user_service: UserService = Depends(get_user_service)
) -> Dict[str, Any]:
    if not access_token:
        raise CREDENTIALS_EXCEPTION
        
    try:
        payload = verify_token(access_token)
        if payload is None:
            raise CREDENTIALS_EXCEPTION
            
        username = payload.get("sub")
        token_type = payload.get("token_type")
        
        if token_type != "access":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token type. Access token required.",
                headers={"WWW-Authenticate": "Bearer"}
            )
            
        user = await user_service.get_user_by_username(username)
        if user is None:
            raise CREDENTIALS_EXCEPTION
            
        request.state.user = username
        return payload
    except JWTError:
        raise CREDENTIALS_EXCEPTION


async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> Dict[str, Any]:
    user = await user_service.get_user_by_username(current_user["sub"])
    if not user.is_active:
        raise INACTIVE_USER_EXCEPTION
    return current_user


async def get_annotator_mode(
    current_user: Dict[str, Any] = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
) -> Optional[str]:
    return await user_service.get_annotator_mode(current_user["sub"])


def check_roles(
    allowed_roles: List[UserRole]
) -> Callable:
    async def role_checker(
        current_user: Dict[str, Any] = Depends(get_current_user),
        user_service: UserService = Depends(get_user_service)
    ) -> Dict[str, Any]:
        user = await user_service.get_user_by_username(current_user["sub"])
        if not user or user.role not in allowed_roles:
            raise PERMISSION_DENIED_EXCEPTION
        return current_user
    return role_checker


def check_roles_and_modes(
    allowed_roles: List[UserRole],
    allowed_modes: Optional[List[str]] = None
) -> Callable:
    """
    Check both user role and annotation mode
    
    Args:
        allowed_roles: List of allowed user roles
        allowed_modes: Optional list of allowed annotation modes (only applies to annotators)
    
    Returns:
        Dependency function that validates both role and mode
    """
    async def role_and_mode_checker(
        current_user: Dict[str, Any] = Depends(get_current_user),
        user_service: UserService = Depends(get_user_service)
    ) -> Dict[str, Any]:
        user = await user_service.get_user_by_username(current_user["sub"])
        
        # Check role first
        if not user or user.role not in allowed_roles:
            raise PERMISSION_DENIED_EXCEPTION
        
        # Check annotation mode if specified and user is annotator
        if allowed_modes and user.role == UserRole.ANNOTATOR:
            user_mode = getattr(user, 'annotator_mode', None)
            # Compare the enum value (string) with allowed modes
            user_mode_value = user_mode.value if user_mode else None
            if user_mode_value not in allowed_modes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied. Required annotation mode: {', '.join(allowed_modes)}. Your mode: {user_mode_value}"
                )
        
        return current_user
    return role_and_mode_checker


def require_admin() -> Callable:
    """Admin-only access"""
    return check_roles([UserRole.ADMIN])


def require_annotator(modes: Optional[List[str]] = None) -> Callable:
    """Annotator access with optional mode restriction"""
    if modes:
        return check_roles_and_modes([UserRole.ANNOTATOR], modes)
    return check_roles([UserRole.ANNOTATOR])


def require_verifier() -> Callable:
    """Verifier-only access"""
    return check_roles([UserRole.VERIFIER])


def require_auditor() -> Callable:
    """Auditor-only access"""
    return check_roles([UserRole.AUDITOR])


def require_client() -> Callable:
    """Client-only access"""
    return check_roles([UserRole.CLIENT])


def normalize_path(path: str) -> str:
    """
    Normalize file path by converting backslashes to forward slashes.
    
    Args:
        path: File path to normalize
        
    Returns:
        Normalized path with forward slashes
    """
    return path.replace('\\', '/') if path else path
