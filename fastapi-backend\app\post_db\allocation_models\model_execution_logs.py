"""
Model Execution Logs - Project Database Model
This module defines the SQLAlchemy model for tracking AI model execution logs,
following the allocation_models pattern for project-specific execution tracking.
"""

from sqlalchemy import (
    Column, Integer, String, TIMESTAMP, Boolean, func, ForeignKey, Text, DECIMAL
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum


class ExecutionStatus(str, PyEnum):
    """Model execution status enumeration."""
    SUCCESS = 'success'
    FAILED = 'failed'
    TIMEOUT = 'timeout'
    CANCELLED = 'cancelled'
    IN_PROGRESS = 'in_progress'


class ModelExecutionLogs(ProjectBase):
    """
    Project-specific AI model execution logs.
    Tracks all model inference operations, performance metrics, and human verification
    for complete audit trail and quality assurance within each project.
    """
    __tablename__ = 'model_execution_logs'

    # Primary Identity & References
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment='Unique execution log identifier within this project database')
    model_name = Column(String(255), nullable=False,
                        comment='Name of the AI model used for this execution')
      
    # Project Context & Workflow Integration
    batch_id = Column(Integer, ForeignKey("allocation_batches.id", ondelete="CASCADE"),
                     comment='Batch identifier for grouped processing and progress tracking')
    file_id = Column(Integer, ForeignKey("files_registry.id", ondelete="CASCADE"),
                    comment='File being processed in this execution')

    #prompt received 
    user_prompt = Column(Text,
                         comment='Prompt received from user for this execution')
    system_prompt = Column(Text,
                           comment='System prompt for this execution')
    # Execution Timing & Performance
    execution_start_time = Column(TIMESTAMP, nullable=False, default=func.now(),
                                 comment='Timestamp when model execution began')
    execution_end_time = Column(TIMESTAMP,
                               comment='Timestamp when model execution completed')
    execution_duration_ms = Column(Integer,
                                  comment='Total execution time in milliseconds for performance tracking')
    execution_status = Column(CaseInsensitiveEnum(ExecutionStatus), nullable=False,
                             comment='Final status of model execution for workflow decisions')
    
    # Model Input/Output Data
    input_data_info = Column(JSONB,
                            comment='Information about input data (size, format, preprocessing applied)')
    output_data = Column(JSONB,
                        comment='Model predictions and results in structured format')
    confidence_scores = Column(JSONB,
                              comment='Confidence scores for each prediction element')
    model_config_snapshot = Column(JSONB,
                                  comment='Complete model configuration used for this execution')
    # Error Handling & Debugging
    error_message = Column(Text,
                          comment='Detailed error message if execution failed')
    error_code = Column(String(50),
                       comment='Standardized error code for categorization and handling')
    retry_count = Column(Integer, default=0, nullable=False,
                        comment='Number of times this execution has been retried')
    
    # Audit Trail & System Context
    triggered_by = Column(String(255),
                         comment='System component or user that initiated this execution')
    
    # Relationships
    batch = relationship("AllocationBatches", foreign_keys=[batch_id])
    file = relationship("FilesRegistry", foreign_keys=[file_id])

    def __repr__(self):
        return f"<ModelExecutionLogs(id={self.id}, model_name={self.model_name}, status={self.execution_status}, file_id={self.file_id})>"

