#!/usr/bin/env python3
"""
Setup script for PostgreSQL test databases.
Creates test databases for DADP integration testing.
"""
import asyncio
import asyncpg
import sys
import os
from pathlib import Path

# Connection configurations
POSTGRES_CONFIGS = {
    "remote": {
        "host": "***********",
        "port": 5432,
        "user": "mansi",  # Usually need postgres user for database creation
        "password": "pass123",  # Update this
        "databases": {
            "test_project_db": {"user": "mansi", "password": "pass123"},
            "test_master_db": {"user": "kanwar_raj", "password": "dadpdev123"}
        }
    },
    "local": {
        "host": "localhost", 
        "port": 5432,
        "user": "mansi",
        "password": "pass123",  # Update this for your local setup
        "databases": {
            "test_project_db": {"user": "mansi", "password": "pass123"},
            "test_master_db": {"user": "kanwar_raj", "password": "dadpdev123"}
        }
    }
}


async def create_test_database(config, db_name, db_config):
    """Create a test database if it doesn't exist."""
    try:
        # Connect to PostgreSQL server (usually 'postgres' database)
        conn = await asyncpg.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            database="postgres"  # Connect to default postgres database
        )
        
        # Check if database exists
        db_exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1", db_name
        )
        
        if not db_exists:
            # Create database
            await conn.execute(f'CREATE DATABASE "{db_name}"')
            print(f"✅ Created database: {db_name}")
        else:
            print(f"ℹ️  Database already exists: {db_name}")
        
        await conn.close()
        
        # Test connection to new database
        test_conn = await asyncpg.connect(
            host=config["host"],
            port=config["port"], 
            user=db_config["user"],
            password=db_config["password"],
            database=db_name
        )
        
        # Test query
        result = await test_conn.fetchval("SELECT version()")
        print(f"✅ Connection test successful for {db_name}")
        print(f"   PostgreSQL version: {result[:50]}...")
        
        await test_conn.close()
        
    except Exception as e:
        print(f"❌ Error with database {db_name}: {e}")
        return False
    
    return True


async def setup_test_databases(use_local=False):
    """Setup all test databases."""
    config_key = "local" if use_local else "remote"
    config = POSTGRES_CONFIGS[config_key]
    
    print(f"🚀 Setting up test databases on {config['host']}:{config['port']}")
    print("="*60)
    
    success_count = 0
    total_count = len(config["databases"])
    
    for db_name, db_config in config["databases"].items():
        if await create_test_database(config, db_name, db_config):
            success_count += 1
    
    print("="*60)
    if success_count == total_count:
        print(f"🎉 All {total_count} test databases set up successfully!")
        print("\n📋 Database connection strings for testing:")
        for db_name, db_config in config["databases"].items():
            conn_str = f"postgresql+asyncpg://{db_config['user']}:{db_config['password']}@{config['host']}:{config['port']}/{db_name}"
            print(f"   {db_name}: {conn_str}")
        
        print(f"\n🔧 Environment variables to set:")
        print(f"   export DATABASE_URL=\"postgresql+asyncpg://{config['databases']['test_project_db']['user']}:{config['databases']['test_project_db']['password']}@{config['host']}:{config['port']}/test_project_db\"")
        print(f"   export MASTER_DATABASE_URL=\"postgresql+asyncpg://{config['databases']['test_master_db']['user']}:{config['databases']['test_master_db']['password']}@{config['host']}:{config['port']}/test_master_db\"")
        
    else:
        print(f"⚠️  Only {success_count}/{total_count} databases set up successfully")


async def clean_test_databases(use_local=False):
    """Clean test databases by dropping them."""
    config_key = "local" if use_local else "remote"
    config = POSTGRES_CONFIGS[config_key]
    
    print(f"🧹 Cleaning test databases on {config['host']}:{config['port']}")
    
    try:
        conn = await asyncpg.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            database="postgres"
        )
        
        for db_name in config["databases"].keys():
            # Terminate connections to database
            await conn.execute(f"""
                SELECT pg_terminate_backend(pid) 
                FROM pg_stat_activity 
                WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
            """)
            
            # Drop database
            await conn.execute(f'DROP DATABASE IF EXISTS "{db_name}"')
            print(f"🗑️  Dropped database: {db_name}")
        
        await conn.close()
        print("✅ Test databases cleaned successfully")
        
    except Exception as e:
        print(f"❌ Error cleaning databases: {e}")


async def test_connections(use_local=False):
    """Test connections to all databases."""
    config_key = "local" if use_local else "remote"
    config = POSTGRES_CONFIGS[config_key]
    
    print(f"🔍 Testing database connections...")
    
    for db_name, db_config in config["databases"].items():
        try:
            conn = await asyncpg.connect(
                host=config["host"],
                port=config["port"],
                user=db_config["user"],
                password=db_config["password"],
                database=db_name
            )
            
            result = await conn.fetchval("SELECT current_database()")
            print(f"✅ {db_name}: Connected successfully (current db: {result})")
            
            await conn.close()
            
        except Exception as e:
            print(f"❌ {db_name}: Connection failed - {e}")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Setup PostgreSQL test databases for DADP')
    parser.add_argument('action', choices=['setup', 'clean', 'test'], 
                       help='Action to perform')
    parser.add_argument('--local', action='store_true',
                       help='Use local PostgreSQL instead of remote')
    parser.add_argument('--update-config', action='store_true',
                       help='Show configuration update instructions')
    
    args = parser.parse_args()
    
    if args.update_config:
        print("🔧 Configuration Update Instructions:")
        print("="*50)
        print("1. Update PostgreSQL credentials in this script:")
        print("   - Edit POSTGRES_CONFIGS with your actual passwords")
        print("   - Ensure PostgreSQL users have database creation privileges")
        print("\n2. Update test configuration in tests/conftest.py:")
        print("   - Uncomment the local configuration lines if using local PostgreSQL")
        print("   - Update connection strings with your actual credentials")
        print("\n3. Ensure PostgreSQL is running and accessible")
        print("   - For local: Check PostgreSQL service is running")
        print("   - For remote: Check network connectivity and firewall settings")
        return
    
    if args.action == 'setup':
        asyncio.run(setup_test_databases(use_local=args.local))
    elif args.action == 'clean':
        asyncio.run(clean_test_databases(use_local=args.local))
    elif args.action == 'test':
        asyncio.run(test_connections(use_local=args.local))


if __name__ == '__main__':
    main()
