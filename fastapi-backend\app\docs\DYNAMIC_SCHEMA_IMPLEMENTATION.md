# Dynamic Schema and Allocation Batches

## Overview

This document describes the implementation of dynamic database schemas based on allocation strategies, with particular focus on the `allocation_batches` table structure.

The dynamic schema implementation allows project databases to be created with custom schemas based on allocation strategies. This enables:

1. Different table structures for different projects
2. Dynamic columns in tables like `allocation_batches` based on strategy requirements
3. Conditional inclusion of tables like `model_execution_logs` based on strategy features
4. Dynamic roles in the `project_users` table based on strategy requirements

## Components

### 1. Enhanced Allocation Strategies

The `AllocationStrategies` model in the master database has been enhanced with new fields:

- `num_annotators`: Number of annotators required
- `audit_levels`: Number of auditors required
- `requires_ai_preprocessing`: Whether AI preprocessing is needed
- `is_blind_workflow`: Whether assignments should be blind to each other

### 2. Dynamic Schema Generator

The `DynamicSchemaGenerator` class generates database schemas based on allocation strategies:

- Creates SQL migration scripts with tables and columns customized for specific strategies
- Adds dynamic columns to tables like `allocation_batches` based on strategy requirements
- Conditionally includes tables like `model_execution_logs` based on strategy features

### 3. Project Database Provisioner

The `ProjectDatabaseProvisioner` class has been enhanced to use dynamic schemas:

- `create_project_database_with_strategy`: Creates a database with a custom schema based on a strategy
- `_run_dynamic_migrations`: Runs migrations with dynamically generated scripts

### 4. Project Creation Routes

The project creation routes have been updated to support allocation strategies:

- `provision_project_database_route`: Accepts a `strategy_id` parameter to use for schema generation
- `get_allocation_strategies`: Returns available allocation strategies

### 5. Project Batch Service

The `DynamicProjectBatchService` class handles dynamic columns in allocation batches:

- `assign_annotators_to_batch`: Assigns annotators to dynamic columns based on strategy
- `assign_auditors_to_batch`: Assigns auditors to dynamic columns based on strategy
- `get_batch_assignments`: Gets all assignments for a batch

### 6. Project Users Service

The `ProjectUsersService` class handles dynamic roles based on allocation strategies:

- `get_available_roles`: Gets available roles for a project based on its strategy
- `add_user_to_project`: Adds a user to a project with a specific role
- `get_project_users`: Gets all users in a project
- `remove_user_from_project`: Removes a user from a project

## Allocation Batches Schema

### Base Columns

These columns are present in all allocation batches tables:

| Column Name | Type | Description |
|-------------|------|-------------|
| id | Integer | Primary key |
| batch_identifier | String | Unique batch identifier |
| batch_purpose | String | Purpose of the batch (annotation, review, etc.) |
| default_strategy | String | Default allocation strategy |
| total_files | Integer | Total number of files in the batch |
| file_list | JSONB | Array of file IDs |
| skill_requirements | JSONB | Required skills for users |
| allocation_criteria | JSONB | Custom criteria for user selection |
| max_concurrent_users | Integer | Maximum number of users working simultaneously |
| batch_status | String | Current batch state |
| total_allocations_needed | Integer | Total allocations required |
| allocations_created | Integer | Number of allocations created |
| allocations_active | Integer | Number of currently active allocations |
| allocations_completed | Integer | Number of completed allocations |
| is_priority | Boolean | Whether this batch has priority |
| created_at | TIMESTAMP | When batch was created |
| allocation_started_at | TIMESTAMP | When allocation process began |
| allocation_completed_at | TIMESTAMP | When all allocations were made |
| deadline | TIMESTAMP | Target completion deadline |
| annotator_count | Integer | Number of annotators for this batch |
| custom_batch_config | JSONB | Project-specific configuration |

### Dynamic Columns

The following columns are added dynamically based on the allocation strategy:

#### Annotator Columns

For each annotator (1 to N, where N is the strategy's `num_annotators`):

| Column Name | Type | Description |
|-------------|------|-------------|
| annotator_X | Integer (FK) | Foreign key to project_users.id |
| annotator_X_review | JSONB | Review data for annotator X |

#### Verifier Column (When Verification Required)

For strategies that require verification (both sequential and parallel):

| Column Name | Type | Description |
|-------------|------|-------------|
| verifier | Integer (FK) | Foreign key to project_users.id |
| verifier_review | JSONB | Review data for verifier |

#### Auditor Columns

If the strategy requires auditors (when `requires_audit` is true and `audit_levels` > 0):

| Column Name | Type | Description |
|-------------|------|-------------|
| auditor_count | Integer | Number of auditors for this batch |
| auditor_X | Integer (FK) | Foreign key to project_users.id |
| auditor_X_review | JSONB | Review data for auditor X |

### Column Order

The columns are ordered as follows:

1. Base columns
2. Annotator columns (annotator_1, annotator_1_review, annotator_2, annotator_2_review, etc.)
3. Verifier columns (verifier, verifier_review) - when verification is required
4. Auditor columns (auditor_count, auditor_1, auditor_1_review, etc.) - if required
5. Custom configuration

### Foreign Keys

User columns (annotator_X, verifier, auditor_X) are foreign keys to the `project_users.id` column, ensuring referential integrity.

### JSONB Review Columns

Review columns (annotator_X_review, verifier_review, auditor_X_review) use the JSONB type to store structured review data, allowing for flexible review schemas.

## Example Schemas

### Sequential Strategy with 2 Annotators, No Auditors

```sql
CREATE TABLE allocation_batches (
    id SERIAL PRIMARY KEY,
    -- Base columns...
    annotator_count INTEGER NOT NULL DEFAULT 2,
    annotator_1 INTEGER REFERENCES project_users(id),
    annotator_1_review JSONB,
    annotator_2 INTEGER REFERENCES project_users(id),
    annotator_2_review JSONB,
    custom_batch_config JSONB
);
```

### Parallel Strategy with 2 Annotators, 1 Verifier, 1 Auditor

```sql
CREATE TABLE allocation_batches (
    id SERIAL PRIMARY KEY,
    -- Base columns...
    annotator_count INTEGER NOT NULL DEFAULT 2,
    annotator_1 INTEGER REFERENCES project_users(id),
    annotator_1_review JSONB,
    annotator_2 INTEGER REFERENCES project_users(id),
    annotator_2_review JSONB,
    verifier INTEGER REFERENCES project_users(id),
    verifier_review JSONB,
    auditor_count INTEGER NOT NULL DEFAULT 1,
    auditor_1 INTEGER REFERENCES project_users(id),
    auditor_1_review JSONB,
    custom_batch_config JSONB
);
```

## Usage Examples

### Creating a Project with Dynamic Schema

```python
# Get available allocation strategies
strategies = await get_allocation_strategies()

# Choose a strategy
strategy_id = strategies[0]["id"]

# Create a project with the chosen strategy
result = await provision_project_database("TEST_PROJECT", strategy_id=strategy_id)
```

### Assigning Users to Dynamic Roles

```python
# Get available roles for a project
roles = await get_available_roles("TEST_PROJECT")

# Add a user with a specific role
result = await add_user_to_project(
    project_code="TEST_PROJECT",
    user_id=123,
    username="john.doe",
    role="annotator_1"
)
```

### Assigning Annotators to Batches

```python
# Assign annotators to a batch
result = await assign_annotators_to_batch(
    project_code="TEST_PROJECT",
    batch_id=1,
    annotator_ids=[123, 456, 789]
)
```

## Benefits

1. **Flexibility**: Each project can have a schema tailored to its specific requirements
2. **Efficiency**: Only necessary tables and columns are created
3. **Type Safety**: All columns are properly typed and integrated with SQLAlchemy
4. **Maintainability**: Changes to allocation strategies automatically reflect in database schemas
