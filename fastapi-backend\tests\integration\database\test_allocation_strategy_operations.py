"""
Integration tests for Allocation Strategy database operations.
Tests strategy creation, management, and project relationships.
"""
import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from httpx import AsyncClient

from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.schemas.allocation_strategy_schemas import AllocationStrategyCreate, AllocationStrategyUpdate
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.strategy         # Feature marker - Strategy operations
@pytest.mark.regression       # Suite marker - CRUD testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestAllocationStrategyOperations:
    """REGRESSION TEST SUITE: Allocation strategy CRUD operations."""
    
    @pytest.mark.asyncio
    async def test_allocation_strategy_creation_and_retrieval(self, test_master_db: AsyncSession, setup_test_database):
        """Test creating and retrieving allocation strategies."""
        #   Create allocation strategy with factory (no hardcoding)
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=True,
            requires_ai_preprocessing=False,
            requires_audit=False,
            allocation_status="active",
            quality_requirements={"min_confidence": 0.8},
            configuration={"auto_assign": True}
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Verify strategy creation
        assert strategy.id is not None
        assert strategy.strategy_name.startswith("Test Strategy")  #   Dynamic verification
        assert strategy.strategy_type == StrategyType.SEQUENTIAL
        assert strategy.num_annotators == 1
        assert strategy.requires_verification is True
        assert strategy.requires_ai_preprocessing is False
        assert strategy.requires_audit is False
        assert strategy.allocation_status == "active"
        assert strategy.quality_requirements == {"min_confidence": 0.8}
        assert strategy.configuration == {"auto_assign": True}
        
        # Test strategy retrieval using dynamic name
        stmt = select(AllocationStrategies).where(
            AllocationStrategies.strategy_name == strategy.strategy_name
        )
        result = await test_master_db.execute(stmt)
        retrieved_strategy = result.scalar_one_or_none()
        
        assert retrieved_strategy is not None
        assert retrieved_strategy.id == strategy.id
        assert retrieved_strategy.strategy_name == strategy.strategy_name
    
    @pytest.mark.asyncio
    async def test_strategy_type_validation(self, test_master_db: AsyncSession, setup_test_database):
        """Test different strategy types and their configurations."""
        #   Test Multi-Annotator Strategy with factory
        multi_strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.PARALLEL,
            num_annotators=3,
            requires_verification=True,
            requires_ai_preprocessing=True,
            requires_audit=True,
            allocation_status="active",
            quality_requirements={"accuracy": 0.95, "speed": "high"},
            configuration={"parallel_mode": True, "max_concurrent": 3}
        )
        
        test_master_db.add(multi_strategy)
        await test_master_db.commit()
        await test_master_db.refresh(multi_strategy)
        
        assert multi_strategy.num_annotators == 3
        assert multi_strategy.strategy_type == StrategyType.PARALLEL
        assert multi_strategy.requires_audit is True
        assert multi_strategy.quality_requirements == {"accuracy": 0.95, "speed": "high"}
        assert multi_strategy.configuration == {"parallel_mode": True, "max_concurrent": 3}
        
        # Test Sequential Strategy
        sequential_strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=2,
            requires_verification=False,
            requires_ai_preprocessing=True,
            requires_audit=False,
            allocation_status="active",
            quality_requirements=None,
            configuration={"sequential_mode": True}
        )
        
        test_master_db.add(sequential_strategy)
        await test_master_db.commit()
        await test_master_db.refresh(sequential_strategy)
        
        assert sequential_strategy.strategy_type == StrategyType.SEQUENTIAL
        assert sequential_strategy.requires_verification is False
        assert sequential_strategy.requires_audit is False
        assert sequential_strategy.quality_requirements is None
        assert sequential_strategy.configuration == {"sequential_mode": True}
    
    @pytest.mark.asyncio
    async def test_strategy_additional_fields_validation(self, test_master_db: AsyncSession, setup_test_database):
        """Test validation of additional fields: requires_audit, quality_requirements, configuration."""
        # Test strategy with comprehensive configuration
        comprehensive_strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.PARALLEL,
            num_annotators=2,
            requires_verification=True,
            requires_ai_preprocessing=True,
            requires_audit=True,
            allocation_status="active",
            quality_requirements={
                "min_accuracy": 0.95,
                "max_time_per_file": 300,
                "required_certifications": ["advanced_annotation"],
                "review_threshold": 0.8
            },
            configuration={
                "auto_assignment": True,
                "priority_queue": True,
                "deadline_enforcement": True,
                "notification_settings": {
                    "email_on_assignment": True,
                    "slack_notifications": False
                }
            }
        )
        
        test_master_db.add(comprehensive_strategy)
        await test_master_db.commit()
        await test_master_db.refresh(comprehensive_strategy)
        
        # Verify all fields are correctly stored and retrieved
        assert comprehensive_strategy.id is not None
        assert comprehensive_strategy.requires_audit is True
        assert comprehensive_strategy.quality_requirements["min_accuracy"] == 0.95
        assert comprehensive_strategy.quality_requirements["required_certifications"] == ["advanced_annotation"]
        assert comprehensive_strategy.configuration["auto_assignment"] is True
        assert comprehensive_strategy.configuration["notification_settings"]["email_on_assignment"] is True
        
        # Test strategy with minimal configuration
        minimal_strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            # All other fields should use defaults
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(minimal_strategy)
        await test_master_db.commit()
        await test_master_db.refresh(minimal_strategy)
        
        # Verify default values
        assert minimal_strategy.requires_verification is False  # Default
        assert minimal_strategy.requires_ai_preprocessing is False  # Default
        assert minimal_strategy.requires_audit is False  # Default
        assert minimal_strategy.quality_requirements is None  # Default
        assert minimal_strategy.configuration is None  # Default
    
    @pytest.mark.asyncio
    async def test_strategy_project_assignment(self, test_master_db: AsyncSession, setup_test_database):
        """Test assigning strategies to projects."""
        # Create client first
        client = test_factory.projects.create_client(
            contact_info={"phone": "555-0100"}
        )
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create allocation strategy
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=False,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Create project with strategy
        project = test_factory.projects.create_project(
            client.id,
            strategy.id,
            project_code="STRAT_TEST_001",
            project_name="Strategy Test Project",
            project_type="image",
            project_status="active",
            priority_level=1
        )
        
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Verify strategy-project relationship
        assert project.allocation_strategy_id == strategy.id
        
        # Test retrieval with relationship
        stmt = select(ProjectsRegistry).where(
            ProjectsRegistry.allocation_strategy_id == strategy.id
        )
        result = await test_master_db.execute(stmt)
        projects_with_strategy = result.scalars().all()
        
        assert len(projects_with_strategy) == 1
        assert projects_with_strategy[0].project_code == "STRAT_TEST_001"
    
    @pytest.mark.asyncio
    async def test_strategy_update_operations(self, test_master_db: AsyncSession, setup_test_database):
        """Test updating allocation strategies."""
        # Create initial strategy
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=False,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        original_id = strategy.id
        
        # Update strategy
        strategy.num_annotators = 2
        strategy.requires_verification = True
        strategy.strategy_type = StrategyType.PARALLEL
        strategy.description = "Updated to multi-annotator workflow"
        
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Verify updates
        assert strategy.id == original_id  # ID should remain same
        assert strategy.num_annotators == 2
        assert strategy.requires_verification is True
        assert strategy.strategy_type == StrategyType.PARALLEL
        assert strategy.description == "Updated to multi-annotator workflow"
    
    @pytest.mark.asyncio
    async def test_strategy_deletion_with_existing_projects(self, test_master_db: AsyncSession, setup_test_database):
        """Test strategy deletion scenarios."""
        # Create client
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create strategy
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Create project using this strategy
        project = test_factory.projects.create_project(
            client.id,
            strategy.id,
            project_code="DEL_TEST_001",
            project_name="Deletion Test Project",
            project_type="image",
            project_status="active",
            priority_level=1
        )
        test_master_db.add(project)
        await test_master_db.commit()
        
        # Try to delete strategy (should handle foreign key constraint)
        strategy_id = strategy.id
        
        # First, remove strategy from project (in real scenario, this would be business logic)
        project.allocation_strategy_id = None
        await test_master_db.commit()
        
        # Now delete strategy
        await test_master_db.delete(strategy)
        await test_master_db.commit()
        
        # Verify strategy is deleted
        stmt = select(AllocationStrategies).where(AllocationStrategies.id == strategy_id)
        result = await test_master_db.execute(stmt)
        deleted_strategy = result.scalar_one_or_none()
        
        assert deleted_strategy is None
        
        # Verify project still exists but without strategy
        stmt = select(ProjectsRegistry).where(ProjectsRegistry.project_code == "DEL_TEST_001")
        result = await test_master_db.execute(stmt)
        existing_project = result.scalar_one_or_none()
        
        assert existing_project is not None
        assert existing_project.allocation_strategy_id is None
    
    @pytest.mark.asyncio
    async def test_strategy_status_management(self, test_master_db: AsyncSession, setup_test_database):
        """Test strategy status transitions."""
        # Create strategy with active status
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        assert strategy.allocation_status == "active"
        
        # Change to inactive
        strategy.allocation_status = "inactive"
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        assert strategy.allocation_status == "inactive"
        
        # Change to deprecated
        strategy.allocation_status = "deprecated"
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        assert strategy.allocation_status == "deprecated"
    
    @pytest.mark.asyncio
    async def test_dynamic_column_generation_scenario(self, test_master_db: AsyncSession, setup_test_database):
        """Test scenarios that would affect dynamic column generation."""
        # Create multi-annotator strategy
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.PARALLEL,
            num_annotators=3,
            requires_verification=True,
            requires_ai_preprocessing=True,
            allocation_status="active",
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # This strategy would generate columns: annotator_1, annotator_2, annotator_3, verifier, ai_processor
        assert strategy.num_annotators == 3
        assert strategy.requires_verification is True
        assert strategy.requires_ai_preprocessing is True
        
        # Test configuration that affects column generation
        expected_roles = ["annotator_1", "annotator_2", "annotator_3"]
        if strategy.requires_verification:
            expected_roles.append("verifier")
        if strategy.requires_ai_preprocessing:
            expected_roles.append("ai_processor")
        
        # In a real implementation, this would be used to create dynamic columns
        assert len(expected_roles) == 5  # 3 annotators + 1 verifier + 1 ai_processor


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.strategy         # Feature marker - Strategy operations
@pytest.mark.api              # Feature marker - API operations
@pytest.mark.regression       # Suite marker - API testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestAllocationStrategyAPI:
    """REGRESSION TEST SUITE: Allocation strategy API operations."""
    
    @pytest.mark.asyncio
    async def test_create_strategy_api(self, authenticated_client: AsyncClient, test_master_db: AsyncSession, setup_test_database):
        """Test strategy creation through API."""
        strategy_data = {
            "strategy_name": "API Test Strategy",
            "strategy_type": "sequential",
            "num_annotators": 1,
            "requires_verification": True,
            "requires_ai_preprocessing": False,
            "requires_audit": True,
            "allocation_status": "active",
            "description": "Strategy created via API",
            "quality_requirements": {"min_score": 0.9},
            "configuration": {"api_created": True}
        }
        
        response = await authenticated_client.post("/allocation-strategies", json=strategy_data)
        
        # Handle different possible response codes
        if response.status_code in [200, 201]:
            result = response.json()
            assert result["strategy_name"] == "API Test Strategy"
            assert result["num_annotators"] == 1
            assert result["requires_audit"] is True
            assert result["quality_requirements"] == {"min_score": 0.9}
            assert result["configuration"] == {"api_created": True}
            
            # Verify in database
            stmt = select(AllocationStrategies).where(
                AllocationStrategies.strategy_name == "API Test Strategy"
            )
            db_result = await test_master_db.execute(stmt)
            created_strategy = db_result.scalar_one_or_none()
            
            assert created_strategy is not None
            assert created_strategy.strategy_name == "API Test Strategy"
    
    @pytest.mark.asyncio
    async def test_list_strategies_api(self, authenticated_client: AsyncClient, test_master_db: AsyncSession, setup_test_database):
        """Test listing strategies through API."""
        # Create test strategies
        strategies = [
            test_factory.projects.create_allocation_strategy(
                strategy_type=StrategyType.SEQUENTIAL,
                num_annotators=1,
                allocation_status="active",
                requires_ai_preprocessing=False,
                requires_audit=False,
                quality_requirements=None,
                configuration=None
            ) for i in range(1, 4)
        ]
        
        for strategy in strategies:
            test_master_db.add(strategy)
        await test_master_db.commit()
        
        # Test API listing
        response = await authenticated_client.get("/allocation-strategies")
        
        if response.status_code == 200:
            result = response.json()
            assert isinstance(result, list)
            
            # Check if our test strategies are in the result
            strategy_names = [s["strategy_name"] for s in result]
            for i in range(1, 4):
                assert f"List Test Strategy {i}" in strategy_names
    
    @pytest.mark.asyncio
    async def test_update_strategy_api(self, authenticated_client: AsyncClient, test_master_db: AsyncSession, setup_test_database):
        """Test updating strategy through API."""
        # Create initial strategy
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Update via API
        update_data = {
            "num_annotators": 2,
            "requires_verification": True,
            "requires_audit": True,
            "description": "Updated via API",
            "quality_requirements": {"updated_score": 0.95},
            "configuration": {"updated_via_api": True}
        }
        
        response = await authenticated_client.patch(f"/allocation-strategies/{strategy.id}", json=update_data)
        
        if response.status_code == 200:
            result = response.json()
            assert result["num_annotators"] == 2
            assert result["requires_verification"] is True
            assert result["requires_audit"] is True
            assert result["quality_requirements"] == {"updated_score": 0.95}
            assert result["configuration"] == {"updated_via_api": True}
            
            # Verify in database
            await test_master_db.refresh(strategy)
            assert strategy.num_annotators == 2
            assert strategy.requires_verification is True
            assert strategy.requires_audit is True
            assert strategy.quality_requirements == {"updated_score": 0.95}
            assert strategy.configuration == {"updated_via_api": True}
    
    @pytest.mark.asyncio
    async def test_delete_strategy_api(self, authenticated_client: AsyncClient, test_master_db: AsyncSession, setup_test_database):
        """Test deleting strategy through API."""
        # Create strategy to delete
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        strategy_id = strategy.id
        
        # Delete via API
        response = await authenticated_client.delete(f"/allocation-strategies/{strategy_id}")
        
        if response.status_code in [200, 204]:
            # Verify deletion in database
            stmt = select(AllocationStrategies).where(AllocationStrategies.id == strategy_id)
            result = await test_master_db.execute(stmt)
            deleted_strategy = result.scalar_one_or_none()
            
            assert deleted_strategy is None


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.strategy         # Feature marker - Strategy operations
@pytest.mark.regression       # Suite marker - Error scenarios
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestAllocationStrategyErrorHandling:
    """REGRESSION TEST SUITE: Allocation strategy error scenarios."""
    
    @pytest.mark.asyncio
    async def test_duplicate_strategy_name_prevention(self, test_master_db: AsyncSession, setup_test_database):
        """Test that duplicate strategy names are prevented."""
        # Create first strategy
        strategy1 = test_factory.projects.create_allocation_strategy(
            strategy_name="Duplicate Test Strategy",
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy1)
        await test_master_db.commit()
        
        # Try to create second strategy with same name
        strategy2 = test_factory.projects.create_allocation_strategy(
            strategy_name="Duplicate Test Strategy",  # Same name
            strategy_type=StrategyType.PARALLEL,
            num_annotators=2,
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy2)
        
        # Should raise integrity error
        with pytest.raises(Exception):  # SQLAlchemy IntegrityError
            await test_master_db.commit()
        
        await test_master_db.rollback()
    
    @pytest.mark.asyncio
    async def test_invalid_num_annotators(self, test_master_db: AsyncSession, setup_test_database):
        """Test validation of num_annotators field."""
        # Test that num_annotators can be 0 (no database constraint currently exists)
        # This test documents current behavior - if validation is added later, update this test
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_name="Zero Annotators Strategy",
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=0,  # Currently allowed by database
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Verify that 0 is indeed stored
        assert strategy.num_annotators == 0
        assert strategy.id is not None
    
    @pytest.mark.asyncio
    async def test_strategy_type_consistency(self, test_master_db: AsyncSession, setup_test_database):
        """Test strategy type and num_annotators consistency."""
        # Single annotator should have num_annotators = 1
        strategy = test_factory.projects.create_allocation_strategy(
            strategy_name="Consistency Test Strategy",
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=3,  # Inconsistent with SINGLE_ANNOTATOR type
            allocation_status="active",
            requires_ai_preprocessing=False,
            requires_audit=False,
            quality_requirements=None,
            configuration=None
        )
        
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # This should be validated at business logic level, not database level
        # Database allows it, but business logic should validate consistency
        assert strategy.strategy_type == StrategyType.SEQUENTIAL
        assert strategy.num_annotators == 3  # Database stored it, but it's inconsistent


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.strategy         # Feature marker - Strategy operations
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Performance tests take time
class TestAllocationStrategyPerformanceWithBulkData:
    """PERFORMANCE TEST SUITE: Allocation strategy with bulk data."""
    
    @pytest.mark.asyncio
    async def test_strategy_performance_with_bulk_data(self, test_master_db: AsyncSession):
        """Test allocation strategy operations with realistic bulk data volumes.
        
        This test uses test_data_manager.py to create a large dataset and then
        tests performance of common operations.
        
        SETUP:
        Run this before the test: ./scripts/setup_test_environments.sh performance
        """
        print("\n⚡ Testing allocation strategy performance with bulk data...")
        
        # Test 1: Query performance with many strategies
        import time
        start_time = time.time()
        
        stmt = select(AllocationStrategies).where(
            AllocationStrategies.allocation_status == "active"
        ).order_by(AllocationStrategies.strategy_name).limit(20)
        
        result = await test_master_db.execute(stmt)
        active_strategies = result.scalars().all()
        
        query_time = time.time() - start_time
        
        print(f"   📊 Found {len(active_strategies)} active strategies in {query_time:.4f}s")
        assert query_time < 1.0, f"Strategy query too slow: {query_time}s"
        
        # Test 2: Join performance with projects
        start_time = time.time()
        
        stmt = select(
            AllocationStrategies.strategy_name,
            func.count(ProjectsRegistry.id).label('project_count')
        ).outerjoin(
            ProjectsRegistry, AllocationStrategies.id == ProjectsRegistry.allocation_strategy_id
        ).group_by(
            AllocationStrategies.id, AllocationStrategies.strategy_name
        ).having(
            func.count(ProjectsRegistry.id) > 0
        ).order_by(
            func.count(ProjectsRegistry.id).desc()
        )
        
        result = await test_master_db.execute(stmt)
        strategy_usage = result.all()
        
        join_time = time.time() - start_time
        
        print(f"   📊 Strategy usage analysis: {len(strategy_usage)} strategies in {join_time:.4f}s")
        for usage in strategy_usage[:3]:  # Show top 3
            print(f"      {usage.strategy_name}: {usage.project_count} projects")
        
        assert join_time < 2.0, f"Strategy join query too slow: {join_time}s"
        assert len(strategy_usage) > 0, "No strategies with projects found"
    
    @pytest.mark.asyncio
    async def test_strategy_aggregation_performance(self, test_master_db: AsyncSession):
        """Test strategy aggregation performance with bulk data."""
        print("\n📊 Testing strategy aggregation performance...")
        
        import time
        start_time = time.time()
        
        # Complex aggregation query
        stmt = select(
            AllocationStrategies.strategy_type,
            AllocationStrategies.requires_verification,
            func.count().label('count'),
            func.avg(AllocationStrategies.num_annotators).label('avg_annotators'),
            func.bool_and(AllocationStrategies.requires_ai_preprocessing).label('all_need_ai')
        ).group_by(
            AllocationStrategies.strategy_type,
            AllocationStrategies.requires_verification
        ).order_by(
            AllocationStrategies.strategy_type
        )
        
        result = await test_master_db.execute(stmt)
        aggregations = result.all()
        
        agg_time = time.time() - start_time
        
        print(f"   📊 Strategy aggregations: {len(aggregations)} groups in {agg_time:.4f}s")
        for agg in aggregations:
            print(f"      {agg.strategy_type.value}, Verification: {agg.requires_verification}: {agg.count} strategies")
        
        assert agg_time < 1.5, f"Aggregation query too slow: {agg_time}s"
        assert len(aggregations) > 0, "No aggregation results found"
    
    @pytest.mark.asyncio
    async def test_bulk_data_consistency_check(self, test_master_db: AsyncSession):
        """Verify data consistency in bulk-populated allocation strategies."""
        print("\n🔍 Testing bulk data consistency for allocation strategies...")
        
        # Check for orphaned projects (projects referencing non-existent strategies)
        orphaned_projects_stmt = select(func.count()).select_from(
            ProjectsRegistry
        ).outerjoin(
            AllocationStrategies, ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id
        ).where(
            ProjectsRegistry.allocation_strategy_id.is_not(None),
            AllocationStrategies.id.is_(None)
        )
        
        orphaned_count = await test_master_db.execute(orphaned_projects_stmt)
        orphan_result = orphaned_count.scalar()
        
        print(f"   📊 Orphaned project references: {orphan_result}")
        assert orphan_result == 0, f"Found {orphan_result} orphaned project references"
        
        # Check strategy type distribution
        type_distribution_stmt = select(
            AllocationStrategies.strategy_type,
            func.count().label('count')
        ).group_by(AllocationStrategies.strategy_type)
        
        type_result = await test_master_db.execute(type_distribution_stmt)
        type_distribution = type_result.all()
        
        print(f"   📊 Strategy type distribution:")
        total_strategies = sum(row.count for row in type_distribution)
        for row in type_distribution:
            percentage = (row.count / total_strategies) * 100 if total_strategies > 0 else 0
            print(f"      {row.strategy_type.value}: {row.count} ({percentage:.1f}%)")
        
        assert len(type_distribution) >= 2, "Expected variety in strategy types"
        assert total_strategies > 0, "No strategies found in database"


# Utility function to work with bulk data scenarios
async def setup_bulk_test_scenario(scenario_name: str = "performance_test"):
    """Utility function to setup bulk test data for performance testing.
    
    This can be called from test fixtures or individual tests.
    """
    try:
        # Import here to avoid dependency issues if test_data_manager isn't available
        import sys
        import os
        sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..")))
        from test_data_manager import DatabaseTestDataManager
        
        async with DatabaseTestDataManager() as manager:
            result = await manager.create_specific_test_scenario(scenario_name)
            return result
    except ImportError:
        pytest.skip(f"test_data_manager not available - skipping bulk data setup")
    except Exception as e:
        pytest.fail(f"Failed to setup bulk test scenario: {e}")


# Example fixture for tests that need bulk data
@pytest.fixture(scope="class")
async def bulk_test_environment():
    """Fixture that sets up bulk test environment for performance tests.
    
    Usage in test classes:
    @pytest.mark.usefixtures("bulk_test_environment")
    class TestWithBulkData:
        # Your tests here
    """
    # Setup
    result = await setup_bulk_test_scenario("core_test")
    yield result
    
    # Cleanup
    try:
        from test_data_manager import DatabaseTestDataManager
        async with DatabaseTestDataManager() as manager:
            await manager.cleanup_all_test_data()
    except Exception as e:
        print(f"Warning: Cleanup failed: {e}")


# Performance measurement decorator for allocation strategy tests
def measure_allocation_strategy_performance(operation_name: str):
    """Decorator to measure performance of allocation strategy operations."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            result = await func(*args, **kwargs)
            end_time = time.time()
            
            print(f"⚡ {operation_name}: {end_time - start_time:.4f}s")
            return result
        return wrapper
    return decorator


# Example usage of the performance decorator
@measure_allocation_strategy_performance("Strategy Creation with Dependencies")
async def create_strategy_with_full_dependencies(test_master_db: AsyncSession):
    """Example function showing how to measure performance of complex operations."""
    # Create client
    client = test_factory.projects.create_client()
    test_master_db.add(client)
    await test_master_db.commit()
    await test_master_db.refresh(client)
    
    # Create strategy
    strategy = test_factory.projects.create_allocation_strategy()
    test_master_db.add(strategy)
    await test_master_db.commit()
    await test_master_db.refresh(strategy)
    
    # Create project with strategy
    project = test_factory.projects.create_project(client.id, strategy.id)
    test_master_db.add(project)
    await test_master_db.commit()
    
    return {"client": client, "strategy": strategy, "project": project}
