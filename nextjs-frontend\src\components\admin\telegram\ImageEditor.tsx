"use client";

import { useState, useEffect, useCallback } from "react";
import {
  FaSync,
  FaArrowLeft,
  FaEdit,
  FaUndo,
  FaCrop,
  FaPlus,
  FaList,
  FaInfoCircle,
  FaTimes,
  FaDownload,
  FaCloudUploadAlt,
} from "react-icons/fa";
import ReactCrop, { Crop, PixelCrop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import {
  BASE_URL,
  TelegramImage,
  CropSelection,
  showStatus as utilShowStatus,
} from "./types";
import Image from "next/image";

interface ImageEditorProps {
  isOpen: boolean;
  image: TelegramImage | null;
  channelTitle: string;
  onClose: () => void;
  onUploadSuccess: (results: unknown) => void;
  onUploadError: (error: string) => void;
}

// Helper function to get cropped image
const getCroppedImg = (image: HTMLImageElement, crop: PixelCrop): string => {
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    throw new Error("Could not get canvas context");
  }

  canvas.width = crop.width;
  canvas.height = crop.height;

  ctx.drawImage(
    image,
    crop.x,
    crop.y,
    crop.width,
    crop.height,
    0,
    0,
    crop.width,
    crop.height
  );

  return canvas.toDataURL("image/jpeg");
};

export default function ImageEditor({
  isOpen,
  image,
  channelTitle,
  onClose,
  onUploadSuccess,
  onUploadError,
}: ImageEditorProps) {
  const [editorImage, setEditorImage] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(0.5);
  const [minZoom, setMinZoom] = useState(0.1);
  const [imgRef, setImgRef] = useState<HTMLImageElement | null>(null);
  const [cropSelections, setCropSelections] = useState<CropSelection[]>([]);
  const [modalUploadMenuOpen, setModalUploadMenuOpen] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [isPanMode, setIsPanMode] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{
    message: string;
    type: "success" | "danger" | "warning" | "info";
  } | null>(null);

  const showStatus = (
    msg: string,
    type: "success" | "danger" | "warning" | "info" = "success",
    duration = 3000
  ) => {
    utilShowStatus(setStatusMessage, msg, type, duration);
  };

  // Calculate proper zoom to fit image in container
  const calculateFitZoom = useCallback(() => {
    if (!imgRef) return 0.5;

    const containerWidth = 320;
    const containerHeight = 320;
    const imageWidth = imgRef.naturalWidth;
    const imageHeight = imgRef.naturalHeight;

    if (!imageWidth || !imageHeight) return 0.5;

    const paddedContainerWidth = containerWidth - 20;
    const paddedContainerHeight = containerHeight - 20;

    const scaleX = paddedContainerWidth / imageWidth;
    const scaleY = paddedContainerHeight / imageHeight;
    const fitZoom = Math.min(scaleX, scaleY);

    return Math.max(fitZoom, 0.1);
  }, [imgRef]);

  const getZoomRange = useCallback(() => {
    const fitZoom = calculateFitZoom();
    const minZoom = Math.max(fitZoom * 0.6, 0.05);
    const maxZoom = Math.min(fitZoom * 2, 1.5);
    return { minZoom, fitZoom, maxZoom };
  }, [calculateFitZoom]);

  const handleImageLoad = useCallback(() => {
    if (imgRef) {
      const { minZoom, fitZoom } = getZoomRange();
      setMinZoom(minZoom);
      setZoom(fitZoom);
      setImagePosition({ x: 0, y: 0 });
    }
  }, [imgRef, getZoomRange]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!isPanMode) return;
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
      setLastMousePos({ x: e.clientX, y: e.clientY });
    },
    [isPanMode]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!isDragging || !isPanMode) return;
      e.preventDefault();
      e.stopPropagation();

      const deltaX = e.clientX - lastMousePos.x;
      const deltaY = e.clientY - lastMousePos.y;

      setImagePosition((prev) => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY,
      }));

      setLastMousePos({ x: e.clientX, y: e.clientY });
    },
    [isDragging, lastMousePos, isPanMode]
  );

  const handleMouseUp = useCallback(
    (e: React.MouseEvent) => {
      if (isPanMode) {
        e.preventDefault();
        e.stopPropagation();
      }
      setIsDragging(false);
    },
    [isPanMode]
  );

  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => setIsDragging(false);
      document.addEventListener("mouseup", handleGlobalMouseUp);
      return () => document.removeEventListener("mouseup", handleGlobalMouseUp);
    }
  }, [isDragging]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(".upload-dropdown")) {
        setModalUploadMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Initialize editor when image changes
  useEffect(() => {
    if (image && isOpen) {
      setEditorImage(`data:image/jpeg;base64,${image.image}`);
      setCropSelections([]);
      setCompletedCrop(undefined);
      setRotation(0);
      setZoom(0.5);
      setMinZoom(0.1);
      setCrop(undefined);
      setImagePosition({ x: 0, y: 0 });
      setIsDragging(false);
      setIsPanMode(false);
    }
  }, [image, isOpen]);

  const rotateEditor = () => {
    setRotation((rotation + 90) % 360);
    showStatus("Image rotated 90°", "success");
  };

  const zoomIn = () => {
    const { maxZoom } = getZoomRange();
    const newZoom = Math.min(zoom + 0.1, maxZoom);
    setZoom(newZoom);
    showStatus(`Zoomed in: ${Math.round(newZoom * 100)}%`, "info", 1500);
  };

  const zoomOut = () => {
    const newZoom = Math.max(zoom - 0.1, minZoom);
    setZoom(newZoom);
    showStatus(`Zoomed out: ${Math.round(newZoom * 100)}%`, "info", 1500);
  };

  const resetZoom = () => {
    setZoom(1);
    setImagePosition({ x: 0, y: 0 });
    showStatus("Zoom reset to 100%", "info", 1500);
  };

  const fitToContainer = () => {
    const fitZoom = calculateFitZoom();
    setZoom(fitZoom);
    setImagePosition({ x: 0, y: 0 });
    showStatus(`Fit to container: ${Math.round(fitZoom * 100)}%`, "info", 1500);
  };

  const resetPosition = () => {
    setImagePosition({ x: 0, y: 0 });
    showStatus("Image position reset", "info", 1500);
  };

  const startCrop = () => {
    setCrop(undefined);
    setIsPanMode(false);
    showStatus("Click and drag on the image to create a crop area", "info");
  };

  const togglePanMode = () => {
    setIsPanMode(!isPanMode);
    setIsDragging(false);
    if (!isPanMode) {
      setCrop(undefined);
      showStatus("Pan mode enabled - click and drag to move the image", "info");
    } else {
      showStatus("Pan mode disabled", "info");
    }
  };

  const addCrop = async () => {
    if (!completedCrop || !imgRef) {
      showStatus("Create a crop area first", "warning");
      return;
    }
    try {
      const c = completedCrop;
      if (!c || c.width <= 0 || c.height <= 0) {
        showStatus("Select an area to crop first", "warning");
        return;
      }

      const preview = getCroppedImg(imgRef, c);
      setCropSelections((cs) => [
        ...cs,
        {
          x: c.x,
          y: c.y,
          width: c.width,
          height: c.height,
          preview,
        },
      ]);
      showStatus("Crop added", "success");
      setCrop(undefined);
    } catch (error) {
      console.error("Error adding crop:", error);
      showStatus("Error adding crop", "danger");
    }
  };

  const resetCrop = () => {
    setCrop(undefined);
    setCompletedCrop(undefined);
    setCropSelections([]);
    showStatus("Crops reset", "info");
  };

  const removeCrop = (idx: number) =>
    setCropSelections((cs) => cs.filter((_, i) => i !== idx));

  const getOriginalFilename = (img: TelegramImage): string => {
    if (img.caption) {
      const cleanCaption = img.caption
        .replace(/[^a-zA-Z0-9\s]/g, "")
        .trim()
        .substring(0, 30);
      if (cleanCaption) {
        return cleanCaption.replace(/\s+/g, "_");
      }
    }

    const date = img.date
      ? img.date.split(" ")[0].replace(/-/g, "")
      : "unknown";
    return `image_${img.id}_${date}`;
  };

  const saveCropsLocally = async () => {
    if (!completedCrop && cropSelections.length === 0) {
      showStatus("No crops to save", "warning");
      return;
    }

    try {
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      if (cropSelections.length > 0) {
        cropSelections.forEach((c, i) => {
          const b = c.preview.split(",")[1];
          zip.file(`crop_${i + 1}.jpg`, b, { base64: true });
        });
      } else if (completedCrop && imgRef) {
        try {
          const preview = getCroppedImg(imgRef, completedCrop);
          const b64 = preview.split(",")[1];
          zip.file("edited_image.jpg", b64, { base64: true });
        } catch (canvasError) {
          console.error("Canvas error:", canvasError);
          showStatus("Error processing image", "danger");
          return;
        }
      }

      const blob = await zip.generateAsync({ type: "blob" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `edited_${new Date().toISOString().slice(0, 10)}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      showStatus("Saved locally", "success");
      onClose();
    } catch (e) {
      console.error(e);
      showStatus("Save error", "danger");
    }
  };

  const uploadCropsToDrive = async () => {
    if (!completedCrop && cropSelections.length === 0) return;
    setIsUploading(true);

    try {
      let imgs: string[] = [];

      if (cropSelections.length > 0) {
        imgs = cropSelections.map((c) => c.preview.split(",")[1]);
      } else if (completedCrop && imgRef) {
        try {
          const preview = getCroppedImg(imgRef, completedCrop);
          imgs = [preview.split(",")[1]];
        } catch (canvasError) {
          console.error("Canvas error:", canvasError);
          showStatus("Error processing image", "danger");
          return;
        }
      }

      const originalFilename = image
        ? getOriginalFilename(image)
        : "cropped_image";

      const res = await fetch(`${BASE_URL}/telegram/upload-to-drive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          channel_name: channelTitle,
          images: imgs.map(() => ({
            image_data: imgs[0],
            type: "base64",
            original_filename: originalFilename,
          })),
        }),
      });

      const json = await res.json();
      if (res.ok) {
        onUploadSuccess(json);
      } else {
        onUploadError(json.detail || json.error || "Upload failed");
      }
    } catch (e) {
      console.error(e);
      if (e instanceof Error) {
        onUploadError(e.message);
      } else {
        onUploadError("Upload failed");
      }
    } finally {
      setIsUploading(false);
      onClose();
    }
  };

  if (!isOpen || !image) return null;

  // Estimate dimensions for Next.js Image (use defaults if unavailable)
  const defaultWidth = 320;
  const defaultHeight = 320;

  return (
    <>
      {/* Status Alert */}
      {statusMessage && (
        <div
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 text-white px-4 py-2 rounded shadow z-50 ${
            statusMessage.type === "success"
              ? "bg-green-600"
              : statusMessage.type === "danger"
              ? "bg-red-600"
              : statusMessage.type === "warning"
              ? "bg-yellow-600"
              : "bg-blue-600"
          }`}
          style={{ maxWidth: "90%" }}
        >
          {statusMessage.message}
        </div>
      )}

      {/* Image Editor Modal */}
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-lg max-w-6xl w-full max-h-[90vh] mx-4 flex flex-col">
          <div className="flex justify-between items-center border-b px-6 py-4">
            <h5 className="text-lg font-semibold">Image Editor</h5>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <FaTimes />
            </button>
          </div>

          <div className="p-6 flex-1 overflow-hidden">
            {/* Two Column Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
              {/* Left Column - Image Area */}
              <div className="lg:col-span-3">
                <div className="relative w-full h-[500px] bg-gray-100 rounded-lg overflow-hidden border">
                  <div
                    className="w-full h-full"
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    style={{
                      cursor: isPanMode
                        ? isDragging
                          ? "grabbing"
                          : "grab"
                        : "default",
                    }}
                  >
                    <ReactCrop
                      crop={isPanMode ? undefined : crop}
                      onChange={(newCrop) => !isPanMode && setCrop(newCrop)}
                      onComplete={(c) => !isPanMode && setCompletedCrop(c)}
                      style={{ width: "100%", height: "100%" }}
                      disabled={isPanMode}
                    >
                      {editorImage ? (
                        <Image
                          ref={(ref: HTMLImageElement | null) => {
                            setImgRef(ref);
                            if (ref) {
                              ref.onload = handleImageLoad;
                            }
                          }}
                          src={editorImage}
                          alt="Crop editor"
                          width={defaultWidth}
                          height={defaultHeight}
                          style={{
                            maxWidth: "none",
                            maxHeight: "none",
                            width: `${zoom * 100}%`,
                            height: "auto",
                            display: "block",
                            margin: "0 auto",
                            transform: `translate(${imagePosition.x}px, ${imagePosition.y}px) rotate(${rotation}deg)`,
                            transformOrigin: "center",
                            userSelect: "none",
                            pointerEvents: isPanMode ? "none" : "auto",
                          }}
                          draggable={false}
                          unoptimized
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-500">
                          Loading image...
                        </div>
                      )}
                    </ReactCrop>
                  </div>
                </div>

                {/* Zoom and Rotation Controls */}
                <div className="mt-3 space-y-2">
                  <div className="flex items-center space-x-3">
                    <label className="text-xs font-medium text-gray-700 w-12">
                      Zoom:
                    </label>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={zoomOut}
                        className="border border-gray-300 px-1.5 py-0.5 rounded hover:bg-gray-50 text-gray-600 text-xs"
                        disabled={zoom <= minZoom}
                      >
                        -
                      </button>
                      <input
                        type="range"
                        min={minZoom}
                        max={getZoomRange().maxZoom}
                        step={0.02}
                        value={zoom}
                        onChange={(e) => setZoom(Number(e.target.value))}
                        className="flex-1"
                      />
                      <button
                        onClick={zoomIn}
                        className="border border-gray-300 px-1.5 py-0.5 rounded hover:bg-gray-50 text-gray-600 text-xs"
                        disabled={zoom >= getZoomRange().maxZoom}
                      >
                        +
                      </button>
                      <button
                        onClick={fitToContainer}
                        className="border border-gray-300 px-1.5 py-0.5 rounded hover:bg-gray-50 text-gray-600 text-xs"
                        title="Fit entire image in container"
                      >
                        Fit
                      </button>
                      <button
                        onClick={resetZoom}
                        className="border border-gray-300 px-1.5 py-0.5 rounded hover:bg-gray-50 text-gray-600 text-xs"
                      >
                        100%
                      </button>
                    </div>
                    <span className="text-xs text-gray-500 w-12">
                      {Math.round(zoom * 100)}%
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <label className="text-xs font-medium text-gray-700 w-12">
                      Rotation:
                    </label>
                    <input
                      type="range"
                      min={-180}
                      max={180}
                      step={1}
                      value={rotation}
                      onChange={(e) => setRotation(Number(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-xs text-gray-500 w-12">
                      {rotation}°
                    </span>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="mt-3 space-y-1.5">
                  <button
                    onClick={fitToContainer}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded hover:bg-gray-50 text-gray-600"
                  >
                    <FaEdit className="inline-block mr-1" />
                    Fit to View
                  </button>
                  <button
                    onClick={resetPosition}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded hover:bg-gray-50 text-gray-600"
                  >
                    <FaArrowLeft className="inline-block mr-1" />
                    Reset Position
                  </button>
                  <button
                    onClick={resetCrop}
                    className="w-full px-2 py-1.5 text-xs border border-gray-300 rounded hover:bg-gray-50 text-gray-600"
                  >
                    <FaUndo className="inline-block mr-1" />
                    Reset All
                  </button>
                </div>
              </div>

              {/* Right Column - Controls and Crop Selections */}
              <div className="lg:col-span-1 flex flex-col">
                {/* Action Buttons */}
                <div className="space-y-2">
                  <div className="flex space-x-1.5">
                    <button
                      onClick={rotateEditor}
                      className="flex-1 flex items-center justify-center px-2 py-2 border border-gray-300 rounded hover:bg-gray-50 text-gray-700 text-xs"
                    >
                      <FaSync className="mr-1" />
                      Rotate 90°
                    </button>

                    <button
                      onClick={togglePanMode}
                      className={`flex-1 flex items-center justify-center px-2 py-2 border rounded text-xs transition-colors duration-200 ${
                        isPanMode
                          ? "border-orange-500 bg-orange-500 text-white"
                          : "border-orange-500 text-orange-600 hover:bg-orange-50"
                      }`}
                    >
                      <FaArrowLeft className="mr-1" />
                      Pan
                    </button>
                  </div>

                  <button
                    onClick={startCrop}
                    disabled={isPanMode}
                    className={`w-full flex items-center justify-center px-2 py-2 border rounded text-xs transition-colors duration-200 ${
                      isPanMode
                        ? "border-gray-300 text-gray-400 cursor-not-allowed"
                        : "border-[#0D47A1] text-[#0D47A1] hover:bg-[#0D47A1] hover:text-white"
                    }`}
                  >
                    <FaCrop className="mr-1" />
                    Crop
                  </button>

                  <button
                    onClick={addCrop}
                    disabled={!completedCrop || isPanMode}
                    className={`w-full flex items-center justify-center px-2 py-2 rounded text-xs ${
                      !completedCrop || isPanMode
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-green-500 text-white hover:bg-green-600"
                    }`}
                  >
                    <FaPlus className="mr-1" />
                    Add
                  </button>
                </div>

                {/* Crop Selections Area */}
                <div className="mt-4 flex-1">
                  <div className="bg-[#0D47A1] text-white px-3 py-1.5 rounded-t-lg">
                    <h6 className="text-sm font-medium flex items-center">
                      <FaList className="mr-1" />
                      Crop Selections
                    </h6>
                  </div>
                  <div className="border border-gray-200 rounded-b-lg p-3 bg-gray-50 min-h-[180px]">
                    {cropSelections.length > 0 ? (
                      <div className="space-y-2">
                        {cropSelections.map((c, idx) => (
                          <div
                            key={idx}
                            className="relative bg-white rounded p-2 border"
                          >
                            <div className="flex items-center space-x-2">
                              <Image
                                src={c.preview}
                                alt={`Crop ${idx + 1}`}
                                width={48}
                                height={48}
                                className="h-12 w-12 object-cover rounded border"
                                unoptimized
                              />
                              <div className="flex-1">
                                <p className="text-xs font-medium text-gray-700">
                                  Crop {idx + 1}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {Math.round(c.width)} × {Math.round(c.height)}
                                </p>
                              </div>
                              <button
                                onClick={() => removeCrop(idx)}
                                className="text-red-500 hover:text-red-700 p-0.5"
                              >
                                <FaTimes className="h-3 w-3" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-6">
                        <FaInfoCircle className="mx-auto h-6 w-6 mb-1 text-gray-400" />
                        <p className="text-xs">No crops selected yet.</p>
                        <p className="text-xs">
                          Use the crop tool to select part of the image.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 border-t px-6 py-4 bg-gray-50">
            <button
              onClick={onClose}
              className="px-6 py-2.5 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-200 transition-all duration-200 font-medium shadow-sm"
            >
              <FaTimes className="inline-block mr-2" />
              Cancel
            </button>
            <button
              onClick={saveCropsLocally}
              className="px-6 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-200 transition-all duration-200 font-medium shadow-sm hover:shadow-md"
            >
              <FaDownload className="inline-block mr-2" />
              Save Locally
            </button>
            <div className="relative upload-dropdown">
              <button
                onClick={() => setModalUploadMenuOpen(!modalUploadMenuOpen)}
                disabled={
                  isUploading || (cropSelections.length === 0 && !completedCrop)
                }
                className={`px-6 py-2.5 rounded-lg focus:outline-none focus:ring-2 transition-all duration-200 font-medium shadow-sm hover:shadow-md ${
                  isUploading || (cropSelections.length === 0 && !completedCrop)
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : "bg-green-500 text-white hover:bg-green-600"
                }`}
              >
                <FaCloudUploadAlt className="inline-block mr-1" />
                {isUploading ? "Uploading..." : "Upload"}
              </button>
              {modalUploadMenuOpen && (
                <div className="absolute right-0 bottom-full mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-xl z-50">
                  <button
                    onClick={() => {
                      uploadCropsToDrive();
                      setModalUploadMenuOpen(false);
                    }}
                    className="w-full text-left px-4 py-2 rounded-t-lg hover:bg-gray-100"
                  >
                    Google Drive
                  </button>
                  <button
                    disabled
                    className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed"
                  >
                    AWS (Coming Soon)
                  </button>
                  <button
                    disabled
                    className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed rounded-b-lg"
                  >
                    Azure (Coming Soon)
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
