# Redis VM Setup & UI Access – Session Summary

This document captures the key points, commands, and files exchanged during our conversation about connecting to a Redis instance running on a VM (`***********`) and exposing a web UI for it.

---

## 1. Checking Redis Configuration on the VM

1. **SSH into the VM**

   ```bash
   ssh <username>@***********
   ```

2. **Locate the `redis.conf` file**

   ```bash
   sudo find / -name redis.conf
   ```

3. **Verify critical directives**

   ```bash
   sudo cat /path/to/redis.conf | grep -E "^bind |^protected-mode |^requirepass "
   ```

   • `bind` should allow external access (`0.0.0.0` or `***********`).  
   • `protected-mode` should be `no` if you do **not** use a password.  
   • `requirepass` should be commented out or set appropriately if you use a password.

4. **Restart Redis after changes**

   ```bash
   sudo systemctl restart redis      # or: sudo service redis-server restart
   ```

5. **Quick connectivity test on the VM**

   ```bash
   redis-cli -h 127.0.0.1 ping
   # Expected output: PONG
   ```

---

## 2. Local Connection Test Script

A Python file `fastapi-backend/test_redis_connection.py` was created with the following features:

• Reads host, port, and (optional) password from environment variables (`REDIS_HOST`, `REDIS_PORT`, `REDIS_PASSWORD`).  
• Connects to Redis, issues a `PING`, performs a simple `SET` / `GET`, and prints troubleshooting hints on failure.

Run it from the project root:

```bash
export REDIS_HOST=***********
export REDIS_PORT=6379
# export REDIS_PASSWORD=<password>   # Only if you have one
cd fastapi-backend
python test_redis_connection.py
```

---

## 3. Installing a Web UI (Redis Commander)

Redis lacks a native web UI, so we used **Redis Commander**.

1. **Install prerequisites on the VM**

   ```bash
   sudo apt-get update
   sudo apt-get install -y nodejs npm
   sudo npm install -g redis-commander
   ```

2. **Open the firewall port**

   ```bash
   sudo ufw allow 8081
   ```

3. **Run Redis Commander manually (quick test)**

   ```bash
   sudo redis-commander --redis-host 127.0.0.1 --host 0.0.0.0 --port 8081
   ```

   • Without a Redis password, omit the `--redis-password` flag.  
   • Accessible from a browser at:  
     `http://***********:8081`

---

## 4. Creating a `systemd` Service for Redis Commander

To keep the UI running persistently:

1. **Create service file**

   ```bash
   sudo nano /etc/systemd/system/redis-commander.service
   ```

   ```ini
   [Unit]
   Description=Redis Commander web UI
   After=network.target redis.service

   [Service]
   Type=simple
   ExecStart=/usr/local/bin/redis-commander \
            --redis-host 127.0.0.1 \
            --host 0.0.0.0 \
            --port 8081
   Restart=always
   User=root
   Group=root

   [Install]
   WantedBy=multi-user.target
   ```

2. **Enable and start the service**

   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable redis-commander
   sudo systemctl start redis-commander
   ```

3. **Verify status**

   ```bash
   sudo systemctl status redis-commander
   # Look for "active (running)"
   ```

The UI remains available at `http://***********:8081` after reboots.

---

## 5. Troubleshooting Highlights

• **Permission error (`EACCES`)** – caused by running Redis Commander (installed as root) without `sudo`.  
  → Solution: run with `sudo` or configure a `systemd` service that runs as `root`.

• **Connection failures from Python** – checklist displayed in `test_redis_connection.py` covers:
  1. Redis server running?
  2. `bind` and `protected-mode` settings?
  3. Firewall/port 6379 open?
  4. Correct password (if any)?

---

## 6. Key Files Added/Modified

| File | Purpose |
|------|---------|
| `fastapi-backend/test_redis_connection.py` | Script to validate external connectivity to Redis |
| `/etc/systemd/system/redis-commander.service` (on VM) | Keeps Redis Commander running under `systemd` |

---

## 7. Next Steps

1. Commit the new Python script to version control if desired.  
2. Monitor the `systemd` service logs for Redis Commander:

   ```bash
   sudo journalctl -u redis-commander -f
   ```

3. Optionally secure Redis access with a password or configure a reverse proxy/TLS in front of Redis Commander for production use.

---

**End of session summary**