"use client";

import React, { useState } from 'react';
import { FaRobot, <PERSON><PERSON><PERSON><PERSON>, FaCheck } from 'react-icons/fa';

interface AISuggestionDialogProps {
  formConfig: Array<{
    field_name: string;
    label: string;
    field_type: string;
    required: boolean;
  }>;
  onApplySuggestion: (fieldName: string, value: any) => void;
  currentAISuggestions?: Record<string, any> | null;
}

export default function AISuggestionDialog({
  formConfig,
  onApplySuggestion,
  currentAISuggestions
}: AISuggestionDialogProps) {
  const [suggestions, setSuggestions] = useState<Record<string, string>>({});
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleInputChange = (fieldName: string, value: string) => {
    setSuggestions(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleCopyToClipboard = async (fieldName: string) => {
    console.log('Copy to clipboard clicked for field:', fieldName);
    console.log('Manual suggestions:', suggestions[fieldName]);
    console.log('Current AI suggestions:', currentAISuggestions?.[fieldName]);
    
    // Use manual input first, then fall back to current AI suggestions
    let value = suggestions[fieldName];
    if (!value && currentAISuggestions && currentAISuggestions[fieldName]) {
      value = Array.isArray(currentAISuggestions[fieldName]) 
             ? currentAISuggestions[fieldName].join(', ')
             : String(currentAISuggestions[fieldName]);
    }
    
    console.log('Value to copy:', value);
    
    if (!value) {
      console.log('No value to copy');
      return;
    }

    try {
      // Copy to clipboard using the Clipboard API
      await navigator.clipboard.writeText(value);
      console.log('Successfully copied to clipboard:', value);
      
      // Show success feedback
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      
      // Fallback: try the older execCommand method
      try {
        const textArea = document.createElement('textarea');
        textArea.value = value;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        console.log('Successfully copied using fallback method');
        
        // Show success feedback
        setCopiedField(fieldName);
        setTimeout(() => setCopiedField(null), 2000);
      } catch (fallbackError) {
        console.error('Fallback copy method also failed:', fallbackError);
      }
    }
  };

  return (
    <div className="bg-gray-50 border-2 border-gray-300 rounded-lg p-4 mt-2">
      <div className="grid gap-3">
        {formConfig.map((field) => (
          <div key={field.field_name} className="bg-white border border-gray-300 rounded-md p-2.5">
            <div className="flex items-center justify-between mb-1.5">
              <label className="font-medium text-gray-700 text-xs">
                {field.label}
                {field.required && <span className="text-red-600 ml-0.5">*</span>}
              </label>
              <span className="bg-purple-100 text-purple-800 px-1.5 py-0.5 rounded-full text-[11px] font-medium">{field.field_type}</span>
            </div>
            
            <div className="flex gap-1.5 items-start">
              <textarea
                value={suggestions[field.field_name] || 
                       (currentAISuggestions && currentAISuggestions[field.field_name] 
                        ? (Array.isArray(currentAISuggestions[field.field_name]) 
                           ? currentAISuggestions[field.field_name].join(', ') 
                           : String(currentAISuggestions[field.field_name]))
                        : '')}
                onChange={(e) => handleInputChange(field.field_name, e.target.value)}
                placeholder={currentAISuggestions && currentAISuggestions[field.field_name] 
                           ? `AI: ${Array.isArray(currentAISuggestions[field.field_name]) 
                                   ? currentAISuggestions[field.field_name].join(', ') 
                                   : String(currentAISuggestions[field.field_name])}`
                           : `Paste ${field.label}...`}
                className="flex-1 p-2 border border-gray-300 rounded text-xs resize-y min-h-[50px] font-inherit focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500/20"
                rows={2}
              />
              
              <button
                onClick={() => handleCopyToClipboard(field.field_name)}
                disabled={(() => {
                  const hasManualInput = suggestions[field.field_name]?.trim();
                  const hasAIInput = currentAISuggestions && currentAISuggestions[field.field_name];
                  return !hasManualInput && !hasAIInput;
                })()}
                className={`p-2 border rounded cursor-pointer transition-all duration-200 flex items-center justify-center text-gray-500 ${
                  copiedField === field.field_name 
                    ? 'border-emerald-500 text-emerald-500 bg-emerald-50' 
                    : 'border-gray-300 bg-white hover:border-blue-500 hover:text-blue-500 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed'
                }`}
                title="Copy to clipboard"
              >
                {copiedField === field.field_name ? <FaCheck /> : <FaCopy />}
              </button>
            </div>
            
            {field.field_type === 'checkboxes' && (
              <div className="mt-1 text-[11px] text-gray-400 italic">Use commas, semicolons, or pipes to separate values</div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
