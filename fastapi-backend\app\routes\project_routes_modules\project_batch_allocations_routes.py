from fastapi import APIRouter, Depends, HTTPException, status # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
from pydantic import BaseModel
from typing import Dict, Any, List
import logging

from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies, AllocationStatus
from dependencies.auth import get_current_user
from core.session_manager import get_project_db_session, get_master_db_session

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/projects", tags=["Projects"])


@router.get("/{project_id}/batch-allocations", response_model=Dict[str, Any])
async def get_project_batch_allocations(
    project_id: int,
    master_db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_user)
):
    """
    Get batch allocation information for a project.
    
    Args:
        project_id: The project ID from master database
        master_db: Master database session
        current_user: Current authenticated user
        
    Returns:
        Dict: Batch allocation information including batches and progress
    """
    try:
        # Get project details from master database
        project_result = await master_db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.id == project_id)
        )
        project = project_result.scalar_one_or_none()
        
        if not project:
            return {
                "success": False,
                "error": f"Project with ID {project_id} not found",
                "batches": [],
                "progress": None
            }
        
        # Get strategy details to check verification requirements
        strategy_details = None
        if project.allocation_strategy_id:
            strategy_result = await master_db.execute(
                select(AllocationStrategies).where(
                    AllocationStrategies.id == project.allocation_strategy_id
                )
            )
            strategy_details = strategy_result.scalar_one_or_none()
        
        # Get project database session
        
        async with get_project_db_session(project.project_code) as session:
            # Build dynamic annotator count query based on strategy
            annotator_count_parts = []
            num_annotators = strategy_details.num_annotators if strategy_details else 1
            for i in range(1, num_annotators + 1):
                annotator_count_parts.append(f"""
                        COALESCE(
                            (SELECT COUNT(DISTINCT fa.annotator_{i}) 
                             FROM file_allocations fa 
                             WHERE fa.batch_id = ab.id AND fa.annotator_{i} IS NOT NULL), 0
                        )""")
            
            annotator_count_sql = " +".join(annotator_count_parts) if annotator_count_parts else "0"
            
            # Build verifier count query only if strategy requires verification
            if strategy_details and strategy_details.requires_verification:
                verifier_count_sql = """COALESCE(
                            (SELECT COUNT(DISTINCT fa.verifier) 
                             FROM file_allocations fa 
                             WHERE fa.batch_id = ab.id AND fa.verifier IS NOT NULL), 0
                        )"""
                verified_count_sql = """CASE 
                            WHEN ab.total_files > 0 AND (
                                SELECT COUNT(*) 
                                FROM file_allocations fa 
                                WHERE fa.batch_id = ab.id AND fa.verifier_review IS NOT NULL
                            ) = ab.total_files THEN 1
                            ELSE 0
                        END"""
            else:
                verifier_count_sql = "0"
                verified_count_sql = "0"
            
            # Get all batches with their allocation data and calculate dynamic status
            batch_result = await session.execute(
                text(f"""
                    SELECT 
                        ab.id as batch_id,
                        ab.batch_identifier as batch_name,
                        ab.batch_status as original_status,
                        ab.total_files,
                        ab.annotation_count,
                        ab.assignment_count,
                        ab.completion_count,
                        ({annotator_count_sql}) as annotator_count,
                        ({verifier_count_sql}) as verifier_count,
                        ({verified_count_sql}) as verified_count
                    FROM allocation_batches ab
                    ORDER BY ab.id
                """)
            )
            batches = batch_result.fetchall()
            
            # Format batch data for frontend
            batch_list = []
            for batch in batches:
                required_annotators = strategy_details.num_annotators if strategy_details else 1
                required_verifiers = 1 if (strategy_details and strategy_details.requires_verification) else 0
                
                # Calculate dynamic status based on completion states
                completion_count = batch.completion_count or 0
                verified_count = batch.verified_count or 0
                total_files = batch.total_files or 0
                
                # Determine status based on progress
                if strategy_details and strategy_details.requires_verification:
                    # For projects requiring verification
                    if verified_count == 1:  # Binary: 1 means fully verified
                        status = "completed"
                    elif batch.verifier_count > 0 or verified_count > 0:
                        status = "verifying"
                    elif completion_count >= total_files and total_files > 0:
                        status = "annotating"  # Fully annotated, ready for verification
                    elif batch.annotator_count > 0 or completion_count > 0:
                        status = "annotating"
                    else:
                        status = "created"
                else:
                    # For projects not requiring verification
                    if completion_count >= total_files and total_files > 0:
                        status = "completed"
                    elif batch.annotator_count > 0 or completion_count > 0:
                        status = "annotating"
                    else:
                        status = "created"
                
                batch_list.append({
                    "batch_id": batch.batch_id,
                    "batch_name": batch.batch_name,
                    "status": status,
                    "annotator_count": completion_count,  # Use completion_count as annotator_count
                    "verifier_count": batch.verifier_count,  # Keep original verifier assignment count
                    "verified_count": verified_count,  # Add binary verification completion status
                    "total_annotators_required": required_annotators,
                    "total_verifiers_required": required_verifiers
                })
            
            # Calculate assignment progress based on batch status
            total_batches = len(batch_list)
            
            # Count batches by status for annotator phase
            annotator_completed = sum(1 for b in batch_list if b["status"] in ["completed"] or (b["status"] == "annotating" and not (strategy_details and strategy_details.requires_verification)))
            annotator_in_progress = sum(1 for b in batch_list if b["status"] == "annotating")
            annotator_pending = sum(1 for b in batch_list if b["status"] == "created")
            
            verifier_completed = 0
            verifier_in_progress = 0
            verifier_pending = 0
            
            if strategy_details and strategy_details.requires_verification:
                # Count batches by status for verifier phase
                verifier_completed = sum(1 for b in batch_list if b["status"] == "completed")
                verifier_in_progress = sum(1 for b in batch_list if b["status"] == "verifying")
                verifier_pending = sum(1 for b in batch_list if b["status"] in ["created", "annotating"])
            
            progress = {
                "total_batches": total_batches,
                "annotator_phase": {
                    "completed_batches": annotator_completed,
                    "in_progress_batches": annotator_in_progress,
                    "pending_batches": annotator_pending
                },
                "verifier_phase": {
                    "completed_batches": verifier_completed,
                    "in_progress_batches": verifier_in_progress,
                    "pending_batches": verifier_pending
                }
            }
            
            return {
                "success": True,
                "batches": batch_list,
                "progress": progress,
                "project_code": project.project_code,
                "requires_verification": strategy_details.requires_verification if strategy_details else False
            }
            
    except Exception as e:
        logger.error(f"Error fetching batch allocations for project {project_id}: {e}")
        return {
            "success": False,
            "error": f"Could not fetch batches from project database {project.project_code if 'project' in locals() else 'unknown'}: {str(e)}",
            "batches": [],
            "progress": None
        }
