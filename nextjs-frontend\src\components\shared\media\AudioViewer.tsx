"use client";

import { useRef, useEffect, useState } from "react";
import { MediaViewerProps, getStreamingUrlEndpoint } from "./types";
import { 
  FaCheck, 
  FaPlay, 
  FaPause, 
  FaVolumeUp, 
  FaVolumeMute, 
  FaMusic,
  FaStepBackward,
  FaStepForward
} from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";

interface AudioViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  isLabeled?: boolean;
}

interface StreamingInfo {
  streaming_url: string;
  type: 'presigned' | 'api_endpoint';
  storage_type?: string;
  expires_in?: number;
  supports_range_requests: boolean;
}

export default function AudioViewer({
  mediaUrl,
  onLoad,
  onError,
  isLabeled = false
}: AudioViewerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [optimizedUrl, setOptimizedUrl] = useState<string | null>(null);
  const [isLoadingUrl, setIsLoadingUrl] = useState(true);
  const [streamingError, setStreamingError] = useState<string | null>(null);

  // Get optimized streaming URL for audio
  useEffect(() => {
    const getStreamingUrl = async () => {
      if (!mediaUrl) return;
      
      setIsLoadingUrl(true);
      setStreamingError(null);
      
      // Extract audio path from the mediaUrl
      const audioPath = mediaUrl.replace(/.*\/media\/audio\//, '').replace(/^\//, '');
      
      console.log('AudioViewer: Setting up audio for path:', audioPath);
      console.log('AudioViewer: Media URL:', mediaUrl);
      
      try {
        // Try to get MinIO presigned URL if available
        const streamingEndpoint = getStreamingUrlEndpoint('audio', audioPath);
        console.log('AudioViewer: Attempting to fetch streaming URL from:', streamingEndpoint);
        const streamUrlResponse = await authFetch(streamingEndpoint);
        
        console.log('AudioViewer: Stream URL response status:', streamUrlResponse.status);
        
        if (streamUrlResponse.ok) {
          const streamingInfo: StreamingInfo = await streamUrlResponse.json();
          console.log('AudioViewer: Got streaming info:', streamingInfo);
          
          // Only use presigned URLs (MinIO), for everything else use direct media URL
          if (streamingInfo.type === 'presigned' && streamingInfo.storage_type === 'MinIO') {
            console.log('AudioViewer: Using MinIO presigned URL for optimized streaming');
            setOptimizedUrl(streamingInfo.streaming_url);
            setIsLoadingUrl(false);
            return;
          } else {
            console.log('AudioViewer: Not a MinIO presigned URL, type:', streamingInfo.type, 'storage:', streamingInfo.storage_type);
          }
        } else {
          console.log('AudioViewer: Stream URL response not ok:', await streamUrlResponse.text());
        }
      } catch (error) {
        console.log('AudioViewer: Error fetching streaming URL:', error);
      }
      
      // For all other cases (NAS-FTP, errors, non-MinIO), use direct media URL
      console.log('AudioViewer: Using direct media URL through API');
      setOptimizedUrl(mediaUrl);
      setStreamingError(null);
      setIsLoadingUrl(false);
    };

    getStreamingUrl();
  }, [mediaUrl, onError]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !optimizedUrl) return;

    console.log('AudioViewer: Setting up audio with URL:', optimizedUrl);

    // Reset audio state when URL changes
    setIsLoading(true);
    setCurrentTime(0);
    setDuration(0);
    setIsPlaying(false);

    const handleLoadedMetadata = () => {
      console.log('AudioViewer: Metadata loaded, duration:', audio.duration);
      setDuration(audio.duration);
      setIsLoading(false);
      onLoad?.();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleError = (e: Event) => {
      const audio = e.target as HTMLAudioElement;
      const error = audio.error;
      
      console.log('AudioViewer: Error event fired', {
        src: audio.src,
        currentMediaUrl: mediaUrl,
        error: error,
        networkState: audio.networkState,
        readyState: audio.readyState
      });
      
      // Only handle errors if the audio source matches the current optimizedUrl
      if (audio.src !== optimizedUrl) {
        console.log('AudioViewer: Ignoring error from different source');
        return;
      }
      
      // Check if there's actually an error - sometimes the error event fires without a real error
      if (!error || error.code === undefined) {
        console.log('AudioViewer: No actual error detected, ignoring');
        return;
      }
      
      let errorMessage = "Audio loading failed";
      
      switch (error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          errorMessage = "Audio loading was aborted";
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          errorMessage = "Network error while loading audio";
          break;
        case MediaError.MEDIA_ERR_DECODE:
          errorMessage = "Audio decode error";
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          errorMessage = "Audio format not supported";
          break;
        default:
          errorMessage = `Audio loading failed (code: ${error.code})`;
      }
      
      console.error('AudioViewer: Real error detected:', errorMessage);
      setIsLoading(false);
      onError?.(errorMessage);
    };

    const handleEnded = () => {
      setIsPlaying(false);
    };

    const handleLoadStart = () => {
      console.log('AudioViewer: Load started');
      setIsLoading(true);
    };

    const handleCanPlay = () => {
      console.log('AudioViewer: Can play');
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
    };
  }, [optimizedUrl, onLoad, onError]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    // Ensure the browser has found a playable source before trying to play
    if (audio.readyState < 2) {
      return;
    }

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newVolume = parseFloat(e.target.value);
    audio.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = parseFloat(e.target.value);
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const skipBackward = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = Math.max(0, audio.currentTime - 10);
  };

  const skipForward = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.currentTime = Math.min(duration, audio.currentTime + 10);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="h-full flex flex-col justify-center items-center relative bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg p-5 text-white">
      {isLoadingUrl ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-white">Loading...</div>
        </div>
      ) : streamingError ? (
        <div className="flex flex-col items-center justify-center h-full text-center p-4">
          <div className="text-red-300 mb-2">Streaming Error</div>
          <div className="text-white text-sm">{streamingError}</div>
          <div className="text-gray-300 text-xs mt-2">Please check MinIO configuration</div>
        </div>
      ) : optimizedUrl ? (
        <>
          <audio 
            key={optimizedUrl} 
            ref={audioRef} 
            src={optimizedUrl} 
            preload="metadata" 
          />
          
          {/* Audio Visualization */}
          <div className="flex flex-col items-center mb-5">
            <div className="mb-2.5 opacity-80">
              <FaMusic size={48} />
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold mb-1">Audio File</div>
              <div className="text-sm opacity-80">
                {isLoading ? "Loading..." : formatTime(duration)}
              </div>
            </div>
          </div>
          
          {/* Waveform-like visualization */}
          <div className="flex items-end justify-center gap-0.5 h-15 my-5 relative">
            {Array.from({ length: 50 }, (_, i) => (
              <div
                key={i}
                className="w-0.5 bg-white/60 rounded-sm transition-opacity duration-300"
                style={{
                  height: `${Math.random() * 40 + 10}px`,
                  opacity: i / 50 <= progressPercentage / 100 ? 1 : 0.3
                }}
              ></div>
            ))}
          </div>
          
          {/* Audio Controls */}
          <div className="bg-black/30 rounded-xl p-4 flex items-center gap-4 min-w-[400px]">
            <button 
              className="bg-white/20 border-none text-white cursor-pointer p-2 rounded-full transition-all duration-200 flex items-center justify-center hover:bg-white/30 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100" 
              onClick={skipBackward} 
              disabled={isLoading}
            >
              <FaStepBackward />
            </button>
            
            <button 
              className="w-12 h-12 bg-white/90 text-gray-800 border-none cursor-pointer rounded-full transition-all duration-200 flex items-center justify-center hover:bg-white hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100" 
              onClick={togglePlay} 
              disabled={isLoading}
            >
              {isPlaying ? <FaPause size={20} /> : <FaPlay size={20} />}
            </button>
            
            <button 
              className="bg-white/20 border-none text-white cursor-pointer p-2 rounded-full transition-all duration-200 flex items-center justify-center hover:bg-white/30 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100" 
              onClick={skipForward} 
              disabled={isLoading}
            >
              <FaStepForward />
            </button>
            
            <div className="flex-1 flex flex-col gap-1">
              <input
                type="range"
                min="0"
                max={duration || 0}
                value={currentTime}
                onChange={handleSeek}
                className="w-full h-1.5 bg-white/30 rounded-full outline-none cursor-pointer appearance-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-md"
                disabled={isLoading}
              />
              <div className="text-xs text-center opacity-80">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <button 
                className="bg-white/20 border-none text-white cursor-pointer p-2 rounded-full transition-all duration-200 flex items-center justify-center hover:bg-white/30 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100" 
                onClick={toggleMute} 
                disabled={isLoading}
              >
                {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-16 h-1 bg-white/30 rounded-full outline-none appearance-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-white [&::-webkit-slider-thumb]:cursor-pointer"
                disabled={isLoading}
              />
            </div>
          </div>
        </>
      ) : (
        <div className="flex items-center justify-center h-full">
          <div className="text-white">No streaming URL available</div>
        </div>
      )}
      
      {isLabeled && (
        <span className="absolute top-2.5 left-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center z-10">
          <FaCheck className="me-1" /> Labeled
        </span>
      )}

    </div>
  );
}
