"use client";
import React from "react";
import { StepIndicator } from './components/StepIndicator';
import { ClientRegistration } from './steps/ClientRegistration';
import { DataConnectorSetup } from './steps/DataConnectorSetup';
import { DatasetSelection } from './steps/DatasetSelection';
import { CSVFieldSelection } from './steps/CSVFieldSelection';
import { EditInstructions } from './steps/EditInstructions';
import { AllocationStrategy } from './steps/AllocationStrategy';
import AnnotatorRequirements from './steps/annotationRequirements';
import ProjectStart from './steps/projectStart';

// Custom hooks
import { useOnboardingSteps } from './hooks/useOnboardingSteps';
import { useClientData } from './hooks/useClientData';
import { useConnectorStatus } from './hooks/useConnectorStatus';
import { useDatasetData } from './hooks/useDatasetData';
import { useNasBrowser } from './hooks/useNasBrowser';
import { useInstructions } from './hooks/useInstructions';
import { useDatasetCreation } from './hooks/useDatasetCreation';

export default function ClientOnboarding() {
  // Step management
  const {
    currentStep,
    steps,
    markStepCompleted,
    goToStep,
    goToNextStep,
    isStepCompleted,
  } = useOnboardingSteps();

  // Client data management
  const {
    selectedClient,
    existingClients,
    clientForm,
    setClientForm,
    clientFormErrors,
    registering,
    handleClientRegistration,
    handleSelectExistingClient,
  } = useClientData(goToNextStep);

  // Connector status management
  const {
    connectorStatus,
    nasForm,
    setNasForm,
    driveForm,
    setDriveForm,
    minioForm,
    setMinioForm,
    connectingNas,
    connectingDrive,
    connectingMinio,
    handleConnectNas,
    handleConnectDrive,
    handleConnectMinio,
  } = useConnectorStatus();

  // Dataset data management
  const {
    activeTab,
    setActiveTab,
    manualDatasetList,
    verificationDatasetList,
    selectedManualDatasetId,
    setSelectedManualDatasetId,
    selectedVerificationDatasetId,
    setSelectedVerificationDatasetId,
    loadingDatasets,
    assigning,
    refreshing,
    isGridView,
    setIsGridView,
    currentImageFolder,
    imagePage,
    totalImagePages,
    loadingImages,
    images,
    fetchDatasets,
    handleRefreshDatasets,
    handleAssignDataset,
    loadImages,
    openImageBrowser,
  } = useDatasetData();

  // NAS browser management
  const {
    isNasBrowserOpen,
    setIsNasBrowserOpen,
    currentSelectionTarget,
    currentBrowsePath,
    currentSelection,
    isSelectingFile,
    directoryContents,
    isLoadingDirectory,
    handleOpenNasBrowser,
    handleSelectItem,
    handleBreadcrumbClick,
  } = useNasBrowser();

  // Instructions management
  const {
    instructions,
    setInstructions,
    savingInstructions,
    selectedProjectCode,
    projectsList,
    loadingProjects,
    currentProjectData,
    fetchProjects,
    handleSaveInstructions,
    handleProjectChange,
  } = useInstructions();

  // Dataset creation management
  const {
    manualFolderPath,
    setManualFolderPath,
    verificationImageFolderPath,
    setVerificationImageFolderPath,
    verificationLabelFilePath,
    setVerificationLabelFilePath,
    filesPerBatch,
    setFilesPerBatch,
    creatingAnnotationBatch,
    creatingVerificationBatch,
    handleSetManualFolder,
    handleSetVerificationFolders,
    handleSelectPath,
  } = useDatasetCreation();

  // Enhanced handlers with step completion logic
  const handleClientRegistrationWithCompletion = async () => {
    const result = await handleClientRegistration();
    if (result.success) {
      markStepCompleted(1);
      goToNextStep();
    }
    return result;
  };

  const handleSelectExistingClientWithCompletion = async (client: any) => {
    const result = await handleSelectExistingClient(client);
    if (result.success) {
      markStepCompleted(1);
      // Note: goToNextStep is already called inside handleSelectExistingClient
    }
    return result;
  };

  const handleConnectNasWithCompletion = async (projectCode: string, overrideCredentials?: any) => {
    const result = await handleConnectNas(projectCode, overrideCredentials);
    if (result.success) {
      markStepCompleted(2);
      goToNextStep();
    }
    return result;
  };

  const handleConnectDriveWithCompletion = async (projectCode: string) => {
    const result = await handleConnectDrive(projectCode);
    if (result.success) {
      markStepCompleted(2);
      goToNextStep();
    }
    return result;
  };

  const handleConnectMinioWithCompletion = async (projectCode: string) => {
    const result = await handleConnectMinio(projectCode);
    if (result.success) {
      markStepCompleted(2);
      goToNextStep();
    }
    return result;
  };

  const handleAssignDatasetWithCompletion = async (mode: "annotation" | "verification") => {
    const result = await handleAssignDataset(mode);
    if (result.success) {
      markStepCompleted(3);
    }
    return result;
  };

  const handleSaveInstructionsWithCompletion = async () => {
  const result = await handleSaveInstructions();
  if (result.success) {
    markStepCompleted(4);
    // Pass the project code when navigating to the next step
    if (selectedProjectCode) {
      // Make sure projectCode state is set
      setProjectCode(selectedProjectCode);
      goToStep(5, selectedProjectCode);
    } else {
      goToNextStep();
    }
  }
  return result;
};

  const handleSetManualFolderWithRefresh = async () => {
    // Get the project code from the current project in localStorage
    let currentProjectCode = null;
    try {
      const projectData = localStorage.getItem('currentProject');
      if (projectData) {
        const project = JSON.parse(projectData);
        currentProjectCode = project.project_code;
        console.log("Using project code from localStorage:", currentProjectCode);
        // Store the project code in state for use in other steps
        setProjectCode(currentProjectCode);
      }
    } catch (error) {
      console.error("Error getting project from localStorage:", error);
    }

    if (!currentProjectCode) {
      console.error("No project found in localStorage for manual folder assignment");
      return { success: false };
    }

    const result = await handleSetManualFolder(currentProjectCode, fetchDatasets as any);

    // Always mark step as completed and proceed to next step if folder was set successfully
    if (result.success) {
      // Mark step as completed and proceed to next step
      markStepCompleted(3);
      goToStep(4, currentProjectCode);
    }

    return result;
  };

  const handleSetVerificationFoldersWithRefresh = async () => {
    return await handleSetVerificationFolders(fetchDatasets);
  };

  const handleSelectPathComplete = () => {
    handleSelectPath(currentSelectionTarget, currentBrowsePath, currentSelection);
    setIsNasBrowserOpen(false);
  };

  // Handle dataset selection changes
  const handleManualDatasetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const datasetId = e.target.value;
    setSelectedManualDatasetId(datasetId || null);
  };

  const handleVerificationDatasetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const datasetId = e.target.value;
    setSelectedVerificationDatasetId(datasetId || null);
  };

  // store projectCode after assignment
  const [projectCode, setProjectCode] = React.useState<string | null>(null);
  const [isNewProjectCreated, setIsNewProjectCreated] = React.useState<boolean>(false);
  const [currentProjectType, setCurrentProjectType] = React.useState<string>('');
  const [isConfiguringCSV, setIsConfiguringCSV] = React.useState<boolean>(false);

  // Load current project type from localStorage
  React.useEffect(() => {
    const loadProjectType = () => {
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          setCurrentProjectType(project.project_type || '');
          setProjectCode(project.project_code || null);
        }
      } catch (error) {
        console.error('Error loading project type from localStorage:', error);
      }
    };

    loadProjectType();
  }, [currentStep]);

  // update projectCode when dataset assigned - only if no new project was created
  React.useEffect(() => {
    // Don't override projectCode if a new project was just created
    if (isNewProjectCreated) {
      return;
    }

    if (manualDatasetList && selectedManualDatasetId) {
      // The selectedManualDatasetId is the project_code
      const newProjectCode = String(selectedManualDatasetId);
      setProjectCode(newProjectCode);
    } else if (verificationDatasetList && selectedVerificationDatasetId) {
      // Same for verification datasets
      const newProjectCode = String(selectedVerificationDatasetId);
      setProjectCode(newProjectCode);
    }
  }, [manualDatasetList, verificationDatasetList, selectedManualDatasetId, selectedVerificationDatasetId, isNewProjectCreated, projectCode]);

  const handleRegisterDataset = async (mode: "annotation" | "verification", folderPath: string, batchSize: number|null, projectCode?: string) => {
    try {
      // Check if we have a project code (required for the new workflow)
      if (!projectCode) {
        throw new Error('Project code is required. Please select a project first.');
      }

      // Use the direct API endpoint that works with the batch_size parameter
      const res = await fetch(`/api/admin/select-annotation-folder`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          folder_path: folderPath,
          files_per_batch: batchSize || 10,
          project_code: projectCode
        })
      });

      const data = await res.json();
      if (!res.ok) throw new Error(data.detail || 'Failed to register dataset');

      // Mark step as completed and go to next step regardless of dataset fetch errors
      console.log("Dataset registration successful, proceeding to instructions");
      markStepCompleted(3);
      goToStep(4);

      return { success: true };
    } catch (e) {
      console.error("Error registering dataset:", e);
      return { success: false };
    }
  };

  const handleSetCSVConfiguration = async (config: { selectedColumn: string; recordsPerBatch: number }) => {
    try {
      setIsConfiguringCSV(true);
      
      // Get current project from localStorage
      const projectData = localStorage.getItem('currentProject');
      if (!projectData) {
        throw new Error('No project found in localStorage');
      }
      
      const project = JSON.parse(projectData);
      const projectCode = project.project_code;
      
      if (!projectCode) {
        throw new Error('No project code found');
      }

      console.log('Configuring CSV project:', { projectCode, ...config });
      
      // Call the backend API to store configuration in Redis
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000';
      const response = await fetch(`${backendUrl}/api/admin/configure-csv/${projectCode}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(config)
      });

      if (!response.ok) {
        let errorMessage = 'Configuration failed';
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch (e) {
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      if (result.success) {
        // Mark step as completed
        markStepCompleted(3);
        return { success: true };
      } else {
        throw new Error(result.message || 'Configuration failed');
      }
    } catch (error: any) {
      console.error('Error configuring CSV project:', error);
      return { success: false, error: error.message };
    } finally {
      setIsConfiguringCSV(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-800">Client Onboarding</h1>
        <div className="w-24 h-1 bg-blue-500 mx-auto mt-2 rounded"></div>
        <p className="text-gray-600 mt-2">Complete B2B client setup process</p>
      </div>

      <StepIndicator steps={steps} currentStep={currentStep} />

      <div className="w-screen max-w-6xl bg-white shadow-md rounded-lg p-2 ml-2">
        {currentStep === 1 && (
          <ClientRegistration
            existingClients={existingClients}
            clientForm={clientForm}
            setClientForm={setClientForm}
            clientFormErrors={clientFormErrors}
            registering={registering}
            onClientRegistration={handleClientRegistrationWithCompletion}
            onSelectExistingClient={handleSelectExistingClientWithCompletion}
          />
        )}

        {currentStep === 2 && (
          <DataConnectorSetup
            selectedClient={selectedClient}
            connectorStatus={connectorStatus}
            nasForm={nasForm}
            setNasForm={setNasForm}
            driveForm={driveForm}
            setDriveForm={setDriveForm}
            minioForm={minioForm}
            setMinioForm={setMinioForm}
            connectingNas={connectingNas}
            connectingDrive={connectingDrive}
            connectingMinio={connectingMinio}
            onConnectNas={handleConnectNasWithCompletion}
            onConnectDrive={handleConnectDriveWithCompletion}
            onConnectMinio={handleConnectMinioWithCompletion}
            onGoToStep={goToStep}
            markStepCompleted={markStepCompleted}
          />
        )}

        {currentStep === 3 && (
          currentProjectType === 'csv' ? (
            <CSVFieldSelection
              selectedClient={selectedClient}
              onGoToStep={goToStep}
              onSetCSVConfiguration={handleSetCSVConfiguration}
              isConfiguring={isConfiguringCSV}
            />
          ) : (
            <DatasetSelection
              selectedClient={selectedClient}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              manualDatasetList={manualDatasetList}
              verificationDatasetList={verificationDatasetList}
              selectedManualDatasetId={selectedManualDatasetId}
              selectedVerificationDatasetId={selectedVerificationDatasetId}
              onManualDatasetChange={handleManualDatasetChange}
              onVerificationDatasetChange={handleVerificationDatasetChange}
              loadingDatasets={loadingDatasets}
              refreshing={refreshing}
              assigning={assigning}
              onRefreshDatasets={handleRefreshDatasets}
              onRegisterDataset={handleRegisterDataset}
              manualFolderPath={manualFolderPath}
              setManualFolderPath={setManualFolderPath}
              verificationImageFolderPath={verificationImageFolderPath}
              setVerificationImageFolderPath={setVerificationImageFolderPath}
              verificationLabelFilePath={verificationLabelFilePath}
              setVerificationLabelFilePath={setVerificationLabelFilePath}
              filesPerBatch={filesPerBatch}
              setFilesPerBatch={setFilesPerBatch}
              creatingAnnotationBatch={creatingAnnotationBatch}
              creatingVerificationBatch={creatingVerificationBatch}
              onSetManualFolder={handleSetManualFolderWithRefresh}
              onSetVerificationFolders={handleSetVerificationFoldersWithRefresh}
              isGridView={isGridView}
              onOpenImageBrowser={openImageBrowser}
              onBackFromImageBrowser={() => setIsGridView(false)}
              imagesBrowserProps={{
                images,
                folder: currentImageFolder,
                page: imagePage,
                totalPages: totalImagePages,
                loading: loadingImages,
                onRefresh: () => loadImages(currentImageFolder, imagePage),
                onPageChange: (newPage) => loadImages(currentImageFolder, newPage),
              }}
              onGoToStep={goToStep}
              isStepCompleted={isStepCompleted(3)}
            />
          )
        )}

        {currentStep === 4 && (
        <EditInstructions
            selectedClient={selectedClient}
            instructions={instructions}
            setInstructions={setInstructions}
            savingInstructions={savingInstructions}
            selectedProjectCode={selectedProjectCode}
            projectsList={projectsList}
            loadingProjects={loadingProjects}
            currentProjectData={currentProjectData}
            fetchProjects={fetchProjects}
            onSaveInstructions={handleSaveInstructionsWithCompletion}
            handleProjectChange={handleProjectChange}
            onGoToStep={goToStep}
            isStepCompleted={isStepCompleted(4)}
            markStepCompleted={markStepCompleted}
            projectCode={projectCode || undefined}
        />
        )}       
        {currentStep === 5 && (
          <AllocationStrategy
            selectedClient={selectedClient}
            projectCode={projectCode || undefined}
            onGoToStep={goToStep}
            isStepCompleted={isStepCompleted(5)}
            markStepCompleted={markStepCompleted}
          />
        )}
        {currentStep === 6 && (
          <AnnotatorRequirements 
            selectedClient={selectedClient}
            projectCode={projectCode || undefined}
            onGoToStep={goToStep}
            isStepCompleted={isStepCompleted(6)}
            markStepCompleted={markStepCompleted}
          />
        )}
        {currentStep === 7 && (
          <ProjectStart
            projectCode={projectCode}
            selectedClient={selectedClient}
            onGoToStep={goToStep}
            markStepCompleted={markStepCompleted}
          />
        )}
      </div>
    </div>
  );
} 