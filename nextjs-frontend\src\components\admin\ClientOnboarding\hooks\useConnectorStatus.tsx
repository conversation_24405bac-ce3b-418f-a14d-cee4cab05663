// hooks/useConnectorStatus.tsx
import { useState, useEffect } from 'react';
import { ConnectorStatus, NasFormData, DriveFormData, MinIOFormData } from '../types';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "@/lib/api";
export const useConnectorStatus = () => {
  const [connectorStatus, setConnectorStatus] = useState<ConnectorStatus>({ 
    nas: false, 
    drive: false,
    minio: false
  });
  const [nasForm, setNasForm] = useState<NasFormData>({
    nasType: "ftp",
    url: "",
    username: "",
    password: "",
  });
  const [driveForm, setDriveForm] = useState<DriveFormData>({
    clientId: "",
    clientSecret: "",
    folderId: "",
  });
  const [minioForm, setMinioForm] = useState<MinIOFormData>({
    endpoint: "",
    accessKey: "",
    secretKey: "",
    bucketName: "",
    secure: false,
    region: "",
  });
  const [connectingNas, setConnectingNas] = useState(false);
  const [connectingDrive, setConnectingDrive] = useState(false);
  const [connectingMinio, setConnectingMinio] = useState(false);

  useEffect(() => {
    checkConnectorStatus();
  }, []);

  const checkConnectorStatus = async () => {
    try {
      const [nasRes, minioRes] = await Promise.all([
        authFetch(`${API_BASE_URL}/admin/check-nas-connection`),
        authFetch(`${API_BASE_URL}/admin/minio-status`),
      ]);

      const nasData = await nasRes.json();
      const minioData = await minioRes.json();

      setConnectorStatus({
        nas: nasData.data?.connected || false,
        drive: false,
        minio: minioData.data?.connected || false,
      });
    } catch (error) {
      console.error("Failed to check connector status:", error);
    }
  };

  const handleConnectNas = async (projectCode: string, overrideCredentials?: any) => {
    setConnectingNas(true);
    try {
      // Use override credentials if provided, otherwise use form data
      const credentials = overrideCredentials || nasForm;

      // Log credentials for debugging (without exposing password)
      console.log("Attempting NAS connection:", {
        nas_type: credentials.nasType || nasForm.nasType,
        nas_url: credentials.url || nasForm.url,
        nas_username: credentials.username || nasForm.username,
        password_length: (credentials.password || nasForm.password).length,
        project_code: projectCode,
        using_override: !!overrideCredentials,
      });

      const res = await authFetch(`${API_BASE_URL}/admin/connect-nas`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          nas_type: credentials.nasType || nasForm.nasType,
          nas_url: credentials.url || nasForm.url,
          nas_username: credentials.username || nasForm.username,
          nas_password: credentials.password || nasForm.password,
          project_code: projectCode, // Use project_code instead of client_id
        }),
      });
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({ detail: "Failed to connect NAS" }));
        throw new Error(errorData.detail || "Failed to connect NAS");
      }

      setConnectorStatus((prev) => ({ ...prev, nas: true }));
      showToast.success("NAS connected successfully!");
      
      // Update the project data in localStorage with the new connection info
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          const credentials = overrideCredentials || nasForm;
          const updatedProject = {
            ...project,
            connection_type: 'NAS-FTP',
            credentials: {
              nas_type: credentials.nasType || nasForm.nasType,
              nas_url: credentials.url || nasForm.url,
              nas_username: credentials.username || nasForm.username,
              nas_password: credentials.password || nasForm.password,
            }
          };
          localStorage.setItem('currentProject', JSON.stringify(updatedProject));
          console.log('Updated project data in localStorage with NAS connection:', updatedProject);
        }
      } catch (error) {
        console.error('Error updating project data in localStorage:', error);
      }
      
      return { success: true };
    } catch (error: unknown) {
      if (error instanceof Error) {
        showToast.error(`NAS connection failed: ${error.message}`);
      } else {
        showToast.error("NAS connection failed");
      }
      return { success: false };
    } finally {
      setConnectingNas(false);
    }
  };

  const handleConnectDrive = async (projectCode: string) => {
    setConnectingDrive(true);
    try {
      const res = await authFetch(`${API_BASE_URL}/admin/configure-google-drive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          client_id: driveForm.clientId,
          client_secret: driveForm.clientSecret,
          folder_id: driveForm.folderId,
          project_code: projectCode,
        }),
      });

      const data = await res.json();
      if (data.data?.auth_url) {
        const authWindow = window.open(
          data.data.auth_url,
          "GoogleDriveAuth",
          "width=600,height=700"
        );
        if (authWindow) {
          const checkClosed = setInterval(() => {
            if (authWindow.closed) {
              clearInterval(checkClosed);
              checkConnectorStatus();
              showToast.success("Google Drive connected successfully!");
            }
          }, 1000);
        }
        return { success: true };
      }
      return { success: false };
    } catch (error: unknown) {
      if (error instanceof Error) {
        showToast.error(`Google Drive connection failed: ${error.message}`);
      } else {
        showToast.error("Google Drive connection failed");
      }
      return { success: false };
    } finally {
      setConnectingDrive(false);
    }
  };

  const handleConnectMinio = async (projectCode: string) => {
    setConnectingMinio(true);
    try {
      // Validate required fields
      if (!minioForm.endpoint.trim() || !minioForm.accessKey.trim() || !minioForm.secretKey.trim() || !minioForm.bucketName.trim()) {
        showToast.error("Please fill in all required MinIO fields");
        return { success: false };
      }

      const res = await authFetch(`${API_BASE_URL}/admin/connect-minio`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          minio_endpoint: minioForm.endpoint.trim(),
          minio_access_key: minioForm.accessKey.trim(),
          minio_secret_key: minioForm.secretKey.trim(),
          minio_bucket_name: minioForm.bucketName.trim(),
          minio_secure: minioForm.secure,
          minio_region: minioForm.region.trim() || null,
          project_code: projectCode,
        }),
      });

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({ detail: "Failed to connect MinIO" }));
        throw new Error(errorData.detail || "Failed to connect MinIO");
      }

      setConnectorStatus((prev) => ({ ...prev, minio: true }));
      showToast.success("MinIO connected successfully!");
      
      // Update the project data in localStorage with the new connection info
      try {
        const projectData = localStorage.getItem('currentProject');
        if (projectData) {
          const project = JSON.parse(projectData);
          const updatedProject = {
            ...project,
            connection_type: 'MinIO',
            credentials: {
              minio_endpoint: minioForm.endpoint.trim(),
              minio_access_key: minioForm.accessKey.trim(),
              minio_secret_key: minioForm.secretKey.trim(),
              minio_bucket_name: minioForm.bucketName.trim(),
              minio_secure: minioForm.secure,
              minio_region: minioForm.region.trim() || null,
            }
          };
          localStorage.setItem('currentProject', JSON.stringify(updatedProject));
          console.log('Updated project data in localStorage with MinIO connection:', updatedProject);
        }
      } catch (error) {
        console.error('Error updating project data in localStorage:', error);
      }
      
      return { success: true };
    } catch (error: unknown) {
      if (error instanceof Error) {
        showToast.error(`MinIO connection failed: ${error.message}`);
      } else {
        showToast.error("MinIO connection failed");
      }
      return { success: false };
    } finally {
      setConnectingMinio(false);
    }
  };

  return {
    connectorStatus,
    setConnectorStatus,
    nasForm,
    setNasForm,
    driveForm,
    setDriveForm,
    minioForm,
    setMinioForm,
    connectingNas,
    connectingDrive,
    connectingMinio,
    checkConnectorStatus,
    handleConnectNas,
    handleConnectDrive,
    handleConnectMinio,
  };
};