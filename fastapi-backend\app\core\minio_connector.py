import os
import logging
import asyncio
import io
import time
from typing import Optional, List, Dict, Any, Union, BinaryIO
from minio import Minio #type:ignore
from minio.error import S3Error, InvalidResponseError #type:ignore
from .storage_base import StorageConnector
from schemas.MinIOConfig import MinIOConfig
from .config import get_settings

logger = logging.getLogger('minio_connector')

class MinIONASConnector(StorageConnector):
    def __init__(self, config: Optional[MinIOConfig] = None):
        # Use provided config or create from centralized settings
        if config is None:
            config = MinIOConfig.from_centralized_config()
        
        # Construct MinIO URL for base class
        protocol = "https" if config.secure else "http"
        minio_url = f"{protocol}://{config.endpoint}"
        super().__init__(minio_url, config.access_key, config.secret_key)
        self.logger = logging.getLogger('MinIONASConnector')
        self.config = config
        self.client = None
        self.bucket_name = config.bucket_name

        try:
            self.logger.info(f"Initializing MinIO client with endpoint={config.endpoint}, secure={config.secure}")
            self.client = Minio(
                config.endpoint,
                access_key=config.access_key,
                secret_key=config.secret_key,
                secure=config.secure,
                region=config.region
            )
        except Exception as e:
            self.logger.error(f"Error initializing MinIO client: {str(e)}")
            self.authenticated = False
            raise

    async def _retry_operation(self, operation, *args, **kwargs):
        """Retry an operation with exponential backoff"""
        for attempt in range(self.config.max_retries):
            try:
                return await operation(*args, **kwargs)
            except Exception as e:
                if attempt == self.config.max_retries - 1:
                    raise e
                
                wait_time = self.config.retry_delay * (2 ** attempt)
                self.logger.warning(f"Operation failed (attempt {attempt + 1}/{self.config.max_retries}): {e}. Retrying in {wait_time}s...")
                await asyncio.sleep(wait_time)

    async def authenticate(self) -> bool:
        """Authenticate with MinIO server and ensure bucket exists"""
        try:
            self.logger.info(f"Authenticating with MinIO server {self.config.endpoint}")
            
            # Test connection by listing buckets with retry
            await self._retry_operation(asyncio.to_thread, self.client.list_buckets)
            
            # Ensure the configured bucket exists
            bucket_exists = await self._retry_operation(asyncio.to_thread, self.client.bucket_exists, self.bucket_name)
            if not bucket_exists:
                self.logger.info(f"Creating bucket '{self.bucket_name}' as it doesn't exist")
                await self._retry_operation(
                    asyncio.to_thread,
                    self.client.make_bucket, 
                    self.bucket_name, 
                    location=self.config.region
                )
            
            self.authenticated = True
            self.logger.info("MinIO authentication successful")
            return True
        except Exception as e:
            self.logger.error(f"MinIO authentication error: {str(e)}")
            self.authenticated = False
            return False

    # ------------- directory operations (MinIO uses object prefixes) -------------
    async def list_directory(self, path: str = "/") -> List[Dict[str, str]]:
        """List objects in a 'directory' (prefix)"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            # Remove leading slash and ensure proper prefix format
            prefix = path.lstrip('/')
            if prefix and not prefix.endswith('/'):
                prefix += '/'
            
            objects = await asyncio.to_thread(
                self.client.list_objects,
                self.bucket_name,
                prefix=prefix,
                recursive=False
            )
            
            result = []
            for obj in objects:
                # Skip the prefix itself if it's an object
                if obj.object_name == prefix:
                    continue
                    
                # Determine if it's a directory (ends with /) or file
                is_directory = obj.object_name.endswith('/')
                name = obj.object_name.rstrip('/').split('/')[-1]
                
                result.append({
                    'name': name,
                    'path': obj.object_name,
                    'size': str(obj.size) if not is_directory else '0',
                    'modified': obj.last_modified.isoformat() if obj.last_modified else '',
                    'type': 'directory' if is_directory else 'file'
                })
            
            return result
        except Exception as e:
            self.logger.error(f"Error listing directory {path}: {str(e)}")
            raise

    async def create_directory(self, path: str) -> bool:
        """Create a 'directory' (object with trailing slash)"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            # Ensure path ends with / for directory
            if not path.endswith('/'):
                path += '/'
            
            # Create empty object to represent directory
            await asyncio.to_thread(
                self.client.put_object,
                self.bucket_name,
                path,
                io.BytesIO(b''),
                0
            )
            
            self.logger.info(f"Created directory: {path}")
            return True
        except Exception as e:
            self.logger.error(f"Error creating directory {path}: {str(e)}")
            return False

    async def delete_directory(self, path: str) -> bool:
        """Delete a 'directory' and all its contents"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            # Ensure path ends with / for directory
            if not path.endswith('/'):
                path += '/'
            
            # List all objects with this prefix
            objects = await asyncio.to_thread(
                self.client.list_objects,
                self.bucket_name,
                prefix=path,
                recursive=True
            )
            
            # Delete all objects
            for obj in objects:
                await asyncio.to_thread(
                    self.client.remove_object,
                    self.bucket_name,
                    obj.object_name
                )
            
            self.logger.info(f"Deleted directory and contents: {path}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting directory {path}: {str(e)}")
            return False

    # ------------- file operations -------------
    async def upload_file(self, local_path: str, remote_path: str) -> bool:
        """Upload a file to MinIO"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            file_size = os.path.getsize(local_path)
            
            await asyncio.to_thread(
                self.client.fput_object,
                self.bucket_name,
                remote_path,
                local_path
            )
            
            self.logger.info(f"Uploaded file: {local_path} -> {remote_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error uploading file {local_path}: {str(e)}")
            return False

    async def download_file(self, remote_path: str, local_path: str) -> bool:
        """Download a file from MinIO"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.fget_object,
                self.bucket_name,
                remote_path,
                local_path
            )
            
            self.logger.info(f"Downloaded file: {remote_path} -> {local_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error downloading file {remote_path}: {str(e)}")
            return False

    async def get_file_content(self, remote_path: str) -> bytes:
        """Get file content as bytes"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            response = await asyncio.to_thread(
                self.client.get_object,
                self.bucket_name,
                remote_path
            )
            
            content = response.read()
            response.close()
            response.release_conn()
            
            return content
        except Exception as e:
            self.logger.error(f"Error getting file content {remote_path}: {str(e)}")
            raise

    async def get_file_content_range(self, remote_path: str, start: int = None, end: int = None) -> bytes:
        """Get file content with byte range support for streaming"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            # Build range header if start/end specified
            range_header = None
            if start is not None or end is not None:
                start = start or 0
                range_header = f"bytes={start}-{end if end is not None else ''}"
            
            # Get object with range support
            response = await asyncio.to_thread(
                self.client.get_object,
                self.bucket_name,
                remote_path,
                request_headers={'Range': range_header} if range_header else None
            )
            
            content = response.read()
            response.close()
            response.release_conn()
            
            return content
        except Exception as e:
            self.logger.error(f"Error getting file content range {remote_path} ({start}-{end}): {str(e)}")
            raise

    def get_file_stream(self, remote_path: str, start: int = None, end: int = None):
        """Get file content as a stream for large files"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            # Build range header if start/end specified
            range_header = None
            if start is not None or end is not None:
                start = start or 0
                range_header = f"bytes={start}-{end if end is not None else ''}"
            
            # Get object stream with range support
            response = self.client.get_object(
                self.bucket_name,
                remote_path,
                request_headers={'Range': range_header} if range_header else None
            )
            
            return response
        except Exception as e:
            self.logger.error(f"Error getting file stream {remote_path} ({start}-{end}): {str(e)}")
            raise

    async def put_file_content(self, remote_path: str, content: bytes) -> bool:
        """Put file content from bytes"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.put_object,
                self.bucket_name,
                remote_path,
                io.BytesIO(content),
                len(content)
            )
            
            self.logger.info(f"Uploaded content to: {remote_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error putting file content {remote_path}: {str(e)}")
            return False

    async def delete_file(self, remote_path: str) -> bool:
        """Delete a file from MinIO"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.remove_object,
                self.bucket_name,
                remote_path
            )
            
            self.logger.info(f"Deleted file: {remote_path}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting file {remote_path}: {str(e)}")
            return False

    async def file_exists(self, remote_path: str) -> bool:
        """Check if a file exists in MinIO"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.stat_object,
                self.bucket_name,
                remote_path
            )
            return True
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return False
            raise
        except Exception as e:
            self.logger.error(f"Error checking file existence {remote_path}: {str(e)}")
            raise

    async def count_files_in_directory(self, directory_path: str = "/") -> int:
        """
        Count the number of files in a MinIO directory/prefix (excluding subdirectories).
        """
        try:
            self.logger.info(f"Counting files in MinIO directory: {directory_path}")
            
            # Use the existing list_directory method
            directory_contents = await self.list_directory(directory_path)
            
            # Filter out directories and count only files
            file_count = sum(1 for item in directory_contents if item.get("type") == "file")
            
            self.logger.info(f"Found {file_count} files in MinIO directory: {directory_path}")
            return file_count
            
        except Exception as e:
            self.logger.error(f"Error counting files in MinIO directory {directory_path}: {e}")
            return 0

    async def get_file_info(self, remote_path: str) -> Optional[Dict[str, Any]]:
        """Get file information"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            stat = await asyncio.to_thread(
                self.client.stat_object,
                self.bucket_name,
                remote_path
            )
            
            return {
                'name': remote_path.split('/')[-1],
                'path': remote_path,
                'size': stat.size,
                'modified': stat.last_modified.isoformat() if stat.last_modified else '',
                'etag': stat.etag,
                'content_type': stat.content_type
            }
        except S3Error as e:
            if e.code == 'NoSuchKey':
                return None
            raise
        except Exception as e:
            self.logger.error(f"Error getting file info {remote_path}: {str(e)}")
            raise

    async def generate_presigned_url(self, remote_path: str, expires_in: int = 3600) -> str:
        """Generate a presigned URL for temporary access"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            from datetime import timedelta
            # Convert seconds to timedelta as required by MinIO client
            expires_timedelta = timedelta(seconds=expires_in)
            
            url = await asyncio.to_thread(
                self.client.presigned_get_object,
                self.bucket_name,
                remote_path,
                expires=expires_timedelta
            )
            return url
        except Exception as e:
            self.logger.error(f"Error generating presigned URL {remote_path}: {str(e)}")
            raise

    async def list_buckets(self) -> List[Dict[str, str]]:
        """List all buckets"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            buckets = await asyncio.to_thread(self.client.list_buckets)
            
            result = []
            for bucket in buckets:
                result.append({
                    'name': bucket.name,
                    'created': bucket.creation_date.isoformat() if bucket.creation_date else '',
                    'region': getattr(bucket, 'region', '')
                })
            
            return result
        except Exception as e:
            self.logger.error(f"Error listing buckets: {str(e)}")
            raise

    async def create_bucket(self, bucket_name: str, region: Optional[str] = None) -> bool:
        """Create a new bucket"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.make_bucket,
                bucket_name,
                location=region or self.config.region
            )
            
            self.logger.info(f"Created bucket: {bucket_name}")
            return True
        except Exception as e:
            self.logger.error(f"Error creating bucket {bucket_name}: {str(e)}")
            return False

    async def delete_bucket(self, bucket_name: str) -> bool:
        """Delete a bucket (must be empty)"""
        if not self.authenticated:
            raise Exception("Not authenticated with MinIO")
        
        try:
            await asyncio.to_thread(
                self.client.remove_bucket,
                bucket_name
            )
            
            self.logger.info(f"Deleted bucket: {bucket_name}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting bucket {bucket_name}: {str(e)}")
            return False
