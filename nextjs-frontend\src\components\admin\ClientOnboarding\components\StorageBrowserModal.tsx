// components/StorageBrowserModal.tsx
import React from 'react';
import { NasBrowserModal } from './NasBrowserModal';
import { MinioBrowserModal } from './MinioBrowserModal';
import { Directory, SelectionTarget } from '../types';

export type StorageType = 'NAS-FTP' | 'MinIO' | 'GoogleDrive';

interface StorageBrowserModalProps {
  isOpen: boolean;
  onClose: () => void;
  storageType: StorageType;
  currentBrowsePath: string;
  currentSelection: string;
  isSelectingFile: boolean;
  directoryContents: Directory[];
  isLoadingDirectory?: boolean;
  onSelectItem: (item: Directory) => void;
  onBreadcrumbClick: (path: string) => void;
  onSelectPath: () => void;
  currentSelectionTarget: SelectionTarget;
  bucketName?: string;
}

export const StorageBrowserModal: React.FC<StorageBrowserModalProps> = ({
  storageType,
  bucketName,
  ...props
}) => {
  switch (storageType) {
    case 'NAS-FTP':
      return <NasBrowserModal {...props} />;
    
    case 'MinIO':
      return <MinioBrowserModal {...props} bucketName={bucketName} />;
    
    case 'GoogleDrive':
      // For future implementation
      return (
        <div className="modal fade show d-block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Google Drive Browser</h5>
                <button type="button" className="btn-close" onClick={props.onClose}></button>
              </div>
              <div className="modal-body">
                <div className="alert alert-info">
                  <strong>Coming Soon:</strong> Google Drive browser is under development.
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={props.onClose}>
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    
    default:
      return null;
  }
};
