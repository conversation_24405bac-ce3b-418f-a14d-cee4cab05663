# """
# Knowledge Base Routes.
# Routes for accessing and managing knowledge base entries.
# """

# from fastapi import APIRouter, Depends, HTTPException, status, Query
# from sqlalchemy.ext.asyncio import AsyncSession
# from sqlalchemy import select, distinct
# from post_db.connect import get_db_connection as get_db
# from dependencies.auth import get_current_active_user, check_roles
# from post_db.master_models.users import UserRole
# from post_db.models.knowledge_base import KnowledgeEntry
# from typing import List, Optional
# from pydantic import BaseModel
# import logging

# logger = logging.getLogger('knowledge_base_routes')

# router = APIRouter(
#     prefix="/knowledge-base",
#     tags=["Knowledge Base"],
#     dependencies=[Depends(get_current_active_user), Depends(check_roles([UserRole.ADMIN]))],
# )

# class KnowledgeEntryResponse(BaseModel):
#     """Schema for knowledge base entry response"""
#     id: int
#     title: str
#     topic: str
#     content: str
#     source: Optional[str] = None
#     created_at: Optional[str] = None
#     updated_at: Optional[str] = None

# class KnowledgeEntryCreate(BaseModel):
#     """Schema for creating knowledge base entry"""
#     title: str
#     topic: str
#     content: str
#     source: Optional[str] = None

# class KnowledgeEntryUpdate(BaseModel):
#     """Schema for updating knowledge base entry"""
#     title: Optional[str] = None
#     topic: Optional[str] = None
#     content: Optional[str] = None
#     source: Optional[str] = None

# @router.get("/entries", response_model=List[KnowledgeEntryResponse])
# async def get_knowledge_entries(
#     topic: Optional[str] = None,
#     skip: int = 0, 
#     limit: int = 100,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Get all knowledge base entries with optional filtering by topic.
    
#     Parameters:
#     - topic: Optional filter by topic
#     - skip: Number of entries to skip (pagination)
#     - limit: Maximum number of entries to return (pagination)
#     """
#     try:
#         stmt = select(KnowledgeEntry)
        
#         if topic:
#             stmt = stmt.where(KnowledgeEntry.topic == topic)
        
#         stmt = stmt.offset(skip).limit(limit)
#         result = await db.execute(stmt)
#         entries = result.scalars().all()
#         return [entry.to_dict() for entry in entries]
#     except Exception as e:
#         logger.error(f"Error fetching knowledge entries: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to fetch knowledge entries: {str(e)}"
#         )

# @router.get("/topics", response_model=List[str])
# async def get_knowledge_topics(db: AsyncSession = Depends(get_db)):
#     """
#     Get all unique topics in the knowledge base.
#     """
#     try:
#         # Using distinct to get unique topics
#         stmt = select(distinct(KnowledgeEntry.topic))
#         result = await db.execute(stmt)
#         topics = result.scalars().all()
#         return [topic for topic in topics if topic]  # Filter out None values
#     except Exception as e:
#         logger.error(f"Error fetching knowledge topics: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to fetch knowledge topics: {str(e)}"
#         )

# @router.get("/entries/{entry_id}", response_model=KnowledgeEntryResponse)
# async def get_knowledge_entry(
#     entry_id: int,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Get a specific knowledge base entry by ID.
    
#     Parameters:
#     - entry_id: ID of the knowledge base entry
#     """
#     try:
#         stmt = select(KnowledgeEntry).where(KnowledgeEntry.id == entry_id)
#         result = await db.execute(stmt)
#         entry = result.scalar_one_or_none()
        
#         if not entry:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"Knowledge entry with ID {entry_id} not found"
#             )
#         return entry.to_dict()
#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(f"Error fetching knowledge entry {entry_id}: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to fetch knowledge entry: {str(e)}"
#         )

# @router.post("/entries", response_model=KnowledgeEntryResponse, status_code=status.HTTP_201_CREATED)
# async def create_knowledge_entry(
#     entry: KnowledgeEntryCreate,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Create a new knowledge base entry.
    
#     Parameters:
#     - entry: Knowledge entry data
#     """
#     try:
#         db_entry = KnowledgeEntry(
#             title=entry.title,
#             topic=entry.topic,
#             content=entry.content,
#             source=entry.source
#         )
#         db.add(db_entry)
#         await db.commit()
#         await db.refresh(db_entry)
#         return db_entry.to_dict()
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error creating knowledge entry: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to create knowledge entry: {str(e)}"
#         )

# @router.put("/entries/{entry_id}", response_model=KnowledgeEntryResponse)
# async def update_knowledge_entry(
#     entry_id: int,
#     entry_update: KnowledgeEntryUpdate,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Update an existing knowledge base entry.
    
#     Parameters:
#     - entry_id: ID of the knowledge base entry to update
#     - entry_update: Knowledge entry data to update
#     """
#     try:
#         stmt = select(KnowledgeEntry).where(KnowledgeEntry.id == entry_id)
#         result = await db.execute(stmt)
#         db_entry = result.scalar_one_or_none()
        
#         if not db_entry:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"Knowledge entry with ID {entry_id} not found"
#             )
        
#         # Update fields that are provided
#         update_data = entry_update.dict(exclude_unset=True)
#         for key, value in update_data.items():
#             setattr(db_entry, key, value)
        
#         await db.commit()
#         await db.refresh(db_entry)
#         return db_entry.to_dict()
#     except HTTPException:
#         raise
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error updating knowledge entry {entry_id}: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to update knowledge entry: {str(e)}"
#         )

# @router.delete("/entries/{entry_id}", status_code=status.HTTP_204_NO_CONTENT)
# async def delete_knowledge_entry(
#     entry_id: int,
#     db: AsyncSession = Depends(get_db)
# ):
#     """
#     Delete a knowledge base entry.
    
#     Parameters:
#     - entry_id: ID of the knowledge base entry to delete
#     """
#     try:
#         stmt = select(KnowledgeEntry).where(KnowledgeEntry.id == entry_id)
#         result = await db.execute(stmt)
#         db_entry = result.scalar_one_or_none()
        
#         if not db_entry:
#             raise HTTPException(
#                 status_code=status.HTTP_404_NOT_FOUND,
#                 detail=f"Knowledge entry with ID {entry_id} not found"
#             )
        
#         await db.delete(db_entry)
#         await db.commit()
#         return None
#     except HTTPException:
#         raise
#     except Exception as e:
#         await db.rollback()
#         logger.error(f"Error deleting knowledge entry {entry_id}: {str(e)}")
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Failed to delete knowledge entry: {str(e)}"
#         ) 