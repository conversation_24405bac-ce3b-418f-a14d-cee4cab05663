"""
Django-style Alembic environment for master_db
This file is persistent and defines how migrations work for this "app"
"""
import sys
from pathlib import Path
from sqlalchemy import engine_from_config, pool
from alembic import context

# Add the app directory to Python path (like Django's manage.py)
sys.path.insert(0, r"C:\Users\<USER>\Desktop\DADP\DADP-Prod-FN\fastapi-backend\app")

# This is the Alembic Config object
config = context.config

# Import the appropriate Base for this "app"
try:
    from post_db.base import Base
    target_metadata = Base.metadata
    print(f"Loaded {len(target_metadata.tables)} tables for master_db")
    
    # Print table names for debugging (like Django's --verbosity=2)
    table_names = list(target_metadata.tables.keys())
    if table_names:
        print(f"Tables: {', '.join(table_names)}")
    
except ImportError as e:
    print(f"Failed to import models for master_db: {e}")
    target_metadata = None

def run_migrations_offline():
    """Run migrations in 'offline' mode (like Django's --fake)"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """Run migrations in 'online' mode (normal Django migrate)"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

# Run the appropriate migration mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
