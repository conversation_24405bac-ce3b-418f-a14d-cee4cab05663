import logging
from typing import Dict, Any, Optional
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select
from core.session_manager import get_master_db_session
from core.nas_connector import get_ftp_connector_from_credentials
from core.minio_utils import get_minio_connector_from_credentials

logger = logging.getLogger(__name__)

class ProjectStorageService:
    
    @staticmethod
    async def get_project_storage_connection(project_code: str) -> Dict[str, Any]:
        try:
            async for master_db in get_master_db_session():
                stmt = select(ProjectsRegistry).where(
                    ProjectsRegistry.project_code == project_code
                )
                result = await master_db.execute(stmt)
                project = result.scalar_one_or_none()
                break
            
            if not project:
                raise ValueError(f"Project '{project_code}' not found")
            
            # Detect storage type and create connector
            storage_type = project.connection_type
            connector = None
            
            # Create appropriate connector based on storage type
            if storage_type == 'MinIO':
                connector = await get_minio_connector_from_credentials(project.credentials)
            elif storage_type == 'NAS-FTP':
                connector = await get_ftp_connector_from_credentials(project.credentials)
            else:
                raise ValueError(f"Unsupported storage type: '{storage_type}'. Suported types: MinIO, NAS-FTP")
            
            if not connector:
                raise ValueError(f"Failed to create {storage_type} connector for project '{project_code}'")
            
            # Return connection info for AI processing
            connection_info = {
                "project_code": project_code,
                "storage_type": storage_type.lower(),
                "connector": connector,
                "credentials": project.credentials,
                "folder_path": project.folder_path,
                "project_data": project
            }
            
            logger.info(f"Storage connection established for project '{project_code}' using {storage_type}")
            return connection_info
            
        except Exception as e:
            logger.error(f"Failed to setup storage for project '{project_code}': {e}")
            raise ValueError(f"Storage setup failed: {e}")


async def get_ai_storage_connection(project_code: str) -> Dict[str, Any]:
    """
    Get storage connection for AI processing.
    """
    return await ProjectStorageService.get_project_storage_connection(project_code)