"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, FaArrowLeft } from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";

type PasswordModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const PasswordModal = ({ isOpen, onClose }: PasswordModalProps) => {
  const [formData, setFormData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const modalRef = useRef<HTMLDivElement>(null);
  const initialFocusRef = useRef<HTMLInputElement>(null);

  // Handle modal close on escape and outside click
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.addEventListener("mousedown", handleClickOutside);
      // Set focus to current password input
      setTimeout(() => initialFocusRef.current?.focus(), 100);
      // Prevent scrolling of background content
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.removeEventListener("mousedown", handleClickOutside);
      document.body.style.overflow = "";
    };
  }, [isOpen, onClose]);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
      setFormErrors({});
      setIsSuccess(false);
    }
  }, [isOpen]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    // Clear error when field is edited
    if (formErrors[name]) {
      setFormErrors((prevErrors) => {
        const newErrors = { ...prevErrors };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    // Current password validation
    if (!formData.currentPassword) {
      errors.currentPassword = "Current password is required";
    }

    // New password validation
    if (!formData.newPassword) {
      errors.newPassword = "New password is required";
    } else if (formData.newPassword.length < 6) {
      errors.newPassword = "New password must be at least 6 characters long";
    }

    // Confirm password validation
    if (formData.newPassword !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setFormErrors(validationErrors);
      return;
    }

    setIsLoading(true);

    try {
      // Call password change API
      const response = await authFetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/change-password`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            current_password: formData.currentPassword,
            new_password: formData.newPassword,
            confirm_password: formData.confirmPassword,
          }),
        }
      );
      const data = await response.json();
      if (!response.ok) {
        // Show API error
        const errMsg = data.error || data.detail || "Password change failed.";
        setFormErrors({ form: errMsg });
      } else {
        // Success
        setIsSuccess(true);
        // Clear form
        setFormData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        // Close modal after delay
        setTimeout(() => {
          onClose();
        }, 2000);
      }
    } catch {
      setFormErrors({ form: "Password change failed. Please try again." });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div
        ref={modalRef}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden"
      >
        <button
          onClick={onClose}
          className="absolute top-4 left-4 text-gray-600 hover:text-gray-900 transition-colors z-10"
          aria-label="Go back"
        >
          <FaArrowLeft />
        </button>

        <div className="p-8">
          <div className="flex items-center justify-center mb-6">
            <div className="bg-blue-100 p-3 rounded-full">
              <FaKey className="text-blue-600 text-xl" />
            </div>
            <h2 className="text-2xl font-bold ml-3">Change Your Password</h2>
          </div>

          {isSuccess ? (
            <div className="bg-green-100 text-green-700 p-4 rounded-lg mb-4 text-center">
              <p className="font-medium">Password successfully changed!</p>
              <p className="text-sm mt-2">Redirecting you back...</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              {/* Current Password */}
              <div className="mb-4">
                <label
                  htmlFor="currentPassword"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Current Password
                </label>
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  autoComplete="current-password"
                  ref={initialFocusRef}
                  className={`w-full px-3 py-2 border ${
                    formErrors.currentPassword
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
                  placeholder="Enter your current password"
                  value={formData.currentPassword}
                  onChange={handleChange}
                  required
                />
                {formErrors.currentPassword && (
                  <p className="text-red-600 text-sm mt-1">
                    {formErrors.currentPassword}
                  </p>
                )}
              </div>

              {/* New Password */}
              <div className="mb-4">
                <label
                  htmlFor="newPassword"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  New Password
                </label>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  autoComplete="new-password"
                  className={`w-full px-3 py-2 border ${
                    formErrors.newPassword
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
                  placeholder="At least 6 characters"
                  value={formData.newPassword}
                  onChange={handleChange}
                  required
                  minLength={6}
                />
                {formErrors.newPassword ? (
                  <p className="text-red-600 text-sm mt-1">
                    {formErrors.newPassword}
                  </p>
                ) : (
                  <p className="text-gray-500 text-sm mt-1">
                    Must be at least 6 characters long.
                  </p>
                )}
              </div>

              {/* Confirm New Password */}
              <div className="mb-6">
                <label
                  htmlFor="confirmPassword"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Confirm New Password
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  autoComplete="new-password"
                  className={`w-full px-3 py-2 border ${
                    formErrors.confirmPassword
                      ? "border-red-500"
                      : "border-gray-300"
                  } rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
                  placeholder="Confirm your new password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                />
                {formErrors.confirmPassword && (
                  <p className="text-red-600 text-sm mt-1">
                    {formErrors.confirmPassword}
                  </p>
                )}
              </div>

              {/* Form error */}
              {formErrors.form && (
                <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-lg text-sm text-center">
                  {formErrors.form}
                </div>
              )}

              {/* Buttons */}
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="py-2 px-4 border border-gray-300 rounded-lg font-medium hover:bg-gray-50 transition-all text-gray-700"
                >
                  Cancel
                </button>

                <button
                  type="submit"
                  className="py-2 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all flex items-center"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  ) : (
                    <FaKey className="mr-2" />
                  )}
                  <span>{isLoading ? "Changing..." : "Change Password"}</span>
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default PasswordModal;

// 'use client';

// import { useState, useEffect, useRef } from 'react';
// import { FaKey, FaArrowLeft } from 'react-icons/fa';
// import { authFetch } from '@/lib/authFetch';

// type PasswordModalProps = {
//   isOpen: boolean;
//   onClose: () => void;
// };

// const PasswordModal = ({ isOpen, onClose }: PasswordModalProps) => {
//   const [formData, setFormData] = useState({
//     currentPassword: '',
//     newPassword: '',
//     confirmPassword: ''
//   });

//   const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
//   const [isLoading, setIsLoading] = useState(false);
//   const [isSuccess, setIsSuccess] = useState(false);

//   const modalRef = useRef<HTMLDivElement>(null);
//   const initialFocusRef = useRef<HTMLInputElement>(null);

//   // Handle modal close on escape and outside click
//   useEffect(() => {
//     const handleEscape = (e: KeyboardEvent) => {
//       if (e.key === 'Escape') onClose();
//     };

//     const handleClickOutside = (e: MouseEvent) => {
//       if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
//         onClose();
//       }
//     };

//     if (isOpen) {
//       document.addEventListener('keydown', handleEscape);
//       document.addEventListener('mousedown', handleClickOutside);
//       // Set focus to current password input
//       setTimeout(() => initialFocusRef.current?.focus(), 100);
//       // Prevent scrolling of background content
//       document.body.style.overflow = 'hidden';
//     }

//     return () => {
//       document.removeEventListener('keydown', handleEscape);
//       document.removeEventListener('mousedown', handleClickOutside);
//       document.body.style.overflow = '';
//     };
//   }, [isOpen, onClose]);

//   // Reset form when modal opens
//   useEffect(() => {
//     if (isOpen) {
//       setFormData({
//         currentPassword: '',
//         newPassword: '',
//         confirmPassword: ''
//       });
//       setFormErrors({});
//       setIsSuccess(false);
//     }
//   }, [isOpen]);

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const { name, value } = e.target;
//     setFormData(prevState => ({
//       ...prevState,
//       [name]: value
//     }));

//     // Clear error when field is edited
//     if (formErrors[name]) {
//       setFormErrors(prevErrors => {
//         const newErrors = { ...prevErrors };
//         delete newErrors[name];
//         return newErrors;
//       });
//     }
//   };

//   const validateForm = () => {
//     const errors: {[key: string]: string} = {};

//     // Current password validation
//     if (!formData.currentPassword) {
//       errors.currentPassword = 'Current password is required';
//     }

//     // New password validation
//     if (!formData.newPassword) {
//       errors.newPassword = 'New password is required';
//     } else if (formData.newPassword.length < 6) {
//       errors.newPassword = 'New password must be at least 6 characters long';
//     }

//     // Confirm password validation
//     if (formData.newPassword !== formData.confirmPassword) {
//       errors.confirmPassword = 'Passwords do not match';
//     }

//     return errors;
//   };

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();

//     // Validate form
//     const validationErrors = validateForm();
//     if (Object.keys(validationErrors).length > 0) {
//       setFormErrors(validationErrors);
//       return;
//     }

//     setIsLoading(true);

//     try {
//       // Call password change API
//       const response = await authFetch(
//         `${process.env.NEXT_PUBLIC_API_URL}/auth/change-password`,
//         {
//           method: 'POST',
//           headers: { 'Content-Type': 'application/json' },
//           body: JSON.stringify({
//             current_password: formData.currentPassword,
//             new_password: formData.newPassword,
//             confirm_password: formData.confirmPassword,
//           }),
//         }
//       );
//       const data = await response.json();
//       if (!response.ok) {
//         // Show API error
//         const errMsg = data.error || data.detail || 'Password change failed.';
//         setFormErrors({ form: errMsg });
//       } else {
//         // Success
//         setIsSuccess(true);
//         // Clear form
//         setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
//         // Close modal after delay
//         setTimeout(() => {
//           onClose();
//         }, 2000);
//       }
//     } catch (error) {
//       setFormErrors({ form: 'Password change failed. Please try again.' });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
//       <div
//         ref={modalRef}
//         className="bg-white rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden"
//       >
//         <button
//           onClick={onClose}
//           className="absolute top-4 left-4 text-gray-600 hover:text-gray-900 transition-colors z-10"
//           aria-label="Go back"
//         >
//           <FaArrowLeft />
//         </button>

//         <div className="p-8">
//           <div className="flex items-center justify-center mb-6">
//             <div className="bg-blue-100 p-3 rounded-full">
//               <FaKey className="text-blue-600 text-xl" />
//             </div>
//             <h2 className="text-2xl font-bold ml-3">Change Your Password</h2>
//           </div>

//           {isSuccess ? (
//             <div className="bg-green-100 text-green-700 p-4 rounded-lg mb-4 text-center">
//               <p className="font-medium">Password successfully changed!</p>
//               <p className="text-sm mt-2">Redirecting you back...</p>
//             </div>
//           ) : (
//             <form onSubmit={handleSubmit}>
//               {/* Current Password */}
//               <div className="mb-4">
//                 <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
//                   Current Password
//                 </label>
//                 <input
//                   type="password"
//                   id="currentPassword"
//                   name="currentPassword"
//                   autoComplete="current-password"
//                   ref={initialFocusRef}
//                   className={`w-full px-3 py-2 border ${formErrors.currentPassword ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
//                   placeholder="Enter your current password"
//                   value={formData.currentPassword}
//                   onChange={handleChange}
//                   required
//                 />
//                 {formErrors.currentPassword && (
//                   <p className="text-red-600 text-sm mt-1">{formErrors.currentPassword}</p>
//                 )}
//               </div>

//               {/* New Password */}
//               <div className="mb-4">
//                 <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
//                   New Password
//                 </label>
//                 <input
//                   type="password"
//                   id="newPassword"
//                   name="newPassword"
//                   autoComplete="new-password"
//                   className={`w-full px-3 py-2 border ${formErrors.newPassword ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
//                   placeholder="At least 6 characters"
//                   value={formData.newPassword}
//                   onChange={handleChange}
//                   required
//                   minLength={6}
//                 />
//                 {formErrors.newPassword ? (
//                   <p className="text-red-600 text-sm mt-1">{formErrors.newPassword}</p>
//                 ) : (
//                   <p className="text-gray-500 text-sm mt-1">Must be at least 6 characters long.</p>
//                 )}
//               </div>

//               {/* Confirm New Password */}
//               <div className="mb-6">
//                 <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
//                   Confirm New Password
//                 </label>
//                 <input
//                   type="password"
//                   id="confirmPassword"
//                   name="confirmPassword"
//                   autoComplete="new-password"
//                   className={`w-full px-3 py-2 border ${formErrors.confirmPassword ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all`}
//                   placeholder="Confirm your new password"
//                   value={formData.confirmPassword}
//                   onChange={handleChange}
//                   required
//                 />
//                 {formErrors.confirmPassword && (
//                   <p className="text-red-600 text-sm mt-1">{formErrors.confirmPassword}</p>
//                 )}
//               </div>

//               {/* Form error */}
//               {formErrors.form && (
//                 <div className="mb-4 p-2 bg-red-100 text-red-700 rounded-lg text-sm text-center">
//                   {formErrors.form}
//                 </div>
//               )}

//               {/* Buttons */}
//               <div className="flex justify-end gap-4">
//                 <button
//                   type="button"
//                   onClick={onClose}
//                   className="py-2 px-4 border border-gray-300 rounded-lg font-medium hover:bg-gray-50 transition-all text-gray-700"
//                 >
//                   Cancel
//                 </button>

//                 <button
//                   type="submit"
//                   className="py-2 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all flex items-center"
//                   disabled={isLoading}
//                 >
//                   {isLoading ? (
//                     <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
//                       <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                       <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                     </svg>
//                   ) : (
//                     <FaKey className="mr-2" />
//                   )}
//                   <span>{isLoading ? 'Changing...' : 'Change Password'}</span>
//                 </button>
//               </div>
//             </form>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default PasswordModal;
