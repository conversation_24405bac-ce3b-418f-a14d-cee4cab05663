'use client';

import { useState, useEffect } from 'react';
import { Inter, Poppins } from 'next/font/google';
import './globals.css';
import { usePathname } from 'next/navigation';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import LoginModal from '@/components/auth/LoginModal';
import RegisterModal from '@/components/auth/RegisterModal';
import PasswordModal from '@/components/auth/PasswordModal';
import { AuthProvider } from '@/contexts/AuthContext';
// Import Bootstrap CSS without source maps
import 'bootstrap/dist/css/bootstrap.css';
import { Toaster } from 'react-hot-toast';

// Font setup
const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap'
});

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  variable: '--font-poppins',
  display: 'swap'
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [mounted, setMounted] = useState(false);
  // Modal state
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const pathname = usePathname();
  const isClientRoute = pathname?.startsWith('/client') || false;
  const isAnnotatorRoute = pathname?.startsWith('/annotator') || false;
  const isAdminRoute = pathname?.startsWith('/admin') || false;
  const isDocumindRoute = pathname?.startsWith('/documind') || false;
  const isNoteOcrRoute = pathname?.startsWith('/note-ocr') || false;
  const isAuditorRoute = pathname?.startsWith('/auditor') || false;
  const isVerifierRoute = pathname?.startsWith('/verifier') || false;
  const isSyntheticRoute = pathname?.startsWith('/synthetic') || false;

  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);

  // Modal handlers
  const openLoginModal = () => {
    setIsLoginModalOpen(true);
    setIsRegisterModalOpen(false);
    setIsPasswordModalOpen(false);
  };

  const openRegisterModal = () => {
    setIsLoginModalOpen(false);
    setIsRegisterModalOpen(true);
    setIsPasswordModalOpen(false);
  };

  const openPasswordModal = () => {
    setIsLoginModalOpen(false);
    setIsRegisterModalOpen(false);
    setIsPasswordModalOpen(true);
  };

  const closeAllModals = () => {
    setIsLoginModalOpen(false);
    setIsRegisterModalOpen(false);
    setIsPasswordModalOpen(false);
  };

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Listen for custom event to open login modal
  useEffect(() => {
    if (!mounted) return;
    const openLoginListener = () => openLoginModal();
    document.addEventListener('open-login-modal', openLoginListener);
    return () => {
      document.removeEventListener('open-login-modal', openLoginListener);
    };
  }, [mounted]);

  // Listen for custom event to open password modal
  useEffect(() => {
    if (!mounted) return;
    const openPasswordListener = () => openPasswordModal();
    document.addEventListener('open-password-modal', openPasswordListener);
    return () => {
      document.removeEventListener('open-password-modal', openPasswordListener);
    };
  }, [mounted]);

  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body suppressHydrationWarning className="min-h-screen flex flex-col">
        <AuthProvider>
          {/* Background elements */}
          <div className="background-gradient"></div>
          
          {mounted && !isAdminRoute && !isDocumindRoute && !isNoteOcrRoute && !isAuditorRoute && !isAnnotatorRoute && !isVerifierRoute && !isSyntheticRoute && !isClientRoute && <Header onOpenLogin={openLoginModal} />}
          
          <main className={`flex-grow ${(!isAdminRoute && !isDocumindRoute && !isNoteOcrRoute && !isAuditorRoute && !isAnnotatorRoute && !isVerifierRoute && !isSyntheticRoute && !isClientRoute) ? 'pt-24' : 'pt-0'}`}>
            {children}
          </main>
          
          {mounted && !isAdminRoute && !isDocumindRoute && !isNoteOcrRoute && !isAuditorRoute && !isAnnotatorRoute && !isVerifierRoute && !isSyntheticRoute && !isClientRoute && <Footer />}
          
          {/* Auth Modals */}
          {mounted && !isAdminRoute && !isAuditorRoute && (
            <>
              <LoginModal 
                isOpen={isLoginModalOpen} 
                onClose={closeAllModals} 
                onRegisterClick={openRegisterModal}
              />
              
              <RegisterModal 
                isOpen={isRegisterModalOpen} 
                onClose={closeAllModals} 
                onLoginClick={openLoginModal}
              />
              
              <PasswordModal 
                isOpen={isPasswordModalOpen} 
                onClose={closeAllModals}
              />
            </>
          )}
          
          {/* Toast Notifications */}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
} 