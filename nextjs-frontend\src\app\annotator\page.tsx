'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useProject } from '@/contexts/ProjectContext';
import AnnotatorDashboard from '@/components/annotator/AnnotatorDashboard';
import MediaAnnotation from '@/components/annotator/MediaAnnotation';
import VerificationMode from '@/components/annotator/VerificationMode';
// import Supervision from '@/old-files/Supervision';

import { API_BASE_URL } from "@/lib/api";


export default function AnnotatorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const { projectCode } = useProject();
  const view = (searchParams.get('view') as 'dashboard' | 'annotation' | 'verification' | 'supervision') || 'dashboard';

  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Fetch dashboard data including instructions
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        console.log('Fetching dashboard data...');
        const response = await fetch(`${API_BASE_URL}/annotator/dashboard`, {
          credentials: 'include'
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('Dashboard data:', data);
          setDashboardData(data);
        } else {
          console.error('Failed to fetch dashboard data:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (view === 'dashboard') {
      fetchDashboardData();
    } else {
      setLoading(false);
    }
  }, [view]);

  // Fallback instructions if no project instructions are available
  const fallbackInstructions = '<p>Please follow these guidelines when annotating documents:</p><ul><li>Label all images accurately</li><li>Take your time to ensure precision</li><li>Follow the taxonomy provided in the documentation</li></ul>';
  
  const adminInstructions = dashboardData?.instructions || fallbackInstructions;

  // Get user data from AuthContext (from database via cookie auth)
  const username = user?.username || '';
  const fullName = user?.full_name || '';
  const annotationMode = user?.annotator_mode as 'annotation' | 'verification' | 'supervision' || 'annotation';

  // Navigate between views
  const handleNavigate = async (action: 'annotation' | 'verification' | 'supervision') => {
    console.log('Navigate action clicked:', action);
    
    if (action === 'annotation') {
      try {
        console.log('Making API call to start-annotation endpoint');
        // Call the batch assignment endpoint first
        const response = await fetch(`${API_BASE_URL}/annotator/start-annotation`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        console.log('API response status:', response.status);
        console.log('API response ok:', response.ok);

        if (response.ok) {
          const result = await response.json();
          console.log('Batch assignment result:', result);
          
          // Now navigate to annotation page
          console.log('Navigating to annotation view...');
          router.push(`/annotator?view=${action}`);
          console.log('Navigation command sent');
        } else {
          const error = await response.json();
          console.error('Batch assignment failed:', error);
          
          // Handle different error cases
          if (response.status === 404 && error.detail?.includes('No available batches')) {
            alert('All batches have been completed or are currently assigned. Please check back later.');
          } else if (response.status === 400 && error.detail?.includes('No active project')) {
            alert('You are not assigned to any active project. Please contact your administrator.');
          } else if (response.status === 409 && error.detail?.includes('All batches are currently full')) {
            alert('All batches are currently full. Please try again later.');
          } else {
            alert(`Failed to start annotation: ${error.detail || 'Unknown error'}`);
          }
        }
      } catch (error) {
        console.error('Network error:', error);
        alert('Network error occurred. Please check your connection and try again.');
      }
    } else {
      // For other actions (verification, supervision), just navigate normally
      console.log('Navigating to other view:', action);
      router.push(`/annotator?view=${action}`);
    }
  };

  // Render according to view
  console.log('Rendering view:', view);
  
  if (view === 'dashboard') {
    if (loading) {
      console.log('Loading dashboard...');
      return (
        <div className="px-8 py-8 max-w-screen-2xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          </div>
        </div>
      );
    }
    
    console.log('Rendering AnnotatorDashboard');
    return (
      <AnnotatorDashboard
        username={username}
        fullName={fullName}
        annotationMode={annotationMode}
        adminInstructions={adminInstructions}
        projectCode={projectCode}
        onActionClick={handleNavigate}
      />
    );
  }
  if (view === 'annotation') {
    console.log('Rendering MediaAnnotation');
    return <MediaAnnotation />;
  }
  if (view === 'verification') {
    console.log('Rendering VerificationMode');
    return <VerificationMode />;
  }
  // if (view === 'supervision') {
  //   console.log('Rendering Supervision');
  //   return <Supervision />;
  // }

  console.log('No view matched, returning null');
  return null;
}