"""
Integration tests that verify existing services work correctly with dynamic schemas.
Tests that ProjectBatchService and ProjectUsersService work with different allocation strategies.

CRITICAL: These tests ensure existing services work with PRODUCTION DYNAMIC SCHEMAS:
- Tests existing services with 1, 2, 3+ annotator strategies
- Verifies batch allocation works with dynamic annotator columns
- Ensures user assignment works with different verification requirements
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
import time
import uuid

from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.project_users import ProjectUsers
from app.services.project_batch_service import ProjectBatchService
from app.services.project_users_service import ProjectUsersService
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.service          # Feature marker - Service integration
@pytest.mark.regression       # Suite marker - Integration testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestDynamicSchemaWithExistingServices:
    """REGRESSION TEST SUITE: Dynamic schema integration with existing services."""
    
    @pytest_asyncio.fixture
    async def single_annotator_setup(self, test_master_db: AsyncSession, setup_test_database_with_strategy):
        """Setup single annotator project with dynamic schema."""
        # Create client
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create single annotator strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=False,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Create project
        project = test_factory.projects.create_project(client.id, strategy.id) + "/single/folder",
            batch_size=3,
            project_status="active",
            priority_level=1
        )
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Setup project database with dynamic schema
        project_db_session = await setup_test_database_with_strategy(strategy)
        
        return {
            "client": client,
            "strategy": strategy,
            "project": project,
            "project_db": project_db_session
        }

    @pytest_asyncio.fixture
    async def multi_annotator_setup(self, test_master_db: AsyncSession, setup_test_database_with_strategy):
        """Setup multi annotator project with dynamic schema."""
        # Create client
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create multi annotator strategy with verification
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=3,
            requires_verification=True,
            requires_ai_preprocessing=True,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Create project
        project = test_factory.projects.create_project(client.id, strategy.id) + "/multi/folder",
            batch_size=2,
            project_status="active",
            priority_level=2
        )
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Setup project database with dynamic schema
        project_db_session = await setup_test_database_with_strategy(strategy)
        
        return {
            "client": client,
            "strategy": strategy,
            "project": project,
            "project_db": project_db_session
        }

    @pytest.mark.asyncio
    async def test_project_batch_service_single_annotator_schema(self, single_annotator_setup):
        """Test ProjectBatchService works with single annotator dynamic schema."""
        setup_data = single_annotator_setup
        project = setup_data["project"]
        project_db = setup_data["project_db"]
        
        # Verify schema has exactly 1 annotator column
        await self._verify_dynamic_schema_columns(
            project_db, 
            expected_annotators=1, 
            has_verifier=False
        )
        
        # Test batch creation with dynamic schema
        batch = AllocationBatches(
            batch_identifier="SINGLE_BATCH_001",
            total_files=3,
            file_list=["file1.jpg", "file2.jpg", "file3.jpg"],
            annotation_count=1
        )
        project_db.add(batch)
        await project_db.commit()
        await project_db.refresh(batch)
        
        # Verify batch was created successfully
        assert batch.id is not None
        assert batch.annotation_count == 1
        
        # Test file registration
        files = [
            FilesRegistry(
                batch_id=batch.id,
                file_identifier="file1.jpg",
                original_filename="file1.jpg",
                file_type=FileType.IMAGE,
                file_size_bytes=1024
            ),
            FilesRegistry(
                batch_id=batch.id,
                file_identifier="file2.jpg",
                original_filename="file2.jpg",
                file_type=FileType.IMAGE,
                file_size_bytes=2048
            )
        ]
        
        for file_obj in files:
            project_db.add(file_obj)
        await project_db.commit()
        
        # Verify files were registered
        result = await project_db.execute(select(FilesRegistry))
        registered_files = result.scalars().all()
        assert len(registered_files) == 2

    @pytest.mark.asyncio
    async def test_project_batch_service_multi_annotator_schema(self, multi_annotator_setup):
        """Test ProjectBatchService works with multi annotator dynamic schema."""
        setup_data = multi_annotator_setup
        project = setup_data["project"]
        project_db = setup_data["project_db"]
        
        # Verify schema has exactly 3 annotator columns and verifier
        await self._verify_dynamic_schema_columns(
            project_db, 
            expected_annotators=3, 
            has_verifier=True
        )
        
        # Test batch creation with dynamic schema
        batch = AllocationBatches(
            batch_identifier="MULTI_BATCH_001",
            total_files=5,
            file_list=["video1.mp4", "video2.mp4", "video3.mp4", "video4.mp4", "video5.mp4"],
            annotation_count=3  # Matches strategy num_annotators
        )
        project_db.add(batch)
        await project_db.commit()
        await project_db.refresh(batch)
        
        # Verify batch was created successfully
        assert batch.id is not None
        assert batch.annotation_count == 3
        
        # Test that dynamic annotator columns can be used
        batch_update_result = await project_db.execute(
            text("UPDATE allocation_batches SET annotator_1 = :user1, annotator_2 = :user2, annotator_3 = :user3, verifier = :verifier WHERE id = :batch_id"),
            {
                "user1": 101,
                "user2": 102, 
                "user3": 103,
                "verifier": 201,
                "batch_id": batch.id
            }
        )
        await project_db.commit()
        
        # Verify the update worked
        assert batch_update_result.rowcount == 1
        
        # Check that the values were set correctly
        check_result = await project_db.execute(
            text("SELECT annotator_1, annotator_2, annotator_3, verifier FROM allocation_batches WHERE id = :batch_id"),
            {"batch_id": batch.id}
        )
        row = check_result.fetchone()
        assert row[0] == 101  # annotator_1
        assert row[1] == 102  # annotator_2
        assert row[2] == 103  # annotator_3
        assert row[3] == 201  # verifier

    @pytest.mark.asyncio
    async def test_project_users_service_dynamic_roles(self, single_annotator_setup, multi_annotator_setup):
        """Test ProjectUsersService returns correct roles for different strategies."""
        service = ProjectUsersService()
        
        # Test single annotator strategy roles
        single_project = single_annotator_setup["project"]
        single_roles = await service.get_available_roles(single_project.project_code)
        
        assert single_roles["success"] is True
        assert "annotator_1" in single_roles["roles"]
        assert "annotator_2" not in single_roles["roles"]  # Should not have second annotator
        assert "verifier" not in single_roles["roles"]  # No verification required
        assert "ai_processor" not in single_roles["roles"]  # No AI preprocessing
        
        # Test multi annotator strategy roles
        multi_project = multi_annotator_setup["project"]
        multi_roles = await service.get_available_roles(multi_project.project_code)
        
        assert multi_roles["success"] is True
        assert "annotator_1" in multi_roles["roles"]
        assert "annotator_2" in multi_roles["roles"]
        assert "annotator_3" in multi_roles["roles"]
        assert "annotator_4" not in multi_roles["roles"]  # Should not have fourth annotator
        assert "verifier" in multi_roles["roles"]  # Verification required
        assert "ai_processor" in multi_roles["roles"]  # AI preprocessing required

    @pytest.mark.asyncio
    async def test_file_allocations_dynamic_columns(self, multi_annotator_setup):
        """Test that file_allocations table works with dynamic annotator columns."""
        setup_data = multi_annotator_setup
        project_db = setup_data["project_db"]
        
        # Create batch and file
        batch = AllocationBatches(
            batch_identifier="FILE_ALLOC_BATCH_001",
            total_files=1,
            file_list=["test_file.mp4"],
            annotation_count=3
        )
        project_db.add(batch)
        await project_db.commit()
        await project_db.refresh(batch)
        
        file_obj = FilesRegistry(
            batch_id=batch.id,
            file_identifier="test_file.mp4",
            original_filename="test_file.mp4",
            file_type=FileType.VIDEO,
            file_size_bytes=5000000
        )
        project_db.add(file_obj)
        await project_db.commit()
        await project_db.refresh(file_obj)
        
        # Test creating file allocation with dynamic columns
        await project_db.execute(
            text("""
                INSERT INTO file_allocations 
                (file_id, batch_id, allocation_sequence, annotator_1, annotator_2, annotator_3, verifier)
                VALUES (:file_id, :batch_id, 1, :user1, :user2, :user3, :verifier)
            """),
            {
                "file_id": file_obj.id,
                "batch_id": batch.id,
                "user1": 101,
                "user2": 102,
                "user3": 103,
                "verifier": 201
            }
        )
        await project_db.commit()
        
        # Test updating review data for each annotator
        review_data = {"labels": {"category": "test"}, "confidence": 0.95}
        
        await project_db.execute(
            text("""
                UPDATE file_allocations 
                SET annotator_1_review = :review1,
                    annotator_2_review = :review2,
                    annotator_3_review = :review3,
                    verifier_review = :verifier_review
                WHERE file_id = :file_id
            """),
            {
                "review1": review_data,
                "review2": review_data,
                "review3": review_data,
                "verifier_review": {"verified": True, "notes": "Good quality"},
                "file_id": file_obj.id
            }
        )
        await project_db.commit()
        
        # Verify all review data was stored correctly
        result = await project_db.execute(
            text("""
                SELECT annotator_1_review, annotator_2_review, annotator_3_review, verifier_review 
                FROM file_allocations 
                WHERE file_id = :file_id
            """),
            {"file_id": file_obj.id}
        )
        row = result.fetchone()
        
        assert row[0] == review_data  # annotator_1_review
        assert row[1] == review_data  # annotator_2_review
        assert row[2] == review_data  # annotator_3_review
        assert row[3] == {"verified": True, "notes": "Good quality"}  # verifier_review

    @pytest.mark.asyncio
    async def test_project_users_with_dynamic_schema(self, single_annotator_setup, multi_annotator_setup):
        """Test ProjectUsers creation works with different dynamic schemas."""
        
        # Test single annotator project users
        single_db = single_annotator_setup["project_db"]
        single_user = ProjectUsers(
            user_id=101,
            username="single_annotator_user",
            role="annotator_1",
            current_batch=None,
            completed_batches=[]
        )
        single_db.add(single_user)
        await single_db.commit()
        await single_db.refresh(single_user)
        
        assert single_user.id is not None
        assert single_user.role == "annotator_1"
        
        # Test multi annotator project users
        multi_db = multi_annotator_setup["project_db"]
        multi_users = [
            ProjectUsers(
                user_id=201,
                username="multi_annotator_1",
                role="annotator_1",
                current_batch=None,
                completed_batches=[]
            ),
            ProjectUsers(
                user_id=202,
                username="multi_annotator_2", 
                role="annotator_2",
                current_batch=None,
                completed_batches=[]
            ),
            ProjectUsers(
                user_id=203,
                username="multi_annotator_3",
                role="annotator_3",
                current_batch=None,
                completed_batches=[]
            ),
            ProjectUsers(
                user_id=204,
                username="multi_verifier",
                role="verifier",
                current_batch=None,
                completed_batches=[]
            )
        ]
        
        for user in multi_users:
            multi_db.add(user)
        await multi_db.commit()
        
        # Verify all users were created
        result = await multi_db.execute(select(ProjectUsers))
        created_users = result.scalars().all()
        assert len(created_users) == 4
        
        # Verify roles match expected patterns
        roles = [user.role for user in created_users]
        assert "annotator_1" in roles
        assert "annotator_2" in roles
        assert "annotator_3" in roles
        assert "verifier" in roles

    async def _verify_dynamic_schema_columns(self, project_db: AsyncSession, expected_annotators: int, has_verifier: bool):
        """Verify that the database schema has the expected dynamic columns."""
        
        # Check allocation_batches table
        result = await project_db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'allocation_batches' 
            AND column_name LIKE 'annotator_%'
            ORDER BY column_name
        """))
        annotator_columns = [row[0] for row in result.fetchall()]
        
        # Verify correct number of annotator columns
        expected_annotator_columns = [f"annotator_{i}" for i in range(1, expected_annotators + 1)]
        assert annotator_columns == expected_annotator_columns, f"Expected {expected_annotator_columns}, got {annotator_columns}"
        
        # Check verifier column
        verifier_result = await project_db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'allocation_batches' 
            AND column_name = 'verifier'
        """))
        verifier_columns = [row[0] for row in verifier_result.fetchall()]
        
        if has_verifier:
            assert "verifier" in verifier_columns, "Verifier column should exist"
        else:
            assert "verifier" not in verifier_columns, "Verifier column should not exist"
        
        # Check file_allocations table
        file_alloc_result = await project_db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'file_allocations' 
            AND (column_name LIKE 'annotator_%' OR column_name = 'verifier' OR column_name LIKE '%_review')
            ORDER BY column_name
        """))
        file_alloc_columns = [row[0] for row in file_alloc_result.fetchall()]
        
        # Verify annotator and review columns in file_allocations
        for i in range(1, expected_annotators + 1):
            assert f"annotator_{i}" in file_alloc_columns, f"Missing annotator_{i} column in file_allocations"
            assert f"annotator_{i}_review" in file_alloc_columns, f"Missing annotator_{i}_review column in file_allocations"
        
        if has_verifier:
            assert "verifier" in file_alloc_columns, "Missing verifier column in file_allocations"
            assert "verifier_review" in file_alloc_columns, "Missing verifier_review column in file_allocations"
