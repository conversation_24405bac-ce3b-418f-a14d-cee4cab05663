from pydantic import BaseModel, <PERSON>, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime

class ClientInfo(BaseModel):
    """Schema for client information in project context"""
    id: int
    name: str
    username: str
    email: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class AllocationStrategyInfo(BaseModel):
    """Schema for allocation strategy information in project context"""
    id: int
    strategy_name: str
    strategy_type: Optional[str] = None
    description: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class ProjectRegistryResponse(BaseModel):
    """Complete schema for project registry response"""
    id: int
    project_code: str
    project_name: str
    project_type: str
    client_id: int  # Foreign key to clients.id (integer)
    client: Optional[ClientInfo] = None

    # Database Information
    database_name: str
    database_host: str
    database_port: int
    database_connection_params: Optional[Dict[str, Any]] = None

    # Source Data Location
    folder_path: Optional[str] = None
    credentials: Optional[Dict[str, Any]] = None
    batch_size: Optional[int] = None
    allocation_strategy_id: Optional[int] = None
    allocation_strategy: Optional[AllocationStrategyInfo] = None

    # Project Configuration
    annotation_requirements: Optional[Dict[str, Any]] = None
    instructions: Optional[str] = None

    # Project Status & Management
    project_status: str
    priority_level: int

    # Performance Statistics
    total_files: int = 0
    total_batches: int = 0
    completed_files: int = 0
    active_annotators: int = 0

    # Timeline & Deadlines
    project_deadline: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_sync_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class ProjectListResponse(BaseModel):
    """Schema for paginated project list response"""
    projects: List[ProjectRegistryResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

    model_config = ConfigDict(from_attributes=True)

class ProjectFilterRequest(BaseModel):
    """Schema for filtering projects"""
    client_id: Optional[int] = None
    project_type: Optional[str] = None
    project_status: Optional[str] = None
    search: Optional[str] = None
    page: int = 1
    page_size: int = 10

    model_config = ConfigDict(from_attributes=True)

class ProjectCreateRequest(BaseModel):
    """Schema for creating a new project"""
    project_name: str
    project_type: str
    client_id: int
    database_name: str
    folder_path: Optional[str] = None
    batch_size: Optional[int] = 10
    allocation_strategy_id: Optional[int] = None
    annotation_requirements: Optional[Dict[str, Any]] = None
    instructions: Optional[str] = None
    project_status: str = "inactive"
    priority_level: int = 1
    project_deadline: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class ProjectUpdateRequest(BaseModel):
    """Schema for updating a project"""
    project_name: Optional[str] = None
    project_type: Optional[str] = None
    folder_path: Optional[str] = None
    batch_size: Optional[int] = None
    allocation_strategy_id: Optional[int] = None
    annotation_requirements: Optional[Dict[str, Any]] = None
    instructions: Optional[str] = None
    project_status: Optional[str] = None
    priority_level: Optional[int] = None
    project_deadline: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)
