import logging
import os
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import io
from datetime import datetime
import uuid
from minio import Minio #type:ignore

from cache.redis_connector import cache_get
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.config import get_settings
from post_db.master_models.allocation_strategies import AllocationStrategies

logger = logging.getLogger(__name__)

class CSVBatchProcessor:
    """
    Processes CSV files to create annotation batches based on selected columns.
    """
    
    def __init__(self):
        # Get MinIO settings from centralized config
        self.settings = get_settings()
        self.minio_config = self.settings.minio_settings
        
        # MinIO connection settings from config
        self.endpoint = self.minio_config.endpoint
        self.access_key = self.minio_config.access_key
        self.secret_key = self.minio_config.secret_key
        self.bucket_name = self.minio_config.csv_bucket_name
        self.region = self.minio_config.region
        self.secure = self.minio_config.secure
        
    async def get_csv_configuration(self, project_code: str) -> Optional[Dict[str, Any]]:
        """
        Get CSV configuration from Redis.
        
        Args:
            project_code: The project code
            
        Returns:
            Dict with CSV configuration or None if not found
        """
        try:
            redis_key = f"csv_config:{project_code}"
            config = await cache_get(redis_key, json_decode=True)
            
            if config:
                logger.info(f"Retrieved CSV configuration for project {project_code}: {config}")
                return config
            else:
                logger.warning(f"No CSV configuration found in Redis for project {project_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving CSV configuration for project {project_code}: {str(e)}")
            return None
    
    async def get_csv_file_path(self, project_code: str, db: AsyncSession) -> Optional[str]:
        """
        Get the CSV file path from the project registry.
        
        Args:
            project_code: The project code
            db: Database session
            
        Returns:
            CSV file path in MinIO or None if not found
        """
        try:
            query = select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            result = await db.execute(query)
            project = result.scalar_one_or_none()
            
            if not project:
                logger.error(f"Project {project_code} not found")
                return None
                
            if project.project_type != 'csv':
                logger.error(f"Project {project_code} is not a CSV project")
                return None
            
            # The folder_path should contain the CSV file path
            folder_path = project.folder_path
            logger.info(f"Retrieved folder_path for project {project_code}: {folder_path}")
            
            if folder_path:
                # Handle both formats: full path or just the filename
                if folder_path.startswith('csv-uploads/'):
                    return folder_path
                elif '/' in folder_path:
                    # If it's already a full path, use it
                    return folder_path
                else:
                    # If it's just a filename, construct the full path
                    constructed_path = f"csv-uploads/{project_code}/{folder_path}"
                    logger.info(f"Constructed CSV file path: {constructed_path}")
                    return constructed_path
            else:
                logger.error(f"No CSV file path found for project {project_code}: folder_path is {folder_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting CSV file path for project {project_code}: {str(e)}")
            return None
    
    async def read_csv_from_minio(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        Read CSV file from MinIO storage directly into memory.
        
        Args:
            file_path: Path to the CSV file in MinIO
            
        Returns:
            DataFrame with CSV data or None if failed
        """
        try:
            # Create MinIO client using config settings
            minio_client = Minio(
                endpoint=self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure,
                region=self.region if self.region else None
            )
            
            logger.info(f"Reading CSV file from MinIO: {file_path}")
            
            # Get the object from MinIO directly into memory
            response = minio_client.get_object(self.bucket_name, file_path)
            file_content = response.read()
            response.close()
            response.release_conn()
            
            logger.info(f"Successfully downloaded CSV file from MinIO, size: {len(file_content)} bytes")
            
            # Read CSV with different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None
            
            for encoding in encodings:
                try:
                    # Create BytesIO buffer from file content
                    file_buffer = io.BytesIO(file_content)
                    df = pd.read_csv(file_buffer, encoding=encoding)
                    logger.info(f"Successfully read CSV with encoding: {encoding}")
                    break
                except (UnicodeDecodeError, pd.errors.ParserError) as e:
                    logger.debug(f"Failed to read with encoding {encoding}: {str(e)}")
                    continue
            
            if df is None:
                logger.error(f"Unable to read CSV file {file_path} with any supported encoding")
                return None
            
            logger.info(f"Successfully loaded CSV: {len(df)} rows, {len(df.columns)} columns")
            return df
            
        except Exception as e:
            logger.error(f"Error reading CSV from MinIO: {str(e)}")
            return None
    
    def create_batches_from_column(
        self, 
        df: pd.DataFrame, 
        column_name: str, 
        batch_size: int,
        project_code: str
    ) -> List[Dict[str, Any]]:
        """
        Create batches from a specific column in the DataFrame.
        
        Args:
            df: The DataFrame containing CSV data
            column_name: Name of the column to create batches from
            batch_size: Number of cells per batch
            project_code: The project code
            
        Returns:
            List of batch dictionaries
        """
        try:
            if column_name not in df.columns:
                logger.error(f"Column '{column_name}' not found in CSV. Available columns: {list(df.columns)}")
                return []
            
            # Get the column data and drop NaN values
            column_data = df[column_name].dropna().astype(str).tolist()
            
            if not column_data:
                logger.error(f"No data found in column '{column_name}' after removing NaN values")
                return []
            
            logger.info(f"Creating batches from column '{column_name}' with {len(column_data)} cells, batch size: {batch_size}")
            
            batches = []
            
            # Create batches
            for i in range(0, len(column_data), batch_size):
                batch_cells = column_data[i:i + batch_size]
                batch_number = i//batch_size + 1
                batch_id = f"BATCH_{batch_number:03d}_CSV"
                
                # Create file entries for each cell
                files = []
                for j, cell_value in enumerate(batch_cells):
                    cell_number = j + 1
                    file_entry = {
                        "file_identifier": f"CELL_{cell_number:03d}",
                        "original_filename": f"cell_{cell_number:03d}.txt",
                        "file_type": "text",
                        "file_extension": ".txt",
                        "cell_value": cell_value,
                        "row_index": i + j,
                        "column_name": column_name,
                        "sequence_order": j + 1
                    }
                    files.append(file_entry)
                
                batch_info = {
                    "batch_identifier": batch_id,
                    "batch_status": "created",
                    "total_files": len(files),
                    "files": files,
                    "batch_metadata": {
                        "source_column": column_name,
                        "start_row": i,
                        "end_row": i + len(batch_cells) - 1,
                        "created_at": datetime.now().isoformat(),
                        "project_code": project_code
                    }
                }
                
                batches.append(batch_info)
                logger.debug(f"Created batch {batch_id} with {len(files)} cells")
            
            logger.info(f"Successfully created {len(batches)} batches from column '{column_name}'")
            return batches
            
        except Exception as e:
            logger.error(f"Error creating batches from column '{column_name}': {str(e)}")
            return []
    
    async def process_csv_for_project(
        self, 
        project_code: str, 
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Process CSV file for a project and create batches.
        
        Args:
            project_code: The project code
            db: Database session
            
        Returns:
            Dict with processing results
        """
        try:
            # Get CSV configuration from Redis
            config = await self.get_csv_configuration(project_code)
            if not config:
                return {
                    "success": False,
                    "error": "CSV configuration not found. Please configure the CSV project first."
                }
            
            # Get project and allocation strategy information
            strategy_name = "CSV"  # Default fallback
            try:
                project_query = select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                project_result = await db.execute(project_query)
                project = project_result.scalar_one_or_none()
                
                if project and project.allocation_strategy_id:
                    strategy_query = select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                    strategy_result = await db.execute(strategy_query)
                    strategy = strategy_result.scalar_one_or_none()
                                        
            except Exception as e:
                logger.warning(f"Could not get allocation strategy for project {project_code}: {str(e)}. Using default 'CSV'")
            
            # Get CSV file path from project registry
            csv_file_path = await self.get_csv_file_path(project_code, db)
            if not csv_file_path:
                return {
                    "success": False,
                    "error": "CSV file path not found in project registry"
                }
            
            # Read CSV from MinIO
            df = await self.read_csv_from_minio(csv_file_path)
            if df is None:
                return {
                    "success": False,
                    "error": "Failed to read CSV file from storage"
                }
            
            # Create batches from selected column
            selected_column = config["selected_column"]
            batch_size = config["records_per_batch"]
            
            batches = self.create_batches_from_column(
                df, selected_column, batch_size, project_code
            )
            
            if not batches:
                return {
                    "success": False,
                    "error": f"Failed to create batches from column '{selected_column}'"
                }
            
            # Calculate statistics
            total_cells = sum(batch["total_files"] for batch in batches)
            
            return {
                "success": True,
                "batches": batches,
                "statistics": {
                    "total_batches": len(batches),
                    "total_cells": total_cells,
                    "selected_column": selected_column,
                    "batch_size": batch_size,
                    "csv_rows": len(df),
                    "csv_columns": len(df.columns)
                },
                "message": f"Successfully processed CSV and created {len(batches)} batches with {total_cells} cells"
            }
            
        except Exception as e:
            logger.error(f"Error processing CSV for project {project_code}: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to process CSV: {str(e)}"
            }
