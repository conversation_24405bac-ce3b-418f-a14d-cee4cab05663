"use client";

import { MediaType, MediaViewerProps, detectMediaType, getMediaEndpoint } from "./types";
import ImageViewer from "./ImageViewer";
import VideoViewer from "./VideoViewer";
import AudioViewer from "./AudioViewer";
import PDFViewer from "./PDFViewer";
import TextViewer from "./TextViewer";
import CSVViewer from "./CSVViewer";
import ZoomControls from "./ZoomControls";

interface UnifiedMediaViewerProps extends Omit<MediaViewerProps, 'mediaType' | 'mediaUrl'> {
  mediaPath: string;
  mediaType?: MediaType;
  isLabeled?: boolean;
  showZoomControls?: boolean;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onResetZoom?: () => void;
  batchId?: number;
  projectCode?: string;
}

export default function MediaViewer({
  mediaPath,
  mediaType,
  zoomLevel = 100,
  isLabeled = false,
  showZoomControls = true,
  onLoad,
  onError,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  batchId,
  projectCode
}: UnifiedMediaViewerProps) {
  // Auto-detect media type if not provided
  const detectedType = mediaType || detectMediaType(mediaPath);
  
  // Get the correct endpoint URL
  const mediaUrl = getMediaEndpoint(detectedType, mediaPath);
  
  console.log('MediaViewer: mediaPath:', mediaPath);
  console.log('MediaViewer: detectedType:', detectedType);
  console.log('MediaViewer: generated mediaUrl:', mediaUrl);

  const renderMediaViewer = () => {
    const commonProps = {
      mediaUrl,
      zoomLevel,
      onLoad,
      onError,
      isLabeled
    };

    switch (detectedType) {
      case 'video':
        return <VideoViewer {...commonProps} />;
      case 'audio':
        return <AudioViewer {...commonProps} />;
      case 'pdf':
        return <PDFViewer {...commonProps} />;
      case 'text':
        return <TextViewer {...commonProps} />;
      case 'csv':
        return <CSVViewer {...commonProps} mediaUrl={mediaPath} batchId={batchId} projectCode={projectCode} />;
      case 'image':
      default:
        return <ImageViewer {...commonProps} />;
    }
  };

  // Zoom controls are only applicable for certain media types
  const supportsZoom = ['image', 'pdf', 'text'].includes(detectedType);
  const shouldShowZoomControls = showZoomControls && supportsZoom && onZoomIn && onZoomOut && onResetZoom;

  return (
    <div className="relative bg-gray-50 rounded-lg shadow-sm h-96 flex justify-center items-center overflow-hidden">
      {renderMediaViewer()}
      
      {shouldShowZoomControls && (
        <ZoomControls
          zoomLevel={zoomLevel}
          onZoomIn={onZoomIn}
          onZoomOut={onZoomOut}
          onResetZoom={onResetZoom}
        />
      )}
    </div>
  );
}
