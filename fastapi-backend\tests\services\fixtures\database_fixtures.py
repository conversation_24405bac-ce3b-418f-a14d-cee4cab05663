"""
Database-related fixtures for testing across all service tests.
"""

import pytest
from unittest.mock import AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession


@pytest.fixture
def mock_db_session():
    """Create mock database session - centralized version."""
    mock_session = AsyncMock(spec=AsyncSession)
    return mock_session


@pytest.fixture
def mock_master_db_session():
    """Create mock master database session."""
    mock_session = AsyncMock(spec=AsyncSession)
    return mock_session


@pytest.fixture
async def real_master_db_session():
    """Real master database session for integration testing."""
    from app.post_db.master_db import MasterSessionLocal
    async with MasterSessionLocal() as session:
        yield session


@pytest.fixture
async def real_project_db_session():
    """Real project database session for integration testing."""
    # This would create a real project database session
    # Implementation depends on your project database setup
    mock_session = AsyncMock(spec=AsyncSession)
    yield mock_session
