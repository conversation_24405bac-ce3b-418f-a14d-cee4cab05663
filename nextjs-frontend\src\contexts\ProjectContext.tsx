'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

interface ProjectContextType {
  projectCode: string | null;
  setProjectCode: (code: string | null) => void;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export function ProjectProvider({ children }: { children: ReactNode }) {
  const [projectCode, setProjectCode] = useState<string | null>(null);

  return (
    <ProjectContext.Provider value={{ projectCode, setProjectCode }}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProject() {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
}
