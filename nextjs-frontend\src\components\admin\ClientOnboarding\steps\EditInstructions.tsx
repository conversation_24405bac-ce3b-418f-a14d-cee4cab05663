// steps/EditInstructions.tsx - Project-based instructions editor
import React, { useEffect } from 'react';
import { FaEdit, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaArrowLeft, FaArrowRight, FaCheckCircle, FaInfoCircle, FaProjectDiagram, FaPlus } from 'react-icons/fa';
import { Client } from '../types';

interface EditInstructionsProps {
  selectedClient: Client | null;
  instructions: string;
  setInstructions: (instructions: string) => void;
  savingInstructions: boolean;
  selectedProjectCode: string;
  projectsList: any[];
  loadingProjects: boolean;
  currentProjectData: any;
  fetchProjects: (clientId?: string) => Promise<void>;
  onSaveInstructions: () => Promise<{ success: boolean; projectCode?: string }>;
  handleProjectChange: (projectCode: string) => void;
  onGoToStep: (step: number, projectCode?: string) => void;
  isStepCompleted: boolean;
  markStepCompleted: (stepId: number) => void;
  projectCode?: string; // Optional project code passed from previous step
}

export const EditInstructions: React.FC<EditInstructionsProps> = ({
  selectedClient,
  instructions,
  setInstructions,
  savingInstructions,
  selectedProjectCode,
  projectsList,
  loadingProjects,
  currentProjectData,
  fetchProjects,
  onSaveInstructions,
  handleProjectChange,
  onGoToStep,
  isStepCompleted,
  markStepCompleted,
  projectCode,
}) => {
  // Add dummy instructions function
  const addDummyInstructions = () => {
    const sampleInstructions = `# Project Instructions for Annotators

## Overview
Welcome to this document annotation project. Your task is to carefully review and annotate each document according to the guidelines below.

## What You Need to Do

### 1. Document Review
- Read through each document completely before starting annotation
- Pay attention to key information such as dates, names, amounts, and signatures
- Look for any anomalies or inconsistencies in the document

### 2. Quality Assessment
- Evaluate the overall quality and legibility of the document
- Note any areas that are unclear, damaged, or difficult to read
- Consider the completeness of the information provided

### 3. Information Extraction
- Identify and mark all relevant data points as specified in the annotation form
- Ensure accuracy when extracting numerical values, dates, and names
- Double-check your entries for spelling and formatting

### 4. Additional Notes
- Provide detailed observations about anything unusual or noteworthy
- Include context that might be helpful for reviewers
- Mention any assumptions you made during the annotation process

## Important Guidelines

### Accuracy First
- Take your time to ensure accuracy over speed
- When in doubt, flag the item for review rather than guessing
- Cross-reference information within the document when possible

### Consistency
- Follow the same approach for similar types of documents
- Use standardized formats for dates, amounts, and other data
- Maintain consistent terminology throughout your annotations

### Communication
- Contact your supervisor if you encounter unclear instructions
- Report technical issues immediately
- Ask questions rather than making assumptions

## Quality Standards
- Aim for 95%+ accuracy in your annotations
- Complete all required fields in the annotation form
- Provide meaningful comments in the notes section

Thank you for your attention to detail and commitment to quality!`;

    setInstructions(sampleInstructions);
  };
  // Fetch projects when component mounts or client changes
  useEffect(() => {
    if (selectedClient) {
      fetchProjects(selectedClient.id?.toString());
    }
  }, [selectedClient]);

  // Set project code from previous step if available
  useEffect(() => {
    if (projectCode && !selectedProjectCode) {
      handleProjectChange(projectCode);
    }
  }, [projectCode, selectedProjectCode]);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4 flex items-center">
        <FaEdit className="mr-2 text-orange-500" />
        Edit Project Instructions
      </h3>

      {selectedClient && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-sm text-blue-800">
            <strong>Selected Client:</strong> {selectedClient.full_name} (@
            {selectedClient.username})
          </div>
        </div>
      )}

      {/* Project Selection Dropdown - Only show if no projectCode from previous step */}
      {!projectCode && (
        <div className="mb-4">
          <label htmlFor="projectSelect" className="block text-sm font-medium mb-2 flex items-center">
            <FaProjectDiagram className="mr-2 text-blue-500" />
            Select Project
          </label>
          <select
            className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            id="projectSelect"
            value={selectedProjectCode}
            onChange={(e) => handleProjectChange(e.target.value)}
            disabled={!projectsList.length || loadingProjects}
          >
            <option value="">-- Select Project --</option>
            {projectsList.map(project => (
              <option key={project.project_code} value={project.project_code}>
                {project.project_name} ({project.project_code})
              </option>
            ))}
          </select>
          {loadingProjects && (
            <div className="text-sm text-blue-600 mt-1">Loading projects...</div>
          )}
          {!projectsList.length && !loadingProjects && (
            <div className="text-sm text-amber-600 mt-1">No projects available.</div>
          )}
        </div>
      )}

      {/* Show selected project info when projectCode is provided */}
      {projectCode && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center text-green-800">
            <FaCheckCircle className="mr-2 text-green-600" />
            <div>
              <div className="text-xs text-green-600 mt-1">
                Using project code: <strong>{projectCode}</strong>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Project Details */}
      {currentProjectData && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-md font-semibold mb-2">Project Details</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div><strong>Type:</strong> {currentProjectData.project_type}</div>
            <div><strong>Files:</strong> {currentProjectData.total_files}</div>
            <div><strong>Batch Size:</strong> {currentProjectData.batch_size || "Default"}</div>
            <div><strong>Status:</strong> {currentProjectData.project_status}</div>
          </div>
        </div>
      )}

      {/* Instructions Text Area */}
      {selectedProjectCode && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium">
              Project Instructions
            </label>
            <button
              type="button"
              onClick={addDummyInstructions}
              className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm flex items-center shadow-sm"
              title="Add sample instructions to get started quickly"
            >
              <FaPlus className="mr-1 text-xs" />
              Add Sample Instructions
            </button>
          </div>
          <textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            rows={12}
            className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter project instructions for annotators..."
          />
          <div className="text-sm text-gray-500 mt-1">
            <FaInfoCircle className="inline mr-1" />
            These instructions will be shown to annotators when working on this project.
            {!instructions.trim() && (
              <span className="ml-2 text-blue-600">
                💡 Tip: Use "Add Sample Instructions" to get started with a comprehensive template.
              </span>
            )}
          </div>
        </div>
      )}

      {/* Submit Button */}
      {selectedProjectCode && (
        <button
          onClick={onSaveInstructions}
          disabled={savingInstructions || !instructions.trim()}
          className="w-full px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50 flex items-center justify-center"
        >
          {savingInstructions ? (
            <FaSpinner className="animate-spin mr-2" />
          ) : (
            <FaSave className="mr-2" />
          )}
          Save Instructions
        </button>
      )}

      <div className="flex justify-between mt-6">
        <button
          onClick={() => onGoToStep(3)}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Back to Dataset Selection
        </button>

        {isStepCompleted && (
          <button
            onClick={() => onGoToStep(5, selectedProjectCode)}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
          >
            Continue to Requirements
            <FaArrowRight className="ml-2" />
          </button>
        )}

        
      </div>
    </div>
  );
};