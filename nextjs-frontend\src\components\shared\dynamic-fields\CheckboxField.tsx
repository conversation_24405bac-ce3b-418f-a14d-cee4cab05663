import React from 'react';
import { FormFieldConfig } from './DynamicField';

interface CheckboxFieldProps {
  config: FormFieldConfig;
  value: string[];
  onChange: (fieldName: string, value: any) => void;
  error?: string;
}

export default function CheckboxField({ 
  config, 
  value, 
  onChange, 
  error 
}: CheckboxFieldProps) {
  const handleChange = (option: string, checked: boolean) => {
    const currentValues = Array.isArray(value) ? value : [];
    
    if (checked) {
      // Add option if checked
      if (!currentValues.includes(option)) {
        onChange(config.field_name, [...currentValues, option]);
      }
    } else {
      // Remove option if unchecked
      onChange(config.field_name, currentValues.filter(v => v !== option));
    }
  };

  if (!config.options || config.options.length === 0) {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {config.label}
          {config.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className="text-sm text-gray-500">No options configured for this field</div>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {config.label}
        {config.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {config.description && (
        <p className="text-sm text-gray-500 mb-2">{config.description}</p>
      )}
      <div className="space-y-2">
        {config.options.map((option, index) => (
          <label key={index} className="flex items-center">
            <input
              type="checkbox"
              id={`${config.field_name}_${index}`}
              value={option}
              checked={Array.isArray(value) ? value.includes(option) : false}
              onChange={(e) => handleChange(option, e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">{option}</span>
          </label>
        ))}
      </div>
      {config.required && (!value || value.length === 0) && (
        <p className="mt-1 text-sm text-gray-500">
          Please select at least one option
        </p>
      )}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
