import React from 'react';
import { FaUserPlus, FaUserCog, FaTimes } from 'react-icons/fa';
import { 
  UserInfo, 
  AssignedUserInfo, 
  StrategyDetails, 
  ProjectRegistryResponse 
} from '../types';

interface UserAssignmentSectionProps {
  project: ProjectRegistryResponse;
  strategyDetails: StrategyDetails | null;
  // Annotators
  availableAnnotators: UserInfo[];
  assignedAnnotators: AssignedUserInfo[];
  selectedAnnotators: number[];
  // Verifiers
  availableVerifiers: UserInfo[];
  assignedVerifiers: AssignedUserInfo[];
  selectedVerifiers: number[];
  // Loading states
  loadingUsers: boolean;
  loadingAssignedUsers: boolean;
  assignmentLoading: boolean;
  // Actions
  onToggleUserSelection: (role: 'annotators' | 'verifiers', userId: number) => void;
  onAssignUsers: (role: 'annotators' | 'verifiers', userIds: number[]) => void;
  onRemoveUser: (role: 'annotators' | 'verifiers', userId: number) => void;
}

export const UserAssignmentSection: React.FC<UserAssignmentSectionProps> = ({
  project,
  strategyDetails,
  availableAnnotators,
  assignedAnnotators,
  selectedAnnotators,
  availableVerifiers,
  assignedVerifiers,
  selectedVerifiers,
  loadingUsers,
  loadingAssignedUsers,
  assignmentLoading,
  onToggleUserSelection,
  onAssignUsers,
  onRemoveUser,
}) => {
  if (project.project_status !== 'active') {
    return (
      <div className="border rounded-md p-4 bg-gray-50">
        <h3 className="text-lg font-semibold mb-3 flex items-center text-gray-600">
          <FaUserPlus className="mr-2" />
          User Assignment
        </h3>
        <p className="text-gray-600">User assignment is only available for active projects. Please activate the project first.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Annotator Assignment */}
      <div className="border rounded-md p-4">
        <h3 className="text-lg font-semibold mb-3 flex items-center">
          <FaUserPlus className="mr-2" />
          Assign Annotators
          <span className="ml-2 text-sm font-normal text-gray-500">
            (Required: {strategyDetails?.num_annotators || 'N/A'})
          </span>
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {!strategyDetails ? 'Strategy details not available' :
            assignedAnnotators.length >= strategyDetails.num_annotators 
              ? `You have ${assignedAnnotators.length} annotator${assignedAnnotators.length > 1 ? 's' : ''} assigned (minimum: ${strategyDetails.num_annotators}). You can assign additional annotators if needed.`
              : `Select at least ${strategyDetails.num_annotators - assignedAnnotators.length} more annotator${strategyDetails.num_annotators - assignedAnnotators.length > 1 ? 's' : ''} to meet the strategy requirements.`
          }
        </p>
        
        {/* Already assigned annotators */}
        {loadingAssignedUsers ? (
          <div className="p-3 bg-gray-50 rounded-md mb-4">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-center mt-1 text-xs text-gray-600">Loading assigned annotators...</p>
          </div>
        ) : assignedAnnotators.length > 0 ? (
          <div className="p-3 bg-gray-50 rounded-md mb-4">
            <h4 className="text-sm font-medium mb-2">Currently Assigned Annotators ({assignedAnnotators.length})</h4>
            <div className="max-h-32 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {assignedAnnotators.map(user => (
                  <div key={user.id} className="flex items-center p-2 border rounded-md bg-white">
                    <div className="text-sm flex-1">
                      <div className="font-medium">{user.full_name || user.username}</div>
                      <div className="text-xs text-gray-500">
                        Status: {user.is_active ? 'Active' : 'Inactive'} | 
                        Assigned: {new Date(user.assigned_at).toLocaleDateString()}
                      </div>
                    </div>
                    <button
                      onClick={() => onRemoveUser('annotators', user.id)}
                      disabled={assignmentLoading}
                      className="ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Remove annotator"
                    >
                      <FaTimes className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="p-3 bg-gray-50 rounded-md mb-4">
            <p className="text-sm text-gray-600">No annotators assigned yet.</p>
          </div>
        )}
        
        {/* Available annotators to assign */}
        <h4 className="text-sm font-medium mb-2">Available Annotators</h4>
        {loadingUsers ? (
          <div className="p-4 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Loading available annotators...</p>
          </div>
        ) : availableAnnotators.length === 0 ? (
          <p className="text-sm text-gray-600">No available annotators found.</p>
        ) : (
          <>
            <div className="max-h-40 overflow-y-auto mb-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {availableAnnotators.map(user => (
                  <div key={user.id} className="flex items-center p-2 border rounded-md">
                    <input
                      type="checkbox"
                      id={`annotator-${user.id}`}
                      checked={selectedAnnotators.includes(user.id)}
                      onChange={() => onToggleUserSelection('annotators', user.id)}
                      className="mr-2"
                    />
                    <label htmlFor={`annotator-${user.id}`} className="text-sm flex-1">
                      <div className="font-medium">{user.full_name || user.username}</div>
                      <div className="text-xs text-gray-500">{user.username}</div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <button
              onClick={() => onAssignUsers('annotators', selectedAnnotators)}
              disabled={assignmentLoading || selectedAnnotators.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {assignmentLoading ? 'Assigning...' : 'Assign Selected Annotators'}
              {strategyDetails && (assignedAnnotators.length + selectedAnnotators.length) < strategyDetails.num_annotators && selectedAnnotators.length > 0 && (
                <span className="block text-xs text-red-200 mt-1">
                  Need {strategyDetails.num_annotators - (assignedAnnotators.length + selectedAnnotators.length)} more
                </span>
              )}
            </button>
          </>
        )}
      </div>
      
      {/* Verifier Assignment - Only if verification is required */}
      {strategyDetails?.requires_verification && (
        <div className="border rounded-md p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <FaUserCog className="mr-2" />
            Assign Verifiers
            <span className="ml-2 text-sm font-normal text-gray-500">
              (Required: At least 1)
            </span>
          </h3>
          
          {/* Already assigned verifiers */}
          {loadingAssignedUsers ? (
            <div className="p-3 bg-gray-50 rounded-md mb-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-center mt-1 text-xs text-gray-600">Loading assigned verifiers...</p>
            </div>
          ) : assignedVerifiers.length > 0 ? (
            <div className="p-3 bg-gray-50 rounded-md mb-4">
              <h4 className="text-sm font-medium mb-2">Currently Assigned Verifiers ({assignedVerifiers.length})</h4>
              <div className="max-h-32 overflow-y-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {assignedVerifiers.map(user => (
                    <div key={user.id} className="flex items-center p-2 border rounded-md bg-white">
                      <div className="text-sm flex-1">
                        <div className="font-medium">{user.full_name || user.username}</div>
                        <div className="text-xs text-gray-500">
                          Status: {user.is_active ? 'Active' : 'Inactive'} | 
                          Assigned: {new Date(user.assigned_at).toLocaleDateString()}
                        </div>
                      </div>
                      <button
                        onClick={() => onRemoveUser('verifiers', user.id)}
                        disabled={assignmentLoading}
                        className="ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                        title="Remove verifier"
                      >
                        <FaTimes className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="p-3 bg-gray-50 rounded-md mb-4">
              <p className="text-sm text-gray-600">No verifiers assigned yet.</p>
            </div>
          )}
          
          {/* Available verifiers to assign */}
          <h4 className="text-sm font-medium mb-2">Available Verifiers</h4>
          {loadingUsers ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">Loading available verifiers...</p>
            </div>
          ) : availableVerifiers.length === 0 ? (
            <p className="text-sm text-gray-600">No available verifiers found.</p>
          ) : (
            <>
              <div className="max-h-40 overflow-y-auto mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {availableVerifiers.map(user => (
                    <div key={user.id} className="flex items-center p-2 border rounded-md">
                      <input
                        type="checkbox"
                        id={`verifier-${user.id}`}
                        checked={selectedVerifiers.includes(user.id)}
                        onChange={() => onToggleUserSelection('verifiers', user.id)}
                        className="mr-2"
                      />
                      <label htmlFor={`verifier-${user.id}`} className="text-sm flex-1">
                        <div className="font-medium">{user.full_name || user.username}</div>
                        <div className="text-xs text-gray-500">{user.username}</div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <button
                onClick={() => onAssignUsers('verifiers', selectedVerifiers)}
                disabled={assignmentLoading || selectedVerifiers.length === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {assignmentLoading ? 'Assigning...' : 'Assign Selected Verifiers'}
              </button>
            </>
          )}
        </div>
      )}
    </div>
  );
};
