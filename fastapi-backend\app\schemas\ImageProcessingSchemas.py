from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class ImageBatchBase(BaseModel):
    batch_name: str
    images: List[str]
    image_count: int
    status: str = "available"

class ProcessedManualBatch(ImageBatchBase):
    id: int
    username: Optional[str] = None
    assigned_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProcessedVerificationBatch(ImageBatchBase):
    id: int
    username: Optional[str] = None
    assigned_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class BatchAssignmentRequest(BaseModel):
    username: str
    batch_name: str