"""
Repository for project database operations.
Handles database connections and CRUD operations for project-specific tables.
"""

import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.session_manager import get_project_db_session
from post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from post_db.allocation_models.files_registry import FilesRegistry
from post_db.allocation_models.file_allocations import FileAllocations
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class ProjectDBRepository:
    """Repository for project database operations."""
    
    def __init__(self):
        pass
        
    async def create_allocation_batch(self, 
                                     project_code: str, 
                                     batch_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create an allocation batch in the project database.
        
        Args:
            project_code: The project code
            batch_data: Dictionary containing batch information
            
        Returns:
            Dict: Created batch information
        """
        try:
            async with get_project_db_session(project_code) as session:
                # Create AllocationBatches object
                batch = AllocationBatches(
                    batch_identifier=batch_data['batch_identifier'],
                    total_files=batch_data['total_files'],
                    file_list=batch_data['file_list'],
                    is_priority=batch_data.get('is_priority', False),
                    annotation_count=batch_data.get('annotation_count', 0),
                    custom_batch_config=batch_data.get('custom_batch_config')
                )
                
                session.add(batch)
                await session.commit()
                await session.refresh(batch)
                
                # Convert to dict for return
                return {
                    'id': batch.id,
                    'batch_identifier': batch.batch_identifier,
                    'batch_status': batch.batch_status,
                    'total_files': batch.total_files,
                    'created_at': batch.created_at
                }
                
        except Exception as e:
            logger.error(f"Error creating allocation batch for project {project_code}: {e}")
            raise
    
    async def get_allocation_batches(self, project_code: str) -> List[Dict[str, Any]]:
        """
        Get all allocation batches for a project.
        
        Args:
            project_code: The project code
            
        Returns:
            List[Dict]: List of batch information
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(select(AllocationBatches))
                batches = result.scalars().all()
                
                return [
                    {
                        'id': batch.id,
                        'batch_identifier': batch.batch_identifier,
                        'batch_status': batch.batch_status,
                        'total_files': batch.total_files,
                        'file_list': batch.file_list,
                        'created_at': batch.created_at,
                        'deadline': batch.deadline
                    }
                    for batch in batches
                ]
                
        except Exception as e:
            logger.error(f"Error getting allocation batches for project {project_code}: {e}")
            raise
    
    async def register_files(self, 
                           project_code: str, 
                           batch_id: int, 
                           files_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Register files in the project database.
        
        Args:
            project_code: The project code
            batch_id: The batch ID to associate files with
            files_data: List of file information dictionaries
            
        Returns:
            List[Dict]: List of registered file information
        """
        try:
            async with get_project_db_session(project_code) as session:
                registered_files = []
                
                for file_data in files_data:
                    file = FilesRegistry(
                        batch_id=batch_id,
                        file_identifier=file_data['file_identifier'],
                        original_filename=file_data.get('original_filename'),
                        file_type=file_data.get('file_type'),
                        file_extension=file_data.get('file_extension'),
                        storage_location=file_data.get('storage_location'),
                        file_size_bytes=file_data.get('file_size_bytes'),
                        file_hash=file_data.get('file_hash'),
                        sequence_order=file_data.get('sequence_order')
                    )
                    
                    session.add(file)
                    registered_files.append(file)
                
                await session.commit()
                
                # Refresh all files to get their IDs
                for file in registered_files:
                    await session.refresh(file)
                
                # Create file allocation entries for each registered file
                file_allocations = []
                for file in registered_files:
                    file_allocation = FileAllocations(
                        file_id=file.id,
                        batch_id=batch_id,
                        allocation_sequence=1,  # Default to 1 for initial allocation
                        workflow_phase='annotation',  # Default workflow phase
                        processing_status='pending',  # Default processing status
                        assignment_count=0,
                        completion_count=0
                    )
                    session.add(file_allocation)
                    file_allocations.append(file_allocation)
                
                # Commit file allocations
                await session.commit()
                
                # Refresh file allocations to get their IDs
                for allocation in file_allocations:
                    await session.refresh(allocation)
                
                # Update batch with total file count
                batch_query = select(AllocationBatches).where(AllocationBatches.id == batch_id)
                batch_result = await session.execute(batch_query)
                batch = batch_result.scalar_one_or_none()
                
                if batch:
                    batch.total_files = len(registered_files)
                    await session.commit()
                
                # Return file information
                return [
                    {
                        'id': file.id,
                        'batch_id': file.batch_id,
                        'file_identifier': file.file_identifier,
                        'original_filename': file.original_filename,
                        'file_type': file.file_type,
                        'file_allocation_id': file_allocations[i].id
                    }
                    for i, file in enumerate(registered_files)
                ]
                
        except Exception as e:
            logger.error(f"Error registering files for project {project_code}: {e}")
            raise
    
    async def get_files_by_batch(self, project_code: str, batch_id: int) -> List[Dict[str, Any]]:
        """
        Get all files for a specific batch.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            
        Returns:
            List[Dict]: List of file information
        """
        try:
            async with get_project_db_session(project_code) as session:
                result = await session.execute(
                    select(FilesRegistry).where(FilesRegistry.batch_id == batch_id)
                )
                files = result.scalars().all()
                
                return [
                    {
                        'id': file.id,
                        'batch_id': file.batch_id,
                        'file_identifier': file.file_identifier,
                        'original_filename': file.original_filename,
                        'file_type': file.file_type,
                        'file_extension': file.file_extension,
                        'storage_location': file.storage_location,
                        'uploaded_at': file.uploaded_at
                    }
                    for file in files
                ]
                
        except Exception as e:
            logger.error(f"Error getting files for batch {batch_id} in project {project_code}: {e}")
            raise
    
    async def update_batch_status(self, 
                                project_code: str, 
                                batch_id: int, 
                                new_status: BatchStatus) -> Dict[str, Any]:
        """
        Update the status of a batch.
        
        Args:
            project_code: The project code
            batch_id: The batch ID
            new_status: The new batch status
            
        Returns:
            Dict: Updated batch information
        """
        try:
            async with get_project_db_session(project_code) as session:
                batch_query = select(AllocationBatches).where(AllocationBatches.id == batch_id)
                batch_result = await session.execute(batch_query)
                batch = batch_result.scalar_one_or_none()
                
                if not batch:
                    raise ValueError(f"Batch with ID {batch_id} not found in project {project_code}")
                
                await session.commit()
                await session.refresh(batch)
                
                return {
                    'id': batch.id,
                    'batch_identifier': batch.batch_identifier,
                    'batch_status': batch.batch_status
                }
                
        except Exception as e:
            logger.error(f"Error updating batch status for batch {batch_id} in project {project_code}: {e}")
            raise
