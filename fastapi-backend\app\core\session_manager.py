"""
Unified database session manager for the application.
Provides centralized session management for both master and project databases.
"""

import logging
from typing import Dict, AsyncContextManager
from contextlib import asynccontextmanager
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import select

from post_db.config import DatabaseSettings, MasterDatabaseSettings
from post_db.master_models.projects_registry import ProjectsRegistry

logger = logging.getLogger(__name__)

class UnifiedSessionManager:
    """
    Unified session manager for both master and project databases.
    Provides consistent interface and connection caching for optimal performance.
    """
    
    def __init__(self):
        self.db_settings = DatabaseSettings()
        self.master_db_settings = MasterDatabaseSettings()
        self.base_db_url_async = self.db_settings.url
        self._project_engines: Dict[str, any] = {}
        self._project_session_makers: Dict[str, any] = {}
        self._master_engine = None
        self._master_session_maker = None
    
    @asynccontextmanager
    async def get_master_session(self) -> AsyncContextManager[AsyncSession]:
        """
        Get an async context manager for master database session.
        
        Usage:
            async with session_manager.get_master_session() as session:
                # use session here
        """
        session_maker = await self._get_master_session_maker()
        async with session_maker() as session:
            yield session
    
    @asynccontextmanager
    async def get_project_session(self, project_code: str) -> AsyncContextManager[AsyncSession]:
        """
        Get an async context manager for project-specific database session.
        Automatically handles connection caching and project lookup.
        
        Args:
            project_code: The project code to get session for
            
        Usage:
            async with session_manager.get_project_session("PROJ001") as session:
                # use session here
        """
        session_maker = await self._get_project_session_maker(project_code)
        async with session_maker() as session:
            yield session
    
    async def _get_project_session_maker(self, project_code: str):
        """Get or create session maker for project database"""
        if project_code in self._project_session_makers:
            return self._project_session_makers[project_code]
        
        # Get database name from master DB
        db_name = await self._get_db_name_from_master(project_code)
        
        # Create engine for this project
        engine = await self._get_project_engine(project_code, db_name)
        
        # Create and cache session maker
        session_maker = async_sessionmaker(
            bind=engine,
            expire_on_commit=False,
            class_=AsyncSession
        )
        self._project_session_makers[project_code] = session_maker
        
        return session_maker
    
    async def _get_master_session_maker(self):
        """Get or create session maker for master database"""
        if self._master_session_maker is not None:
            return self._master_session_maker
        
        # Create engine for master database
        engine = await self._get_master_engine()
        
        # Create and cache session maker
        session_maker = async_sessionmaker(
            bind=engine,
            expire_on_commit=False,
            class_=AsyncSession
        )
        self._master_session_maker = session_maker
        
        return session_maker
    
    async def _get_master_engine(self):
        """Get or create engine for master database"""
        if self._master_engine is not None:
            return self._master_engine
        
        # Create engine with optimal settings
        engine = create_async_engine(
            self.master_db_settings.url,
            pool_size=self.master_db_settings.pool_size,
            max_overflow=self.master_db_settings.max_overflow,
            pool_timeout=self.master_db_settings.pool_timeout,
            pool_recycle=self.master_db_settings.pool_recycle,
            pool_pre_ping=True,
            echo=self.master_db_settings.echo
        )
        
        self._master_engine = engine
        return engine
    
    async def _get_project_engine(self, project_code: str, db_name: str):
        """Get or create engine for project database"""
        if project_code in self._project_engines:
            return self._project_engines[project_code]
        
        # Extract the base URL without the database name
        base_url_parts = self.base_db_url_async.split('/')
        base_url = '/'.join(base_url_parts[:-1]) + '/'
        
        # Create the full URL with the correct database name
        db_url = f"{base_url}{db_name}"
        
        # Create engine with optimal settings
        engine = create_async_engine(
            db_url,
            pool_size=self.db_settings.pool_size,
            max_overflow=self.db_settings.max_overflow,
            pool_timeout=self.db_settings.pool_timeout,
            pool_recycle=self.db_settings.pool_recycle,
            pool_pre_ping=True,
            echo=self.db_settings.echo
        )
        
        self._project_engines[project_code] = engine
        return engine
    
    async def _get_db_name_from_master(self, project_code: str) -> str:
        """Get database name for project from master database"""
        async with self.get_master_session() as session:
            result = await session.execute(
                select(ProjectsRegistry).where(
                    ProjectsRegistry.project_code == project_code,
                    ProjectsRegistry.project_status.in_(['inactive', 'active', 'annotating', 'verifying', 'completed'])
                )
            )
            project = result.scalar_one_or_none()
            if not project:
                raise ValueError(f"Project {project_code} not found or has invalid status")
            return project.database_name
    
    async def validate_project(self, project_code: str) -> bool:
        """Validate if project exists and is active"""
        try:
            await self._get_db_name_from_master(project_code)
            return True
        except ValueError:
            return False
    
    def clear_project_cache(self, project_code: str = None):
        """Clear cached connections for a specific project or all projects"""
        if project_code:
            self._project_engines.pop(project_code, None)
            self._project_session_makers.pop(project_code, None)
            logger.info(f"Cleared cache for project {project_code}")
        else:
            self._project_engines.clear()
            self._project_session_makers.clear()
            logger.info("Cleared all project connection caches")

# Global singleton instance
_session_manager_instance = None

def get_session_manager() -> UnifiedSessionManager:
    """Get the global session manager instance"""
    global _session_manager_instance
    if _session_manager_instance is None:
        _session_manager_instance = UnifiedSessionManager()
    return _session_manager_instance

# Convenience functions for common patterns
async def get_master_db_session():
    """
    Get master database session for FastAPI dependency injection.
    Automatically handles session lifecycle.
    
    Usage as FastAPI dependency:
        async def endpoint(db: AsyncSession = Depends(get_master_db_session)):
            # use db session here
    
    Usage in code:
        async for session in get_master_db_session():
            # use session here
    """
    session_manager = get_session_manager()
    session_maker = await session_manager._get_master_session_maker()
    session = session_maker()
    try:
        yield session
    finally:
        await session.close()

@asynccontextmanager
async def get_master_db_context() -> AsyncContextManager[AsyncSession]:
    """
    Get master database session as async context manager.
    
    Usage:
        async with get_master_db_context() as session:
            # use session here
            result = await session.execute(query)
    """
    session_manager = get_session_manager()
    async with session_manager.get_master_session() as session:
        yield session

@asynccontextmanager 
async def get_project_db_session(project_code: str) -> AsyncContextManager[AsyncSession]:
    """Convenience function to get project database session"""
    session_manager = get_session_manager()
    async with session_manager.get_project_session(project_code) as session:
        yield session
