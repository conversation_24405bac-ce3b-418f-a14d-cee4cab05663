# Database Initialization Guide

This document explains how to initialize and set up databases for the DADP application. The application supports both SQLite and PostgreSQL databases.

## Prerequisites

### For SQLite
- Python 3.7+
- All dependencies from `requirements.txt` installed

### For PostgreSQL
- Python 3.7+
- All dependencies from `requirements.txt` installed (including `psycopg2-binary`)
- PostgreSQL server running and accessible
- Database credentials configured

## Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

## Database Configuration

### SQLite Configuration
SQLite configuration is handled in `db/config.py`. By default, it uses:
- Database file: `./database.db`
- Connection settings optimized for SQLite

### PostgreSQL Configuration  
PostgreSQL configuration is handled in `post_db/config.py`. Default settings:
- Host: `***********`
- Port: `5432`
- Database: `project_db`
- Username: `supabase`
- Password: `supabase`

You can override these settings using environment variables:
```bash
export DATABASE_URL="postgresql://username:password@host:port/database"
```

## Database Initialization

The `init_db.py` script supports both database types with command-line arguments.

### Initialize SQLite Database
```bash
python init_db.py sqlite
```

### Initialize PostgreSQL Database
```bash
python init_db.py postgresql
```
or
```bash
python init_db.py postgres
```

### Verbose Output
Add the `-v` or `--verbose` flag for detailed logging:
```bash
python init_db.py postgresql -v
```

## What the Initialization Does

1. **Tests Database Connection** (PostgreSQL only)
   - Verifies connectivity to PostgreSQL server
   - Displays server version information

2. **Creates Database Tables**
   - Creates all tables defined in the models
   - Handles both SQLite and PostgreSQL schema differences

3. **Seeds Initial Data**
   - Creates default users (admin, annotators, auditor)
   - Populates knowledge base with sample entries

## Default Users Created

The initialization script creates these default users:

| Username    | Password  | Role       | Full Name   |
|-------------|-----------|------------|-------------|
| admin       | admin123  | ADMIN      | Admin User  |
| annotator1  | 12345678  | ANNOTATOR  | Annotator 1 |
| annotator2  | 12345678  | ANNOTATOR  | Annotator 2 |
| annotator3  | 12345678  | ANNOTATOR  | Annotator 3 |
| auditor     | 12345678  | AUDITOR    | Auditor     |

## Troubleshooting

### PostgreSQL Connection Issues
1. Verify PostgreSQL server is running
2. Check network connectivity to the database server
3. Verify credentials and database exist
4. Ensure PostgreSQL allows connections from your IP

### Import Errors
```bash
# Install missing PostgreSQL driver
pip install psycopg2-binary

# Or for development
pip install psycopg2
```

### Permission Issues
Ensure the application has:
- Write permissions for SQLite database file and directory
- Network access to PostgreSQL server
- Correct database permissions for PostgreSQL user

## File Structure

```
app/
├── init_db.py                 # Main initialization script
├── db/                        # SQLite database package
│   ├── config.py             # SQLite configuration
│   ├── db_connector.py       # SQLite connection and utilities
│   └── models/               # SQLite database models
├── post_db/                  # PostgreSQL database package
│   ├── config.py            # PostgreSQL configuration
│   ├── connect.py           # PostgreSQL connection and utilities
│   └── models/              # PostgreSQL database models (same as SQLite)
└── requirements.txt         # Dependencies including psycopg2-binary
```

## Environment Variables

### PostgreSQL
- `DATABASE_URL`: Full PostgreSQL connection string
- Example: `postgresql://user:pass@host:port/dbname`

### SQLite  
- `DATABASE_URL`: SQLite file path
- Example: `sqlite:///./database.db`

## Usage Examples

```bash
# Initialize SQLite with verbose logging
python init_db.py sqlite -v

# Initialize PostgreSQL with custom connection
export DATABASE_URL="postgresql://myuser:mypass@localhost:5432/mydb"
python init_db.py postgresql

# Test PostgreSQL connection only
python post_db/connect.py
```

## Next Steps

After successful initialization:
1. Start the FastAPI application
2. Login with default admin credentials  
3. Change default passwords
4. Configure additional users as needed
5. Begin using the application features 