// steps/ClientRegistration.tsx
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaProjectDiagram, FaMagic } from 'react-icons/fa';
import { Client, ClientFormData } from '../types';

interface ClientRegistrationProps {
  existingClients: Client[];
  clientForm: ClientFormData;
  setClientForm: React.Dispatch<React.SetStateAction<ClientFormData>>;
  clientFormErrors: Record<string, string>;
  registering: boolean;
  onClientRegistration: () => Promise<{ success: boolean; client?: Client }>;
  onSelectExistingClient: (client: Client) => Promise<{ success: boolean; client: Client }>;
}

export const ClientRegistration: React.FC<ClientRegistrationProps> = ({
  existingClients,
  clientForm,
  setClientForm,
  clientFormErrors,
  registering,
  onClientRegistration,
  onSelectExistingClient,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setClientForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onClientRegistration();
  };

  // Development helper function to fill dummy data
  const fillDummyData = () => {
    const randomId = Math.floor(Math.random() * 1000);
    const dummyData = {
      username: `testclient${randomId}`,
      fullName: "Test Client User",
      email: `testclient${randomId}@example.com`,
      password: "TestPass123!",
      confirmPassword: "TestPass123!",
      projectName: `TestProject${randomId}`,
      projectType: "image",
      projectDescription: "Automated invoice processing and data extraction project for testing purposes"
    };
    setClientForm(dummyData);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4 flex items-center">
        <FaUser className="mr-2 text-blue-500" />
        Client Registration
      </h3>

      

      {/* Existing Clients Selection
      {existingClients.length > 0 && (
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-3">Select Existing Client</h4>
          <div className="flex flex-wrap gap-2">
            {existingClients.map((client) => (
              <div
                key={client.id}
                onClick={async () => await onSelectExistingClient(client)}
                className="px-3 py-2 border rounded-lg cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors w-fit"
              >
                <div className="text-sm text-gray-600">@{client.username}</div>
              </div>
            ))}
          </div>
          <div className="text-center my-4">
            <span className="text-gray-500">--- OR ---</span>
          </div>
        </div>
      )} */}

      {/* New Client Registration Form */}
      <form onSubmit={handleSubmit}>
        {/* User Account Section */}
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-3 flex items-center">
            <FaUser className="mr-2 text-blue-500" />
            User Account
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Username</label>
              <input
                type="text"
                name="username"
                value={clientForm.username}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.username
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="client_username"
              />
              {clientFormErrors.username && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.username}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Full Name</label>
              <input
                type="text"
                name="fullName"
                value={clientForm.fullName}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.fullName
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="Client Full Name"
              />
              {clientFormErrors.fullName && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.fullName}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <input
                type="email"
                name="email"
                value={clientForm.email}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.email
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="<EMAIL>"
              />
              {clientFormErrors.email && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.email}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <input
                type="password"
                name="password"
                value={clientForm.password}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.password
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="Minimum 8 characters"
              />
              {clientFormErrors.password && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.password}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1">
                Confirm Password
              </label>
              <input
                type="password"
                name="confirmPassword"
                value={clientForm.confirmPassword}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.confirmPassword
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="Confirm password"
              />
              {clientFormErrors.confirmPassword && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.confirmPassword}
                </p>
              )}
            </div>
          </div>
        </div>



        {/* Project Details Section */}
        <div className="mb-6">
          <h4 className="text-lg font-medium mb-3 flex items-center">
            Project Details
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Project Name</label>
              <input
                type="text"
                name="projectName"
                value={clientForm.projectName || ''}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.projectName
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
                placeholder="e.g. Invoice Processing"
              />
              {clientFormErrors.projectName && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.projectName}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Project Type</label>
              <select
                name="projectType"
                value={clientForm.projectType || ''}
                onChange={handleChange}
                className={`w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 ${
                  clientFormErrors.projectType
                    ? "border-red-500 focus:ring-red-500"
                    : "border-gray-300 focus:ring-blue-500"
                }`}
              >
                <option value="">-- Select Type --</option>
                <option value="image">Image</option>
                <option value="pdf">PDF</option>
                <option value="video">Video</option>
                <option value="audio">Audio</option>
                <option value="text">Text</option>
                <option value="csv">CSV</option>
              </select>
              {clientFormErrors.projectType && (
                <p className="text-red-600 text-sm mt-1">
                  {clientFormErrors.projectType}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium mb-1">
                Project Description (Optional)
              </label>
              <textarea
                name="projectDescription"
                value={clientForm.projectDescription || ''}
                onChange={handleChange}
                className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Brief description of the project"
              ></textarea>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-6">
          {/* Development helper button - Remove in production */}
          <button
            type="button"
            onClick={fillDummyData}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 flex items-center"
          >
            <FaMagic className="mr-2" />
            Fill Dummy Data
          </button>

          <button
            type="submit"
            disabled={registering}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            {registering ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaUser className="mr-2" />
            )}
            Register Client & Project
          </button>
        </div>
      </form>
    </div>
  );
};