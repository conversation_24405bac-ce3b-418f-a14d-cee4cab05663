"""
Media Streaming Service

Handles streaming for audio and video files with MinIO presigned URLs
and fallback to direct file serving for other storage types.
"""

import logging
from typing import Optional, Dict, Any
from fastapi import HTTPException, Request # type: ignore
from fastapi.responses import Response, StreamingResponse # type: ignore

from utils.media_utils import get_storage_connector_context, get_media_from_storage
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from sqlalchemy import select

logger = logging.getLogger('media_streaming_service')


class MediaStreamingService:
    """Service for handling media streaming with MinIO optimization"""
    
    async def get_streaming_url(self, media_path: str, media_type: str, project_code: str, expires_in: int = 3600) -> Dict[str, Any]:
        """
        Get streaming URL for media files.
        Only returns presigned URLs for MinIO projects.
        Raises HTTPException for non-MinIO projects.
        """
        try:
            # Get project connection type
            connection_type = await self._get_project_connection_type(project_code)
            
            if connection_type == 'MinIO':
                return await self._get_minio_streaming_url(media_path, media_type, project_code, expires_in)
            else:
                raise HTTPException(status_code=404, detail="Streaming URLs only available for MinIO projects")
                
        except Exception as e:
            logger.error(f"Error getting streaming URL for {media_path}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get streaming URL: {str(e)}")
    
    async def stream_media(self, media_path: str, media_type: str, project_code: str, request: Optional[Request] = None) -> Response:
        """
        Stream media file directly.
        For MinIO: Should not be used (use presigned URLs instead)
        For other storage: Direct file serving with range support
        """
        try:
            connection_type = await self._get_project_connection_type(project_code)
            
            if connection_type == 'MinIO':
                # For MinIO, we should prefer presigned URLs, but provide fallback
                logger.warning(f"Direct streaming requested for MinIO project {project_code}. Consider using presigned URLs instead.")
            
            # Use existing media serving logic
            return await get_media_from_storage(
                media_path=media_path,
                media_type=media_type,
                include_response_time=True,
                request=request,
                project_code=project_code
            )
            
        except Exception as e:
            logger.error(f"Error streaming media {media_path}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to stream media: {str(e)}")
    
    async def _get_project_connection_type(self, project_code: str) -> str:
        """Get the connection type for a project"""
        
        async with get_master_db_context() as session:
            result = await session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
            project = result.scalar_one_or_none()
                
            if not project:
                logger.warning(f"Project {project_code} not found, defaulting to NAS")
                return "NAS-FTP"
                
            return project.connection_type or "NAS-FTP"
                
    
    async def _get_minio_streaming_url(self, media_path: str, media_type: str, project_code: str, expires_in: int) -> Dict[str, Any]:
        """Get MinIO presigned URL for streaming"""
        async with get_storage_connector_context(project_code) as (connector, is_minio_pooled):
            if not connector:
                raise HTTPException(status_code=400, detail="Storage connection failed")
            
            if not hasattr(connector, 'generate_presigned_url'):
                raise HTTPException(
                    status_code=500, 
                    detail="MinIO connector does not support presigned URLs. Please check MinIO configuration."
                )
            
            logger.info(f"Generating MinIO presigned URL for {media_type}: {media_path}")
            presigned_url = await connector.generate_presigned_url(media_path, expires_in)
            
            return {
                "streaming_url": presigned_url,
                "type": "presigned",
                "storage_type": "MinIO",
                "expires_in": expires_in,
                "supports_range_requests": True,
                "direct_streaming": True
            }
    


# Global service instance
media_streaming_service = MediaStreamingService()
