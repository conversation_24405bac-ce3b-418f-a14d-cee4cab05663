// steps/AllocationStrategy.tsx
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aArrowLeft, FaInfoCircle } from 'react-icons/fa';
import { Client } from '../types';
import { showToast } from '@/lib/toast';

interface AllocationStrategy {
  id: number;
  strategy_name: string;
  strategy_type: string;
  description: string | null;
  allocation_status: string;
  num_annotators: number;
  requires_verification: boolean;
  requires_ai_preprocessing: boolean;
  created_at: string;
}

interface AllocationStrategyProps {
  selectedClient: Client | null;
  projectCode: string | undefined;
  onGoToStep: (step: number) => void;
  isStepCompleted: boolean;
  markStepCompleted: (stepId: number) => void;
}

export const AllocationStrategy: React.FC<AllocationStrategyProps> = ({
  selectedClient,
  projectCode,
  onGoToStep,
  isStepCompleted,
  markStepCompleted,
}) => {
  console.log('AllocationStrategy component mounted with props:', { selectedClient, projectCode, isStepCompleted });
  
  const [strategies, setStrategies] = useState<AllocationStrategy[]>([]);
  const [selectedStrategyId, setSelectedStrategyId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState(false);

  // Fetch allocation strategies with optimizations
  const fetchStrategies = async () => {
    try {
      setLoading(true);
      // Add query parameter to filter active strategies on the backend
      const response = await fetch('/api/allocation-strategies?allocation_status=active&limit=20', { 
        credentials: 'include' 
      });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const data = await response.json();
      
      // Remove client-side filtering since we're filtering on backend
      setStrategies(data);
    } catch (error) {
      console.error('Error fetching strategies:', error);
      showToast.error('Failed to load allocation strategies');
    } finally {
      setLoading(false);
    }
  };

  // Assign strategy to project with better error handling and timeout
  const handleAssignStrategy = async () => {
    if (!projectCode || !selectedStrategyId) {
      showToast.warning('Please select an allocation strategy');
      return;
    }

    setAssigning(true);
    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`/api/projects/by-code/${projectCode}/allocation-strategy`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ strategy_id: selectedStrategyId }),
        credentials: 'include',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to assign allocation strategy');
      }

      const data = await response.json();
      showToast.success(data.message || 'Allocation strategy assigned successfully!');
      
      // Mark step as completed and proceed to next step
      markStepCompleted(5);
      onGoToStep(6);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        showToast.error('Request timed out. Please try again.');
      } else {
        console.error('Error assigning strategy:', error);
        showToast.error(error instanceof Error ? error.message : 'Failed to assign allocation strategy');
      }
    } finally {
      setAssigning(false);
    }
  };

  // Initialize data - only fetch once on mount
  useEffect(() => {
    fetchStrategies();
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4 flex items-center">
        <FaCog className="mr-2 text-purple-500" />
        Allocation Strategy Assignment
      </h3>

      <p className="text-gray-600 mb-6">
        Select an allocation strategy for your project. This will define how annotation work is distributed among annotators.
      </p>

      {/* Allocation Strategy Selection */}
      <div className="mb-6">
        <h4 className="text-lg font-medium mb-3">Select Allocation Strategy</h4>
        
        {loading ? (
          <div className="text-center py-4">
            <FaSpinner className="animate-spin text-2xl text-blue-500 mx-auto mb-2" />
            <p>Loading allocation strategies...</p>
          </div>
        ) : strategies.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            <p>No active allocation strategies available.</p>
            <p className="text-sm mt-2">Please contact an administrator to create allocation strategies.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {strategies.map((strategy) => (
              <div
                key={strategy.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedStrategyId === strategy.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedStrategyId(strategy.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <input
                        type="radio"
                        name="strategy"
                        value={strategy.id}
                        checked={selectedStrategyId === strategy.id}
                        onChange={() => setSelectedStrategyId(strategy.id)}
                        className="text-blue-600"
                      />
                      <h5 className="font-medium text-gray-900">{strategy.strategy_name}</h5>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        strategy.strategy_type === 'sequential' ? 'bg-yellow-100 text-yellow-800' :
                        strategy.strategy_type === 'parallel' ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {strategy.strategy_type.replace('_', ' ')}
                      </span>
                    </div>
                    
                    {strategy.description && (
                      <p className="text-sm text-gray-600 mb-2">{strategy.description}</p>
                    )}
                    
                    <div className="flex gap-4 text-xs text-gray-500">
                      <span>Annotators: {strategy.num_annotators}</span>
                      <span>Verification: {strategy.requires_verification ? 'Yes' : 'No'}</span>
                      <span>AI Preprocessing: {strategy.requires_ai_preprocessing ? 'Yes' : 'No'}</span>
                      <span>Created: {new Date(strategy.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  {selectedStrategyId === strategy.id && (
                    <FaCheckCircle className="text-blue-500 text-xl ml-2" />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between mt-6">
        <button
          onClick={() => onGoToStep(4)}
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 flex items-center"
        >
          <FaArrowLeft className="mr-2" />
          Back to Instructions
        </button>

        {selectedStrategyId && (
          <button
            onClick={handleAssignStrategy}
            disabled={assigning}
            className="px-6 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 flex items-center"
          >
            {assigning ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaCheckCircle className="mr-2" />
            )}
            {assigning ? 'Assigning...' : 'Assign Strategy & Continue'}
          </button>
        )}
      </div>

      {/* Info Section */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <FaInfoCircle className="text-blue-500 mr-2 mt-1 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <h5 className="font-medium mb-1">About Allocation Strategies</h5>
            <p className="mb-2">
              Allocation strategies define how annotation work is distributed among annotators. 
              Choose the strategy that best fits your project's quality and workflow requirements.
            </p>
            <ul className="text-xs space-y-1">
              <li><strong>Sequential:</strong> Files are processed one after another by annotators</li>
              <li><strong>Parallel:</strong> Multiple annotators work on different files simultaneously</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
