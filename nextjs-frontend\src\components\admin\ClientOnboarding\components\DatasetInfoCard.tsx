// components/DatasetInfoCard.tsx
import React from 'react';
import { FaCheckCircle, FaInfoCircle, FaExclamationCircle, FaExclamationTriangle, FaImages } from 'react-icons/fa';
import { Dataset } from '../types';
import { getCompletionStatus } from '../utils/helpers';

interface DatasetInfoCardProps {
  dataset: Dataset;
  mode: 'annotation' | 'verification';
  onBrowseImages: () => void;
  horizontalAlertAndButton?: boolean;
}

export const DatasetInfoCard: React.FC<DatasetInfoCardProps> = ({ dataset, mode, onBrowseImages, horizontalAlertAndButton }) => {
  const status = getCompletionStatus(dataset);
  
  const statusClass = status === 'completed' 
    ? 'text-success' 
    : status === 'in-progress' 
      ? 'text-primary' 
      : 'text-muted';
  
  const StatusIcon = status === 'completed' 
    ? FaCheckCircle 
    : status === 'in-progress' 
      ? FaInfoCircle 
      : FaExclamationCircle;
  
  const alertClass = status === 'completed' 
    ? 'alert-success' 
    : status === 'in-progress' 
      ? 'alert-info' 
      : 'alert-secondary';
  
  const AlertIcon = status === 'completed' 
    ? FaExclamationTriangle 
    : status === 'in-progress' 
      ? FaInfoCircle 
      : FaExclamationCircle;
  
  return (
    <div className="card justify-content-between">
      <div className="card-header d-flex flex-row align-items-center justify-content-between whitespace-nowrap gap-4">
        <h5 className="mb-0 flex-shrink-1 text-truncate max-w-[60%] overflow-hidden text-ellipsis">{dataset.name}</h5>
        <span className={statusClass + ' d-flex align-items-center flex-shrink-0 font-medium'}>
          <StatusIcon className="me-1" />
          {status === 'completed' ? 'Completed' : status === 'in-progress' ? 'In Progress' : 'Empty'}
        </span>
      </div>
      <div className="card-body">
        <div className="row">
          <div className="col-md-6">
            <p><strong>Total Batches:</strong> {dataset.total_batches}</p>
            <p><strong>Completed Batches:</strong> {dataset.completed_batches}</p>
            <p><strong>Completion:</strong> {dataset.progress_percentage}%</p>
          </div>
          <div className="col-md-6">
            {mode === 'verification' && dataset.label_file && (
              <p><strong>Label File:</strong> {dataset.label_file}</p>
            )}
          </div>
        </div>
        <div className="row">
          <div className="col-12">
            <div className="progress h-4 mb-4">
              <div 
                className={`progress-bar ${status === 'completed' ? 'bg-success' : 'bg-primary'}`} 
                role="progressbar" 
                style={{ width: `${dataset.progress_percentage}%` }}
                aria-valuenow={dataset.progress_percentage}
                aria-valuemin={0}
                aria-valuemax={100}
              ></div>
            </div>
          </div>
        </div>
        {horizontalAlertAndButton ? (
          <div className="d-flex align-items-center gap-3 mt-3">
            <div className={`alert ${alertClass} mb-0 d-flex align-items-center gap-2 py-2 px-3`}>
              <AlertIcon className="me-2" />
              <span>
                {status === 'completed' ? 
                  'All batches in this dataset have been completed. You may select a different dataset.' : 
                  status === 'in-progress' ? 
                    'This dataset is partially processed and ready for annotators to continue working on.' :
                    'This dataset has no batches available.'}
              </span>
            </div>
            <button className="btn btn-outline-primary ms-2 flex items-center" onClick={onBrowseImages}>
              <span className="flex items-center">
                <FaImages className="text-3xl" />
                <span>Browse Images</span>
              </span>
            </button>
          </div>
        ) : (
          <>
            <div className={`alert ${alertClass} mb-0`}>
              <AlertIcon className="me-2" />
              {status === 'completed' ? 
                'All batches in this dataset have been completed. You may select a different dataset.' : 
                status === 'in-progress' ? 
                  'This dataset is partially processed and ready for annotators to continue working on.' :
                  'This dataset has no batches available.'}
            </div>
            <div className="mt-3 d-flex justify-content-end">
              <button className="btn btn-outline-primary" onClick={onBrowseImages}>
                <span className="flex items-center">
                  <FaImages className="me-1" />
                  <span>Browse Images</span>
                </span>
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};