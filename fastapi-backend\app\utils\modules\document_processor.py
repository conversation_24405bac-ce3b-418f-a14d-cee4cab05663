"""
Document processing module for handling document extraction and processing tasks
"""
import queue
import threading
import os
import tempfile
from utils.modules.main_model import load_model, process_document_image
import logging

logger = logging.getLogger('document_processor')

# Global processing queue and results dictionary
process_queue = queue.Queue()
results_dict = {}

def background_processor():
    """Background thread function to process documents from the queue"""
    while True:
        task = process_queue.get()
        if task is None:  # Sentinel to end the thread
            break

        if len(task) >= 5:
            image_path, document_type, image_id, in_memory, model_type = task
        else:
            image_path, document_type, image_id, in_memory = task
            model_type = results_dict.get(image_id, {}).get('model_type', 'standard')
        
        temp_file_path = None

        if in_memory:
            from utils.modules.file_handler import get_file_extension
            file_extension, _ = get_file_extension(results_dict[image_id]['path'])
            
            temp_fd, temp_file_path = tempfile.mkstemp(suffix=file_extension)
            os.close(temp_fd)
            
            with open(temp_file_path, 'wb') as f:
                f.write(image_path.getvalue())
            image_path = temp_file_path
        else:
            if not os.path.exists(image_path):
                if 'image_data' in results_dict[image_id]:
                    from utils.modules.file_handler import get_file_extension
                    file_extension, _ = get_file_extension(image_path)
                    
                    temp_fd, temp_file_path = tempfile.mkstemp(suffix=file_extension)
                    os.close(temp_fd)
                    
                    with open(temp_file_path, 'wb') as f:
                        f.write(results_dict[image_id]['image_data'])
                    image_path = temp_file_path
                    in_memory = True
                else:
                    results_dict[image_id]['status'] = 'error'
                    results_dict[image_id]['error'] = 'File not found'
                    process_queue.task_done()
                    continue

        # Process document with model
        tokenizer, model = load_model(model_type)
        extracted_data = process_document_image(
            image_path, document_type, model_type, tokenizer, model
        )

        # Update results with preserving existing data
        existing_data = results_dict[image_id].copy() if image_id in results_dict else {}
        
        # Parse extracted data into structured format
        parsed_data = {}
        if isinstance(extracted_data, str):
            lines = extracted_data.strip().split('\n')
            for line in lines:
                if ':' in line:
                    key, value = line.split(':', 1)
                    parsed_data[key.strip()] = value.strip()
        elif isinstance(extracted_data, dict):
            parsed_data = extracted_data
        
        results_dict[image_id] = {
            'status': 'completed',
            'data': extracted_data,
            'parsed_data': parsed_data,
            'path': existing_data.get('path', image_path),
            'model_type': model_type,
            'source': existing_data.get('source', 'local'),
            'drive_link': existing_data.get('drive_link', '')
        }
        
        # Preserve other metadata
        for key, value in existing_data.items():
            if key not in results_dict[image_id]:
                results_dict[image_id][key] = value

        # Process document-specific content
        process_extracted_data_by_type(image_id, document_type, extracted_data)

        # Clean up temporary file if created
        from utils.modules.file_handler import delete_file
        if temp_file_path and os.path.exists(temp_file_path):
            delete_file(temp_file_path)
        
        process_queue.task_done()

def process_extracted_data_by_type(image_id, document_type, extracted_data):
    """Process extracted data according to document type"""
    txt_content = ""
    
    extracted_data_dict = {}
    if isinstance(extracted_data, str):
        lines = extracted_data.strip().split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                extracted_data_dict[key.strip()] = value.strip()
    elif isinstance(extracted_data, dict):
        extracted_data_dict = extracted_data
    else:
        str_data = str(extracted_data)
        lines = str_data.strip().split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                extracted_data_dict[key.strip()] = value.strip()
    
    if document_type == 'check':
        txt_content = "\n\n".join([
            f"File: {os.path.basename(results_dict[image_id]['path'])}\n"
            f"Link to The file: \n"
            f"Pic Date:  \n"
            f"Download Date: \n"
            f"Check Type: \n"
            f"Bank Name: {extracted_data_dict.get('Bank Name', ' ')}\n"
            f"1st Payor First Name: {extracted_data_dict.get('Payor Name', extracted_data_dict.get('1st Payor First Name', ' '))}\n"
            f"1st Payor Family Name: \n"
            f"2nd Payor First Name: \n"
            f"2nd Payor Family Name: \n"
            f"Payor Street Address: {extracted_data_dict.get('Payor Address', extracted_data_dict.get('Payor Street Address', ' '))}\n"
            f"Payor City: \n"
            f"Payor State: \n"
            f"Payor Zip code: \n"
            f"Check Amount: {extracted_data_dict.get('Amount', extracted_data_dict.get('Check Amount', '  '))}\n"
            f"Account Number: \n"
            f"Routing Number: \n"
            f"Payee Type: \n"
            f"1st Payee First Name: {extracted_data_dict.get('Payee Name', extracted_data_dict.get('1st Payee First Name', '  '))}\n"
            f"1st Payee Family Name: \n"
            f"2nd Payee First Name: \n"
            f"2nd Payee Family Name: \n"
            f"Check Number: {extracted_data_dict.get('Check Number', ' ')}\n"
            f"Payee Street Address: {extracted_data_dict.get('Payee Address', extracted_data_dict.get('Payee Street Address', '  '))}\n"
            f"Payee City: \n"
            f"Payee State: \n"
            f"Payee Zip Code: \n"
            f"Market: \n"
            f"{'-'*50}"
        ])

    elif document_type == 'passport':
        txt_content = "\n\n".join([
            f"File: {os.path.basename(results_dict[image_id]['path'])}\n"
            f"Passport Country Code: {extracted_data_dict.get('Passport Country Code', ' ')}\n"
            f"Passport Type: {extracted_data_dict.get('Passport Type', ' ')}\n"
            f"Passport Number: {extracted_data_dict.get('Passport Number', ' ')}\n"
            f"First Name: {extracted_data_dict.get('First Name', ' ')}\n"
            f"Family Name: {extracted_data_dict.get('Family Name', ' ')}\n"
            f"Date of Birth: {extracted_data_dict.get('Date of Birth', ' ')}\n"
            f"Date of Birth Month: {extracted_data_dict.get('Date of Birth Month', ' ')}\n"
            f"Date of Birth Year: {extracted_data_dict.get('Date of Birth Year', ' ')}\n"
            f"Place of Birth: {extracted_data_dict.get('Place of Birth', ' ')}\n"
            f"Gender: {extracted_data_dict.get('Gender', ' ')}\n"
            f"Date of Issue: {extracted_data_dict.get('Date of Issue', ' ')}\n"
            f"Date of Issue Month: {extracted_data_dict.get('Date of Issue Month', ' ')}\n"
            f"Date of Issue Year: {extracted_data_dict.get('Date of Issue Year', ' ')}\n"
            f"Date of Expiration: {extracted_data_dict.get('Date of Expiration', ' ')}\n"
            f"Date of Expiration Month: {extracted_data_dict.get('Date of Expiration Month', ' ')}\n"
            f"Date of Expiration Year: {extracted_data_dict.get('Date of Expiration Year', ' ')}\n"
            f"Authority: {extracted_data_dict.get('Authority', ' ')}\n"
            f"Signature: {extracted_data_dict.get('Signature', ' ')}\n"
            f"Stamp: {extracted_data_dict.get('Stamp', ' ')}\n"
            f"{'-'*50}"
        ])
    elif document_type == 'invoice':
        txt_content = "\n\n".join([
            f"File: {os.path.basename(results_dict[image_id]['path'])}\n"
            f"Invoice Number: {extracted_data_dict.get('Invoice Number', ' ')}\n"
            f"Invoice Date: {extracted_data_dict.get('Invoice Date', extracted_data_dict.get('Date', ' '))}\n"
            f"Due Date: {extracted_data_dict.get('Due Date', ' ')}\n"
            f"Vendor/Seller: {extracted_data_dict.get('Vendor/Seller', extracted_data_dict.get('Vendor Name', ' '))}\n"
            f"Customer: {extracted_data_dict.get('Customer', extracted_data_dict.get('Customer Name', ' '))}\n"
            f"Total Amount: {extracted_data_dict.get('Total Amount', ' ')}\n"
            f"Payment Terms: {extracted_data_dict.get('Payment Terms', ' ')}\n"
            f"{'-'*50}"
        ])
    else:
        txt_content = str(extracted_data)

    results_dict[image_id]['txt_content'] = txt_content
    results_dict[image_id]['parsed_data'] = extracted_data_dict

    # Generate CSV content
    from utils.modules.export import convert_to_csv_content
    all_results = [{
        'filename': os.path.basename(results_dict[image_id]['path']),
        'extraction_data': extracted_data_dict
    }]
    csv_content = convert_to_csv_content(all_results, document_type)
    results_dict[image_id]['csv_content'] = csv_content

def parse_extracted_text(text):
    """Parse extracted text into structured data dictionary"""
    data = {}
    if text:
        lines = text.strip().split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                data[key.strip()] = value.strip()
    return data

# Start background processing thread
processor_thread = threading.Thread(target=background_processor, daemon=True)
processor_thread.start() 
