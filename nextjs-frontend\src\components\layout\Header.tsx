"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { FaChevronDown } from "react-icons/fa";
import Image from "next/image";
type HeaderProps = {
  onOpenLogin: () => void;
};

const Header = ({ onOpenLogin }: HeaderProps) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const pathname = usePathname();

  // Handle scroll event to change header style
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const isLandingPage = pathname === "/";

  return (
    // Start of Selection
    <header
      className="fixed top-0 left-0 w-full z-50 transition-all duration-300 py-3 bg-white/90 backdrop-blur-md shadow-md"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="no-underline flex items-center">
              <Image
                src="/img/PVlogo-1024x780.png"
                alt="ProcessVenue Logo"
                width={63} // 48 * (1024/780) ≈ 63
                height={48}
                className="h-16 w-auto mr-4"
              />
              <div className="flex flex-col justify-center">
                <span className="text-[1.2rem] font-bold text-blue-800 whitespace-nowrap">
                  Human Augmented Intelligence
                </span>
                <span className="text-[1rem] font-bold text-blue-800 whitespace-nowrap">
                  Powered by <span className="text-blue-500">ProcessVenue</span>
                </span>
                <span className="text-[0.8rem] font-medium bg-green-200 px-2 py-0.5 rounded-full text-gray-800 w-fit">
                  EARLY ACCESS
                </span>
              </div>
            </Link>
          </div>

          {/* Mobile menu toggle */}
          <button
            className="lg:hidden flex flex-col justify-center items-center w-10 h-10"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            <span
              className={`block w-6 h-0.5 bg-gray-800 transition-all duration-300 mb-1.5 ${
                isMobileMenuOpen ? "transform rotate-45 translate-y-2" : ""
              }`}
            ></span>
            <span
              className={`block w-6 h-0.5 bg-gray-800 transition-all duration-300 mb-1.5 ${
                isMobileMenuOpen ? "opacity-0" : ""
              }`}
            ></span>
            <span
              className={`block w-6 h-0.5 bg-gray-800 transition-all duration-300 ${
                isMobileMenuOpen ? "transform -rotate-45 -translate-y-2" : ""
              }`}
            ></span>
          </button>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <ul className="flex space-x-8">
              <li>
                <Link
                  href="/"
                  className="no-underline inline-block py-2 text-blue-900 hover:text-blue-700 font-medium"
                >
                  Home
                </Link>
              </li>
              {isLandingPage ? (
                <>
                  <li className="relative">
                    <button
                      className="inline-flex items-center py-2 text-blue-900 hover:text-blue-700 font-medium"
                      onClick={() => setIsServicesOpen(!isServicesOpen)}
                    >
                      Services <FaChevronDown className="ml-1 w-3 h-3" />
                    </button>
                    <div
                      className={`absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-xl py-2 transition-all duration-300 z-50 ${
                        isServicesOpen
                          ? "opacity-100 visible"
                          : "opacity-0 invisible"
                      }`}
                    >
                      <Link
                        href="/synthetic"
                        className="no-underline block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50"
                        onClick={() => setIsServicesOpen(false)}
                      >
                        SynGround
                      </Link>
                      <Link
                        href="/documind"
                        className="no-underline block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50"
                        onClick={() => setIsServicesOpen(false)}
                      >
                        Documind-o
                      </Link>
                      <Link
                        href="/note-ocr"
                        className="no-underline block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50"
                        onClick={() => setIsServicesOpen(false)}
                      >
                        NoteOCR
                      </Link>
                    </div>
                  </li>
                  <li>
                    <a
                      href="#about"
                      className="no-underline inline-block py-2 text-blue-900 hover:text-blue-700 font-medium"
                    >
                      About
                    </a>
                  </li>
                  <li>
                    <a
                      href="#contact"
                      className="no-underline inline-block py-2 text-blue-900 hover:text-blue-700 font-medium"
                    >
                      Contact
                    </a>
                  </li>
                </>
              ) : (
                <>
                  <li>
                    <Link
                      href="/synthetic"
                      className={`no-underline inline-block py-2 ${
                        pathname === "/synthetic"
                          ? "text-blue-700 font-bold"
                          : "text-blue-900"
                      } hover:text-blue-700 font-medium`}
                    >
                      SynGround
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/documind"
                      className={`no-underline inline-block py-2 ${
                        pathname === "/documind"
                          ? "text-blue-700 font-bold"
                          : "text-blue-900"
                      } hover:text-blue-700 font-medium`}
                    >
                      Documind-o
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/note-ocr"
                      className={`no-underline inline-block py-2 ${
                        pathname === "/note-ocr"
                          ? "text-blue-700 font-bold"
                          : "text-blue-900"
                      } hover:text-blue-700 font-medium`}
                    >
                      NoteOCR
                    </Link>
                  </li>
                </>
              )}
            </ul>

            <button
              onClick={onOpenLogin}
              className="inline-flex items-center px-6 py-2 rounded-full bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] text-white font-medium hover:opacity-90 transition-all duration-300"
            >
              Login
            </button>
          </nav>

          {/* Mobile menu - only shown when toggled */}
          {isMobileMenuOpen && (
            <div className="fixed inset-0 bg-white z-40 lg:hidden overflow-y-auto">
              <div className="flex flex-col h-full p-6">
                <div className="flex justify-between items-center mb-8">
                  <Link
                    href="/"
                    className="no-underline flex items-center"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <div className="h-10 w-10 bg-blue-100 flex items-center justify-center text-blue-800 font-bold rounded-md">
                      LOGO
                    </div>
                    <span className="text-lg font-bold ml-2">DADP</span>
                  </Link>
                  <button
                    className="text-gray-800"
                    onClick={toggleMobileMenu}
                    aria-label="Close menu"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <nav className="flex-1">
                  <ul className="space-y-6 text-lg">
                    <li>
                      <Link
                        href="/"
                        className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Home
                      </Link>
                    </li>
                    <li>
                      <p className="block text-gray-400 text-sm uppercase tracking-wider mb-2">
                        Services
                      </p>
                      <ul className="pl-2 space-y-4 border-l-2 border-gray-100">
                        <li>
                          <Link
                            href="/synthetic"
                            className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            SynGround
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/documind"
                            className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            Documind-o
                          </Link>
                        </li>
                        <li>
                          <Link
                            href="/note-ocr"
                            className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            NoteOCR
                          </Link>
                        </li>
                      </ul>
                    </li>
                    {isLandingPage && (
                      <>
                        <li>
                          <a
                            href="#about"
                            className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            About
                          </a>
                        </li>
                        <li>
                          <a
                            href="#contact"
                            className="no-underline block text-blue-900 hover:text-blue-700 font-medium"
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            Contact
                          </a>
                        </li>
                      </>
                    )}
                  </ul>
                </nav>

                <div className="mt-auto pt-6">
                  <button
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      onOpenLogin();
                    }}
                    className="w-full px-6 py-3 rounded-full bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] text-white font-medium hover:opacity-90 transition-all duration-300 flex justify-center items-center"
                  >
                    Login
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
