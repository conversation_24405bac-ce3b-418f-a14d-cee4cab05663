"""
Export functionality for document processing results
"""
import csv
from io import StringIO, BytesIO
from fastapi.responses import StreamingResponse
import logging

def convert_to_csv_content(all_results, document_type=None):
    """Convert text extraction results to CSV format with proper document type handling"""
    output = StringIO()
    writer = csv.writer(output, delimiter=',', quoting=csv.QUOTE_ALL)
    
    # Use passed document_type instead of reading from session
    doc_type = document_type or 'unknown'
    
    # Define headers and data extraction based on document type
    if doc_type == 'check':
        headers = [
            'Filename',
            'Link to The file',
            'Pic Date',
            'Download Date',
            'Check Type',
            'Bank Name',
            '1st Payor First Name',
            '1st Payor Family Name',
            '2nd Payor First Name',
            '2nd Payor Family Name',
            'Payor Street Address',
            'Payor City',
            'Payor State',
            'Payor Zip code',
            'Check Amount',
            'Account Number',
            'Routing Number',
            'Payee Type',
            '1st Payee First Name',
            '1st Payee Family Name',
            '2nd Payee First Name',
            '2nd Payee Family Name',
            'Check Number',
            'Payee Street Address',
            'Payee City',
            'Payee State',
            'Payee Zip Code',
            'Market'
        ]
        
        def extract_check_data(filename, extraction_text):
            # Parse the raw extraction text to get check-specific data
            data = {}
            if isinstance(extraction_text, str):
                lines = extraction_text.split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = [x.strip() for x in line.split(':', 1)]
                        data[key] = value if value else 'NA'
            else:
                # If it's already a dictionary
                data = extraction_text
            
            # Try to get the link - check multiple possible key variations
            link = data.get('Link to File', 
                   data.get('Link to The file', 
                   data.get('drive_link', 
                   data.get('Link', 'Not found'))))
            
            # Helper function to get value with multiple possible keys
            def get_value(keys, default='Not found'):
                for key in keys:
                    if key in data and data[key]:
                        return data[key]
                return default
            
            return [
                filename,
                link,  # Link to The file
                get_value(['Pic Date', 'Picture Date', 'Image Date']),  # Pic Date
                get_value(['Download Date', 'Retrieved Date']),  # Download Date
                get_value(['Check Type', 'Type of Check', 'Check Category']),  # Check Type
                get_value(['Bank Name', 'Financial Institution', 'Issuing Bank']),
                get_value(['Payor First Name', '1st Payor First Name', 'Drawer First Name', 'Payor Name']),  # 1st Payor First Name
                get_value(['Payor Family Name', '1st Payor Family Name', 'Drawer Last Name', 'Payor Name']),  # 1st Payor Family Name
                get_value(['2nd Payor First Name', 'Second Payor First Name']),  # 2nd Payor First Name
                get_value(['2nd Payor Family Name', 'Second Payor Last Name']),  # 2nd Payor Family Name
                get_value(['Payor Street Address', 'Payor Address', 'Drawer Address']),
                get_value(['Payor City', 'Drawer City']),  # Payor City
                get_value(['Payor State', 'Drawer State']),  # Payor State
                get_value(['Payor Zip code', 'Payor Zip', 'Drawer Zip', 'Drawer Postal Code']),  # Payor Zip code
                get_value(['Amount', 'Check Amount', 'Payment Amount', 'Sum']),
                get_value(['Account Number', 'Account No', 'A/C Number']),  # Account Number
                get_value(['Routing Number', 'Routing No', 'ABA Number']),  # Routing Number
                get_value(['Payee Type', 'Beneficiary Type']),  # Payee Type
                get_value(['Payee First Name', '1st Payee First Name', 'Beneficiary First Name', 'Payee Name']),  # 1st Payee First Name
                get_value(['Payee Family Name', '1st Payee Family Name', 'Beneficiary Last Name', 'Payee Name']),  # 1st Payee Family Name
                get_value(['2nd Payee First Name', 'Second Payee First Name']),  # 2nd Payee First Name
                get_value(['2nd Payee Family Name', 'Second Payee Last Name']),  # 2nd Payee Family Name
                get_value(['Check Number', 'Cheque Number', 'Check No', 'Cheque No']),
                get_value(['Payee Street Address', 'Payee Address', 'Beneficiary Address']),
                get_value(['Payee City', 'Beneficiary City']),  # Payee City
                get_value(['Payee State', 'Beneficiary State']),  # Payee State
                get_value(['Payee Zip Code', 'Payee Zip', 'Beneficiary Zip', 'Beneficiary Postal Code']),  # Payee Zip Code
                get_value(['Market', 'Market Region', 'Region'])   # Market
            ]
        
        # Write headers
        writer.writerow(headers)
        
        # Process each result
        for result in all_results:
            filename = result['filename']
            row = extract_check_data(filename, result['extraction_data'])
            writer.writerow(row)
            
    elif doc_type == 'passport':
        headers = [
            'Filename',
            'Link to The file',
            'Passport Country Code',
            'Passport Type',
            'Passport Number',
            'First Name',
            'Family Name',
            'Date of Birth Day',
            'Date of Birth Month',
            'Date of Birth Year',
            'Place of Birth',
            'Gender',
            'Date of Issue Day',
            'Date of Issue Month',
            'Date of Issue Year',
            'Date of Expiration Day',
            'Date of Expiration Month',
            'Date of Expiration Year',
            'Authority'
        ]
        
        def extract_passport_data(filename, data):
            if isinstance(data, str):
                # Parse the data string into a dictionary
                data_dict = {}
                lines = data.split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        data_dict[key.strip()] = value.strip()
                data = data_dict
            
            # Helper function to get value with multiple possible keys
            def get_value(keys, default='NA'):
                for key in keys:
                    if key in data and data[key]:
                        return data[key]
                return default
                
            # Get the link with multiple possible keys
            link = get_value(['Link to File', 'Link to The file', 'drive_link', 'Link'])
                
            return [
                filename,
                link,
                get_value(['Passport Country Code', 'Country Code', 'Issuing Country']),
                get_value(['Passport Type', 'Document Type']),
                get_value(['Passport Number', 'Document Number', 'Passport No']),
                get_value(['First Name', 'Given Name', 'Forename']),
                get_value(['Family Name', 'Last Name', 'Surname']),
                get_value(['Date of Birth Day', 'Birth Day', 'DOB Day']),
                get_value(['Date of Birth Month', 'Birth Month', 'DOB Month']),
                get_value(['Date of Birth Year', 'Birth Year', 'DOB Year']),
                get_value(['Place of Birth', 'Birth Place']),
                get_value(['Gender', 'Sex']),
                get_value(['Date of Issue Day', 'Issue Day']),
                get_value(['Date of Issue Month', 'Issue Month']),
                get_value(['Date of Issue Year', 'Issue Year']),
                get_value(['Date of Expiration Day', 'Expiration Day', 'Expiry Day']),
                get_value(['Date of Expiration Month', 'Expiration Month', 'Expiry Month']),
                get_value(['Date of Expiration Year', 'Expiration Year', 'Expiry Year']),
                get_value(['Authority', 'Issuing Authority'])
            ]
        
        # Write headers
        writer.writerow(headers)
        
        # Process each result
        for result in all_results:
            filename = result['filename']
            row = extract_passport_data(filename, result['extraction_data'])
            writer.writerow(row)
        
    elif doc_type == 'invoice':
        headers = [
            'Filename',
            'Link to The file',
            'Invoice Number',
            'Date',
            'Due Date',
            'Total Amount',
            'Vendor Name',
            'Customer Name',
            'Payment Terms'
        ]
        
        def extract_invoice_data(filename, data):
            if isinstance(data, str):
                # Parse the data string into a dictionary
                data_dict = {}
                lines = data.split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        data_dict[key.strip()] = value.strip()
                data = data_dict
            
            # Helper function to get value with multiple possible keys
            def get_value(keys, default='NA'):
                for key in keys:
                    if key in data and data[key]:
                        return data[key]
                return default
                
            # Get the link with multiple possible keys
            link = get_value(['Link to File', 'Link to The file', 'drive_link', 'Link'])
                
            return [
                filename,
                link,
                get_value(['Invoice Number', 'Invoice No', 'Bill Number']),
                get_value(['Date', 'Invoice Date', 'Bill Date']),
                get_value(['Due Date', 'Payment Due Date']),
                get_value(['Total Amount', 'Amount', 'Invoice Amount', 'Bill Amount']),
                get_value(['Vendor Name', 'Seller', 'Company Name']),
                get_value(['Customer Name', 'Buyer', 'Client Name']),
                get_value(['Payment Terms', 'Terms'])
            ]
        
        # Write headers
        writer.writerow(headers)
        
        # Process each result
        for result in all_results:
            filename = result['filename']
            row = extract_invoice_data(filename, result['extraction_data'])
            writer.writerow(row)
    
    else:
        # Generic handling for unknown document types
        # First, collect all keys from the data
        all_keys = set()
        for result in all_results:
            data = result['extraction_data']
            if isinstance(data, dict):
                all_keys.update(data.keys())
        
        # Add filename and link to file
        headers = ['Filename', 'Link to The file']
        # Add the rest of the keys, sorted
        headers.extend(sorted(list(all_keys)))
        
        # Write the headers
        writer.writerow(headers)
        
        # Write each row
        for result in all_results:
            filename = result['filename']
            drive_link = result.get('drive_link', '')
            data = result['extraction_data']
            
            # Start with filename and link to file
            row = [filename, drive_link]
            
            # Add each field value from the data
            for key in headers[2:]:  # Skip Filename and Link to The file
                if isinstance(data, dict) and key in data:
                    row.append(data[key])
                else:
                    row.append('')
            
            writer.writerow(row)
    
    return output.getvalue()

def download_csv(all_results, document_type=None, filename=None):
    """Generate and return a downloadable CSV file using StreamingResponse"""
    logger = logging.getLogger('export')

    if filename is None:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"extraction_results_{timestamp}.csv"

    # Log input for debugging
    logger.info(f"Generating CSV with document_type={document_type}, filename={filename}")

    # Generate CSV content
    csv_content = convert_to_csv_content(all_results, document_type)
    # Create BytesIO buffer with BOM
    buffer = BytesIO()
    buffer.write(b'\xef\xbb\xbf')
    buffer.write(csv_content.encode('utf-8'))
    buffer.seek(0)

    headers = {"Content-Disposition": f'attachment; filename="{filename}"'}
    return StreamingResponse(buffer, media_type="text/csv", headers=headers) 