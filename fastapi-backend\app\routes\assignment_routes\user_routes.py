"""
Routes for retrieving users by role for project assignment.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from typing import List, Dict, Any, Optional
import logging

from post_db.master_models.users import users as Users
from post_db.master_models.user_project_access import UserProjectAccess
from dependencies.auth import get_current_active_user, require_admin
from schemas.ProjectAssignmentSchemas import UserListResponse
from core.session_manager import get_master_db_session  

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/users",
    tags=["User Assignment"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)]
)


@router.get("/annotators", response_model=UserListResponse)
async def get_available_annotators(
    project_id: Optional[int] = Query(None, description="Project ID to check for existing assignments"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get all available users with the annotator role.
    
    Args:
        project_id: Optional project ID to exclude users already assigned to this project
        
    Returns:
        List of available annotators
    """
    try:
        # Base query for annotators - only include users with exactly "annotator" role
        query = select(Users).where(
            and_(
                Users.role == "annotator",  # Only include users with exactly "annotator" role
                Users.is_active == True,
                Users.active_project.is_(None)  # Only show users not assigned to any project
            )
        )
        
        # Execute query
        result = await db.execute(query)
        all_annotators = result.scalars().all()
        
        # If project_id is provided, exclude users already assigned to this project
        excluded_user_ids = set()
        if project_id:
            assigned_query = select(UserProjectAccess.user_id).where(
                UserProjectAccess.project_id == project_id
            )
            assigned_result = await db.execute(assigned_query)
            excluded_user_ids = {user_id for user_id, in assigned_result}
        
        # Format response
        annotators = []
        for user in all_annotators:
            # Skip if already assigned to this project
            if user.id in excluded_user_ids:
                continue
                
            # Check if user is at max_concurrent_projects limit
            project_count_query = select(func.count(UserProjectAccess.id)).where(
                and_(
                    UserProjectAccess.user_id == user.id,
                    UserProjectAccess.is_active == True
                )
            )
            project_count_result = await db.execute(project_count_query)
            current_projects = project_count_result.scalar_one()
            
            # Skip if at max concurrent projects
            if user.max_concurrent_projects and current_projects >= user.max_concurrent_projects:
                continue
                
            # Add user to response
            annotators.append({
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "skills": user.annotation_skills,
                "max_concurrent_projects": user.max_concurrent_projects,
                "current_projects": current_projects,
                "max_concurrent_batches": user.max_concurrent_batches,
                "overall_quality_score": float(user.overall_quality_score) if user.overall_quality_score else None
            })
        
        return UserListResponse(
            users=annotators,
            total=len(annotators)
        )
        
    except Exception as e:
        logger.error(f"Error getting available annotators: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving annotators: {str(e)}"
        )





@router.get("/verifiers", response_model=UserListResponse)
async def get_available_verifiers(
    project_id: Optional[int] = Query(None, description="Project ID to check for existing assignments"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get all available users with the verifier role.
    
    Args:
        project_id: Optional project ID to exclude users already assigned to this project
        
    Returns:
        List of available verifiers
    """
    try:
        # Query for users with verifier role
        query = select(Users).where(
            and_(
                Users.role == "verifier",
                Users.is_active == True,
                Users.active_project.is_(None)  # Only show users not assigned to any project
            )
        )
        
        # Execute query
        result = await db.execute(query)
        all_verifiers = result.scalars().all()
        
        # If project_id is provided, exclude users already assigned to this project as verifiers
        excluded_user_ids = set()
        if project_id:
            assigned_query = select(UserProjectAccess.user_id).where(
                and_(
                    UserProjectAccess.project_id == project_id,
                    UserProjectAccess.project_role == "verifier"
                )
            )
            assigned_result = await db.execute(assigned_query)
            excluded_user_ids = {user_id for user_id, in assigned_result}
        
        # Format response
        verifiers = []
        for user in all_verifiers:
            # Skip if already assigned to this project as verifier
            if user.id in excluded_user_ids:
                continue
                
            # Check if user is at max_concurrent_projects limit
            project_count_query = select(func.count(UserProjectAccess.id)).where(
                and_(
                    UserProjectAccess.user_id == user.id,
                    UserProjectAccess.is_active == True,
                    UserProjectAccess.project_role == "verifier"
                )
            )
            project_count_result = await db.execute(project_count_query)
            current_projects = project_count_result.scalar_one()
            
            # Skip if at max concurrent projects
            if user.max_concurrent_projects and current_projects >= user.max_concurrent_projects:
                continue
                
            # Add user to response
            verifiers.append({
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "email": user.email,
                "skills": user.annotation_skills,
                "max_concurrent_projects": user.max_concurrent_projects,
                "current_projects": current_projects,
                "max_concurrent_batches": user.max_concurrent_batches,
                "overall_quality_score": float(user.overall_quality_score) if user.overall_quality_score else None
            })
        
        return UserListResponse(
            users=verifiers,
            total=len(verifiers)
        )
        
    except Exception as e:
        logger.error(f"Error getting available verifiers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving verifiers: {str(e)}"
        )
