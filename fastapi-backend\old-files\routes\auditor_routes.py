import os
import json
import asyncio
from fastapi import APIRouter, HTTPException, status, Depends #type:ignore
from fastapi.responses import Response #type:ignore
from pydantic import BaseModel
from typing import List, Dict, Any
from services.auditor_service import auditor_service
from core.nas_connector import get_ftp_connector
from dependencies.auth import get_current_active_user, require_auditor
from post_db.master_models.users import UserRole
from fastapi import Depends #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
# from post_db.allocation_models.image_annotation import ImageAnnotation
# from post_db.allocation_models.image_verification import ImageVerification
from post_db.connect import get_db_connection as get_db
from sqlalchemy import select

router = APIRouter(
    prefix="/auditor",
    tags=["Auditor"],
    dependencies=[Depends(get_current_active_user), Depends(require_auditor())]
)

class AuditRecordRequest(BaseModel):
    mode: str
    record_id: int
    auditor_username: str
    audit_status: bool
    comments: str = ""

class SaveLabelsRequest(BaseModel):
    mode: str
    dataset_name: str
    verifier_username: str
    file_name: str
    tasks: List[Dict[str, Any]]
    comments: str = ""

class SaveLabelsResponse(BaseModel):
    saved_path: str
    message: str

@router.get("/modes", response_model=List[str])
async def list_modes():
    """
    Return available audit modes for the first dropdown.
    """
    return ["annotation", "verification"]

@router.get("/datasets", response_model=List[Dict[str, Any]])
async def list_datasets(mode: str = None, db: AsyncSession = Depends(get_db)):
    """
    Return available datasets for auditing.
    """
    raw = await auditor_service.get_audit_datasets(db, mode)
    return [
        {"dataset_name": name, "dataset_image_path": path}
        for name, path in raw
    ]

@router.get("/verifiers", response_model=List[str])
async def list_verifiers(dataset_name: str, mode: str, db: AsyncSession = Depends(get_db)):
    """
    Return list of verifiers for a dataset.
    """
    return await auditor_service.get_verifiers_for_dataset(db, dataset_name, mode)

@router.get("/files", response_model=List[str])
async def list_files(dataset_name: str, verifier_username: str, mode: str, db: AsyncSession = Depends(get_db)):
    """
    Return list of JSON files for the selected dataset and verifier, filtered by mode.
    """
    return await auditor_service.get_files(db, dataset_name, verifier_username, mode)

@router.get("/tasks", response_model=List[Dict[str, Any]])
async def list_tasks(mode: str, dataset_name: str, verifier_username: str, file_name: str, db: AsyncSession = Depends(get_db)):
    """
    Load tasks (image paths and labels) for the selected batch.
    """
    return await auditor_service.get_tasks(db, mode, dataset_name, verifier_username, file_name)

@router.get("/image")
async def get_image(path: str):
    """Proxy an image from NAS over HTTP."""
    connector = await get_ftp_connector()
    if not connector:
        raise HTTPException(status.HTTP_503_SERVICE_UNAVAILABLE, detail="NAS connector not available")
    content = await connector.get_file_content(path)
    if not content:
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail="Image not found")
    ext = os.path.splitext(path)[1].lower()
    if ext in (".jpg", ".jpeg"):
        media_type = "image/jpeg"
    elif ext == ".png":
        media_type = "image/png"
    elif ext == ".gif":
        media_type = "image/gif"
    else:
        media_type = "application/octet-stream"
    return Response(content=content, media_type=media_type)

@router.get("/history", response_model=List[Dict[str, Any]])
async def auditor_history(auditor_username: str, db: AsyncSession = Depends(get_db)):
    """
    Return audit history entries for given auditor.
    """
    return await auditor_service.get_audit_history(db, auditor_username)

@router.post("/audit-record", status_code=204)
async def audit_record(record: AuditRecordRequest, db: AsyncSession = Depends(get_db)):
    """
    Update an audit record with JSON body parameters.
    """
    # Use passed record_id directly
    record_id = record.record_id
    success = await auditor_service.update_audit_record(
        db,
        record.mode,
        record_id,
        record.auditor_username,
        record.audit_status,
        record.comments
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Audit record update failed"
        )
    return None

@router.post("/save-labels", response_model=SaveLabelsResponse)
async def save_labels(request_data: SaveLabelsRequest, current_user=Depends(get_current_active_user), db: AsyncSession = Depends(get_db)):
    """
    Save updated labels to NAS and update audit record.
    """
    # Build output JSON data
    output_data: Dict[str, Any] = {}
    for task in request_data.tasks:
        label = task.get('labels', '')
        img_path = task.get('image_path')
        if img_path:
            filename = os.path.basename(img_path)
        else:
            task_id = task.get('task_id', '')
            filename = f"{task_id}.jpg"
        output_data[filename] = label

    # Prepare NAS paths
    # Verified dataset directory
    target_dir = f"/Data/DATP Datasets/VERIFIED DATASET/{request_data.dataset_name}"
   
    connector = await get_ftp_connector()
    if not connector:
        raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="NAS connector not available")
    target_dir = connector.normalize_path(target_dir)
   
    if not await connector.directory_exists(target_dir):
        created = await connector.create_directory(target_dir)
        if not created:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to create directory: {target_dir}")
    # Determine filename for JSON
    json_filename = os.path.basename(request_data.file_name)
    target_path = connector.normalize_path(f"{target_dir}/{json_filename}")
    # Write JSON content
    try:
        content_bytes = json.dumps(output_data, indent=2, ensure_ascii=False).encode('utf-8')
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Failed to serialize JSON: {e}")
   
    saved = await connector.save_file(target_path, content_bytes)
    if not saved:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to save labels file to NAS")

    # Update audit record status
    # Use direct async query
    stmt = select(ImageAnnotation.id if request_data.mode == 'annotation' else ImageVerification.id).where(
        (ImageAnnotation.dataset_name if request_data.mode=='annotation' else ImageVerification.dataset_name) == request_data.dataset_name,
        (ImageAnnotation.dataset_batch_name if request_data.mode=='annotation' else ImageVerification.dataset_batch_name) == os.path.splitext(request_data.file_name)[0],
        (ImageAnnotation.annotator_username if request_data.mode=='annotation' else ImageVerification.annotator_username) == request_data.verifier_username
    )
    res = await db.execute(stmt)
    rec_id_tuple = res.first()
    record_id = rec_id_tuple[0] if rec_id_tuple else None
    if record_id is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Audit record not found")
    success = await auditor_service.update_audit_record(
        db,
        request_data.mode,
        record_id,
        current_user["sub"],
        True,
        request_data.comments
    )
    if not success:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update audit record in database")

    return SaveLabelsResponse(saved_path=target_path, message="Labels saved successfully") 