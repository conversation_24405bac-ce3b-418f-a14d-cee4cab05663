"""
Schemas for project assignment and user access management.
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any
from datetime import datetime


class UserAssignmentRequest(BaseModel):
    """Request model for assigning users to a project."""
    user_ids: List[int] = Field(..., description="List of user IDs to assign to the project")


class UserAssignmentResponse(BaseModel):
    """Response model for user assignment operations."""
    success: bool
    message: str
    assigned_users: List[Dict[str, Any]] = []
    failed_assignments: List[Dict[str, Any]] = []


class ProjectActivationRequest(BaseModel):
    """Request model for activating a project."""
    create_batches: bool = Field(False, description="Whether to create batches upon activation")


class ProjectActivationResponse(BaseModel):
    """Response model for project activation."""
    success: bool
    message: str
    project_id: int
    project_code: str
    project_status: str
    batch_creation_status: Optional[Dict[str, Any]] = None


class UserListResponse(BaseModel):
    """Response model for listing users."""
    users: List[Dict[str, Any]]
    total: int


class ProjectAssignmentSummary(BaseModel):
    """Response model for project assignment summary."""
    project_id: int
    project_code: str
    project_name: str
    project_status: str
    allocation_strategy: Dict[str, Any]
    annotators: List[Dict[str, Any]] = []
    verifiers: List[Dict[str, Any]] = []
    batch_info: Optional[Dict[str, Any]] = None


class BatchAllocationInfo(BaseModel):
    """Model for batch allocation information."""
    batch_id: int
    batch_name: str
    annotator_count: int
    verifier_count: int
    total_annotators_required: int
    total_verifiers_required: int
    status: str  # 'pending', 'annotating', 'verifying', 'completed'


class AssignmentProgress(BaseModel):
    """Model for assignment progress tracking."""
    total_batches: int
    annotator_phase: Dict[str, int]
    verifier_phase: Dict[str, int]


class BatchAllocationResponse(BaseModel):
    """Response model for batch allocation operations."""
    success: bool
    batches: List[BatchAllocationInfo]
    progress: AssignmentProgress
    assigned_users: Dict[str, int]
