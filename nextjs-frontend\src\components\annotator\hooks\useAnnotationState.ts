"use client";

import { useState, useCallback, useEffect } from "react";
import { FormFieldConfig } from "@/components/shared/dynamic-fields";

export function useAnnotationState() {
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [label, setLabel] = useState("");
  const [labels, setLabels] = useState<Record<string, string>>({});
  const [formConfig, setFormConfig] = useState<FormFieldConfig[]>([]);
  const [formData, setFormData] = useState<Record<string, Record<string, any>>>({});
  const [isFormValid, setIsFormValid] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [completedCount, setCompletedCount] = useState(0);
  const [allChangesSaved, setAllChangesSaved] = useState(false);
  const [batchCompleted, setBatchCompleted] = useState(false);
  const [hasReviewed, setHasReviewed] = useState(false);
  const [currentFileSaved, setCurrentFileSaved] = useState(false);

  // Load any existing labels from localStorage
  useEffect(() => {
    const savedLabels = localStorage.getItem("savedLabels");
    if (savedLabels) {
      const parsedLabels = JSON.parse(savedLabels);
      setLabels(parsedLabels);
      setCompletedCount(Object.keys(parsedLabels).length);
    }
  }, []);

  // Navigation functions
  const handlePrevious = useCallback((fileArray: any[], formConfig: FormFieldConfig[]) => {
    if (currentFileIndex > 0) {
      const prevIndex = currentFileIndex - 1;
      setCurrentFileIndex(prevIndex);
      setLabel(labels[fileArray[prevIndex]?.url] ?? "");
      
      // Clear dynamic form data state when moving
      if (formConfig.length > 0) {
        setFormData((prev) => ({ ...prev }));
      }
      
      // Reset saved state - user must save each file explicitly
      setCurrentFileSaved(false);
    }
  }, [currentFileIndex, labels]);

  const handleNext = useCallback((fileArray: any[], formConfig: FormFieldConfig[]) => {
    if (currentFileIndex < fileArray.length - 1) {
      const nextIndex = currentFileIndex + 1;
      setCurrentFileIndex(nextIndex);
      setLabel(labels[fileArray[nextIndex]?.url] ?? "");
      
      if (formConfig.length > 0) {
        setFormData((prev) => ({ ...prev }));
      }
      
      // Reset saved state - user must save each file explicitly
      setCurrentFileSaved(false);
    }
  }, [currentFileIndex, labels]);

  // Zoom functions
  const handleZoomIn = useCallback(
    () => setZoomLevel((prev) => Math.min(prev + 10, 200)),
    []
  );
  
  const handleZoomOut = useCallback(
    () => setZoomLevel((prev) => Math.max(prev - 10, 50)),
    []
  );
  
  const handleResetZoom = useCallback(() => setZoomLevel(100), []);

  // Form data handlers
  const handleFormDataChange = useCallback((fileKey: string, newFormData: Record<string, any>) => {
    setFormData(prevFormData => ({
      ...prevFormData,
      [fileKey]: newFormData
    }));
  }, []);

  const handleFormValidationChange = useCallback((isValid: boolean) => {
    setIsFormValid(isValid);
  }, []);

  // Reset state for new batch
  const resetState = useCallback(() => {
    setCurrentFileIndex(0);
    setLabel("");
    setLabels({});
    setFormData({});
    setCompletedCount(0);
    setAllChangesSaved(false);
    setBatchCompleted(false);
    setHasReviewed(false);
    setCurrentFileSaved(false);
    setZoomLevel(100);
  }, []);

  // Calculate completion
  const calculateCompletedCount = useCallback((fileArray: any[], formConfig: FormFieldConfig[]) => {
    if (fileArray.length === 0) return 0;
    
    let count = 0;
    fileArray.forEach(file => {
      const fileUrl = file.url;
      if (formConfig.length > 0) {
        // For dynamic forms, check if form data exists
        if (formData[fileUrl] && Object.keys(formData[fileUrl]).length > 0) {
          count++;
        }
      } else {
        // For simple labels, check if label exists
        if (labels[fileUrl]) {
          count++;
        }
      }
    });
    return count;
  }, [formData, labels]);

  return {
    // State
    currentFileIndex,
    label,
    labels,
    formConfig,
    formData,
    isFormValid,
    zoomLevel,
    completedCount,
    allChangesSaved,
    batchCompleted,
    hasReviewed,
    currentFileSaved,
    
    // Setters
    setCurrentFileIndex,
    setLabel,
    setLabels,
    setFormConfig,
    setFormData,
    setIsFormValid,
    setZoomLevel,
    setCompletedCount,
    setAllChangesSaved,
    setBatchCompleted,
    setHasReviewed,
    setCurrentFileSaved,
    
    // Handlers
    handlePrevious,
    handleNext,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleFormDataChange,
    handleFormValidationChange,
    resetState,
    calculateCompletedCount
  };
}
