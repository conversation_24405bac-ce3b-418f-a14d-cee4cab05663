pytest_xdist-3.5.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
pytest_xdist-3.5.0.dist-info/LICENSE,sha256=6J7tEHTTqUMZi6E5uAhE9bRFuGC7p0qK6twGEFZhZOo,1054
pytest_xdist-3.5.0.dist-info/METADATA,sha256=6KayKIFj5EEKLxmOShHeQVuh88urk0ZdSm9ikutTn7k,3100
pytest_xdist-3.5.0.dist-info/RECORD,,
pytest_xdist-3.5.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pytest_xdist-3.5.0.dist-info/WHEEL,sha256=Xo9-1PvkuimrydujYJAjF7pCkriuXBpUPEjma1nZyJ0,92
pytest_xdist-3.5.0.dist-info/entry_points.txt,sha256=oeWKQMmhLcrjhaXBWXmgRv3xg6BAn2UZF9PoR2FQ364,68
pytest_xdist-3.5.0.dist-info/top_level.txt,sha256=B9Iy6rAx6Uvy-JTIu9N7Yv_ug1-EzD3nX_CB-odTarY,6
xdist/__init__.py,sha256=U4Rb5msAzlQQCkj7WcX3En_y3BwGTqx7PC4FQ9MArlg,305
xdist/_path.py,sha256=kt1OOINkGIHrsrjHIoXBij-cNmwzoMBMG4ECpk_XCv8,639
xdist/_version.py,sha256=wy34mXzQ8fLJu7i4fZikKwCDGQODEviQb-OrdMe9F4Q,411
xdist/dsession.py,sha256=WZ2qbuELD_b_n-i_7DQ-SkJzPCB_Ab1MUl4kSIqfIIk,20224
xdist/looponfail.py,sha256=wfbhmrAcbBzHeEaYeBwm2thC6wQkP2ExC3QbZXZ1RGg,9357
xdist/newhooks.py,sha256=yUJkgvtVqDZt4e-K0ov2j2V3jz8WzNBgH3v_51ELDU8,2667
xdist/plugin.py,sha256=AR6-N9DBt57hoBxQZ64BN2P4SoVjoYHWjum5kPI_SBA,11561
xdist/remote.py,sha256=nRyAxapMeEvLyMbe4WW2sRpzdSozdfLVOLP-vx0hEWM,12100
xdist/report.py,sha256=-99kuDvKgn8Ze1nawacWBiUkDIUTSQuVUQgdHfrwjpU,817
xdist/scheduler/__init__.py,sha256=9ilXO-X007vG21cIwZSHNFkIc6PTPaqHAUoySaw7puQ,377
xdist/scheduler/each.py,sha256=lYNoEoZfIKwLV6gowQswN9neDcvbKKzlaZjlXiWv3Ho,5141
xdist/scheduler/load.py,sha256=mhnJ5fsU2OXS7rNFjCzgClPdLxv9dHrnCO05jzGhtmU,12269
xdist/scheduler/loadfile.py,sha256=-0CPOY21_jmeoQNmsaVO-dt4FhifM6XpVoaz3ZIyzUw,2172
xdist/scheduler/loadgroup.py,sha256=169AEYjDhk4JfRmjjeQRONDsWd64Hi1Rud-Bfw1R5E0,2167
xdist/scheduler/loadscope.py,sha256=El9iUjlF-Odr8X0dUy9-LrokeRORH3jTd48XdWMgy5Q,14493
xdist/scheduler/worksteal.py,sha256=EklXr4wzfaTe7VWxnJvLOytzsBK6PV9t1XeF1Cp5_-g,11535
xdist/workermanage.py,sha256=WqB7CDING_-m624UG_z4t0f14eHBJkSGso9BbpidTEY,16552
