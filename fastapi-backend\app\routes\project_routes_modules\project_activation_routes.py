"""
Routes for project activation and deactivation.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Path # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, update
from typing import List, Dict, Any
import logging

from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from post_db.master_models.user_project_access import UserProjectAccess
from dependencies.auth import get_current_active_user, require_admin
from schemas.ProjectAssignmentSchemas import ProjectActivationRequest, ProjectActivationResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/projects",
    tags=["Project Activation"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)]
)


class ProjectDeadlineRequest(BaseModel):
    deadline: str


async def get_project_or_404(project_id: int, db: AsyncSession) -> ProjectsRegistry:
    """Helper to fetch project or raise 404 if not found."""
    project = await db.get(ProjectsRegistry, project_id)
    if not project:
        logger.warning(f"Project not found with ID: {project_id}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Project not found")
    return project


async def create_project_batches(project: ProjectsRegistry) -> Dict[str, Any]:
    """
    Create batches in the project database based on assigned annotators.
    
    Args:
        project: Project registry record
        
    Returns:
        Dict: Batch creation results
    """
    try:
        # This is a placeholder for the actual implementation
        # You would connect to the project database and create batches
        
        database_name = project.database_name
        logger.info(f"Creating batches in project database {database_name}")
        
        # TODO: Implement actual batch creation
        # For now, just return success
        return {
            "success": True,
            "message": "Batch creation simulated",
            "batches_created": 0
        }
        
    except Exception as e:
        logger.error(f"Error creating project batches: {str(e)}")
        return {
            "success": False,
            "message": f"Error creating batches: {str(e)}",
            "batches_created": 0
        }


@router.post("/{project_id}/activate", response_model=ProjectActivationResponse)
async def activate_project(
    project_id: int = Path(..., description="Project ID"),
    request: ProjectActivationRequest = ...,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Activate a project, optionally creating batches.
    
    Args:
        project_id: Project ID
        request: Activation request with options
        
    Returns:
        Activation response with results
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project is already active
        if project.project_status == "active":
            return ProjectActivationResponse(
                success=True,
                message="Project is already active",
                project_id=project.id,
                project_code=project.project_code,
                project_status=project.project_status
            )
        
        # Optional: Get allocation strategy for batch creation if needed
        strategy = None
        if project.allocation_strategy_id:
            strategy = await db.get(AllocationStrategies, project.allocation_strategy_id)
        
        # Update project status to active
        project.project_status = "active"
        await db.commit()
        
        # Create batches if requested
        batch_creation_status = None
        if request.create_batches:
            batch_creation_status = await create_project_batches(project)
        
        return ProjectActivationResponse(
            success=True,
            message="Project activated successfully",
            project_id=project.id,
            project_code=project.project_code,
            project_status=project.project_status,
            batch_creation_status=batch_creation_status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error activating project: {str(e)}"
        )


@router.post("/{project_id}/deactivate", response_model=ProjectActivationResponse)
async def deactivate_project(
    project_id: int = Path(..., description="Project ID"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Deactivate a project and clear active_project field for all users assigned to it.
    
    Args:
        project_id: Project ID
        
    Returns:
        Activation response with results
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project is already inactive
        if project.project_status == "inactive":
            return ProjectActivationResponse(
                success=True,
                message="Project is already inactive",
                project_id=project.id,
                project_code=project.project_code,
                project_status=project.project_status
            )
        
        # Update project status to inactive
        project.project_status = "inactive"
        
        # Clear active_project field for all users who have this project as their active project
        from post_db.master_models.users import users as Users
        users_query = select(Users).where(Users.active_project == project.project_code)
        users_result = await db.execute(users_query)
        affected_users = users_result.scalars().all()
        
        # Update all affected users to set active_project to null
        for user in affected_users:
            user.active_project = None
        
        # Set is_active to false for all user_project_access records for this project
        access_query = select(UserProjectAccess).where(
            and_(
                UserProjectAccess.project_id == project_id,
                UserProjectAccess.is_active == True
            )
        )
        access_result = await db.execute(access_query)
        affected_access_records = access_result.scalars().all()
        
        # Update all affected access records to set is_active to false
        for access in affected_access_records:
            access.is_active = False
        
        # Commit all changes
        await db.commit()
        
        # Log the number of users affected
        logger.info(f"Project {project.project_code} deactivated. Cleared active_project for {len(affected_users)} users and deactivated {len(affected_access_records)} access records.")
        
        return ProjectActivationResponse(
            success=True,
            message=f"Project deactivated successfully. Cleared active project for {len(affected_users)} users and deactivated {len(affected_access_records)} access records.",
            project_id=project.id,
            project_code=project.project_code,
            project_status=project.project_status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deactivating project: {str(e)}"
        )


@router.get("/{project_id}/strategy-details")
async def get_project_strategy_details(
    project_id: int = Path(..., description="Project ID"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Get details about a project's allocation strategy.
    
    Args:
        project_id: Project ID
        
    Returns:
        Dict: Strategy details
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project has allocation strategy
        if not project.allocation_strategy_id:
            return {
                "success": False,
                "message": "Project does not have an allocation strategy",
                "project_id": project.id,
                "project_code": project.project_code,
                "project_status": project.project_status,
                "has_strategy": False
            }
        
        # Get allocation strategy
        strategy = await db.get(AllocationStrategies, project.allocation_strategy_id)
        if not strategy:
            return {
                "success": False,
                "message": "Project's allocation strategy not found",
                "project_id": project.id,
                "project_code": project.project_code,
                "project_status": project.project_status,
                "has_strategy": False
            }
        
        # Return strategy details
        return {
            "success": True,
            "project_id": project.id,
            "project_code": project.project_code,
            "project_status": project.project_status,
            "has_strategy": True,
            "strategy": {
                "id": strategy.id,
                "name": strategy.strategy_name,
                "type": strategy.strategy_type,
                "description": strategy.description,
                "num_annotators": strategy.num_annotators,
                "requires_audit": strategy.requires_audit,
                "requires_verification": strategy.requires_verification
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project strategy details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting project strategy details: {str(e)}"
        )


@router.post("/{project_id}/pause", response_model=ProjectActivationResponse)
async def pause_project(
    project_id: int = Path(..., description="Project ID"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Pause a project by setting its status to 'paused'.
    
    Args:
        project_id: Project ID
        
    Returns:
        Activation response with results
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project is already paused
        if project.project_status == "paused":
            return ProjectActivationResponse(
                success=True,
                message="Project is already paused",
                project_id=project.id,
                project_code=project.project_code,
                project_status=project.project_status
            )
        
        # Check if project can be paused (must be active)
        if project.project_status != "active":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot pause project with status '{project.project_status}'. Only active projects can be paused."
            )
        
        # Update project status to paused
        project.project_status = "paused"
        await db.commit()
        
        logger.info(f"Project {project.project_code} paused successfully")
        
        return ProjectActivationResponse(
            success=True,
            message="Project paused successfully",
            project_id=project.id,
            project_code=project.project_code,
            project_status=project.project_status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error pausing project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error pausing project: {str(e)}"
        )


@router.post("/{project_id}/complete", response_model=ProjectActivationResponse)
async def complete_project(
    project_id: int = Path(..., description="Project ID"),
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Mark a project as completed by setting its status to 'completed'.
    
    Args:
        project_id: Project ID
        
    Returns:
        Activation response with results
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Check if project is already completed
        if project.project_status == "completed":
            return ProjectActivationResponse(
                success=True,
                message="Project is already completed",
                project_id=project.id,
                project_code=project.project_code,
                project_status=project.project_status
            )
        
        # Update project status to completed
        project.project_status = "completed"
        
        # Clear active_project field for all users who have this project as their active project
        from post_db.master_models.users import users as Users
        users_query = select(Users).where(Users.active_project == project.project_code)
        users_result = await db.execute(users_query)
        affected_users = users_result.scalars().all()
        
        # Update all affected users to set active_project to null
        for user in affected_users:
            user.active_project = None
        
        # Set is_active to false for all user_project_access records for this project
        access_query = select(UserProjectAccess).where(
            and_(
                UserProjectAccess.project_id == project_id,
                UserProjectAccess.is_active == True
            )
        )
        access_result = await db.execute(access_query)
        affected_access_records = access_result.scalars().all()
        
        # Update all affected access records to set is_active to false
        for access in affected_access_records:
            access.is_active = False
        
        # Commit all changes
        await db.commit()
        
        # Log the completion
        logger.info(f"Project {project.project_code} completed successfully. Cleared active_project for {len(affected_users)} users and deactivated {len(affected_access_records)} access records.")
        
        return ProjectActivationResponse(
            success=True,
            message=f"Project completed successfully. Cleared active project for {len(affected_users)} users and deactivated {len(affected_access_records)} access records.",
            project_id=project.id,
            project_code=project.project_code,
            project_status=project.project_status
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing project: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error completing project: {str(e)}"
        )


@router.put("/{project_id}/deadline", response_model=Dict[str, Any])
async def set_project_deadline(
    project_id: int = Path(..., description="Project ID"),
    request: ProjectDeadlineRequest = ...,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Set or update a project's deadline.
    
    Args:
        project_id: Project ID
        request: Deadline request with deadline date
        
    Returns:
        Dict: Success response with updated project info
    """
    try:
        # Get project
        project = await get_project_or_404(project_id, db)
        
        # Validate deadline format (should be YYYY-MM-DD) and convert to date object
        try:
            from datetime import datetime, date
            deadline_date = datetime.strptime(request.deadline, '%Y-%m-%d').date()
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deadline format. Expected YYYY-MM-DD"
            )
        
        # Update project deadline with date object
        project.project_deadline = deadline_date
        await db.commit()
        await db.refresh(project)
        
        logger.info(f"Project {project.project_code} deadline set to {request.deadline}")
        
        return {
            "success": True,
            "message": "Project deadline updated successfully",
            "project_id": project.id,
            "project_code": project.project_code,
            "project_deadline": project.project_deadline
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting project deadline: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error setting project deadline: {str(e)}"
        )
