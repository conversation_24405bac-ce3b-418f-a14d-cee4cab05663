"""
Django-style Alembic environment for project_db
This file is persistent and defines how migrations work for this "app"
"""
import sys
import os
from pathlib import Path
from sqlalchemy import engine_from_config, pool
from alembic import context

# Robust path resolution for finding the app directory
def find_app_directory():
    """Find the app directory containing post_db module"""
    # Start with current file location
    current_dir = os.path.dirname(os.path.abspath(__file__))
    search_dir = current_dir
    max_depth = 20
    
    # Search up the directory tree
    for _ in range(max_depth):
        if os.path.exists(os.path.join(search_dir, 'post_db')):
            return search_dir
        parent = os.path.dirname(search_dir)
        if parent == search_dir:  # Reached root directory
            break
        search_dir = parent
    
    # Fallback strategies
    fallback_paths = [
        os.getcwd(),  # Current working directory
        os.path.join(os.getcwd(), 'app'),  # app subdirectory
        os.path.join(os.getcwd(), 'fastapi-backend', 'app'),  # common structure
    ]
    
    for path in fallback_paths:
        if os.path.exists(os.path.join(path, 'post_db')):
            return path
    
    # Last resort: try to find via __file__ from any imported module
    try:
        import post_db
        post_db_file = getattr(post_db, '__file__', None)
        if post_db_file:
            return os.path.dirname(os.path.dirname(post_db_file))
    except ImportError:
        pass
    
    # If all else fails, return None and let the import fail with a clear error
    return None

# Set up the path
app_dir = find_app_directory()
if app_dir:
    sys.path.insert(0, app_dir)
    print(f"Added to Python path: {app_dir}")
else:
    print("Warning: Could not find app directory containing post_db module")

# This is the Alembic Config object
config = context.config

# Import the appropriate Base for this "app"
try:
    from post_db.project_base import ProjectBase
    target_metadata = ProjectBase.metadata
    print(f"Loaded {len(target_metadata.tables)} tables for project_db")
    
    # Print table names for debugging (like Django's --verbosity=2)
    table_names = list(target_metadata.tables.keys())
    if table_names:
        print(f"Tables: {', '.join(table_names)}")
    
except ImportError as e:
    print(f"Failed to import models for project_db: {e}")
    if app_dir:
        print(f"App directory: {app_dir}")
        print(f"Directory contents: {os.listdir(app_dir) if os.path.exists(app_dir) else 'Not found'}")
    else:
        print("App directory not found")
    target_metadata = None

def run_migrations_offline():
    """Run migrations in 'offline' mode (like Django's --fake)"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """Run migrations in 'online' mode (normal Django migrate)"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

# Run the appropriate migration mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
