from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from typing import Dict, Any, List, Optional
from pathlib import Path
import json
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from post_db.master_models.projects_registry import ProjectsRegistry
from core.session_manager import get_master_db_session

from ..model_endpoints.image_vqa import (
    get_image_vqa_service,
    ImageVQAService
)
from services.ai_processing_service import get_ai_processing_service, AIProcessingService
router = APIRouter()

@router.post("/batch/project", response_model=Dict[str, Any])
async def batch_vqa_project_folder(
    project_code: str = Form(...),
    question: str = Form(...),
    model_name: str = Form(...),
    ai_service: AIProcessingService = Depends(get_ai_processing_service),
    service: ImageVQAService = Depends(get_image_vqa_service),
    db: AsyncSession = Depends(get_master_db_session)
):
    try:
        stmt = select(ProjectsRegistry).where(
            ProjectsRegistry.project_code == project_code,
            ProjectsRegistry.folder_path.isnot(None),
            ProjectsRegistry.project_status == 'active'
        )
        result = await db.execute(stmt)
        project = result.scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project '{project_code}' not found or no folder path available"
            )

        folder_path = project.folder_path or ""

        if model_name and model_name.startswith("AI-Processing/"):
            model_name = model_name[len("AI-Processing/"):]

        # Use empty file identifiers list - let the AI service handle file discovery
        file_identifiers = []
        
        # Use the new integrated AI processing service
        result = await ai_service.process_files_for_project(
            project_code=project_code,
            file_identifiers=file_identifiers,
            model_name=model_name,
            processing_type="vqa",
            question=question  # Pass the question as a parameter
        )


        result["project_code"] = project_code
        result["project_name"] = project.project_name
        result["folder_path"] = folder_path

        return result

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error processing project folder: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing project folder: {str(e)}"
        )