import logging
from typing import Dict, Any, Optional
from core.minio_connector import MinIONASConnector
from schemas.MinIOConfig import MinIOConfig
from post_db.master_models.projects_registry import ProjectsRegistry
from .config import get_settings

logger = logging.getLogger(__name__)

async def create_minio_connector(config: Optional[MinIOConfig] = None) -> Optional[MinIONASConnector]:
    """Create a MinIO connector instance"""
    try:
        if config is None:
            config = MinIOConfig.from_env()
        
        logger.info(f"Creating MinIO connector for endpoint: {config.endpoint}")
        connector = MinIONASConnector(config)
        
        if await connector.authenticate():
            logger.info("MinIO connector created and authenticated successfully")
            return connector
        else:
            logger.error("MinIO authentication failed")
            return None
    except Exception as e:
        logger.error(f"Error creating MinIO connector: {e}")
        return None

async def create_minio_connector_from_settings() -> Optional[MinIONASConnector]:
    """Create a MinIO connector from application settings"""
    try:
        settings = get_settings()
        config = MinIOConfig.from_settings(settings)
        
        if not settings.minio_settings.is_connected:
            logger.warning("MinIO not configured in environment variables")
            return None
        
        return await create_minio_connector(config)
    except Exception as e:
        logger.error(f"Error creating MinIO connector from settings: {e}")
        return None

async def get_default_minio_connector() -> Optional[MinIONASConnector]:
    """Get default MinIO connector using environment variables or settings"""
    try:
        # Try to create from environment first
        connector = await create_minio_connector()
        if connector:
            return connector
        
        # Fallback to settings
        return await create_minio_connector_from_settings()
    except Exception as e:
        logger.error(f"Error getting default MinIO connector: {e}")
        return None

async def get_minio_connector_from_credentials(minio_credentials: dict) -> Optional[MinIONASConnector]:
    """Get a MinIO connector using project-specific credentials"""
    try:
        if not minio_credentials:
            logger.warning("No MinIO credentials provided")
            return None

        endpoint = minio_credentials.get("minio_endpoint")
        access_key = minio_credentials.get("minio_access_key")
        secret_key = minio_credentials.get("minio_secret_key")
        bucket_name = minio_credentials.get("minio_bucket_name", "default-bucket")
        secure = minio_credentials.get("minio_secure", False)
        region = minio_credentials.get("minio_region")

        if not all([endpoint, access_key, secret_key]):
            logger.warning("Missing MinIO credentials in project registry")
            return None

        config = MinIOConfig(
            endpoint=endpoint,
            access_key=access_key,
            secret_key=secret_key,
            bucket_name=bucket_name,
            secure=secure,
            region=region
        )
        
        connector = await create_minio_connector(config)
        if not connector or not await connector.authenticate():
            logger.error("MinIO authentication failed on connector")
            return None

        logger.info(f"MinIO connector ready for use with project credentials")
        return connector
    except Exception as e:
        logger.error(f"Error creating MinIO connector from project credentials: {e}")
        return None

async def check_minio_connection(minio_credentials: Dict[str, Any]) -> bool:
    """Check if MinIO connection can be established with given credentials"""
    try:
        logger.info(f"Checking MinIO connection with credentials")
        
        if not minio_credentials or not minio_credentials.get('minio_endpoint'):
            logger.error(f"Invalid MinIO credentials provided: {minio_credentials}")
            return False
        
        minio_connector = await get_minio_connector_from_credentials(minio_credentials)
        if not minio_connector:
            logger.error("Failed to create MinIO connector")
            return False
        
        logger.info(f"Successfully verified MinIO connection")
        return True
        
    except Exception as e:
        logger.error(f"Error checking MinIO connection: {e}")
        return False

async def validate_and_get_minio_connector(minio_credentials: Dict[str, Any]) -> Optional[MinIONASConnector]:
    """Validate MinIO credentials and return connector if valid"""
    try:
        if not minio_credentials:
            logger.error("No MinIO credentials provided")
            return None
        
        # Check connection first
        if not await check_minio_connection(minio_credentials):
            logger.error("MinIO connection validation failed")
            return None
        
        # Get connector
        connector = await get_minio_connector_from_credentials(minio_credentials)
        if not connector:
            logger.error("Failed to create MinIO connector after validation")
            return None
        
        logger.info("MinIO connector validated and ready")
        return connector
        
    except Exception as e:
        logger.error(f"Error validating MinIO connector: {e}")
        return None

async def get_project_minio_connector(db, project: ProjectsRegistry) -> Optional[MinIONASConnector]:
    """Get MinIO connector for a specific project"""
    try:
        if not project:
            logger.error("No project provided")
            return None
        
        # Get MinIO credentials from project
        minio_credentials = project.credentials
        if not minio_credentials or project.connection_type != "MinIO":
            logger.warning(f"No MinIO credentials found for project {project.project_code}")
            return None
        
        # Create and validate connector
        connector = await validate_and_get_minio_connector(minio_credentials)
        if not connector:
            logger.error(f"Failed to create MinIO connector for project {project.project_code}")
            return None
        
        logger.info(f"MinIO connector ready for project {project.project_code}")
        return connector
        
    except Exception as e:
        logger.error(f"Error getting project MinIO connector: {e}")
        return None

async def upload_file_to_minio(
    minio_connector: MinIONASConnector,
    local_file_path: str,
    remote_file_path: str
) -> bool:
    """Upload a file to MinIO using the connector"""
    try:
        if not minio_connector or not minio_connector.authenticated:
            logger.error("MinIO connector not authenticated")
            return False
        
        success = await minio_connector.upload_file(local_file_path, remote_file_path)
        if success:
            logger.info(f"Successfully uploaded {local_file_path} to {remote_file_path}")
        else:
            logger.error(f"Failed to upload {local_file_path} to {remote_file_path}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error uploading file to MinIO: {e}")
        return False

async def download_file_from_minio(
    minio_connector: MinIONASConnector,
    remote_file_path: str,
    local_file_path: str
) -> bool:
    """Download a file from MinIO using the connector"""
    try:
        if not minio_connector or not minio_connector.authenticated:
            logger.error("MinIO connector not authenticated")
            return False
        
        success = await minio_connector.download_file(remote_file_path, local_file_path)
        if success:
            logger.info(f"Successfully downloaded {remote_file_path} to {local_file_path}")
        else:
            logger.error(f"Failed to download {remote_file_path} to {local_file_path}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error downloading file from MinIO: {e}")
        return False

async def get_file_content_from_minio(
    minio_connector: MinIONASConnector,
    remote_file_path: str
) -> Optional[bytes]:
    """Get file content from MinIO as bytes"""
    try:
        if not minio_connector or not minio_connector.authenticated:
            logger.error("MinIO connector not authenticated")
            return None
        
        content = await minio_connector.get_file_content(remote_file_path)
        logger.info(f"Successfully retrieved content for {remote_file_path}")
        return content
        
    except Exception as e:
        logger.error(f"Error getting file content from MinIO: {e}")
        return None

async def generate_minio_presigned_url(
    minio_connector: MinIONASConnector,
    remote_file_path: str,
    expires_in: int = 3600
) -> Optional[str]:
    """Generate a presigned URL for temporary access to a file"""
    try:
        if not minio_connector or not minio_connector.authenticated:
            logger.error("MinIO connector not authenticated")
            return None
        
        url = await minio_connector.generate_presigned_url(remote_file_path, expires_in)
        logger.info(f"Successfully generated presigned URL for {remote_file_path}")
        return url
        
    except Exception as e:
        logger.error(f"Error generating presigned URL: {e}")
        return None

async def cache_minio_credentials(credentials: Dict[str, Any], username: str) -> bool:
    """Cache MinIO credentials for a user"""
    try:
        from cache import cache_set
        cache_key = f"minio_credentials:{username}"
        success = await cache_set(cache_key, credentials, ttl=3600)  # Cache for 1 hour
        if success:
            logger.info(f"Cached MinIO credentials for user {username}")
        else:
            logger.warning(f"Failed to cache MinIO credentials for user {username}")
        return success
    except Exception as e:
        logger.error(f"Error caching MinIO credentials for user {username}: {e}")
        return False

async def clear_minio_credentials_cache(username: str) -> bool:
    """Clear cached MinIO credentials for a user"""
    try:
        from cache import cache_delete
        cache_key = f"minio_credentials:{username}"
        success = await cache_delete(cache_key)
        if success:
            logger.info(f"Cleared MinIO credentials cache for user {username}")
        else:
            logger.warning(f"Failed to clear MinIO credentials cache for user {username}")
        return success
    except Exception as e:
        logger.error(f"Error clearing MinIO credentials cache for user {username}: {e}")
        return False

async def is_minio_available() -> bool:
    """Check if MinIO is available using default configuration"""
    try:
        connector = await get_default_minio_connector()
        return connector is not None and connector.authenticated
    except Exception as e:
        logger.error(f"Error checking MinIO availability: {e}")
        return False

async def get_minio_connector_for_project(db, project: ProjectsRegistry) -> Optional[MinIONASConnector]:
    """Get MinIO connector for a project, with fallback to default configuration"""
    try:
        # First try project-specific MinIO credentials
        if project and project.minio_credentials:
            connector = await get_project_minio_connector(db, project)
            if connector:
                return connector
        
        # Fallback to default MinIO configuration
        logger.info(f"No project-specific MinIO credentials found for {project.project_code}, trying default configuration")
        return await get_default_minio_connector()
        
    except Exception as e:
        logger.error(f"Error getting MinIO connector for project: {e}")
        return None
