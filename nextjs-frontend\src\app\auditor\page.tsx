"use client";

import { Suspense } from "react";
import { Dashboard, TaskList, History } from "@/components/auditor";
import { useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

function AuditorPageContent() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const searchParams = useSearchParams();
  const view = searchParams.get("view") || "dashboard";

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return <div>Please log in to access this page.</div>;
  }

  return (
    <>
      {view === "dashboard" && <Dashboard />}
      {view === "tasks" && <TaskList />}
      {view === "history" && <History username={user.username} />}
    </>
  );
}

export default function AuditorPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
        </div>
      }
    >
      <AuditorPageContent />
    </Suspense>
  );
}
