import React from 'react';
import { FormFieldConfig } from './DynamicField';

interface LongAnswerFieldProps {
  config: FormFieldConfig;
  value: string;
  onChange: (fieldName: string, value: any) => void;
  error?: string;
}

export default function LongAnswerField({ 
  config, 
  value, 
  onChange, 
  error 
}: LongAnswerFieldProps) {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    
    // Apply max_length validation if specified
    if (config.max_length && newValue.length > config.max_length) {
      return;
    }
    
    onChange(config.field_name, newValue);
  };

  return (
    <div className="mb-4">
      <label htmlFor={config.field_name} className="block text-sm font-medium text-gray-700 mb-2">
        {config.label}
        {config.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {config.description && (
        <p className="text-sm text-gray-500 mb-2">{config.description}</p>
      )}
      <textarea
        id={config.field_name}
        name={config.field_name}
        className={`block w-full border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-vertical ${
          error ? 'border-red-300' : 'border-gray-300'
        }`}
        placeholder={config.placeholder || `Enter ${config.label.toLowerCase()}`}
        value={value || ''}
        onChange={handleChange}
        maxLength={config.max_length}
        required={config.required}
        rows={4}
        aria-describedby={error ? `${config.field_name}-error` : undefined}
      />
      {config.max_length && (
        <p className="mt-1 text-xs text-gray-500">
          {value?.length || 0}/{config.max_length} characters
        </p>
      )}
      {error && (
        <p id={`${config.field_name}-error`} className="mt-1 text-sm text-red-600">
          {error}
        </p>
      )}
    </div>
  );
}
