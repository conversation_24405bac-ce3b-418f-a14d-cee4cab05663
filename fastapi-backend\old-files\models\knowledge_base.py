"""
Knowledge Base models for the application.
Contains models for storing reference data for synthetic dataset generation.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, func
from . import Base

class KnowledgeEntry(Base):
    """
    Knowledge Base entry model.
    Stores reference data that can be used for synthetic dataset generation.
    """
    __tablename__ = "knowledge_base"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False, index=True)
    topic = Column(String(100), nullable=False, index=True)
    content = Column(Text, nullable=False)
    source = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<KnowledgeEntry(id={self.id}, title='{self.title}', topic='{self.topic}')>"
    
    def to_dict(self):
        """Convert the model to a dictionary."""
        return {
            "id": self.id,
            "title": self.title,
            "topic": self.topic,
            "content": self.content,
            "source": self.source,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 