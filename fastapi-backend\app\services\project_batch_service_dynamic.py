"""
Enhanced Project Batch Service to handle dynamic columns based on allocation strategies.
This file extends the functionality of the ProjectBatchService to work with dynamic schemas.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from core.session_manager import get_project_db_session

logger = logging.getLogger(__name__)

class DynamicProjectBatchService:
    """
    Service for managing project batches with dynamic schemas.
    This class extends the functionality of ProjectBatchService to work with
    dynamic columns in allocation_batches and other tables.
    """
    
    def __init__(self):
        pass
    
    async def get_project_strategy(self, project_code: str) -> Optional[AllocationStrategies]:
        """
        Get the allocation strategy for a project.
        
        Args:
            project_code: Project code
            
        Returns:
            Optional[AllocationStrategies]: The project's allocation strategy, or None if not found
        """
        try:
            async with get_master_db_context() as session:
                # Get project info
                result = await session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
                )
                project = result.scalar_one_or_none()
                
                if not project or not project.allocation_strategy_id:
                    return None
                
                # Get strategy info
                strategy_result = await session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                return strategy
                
        except Exception as e:
            logger.error(f"Error getting project strategy: {str(e)}")
            return None
    
    async def assign_annotators_to_batch(
        self, 
        project_code: str, 
        batch_id: int, 
        annotator_ids: List[int]
    ) -> Dict[str, Any]:
        """
        Assign annotators to a batch based on the project's allocation strategy.
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            annotator_ids: List of annotator user IDs
            
        Returns:
            Dict: Result with success status and details
        """
        try:
            # Get the project's allocation strategy
            strategy = await self.get_project_strategy(project_code)
            if not strategy:
                return {
                    "success": False,
                    "error": f"No allocation strategy found for project {project_code}"
                }
            
            # Check if we have enough annotators
            if len(annotator_ids) < strategy.num_annotators:
                return {
                    "success": False,
                    "error": f"Strategy requires {strategy.num_annotators} annotators, but only {len(annotator_ids)} provided"
                }
            
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                # Get batch info
                from post_db.allocation_models.allocation_batches import AllocationBatches
                result = await session.execute(
                    select(AllocationBatches).where(AllocationBatches.id == batch_id)
                )
                batch = result.scalar_one_or_none()
                
                if not batch:
                    return {
                        "success": False,
                        "error": f"Batch {batch_id} not found"
                    }
                
                # Prepare update data for dynamic columns
                update_data = {}
                
                # Assign annotators to dynamic columns
                for i, annotator_id in enumerate(annotator_ids[:strategy.num_annotators], 1):
                    column_name = f"annotator_{i}"
                    if hasattr(batch, column_name):
                        update_data[column_name] = annotator_id
                
                # Update the batch
                if update_data:
                    await session.execute(
                        update(AllocationBatches)
                        .where(AllocationBatches.id == batch_id)
                        .values(**update_data)
                    )
                    await session.commit()
                    
                    return {
                        "success": True,
                        "message": f"Assigned {len(update_data)} annotators to batch {batch_id}",
                        "batch_id": batch_id,
                        "assigned_annotators": update_data
                    }
                else:
                    return {
                        "success": False,
                        "error": f"No annotator columns found in batch table for strategy {strategy.strategy_name}"
                    }
                
        except Exception as e:
            logger.error(f"Error assigning annotators to batch: {str(e)}")
            return {
                "success": False,
                "error": f"Error assigning annotators: {str(e)}"
            }
    
    async def assign_verifier_to_batch(
        self, 
        project_code: str, 
        batch_id: int, 
        verifier_id: int
    ) -> Dict[str, Any]:
        """
        Assign a verifier to a batch for strategies that require verification.
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            verifier_id: Verifier user ID
            
        Returns:
            Dict: Result with success status and details
        """
        try:
            # Get the project's allocation strategy
            strategy = await self.get_project_strategy(project_code)
            if not strategy:
                return {
                    "success": False,
                    "error": f"No allocation strategy found for project {project_code}"
                }
            
            # Check if strategy requires verification
            if not strategy.requires_verification:
                return {
                    "success": False,
                    "error": f"Strategy {strategy.strategy_name} does not require verification"
                }
            
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                # Get batch info
                from post_db.allocation_models.allocation_batches import AllocationBatches
                result = await session.execute(
                    select(AllocationBatches).where(AllocationBatches.id == batch_id)
                )
                batch = result.scalar_one_or_none()
                
                if not batch:
                    return {
                        "success": False,
                        "error": f"Batch {batch_id} not found"
                    }
                
                # Check if batch has verifier column
                if not hasattr(batch, "verifier"):
                    return {
                        "success": False,
                        "error": f"Batch table does not have verifier column"
                    }
                
                # Update the batch
                await session.execute(
                    update(AllocationBatches)
                    .where(AllocationBatches.id == batch_id)
                    .values(verifier=verifier_id)
                )
                await session.commit()
                
                return {
                    "success": True,
                    "message": f"Assigned verifier to batch {batch_id}",
                    "batch_id": batch_id,
                    "verifier_id": verifier_id
                }
                
        except Exception as e:
            logger.error(f"Error assigning verifier to batch: {str(e)}")
            return {
                "success": False,
                "error": f"Error assigning verifier: {str(e)}"
            }
    
   
    async def get_batch_assignments(
        self, 
        project_code: str, 
        batch_id: int
    ) -> Dict[str, Any]:
        """
        Get all assignments for a batch (annotators, auditors, etc.).
        
        Args:
            project_code: Project code
            batch_id: Batch ID
            
        Returns:
            Dict: Batch assignment information
        """
        try:
            # Get the project's allocation strategy
            strategy = await self.get_project_strategy(project_code)
            if not strategy:
                return {
                    "success": False,
                    "error": f"No allocation strategy found for project {project_code}"
                }
            
            # Get the project database session
            async with get_project_db_session(project_code) as session:
                # Get batch info
                from post_db.allocation_models.allocation_batches import AllocationBatches
                result = await session.execute(
                    select(AllocationBatches).where(AllocationBatches.id == batch_id)
                )
                batch = result.scalar_one_or_none()
                
                if not batch:
                    return {
                        "success": False,
                        "error": f"Batch {batch_id} not found"
                    }
                
                # Extract dynamic column values
                assignments = {
                    "batch_id": batch.id,
                    "batch_identifier": batch.batch_identifier,
                    "batch_status": batch.batch_status,
                    "annotators": {},
                    "auditors": {},
                    "verification": {}
                }
                
                # Get annotator assignments
                for i in range(1, strategy.num_annotators + 1):
                    annotator_col = f"annotator_{i}"
                    review_col = f"annotator_{i}_review"
                    
                    if hasattr(batch, annotator_col):
                        annotator_id = getattr(batch, annotator_col)
                        review_id = getattr(batch, review_col, None) if hasattr(batch, review_col) else None
                        
                        if annotator_id:
                            assignments["annotators"][f"annotator_{i}"] = {
                                "user_id": annotator_id,
                                "review_id": review_id
                            }
                
                # Get auditor assignments if applicable
                if strategy.requires_audit and strategy.audit_levels > 0:
                    for i in range(1, strategy.audit_levels + 1):
                        auditor_col = f"auditor_{i}"
                        review_col = f"auditor_{i}_review"
                        
                        if hasattr(batch, auditor_col):
                            auditor_id = getattr(batch, auditor_col)
                            review_id = getattr(batch, review_col, None) if hasattr(batch, review_col) else None
                            
                            if auditor_id:
                                assignments["auditors"][f"auditor_{i}"] = {
                                    "user_id": auditor_id,
                                    "review_id": review_id
                                }
                
                # Get verifier assignment for parallel strategies
                if strategy.strategy_type == "parallel":
                    if hasattr(batch, "verifier"):
                        verifier_id = getattr(batch, "verifier")
                        verifier_review = getattr(batch, "verifier_review", None) if hasattr(batch, "verifier_review") else None
                        
                        if verifier_id:
                            assignments["verification"]["verifier"] = {
                                "user_id": verifier_id,
                                "review_data": verifier_review
                            }
                
                return {
                    "success": True,
                    "assignments": assignments,
                    "strategy": {
                        "id": strategy.id,
                        "name": strategy.strategy_name,
                        "type": strategy.strategy_type,
                        "num_annotators": strategy.num_annotators,
                        "audit_levels": strategy.audit_levels if strategy.requires_audit else 0,
                        "requires_verification": strategy.requires_audit
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting batch assignments: {str(e)}")
            return {
                "success": False,
                "error": f"Error getting batch assignments: {str(e)}"
            }
