"""
Cache module for the application.

Provides core Redis caching utilities and domain-specific cache modules.
"""

# Import core functionality
from .redis_connector import (
    set_redis_client, get_redis_client, create_redis_client,
    set_redis_enabled, is_redis_enabled,
    cache_set, cache_get, cache_delete, cache_exists,
    flush_cache, delete_keys_by_pattern
)

# Import base functionality
from .base import serialize_for_cache

# Domain-specific cache modules
from . import auth_cache
from . import admin_cache
from . import fetch_data_cache
from . import annotator_cache

# Version
__version__ = '1.0.0'

# Public API
__all__ = [
    # Core functions
    "set_redis_client", "get_redis_client", "create_redis_client",
    "set_redis_enabled", "is_redis_enabled",
    "cache_set", "cache_get", "cache_delete", "cache_exists",
    "flush_cache", "delete_keys_by_pattern",
    # Serializer
    "serialize_for_cache",
    # Domain-specific modules
    "auth_cache", "admin_cache", "auditor_cache",
    "fetch_data_cache", "annotator_cache", "supervision_cache",
]
