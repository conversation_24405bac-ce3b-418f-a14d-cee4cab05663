"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import {
  FaTelegram,
  FaFilter,
  FaDownload,
  FaSearch,
  FaSync,
  FaCheckSquare,
  FaSquare,
  FaCloudUploadAlt,
  FaEdit,
  FaTimes,
  FaImages,
  FaInfoCircle,
  FaList,
} from "react-icons/fa";
import JSZip from "jszip";
import { authFetch } from "@/lib/authFetch";
import { API_BASE_URL } from "@/lib/api";
import {
  TelegramChannel,
  TelegramImage,
  TelegramImagesProps,
  showStatus as utilShowStatus,
  fetchTelegramChannels,
  checkTelegramAuth,
} from "./types";
import ImageEditor from "./ImageEditor";

export default function TelegramImages({ channelId }: TelegramImagesProps) {
  const [channels, setChannels] = useState<TelegramChannel[]>([]);
  const [globalLoading, setGlobalLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<{
    message: string;
    type: "success" | "danger" | "warning" | "info";
  } | null>(null);

  const [dateStructure, setDateStructure] = useState<
    Record<string, Record<string, string[]>>
  >({});
  const [yearList, setYearList] = useState<string[]>([]);
  const [monthList, setMonthList] = useState<string[]>([]);
  const [dayList, setDayList] = useState<string[]>([]);
  const [selectedYear, setSelectedYear] = useState<string>("");
  const [selectedMonth, setSelectedMonth] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");

  const [images, setImages] = useState<TelegramImage[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const [selectedImages, setSelectedImages] = useState<number[]>([]);

  const [previewModalOpen, setPreviewModalOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState<string>("");
  const [previewCaption, setPreviewCaption] = useState<string>("");

  const [isUploading, setIsUploading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadResults, setUploadResults] = useState<{
    folder_name: string;
    date_folder_name?: string;
    date_folder_link?: string;
    folder_link: string;
    uploaded_files: { name: string; link: string }[];
  } | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const [channelTitle, setChannelTitle] = useState<string>("");
  const [editorModalOpen, setEditorModalOpen] = useState(false);
  const [currentEditingImage, setCurrentEditingImage] =
    useState<TelegramImage | null>(null);
  const [uploadMenuOpen, setUploadMenuOpen] = useState(false);
  const [isDriveConnected, setIsDriveConnected] = useState(false);

  // Status Toast Helper
  const showStatus = useCallback(
    (
      msg: string,
      type: "success" | "danger" | "warning" | "info" = "success",
      duration = 3000
    ) => {
      utilShowStatus(setStatusMessage, msg, type, duration);
    },
    []
  );

  // Upload Dropdown
  const UploadDropdown = ({
    isOpen,
    onToggle,
    onUpload,
    disabled = false,
    buttonText = "Upload",
    buttonClass = "px-4 py-2 rounded focus:outline-none",
  }: {
    isOpen: boolean;
    onToggle: () => void;
    onUpload: () => void;
    disabled?: boolean;
    buttonText?: string;
    buttonClass?: string;
  }) => (
    <div className="relative upload-dropdown">
      <button
        onClick={() => isDriveConnected && onToggle()}
        disabled={!isDriveConnected || disabled}
        className={`${buttonClass} ${
          isDriveConnected && !disabled
            ? "bg-green-500 text-white hover:bg-green-600"
            : "bg-gray-200 text-gray-400 cursor-not-allowed"
        }`}
      >
        <FaCloudUploadAlt className="inline-block mr-1" /> {buttonText}
      </button>
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-xl z-50">
          <button
            onClick={() => {
              onUpload();
              onToggle();
            }}
            disabled={!isDriveConnected}
            className={`w-full text-left px-4 py-2 rounded-t-lg ${
              isDriveConnected
                ? "hover:bg-gray-100"
                : "text-gray-400 cursor-not-allowed"
            }`}
          >
            Google Drive
          </button>
          <button
            disabled
            className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed"
          >
            AWS (Coming Soon)
          </button>
          <button
            disabled
            className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed rounded-b-lg"
          >
            Azure (Coming Soon)
          </button>
        </div>
      )}
    </div>
  );

  // Hide upload menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest(".upload-dropdown")) {
        setUploadMenuOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Channels, Auth, Drive
  const fetchChannels = useCallback(async () => {
    try {
      const channelData = await fetchTelegramChannels();
      setChannels(channelData);
    } catch (err) {
      console.error("Failed to load channels:", err);
      showStatus("Error loading channels", "danger");
    }
  }, [showStatus]);

  useEffect(() => {
    (async () => {
      try {
        const authData = await checkTelegramAuth();
        if (!authData.authenticated) {
          window.location.href = "/admin/fetch-data/telegram-connect";
          return;
        }
      } catch {
        window.location.href = "/admin/fetch-data/telegram-connect";
        return;
      }
      fetchChannels();
    })();
  }, [fetchChannels]);

  useEffect(() => {
    (async () => {
      try {
        const res = await authFetch(
          `${API_BASE_URL}/api/admin/check-google-drive-connection`
        );
        const json = await res.json();
        setIsDriveConnected(!!json.data?.connected);
      } catch {
        setIsDriveConnected(false);
      }
    })();
  }, []);

  // Dates Structure
  const organizeDates = (dates: string[]) =>
    dates.reduce((acc, dateStr) => {
      const [year, month, day] = dateStr.split("-");
      acc[year] = acc[year] || {};
      acc[year][month] = acc[year][month] || [];
      if (!acc[year][month].includes(day)) acc[year][month].push(day);
      return acc;
    }, {} as Record<string, Record<string, string[]>>);

  const fetchDates = useCallback(
    async (channelId: number) => {
      setGlobalLoading(true);
      try {
        const res = await fetch(
          `${API_BASE_URL}/telegram/dates?channel_id=${channelId}`,
          { credentials: "include" }
        );
        const json = await res.json();
        const struct = organizeDates(json.dates || []);
        setDateStructure(struct);
        const years = Object.keys(struct).sort((a, b) => b.localeCompare(a));
        setYearList(years);
        setSelectedYear("");
        setSelectedMonth("");
        setSelectedDate("");
        setMonthList([]);
        setDayList([]);
      } catch (err) {
        console.error("Failed to load dates:", err);
        showStatus("Error loading dates", "danger");
      } finally {
        setGlobalLoading(false);
      }
    },
    [showStatus]
  );

  // Fetch Images for filters
  const fetchImages = useCallback(
    async (channelId: number, dateParam?: string) => {
      setGlobalLoading(true);
      setIsLoadingImages(true);
      try {
        let url = `${API_BASE_URL}/telegram/images?channel_id=${channelId}`;
        if (dateParam) url += `&date=${dateParam}`;
        const res = await fetch(url, { credentials: "include" });
        const json = await res.json();
        setImages(json.images || []);
      } catch (err) {
        console.error("Error fetching images:", err);
        setImages([]);
        showStatus("Error loading images", "danger");
      } finally {
        setIsLoadingImages(false);
        setGlobalLoading(false);
      }
    },
    [showStatus]
  );

  // Channel/dates on select
  useEffect(() => {
    if (channelId) {
      const ch = channels.find((c) => c.id === channelId);
      if (ch) setChannelTitle(ch.title);
      fetchDates(channelId);
    } else {
      setChannelTitle("");
    }
  }, [channelId, channels, fetchDates]);

  useEffect(() => {
    if (!selectedYear) {
      setMonthList([]);
      setSelectedMonth("");
      setDayList([]);
      setSelectedDate("");
      return;
    }
    const rawMonths = dateStructure[selectedYear]
      ? Object.keys(dateStructure[selectedYear])
      : [];
    const months = rawMonths.sort((a, b) => parseInt(b) - parseInt(a));
    setMonthList(months);
    if (selectedMonth && !months.includes(selectedMonth)) {
      setSelectedMonth("");
      setDayList([]);
      setSelectedDate("");
    }
  }, [selectedYear, dateStructure, selectedMonth]);

  useEffect(() => {
    if (
      !selectedMonth ||
      !dateStructure[selectedYear] ||
      !dateStructure[selectedYear][selectedMonth]
    ) {
      setDayList([]);
      setSelectedDate("");
      return;
    }
    const rawDays = dateStructure[selectedYear][selectedMonth];
    const days = rawDays.sort((a, b) => parseInt(b) - parseInt(a));
    setDayList(days);
    if (selectedDate && !days.includes(selectedDate)) {
      setSelectedDate("");
    }
  }, [selectedMonth, selectedYear, dateStructure, selectedDate]);

  useEffect(() => {
    if (!channelId) return;
    if (selectedYear && selectedMonth && selectedDate) {
      const dateParam = `${selectedYear}-${selectedMonth}-${selectedDate}`;
      fetchImages(channelId, dateParam);
    }
  }, [selectedYear, selectedMonth, selectedDate, channelId, fetchImages]);

  // Download Selected as ZIP
  const downloadSelectedImages = async () => {
    setGlobalLoading(true);
    try {
      const zip = new JSZip();
      selectedImages.forEach((id) => {
        const img = images.find((i) => i.id === id);
        if (!img) return;
        const data = img.image;
        const fileName = img.caption
          ? img.caption.replace(/[^a-z0-9]/gi, "_").substring(0, 30) + ".jpg"
          : `image_${id}.jpg`;
        zip.file(fileName, data, { base64: true });
      });
      const blob = await zip.generateAsync({ type: "blob" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${channelTitle || "images"}_${new Date()
        .toISOString()
        .slice(0, 10)}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error(err);
      showStatus("Error creating ZIP", "danger");
    } finally {
      setGlobalLoading(false);
    }
  };

  // Download Single
  const downloadSingleImage = (img: TelegramImage) => {
    const link = document.createElement("a");
    link.href = `data:image/jpeg;base64,${img.image}`;
    link.download = img.caption
      ? img.caption.replace(/[^\w]+/g, "_") + ".jpg"
      : `image_${img.id}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Selection logic
  const selectAll = () => setSelectedImages(images.map((i) => i.id));
  const deselectAll = () => setSelectedImages([]);

  // Preview
  const openPreview = (img: TelegramImage) => {
    setPreviewImage(`data:image/jpeg;base64,${img.image}`);
    setPreviewCaption(img.caption);
    setPreviewModalOpen(true);
  };
  const closePreview = () => setPreviewModalOpen(false);
  const downloadPreview = () => {
    const link = document.createElement("a");
    link.href = previewImage;
    link.download = previewCaption
      ? previewCaption.replace(/\W+/g, "_") + ".jpg"
      : "image.jpg";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Image Editor
  const openEditor = (img: TelegramImage) => {
    setCurrentEditingImage(img);
    setEditorModalOpen(true);
  };
  const closeEditor = () => {
    setCurrentEditingImage(null);
    setEditorModalOpen(false);
  };

  // Upload to Drive
  const uploadToDrive = async () => {
    if (!channelId) return;
    setIsUploading(true);
    setGlobalLoading(true);
    try {
      const res = await fetch(`${API_BASE_URL}/telegram/upload-to-drive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          channel_name: channelTitle,
          images: selectedImages.map((id) => ({ id, channel_id: channelId })),
        }),
      });
      const json = await res.json();
      if (res.ok) {
        setUploadResults(json);
        setUploadError(null);
      } else {
        setUploadError(json.detail || json.error || "Upload failed");
      }
    } catch (err: unknown) {
      console.error(err);
      setUploadError((err as Error).message || "Upload error");
    } finally {
      setIsUploading(false);
      setGlobalLoading(false);
      setShowUploadModal(true);
    }
  };

  // Render
  return (
    <>
      {globalLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="animate-spin h-8 w-8 border-4 border-t-4 border-white rounded-full"
            role="status"
          >
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      {statusMessage &&
        (() => {
          const bg =
            statusMessage.type === "success"
              ? "bg-green-600"
              : statusMessage.type === "danger"
              ? "bg-red-600"
              : statusMessage.type === "warning"
              ? "bg-yellow-600"
              : "bg-blue-600";
          return (
            <div
              className={`${bg} fixed top-4 left-1/2 transform -translate-x-1/2 text-white px-4 py-2 rounded shadow z-50`}
              style={{ maxWidth: "90%" }}
            >
              {statusMessage.message}
            </div>
          );
        })()}

      <div className="bg-white shadow-lg rounded-xl overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-purple-400 p-6 text-white">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-opacity-20 rounded-full flex items-center justify-center">
              <FaTelegram className="text-white text-xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">
                {channelTitle
                  ? `Images from ${channelTitle}`
                  : "Telegram Images"}
              </h2>
              <p className="text-blue-100 text-sm">
                {images.length} image{images.length !== 1 ? "s" : ""} available
              </p>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 bg-gray-50 border-b">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center">
              <FaFilter className="mr-2 text-gray-600" />
              Date Filters
            </h3>
            {(selectedYear || selectedMonth || selectedDate) && (
              <button
                onClick={() => {
                  setSelectedYear("");
                  setSelectedMonth("");
                  setSelectedDate("");
                }}
                className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                Clear All Filters
              </button>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Year
              </label>
              <select
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200"
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
              >
                <option value="">All Years</option>
                {yearList.map((y) => (
                  <option key={y} value={y}>
                    {y}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Month
              </label>
              <select
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
                disabled={!selectedYear}
              >
                <option value="">All Months</option>
                {monthList.map((m) => {
                  const name = new Date(
                    `${selectedYear}-${m}-01`
                  ).toLocaleString("default", { month: "long" });
                  return (
                    <option key={m} value={m}>
                      {name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date
              </label>
              <select
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                disabled={!selectedMonth}
              >
                <option value="">All Dates</option>
                {dayList.map((d) => (
                  <option key={d} value={d}>
                    {d}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Selection and images grid */}
        <div className="p-6 space-y-6">
          {images.length > 0 && (
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-5 shadow-sm">
              <div className="flex flex-wrap justify-between items-center">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <button
                      className="flex items-center space-x-2 px-4 py-2 border-2 border-blue-500 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                      onClick={selectAll}
                      disabled={selectedImages.length === images.length}
                    >
                      <FaCheckSquare className="text-sm" />
                      <span>Select All</span>
                    </button>
                    <button
                      className="flex items-center space-x-2 px-4 py-2 border-2 border-gray-400 text-gray-600 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                      onClick={deselectAll}
                      disabled={selectedImages.length === 0}
                    >
                      <FaSquare className="text-sm" />
                      <span>Deselect All</span>
                    </button>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600">
                        {selectedImages.length}
                      </span>
                    </div>
                    <span className="text-gray-700 font-medium">
                      {selectedImages.length} of {images.length} selected
                    </span>
                  </div>
                </div>
                <div className="flex space-x-3 mt-3 sm:mt-0">
                  <button
                    className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-5 py-2.5 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:scale-105"
                    onClick={downloadSelectedImages}
                    disabled={selectedImages.length === 0}
                  >
                    <FaDownload className="text-sm" />
                    <span>Download</span>
                  </button>
                  <UploadDropdown
                    isOpen={uploadMenuOpen}
                    onToggle={() => setUploadMenuOpen(!uploadMenuOpen)}
                    onUpload={uploadToDrive}
                    disabled={selectedImages.length === 0}
                    buttonText="Upload"
                    buttonClass="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-5 py-2.5 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:scale-105"
                  />
                </div>
              </div>
            </div>
          )}

          {/* No channel or no filter */}
          {!channelId ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaTelegram className="text-4xl text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                No Channel Selected
              </h3>
              <p className="text-gray-600">
                Please select a channel to view images.
              </p>
            </div>
          ) : !selectedYear || !selectedMonth || !selectedDate ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaFilter className="text-4xl text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                Select Date Filters
              </h3>
              <p className="text-gray-600">
                Please select Year, Month, and Date to view images.
              </p>
              <div className="mt-4 text-sm text-gray-500">
                <p>Current selection:</p>
                <p>Year: {selectedYear || "Not selected"}</p>
                <p>Month: {selectedMonth || "Not selected"}</p>
                <p>Date: {selectedDate || "Not selected"}</p>
              </div>
            </div>
          ) : isLoadingImages ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {Array.from({ length: 8 }).map((_, idx) => (
                <div
                  key={idx}
                  className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse"
                >
                  <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    <div className="flex justify-between pt-2">
                      <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                      <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : images.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {images.map((img) => {
                const isSelected = selectedImages.includes(img.id);
                const toggleSelection = () => {
                  setSelectedImages((sel) =>
                    sel.includes(img.id)
                      ? sel.filter((i) => i !== img.id)
                      : [...sel, img.id]
                  );
                };
                return (
                  <div
                    key={img.id}
                    className={`group relative bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border-2 cursor-pointer ${
                      isSelected
                        ? "border-blue-500 ring-2 ring-blue-200"
                        : "border-gray-100 hover:border-gray-200"
                    }`}
                    onClick={toggleSelection}
                  >
                    <div
                      className="absolute top-3 left-3 z-10"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <input
                        type="checkbox"
                        className="h-5 w-5 text-blue-600 bg-white border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                        checked={isSelected}
                        onChange={toggleSelection}
                      />
                    </div>
                    <div className="relative overflow-hidden">
                      <Image
                        src={`data:image/jpeg;base64,${img.image}`}
                        className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                        alt={img.caption || "Image"}
                        width={300}
                        height={192}
                        unoptimized
                      />
                      <div className="absolute inset-0  bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
                      {isSelected && (
                        <div className="absolute inset-0 bg-blue-500 bg-opacity-10"></div>
                      )}
                    </div>
                    <div className="p-4 space-y-3">
                      <div className="space-y-1">
                        <p
                          className="text-sm font-semibold text-gray-800 line-clamp-2 leading-tight"
                          title={img.caption || "No caption"}
                        >
                          {img.caption || "No caption"}
                        </p>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <p className="text-xs text-gray-500 font-medium">
                            {img.date}
                          </p>
                        </div>
                      </div>
                      <div className="flex justify-between items-center pt-2 border-t border-gray-100">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openPreview(img);
                          }}
                          className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                          title="Preview"
                        >
                          <FaSearch className="text-sm" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openEditor(img);
                          }}
                          className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
                          title="Edit"
                        >
                          <FaEdit className="text-sm" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            downloadSingleImage(img);
                          }}
                          className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200"
                          title="Download"
                        >
                          <FaDownload className="text-sm" />
                        </button>
                      </div>
                    </div>
                    {isSelected && (
                      <div className="absolute top-3 right-3 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg
                          className="w-4 h-4 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaImages className="text-4xl text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                No Images Found
              </h3>
              <p className="text-gray-600 mb-4">
                No images found
                {selectedDate
                  ? ` for ${selectedYear}-${selectedMonth}-${selectedDate}`
                  : selectedMonth
                  ? ` for ${selectedYear}-${selectedMonth}`
                  : selectedYear
                  ? ` for ${selectedYear}`
                  : ""}
                .
              </p>
              {(selectedYear || selectedMonth || selectedDate) && (
                <button
                  onClick={() => {
                    setSelectedYear("");
                    setSelectedMonth("");
                    setSelectedDate("");
                  }}
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                >
                  <FaSync className="text-sm" />
                  <span>Clear Filters</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      {previewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4">
            <div className="flex justify-between items-center border-b px-6 py-4">
              <h5 className="text-lg font-semibold">Image Preview</h5>
              <button
                onClick={closePreview}
                className="text-gray-500 hover:text-gray-700"
              >
                <FaTimes />
              </button>
            </div>
            <div className="p-6 text-center">
              {/* NEXT.JS Image for preview: */}
              <Image
                src={previewImage}
                alt={previewCaption}
                className="mx-auto max-h-96 w-auto"
                width={600}
                height={400}
                unoptimized
              />
              <p className="text-gray-500 mt-2">{previewCaption}</p>
            </div>
            <div className="flex justify-end space-x-2 border-t px-6 py-4">
              <button
                onClick={closePreview}
                className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
              >
                Close
              </button>
              <button
                onClick={downloadPreview}
                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
              >
                Download
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
            <div className="bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 text-white">
              <div className="flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <FaCloudUploadAlt className="text-white" />
                  </div>
                  <h5 className="text-xl font-semibold">Upload Results</h5>
                </div>
                <button
                  onClick={() => setShowUploadModal(false)}
                  className="text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white hover:bg-opacity-20"
                >
                  <FaTimes size={18} />
                </button>
              </div>
            </div>
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              {isUploading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                  <p className="text-lg text-gray-600">
                    Uploading your files...
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Please wait while we process your images
                  </p>
                </div>
              ) : uploadError ? (
                <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FaInfoCircle className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">
                        Upload Failed
                      </h3>
                      <p className="text-sm text-red-700 mt-1">{uploadError}</p>
                    </div>
                  </div>
                </div>
              ) : uploadResults ? (
                <div className="space-y-6">
                  <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                          <svg
                            className="w-4 h-4 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-green-800">
                          Upload Successful!
                        </h3>
                        <p className="text-sm text-green-700 mt-1">
                          {uploadResults.uploaded_files.length} file
                          {uploadResults.uploaded_files.length !== 1
                            ? "s"
                            : ""}{" "}
                          uploaded successfully
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                      <FaList className="mr-2 text-gray-600" />
                      Folder Structure
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between p-2 bg-white rounded border">
                        <span className="text-gray-700">
                          <strong>Main Folder:</strong>{" "}
                          {uploadResults.folder_name}
                        </span>
                        <a
                          href={uploadResults.folder_link}
                          target="_blank"
                          className="text-blue-600 hover:text-blue-800 transition-colors"
                          title="Open folder"
                          rel="noopener noreferrer"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                            />
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
                      <FaCloudUploadAlt className="mr-2 text-gray-600" />
                      Uploaded Files ({uploadResults.uploaded_files.length})
                    </h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {uploadResults.uploaded_files.map((file, index) => (
                        <div
                          key={file.link}
                          className="flex items-center justify-between p-3 bg-white border rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-xs font-semibold text-blue-600">
                                {index + 1}
                              </span>
                            </div>
                            <div>
                              <p
                                className="text-sm font-medium text-gray-900 truncate max-w-xs"
                                title={file.name}
                              >
                                {file.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                Image file
                              </p>
                            </div>
                          </div>
                          <a
                            href={file.link}
                            target="_blank"
                            className="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded hover:bg-blue-50"
                            title="Open file"
                            rel="noopener noreferrer"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                              />
                            </svg>
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
            <div className="bg-gray-50 px-6 py-4 border-t">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowUploadModal(false)}
                  className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Close
                </button>
                {uploadResults && (
                  <a
                    href={uploadResults.folder_link}
                    target="_blank"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2"
                    rel="noopener noreferrer"
                  >
                    <span>View in Drive</span>
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                      />
                    </svg>
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Editor */}
      <ImageEditor
        isOpen={editorModalOpen}
        image={currentEditingImage}
        channelTitle={channelTitle}
        onClose={closeEditor}
        onUploadSuccess={(results) => {
          setUploadResults(
            results as {
              folder_name: string;
              date_folder_name?: string;
              date_folder_link?: string;
              folder_link: string;
              uploaded_files: { name: string; link: string }[];
            }
          );
          setUploadError(null);
          setShowUploadModal(true);
        }}
        onUploadError={(error) => {
          setUploadError(error as string);
          setShowUploadModal(true);
        }}
      />
    </>
  );
}

// 'use client';

// import { useState, useEffect } from 'react';
// import { FaTelegram, FaFilter, FaDownload, FaSearch, FaSync, FaCheckSquare, FaSquare, FaCloudUploadAlt, FaEdit, FaTimes, FaImages, FaInfoCircle, FaList } from 'react-icons/fa';
// import JSZip from 'jszip';
// import { authFetch } from '@/lib/authFetch';
// import { BASE_URL, ADMIN_BASE_URL, TelegramChannel,
//   TelegramImage, TelegramImagesProps, showStatus as utilShowStatus,
//   fetchTelegramChannels, checkTelegramAuth
// } from './types';
// import ImageEditor from './ImageEditor';

// export default function TelegramImages({ channelId }: TelegramImagesProps) {
//   const [isLoadingChannels, setIsLoadingChannels] = useState(false);
//   const [channels, setChannels] = useState<TelegramChannel[]>([]);
//   const [globalLoading, setGlobalLoading] = useState(false);
//   const [statusMessage, setStatusMessage] = useState<{message:string; type:'success'|'danger'|'warning'|'info'}|null>(null);

//   const showStatus = (msg: string, type: 'success'|'danger'|'warning'|'info' = 'success', duration = 3000) => {
//     utilShowStatus(setStatusMessage, msg, type, duration);
//   };

//   const [dateStructure, setDateStructure] = useState({} as Record<string, Record<string, string[]>>);
//   const [yearList, setYearList] = useState<string[]>([]);
//   const [monthList, setMonthList] = useState<string[]>([]);
//   const [dayList, setDayList] = useState<string[]>([]);
//   const [selectedYear, setSelectedYear] = useState<string>('');
//   const [selectedMonth, setSelectedMonth] = useState<string>('');
//   const [selectedDate, setSelectedDate] = useState<string>('');
//   const [images, setImages] = useState<TelegramImage[]>([]);
//   const [isLoadingImages, setIsLoadingImages] = useState(false);
//   const [selectedImages, setSelectedImages] = useState<number[]>([]);
//   const [previewModalOpen, setPreviewModalOpen] = useState(false);
//   const [previewImage, setPreviewImage] = useState<string>('');
//   const [previewCaption, setPreviewCaption] = useState<string>('');
//   const [isUploading, setIsUploading] = useState(false);
//   const [showUploadModal, setShowUploadModal] = useState(false);
//   const [uploadResults, setUploadResults] = useState<{folder_name:string; date_folder_name?:string; date_folder_link?:string; folder_link:string; uploaded_files:{name:string; link:string}[]} | null>(null);
//   const [uploadError, setUploadError] = useState<string|null>(null);
//   // New state for channel title and image editor
//   const [channelTitle, setChannelTitle] = useState<string>('');
//   const [editorModalOpen, setEditorModalOpen] = useState(false);
//   const [currentEditingImage, setCurrentEditingImage] = useState<TelegramImage | null>(null);
//   const [uploadMenuOpen, setUploadMenuOpen] = useState(false);
//   const [isDriveConnected, setIsDriveConnected] = useState(false);

//   // Reusable upload dropdown component
//   const UploadDropdown = ({
//     isOpen,
//     onToggle,
//     onUpload,
//     disabled = false,
//     buttonText = "Upload",
//     buttonClass = "px-4 py-2 rounded focus:outline-none"
//   }: {
//     isOpen: boolean;
//     onToggle: () => void;
//     onUpload: () => void;
//     disabled?: boolean;
//     buttonText?: string;
//     buttonClass?: string;
//   }) => (
//     <div className="relative upload-dropdown">
//       <button
//         onClick={() => isDriveConnected && onToggle()}
//         disabled={!isDriveConnected || disabled}
//         className={`${buttonClass} ${isDriveConnected && !disabled ? 'bg-green-500 text-white hover:bg-green-600' : 'bg-gray-200 text-gray-400 cursor-not-allowed'}`}
//       >
//         <FaCloudUploadAlt className="inline-block mr-1" /> {buttonText}
//       </button>
//       {isOpen && (
//         <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-xl z-50">
//           <button
//             onClick={() => { onUpload(); onToggle(); }}
//             disabled={!isDriveConnected}
//             className={`w-full text-left px-4 py-2 rounded-t-lg ${isDriveConnected ? 'hover:bg-gray-100' : 'text-gray-400 cursor-not-allowed'}`}
//           >
//             Google Drive
//           </button>
//           <button disabled className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed">
//             AWS (Coming Soon)
//           </button>
//           <button disabled className="w-full text-left px-4 py-2 text-gray-400 cursor-not-allowed rounded-b-lg">
//             Azure (Coming Soon)
//           </button>
//         </div>
//       )}
//     </div>
//   );

//   useEffect(() => {
//     const handleClickOutside = (event: MouseEvent) => {
//       const target = event.target as Element;
//       if (!target.closest('.upload-dropdown')) {
//         setUploadMenuOpen(false);
//       }
//     };

//     document.addEventListener('mousedown', handleClickOutside);
//     return () => document.removeEventListener('mousedown', handleClickOutside);
//   }, []);

//   useEffect(() => {
//     (async () => {
//       try {
//         const authData = await checkTelegramAuth();
//         if (!authData.authenticated) {
//           window.location.href = '/admin/fetch-data/telegram-connect';
//           return;
//         }
//       } catch {
//         window.location.href = '/admin/fetch-data/telegram-connect';
//         return;
//       }
//       fetchChannels();
//     })();
//   }, []);

//   useEffect(() => {
//     (async () => {
//       try {
//         const res = await authFetch(
//           `${ADMIN_BASE_URL}/api/admin/check-google-drive-connection`
//         );
//         const json = await res.json();
//         setIsDriveConnected(!!json.data?.connected);
//       } catch {
//         setIsDriveConnected(false);
//       }
//     })();
//   }, []);

//   const fetchChannels = async () => {
//     setIsLoadingChannels(true);
//     try {
//       const channelData = await fetchTelegramChannels();
//       setChannels(channelData);
//     } catch (err) {
//       console.error('Failed to load channels:', err);
//       showStatus('Error loading channels','danger');
//     } finally {
//       setIsLoadingChannels(false);
//     }
//   };

//   const organizeDates = (dates: string[]) => dates.reduce((acc, dateStr) => {
//     const [year, month, day] = dateStr.split('-');
//     acc[year] = acc[year] || {};
//     acc[year][month] = acc[year][month] || [];
//     if (!acc[year][month].includes(day)) acc[year][month].push(day);
//     return acc;
//   }, {} as Record<string, Record<string, string[]>>);

//   const fetchDates = async (channelId: number) => {
//     setGlobalLoading(true);
//     try {
//       const res = await fetch(`${BASE_URL}/telegram/dates?channel_id=${channelId}`, {
//         credentials: 'include'
//       });
//       const json = await res.json();
//       const struct = organizeDates(json.dates || []);
//       setDateStructure(struct);
//       const years = Object.keys(struct).sort((a,b) => b.localeCompare(a));
//       setYearList(years);
//       setSelectedYear(''); setSelectedMonth(''); setSelectedDate('');
//       setMonthList([]); setDayList([]);
//     } catch (err) {
//       console.error('Failed to load dates:', err);
//       showStatus('Error loading dates', 'danger');
//     } finally { setGlobalLoading(false); }
//   };

//   const fetchImages = async (channelId: number, dateParam?: string) => {
//     setGlobalLoading(true);
//     setIsLoadingImages(true);
//     try {
//       let url = `${BASE_URL}/telegram/images?channel_id=${channelId}`;
//       if (dateParam) url += `&date=${dateParam}`;
//       console.log('Fetching images from:', url);
//       const res = await fetch(url, {
//         credentials: 'include'
//       });
//       const json = await res.json();
//       console.log('Images response:', json);
//       setImages(json.images || []);
//     } catch (err) {
//       console.error('Error fetching images:', err);
//       setImages([]);
//       showStatus('Error loading images', 'danger');
//     } finally {
//       setIsLoadingImages(false);
//       setGlobalLoading(false);
//     }
//   };

//   useEffect(() => {
//     if (channelId) {
//       const ch = channels.find(c => c.id === channelId);
//       if (ch) setChannelTitle(ch.title);
//       fetchDates(channelId);
//     } else {
//       setChannelTitle('');
//     }
//   }, [channelId, channels]);

//   useEffect(() => {
//     if (!selectedYear) {
//       setMonthList([]);
//       setSelectedMonth('');
//       setDayList([]);
//       setSelectedDate('');
//       return;
//     }
//     const rawMonths = dateStructure[selectedYear] ? Object.keys(dateStructure[selectedYear]) : [];
//     const months = rawMonths.sort((a, b) => parseInt(b) - parseInt(a));
//     setMonthList(months);

//     if (selectedMonth && !months.includes(selectedMonth)) {
//       setSelectedMonth('');
//       setDayList([]);
//       setSelectedDate('');
//     }
//   }, [selectedYear, dateStructure]);

//   useEffect(() => {
//     if (!selectedMonth || !dateStructure[selectedYear] || !dateStructure[selectedYear][selectedMonth]) {
//       setDayList([]);
//       setSelectedDate('');
//       return;
//     }
//     const rawDays = dateStructure[selectedYear][selectedMonth];
//     const days = rawDays.sort((a, b) => parseInt(b) - parseInt(a));
//     setDayList(days);

//     if (selectedDate && !days.includes(selectedDate)) {
//       setSelectedDate('');
//     }
//   }, [selectedMonth, selectedYear, dateStructure]);

//   useEffect(() => {
//     if (!channelId) return;
//     if (selectedYear && selectedMonth && selectedDate) {
//       const dateParam = `${selectedYear}-${selectedMonth}-${selectedDate}`;
//       fetchImages(channelId, dateParam);
//     }
//   }, [selectedYear, selectedMonth, selectedDate, channelId]);

//   const downloadSelectedImages = async () => {
//     setGlobalLoading(true);
//     try {
//       const zip = new JSZip();
//       selectedImages.forEach(id => {
//         const img = images.find(i => i.id === id);
//         if (!img) return;
//         const data = img.image;
//         const fileName = img.caption
//           ? img.caption.replace(/[^a-z0-9]/gi, '_').substring(0, 30) + '.jpg'
//           : `image_${id}.jpg`;
//         zip.file(fileName, data, { base64: true });
//       });
//       const blob = await zip.generateAsync({ type: 'blob' });
//       const url = URL.createObjectURL(blob);
//       const link = document.createElement('a');
//       link.href = url;
//       link.download = `${channelTitle || 'images'}_${new Date().toISOString().slice(0,10)}.zip`;
//       document.body.appendChild(link);
//       link.click();
//       document.body.removeChild(link);
//       URL.revokeObjectURL(url);
//     } catch (err) {
//       console.error(err);
//       showStatus('Error creating ZIP','danger');
//     } finally {
//       setGlobalLoading(false);
//     }
//   };

//   const downloadSingleImage = (img: TelegramImage) => {
//     const link = document.createElement('a');
//     link.href = `data:image/jpeg;base64,${img.image}`;
//     link.download = img.caption
//       ? img.caption.replace(/[^\w]+/g,'_') + '.jpg'
//       : `image_${img.id}.jpg`;
//     document.body.appendChild(link);
//     link.click();
//     document.body.removeChild(link);
//   };

//   const selectAll = () => setSelectedImages(images.map(i => i.id));
//   const deselectAll = () => setSelectedImages([]);

//   const openPreview = (img: TelegramImage) => {
//     setPreviewImage(`data:image/jpeg;base64,${img.image}`);
//     setPreviewCaption(img.caption);
//     setPreviewModalOpen(true);
//   };
//   const closePreview = () => setPreviewModalOpen(false);
//   const downloadPreview = () => {
//     const link = document.createElement('a');
//     link.href = previewImage;
//     link.download = previewCaption ? previewCaption.replace(/\W+/g,'_') + '.jpg' : 'image.jpg';
//     document.body.appendChild(link);
//     link.click();
//     document.body.removeChild(link);
//   };

//   const openEditor = (img: TelegramImage) => {
//     setCurrentEditingImage(img);
//     setEditorModalOpen(true);
//   };
//   const closeEditor = () => {
//     setCurrentEditingImage(null);
//     setEditorModalOpen(false);
//   };

//   const uploadToDrive = async () => {
//     if (!channelId) return;
//     setIsUploading(true);
//     setGlobalLoading(true);
//     try {
//       const res = await fetch(`${BASE_URL}/telegram/upload-to-drive`, {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         credentials: 'include',
//         body: JSON.stringify({
//           channel_name: channelTitle,
//           images: selectedImages.map(id => ({ id, channel_id: channelId }))
//         })
//       });
//       const json = await res.json();
//       if (res.ok) {
//         setUploadResults(json);
//         setUploadError(null);
//       } else {
//         // Handle error response
//         setUploadError(json.detail || json.error || 'Upload failed');
//       }
//     } catch (err: any) {
//       console.error(err);
//       setUploadError(err.message || 'Upload error');
//     } finally {
//       setIsUploading(false);
//       setGlobalLoading(false);
//       setShowUploadModal(true);
//     }
//   };

//   return (
//     <>
//       {/* Global Loading Spinner */}
//       {globalLoading && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="animate-spin h-8 w-8 border-4 border-t-4 border-white rounded-full" role="status">
//             <span className="sr-only">Loading...</span>
//           </div>
//         </div>
//       )}
//       {/* Status Alert */}
//       {statusMessage && (() => {
//         const bg = statusMessage.type === 'success'
//           ? 'bg-green-600' : statusMessage.type === 'danger'
//           ? 'bg-red-600' : statusMessage.type === 'warning'
//           ? 'bg-yellow-600' : 'bg-blue-600';
//         return (
//           <div className={`${bg} fixed top-4 left-1/2 transform -translate-x-1/2 text-white px-4 py-2 rounded shadow z-50`} style={{ maxWidth: '90%' }}>
//             {statusMessage.message}
//           </div>
//         );
//       })()}
//       <div className="bg-white shadow-lg rounded-xl overflow-hidden">
//         {/* Header Section */}
//         <div className="bg-gradient-to-r from-blue-600 to-purple-400 p-6 text-white">
//           <div className="flex items-center justify-between">
//             <div className="flex items-center space-x-3">
//               <div className="w-10 h-10  bg-opacity-20 rounded-full flex items-center justify-center">
//                 <FaTelegram className="text-white text-xl" />
//               </div>
//               <div>
//                 <h2 className="text-2xl font-bold text-white">
//                   {channelTitle ? `Images from ${channelTitle}` : 'Telegram Images'}
//                 </h2>
//                 <p className="text-blue-100 text-sm">
//                   {images.length} image{images.length !== 1 ? 's' : ''} available
//                 </p>
//               </div>
//             </div>
//           </div>
//         </div>

//         {/* Filters Section */}
//         <div className="p-6 bg-gray-50 border-b">
//           <div className="flex items-center justify-between mb-4">
//             <h3 className="text-lg font-semibold text-gray-800 flex items-center">
//               <FaFilter className="mr-2 text-gray-600" />
//               Date Filters
//             </h3>
//             {(selectedYear || selectedMonth || selectedDate) && (
//               <button
//                 onClick={() => {
//                   setSelectedYear('');
//                   setSelectedMonth('');
//                   setSelectedDate('');
//                 }}
//                 className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
//               >
//                 Clear All Filters
//               </button>
//             )}
//           </div>
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">Year</label>
//               <select
//                 className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200"
//                 value={selectedYear}
//                 onChange={e => setSelectedYear(e.target.value)}
//               >
//                 <option value="">All Years</option>
//                 {yearList.map(y => <option key={y} value={y}>{y}</option>)}
//               </select>
//             </div>
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">Month</label>
//               <select
//                 className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
//                 value={selectedMonth}
//                 onChange={e => setSelectedMonth(e.target.value)}
//                 disabled={!selectedYear}
//               >
//                 <option value="">All Months</option>
//                 {monthList.map(m => {
//                   const name = new Date(`${selectedYear}-${m}-01`).toLocaleString('default',{month:'long'});
//                   return <option key={m} value={m}>{name}</option>;
//                 })}
//               </select>
//             </div>
//             <div>
//               <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
//               <select
//                 className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white transition-all duration-200 disabled:bg-gray-100 disabled:cursor-not-allowed"
//                 value={selectedDate}
//                 onChange={e => setSelectedDate(e.target.value)}
//                 disabled={!selectedMonth}
//               >
//                 <option value="">All Dates</option>
//                 {dayList.map(d => <option key={d} value={d}>{d}</option>)}
//               </select>
//             </div>
//           </div>
//         </div>

//         {/* Content Section */}
//         <div className="p-6 space-y-6">

//         {/* Selection Controls */}
//         {images.length > 0 && (
//           <div className="bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-5 shadow-sm">
//             <div className="flex flex-wrap justify-between items-center">
//               <div className="flex items-center space-x-4">
//                 <div className="flex items-center space-x-2">
//                   <button
//                     className="flex items-center space-x-2 px-4 py-2 border-2 border-blue-500 text-blue-600 rounded-lg hover:bg-blue-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
//                     onClick={selectAll}
//                     disabled={selectedImages.length===images.length}
//                   >
//                     <FaCheckSquare className="text-sm" />
//                     <span>Select All</span>
//                   </button>
//                   <button
//                     className="flex items-center space-x-2 px-4 py-2 border-2 border-gray-400 text-gray-600 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
//                     onClick={deselectAll}
//                     disabled={selectedImages.length===0}
//                   >
//                     <FaSquare className="text-sm" />
//                     <span>Deselect All</span>
//                   </button>
//                 </div>
//                 <div className="flex items-center space-x-2">
//                   <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
//                     <span className="text-sm font-bold text-blue-600">{selectedImages.length}</span>
//                   </div>
//                   <span className="text-gray-700 font-medium">
//                     {selectedImages.length} of {images.length} selected
//                   </span>
//                 </div>
//               </div>
//               <div className="flex space-x-3 mt-3 sm:mt-0">
//                 <button
//                   className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-5 py-2.5 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:scale-105"
//                   onClick={downloadSelectedImages}
//                   disabled={selectedImages.length===0}
//                 >
//                   <FaDownload className="text-sm" />
//                   <span>Download</span>
//                 </button>
//                 <UploadDropdown
//                   isOpen={uploadMenuOpen}
//                   onToggle={() => setUploadMenuOpen(!uploadMenuOpen)}
//                   onUpload={uploadToDrive}
//                   disabled={selectedImages.length === 0}
//                   buttonText="Upload"
//                   buttonClass="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-5 py-2.5 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-md hover:shadow-lg transform hover:scale-105"
//                 />
//               </div>
//             </div>
//           </div>
//         )}

//         {/* Images Display Logic */}
//         {!channelId ? (
//           <div className="text-center py-16">
//             <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
//               <FaTelegram className="text-4xl text-gray-400" />
//             </div>
//             <h3 className="text-lg font-semibold text-gray-800 mb-2">No Channel Selected</h3>
//             <p className="text-gray-600">Please select a channel to view images.</p>
//           </div>
//         ) : !selectedYear || !selectedMonth || !selectedDate ? (
//           <div className="text-center py-16">
//             <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
//               <FaFilter className="text-4xl text-gray-400" />
//             </div>
//             <h3 className="text-lg font-semibold text-gray-800 mb-2">Select Date Filters</h3>
//             <p className="text-gray-600">Please select Year, Month, and Date to view images.</p>
//             <div className="mt-4 text-sm text-gray-500">
//               <p>Current selection:</p>
//               <p>Year: {selectedYear || 'Not selected'}</p>
//               <p>Month: {selectedMonth || 'Not selected'}</p>
//               <p>Date: {selectedDate || 'Not selected'}</p>
//             </div>
//           </div>
//         ) : isLoadingImages ? (
//           <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
//             {Array.from({ length: 8 }).map((_, idx) => (
//               <div key={idx} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
//                 <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300"></div>
//                 <div className="p-4 space-y-3">
//                   <div className="h-4 bg-gray-200 rounded w-3/4"></div>
//                   <div className="h-3 bg-gray-200 rounded w-1/2"></div>
//                   <div className="flex justify-between pt-2">
//                     <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
//                     <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
//                     <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
//                   </div>
//                 </div>
//               </div>
//             ))}
//           </div>
//         ) : images.length > 0 ? (
//           <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
//             {images.map(img => {
//               const isSelected = selectedImages.includes(img.id);
//               const toggleSelection = () => {
//                 setSelectedImages(sel =>
//                   sel.includes(img.id) ? sel.filter(i => i !== img.id) : [...sel, img.id]
//                 );
//               };

//               return (
//                 <div
//                   key={img.id}
//                   className={`group relative bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border-2 cursor-pointer ${
//                     isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-100 hover:border-gray-200'
//                   }`}
//                   onClick={toggleSelection}
//                 >
//                   {/* Selection Checkbox */}
//                   <div className="absolute top-3 left-3 z-10" onClick={(e) => e.stopPropagation()}>
//                     <input
//                       type="checkbox"
//                       className="h-5 w-5 text-blue-600 bg-white border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 transition-all duration-200"
//                       checked={isSelected}
//                       onChange={toggleSelection}
//                     />
//                   </div>

//                                     {/* Image Container */}
//                   <div className="relative overflow-hidden">
//                     <img
//                       src={`data:image/jpeg;base64,${img.image}`}
//                       className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
//                       alt={img.caption || 'Image'}
//                     />
//                     {/* Overlay on hover */}
//                     <div className="absolute inset-0  bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>
//                     {/* Selection overlay */}
//                     {isSelected && (
//                       <div className="absolute inset-0 bg-blue-500 bg-opacity-10"></div>
//                     )}
//                    </div>

//                   {/* Content */}
//                   <div className="p-4 space-y-3">
//                     <div className="space-y-1">
//                       <p className="text-sm font-semibold text-gray-800 line-clamp-2 leading-tight" title={img.caption || 'No caption'}>
//                         {img.caption || 'No caption'}
//                       </p>
//                       <div className="flex items-center space-x-2">
//                         <div className="w-2 h-2 bg-green-400 rounded-full"></div>
//                         <p className="text-xs text-gray-500 font-medium">{img.date}</p>
//                       </div>
//                     </div>

//                     {/* Action Buttons */}
//                     <div className="flex justify-between items-center pt-2 border-t border-gray-100">
//                       <button
//                         onClick={(e) => { e.stopPropagation(); openPreview(img); }}
//                         className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
//                         title="Preview"
//                       >
//                         <FaSearch className="text-sm" />
//                       </button>
//                       <button
//                         onClick={(e) => { e.stopPropagation(); openEditor(img); }}
//                         className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
//                         title="Edit"
//                       >
//                         <FaEdit className="text-sm" />
//                       </button>
//                       <button
//                         onClick={(e) => { e.stopPropagation(); downloadSingleImage(img); }}
//                         className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200"
//                         title="Download"
//                       >
//                         <FaDownload className="text-sm" />
//                       </button>
//                     </div>
//                   </div>

//                   {/* Selected Indicator */}
//                   {isSelected && (
//                     <div className="absolute top-3 right-3 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
//                       <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
//                         <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
//                       </svg>
//                     </div>
//                   )}
//                 </div>
//                );
//              })}
//           </div>
//         ) : (
//           <div className="text-center py-16">
//             <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
//               <FaImages className="text-4xl text-gray-400" />
//             </div>
//             <h3 className="text-lg font-semibold text-gray-800 mb-2">No Images Found</h3>
//             <p className="text-gray-600 mb-4">
//               No images found{selectedDate ? ` for ${selectedYear}-${selectedMonth}-${selectedDate}` :
//                 selectedMonth ? ` for ${selectedYear}-${selectedMonth}` :
//                 selectedYear ? ` for ${selectedYear}` : ''}.
//             </p>
//             {(selectedYear || selectedMonth || selectedDate) && (
//               <button
//                 onClick={() => {
//                   setSelectedYear('');
//                   setSelectedMonth('');
//                   setSelectedDate('');
//                 }}
//                 className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
//               >
//                 <FaSync className="text-sm" />
//                 <span>Clear Filters</span>
//               </button>
//             )}
//           </div>
//         )}
//         </div>
//       </div>

//       {/* Preview Modal */}
//       {previewModalOpen && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//           <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full mx-4">
//             <div className="flex justify-between items-center border-b px-6 py-4">
//               <h5 className="text-lg font-semibold">Image Preview</h5>
//               <button onClick={closePreview} className="text-gray-500 hover:text-gray-700"><FaTimes /></button>
//             </div>
//             <div className="p-6 text-center">
//               <img src={previewImage} alt={previewCaption} className="mx-auto max-h-96 w-auto" />
//               <p className="text-gray-500 mt-2">{previewCaption}</p>
//             </div>
//             <div className="flex justify-end space-x-2 border-t px-6 py-4">
//               <button onClick={closePreview} className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Close</button>
//               <button onClick={downloadPreview} className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Download</button>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Upload Results Modal */}
//       {showUploadModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
//           <div className="bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
//             {/* Header */}
//             <div className="bg-gradient-to-r from-green-500 to-blue-500 px-6 py-4 text-white">
//               <div className="flex justify-between items-center">
//                 <div className="flex items-center space-x-3">
//                   <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
//                     <FaCloudUploadAlt className="text-white" />
//                   </div>
//                   <h5 className="text-xl font-semibold">Upload Results</h5>
//                 </div>
//                 <button
//                   onClick={() => setShowUploadModal(false)}
//                   className="text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white hover:bg-opacity-20"
//                 >
//                   <FaTimes size={18} />
//                 </button>
//               </div>
//             </div>

//             {/* Content */}
//             <div className="p-6 max-h-[60vh] overflow-y-auto">
//               {isUploading ? (
//                 <div className="text-center py-8">
//                   <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
//                   <p className="text-lg text-gray-600">Uploading your files...</p>
//                   <p className="text-sm text-gray-500 mt-2">Please wait while we process your images</p>
//                 </div>
//               ) : uploadError ? (
//                 <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
//                   <div className="flex items-center">
//                     <div className="flex-shrink-0">
//                       <FaInfoCircle className="h-5 w-5 text-red-400" />
//                     </div>
//                     <div className="ml-3">
//                       <h3 className="text-sm font-medium text-red-800">Upload Failed</h3>
//                       <p className="text-sm text-red-700 mt-1">{uploadError}</p>
//                     </div>
//                   </div>
//                 </div>
//               ) : uploadResults ? (
//                 <div className="space-y-6">
//                   {/* Success Header */}
//                   <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-lg">
//                     <div className="flex items-center">
//                       <div className="flex-shrink-0">
//                         <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
//                           <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
//                             <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
//                           </svg>
//                         </div>
//                       </div>
//                       <div className="ml-3">
//                         <h3 className="text-sm font-medium text-green-800">Upload Successful!</h3>
//                         <p className="text-sm text-green-700 mt-1">
//                           {uploadResults.uploaded_files.length} file{uploadResults.uploaded_files.length !== 1 ? 's' : ''} uploaded successfully
//                         </p>
//                       </div>
//                     </div>
//                   </div>

//                   {/* Folder Information */}
//                   <div className="bg-gray-50 rounded-lg p-4">
//                     <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
//                       <FaList className="mr-2 text-gray-600" />
//                       Folder Structure
//                     </h4>
//                     <div className="space-y-2 text-sm">
//                       <div className="flex items-center justify-between p-2 bg-white rounded border">
//                         <span className="text-gray-700">
//                           <strong>Main Folder:</strong> {uploadResults.folder_name}
//                         </span>
//                         <a
//                           href={uploadResults.folder_link}
//                           target="_blank"
//                           className="text-blue-600 hover:text-blue-800 transition-colors"
//                           title="Open folder"
//                         >
//                           <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                             <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
//                           </svg>
//                         </a>
//                       </div>
//                     </div>
//                   </div>

//                   {/* Uploaded Files */}
//                   <div>
//                     <h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
//                       <FaCloudUploadAlt className="mr-2 text-gray-600" />
//                       Uploaded Files ({uploadResults.uploaded_files.length})
//                     </h4>
//                     <div className="space-y-2 max-h-60 overflow-y-auto">
//                       {uploadResults.uploaded_files.map((file, index) => (
//                         <div key={file.link} className="flex items-center justify-between p-3 bg-white border rounded-lg hover:bg-gray-50 transition-colors">
//                           <div className="flex items-center space-x-3">
//                             <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
//                               <span className="text-xs font-semibold text-blue-600">{index + 1}</span>
//                             </div>
//                             <div>
//                               <p className="text-sm font-medium text-gray-900 truncate max-w-xs" title={file.name}>
//                                 {file.name}
//                               </p>
//                               <p className="text-xs text-gray-500">Image file</p>
//                             </div>
//                           </div>
//                           <a
//                             href={file.link}
//                             target="_blank"
//                             className="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded hover:bg-blue-50"
//                             title="Open file"
//                           >
//                             <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
//                             </svg>
//                           </a>
//                         </div>
//                       ))}
//                     </div>
//                   </div>
//                 </div>
//               ) : null}
//             </div>

//             {/* Footer */}
//             <div className="bg-gray-50 px-6 py-4 border-t">
//               <div className="flex justify-end space-x-3">
//                 <button
//                   onClick={() => setShowUploadModal(false)}
//                   className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
//                 >
//                   Close
//                 </button>
//                 {uploadResults && (
//                   <a
//                     href={uploadResults.folder_link}
//                     target="_blank"
//                     className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2"
//                   >
//                     <span>View in Drive</span>
//                     <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
//                     </svg>
//                   </a>
//                 )}
//               </div>
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Image Editor */}
//       <ImageEditor
//         isOpen={editorModalOpen}
//         image={currentEditingImage}
//         channelTitle={channelTitle}
//         onClose={closeEditor}
//         onUploadSuccess={(results) => {
//           setUploadResults(results);
//           setUploadError(null);
//           setShowUploadModal(true);
//         }}
//         onUploadError={(error) => {
//           setUploadError(error);
//           setShowUploadModal(true);
//         }}
//       />
//     </>
//   );
// }
