# Django-style Alembic config for master_db
[alembic]
script_location = C:\Users\<USER>\Desktop\DADP\DADP-Prod-FN\fastapi-backend\app\migrations\master_db
sqlalchemy.url = postgresql+psycopg2://kanwar_raj:dadpdev123@***********:5432/master_db
version_locations = C:\Users\<USER>\Desktop\DADP\DADP-Prod-FN\fastapi-backend\app\migrations\master_db\versions
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(slug)s

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARNING
handlers = console
qualname =

[logger_sqlalchemy]
level = WARNING
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
