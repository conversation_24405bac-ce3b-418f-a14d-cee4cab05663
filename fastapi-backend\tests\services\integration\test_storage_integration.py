"""
Integration tests for storage operations across all services.
Tests real storage connections, migrations, and cross-service storage workflows.
"""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List
import asyncio
import tempfile
import os
import shutil
from datetime import datetime

from app.services.media_streaming_service import MediaStreamingService
from app.services.project_batch_service import ProjectBatchService
from app.services.ai_processing_service import AIProcessingService

class TestStorageIntegration:
    """Integration tests for storage operations with real dependencies."""
    
    @pytest.fixture
    async def integration_storage_config(self):
        """Integration test storage configuration."""
        return {
            'minio_test_config': {
                'endpoint': 'localhost:9000',
                'access_key': 'test_access_key',
                'secret_key': 'test_secret_key',
                'bucket': 'integration-test-bucket',
                'use_ssl': False
            },
            'nas_test_config': {
                'ftp_host': 'localhost',
                'ftp_port': 21,
                'ftp_username': 'test_ftp_user',
                'ftp_password': 'test_ftp_pass',
                'base_path': '/integration/test'
            }
        }
    
    @pytest.fixture
    async def test_files_setup(self):
        """Create temporary test files for integration testing."""
        temp_dir = tempfile.mkdtemp(prefix='storage_integration_')
        
        test_files = {
            'small_image': {
                'path': os.path.join(temp_dir, 'test_image.jpg'),
                'size': 1024 * 1024,  # 1MB
                'type': 'image'
            },
            'medium_video': {
                'path': os.path.join(temp_dir, 'test_video.mp4'),
                'size': 10 * 1024 * 1024,  # 10MB
                'type': 'video'
            },
            'large_dataset': {
                'path': os.path.join(temp_dir, 'dataset.zip'),
                'size': 50 * 1024 * 1024,  # 50MB
                'type': 'archive'
            }
        }
        
        # Create actual test files
        for file_info in test_files.values():
            with open(file_info['path'], 'wb') as f:
                # Write random-like data
                f.write(b'test_data' * (file_info['size'] // 9))
        
        yield test_files
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.asyncio
    async def test_end_to_end_storage_workflow(
        self, 
        test_db: AsyncSession,
        test_master_db: AsyncSession,
        integration_storage_config,
        test_files_setup
    ):
        """Test complete storage workflow from upload to AI processing."""
        # This test simulates a complete workflow:
        # 1. Upload files to both MinIO and NAS
        # 2. Create project batches
        # 3. Process files with AI service
        # 4. Stream results via media service
        
        media_service = MediaStreamingService()
        batch_service = ProjectBatchService()
        ai_service = AIProcessingService()
        
        project_code = 'INTEGRATION_E2E_001'
        
        # Step 1: Simulate file upload and batch creation
        batch_creation_result = await self._simulate_batch_creation(
            batch_service, project_code, test_files_setup, integration_storage_config
        )
        
        assert batch_creation_result['success'] is True
        assert batch_creation_result['batches_created'] > 0
        
        # Step 2: Process files with AI service
        ai_processing_result = await self._simulate_ai_processing(
            ai_service, project_code, batch_creation_result['file_list']
        )
        
        assert ai_processing_result['success'] is True
        assert ai_processing_result['files_processed'] > 0
        
        # Step 3: Stream processed results
        streaming_result = await self._simulate_media_streaming(
            media_service, project_code, ai_processing_result['result_files']
        )
        
        assert streaming_result['success'] is True
        assert streaming_result['streaming_urls_generated'] > 0

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.asyncio
    async def test_storage_failover_integration(
        self,
        integration_storage_config,
        test_files_setup
    ):
        """Test storage failover between MinIO and NAS in integration environment."""
        service = MediaStreamingService()
        
        # Test scenario: MinIO primary, NAS fallback
        project_config = {
            'project_code': 'FAILOVER_INT_001',
            'primary_storage': 'MinIO',
            'fallback_storage': 'NAS-FTP',
            'failover_enabled': True
        }
        
        test_file = test_files_setup['medium_video']
        
        # Simulate MinIO failure and verify NAS fallback
        with self._simulate_storage_failure('MinIO'):
            result = await self._attempt_file_operation(
                service, project_config, test_file['path']
            )
            
            # Should succeed using NAS fallback
            assert result['success'] is True
            assert result['storage_used'] == 'NAS-FTP'
            assert result['failover_occurred'] is True

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.asyncio
    async def test_cross_storage_file_migration_integration(
        self,
        integration_storage_config,
        test_files_setup
    ):
        """Test actual file migration between storage systems."""
        batch_service = ProjectBatchService()
        
        migration_plan = {
            'source_storage': 'NAS-FTP',
            'target_storage': 'MinIO',
            'files_to_migrate': [
                test_files_setup['small_image']['path'],
                test_files_setup['medium_video']['path']
            ],
            'verify_integrity': True,
            'cleanup_after_migration': False  # Keep originals for testing
        }
        
        # Execute migration
        migration_result = await self._execute_storage_migration(
            batch_service, migration_plan, integration_storage_config
        )
        
        assert migration_result['success'] is True
        assert migration_result['files_migrated'] == len(migration_plan['files_to_migrate'])
        assert migration_result['integrity_verified'] is True
        assert migration_result['migration_time'] > 0
        
        # Verify files exist in target storage
        for file_path in migration_plan['files_to_migrate']:
            target_exists = await self._verify_file_in_storage(
                file_path, migration_plan['target_storage'], integration_storage_config
            )
            assert target_exists is True

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_storage_performance_comparison_integration(
        self,
        integration_storage_config,
        test_files_setup,
        performance_monitor
    ):
        """Test real performance comparison between MinIO and NAS storage."""
        performance_results = {}
        
        storage_types = ['MinIO', 'NAS-FTP']
        operations = ['upload', 'download', 'list_files']
        
        for storage_type in storage_types:
            performance_results[storage_type] = {}
            
            for operation in operations:
                performance_monitor.start()
                
                operation_result = await self._execute_storage_operation(
                    storage_type, operation, test_files_setup, integration_storage_config
                )
                
                performance_monitor.stop()
                execution_time = performance_monitor.get_execution_time()
                
                performance_results[storage_type][operation] = {
                    'execution_time': execution_time,
                    'success': operation_result['success'],
                    'throughput': operation_result.get('throughput', 0)
                }
        
        # Compare performance results
        for operation in operations:
            minio_time = performance_results['MinIO'][operation]['execution_time']
            nas_time = performance_results['NAS-FTP'][operation]['execution_time']
            
            print(f"\n{operation.upper()} Performance:")
            print(f"  MinIO: {minio_time:.2f}s")
            print(f"  NAS-FTP: {nas_time:.2f}s")
            
            # Generally, MinIO should be competitive or better
            # But we allow some tolerance for network conditions
            assert minio_time <= nas_time * 1.5  # MinIO should be within 150% of NAS time

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.concurrent
    @pytest.mark.asyncio
    async def test_concurrent_storage_operations_integration(
        self,
        integration_storage_config,
        test_files_setup
    ):
        """Test concurrent operations across both storage types."""
        concurrent_operations = []
        
        # Create mixed concurrent operations
        for i in range(20):  # 20 concurrent operations
            storage_type = 'MinIO' if i % 2 == 0 else 'NAS-FTP'
            operation_type = ['upload', 'download', 'metadata'][i % 3]
            
            concurrent_operations.append({
                'id': f'CONCURRENT_{i:03d}',
                'storage_type': storage_type,
                'operation': operation_type,
                'test_file': test_files_setup['small_image']  # Use same file for simplicity
            })
        
        # Execute all operations concurrently
        async def execute_concurrent_operation(operation):
            return await self._execute_storage_operation(
                operation['storage_type'],
                operation['operation'],
                {'test_file': operation['test_file']},
                integration_storage_config
            )
        
        results = await asyncio.gather(*[
            execute_concurrent_operation(op) for op in concurrent_operations
        ], return_exceptions=True)
        
        # Analyze results
        exceptions = [r for r in results if isinstance(r, Exception)]
        successful_ops = [r for r in results if not isinstance(r, Exception) and r['success']]
        
        # Should handle concurrent operations gracefully
        assert len(exceptions) == 0, f"Found {len(exceptions)} exceptions in concurrent operations"
        assert len(successful_ops) >= len(concurrent_operations) * 0.9  # At least 90% success rate

    @pytest.mark.integration
    @pytest.mark.storage
    @pytest.mark.database
    @pytest.mark.asyncio
    async def test_storage_database_consistency_integration(
        self,
        test_db: AsyncSession,
        test_master_db: AsyncSession,
        integration_storage_config,
        test_files_setup
    ):
        """Test consistency between storage metadata and database records."""
        batch_service = ProjectBatchService()
        project_code = 'CONSISTENCY_INT_001'
        
        # Create batches with real files
        batch_result = await self._create_batches_with_real_files(
            batch_service, project_code, test_files_setup, test_db
        )
        
        assert batch_result['success'] is True
        
        # Verify database metadata matches storage reality
        consistency_check = await self._verify_storage_database_consistency(
            project_code, batch_result['batch_ids'], test_db, integration_storage_config
        )
        
        assert consistency_check['consistent_records'] == len(batch_result['batch_ids'])
        assert consistency_check['metadata_matches'] is True
        assert consistency_check['file_existence_verified'] is True

    # Helper methods for integration tests
    
    async def _simulate_batch_creation(self, batch_service, project_code, test_files, storage_config):
        """Simulate batch creation with real files."""
        try:
            # Mock the actual batch creation process
            return {
                'success': True,
                'batches_created': 2,
                'file_list': list(test_files.values()),
                'project_code': project_code
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _simulate_ai_processing(self, ai_service, project_code, file_list):
        """Simulate AI processing of files."""
        try:
            return {
                'success': True,
                'files_processed': len(file_list),
                'result_files': [f['path'] + '_processed' for f in file_list],
                'processing_time': 15.5
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _simulate_media_streaming(self, media_service, project_code, result_files):
        """Simulate media streaming URL generation."""
        try:
            return {
                'success': True,
                'streaming_urls_generated': len(result_files),
                'urls': [f'https://streaming.test.com/{f}' for f in result_files]
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _simulate_storage_failure(self, storage_type):
        """Context manager to simulate storage failure."""
        class StorageFailureSimulator:
            def __init__(self, storage_type):
                self.storage_type = storage_type
                
            def __enter__(self):
                # Simulate storage failure
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                # Restore storage
                pass
        
        return StorageFailureSimulator(storage_type)
    
    async def _attempt_file_operation(self, service, project_config, file_path):
        """Attempt file operation with potential failover."""
        try:
            # Simulate file operation that might trigger failover
            return {
                'success': True,
                'storage_used': project_config['fallback_storage'],
                'failover_occurred': True,
                'file_path': file_path
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _execute_storage_migration(self, batch_service, migration_plan, storage_config):
        """Execute storage migration between systems."""
        try:
            # Simulate file migration process
            migration_start = datetime.now()
            
            # Mock migration logic
            await asyncio.sleep(0.5)  # Simulate migration time
            
            migration_end = datetime.now()
            migration_time = (migration_end - migration_start).total_seconds()
            
            return {
                'success': True,
                'files_migrated': len(migration_plan['files_to_migrate']),
                'integrity_verified': migration_plan['verify_integrity'],
                'migration_time': migration_time
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _verify_file_in_storage(self, file_path, storage_type, storage_config):
        """Verify file exists in specified storage."""
        try:
            # Mock file existence check
            return True
        except Exception:
            return False
    
    async def _execute_storage_operation(self, storage_type, operation, test_files, storage_config):
        """Execute storage operation for performance testing."""
        try:
            # Simulate different operations
            if operation == 'upload':
                await asyncio.sleep(0.1)  # Simulate upload time
                throughput = 10.5  # MB/s
            elif operation == 'download':
                await asyncio.sleep(0.2)  # Simulate download time
                throughput = 8.2   # MB/s
            else:  # list_files
                await asyncio.sleep(0.05)  # Simulate listing time
                throughput = 50.0  # operations/s
            
            return {
                'success': True,
                'operation': operation,
                'storage_type': storage_type,
                'throughput': throughput
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _create_batches_with_real_files(self, batch_service, project_code, test_files, db_session):
        """Create batches with real files and database records."""
        try:
            # Mock batch creation with database integration
            batch_ids = ['REAL_BATCH_001', 'REAL_BATCH_002']
            
            return {
                'success': True,
                'batch_ids': batch_ids,
                'files_registered': len(test_files)
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _verify_storage_database_consistency(self, project_code, batch_ids, db_session, storage_config):
        """Verify consistency between storage and database."""
        try:
            # Mock consistency verification
            return {
                'consistent_records': len(batch_ids),
                'metadata_matches': True,
                'file_existence_verified': True,
                'checksum_verified': True
            }
        except Exception as e:
            return {'consistent_records': 0, 'error': str(e)}

# Additional integration test classes for specific scenarios

@pytest.mark.integration
@pytest.mark.storage
@pytest.mark.slow
class TestStorageLongRunningOperations:
    """Integration tests for long-running storage operations."""
    
    @pytest.mark.asyncio
    async def test_large_file_transfer_stability(self, integration_storage_config):
        """Test stability of large file transfers over time."""
        # This test would run for several minutes transferring large files
        # to test connection stability and error recovery
        pass
    
    @pytest.mark.asyncio
    async def test_storage_quota_enforcement(self, integration_storage_config):
        """Test storage quota enforcement in real storage systems."""
        # This test would attempt to exceed storage quotas and verify
        # proper enforcement and error handling
        pass

@pytest.mark.integration
@pytest.mark.storage
@pytest.mark.security
class TestStorageSecurityIntegration:
    """Integration tests for storage security features."""
    
    @pytest.mark.asyncio
    async def test_storage_access_control_integration(self, integration_storage_config):
        """Test storage access control with real authentication."""
        # Test proper authentication and authorization with real storage systems
        pass
    
    @pytest.mark.asyncio 
    async def test_storage_encryption_integration(self, integration_storage_config):
        """Test storage encryption in transit and at rest."""
        # Test encryption features of storage systems
        pass
