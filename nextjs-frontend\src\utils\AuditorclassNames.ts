export const primaryBgClass = 'bg-[#0D47A1]';
export const primaryHoverBgClass = 'hover:bg-[#1159B8]';
export const primaryActiveBgClass = 'bg-[#0B3A88]';
export const primaryTextClass = 'text-[#0D47A1]';
export const overlayBaseClasses = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center';
export const cardShadowRoundedBase = 'bg-white shadow rounded';
export const inputBaseClasses = 'border border-gray-300 rounded-md';
export const focusRingClasses = 'focus:outline-none focus:ring-2 focus:ring-[#0D47A1] focus:border-[#0D47A1] transition';
export const paginationBaseClasses = 'px-3 py-1 border border-gray-300';
export const pageWrapperClasses = 'flex h-screen';
export const brandLinkClasses = 'text-3xl font-extrabold text-white no-underline hover:no-underline';
export const userDropdownContainerClasses = 'absolute left-0 bottom-full bg-gray-700 rounded shadow-lg w-full mb-1';
export const changePasswordButtonClasses = 'flex items-center text-white no-underline hover:no-underline text-lg px-4 py-1 hover:bg-gray-600 w-full text-left';
export const dashboardContainerClasses = 'bg-white min-h-screen py-16';
export const dashboardContentWrapperClasses = 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8';
export const dashboardSectionHeaderClasses = 'text-center';
export const dashboardTitleClasses = 'text-5xl font-extrabold text-[#0D47A1]';
export const dashboardDividerClasses = 'mt-4 w-20 h-1 bg-[#0D47A1] mx-auto';
export const dashboardGridClasses = 'mt-12 grid grid-cols-1 md:grid-cols-2 gap-8';
export const btnBaseClasses = `no-underline hover:no-underline ${primaryBgClass} ${primaryHoverBgClass} text-white py-3 px-6 rounded-lg italic shadow-lg transition-all duration-200 ease-in-out transform hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0D47A1]`;
export const dashboardGetStartedButtonClasses = `${btnBaseClasses} px-7`;
export const dashboardViewHistoryButtonClasses = `${btnBaseClasses} px-6`;

export function getSidebarClasses(collapsed: boolean) {
  const bgColor = primaryBgClass;
  return `${bgColor} text-white transition-all duration-300 flex flex-col ${collapsed ? 'w-28' : 'w-64'}`;
}

export function getHeaderClasses(collapsed: boolean) {
  return `flex items-center py-4 gap-4 ${collapsed ? 'justify-center px-0' : 'justify-start px-6'}`;
}

export function getNavItemClasses(
  collapsed: boolean,
  isActive: boolean,
  fullWidth: boolean = true
) {
  const hoverBg = primaryHoverBgClass;
  const activeBg = primaryActiveBgClass;
  const justify = collapsed ? 'justify-center' : 'justify-start';
  const width = fullWidth ? 'w-full' : '';
  return [
    'flex',
    width,
    'items-center text-white no-underline hover:no-underline text-lg',
    justify,
    'px-6 py-4 gap-4',
    hoverBg,
    'rounded',
    isActive ? activeBg : '',
  ]
    .filter(Boolean)
    .join(' ');
}

export const mainContentClasses = 'flex-1 bg-white transition-all duration-300 overflow-auto';
export const flashContainerClasses = 'container mx-auto p-4';

// Shared card and button classes for AuditorDashboard
export const cardContainerClasses = 'bg-transparent rounded-xl transition-all duration-200 ease-in-out cursor-pointer p-8 text-center flex flex-col items-center space-y-6';
export const cardTitleClasses = 'text-xl font-semibold text-gray-800';
export const cardSubtitleClasses = 'text-gray-500';
export const cardDetailClasses = 'text-gray-600 text-center max-w-md mx-auto mb-4';

// Shared classes for TaskList and History components
export const pageContainerClasses = 'max-w-[96rem] mx-auto p-4 m-4';
export const headerContainerClasses = 'text-center mb-8';
export const headerLineClasses = `w-20 h-1 ${primaryBgClass} mx-auto mt-2 rounded`;
export const formContainerClasses = `${cardShadowRoundedBase} mb-4 transition-max-h overflow-hidden`;
export const formHeaderClasses = `${primaryBgClass} text-white border-b py-2 px-4 flex justify-between items-center rounded-t-md`;
export const collapseButtonClasses = 'text-white text-lg hover:text-gray-200';
export const formBodyClasses = 'p-4';
export const formGridClasses = 'grid grid-cols-1 md:grid-cols-3 gap-4 items-end';
export const labelClasses = 'block text-sm font-medium mb-1';
export const selectClasses = `mt-1 block w-full bg-white py-2 px-3 shadow-sm text-sm ${inputBaseClasses} ${focusRingClasses}`;
export const buttonPrimaryClasses = `w-full ${primaryBgClass} ${primaryHoverBgClass} text-white py-2 rounded-md flex justify-center items-center`;
export const sectionContainerClasses = `${cardShadowRoundedBase} p-4`;
export const reviewHeaderClasses = 'flex justify-between items-center border-b pb-2 mb-4';
export const loadingContainerClasses = 'flex flex-col items-center py-10';
export const spinnerClasses = `animate-spin ${primaryTextClass} text-3xl`;
export const loadingTextClasses = 'mt-4 text-gray-500';
export const noDataContainerClasses = 'flex flex-col items-center py-10 text-gray-500';
export const tableContainerClasses = 'overflow-x-auto';
export const tableClasses = 'min-w-full divide-y divide-gray-200 text-sm';
export const tableHeadClasses = 'bg-gray-50';
export const tableHeaderCellClasses = 'px-4 py-2 text-left';
export const tableCellClasses = 'p-2';
export const tableBodyClasses = 'divide-y divide-gray-100';
export const thumbnailImageClasses = 'object-contain cursor-pointer';
export const imageLoadingClasses = 'text-gray-500';
export const inputTableClasses = 'w-full border border-gray-300 rounded-md p-1 text-sm';
export const footerButtonsContainerClasses = 'flex justify-end space-x-2 mt-4';
export const textActionClasses = 'text-gray-600 hover:text-gray-800';
export const commentsButtonClasses = `${textActionClasses} flex items-center`;
export const saveButtonClasses = 'bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded-md flex items-center';
export const modalContainerClasses = `${cardShadowRoundedBase} rounded-md w-96 p-4`;
export const modalHeaderClasses = 'flex justify-between items-center mb-2';
export const textareaClasses = `${inputBaseClasses} p-2 text-sm mb-4`;
export const modalFooterClasses = 'flex justify-end space-x-2';
export const cancelButtonClasses = textActionClasses;
export const saveCommentsButtonClasses = `${primaryBgClass} ${primaryHoverBgClass} text-white py-1 px-3 rounded-md`;
export const previewContainerClasses = `${cardShadowRoundedBase} rounded-md w-[90%] max-w-4xl p-4`;
export const previewHeaderClasses = 'flex justify-between items-center mb-2';
export const previewImageContainerClasses = 'relative overflow-hidden bg-gray-100';
export const previewImageClasses = 'mx-auto object-contain';
export const zoomButtonClasses = 'bg-gray-200 hover:bg-gray-300 p-1 rounded-md';

// Pagination classes
export const paginationContainerClasses = 'flex justify-center mt-4 space-x-1';
export const paginationButtonClasses = `${paginationBaseClasses} hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed`;
export const paginationActiveButtonClasses = `${paginationBaseClasses} ${primaryBgClass} text-white`;
export const paginationPrevButtonClasses = `${paginationButtonClasses} rounded-l`;
export const paginationNextButtonClasses = `${paginationButtonClasses} rounded-r`;
export const paginationPageBaseClasses = 'px-3 py-1 border-t border-b border-gray-300 hover:bg-gray-100';
export const paginationPageActiveClasses = `${paginationPageBaseClasses} ${primaryBgClass} text-white`;
export const paginationPageInactiveClasses = `${paginationPageBaseClasses} bg-white text-gray-700`;

// History component layout constants
export const historyPageContainerClasses = 'max-w-7xl mx-auto p-5';
export const historySearchContainerClasses = 'flex justify-center mb-6';
export const historySearchIconContainerClasses = 'absolute inset-y-0 left-3 flex items-center pointer-events-none';
export const historyLoadingWrapperClasses = 'flex flex-col items-center justify-center p-10';
export const historyTableCellClasses = 'px-4 py-2';

// End of button classes
// Shared row hover for tables
export const rowHoverClasses = 'hover:bg-gray-50'; 