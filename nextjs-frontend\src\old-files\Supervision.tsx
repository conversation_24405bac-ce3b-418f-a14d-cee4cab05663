'use client';

import { useState, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { FaFileUpload, FaCloudUploadAlt, FaSync, FaBolt, FaDatabase, FaInfoCircle, FaGoogle, FaFolder, FaExternalLinkAlt, FaFile, FaUpload } from 'react-icons/fa';
import ReviewInterface from '../components/annotator/ReviewInterface';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';

// Interfaces
interface FileData {
  id: string;
  name: string;
  size: number;
  status: 'queued' | 'processing' | 'completed' | 'error';
  type: string;
  file?: File;
}

interface SupervisionProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onProcessingComplete?: (documents: any[]) => void;
}

interface DriveFile {
  id: string;
  name: string;
  mimeType?: string;
  type?: string;
  thumbnailLink?: string;
  webViewLink?: string;
}

// Constants
const API_BASE = `${process.env.NEXT_PUBLIC_API_URL}/supervision`;
const DOCUMENT_TYPES = [
  { value: 'check', label: 'Cheque' },
  { value: 'passport', label: 'Passport' },
  { value: 'invoice', label: 'Invoice' }
];

const MODEL_TYPES = [
  { value: 'standard', label: 'Standard (Recommended)', description: 'Perfect for clear documents' },
  { value: 'enhanced', label: 'Enhanced (Complex Documents)', description: 'Better for mixed quality' },
  { value: 'premium', label: 'Premium (Difficult Documents)', description: 'Best for poor quality scans' }
];

// Reusable Components
const FormSection = ({ title, icon, children, className = "" }: {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}) => (
  <div className={`bg-white rounded-xl shadow-lg transition-all duration-200 hover:shadow-xl ${className}`}>
    <div className="p-6">
      <div className="flex items-center mb-6">
        <div className="p-3 bg-blue-50 text-blue-600 rounded-full mr-4 text-xl">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  </div>
);

const SelectField = ({ label, value, onChange, options, required = false }: {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string; description?: string }[];
  required?: boolean;
}) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
    <select 
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      className="block w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
    >
      <option value="">Select {label.toLowerCase()}...</option>
      {options.map(option => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  </div>
);

const LoadingState = ({ message }: { message: string }) => (
  <div className="flex flex-col items-center justify-center py-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
    <p className="text-sm text-gray-600">{message}</p>
  </div>
);

const DriveFileItem = ({ file, isSelected, onToggle }: {
  file: DriveFile;
  isSelected: boolean;
  onToggle: () => void;
}) => (
  <div className="flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all
    ${isSelected ? 'bg-blue-50 border-2 border-blue-500' : 'bg-white border border-gray-200 hover:bg-gray-50'}"
    onClick={onToggle}
  >
    {/* File thumbnail or icon */}
    {file.thumbnailLink ? (
      <Image
        src={file.thumbnailLink.replace('=s220', '=s32')}
        alt={file.name}
        width={32}
        height={32}
        className="w-8 h-8 object-cover rounded border mr-3 flex-shrink-0"
      />
    ) : (
      <div className="w-8 h-8 bg-gray-100 rounded border mr-3 flex items-center justify-center flex-shrink-0">
        <FaFile className="text-gray-400 text-sm" />
      </div>
    )}
    {/* File info */}
    <div className="flex-1 min-w-0">
      <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
      <p className="text-xs text-gray-500 truncate">
        {file.mimeType || file.type || 'Unknown type'}
      </p>
    </div>
    {/* Checkbox and external link */}
    <div className="flex items-center space-x-2 ml-2">
      <input
        type="checkbox"
        checked={isSelected}
        onChange={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className="h-4 w-4 text-blue-600 rounded focus:ring-blue-500"
      />
      {file.webViewLink && (
        <a
          href={file.webViewLink}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 hover:text-blue-700 transition-colors"
          onClick={(e) => e.stopPropagation()}
        >
          <FaExternalLinkAlt className="h-3 w-3" />
        </a>
      )}
    </div>
  </div>
);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function Supervision({ onProcessingComplete }: SupervisionProps) {
  // State management
  const [step, setStep] = useState<'upload' | 'review'>('upload');
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [reviewData, setReviewData] = useState<any>(null);
  
  // Local file state
  const [localDocumentType, setLocalDocumentType] = useState('');
  const [localModelType, setLocalModelType] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<FileData[]>([]);
  
  // Drive state
  const [driveDocumentType, setDriveDocumentType] = useState('');
  const [driveModelType, setDriveModelType] = useState('');
  const [driveBreadcrumb, setDriveBreadcrumb] = useState<{id: string; name: string}[]>([]);
  const [currentDriveFolderId, setCurrentDriveFolderId] = useState<string | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [driveFolders, setDriveFolders] = useState<any[]>([]);
  const [driveItems, setDriveItems] = useState<DriveFile[]>([]);
  const [selectedDriveFileIds, setSelectedDriveFileIds] = useState<Set<string>>(new Set());
  
  // Loading states
  const [isLocalLoading, setIsLocalLoading] = useState(false);
  const [localStatusMessage, setLocalStatusMessage] = useState('');
  const [isDriveLoading, setIsDriveLoading] = useState(false);
  const [driveStatusMessage, setDriveStatusMessage] = useState('');
  
  // UI state
  const [showGuide, setShowGuide] = useState(false);
  
  // Computed values
  const canProcessLocal = useMemo(() => 
    localDocumentType && localModelType && selectedFiles.length > 0,
    [localDocumentType, localModelType, selectedFiles.length]
  );
  
  const canProcessDrive = useMemo(() => 
    driveDocumentType && driveModelType && selectedDriveFileIds.size > 0,
    [driveDocumentType, driveModelType, selectedDriveFileIds.size]
  );

  // Event handlers
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    const files = Array.from(e.target.files);
    const newFiles: FileData[] = files.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      status: 'queued',
      type: file.type,
      file,
    }));
    
    setSelectedFiles(newFiles);
  }, []);

  const handleLocalUploadSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!canProcessLocal) return;
    
    setIsLocalLoading(true);
    setLocalStatusMessage('Uploading files...');
    
    const formData = new FormData();
    formData.append('document_type', localDocumentType);
    formData.append('model_type', localModelType);
    selectedFiles.forEach(fileData => {
      if (fileData.file) {
        formData.append('files', fileData.file, fileData.name);
      }
    });

    const response = await authFetch(`${API_BASE}/upload`, {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    if (result.success) {
      const resp = await authFetch(`${API_BASE}/review`);
      const data = await resp.json();
      setReviewData({ ...data, source: 'local' });
      setStep('review');
    } else {
      throw new Error(result.error || 'Upload failed');
    }
    
    setIsLocalLoading(false);
    setLocalStatusMessage('');
  }, [canProcessLocal, localDocumentType, localModelType, selectedFiles]);

  const handleLoadDriveFiles = useCallback(async () => {
    if (!driveDocumentType || !driveModelType) {
      showToast.warning('Please select document type and processing power');
      return;
    }
    
    setIsDriveLoading(true);
    setDriveStatusMessage('Loading Drive contents...');
    
    const response = await authFetch(`${API_BASE}/list-drive-folders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        document_type: driveDocumentType, 
        model_type: driveModelType, 
        folder_id: currentDriveFolderId 
      })
    });
    
    const result = await response.json();
    if (result.success) {
      setDriveBreadcrumb(result.breadcrumb || []);
      setCurrentDriveFolderId(result.current_folder_id || null);
      setDriveFolders(result.folders || []);
      setDriveItems(result.files || []);
      setSelectedDriveFileIds(new Set());
    } else {
      throw new Error(result.error || 'Error loading Drive folders');
    }
    
    setIsDriveLoading(false);
    setDriveStatusMessage('');
  }, [driveDocumentType, driveModelType, currentDriveFolderId]);

  const handleNavigateToFolder = useCallback(async (folderId: string | null) => {
    setIsDriveLoading(true);
    setDriveStatusMessage('Loading folder contents...');
    
    const response = await authFetch(`${API_BASE}/list-drive-folders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        document_type: driveDocumentType, 
        model_type: driveModelType, 
        folder_id: folderId 
      })
    });
    
    const result = await response.json();
    if (result.success) {
      setDriveBreadcrumb(result.breadcrumb || []);
      setCurrentDriveFolderId(result.current_folder_id || null);
      setDriveFolders(result.folders || []);
      setDriveItems(result.files || []);
      setSelectedDriveFileIds(new Set());
    } else {
      throw new Error(result.error || 'Error loading folder contents');
    }
    
    setIsDriveLoading(false);
    setDriveStatusMessage('');
  }, [driveDocumentType, driveModelType]);

  const toggleDriveFileSelection = useCallback((fileId: string) => {
    setSelectedDriveFileIds(prev => {
      const next = new Set(prev);
      if (next.has(fileId)) {
        next.delete(fileId);
      } else {
        next.add(fileId);
      }
      return next;
    });
  }, []);

  const handleProcessDriveFiles = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!canProcessDrive) return;
    
    setIsDriveLoading(true);
    setDriveStatusMessage('Processing selected files...');
    
    const fileLinks: Record<string, { name: string; webViewLink: string }> = {};
    driveItems.forEach(item => {
      if (selectedDriveFileIds.has(item.id)) {
        fileLinks[item.id] = {
          name: item.name,
          webViewLink: item.webViewLink || ''
        };
      }
    });

    const formData = new FormData();
    formData.append('document_type', driveDocumentType);
    formData.append('model_type', driveModelType);
    formData.append('drive_file_ids', JSON.stringify(Array.from(selectedDriveFileIds)));
    formData.append('drive_file_info', JSON.stringify(fileLinks));

    const response = await authFetch(`${API_BASE}/upload`, {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    if (result.success) {
      const resp = await authFetch(`${API_BASE}/review`);
      const data = await resp.json();
      setReviewData({ ...data, source: 'drive' });
      setStep('review');
    } else {
      throw new Error(result.error || 'Processing failed');
    }
    
    setIsDriveLoading(false);
    setDriveStatusMessage('');
  }, [canProcessDrive, driveDocumentType, driveModelType, selectedDriveFileIds, driveItems]);

  const resetToUpload = useCallback(() => {
    setStep('upload');
    setReviewData(null);
    setSelectedFiles([]);
    setSelectedDriveFileIds(new Set());
    setIsLocalLoading(false);
    setLocalStatusMessage('');
    setIsDriveLoading(false);
    setDriveStatusMessage('');
  }, []);

  // Render review interface
  if (step === 'review' && reviewData) {
    return (
      <div>
          <div className="flex justify-end">
            <button
              onClick={resetToUpload}
              className="px-2 py-1 bg-gray-500 mb-2 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium"
            >
              ← Back to Upload
            </button>
          </div>
        <ReviewInterface 
          documents={reviewData.files} 
          documentType={reviewData.document_type}
          source={reviewData.source}
          resetToUpload={resetToUpload}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Process Documents</h1>
          <p className="text-xl text-gray-600 mb-6">
            Extract valuable information from your documents with advanced AI processing
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Local File Upload */}
          <FormSection
            title="Process Local Files"
            icon={<FaFileUpload />}
            className="h-fit"
          >
            <form onSubmit={handleLocalUploadSubmit} className="space-y-4">
              <SelectField
                label="Document Type"
                value={localDocumentType}
                onChange={setLocalDocumentType}
                options={DOCUMENT_TYPES}
                required
              />

              <SelectField
                label="Processing Power"
                value={localModelType}
                onChange={setLocalModelType}
                options={MODEL_TYPES}
                required
              />

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Select Files</label>
                <div className="relative">
                  <input 
                    type="file" 
                    multiple 
                    onChange={handleFileSelect}
                    required
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 transition-colors" 
                  />
                  <div className="mt-3 text-center">
                    <FaCloudUploadAlt className="mx-auto text-gray-400 text-3xl mb-2" />
                    <span className="text-sm text-gray-500">Drag and drop files or click to browse</span>
                    {selectedFiles.length > 0 && (
                      <div className="mt-2">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                          <FaFile className="mr-1" />
                          {selectedFiles.length} File{selectedFiles.length !== 1 ? 's' : ''} Selected
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {isLocalLoading && localStatusMessage && (
                <LoadingState message={localStatusMessage} />
              )}

              <button 
                type="submit" 
                disabled={!canProcessLocal || isLocalLoading}
                className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                <FaUpload className="mr-2" />
                Process Local Files
              </button>
            </form>
          </FormSection>

          {/* Google Drive */}
          <FormSection
            title="Process from Google Drive"
            icon={<FaGoogle />}
            className="h-fit"
          >
            <form onSubmit={handleProcessDriveFiles} className="space-y-4">
              <SelectField
                label="Document Type"
                value={driveDocumentType}
                onChange={setDriveDocumentType}
                options={DOCUMENT_TYPES}
                required
              />

              <SelectField
                label="Processing Power"
                value={driveModelType}
                onChange={setDriveModelType}
                options={MODEL_TYPES}
                required
              />

              <button 
                type="button"
                onClick={handleLoadDriveFiles}
                disabled={!driveDocumentType || !driveModelType || isDriveLoading}
                className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                <FaSync className="mr-2" />
                Load Drive Files
              </button>

              {isDriveLoading && driveStatusMessage && (
                <LoadingState message={driveStatusMessage} />
              )}

              {/* Drive Navigation */}
              {(driveFolders.length > 0 || driveItems.length > 0) && (
                <div className="border border-gray-200 rounded-lg bg-gray-50 p-4">
                  {/* Breadcrumb */}
                  {driveBreadcrumb.length > 0 && (
                    <nav className="mb-4">
                      <ol className="flex space-x-2 text-sm text-gray-500">
                        {driveBreadcrumb.map((item, idx) => (
                          <li key={item.id} className="flex items-center">
                            {idx < driveBreadcrumb.length - 1 ? (
                              <button
                                onClick={() => handleNavigateToFolder(item.id)}
                                className="text-blue-600 hover:text-blue-800 hover:underline"
                              >
                                {item.name}
                              </button>
                            ) : (
                              <span className="text-gray-900 font-medium">{item.name}</span>
                            )}
                            {idx < driveBreadcrumb.length - 1 && <span className="mx-2">/</span>}
                          </li>
                        ))}
                      </ol>
                    </nav>
                  )}

                  {/* Folders */}
                  {driveFolders.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">
                        Folders ({driveFolders.length})
                      </h4>
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {driveFolders.map(folder => (
                          <button
                            key={folder.id}
                            onClick={() => handleNavigateToFolder(folder.id)}
                            className="w-full flex items-center p-2 bg-white rounded border border-gray-200 hover:bg-gray-50 transition-colors text-left"
                          >
                            <FaFolder className="text-yellow-500 mr-2 flex-shrink-0" />
                            <span className="text-gray-800 truncate">{folder.name}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Files */}
                  {driveItems.length > 0 && (
                    <div>
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="text-sm font-medium text-gray-700">
                          Files ({driveItems.length})
                        </h4>
                        <div className="text-xs text-gray-500">
                          {selectedDriveFileIds.size} of {driveItems.length} selected
                        </div>
                      </div>
                      
                      <div className="max-h-64 overflow-y-auto space-y-2 mb-4">
                        {driveItems.map(file => (
                          <DriveFileItem
                            key={file.id}
                            file={file}
                            isSelected={selectedDriveFileIds.has(file.id)}
                            onToggle={() => toggleDriveFileSelection(file.id)}
                          />
                        ))}
                      </div>

                      <div className="flex justify-between items-center">
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            onClick={() => setSelectedDriveFileIds(new Set(driveItems.map(f => f.id)))}
                            disabled={selectedDriveFileIds.size === driveItems.length}
                            className="px-3 py-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 text-white rounded text-sm font-medium transition-colors"
                          >
                            Select All
                          </button>
                          <button
                            type="button"
                            onClick={() => setSelectedDriveFileIds(new Set())}
                            disabled={selectedDriveFileIds.size === 0}
                            className="px-3 py-1 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-300 text-white rounded text-sm font-medium transition-colors"
                          >
                            Clear
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <button 
                type="submit" 
                disabled={!canProcessDrive || isDriveLoading}
                className="w-full flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg font-medium transition-colors"
              >
                <FaDatabase className="mr-2" />
                Process {selectedDriveFileIds.size > 0 ? `${selectedDriveFileIds.size} ` : ''}Selected File{selectedDriveFileIds.size !== 1 ? 's' : ''}
              </button>
            </form>
          </FormSection>
        </div>

        {/* Processing Power Guide */}
        <div className="bg-white rounded-xl shadow-lg">
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <FaBolt className="text-blue-600 text-xl mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Processing Power Guide</h3>
            </div>
            <button
              onClick={() => setShowGuide(!showGuide)}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
            >
              <FaInfoCircle className="mr-2" />
              {showGuide ? 'Hide Guide' : 'Show Guide'}
            </button>
          </div>
          
          {showGuide && (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {MODEL_TYPES.map((model, index) => (
                  <div
                    key={model.value}
                    className="bg-gray-50 rounded-lg p-6 border-l-4 border-blue-500"
                  >
                    <div className="flex items-center mb-3">
                      <FaBolt className="text-blue-600 mr-2" />
                      <h4 className="text-lg font-semibold text-gray-900">{model.label.split(' ')[0]}</h4>
                    </div>
                    
                    <div className="flex mb-3">
                      {[...Array(3)].map((_, i) => (
                        <div 
                          key={i}
                          className={`h-2 flex-1 rounded mr-1 last:mr-0 ${
                            i <= index ? 'bg-blue-600' : 'bg-gray-200'
                          }`}
                        />
                      ))}
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-2">{model.description}</p>
                    <p className="text-xs text-gray-500">
                      {index === 0 && 'Best for: Clean, high-quality documents'}
                      {index === 1 && 'Best for: Mixed quality documents, complex forms'}
                      {index === 2 && 'Best for: Difficult documents, poor scans'}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 