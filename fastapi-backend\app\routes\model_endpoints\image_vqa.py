import logging
from typing import Dict, Any, Optional
import httpx
from core.config import settings
from .model_utils import async_exception_handler

logger = logging.getLogger('image_vqa_service')

class ImageVQAService:
    def __init__(self):
        self.wrapper_url = settings.api_settings.wrapper_url
    def create_client(self) -> httpx.AsyncClient:
        return httpx.AsyncClient(
            base_url=self.wrapper_url.rstrip('/'),
            timeout=None,
            follow_redirects=True
        )

    @async_exception_handler
    async def answer_question(
        self,
        image_data,
        question: str,
        model_name: Optional[str] = None,
    ) -> Dict[str, Any]:
        original_filename = getattr(image_data, 'filename', 'image.jpg')
        
        try:
            async with self.create_client() as client:
                if hasattr(image_data, 'seek'):
                    await image_data.seek(0)
                
                if hasattr(image_data, 'file') and hasattr(image_data.file, '__await__'):
                    file_stream = await image_data.file
                else:
                    file_stream = image_data.file if hasattr(image_data, 'file') else image_data
                
                files = {
                    "image": (original_filename, file_stream,
                            getattr(image_data, 'content_type', 'image/jpeg'))
                }
                
                if question is not None and str(question).lower() == "string":
                    question = ""
                if model_name is not None and str(model_name).lower() == "string":
                    model_name = None
                    
                params: Dict[str, Any] = {"question": question}
                if model_name:
                    params["model_name"] = model_name
                    
                response = await client.post("/wrapper/vqa", files=files, data=params)
                response.raise_for_status()
                result = response.json()
                
                return {
                    "file_name": original_filename,
                    "question": question,
                    "answer": result.get("answer", ""),
                    "status": "success"
                }
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error answering question via wrapper: {error_msg}", exc_info=True)
            return {
                "file_name": original_filename,
                "question": question,
                "error": error_msg,
                "status": "failed"
            }

def get_image_vqa_service() -> ImageVQAService:
    """Get the image VQA service instance"""
    return ImageVQAService()
