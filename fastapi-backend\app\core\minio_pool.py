"""
MinIO Connection Pool Implementation

This module provides a connection pool for MinIO connectors to avoid
the overhead of creating new connections for each request.
"""
import asyncio
import logging
import time
from typing import Dict, Optional
from dataclasses import dataclass
from core.minio_connector import MinIONASConnector
from core.minio_utils import get_minio_connector_from_credentials

logger = logging.getLogger(__name__)

@dataclass
class PooledConnection:
    """Wrapper for a pooled MinIO connection with metadata"""
    connector: MinIONASConnector
    project_code: str
    created_at: float
    last_used: float
    in_use: bool = False
    use_count: int = 0
    is_buffer: bool = False

class MinIOConnectionPool:
    """
    Connection pool for MinIO connectors to reduce connection overhead
    """
    
    def __init__(self,
                 max_connections_per_project: int = 50,
                 buffer_connections_per_project: int = 25,  # Additional buffer capacity
                 connection_ttl: int = 1800,  # 30 minutes
                 cleanup_interval: int = 300):  # 5 minutes
        self.max_connections_per_project = max_connections_per_project
        self.buffer_connections_per_project = buffer_connections_per_project
        self.total_max_connections = max_connections_per_project + buffer_connections_per_project
        self.connection_ttl = connection_ttl
        self.cleanup_interval = cleanup_interval
        
        # Pool storage: project_code -> list of PooledConnection
        self._pools: Dict[str, list[PooledConnection]] = {}
        self._locks: Dict[str, asyncio.Lock] = {}
        self._global_lock = asyncio.Lock()
        
        # Background cleanup task
        self._cleanup_task = None
        self._shutdown = False
        
        logger.info(f"MinIO Connection Pool initialized with base={max_connections_per_project}, buffer={buffer_connections_per_project}, total_max={self.total_max_connections}")
    
    async def start(self):
        """Start the connection pool and background cleanup task"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("MinIO Connection Pool cleanup task started")
    
    async def stop(self):
        """Stop the connection pool and cleanup all connections"""
        self._shutdown = True
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Close all connections
        async with self._global_lock:
            for project_code, connections in self._pools.items():
                for conn in connections:
                    try:
                        # MinIO connections don't need explicit closing
                        # but we can log the cleanup
                        logger.debug(f"Cleaning up connection for project {project_code}")
                    except Exception as e:
                        logger.warning(f"Error cleaning up connection for {project_code}: {e}")
                connections.clear()
            self._pools.clear()
            self._locks.clear()
        
        logger.info("MinIO Connection Pool stopped and all connections cleaned up")
    
    async def get_connector(self, project_code: str, project_credentials: dict) -> Optional[MinIONASConnector]:
        """
        Get a MinIO connector from the pool or create a new one
        
        Args:
            project_code: The project code to get connector for
            project_credentials: MinIO credentials for the project
            
        Returns:
            MinIONASConnector instance or None if failed
        """
        if self._shutdown:
            logger.warning("Connection pool is shutting down, cannot provide connector")
            return None
        
        # Get or create project-specific lock
        async with self._global_lock:
            if project_code not in self._locks:
                self._locks[project_code] = asyncio.Lock()
            project_lock = self._locks[project_code]
        
        # Use project-specific lock to avoid contention between different projects
        async with project_lock:
            # Initialize pool for project if needed
            if project_code not in self._pools:
                self._pools[project_code] = []
            
            pool = self._pools[project_code]
            current_time = time.time()
            
            # Try to find an available connection
            for conn in pool:
                if (not conn.in_use and 
                    current_time - conn.created_at < self.connection_ttl):
                    
                    conn.in_use = True
                    conn.last_used = current_time
                    conn.use_count += 1
                    logger.debug(f"Reusing MinIO connection for project {project_code} (use_count: {conn.use_count})")
                    return conn.connector
            
            # No available connection, create new one if under limit
            pool_size = len(pool)

            if pool_size < self.max_connections_per_project:
                # Within base pool size - create normal connection
                connector = await get_minio_connector_from_credentials(project_credentials)
                if connector:
                    pooled_conn = PooledConnection(
                        connector=connector,
                        project_code=project_code,
                        created_at=current_time,
                        last_used=current_time,
                        in_use=True,
                        use_count=1,
                        is_buffer=False
                    )
                    pool.append(pooled_conn)
                    logger.info(f"Created base MinIO connection for project {project_code} (pool: {len(pool)}/{self.max_connections_per_project})")
                    return connector

            elif pool_size < self.total_max_connections:
                # Using buffer capacity - create buffer connection
                connector = await get_minio_connector_from_credentials(project_credentials)
                if connector:
                    pooled_conn = PooledConnection(
                        connector=connector,
                        project_code=project_code,
                        created_at=current_time,
                        last_used=current_time,
                        in_use=True,
                        use_count=1,
                        is_buffer=True  # Mark as buffer connection
                    )
                    pool.append(pooled_conn)
                    logger.warning(f"Created BUFFER MinIO connection for project {project_code} (pool: {len(pool)}/{self.total_max_connections})")
                    return connector

            else:
                # Pool and buffer are both full - try to reuse oldest idle connection
                idle_connections = [conn for conn in pool if not conn.in_use]
                if idle_connections:
                    oldest_conn = min(idle_connections, key=lambda c: c.last_used)
                    oldest_conn.in_use = True
                    oldest_conn.last_used = current_time
                    oldest_conn.use_count += 1
                    logger.debug(f"Reusing oldest idle connection for project {project_code}")
                    return oldest_conn.connector

                logger.error(f"POOL EXHAUSTED: All {self.total_max_connections} connections busy for project {project_code}")
                return None
    
    async def return_connector(self, project_code: str, connector: MinIONASConnector):
        """
        Return a connector to the pool
        
        Args:
            project_code: The project code
            connector: The connector to return
        """
        if self._shutdown:
            return
        
        async with self._global_lock:
            if project_code not in self._locks:
                return
            project_lock = self._locks[project_code]
        
        async with project_lock:
            if project_code in self._pools:
                for conn in self._pools[project_code]:
                    if conn.connector is connector:
                        conn.in_use = False
                        conn.last_used = time.time()
                        logger.debug(f"Returned MinIO connection to pool for project {project_code}")
                        return
        
        logger.warning(f"Attempted to return unknown connector for project {project_code}")
    
    
    async def _cleanup_loop(self):
        """Background task to clean up expired connections"""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in connection pool cleanup: {e}")
    
    async def _cleanup_expired_connections(self):
        """Remove expired or idle connections from the pool"""
        current_time = time.time()
        total_cleaned = 0
        
        async with self._global_lock:
            for project_code, pool in list(self._pools.items()):
                cleaned_count = 0
                
                # Remove expired connections that are not in use
                original_count = len(pool)
                pool[:] = [
                    conn for conn in pool
                    if conn.in_use or current_time - conn.created_at < self.connection_ttl
                ]
                
                cleaned_count = original_count - len(pool)
                
                total_cleaned += cleaned_count
                
                # Remove empty pools
                if not pool:
                    del self._pools[project_code]
                    if project_code in self._locks:
                        del self._locks[project_code]
        
        if total_cleaned > 0:
            logger.info(f"Cleaned up {total_cleaned} expired MinIO connections")
    
    async def get_pool_stats(self) -> Dict[str, Dict[str, int]]:
        """Get statistics about the connection pool"""
        stats = {}
        current_time = time.time()
        
        async with self._global_lock:
            for project_code, pool in self._pools.items():
                active = sum(1 for conn in pool if conn.in_use)
                idle = len(pool) - active
                base_connections = sum(1 for conn in pool if not conn.is_buffer)
                buffer_connections = sum(1 for conn in pool if conn.is_buffer)
                
                stats[project_code] = {
                    "total": len(pool),
                    "active": active,
                    "idle": idle,
                    "base_connections": base_connections,
                    "buffer_connections": buffer_connections,
                    "base_limit": self.max_connections_per_project,
                    "buffer_limit": self.buffer_connections_per_project,
                    "total_limit": self.total_max_connections,
                    "pool_exhausted": len(pool) >= self.total_max_connections and active == len(pool)
                }
        
        return stats

# Global connection pool instance
_connection_pool: Optional[MinIOConnectionPool] = None

async def get_connection_pool() -> MinIOConnectionPool:
    """Get the global MinIO connection pool instance"""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = MinIOConnectionPool()
        await _connection_pool.start()
    return _connection_pool

async def shutdown_connection_pool():
    """Shutdown the global connection pool"""
    global _connection_pool
    if _connection_pool:
        await _connection_pool.stop()
        _connection_pool = None
