"""
Logging configuration module.

This module provides utilities for setting up logging across the application.
It configures file and console handlers with appropriate formatting and rotation.
"""
import os
import logging
from logging.handlers import RotatingFileHandler
from enum import Enum
import platform

# Default log format
DEFAULT_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Log levels
class LogLevel(Enum):
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# Logger categories for better organization
CACHE_LOGGERS = [
    'admin_cache','annotator_cache','auditor_cache','auth_cache','fetch_data_cache','redis_connector',
    'supervision_cache'
]

CORE_LOGGERS = ['ftp_connector','ftp_pool','nas_connector','security']

DB_LOGGERS = ['init_db','db_connector']

DEPENDENCIES_LOGGERS = ['auth']

ROUTE_LOGGERS = [
    'admin_routes', 'annotator_routes', 'annotator_supervision_routes',
    'auditor_routes', 'auth_routes','image_extractor_routes','telegram_fetch_data_routes'
]
SERVICES_LOGGERS = [
    'annotator_service','auditor_service','image_extractor_service','batch_service','auth_service']

# UTILS_LOGGERS = [
#     'google_sheets','google_drive','google_auth','telegram_drive_integration','telegram_fetcher',
#     'telegram_image_handler','media_utils','document_processor','export','file_handler','main_model'
# ]

# Combine all logger categories
UVICORN_LOGGERS = ['uvicorn', 'uvicorn.error', 'uvicorn.access']
ALL_LOGGERS = (
       CACHE_LOGGERS + CORE_LOGGERS + DB_LOGGERS + DEPENDENCIES_LOGGERS + ROUTE_LOGGERS + SERVICES_LOGGERS +  UVICORN_LOGGERS
)


def get_console_handler(formatter):
    """
    Create an appropriate console handler based on the platform.

    Args:
        formatter: The formatter to use for log messages

    Returns:
        A configured console handler
    """
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # On Windows, handle potential encoding issues
    if platform.system() == 'Windows':
        # Add a filter to handle encoding errors
        def encoding_filter(record):
            try:
                # Try to encode/decode with cp1252 (Windows default)
                record.getMessage().encode('cp1252', errors='replace').decode('cp1252')
                return True
            except Exception:
                return False

        console_handler.addFilter(encoding_filter)

    return console_handler

def get_file_handler(log_path, formatter, max_bytes=10*1024*1024, backup_count=5):
    """
    Create a rotating file handler.

    Args:
        log_path: Path to the log file
        formatter: The formatter to use for log messages
        max_bytes: Maximum size of log file before rotation
        backup_count: Number of backup files to keep

    Returns:
        A configured file handler
    """
    file_handler = RotatingFileHandler(
        log_path,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    return file_handler

def setup_logger(name, log_file, level=LogLevel.INFO.value, log_format=DEFAULT_LOG_FORMAT,
               propagate=False, console_output=True):
    """
    Set up a logger with file and console handlers.

    Args:
        name: Logger name
        log_file: Log file name (will be stored in logs directory)
        level: Logging level (default: INFO)
        log_format: Format string for log messages
        propagate: Whether to propagate messages to parent loggers (default: False)
        console_output: Whether to add a console handler (default: True)

    Returns:
        Logger instance
    """
    # Create full path to log file
    log_path = os.path.join(logs_dir, log_file)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Prevent propagation to parent loggers (e.g., root) to avoid duplicate logs
    logger.propagate = propagate

    # Remove existing handlers to avoid duplicates
    if logger.handlers:
        logger.handlers = []

    # Create formatter
    formatter = logging.Formatter(log_format)

    # Add file handler
    file_handler = get_file_handler(log_path, formatter)
    logger.addHandler(file_handler)

    # Add console handler if requested
    if console_output:
        try:
            console_handler = get_console_handler(formatter)
            logger.addHandler(console_handler)
        except Exception as e:
            # If console handler fails, create a fallback file handler
            fallback_handler = get_file_handler(
                os.path.join(logs_dir, f"{name}_console.log"),
                formatter,
                max_bytes=10*1024*1024,
                backup_count=2
            )
            logger.addHandler(fallback_handler)
            # Log the error to the file
            logger.error(f"Failed to create console handler: {str(e)}")

    return logger

def get_environment_log_level():
    """
    Determine the appropriate log level based on environment variables.

    Returns:
        Logging level (default: INFO)
    """
    env_level = os.environ.get('LOG_LEVEL', 'INFO').upper()

    if env_level == 'DEBUG':
        return LogLevel.DEBUG.value
    elif env_level == 'WARNING':
        return LogLevel.WARNING.value
    elif env_level == 'ERROR':
        return LogLevel.ERROR.value
    elif env_level == 'CRITICAL':
        return LogLevel.CRITICAL.value
    else:
        return LogLevel.INFO.value  

def set_logger_level(logger_name, level):
    """
    Set the log level for a specific logger.

    Args:
        logger_name: Name of the logger to adjust
        level: New log level (can be a LogLevel enum value or a logging level constant)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
        return True
    except Exception as e:
        root_logger = logging.getLogger('root')
        root_logger.error(f"Failed to set log level for {logger_name}: {str(e)}")
        return False

def configure_app_logging(default_level=None, console_for_root=False, console_for_modules=False, module_levels=None):
    """
    Configure logging for the entire application.

    Args:
        default_level: Override the default log level
        console_for_root: Whether to add a console handler for the root logger (default: False)
        console_for_modules: Whether to add console handlers for module loggers (default: False)
                            Set to True for development, False for production
        module_levels: Dictionary mapping module names to specific log levels
                      Example: {'nas_connector': LogLevel.DEBUG.value}

    Returns:
        Root logger instance
    """
    # Determine log level
    log_level = default_level if default_level is not None else get_environment_log_level()

    # Initialize module_levels if not provided
    if module_levels is None:
        module_levels = {}

    # Configure root logger (always with console output)
    root_logger = setup_logger('root', 'main.log', level=log_level, propagate=False, console_output=console_for_root)

    # Configure all other loggers
    for logger_name in ALL_LOGGERS:
        # Skip root logger as it's already configured
        if logger_name != 'root':
            # Get specific level for this module if defined, otherwise use default
            module_level = module_levels.get(logger_name, log_level)

            # Module loggers don't propagate to root and only have console output if requested
            setup_logger(
                logger_name,
                f"{logger_name}.log",
                level=module_level,
                propagate=False,  # Don't propagate to root logger
                console_output=console_for_modules  # Console output only if requested
            )

    # Get level name safely (avoiding deprecation warning)
    def get_level_name(level):
        level_names = {
            logging.DEBUG: 'DEBUG',
            logging.INFO: 'INFO',
            logging.WARNING: 'WARNING',
            logging.ERROR: 'ERROR',
            logging.CRITICAL: 'CRITICAL'
        }
        return level_names.get(level, str(level))

    root_logger.info(f"Logging configured with default level: {get_level_name(log_level)}")

    # Log any custom module levels
    if module_levels:
        for module, level in module_levels.items():
            root_logger.info(f"Custom log level for {module}: {get_level_name(level)}")

    # Silence file-watcher noise from watchfiles (used by Uvicorn reload)
    for watch_mod in ('watchfiles', 'watchfiles.main'):
        wf_logger = logging.getLogger(watch_mod)
        wf_logger.setLevel(logging.WARNING)
        wf_logger.propagate = False

    return root_logger
