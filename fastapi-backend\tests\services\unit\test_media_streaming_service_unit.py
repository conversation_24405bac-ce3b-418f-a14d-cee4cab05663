"""
Comprehensive unit tests for MediaStreamingService.
Tests media streaming, MinIO integration, and file serving capabilities.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTTPException, Request
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional
import io
import time

from app.services.media_streaming_service import MediaStreamingService

class TestMediaStreamingServiceUnit:
    """Unit tests for MediaStreamingService with mocked dependencies."""
    
    @pytest.fixture
    def media_service(self):
        """MediaStreamingService instance for testing."""
        return MediaStreamingService()
    
    @pytest.fixture  
    def mock_media_files(self):
        """Mock media files for testing."""
        return {
            'video_file': {
                'path': '/videos/test_video.mp4',
                'size': 10485760,  # 10MB
                'mime_type': 'video/mp4',
                'duration': 120  # seconds
            },
            'audio_file': {
                'path': '/audio/test_audio.wav',
                'size': 5242880,   # 5MB  
                'mime_type': 'audio/wav',
                'duration': 60
            },
            'large_video': {
                'path': '/videos/large_video.mov',
                'size': 104857600, # 100MB
                'mime_type': 'video/quicktime',
                'duration': 300
            }
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_streaming_url_minio_project(self, media_service, mock_media_files):
        """Test getting streaming URL for MinIO project."""
        media_path = mock_media_files['video_file']['path']
        project_code = 'MINIO_PROJECT_001'
        
        # Mock MinIO project detection
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            # Mock MinIO streaming URL generation
            with patch.object(media_service, '_get_minio_streaming_url') as mock_get_url:
                expected_url = 'https://minio.test.com/presigned/video.mp4?expires=3600'
                mock_get_url.return_value = {
                    'streaming_url': expected_url,
                    'expires_in': 3600,
                    'file_size': mock_media_files['video_file']['size']
                }
                
                result = await media_service.get_streaming_url(
                    media_path, 'video', project_code, 3600
                )
                
                assert result['streaming_url'] == expected_url
                assert result['expires_in'] == 3600
                assert mock_get_type.called_with(project_code)
                assert mock_get_url.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_streaming_url_non_minio_project(self, media_service):
        """Test getting streaming URL for non-MinIO project (should raise exception)."""
        media_path = '/videos/test.mp4'
        project_code = 'FTP_PROJECT_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'FTP'
            
            with pytest.raises(HTTPException) as exc_info:
                await media_service.get_streaming_url(
                    media_path, 'video', project_code
                )
            
            assert exc_info.value.status_code == 404
            assert 'only available for MinIO' in exc_info.value.detail

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_stream_media_ftp_project(self, media_service, mock_media_files):
        """Test direct media streaming for FTP project."""
        media_path = mock_media_files['audio_file']['path']
        project_code = 'FTP_PROJECT_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'FTP'
            
            # Mock storage connector context
            with patch('app.utils.media_utils.get_storage_connector_context') as mock_connector:
                mock_context = MagicMock()
                mock_connector.return_value.__aenter__.return_value = mock_context
                
                # Mock file streaming
                with patch('app.utils.media_utils.get_media_from_storage') as mock_get_media:
                    mock_file_data = b'mock audio data' * 1000
                    mock_get_media.return_value = StreamingResponse(
                        io.BytesIO(mock_file_data),
                        media_type='audio/wav'
                    )
                    
                    result = await media_service.stream_media(
                        media_path, 'audio', project_code
                    )
                    
                    assert isinstance(result, StreamingResponse)
                    assert mock_connector.called
                    assert mock_get_media.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_stream_media_minio_should_use_presigned(self, media_service):
        """Test that MinIO projects should use presigned URLs, not direct streaming."""
        media_path = '/videos/minio_video.mp4'
        project_code = 'MINIO_PROJECT_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            # Should still work but log a warning about inefficient usage
            with patch('app.utils.media_utils.get_storage_connector_context') as mock_connector:
                mock_context = MagicMock()
                mock_connector.return_value.__aenter__.return_value = mock_context
                
                with patch('app.utils.media_utils.get_media_from_storage') as mock_get_media:
                    mock_get_media.return_value = StreamingResponse(
                        io.BytesIO(b'video data'),
                        media_type='video/mp4'
                    )
                    
                    result = await media_service.stream_media(
                        media_path, 'video', project_code
                    )
                    
                    assert isinstance(result, StreamingResponse)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_connection_type_success(self, media_service):
        """Test successful project connection type retrieval."""
        project_code = 'CONNECTION_TEST_001'
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            
            # Mock project query result
            mock_project = MagicMock()
            mock_project.credentials = {'storage_type': 'MinIO'}
            
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = mock_project
            mock_session.execute.return_value = mock_result
            
            result = await media_service._get_project_connection_type(project_code)
            
            assert result == 'MinIO'
            assert mock_session.execute.called

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_get_project_connection_type_not_found(self, media_service):
        """Test project connection type retrieval for non-existent project."""
        project_code = 'NONEXISTENT_001'
        
        with patch('app.post_db.master_db.MasterSessionLocal') as mock_session_local:
            mock_session = AsyncMock()
            mock_session_local.return_value.__aenter__.return_value = mock_session
            
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none.return_value = None
            mock_session.execute.return_value = mock_result
            
            with pytest.raises(HTTPException) as exc_info:
                await media_service._get_project_connection_type(project_code)
            
            assert exc_info.value.status_code == 404
            assert 'Project not found' in exc_info.value.detail

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_minio_streaming_url_success(self, media_service, mock_media_files):
        """Test successful MinIO streaming URL generation."""
        media_path = mock_media_files['video_file']['path']
        project_code = 'MINIO_SUCCESS_001'
        
        with patch('app.core.minio_connector.get_minio_client') as mock_get_client:
            mock_client = MagicMock()
            mock_get_client.return_value = mock_client
            
            # Mock presigned URL generation
            expected_url = 'https://minio.test.com/bucket/video.mp4?X-Amz-Expires=3600'
            mock_client.presigned_get_object.return_value = expected_url
            
            # Mock file stat
            mock_stat = MagicMock()
            mock_stat.size = mock_media_files['video_file']['size']
            mock_client.stat_object.return_value = mock_stat
            
            result = await media_service._get_minio_streaming_url(
                media_path, 'video', project_code, 3600
            )
            
            assert result['streaming_url'] == expected_url
            assert result['expires_in'] == 3600
            assert result['file_size'] == mock_media_files['video_file']['size']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_minio_streaming_url_file_not_found(self, media_service):
        """Test MinIO streaming URL generation for non-existent file."""
        media_path = '/videos/nonexistent.mp4'
        project_code = 'MINIO_ERROR_001'
        
        with patch('app.core.minio_connector.get_minio_client') as mock_get_client:
            mock_client = MagicMock()
            mock_get_client.return_value = mock_client
            
            # Mock file not found error
            from minio.error import NoSuchKey
            mock_client.stat_object.side_effect = NoSuchKey('Object not found')
            
            with pytest.raises(HTTPException) as exc_info:
                await media_service._get_minio_streaming_url(
                    media_path, 'video', project_code
                )
            
            assert exc_info.value.status_code == 404
            assert 'File not found' in exc_info.value.detail

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_range_request_support(self, media_service, mock_media_files):
        """Test HTTP range request support for video streaming."""
        media_path = mock_media_files['large_video']['path']
        project_code = 'RANGE_TEST_001'
        
        # Mock request with Range header
        mock_request = MagicMock(spec=Request)
        mock_request.headers = {'Range': 'bytes=0-1023'}  # First 1KB
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'FTP'
            
            with patch('app.utils.media_utils.get_storage_connector_context') as mock_connector:
                mock_context = MagicMock()
                mock_connector.return_value.__aenter__.return_value = mock_context
                
                with patch('app.utils.media_utils.get_media_from_storage') as mock_get_media:
                    # Mock partial content response
                    partial_data = b'video chunk' * 100
                    mock_response = StreamingResponse(
                        io.BytesIO(partial_data),
                        status_code=206,  # Partial Content
                        media_type='video/quicktime',
                        headers={
                            'Content-Range': f'bytes 0-1023/{mock_media_files["large_video"]["size"]}',
                            'Accept-Ranges': 'bytes'
                        }
                    )
                    mock_get_media.return_value = mock_response
                    
                    result = await media_service.stream_media(
                        media_path, 'video', project_code, mock_request
                    )
                    
                    assert isinstance(result, StreamingResponse)
                    # In a real implementation, would check status_code == 206

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_streaming_performance_large_file(self, media_service, mock_media_files, 
                                                   performance_monitor, service_performance_data):
        """Test streaming performance with large files."""
        large_video = mock_media_files['large_video']
        project_code = 'PERFORMANCE_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            with patch.object(media_service, '_get_minio_streaming_url') as mock_get_url:
                performance_monitor.start()
                
                mock_get_url.return_value = {
                    'streaming_url': 'https://fast.minio.com/large_video.mov',
                    'expires_in': 3600,
                    'file_size': large_video['size']
                }
                
                result = await media_service.get_streaming_url(
                    large_video['path'], 'video', project_code
                )
                
                performance_monitor.stop()
                
                execution_time = performance_monitor.get_execution_time()
                acceptable_time = service_performance_data['acceptable_response_times']['stream_media']
                
                assert execution_time < acceptable_time, f"Streaming URL generation took {execution_time}s"
                assert result['file_size'] == large_video['size']

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_streaming_requests(self, media_service, mock_media_files):
        """Test handling concurrent streaming requests."""
        import asyncio
        
        media_files = [
            mock_media_files['video_file']['path'],
            mock_media_files['audio_file']['path'], 
            mock_media_files['large_video']['path']
        ]
        project_code = 'CONCURRENT_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            with patch.object(media_service, '_get_minio_streaming_url') as mock_get_url:
                mock_get_url.return_value = {
                    'streaming_url': 'https://minio.test.com/concurrent/file.mp4',
                    'expires_in': 3600
                }
                
                # Create concurrent requests
                tasks = []
                for i, media_path in enumerate(media_files):
                    task = media_service.get_streaming_url(
                        media_path, 'video', project_code, 3600
                    )
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # All requests should complete successfully
                exceptions = [r for r in results if isinstance(r, Exception)]
                assert len(exceptions) == 0
                
                successful_results = [r for r in results if isinstance(r, dict)]
                assert len(successful_results) == len(media_files)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_security_path_traversal_prevention(self, media_service, security_test_data):
        """Test prevention of path traversal attacks in media paths."""
        malicious_paths = security_test_data['malicious_inputs']
        project_code = 'SECURITY_001'
        
        for malicious_path in malicious_paths:
            if '../' in malicious_path or '..' in malicious_path:
                with patch.object(media_service, '_validate_media_path') as mock_validate:
                    mock_validate.return_value = False  # Should reject malicious paths
                    
                    with pytest.raises(HTTPException) as exc_info:
                        await media_service.get_streaming_url(
                            malicious_path, 'video', project_code
                        )
                    
                    assert exc_info.value.status_code == 400
                    assert 'Invalid media path' in exc_info.value.detail

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_media_type_validation(self, media_service):
        """Test validation of supported media types."""
        valid_types = ['video', 'audio']
        invalid_types = ['image', 'document', 'executable', '', None]
        
        project_code = 'VALIDATION_001'
        media_path = '/test/file.mp4'
        
        # Test valid media types
        for media_type in valid_types:
            with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
                mock_get_type.return_value = 'MinIO'
                
                with patch.object(media_service, '_get_minio_streaming_url') as mock_get_url:
                    mock_get_url.return_value = {'streaming_url': 'https://test.com/file'}
                    
                    # Should not raise exception for valid types
                    result = await media_service.get_streaming_url(
                        media_path, media_type, project_code
                    )
                    assert 'streaming_url' in result
        
        # Test invalid media types
        for media_type in invalid_types:
            with patch.object(media_service, '_validate_media_type') as mock_validate:
                mock_validate.return_value = False
                
                with pytest.raises(HTTPException) as exc_info:
                    await media_service.get_streaming_url(
                        media_path, media_type, project_code
                    )
                
                assert exc_info.value.status_code == 400

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_presigned_url_expiration_validation(self, media_service):
        """Test validation of presigned URL expiration times."""
        media_path = '/videos/test.mp4'
        project_code = 'EXPIRATION_001'
        
        expiration_scenarios = [
            (1, True),        # 1 second - valid minimum
            (3600, True),     # 1 hour - valid default
            (604800, True),   # 1 week - valid maximum
            (0, False),       # 0 seconds - invalid
            (-1, False),      # Negative - invalid
            (604801, False),  # Over 1 week - invalid
        ]
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            for expires_in, should_succeed in expiration_scenarios:
                if should_succeed:
                    with patch.object(media_service, '_get_minio_streaming_url') as mock_get_url:
                        mock_get_url.return_value = {
                            'streaming_url': 'https://minio.test.com/file.mp4',
                            'expires_in': expires_in
                        }
                        
                        result = await media_service.get_streaming_url(
                            media_path, 'video', project_code, expires_in
                        )
                        assert result['expires_in'] == expires_in
                else:
                    with pytest.raises(HTTPException) as exc_info:
                        await media_service.get_streaming_url(
                            media_path, 'video', project_code, expires_in
                        )
                    assert exc_info.value.status_code == 400

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_error_handling_minio_service_unavailable(self, media_service):
        """Test error handling when MinIO service is unavailable."""
        media_path = '/videos/test.mp4'
        project_code = 'MINIO_DOWN_001'
        
        with patch.object(media_service, '_get_project_connection_type') as mock_get_type:
            mock_get_type.return_value = 'MinIO'
            
            with patch('app.core.minio_connector.get_minio_client') as mock_get_client:
                mock_get_client.side_effect = Exception("MinIO service unavailable")
                
                with pytest.raises(HTTPException) as exc_info:
                    await media_service.get_streaming_url(
                        media_path, 'video', project_code
                    )
                
                assert exc_info.value.status_code == 500
                assert 'Failed to get streaming URL' in exc_info.value.detail

    @pytest.mark.unit
    def test_memory_usage_streaming_metadata(self, media_service, service_performance_data):
        """Test memory usage when handling streaming metadata."""
        import sys
        
        # Simulate large metadata objects
        large_metadata = {
            'file_paths': [f'/videos/file_{i}.mp4' for i in range(10000)],
            'file_sizes': [1024 * 1024 * 10] * 10000,  # 10MB each
            'durations': [120] * 10000,  # 2 minutes each
            'formats': ['mp4'] * 10000
        }
        
        initial_size = sys.getsizeof(large_metadata)
        
        # Simulate processing metadata
        processed_metadata = {}
        for i, path in enumerate(large_metadata['file_paths']):
            processed_metadata[path] = {
                'size': large_metadata['file_sizes'][i],
                'duration': large_metadata['durations'][i],
                'format': large_metadata['formats'][i],
                'streaming_url': f'https://cdn.test.com/{path.split("/")[-1]}'
            }
        
        final_size = sys.getsizeof(processed_metadata)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['file_operations']
        assert memory_increase < max_memory, f"Memory usage {memory_increase}MB exceeds limit"
