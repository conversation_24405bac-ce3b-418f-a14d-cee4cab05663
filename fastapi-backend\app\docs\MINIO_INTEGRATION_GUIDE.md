# MinIO Integration Guide

This guide explains how to integrate MinIO object storage with your DADP application, following the same pattern as your existing FTP/NAS integration.

## Overview

MinIO is a high-performance, S3-compatible object storage system. The integration provides:
- Bucket management (create, list, delete)
- File operations (upload, download, delete)
- Directory-like operations using object prefixes
- Presigned URL generation for temporary access
- Project-specific credential management

## Installation

The MinIO Python SDK is already added to `requirements.txt`:
```
minio==7.2.11
```

Install it with:
```bash
pip install -r requirements.txt
```

## Configuration

### 1. MinIO Server Setup

First, set up a MinIO server. You can use Docker:

```bash
# Run MinIO server
docker run -p 9000:9000 -p 9001:9001 \
  --name minio \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  minio/minio server /data --console-address ":9001"
```

Access the MinIO console at `http://localhost:9001` with credentials `minioadmin`/`minioadmin`.

### 2. Create a Bucket

Using the MinIO console or CLI:
```bash
# Install MinIO CLI
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# Configure MinIO client
mc alias set myminio http://localhost:9000 minioadmin minioadmin

# Create a bucket
mc mb myminio/my-bucket
```

## API Endpoints

### Connect to MinIO
```http
POST /admin/connect-minio
Content-Type: application/json

{
  "minio_endpoint": "localhost:9000",
  "minio_access_key": "minioadmin",
  "minio_secret_key": "minioadmin",
  "minio_bucket_name": "my-bucket",
  "minio_secure": false,
  "minio_region": null,
  "project_code": "PROJ_CLIENTNAME_0001",
  "redirect_after": false
}
```

### Check MinIO Status
```http
GET /admin/minio-status
```

### Disconnect MinIO
```http
POST /admin/disconnect-minio
```

## Code Usage Examples

### 1. Basic File Operations

```python
from core.minio_utils import get_project_minio_connector
from post_db.master_models.projects_registry import ProjectsRegistry

# Get MinIO connector for a project
async def upload_file_to_minio(project: ProjectsRegistry, local_file_path: str, remote_path: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    # Upload file
    success = await minio_connector.upload_file(local_file_path, remote_path)
    return success

# Download file
async def download_file_from_minio(project: ProjectsRegistry, remote_path: str, local_path: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    success = await minio_connector.download_file(remote_path, local_path)
    return success
```

### 2. Directory Operations

```python
# List files in a "directory" (prefix)
async def list_minio_directory(project: ProjectsRegistry, path: str = "/"):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    files = await minio_connector.list_directory(path)
    return files

# Create a "directory" (object with trailing slash)
async def create_minio_directory(project: ProjectsRegistry, path: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    success = await minio_connector.create_directory(path)
    return success
```

### 3. File Content Operations

```python
# Get file content as bytes
async def get_file_content(project: ProjectsRegistry, remote_path: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    content = await minio_connector.get_file_content(remote_path)
    return content

# Upload file content from bytes
async def upload_file_content(project: ProjectsRegistry, remote_path: str, content: bytes):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    success = await minio_connector.put_file_content(remote_path, content)
    return success
```

### 4. Presigned URLs

```python
# Generate temporary access URL
async def get_file_url(project: ProjectsRegistry, remote_path: str, expires_in: int = 3600):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    url = await minio_connector.generate_presigned_url(remote_path, expires_in)
    return url
```

### 5. Bucket Management

```python
# List all buckets
async def list_buckets(project: ProjectsRegistry):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    buckets = await minio_connector.list_buckets()
    return buckets

# Create a new bucket
async def create_bucket(project: ProjectsRegistry, bucket_name: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    success = await minio_connector.create_bucket(bucket_name)
    return success
```

## Integration with Existing Services

### 1. AI Processing Service

You can integrate MinIO with your AI processing service similar to how FTP is used:

```python
# In your AI processing service
from core.minio_utils import get_project_minio_connector

async def process_files_with_minio(project: ProjectsRegistry, file_paths: List[str]):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        # Fallback to FTP if MinIO not configured
        ftp_connector = await get_ftp_connector_from_credentials(project.credentials)
        # ... existing FTP logic
        return
    
    # Process files using MinIO
    for file_path in file_paths:
        # Download from MinIO for processing
        local_temp_path = f"/tmp/{os.path.basename(file_path)}"
        await minio_connector.download_file(file_path, local_temp_path)
        
        # Process the file
        # ... your processing logic
        
        # Upload result back to MinIO
        result_path = f"processed/{os.path.basename(file_path)}"
        await minio_connector.upload_file(local_temp_path, result_path)
        
        # Clean up
        os.remove(local_temp_path)
```

### 2. File Handler Integration

```python
# In your file handler
from core.minio_utils import get_project_minio_connector

async def handle_file_upload(project: ProjectsRegistry, file_data: bytes, filename: str):
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    # Upload to MinIO
    remote_path = f"uploads/{filename}"
    success = await minio_connector.put_file_content(remote_path, file_data)
    
    if success:
        # Generate presigned URL for immediate access
        url = await minio_connector.generate_presigned_url(remote_path)
        return {"success": True, "url": url, "path": remote_path}
    
    return {"success": False}
```

## Error Handling

The MinIO connector includes comprehensive error handling:

```python
try:
    minio_connector = await get_project_minio_connector(db, project)
    if not minio_connector:
        raise Exception("MinIO not configured for this project")
    
    # Your MinIO operations here
    result = await minio_connector.upload_file(local_path, remote_path)
    
except Exception as e:
    logger.error(f"MinIO operation failed: {e}")
    # Handle error appropriately
```

## Security Considerations

1. **Credentials Storage**: MinIO credentials are stored in the project registry and cached securely
2. **Access Control**: Use MinIO's built-in access policies for fine-grained permissions
3. **Presigned URLs**: Use presigned URLs for temporary access instead of exposing credentials
4. **HTTPS**: Enable HTTPS (`minio_secure: true`) for production environments

## Migration from FTP/NAS

To migrate from FTP to MinIO:

1. **Parallel Setup**: Configure both FTP and MinIO for the same project
2. **Gradual Migration**: Update services to check for MinIO first, fallback to FTP
3. **Data Migration**: Use the MinIO connector to upload existing FTP data
4. **Testing**: Verify all operations work with MinIO before removing FTP

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check endpoint, credentials, and network connectivity
2. **Bucket Not Found**: Ensure the bucket exists or enable auto-creation
3. **Permission Denied**: Verify access key permissions in MinIO
4. **SSL Errors**: Check `minio_secure` setting matches your server configuration

### Debugging

Enable debug logging:
```python
import logging
logging.getLogger('minio_connector').setLevel(logging.DEBUG)
```

## Performance Tips

1. **Connection Pooling**: The connector handles connection pooling automatically
2. **Async Operations**: All operations are async for better performance
3. **Presigned URLs**: Use presigned URLs for direct client access instead of proxying
4. **Batch Operations**: Consider batching multiple operations when possible

## References

- [MinIO Documentation](https://docs.min.io/enterprise/aistor-object-store/)
- [MinIO Python SDK](https://docs.min.io/docs/python-client-quickstart-guide.html)
- [S3 API Compatibility](https://docs.aws.amazon.com/AmazonS3/latest/API/Welcome.html)
