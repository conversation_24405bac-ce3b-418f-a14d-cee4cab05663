"""
Integration tests for AnnotatorService with real database connections.
Tests end-to-end workflows with actual database operations.
"""

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
import time

from app.services.annotator_service import AnnotatorService

class TestAnnotatorServiceIntegration:
    """Integration tests for AnnotatorService with real dependencies."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_complete_annotation_workflow_real_database(
        self, 
        test_db: AsyncSession,
        test_master_db: AsyncSession,
        test_user_data,
        test_project_data
    ):
        """Test complete annotation workflow with real database."""
        # Create test user and project
        user_data = test_user_data['annotator']
        project = test_project_data['single_annotator']
        
        # Test getting batch for user
        result = await AnnotatorService.get_batch_for_user(
            test_db, user_data.username, 'annotation'
        )
        
        images, batch_info, batch_id, error = result
        
        if error is None:
            assert len(images) > 0
            assert batch_id is not None
            assert batch_info['batch_id'] == batch_id
            
            # Test saving annotation
            annotation_data = {
                'image_path': images[0].image_path if images else '/test/img1.jpg',
                'annotations': [{'x': 100, 'y': 150, 'label': 'test_annotation'}],
                'batch_id': batch_id,
                'user_id': user_data.username
            }
            
            save_result = await AnnotatorService.save_annotation(test_db, annotation_data)
            assert save_result['success'] is True
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_user_batch_access_real_database(
        self,
        test_db: AsyncSession,
        test_user_data
    ):
        """Test concurrent batch access by multiple users."""
        import asyncio
        
        users = [test_user_data['annotator'], test_user_data['verifier']]
        
        # Create concurrent batch requests
        tasks = []
        for user in users:
            task = AnnotatorService.get_batch_for_user(
                test_db, user.username, 'annotation'
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify no database conflicts occurred
        for result in results:
            if not isinstance(result, Exception):
                images, batch_info, batch_id, error = result
                # Each user should either get a batch or get a reasonable error
                assert error is None or 'no available batches' in str(error).lower()
    
    @pytest.mark.integration  
    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_database_performance_large_batch(
        self,
        test_db: AsyncSession,
        test_batch_data,
        performance_monitor
    ):
        """Test database performance with large batch operations."""
        large_batch = test_batch_data['large_batch']
        
        performance_monitor.start()
        
        # Test multiple path normalizations (simulating large batch processing)
        normalized_paths = []
        for file_path in large_batch.file_list:
            normalized_path = AnnotatorService._normalize_image_path(file_path)
            normalized_paths.append(normalized_path)
        
        performance_monitor.stop()
        
        execution_time = performance_monitor.get_execution_time()
        assert execution_time < 2.0  # Should process 1000 paths in under 2 seconds
        assert len(normalized_paths) == len(large_batch.file_list)
    
    @pytest.mark.integration
    @pytest.mark.database_recovery
    @pytest.mark.asyncio
    async def test_database_connection_recovery(self, test_db: AsyncSession):
        """Test service recovery from database connection issues."""
        # This test would simulate database disconnection and recovery
        # Implementation would depend on your specific database setup
        
        # Simulate connection issue
        original_execute = test_db.execute
        
        async def mock_failing_execute(*args, **kwargs):
            raise Exception("Database connection lost")
        
        test_db.execute = mock_failing_execute
        
        # Test service handles error gracefully
        result = await AnnotatorService.get_batch_for_user(
            test_db, 'test_user', 'annotation'
        )
        
        images, batch_info, batch_id, error = result
        assert error is not None
        assert 'connection' in str(error).lower() or 'error' in str(error).lower()
        
        # Restore connection
        test_db.execute = original_execute
