// hooks/useOnboardingSteps.tsx
import { useState } from 'react';
import { OnboardingStep } from '../types';

export const useOnboardingSteps = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [steps, setSteps] = useState<OnboardingStep[]>([
    {
      id: 1,
      title: "Client Registration",
      description: "Create client account",
      completed: false,
    },
    {
      id: 2,
      title: "Data Connector Setup",
      description: "Configure NAS or Google Drive",
      completed: false,
    },
    {
      id: 3,
      title: "Dataset Selection",
      description: "Choose and assign datasets",
      completed: false,
    },
    {
      id: 4,
      title: "Edit Instructions",
      description: "Configure annotation guidelines",
      completed: false,
    },
    {
      id: 5,
      title: "Allocation Strategy",
      description: "Assign allocation strategy to project",
      completed: false,
    },
    {
      id: 6,
      title: "Annotation Requirements",
      description: "Design annotator questions",
      completed: false,
    },
    {
      id: 7,
      title: "Start Processing",
      description: "Create DB & start dataset batching",
      completed: false,
    },
  ]);

  const markStepCompleted = (stepId: number) => {
    setSteps((prev) =>
      prev.map((step) =>
        step.id === stepId ? { ...step, completed: true } : step
      )
    );
  };

  const goToStep = (stepId: number, projectCode?: string) => {
    setCurrentStep(stepId);
    // Project code handling is managed at the ClientOnboarding component level
  };

  const goToNextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const isStepCompleted = (stepId: number) => {
    const step = steps.find(s => s.id === stepId);
    return step?.completed || false;
  };

  return {
    currentStep,
    steps,
    setSteps,
    markStepCompleted,
    goToStep,
    goToNextStep,
    isStepCompleted,
  };
};