#!/usr/bin/env python3
"""
Django-Style Migration System for SQLAlchemy + Alembic
Implements Django's approach: persistent files + sequential numbering
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
import re

# Setup paths - Updated for new structure
app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))

def get_database_url(db_name):
    """Get database URL (sync version for Alembic)"""
    urls = {
        "master_db": os.getenv("MASTER_DB_DATABASE_URL", 
                             "postgresql+asyncpg://kanwar_raj:dadpdev123@10.10.10.30:5432/master_db"),
        "project_db": os.getenv("PROJECT_DB_DATABASE_URL", 
                               "postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/project_db")
    }
    return urls[db_name].replace('postgresql+asyncpg://', 'postgresql+psycopg2://')

def setup_django_style_structure(db_name):
    """Setup Django-style persistent migration structure"""
    
    # Create app-specific migrations directory (like Django apps)
    migrations_dir = Path(__file__).parent / db_name
    versions_dir = migrations_dir / "versions"
    
    migrations_dir.mkdir(exist_ok=True)
    versions_dir.mkdir(exist_ok=True)
    
    print(f"Migration directory: {migrations_dir}")
    
    # Create alembic.ini (persistent, like Django's settings)
    config_path = migrations_dir / "alembic.ini"
    # Escape % characters in URL for ConfigParser
    escaped_url = get_database_url(db_name).replace('%', '%%')
    
    config_content = f"""# Django-style Alembic config for {db_name}
[alembic]
script_location = {migrations_dir}
sqlalchemy.url = {escaped_url}
version_locations = {versions_dir}
file_template = %%(year)d%%(month).2d%%(day).2d_%%(hour).2d%%(minute).2d_%%(slug)s

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARNING
handlers = console
qualname =

[logger_sqlalchemy]
level = WARNING
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
"""
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    # Create env.py (persistent, like Django's apps.py)
    base_import = "base" if db_name == "master_db" else "project_base"
    base_name = "Base" if db_name == "master_db" else "ProjectBase"
    
    env_content = f'''"""
Django-style Alembic environment for {db_name}
This file is persistent and defines how migrations work for this "app"
"""
import sys
from pathlib import Path
from sqlalchemy import engine_from_config, pool
from alembic import context

# Add the app directory to Python path (like Django's manage.py)
sys.path.insert(0, r"{app_dir}")

# This is the Alembic Config object
config = context.config

# Import the appropriate Base for this "app"
try:
    from post_db.{base_import} import {base_name}
    target_metadata = {base_name}.metadata
    print(f"Loaded {{len(target_metadata.tables)}} tables for {db_name}")
    
    # Print table names for debugging (like Django's --verbosity=2)
    table_names = list(target_metadata.tables.keys())
    if table_names:
        print(f"Tables: {{', '.join(table_names)}}")
    
except ImportError as e:
    print(f"Failed to import models for {db_name}: {{e}}")
    target_metadata = None

def run_migrations_offline():
    """Run migrations in 'offline' mode (like Django's --fake)"""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={{"paramstyle": "named"}},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    """Run migrations in 'online' mode (normal Django migrate)"""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

# Run the appropriate migration mode
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''
    
    env_path = migrations_dir / "env.py"
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    # Create script template (like Django's migration template)
    template_content = '''"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}
Database: ''' + db_name + '''

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade():
    """Apply changes to database (like Django's forward operation)"""
    ${upgrades if upgrades else "pass"}


def downgrade():
    """Reverse changes to database (like Django's reverse operation)"""
    ${downgrades if downgrades else "pass"}
'''
    
    template_path = migrations_dir / "script.py.mako"
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    return str(config_path), migrations_dir

def get_next_migration_number(migrations_dir):
    """Get next migration number (like Django's auto-numbering)"""
    versions_dir = migrations_dir / "versions"
    if not versions_dir.exists():
        return "0001"
    
    existing_files = list(versions_dir.glob("*.py"))
    if not existing_files:
        return "0001"
    
    # Extract numbers from existing migration files
    numbers = []
    for file in existing_files:
        match = re.match(r'(\d{4})', file.name)
        if match:
            numbers.append(int(match.group(1)))
    
    if numbers:
        next_num = max(numbers) + 1
        return f"{next_num:04d}"
    else:
        return "0001"

def run_command(command, cwd=None):
    """Run command with proper output handling"""
    try:
        # Default to the app directory (parent of migrations folder)
        default_cwd = Path(__file__).parent.parent
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=cwd or str(default_cwd),
            shell=True
        )
        
        if result.stdout.strip():
            print(result.stdout.strip())
        if result.stderr.strip():
            print(result.stderr.strip())
            
        return result.returncode == 0
    except Exception as e:
        print(f"ERROR: Command failed: {e}")
        return False

def makemigrations(db_name, message=None, reset_history=False):
    """Django-style makemigrations command"""
    print(f"\nMaking migrations for {db_name}...")
    print("-" * 50)
    
    # Setup Django-style structure
    config_path, migrations_dir = setup_django_style_structure(db_name)
    
    # Reset migration history if requested (like Django's --fake-initial)
    if reset_history:
        print("Resetting migration history...")
        reset_migration_history_for_db(db_name)
    
    # Auto-generate message if not provided
    if not message:
        next_num = get_next_migration_number(migrations_dir)
        message = f"{next_num}_auto_migration"
    
    # Check for changes (like Django does)
    print("Checking for model changes...")
    
    # Generate migration using Alembic
    command = f'alembic -c "{config_path}" revision --autogenerate -m "{message}"'
    
    if run_command(command):
        print(f"SUCCESS: Migration created for {db_name}")
        
        # List the migration files (like Django does)
        versions_dir = migrations_dir / "versions"
        migration_files = list(versions_dir.glob("*.py"))
        if migration_files:
            latest = max(migration_files, key=lambda x: x.stat().st_mtime)
            print(f"Created: {latest.name}")
        
        return True
    else:
        print(f"ERROR: Failed to create migration for {db_name}")
        return False

def reset_migration_history_for_db(db_name):
    """Reset migration history for a database (like Django's --fake-initial)"""
    try:
        import psycopg2 # type: ignore
        from urllib.parse import urlparse
        
        url = get_database_url(db_name)
        parsed = urlparse(url)
        
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path[1:]
        )
        
        with conn.cursor() as cur:
            # Check if alembic_version table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'alembic_version'
                );
            """)
            
            if cur.fetchone()[0]:
                cur.execute("DELETE FROM alembic_version;")
                print(f"Cleared migration history for {db_name}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"WARNING: Could not reset history for {db_name}: {e}")
        return False

def migrate(db_name, target="head"):
    """Django-style migrate command"""
    print(f"\nApplying migrations for {db_name}...")
    print("-" * 50)
    
    # Get config path
    migrations_dir = Path(__file__).parent / db_name
    config_path = migrations_dir / "alembic.ini"
    
    if not config_path.exists():
        print(f"ERROR: No migrations found for {db_name}. Run makemigrations first.")
        return False
    
    # Show current status (like Django's --plan)
    print("Migration status:")
    status_command = f'alembic -c "{config_path}" current'
    run_command(status_command)
    
    # Apply migrations
    print(f"Applying migrations to {target}...")
    migrate_command = f'alembic -c "{config_path}" upgrade {target}'
    
    if run_command(migrate_command):
        print(f"SUCCESS: Migrations applied to {db_name}")
        return True
    else:
        print(f"ERROR: Failed to apply migrations to {db_name}")
        return False

def showmigrations(db_name):
    """Django-style showmigrations command"""
    print(f"\nMigration history for {db_name}:")
    print("-" * 50)
    
    migrations_dir = Path(__file__).parent / db_name
    config_path = migrations_dir / "alembic.ini"
    
    if not config_path.exists():
        print(f"ERROR: No migrations found for {db_name}")
        return False
    
    # Show history
    history_command = f'alembic -c "{config_path}" history --verbose'
    run_command(history_command)
    
    # Show current
    print(f"\nCurrent migration for {db_name}:")
    current_command = f'alembic -c "{config_path}" current --verbose'
    run_command(current_command)
    
    return True

def main():
    """Django-style command interface"""
    parser = argparse.ArgumentParser(description='Django-Style Migration System')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # makemigrations command
    make_parser = subparsers.add_parser('makemigrations', help='Create new migration files')
    make_parser.add_argument('app', choices=['master_db', 'project_db', 'all'], help='Which app to create migrations for')
    make_parser.add_argument('--message', '-m', help='Migration message')
    make_parser.add_argument('--reset', action='store_true', help='Reset migration history first (like Django --fake-initial)')
    
    # migrate command  
    migrate_parser = subparsers.add_parser('migrate', help='Apply migrations to database')
    migrate_parser.add_argument('app', choices=['master_db', 'project_db', 'all'], help='Which app to migrate')
    migrate_parser.add_argument('target', nargs='?', default='head', help='Target migration (default: head)')
    
    # showmigrations command
    show_parser = subparsers.add_parser('showmigrations', help='Show migration status')
    show_parser.add_argument('app', choices=['master_db', 'project_db', 'all'], help='Which app to show')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    
    print("=" * 60)
    
    if args.command == 'makemigrations':
        if args.app == 'all':
            success1 = makemigrations('master_db', args.message, args.reset)
            success2 = makemigrations('project_db', args.message, args.reset)
            success = success1 and success2
        else:
            success = makemigrations(args.app, args.message, args.reset)
            
    elif args.command == 'migrate':
        if args.app == 'all':
            success1 = migrate('master_db', args.target)
            success2 = migrate('project_db', args.target)
            success = success1 and success2
        else:
            success = migrate(args.app, args.target)
            
    elif args.command == 'showmigrations':
        if args.app == 'all':
            success1 = showmigrations('master_db')
            success2 = showmigrations('project_db') 
            success = success1 and success2
        else:
            success = showmigrations(args.app)
    
    print("\n" + "=" * 60)
    if success:
        print("COMMAND COMPLETED SUCCESSFULLY!")
    else:
        print("COMMAND FAILED!")
        sys.exit(1)

if __name__ == "__main__":
    main()
