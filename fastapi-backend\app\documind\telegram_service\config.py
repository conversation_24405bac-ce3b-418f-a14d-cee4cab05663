from pydantic_settings import BaseSettings
from pydantic import Field
from typing import List


class CORSSettings(BaseSettings):
    """CORS configuration settings"""
    # Allow only the frontend origin when using credentials
    allow_origins: List[str] = Field(default=["http://localhost:3000"])
    allow_credentials: bool = Field(default=True)
    allow_methods: List[str] = Field(default=["*"])
    allow_headers: List[str] = Field(default=["*"])

class TelegramServiceSettings(BaseSettings):
    """Telegram service configuration settings."""
    version: str = Field(default="1.0.0", env="TELEGRAM_SERVICE_VERSION")
    host: str = Field(default="0.0.0.0", env="TELEGRAM_SERVICE_HOST")
    port: int = Field(default=8002, env="TELEGRAM_SERVICE_PORT")
    docs_url: str = Field(default="/docs", env="TELEGRAM_SERVICE_DOCS_URL")
    redoc_url: str = Field(default="/redoc", env="TELEGRAM_SERVICE_REDOC_URL")
    cors_settings: CORSSettings = Field(default_factory=CORSSettings)