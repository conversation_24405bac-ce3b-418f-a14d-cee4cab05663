# Services Comprehensive Test Suite

This directory contains comprehensive tests for all services in `app/services/`. 

## Test Structure

### Test Categories

1. **Unit Tests** (`unit/`) - Test individual service methods in isolation
2. **Integration Tests** (`integration/`) - Test service interactions with databases and external systems
3. **Mock Tests** (`mocks/`) - Test services with mocked dependencies
4. **Performance Tests** (`performance/`) - Load testing and performance benchmarks
5. **Security Tests** (`security/`) - Authentication, authorization, and data validation
6. **Edge Cases** (`edge_cases/`) - Boundary conditions and error scenarios

### Coverage Goals

- **100% Method Coverage** - Every public method tested
- **Error Handling** - All exception paths covered  
- **Database Operations** - All CRUD operations verified
- **External Integrations** - Storage, FTP, MinIO connections tested
- **Data Validation** - Input sanitization and validation
- **Performance** - Response time and resource usage benchmarks
- **Security** - Access control and data protection

### Test Organization

Each service has dedicated test files:
- `test_{service_name}_unit.py` - Pure unit tests
- `test_{service_name}_integration.py` - Integration tests with real dependencies
- `test_{service_name}_mocks.py` - Tests with mocked external systems
- `test_{service_name}_performance.py` - Performance and load tests
- `test_{service_name}_security.py` - Security-focused tests
- `test_{service_name}_edge_cases.py` - Edge cases and error conditions

### Services Under Test

#### Currently Missing Comprehensive Tests:
1. **annotator_service.py** - Annotation and verification operations
2. **csv_batch_service.py** - CSV batch processing
3. **media_streaming_service.py** - Media streaming with MinIO
4. **batch_allocation_sync_service.py** - Batch synchronization
5. **project_batch_service_dynamic.py** - Dynamic schema handling
6. **verifier_data_service.py** - Verification data management

#### Already Well-Tested (Enhancement Tests):
7. **ai_processing_service.py** - Additional edge case coverage
8. **auth_service.py** - Security hardening tests
9. **project_batch_service.py** - Performance optimization tests

### Running Tests

```bash
# Run all service tests
pytest tests/services/ -v

# Run specific service tests
pytest tests/services/unit/test_annotator_service_unit.py -v

# Run by category
pytest tests/services/integration/ -v
pytest tests/services/performance/ -v --durations=10

# Run with coverage
pytest tests/services/ --cov=app.services --cov-report=html
```

### Test Data and Fixtures

- `conftest.py` - Shared fixtures and test configuration
- `fixtures/` - Test data generators and mock objects
- `factories/` - Data factories for creating test objects
- `utils/` - Test utilities and helper functions
