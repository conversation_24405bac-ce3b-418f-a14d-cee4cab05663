"""
Integration tests for Dynamic Schema Generation.
Tests that database schemas are created correctly based on different AllocationStrategies.

CRITICAL: These tests ensure test environment matches PRODUCTION ARCHITECTURE:
- Dynamic annotator columns based on AllocationStrategies.num_annotators
- Dynamic verification columns based on AllocationStrategies.requires_verification
- Dynamic AI tables based on AllocationStrategies.requires_ai_preprocessing
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text, select, MetaData, inspect
from sqlalchemy.pool import NullPool
import os
import time
import uuid

from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.utils.dynamic_schema_generator import DynamicSchemaGenerator
from app.utils.project_provisioning import ProjectDatabaseProvisioner
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.regression       # Suite marker - Schema generation
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestDynamicSchemaGeneration:
    """REGRESSION TEST SUITE: Dynamic schema generation for allocation strategies."""
    
    @pytest_asyncio.fixture
    async def dynamic_test_engine(self):
        """Create a separate test engine for dynamic schema testing."""
        # Use a unique database name for schema testing
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        db_name = f"test_dynamic_schema_{unique_id}"
        
        # Create test database URL
        base_url = os.environ["DATABASE_URL"].rsplit('/', 1)[0]  # Remove database name
        test_db_url = f"{base_url}/{db_name}"
        
        # Create the database first
        admin_engine = create_async_engine(
            os.environ["DATABASE_URL"],
            pool_pre_ping=True,
            poolclass=NullPool
        )
        
        try:
            async with admin_engine.connect() as conn:
                # Ensure we're not in a transaction for CREATE DATABASE
                await conn.commit()
                await conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                
            # Create test engine for the new database
            test_engine = create_async_engine(
                test_db_url,
                pool_pre_ping=True,
                poolclass=NullPool
            )
            
            yield test_engine, db_name
            
        finally:
            # Cleanup: close test engine and drop database
            try:
                await test_engine.dispose()
            except Exception:
                pass
            
            try:
                async with admin_engine.connect() as conn:
                    # Terminate connections to test database
                    await conn.execute(text(f"""
                        SELECT pg_terminate_backend(pid) 
                        FROM pg_stat_activity 
                        WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
                    """))
                    await conn.commit()
                    await conn.execute(text(f'DROP DATABASE IF EXISTS "{db_name}"'))
            except Exception as e:
                print(f"Warning: Could not cleanup test database {db_name}: {e}")
            finally:
                await admin_engine.dispose()

    @pytest.mark.asyncio
    async def test_single_annotator_schema_generation(self, test_master_db: AsyncSession, dynamic_test_engine):
        """Test schema generation for single annotator strategy."""
        test_engine, db_name = dynamic_test_engine
        
        # Create single annotator strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=False,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Generate schema using dynamic generator
        generator = DynamicSchemaGenerator(strategy)
        migration_script = generator.generate_migration_script()
        
        # Verify migration script contains expected columns
        assert "annotator_1" in migration_script
        assert "annotator_2" not in migration_script  # Should not have second annotator
        assert "verifier" not in migration_script  # No verification required
        assert "model_execution_logs" not in migration_script  # No AI preprocessing
        
        # Execute the migration to create tables
        await self._execute_migration_script(test_engine, migration_script)
        
        # Verify actual table structure
        await self._verify_allocation_batches_structure(test_engine, expected_annotators=1, has_verifier=False)
        await self._verify_file_allocations_structure(test_engine, expected_annotators=1, has_verifier=False)
        await self._verify_ai_tables_exist(test_engine, should_exist=False)

    @pytest.mark.asyncio
    async def test_multi_annotator_schema_generation(self, test_master_db: AsyncSession, dynamic_test_engine):
        """Test schema generation for multi-annotator strategy."""
        test_engine, db_name = dynamic_test_engine
        
        # Create multi-annotator strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=3,
            requires_verification=True,
            requires_ai_preprocessing=True,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Generate schema using dynamic generator
        generator = DynamicSchemaGenerator(strategy)
        migration_script = generator.generate_migration_script()
        
        # Verify migration script contains expected columns
        assert "annotator_1" in migration_script
        assert "annotator_2" in migration_script
        assert "annotator_3" in migration_script
        assert "annotator_4" not in migration_script  # Should not have fourth annotator
        assert "verifier" in migration_script  # Verification required
        assert "model_execution_logs" in migration_script  # AI preprocessing required
        
        # Execute the migration to create tables
        await self._execute_migration_script(test_engine, migration_script)
        
        # Verify actual table structure
        await self._verify_allocation_batches_structure(test_engine, expected_annotators=3, has_verifier=True)
        await self._verify_file_allocations_structure(test_engine, expected_annotators=3, has_verifier=True)
        await self._verify_ai_tables_exist(test_engine, should_exist=True)

    @pytest.mark.asyncio
    async def test_five_annotator_schema_generation(self, test_master_db: AsyncSession, dynamic_test_engine):
        """Test schema generation for strategy with 5 annotators."""
        test_engine, db_name = dynamic_test_engine
        
        # Create 5-annotator strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=5,
            requires_verification=True,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Generate schema using dynamic generator
        generator = DynamicSchemaGenerator(strategy)
        migration_script = generator.generate_migration_script()
        
        # Verify migration script contains all 5 annotators
        for i in range(1, 6):
            assert f"annotator_{i}" in migration_script
        assert "annotator_6" not in migration_script  # Should not have sixth annotator
        assert "verifier" in migration_script  # Verification required
        assert "model_execution_logs" not in migration_script  # No AI preprocessing
        
        # Execute the migration to create tables
        await self._execute_migration_script(test_engine, migration_script)
        
        # Verify actual table structure
        await self._verify_allocation_batches_structure(test_engine, expected_annotators=5, has_verifier=True)
        await self._verify_file_allocations_structure(test_engine, expected_annotators=5, has_verifier=True)
        await self._verify_ai_tables_exist(test_engine, should_exist=False)

    @pytest.mark.asyncio
    async def test_verification_only_strategy(self, test_master_db: AsyncSession, dynamic_test_engine):
        """Test schema generation for strategy with verification but no AI preprocessing."""
        test_engine, db_name = dynamic_test_engine
        
        # Create verification-only strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=2,
            requires_verification=True,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Generate schema using dynamic generator
        generator = DynamicSchemaGenerator(strategy)
        migration_script = generator.generate_migration_script()
        
        # Verify migration script
        assert "annotator_1" in migration_script
        assert "annotator_2" in migration_script
        assert "annotator_3" not in migration_script
        assert "verifier" in migration_script  # Verification required
        assert "model_execution_logs" not in migration_script  # No AI preprocessing
        
        # Execute the migration to create tables
        await self._execute_migration_script(test_engine, migration_script)
        
        # Verify actual table structure
        await self._verify_allocation_batches_structure(test_engine, expected_annotators=2, has_verifier=True)
        await self._verify_file_allocations_structure(test_engine, expected_annotators=2, has_verifier=True)
        await self._verify_ai_tables_exist(test_engine, should_exist=False)

    @pytest.mark.asyncio
    async def test_ai_preprocessing_only_strategy(self, test_master_db: AsyncSession, dynamic_test_engine):
        """Test schema generation for strategy with AI preprocessing but no verification."""
        test_engine, db_name = dynamic_test_engine
        
        # Create AI-only strategy
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=2,
            requires_verification=False,
            requires_ai_preprocessing=True,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        test_master_db.add(strategy)
        await test_master_db.commit()
        await test_master_db.refresh(strategy)
        
        # Generate schema using dynamic generator
        generator = DynamicSchemaGenerator(strategy)
        migration_script = generator.generate_migration_script()
        
        # Verify migration script
        assert "annotator_1" in migration_script
        assert "annotator_2" in migration_script
        assert "annotator_3" not in migration_script
        assert "verifier" not in migration_script  # No verification
        assert "model_execution_logs" in migration_script  # AI preprocessing required
        
        # Execute the migration to create tables
        await self._execute_migration_script(test_engine, migration_script)
        
        # Verify actual table structure
        await self._verify_allocation_batches_structure(test_engine, expected_annotators=2, has_verifier=False)
        await self._verify_file_allocations_structure(test_engine, expected_annotators=2, has_verifier=False)
        await self._verify_ai_tables_exist(test_engine, should_exist=True)

    async def _execute_migration_script(self, engine, migration_script: str):
        """Execute a migration script to create tables."""
        # Extract the upgrade function content
        lines = migration_script.split('\n')
        upgrade_start = None
        upgrade_end = None
        
        for i, line in enumerate(lines):
            if line.strip() == "def upgrade():":
                upgrade_start = i + 1
            elif line.strip() == "def downgrade():" and upgrade_start is not None:
                upgrade_end = i
                break
        
        if upgrade_start is None:
            raise ValueError("Could not find upgrade function in migration script")
        
        # Get the upgrade content (skip the docstring)
        upgrade_lines = lines[upgrade_start:upgrade_end]
        
        # Remove docstring and comments, extract CREATE TABLE statements
        create_statements = []
        current_statement = []
        in_table_creation = False
        
        for line in upgrade_lines:
            line = line.strip()
            if line.startswith('"""') or line.startswith("'''") or line.startswith('#'):
                continue
            if "op.create_table(" in line:
                in_table_creation = True
                current_statement = [line]
            elif in_table_creation:
                current_statement.append(line)
                if line.strip() == ")":
                    in_table_creation = False
                    create_statements.append('\n'.join(current_statement))
                    current_statement = []
        
        # Convert alembic statements to direct SQL
        async with engine.begin() as conn:
            for statement in create_statements:
                sql = self._convert_alembic_to_sql(statement)
                if sql:
                    await conn.execute(text(sql))

    def _convert_alembic_to_sql(self, alembic_statement: str) -> str:
        """Convert alembic create_table statement to direct SQL."""
        # This is a simplified converter for testing purposes
        # In production, you'd use actual alembic operations
        
        lines = alembic_statement.split('\n')
        table_name = None
        columns = []
        constraints = []
        
        for line in lines:
            line = line.strip()
            if "op.create_table(" in line:
                table_name = line.split("'")[1]
            elif "sa.Column(" in line:
                col_sql = self._parse_column_definition(line)
                if col_sql:
                    columns.append(col_sql)
            elif "sa.PrimaryKeyConstraint(" in line:
                constraints.append("PRIMARY KEY (id)")
            elif "sa.UniqueConstraint(" in line and "_batch_identifier_uc" in line:
                constraints.append("UNIQUE (batch_identifier)")
            elif "sa.ForeignKeyConstraint(" in line:
                # Skip FK constraints for simplicity in tests
                pass
        
        if not table_name or not columns:
            return ""
        
        sql = f"CREATE TABLE {table_name} (\n"
        sql += ",\n".join(columns)
        if constraints:
            sql += ",\n" + ",\n".join(constraints)
        sql += "\n)"
        
        return sql

    def _parse_column_definition(self, line: str) -> str:
        """Parse alembic column definition to SQL."""
        # Extract column name
        if "'" not in line:
            return ""
        
        parts = line.split("'")
        col_name = parts[1]
        
        # Determine column type and properties
        col_def = f"{col_name} "
        
        if "sa.Integer()" in line:
            col_def += "INTEGER"
        elif "sa.String(" in line:
            length_match = line.split("length=")[1].split(")")[0] if "length=" in line else "255"
            col_def += f"VARCHAR({length_match})"
        elif "sa.Boolean()" in line:
            col_def += "BOOLEAN"
        elif "sa.TIMESTAMP()" in line:
            col_def += "TIMESTAMP"
        elif "sa.BigInteger()" in line:
            col_def += "BIGINT"
        elif "postgresql.JSONB(" in line:
            col_def += "JSONB"
        elif "postgresql.ARRAY(" in line:
            col_def += "INTEGER[]"
        else:
            col_def += "TEXT"
        
        # Add constraints
        if "nullable=False" in line:
            col_def += " NOT NULL"
        if "autoincrement=True" in line:
            col_def += " GENERATED ALWAYS AS IDENTITY"
        if "default=" in line:
            if "default=0" in line:
                col_def += " DEFAULT 0"
            elif "default=False" in line:
                col_def += " DEFAULT FALSE"
            elif "default=True" in line:
                col_def += " DEFAULT TRUE"
            elif "default='created'" in line:
                col_def += " DEFAULT 'created'"
        
        return col_def

    async def _verify_allocation_batches_structure(self, engine, expected_annotators: int, has_verifier: bool):
        """Verify the allocation_batches table has the correct structure."""
        async with engine.connect() as conn:
            # Check table exists
            result = await conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'allocation_batches'
                ORDER BY column_name
            """))
            columns = {row[0]: row[1] for row in result.fetchall()}
            
            # Verify base columns exist
            assert 'id' in columns
            assert 'batch_identifier' in columns
            assert 'batch_status' in columns
            assert 'total_files' in columns
            assert 'annotation_count' in columns
            assert 'assignment_count' in columns
            assert 'completion_count' in columns
            
            # Verify dynamic annotator columns
            for i in range(1, expected_annotators + 1):
                annotator_col = f'annotator_{i}'
                assert annotator_col in columns, f"Missing annotator column: {annotator_col}"
                assert columns[annotator_col] == 'integer', f"Wrong type for {annotator_col}: {columns[annotator_col]}"
            
            # Verify no extra annotator columns
            extra_annotator = f'annotator_{expected_annotators + 1}'
            assert extra_annotator not in columns, f"Unexpected annotator column: {extra_annotator}"
            
            # Verify verifier column
            if has_verifier:
                assert 'verifier' in columns, "Missing verifier column"
                assert columns['verifier'] == 'integer', f"Wrong type for verifier: {columns['verifier']}"
            else:
                assert 'verifier' not in columns, "Unexpected verifier column"

    async def _verify_file_allocations_structure(self, engine, expected_annotators: int, has_verifier: bool):
        """Verify the file_allocations table has the correct structure."""
        async with engine.connect() as conn:
            # Check table exists
            result = await conn.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'file_allocations'
                ORDER BY column_name
            """))
            columns = {row[0]: row[1] for row in result.fetchall()}
            
            # Verify base columns exist
            assert 'id' in columns
            assert 'file_id' in columns
            assert 'batch_id' in columns
            assert 'allocation_sequence' in columns
            assert 'assignment_count' in columns
            assert 'completion_count' in columns
            
            # Verify dynamic annotator columns and review columns
            for i in range(1, expected_annotators + 1):
                annotator_col = f'annotator_{i}'
                review_col = f'annotator_{i}_review'
                
                assert annotator_col in columns, f"Missing annotator column: {annotator_col}"
                assert columns[annotator_col] == 'integer', f"Wrong type for {annotator_col}: {columns[annotator_col]}"
                
                assert review_col in columns, f"Missing review column: {review_col}"
                assert columns[review_col] == 'jsonb', f"Wrong type for {review_col}: {columns[review_col]}"
            
            # Verify no extra annotator columns
            extra_annotator = f'annotator_{expected_annotators + 1}'
            extra_review = f'annotator_{expected_annotators + 1}_review'
            assert extra_annotator not in columns, f"Unexpected annotator column: {extra_annotator}"
            assert extra_review not in columns, f"Unexpected review column: {extra_review}"
            
            # Verify verifier columns
            if has_verifier:
                assert 'verifier' in columns, "Missing verifier column"
                assert columns['verifier'] == 'integer', f"Wrong type for verifier: {columns['verifier']}"
                assert 'verifier_review' in columns, "Missing verifier_review column"
                assert columns['verifier_review'] == 'jsonb', f"Wrong type for verifier_review: {columns['verifier_review']}"
            else:
                assert 'verifier' not in columns, "Unexpected verifier column"
                assert 'verifier_review' not in columns, "Unexpected verifier_review column"

    async def _verify_ai_tables_exist(self, engine, should_exist: bool):
        """Verify AI-related tables exist or don't exist as expected."""
        async with engine.connect() as conn:
            # Check if model_execution_logs table exists
            result = await conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'model_execution_logs'
            """))
            tables = [row[0] for row in result.fetchall()]
            
            if should_exist:
                assert 'model_execution_logs' in tables, "model_execution_logs table should exist but doesn't"
            else:
                assert 'model_execution_logs' not in tables, "model_execution_logs table should not exist but does"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.regression       # Suite marker - Production validation
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestProductionSchemaMatching:
    """REGRESSION TEST SUITE: Production schema generation validation."""
    
    @pytest.mark.asyncio
    async def test_schema_generator_table_list(self, test_master_db: AsyncSession):
        """Test that DynamicSchemaGenerator returns correct table lists for different strategies."""
        
        # Test minimal strategy
        minimal_strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.SEQUENTIAL,
            num_annotators=1,
            requires_verification=False,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        
        generator = DynamicSchemaGenerator(minimal_strategy)
        tables = generator.get_required_tables()
        
        expected_base_tables = [
            'project_metadata',
            'allocation_batches',
            'files_registry',
            'user_allocations',
            'file_allocations',
            'project_users'
        ]
        
        assert all(table in tables for table in expected_base_tables), f"Missing base tables: {set(expected_base_tables) - set(tables)}"
        assert 'model_execution_logs' not in tables, "model_execution_logs should not be included for minimal strategy"
        
        # Test full strategy
        full_strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=3,
            requires_verification=True,
            requires_ai_preprocessing=True,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        
        full_generator = DynamicSchemaGenerator(full_strategy)
        full_tables = full_generator.get_required_tables()
        
        assert all(table in full_tables for table in expected_base_tables), f"Missing base tables in full strategy: {set(expected_base_tables) - set(full_tables)}"
        assert 'model_execution_logs' in full_tables, "model_execution_logs should be included for full strategy"

    @pytest.mark.asyncio
    async def test_migration_script_generation(self, test_master_db: AsyncSession):
        """Test that migration scripts are generated correctly."""
        
        strategy = test_factory.projects.create_allocation_strategy(,
            strategy_type=StrategyType.PARALLEL,
            num_annotators=2,
            requires_verification=True,
            requires_ai_preprocessing=False,
            allocation_status="active",
        requires_audit=False,
        quality_requirements=None,
        configuration=None)
        
        generator = DynamicSchemaGenerator(strategy)
        script = generator.generate_migration_script()
        
        # Verify script structure
        assert "def upgrade():" in script
        assert "def downgrade():" in script
        assert "revision = " in script
        assert "down_revision = None" in script
        
        # Verify table creation
        assert "op.create_table('project_metadata'" in script
        assert "op.create_table('allocation_batches'" in script
        assert "op.create_table('files_registry'" in script
        assert "op.create_table('user_allocations'" in script
        assert "op.create_table('file_allocations'" in script
        assert "op.create_table('project_users'" in script
        
        # Verify dynamic columns
        assert "'annotator_1'" in script
        assert "'annotator_2'" in script
        assert "'annotator_3'" not in script
        assert "'verifier'" in script
        
        # Verify review columns
        assert "'annotator_1_review'" in script
        assert "'annotator_2_review'" in script
        assert "'verifier_review'" in script
