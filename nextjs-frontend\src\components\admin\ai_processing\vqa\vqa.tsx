"use client";

import React, { useState } from "react";
import { FaSpinner } from "react-icons/fa";
import toast from "react-hot-toast";
import { VQAProps, VQAFormData } from "./vqa";
import { useProjectServiceWithFilter } from "../projectService";
import { useModelService } from "../modelService";
import { API_BASE_URL } from "../../../../lib/api";

export const VQA: React.FC<VQAProps> = ({ onResultReceived }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<VQAFormData>({
    folderPath: "",
    processingMode: "project",
    projectCode: "",
    question: "",
    modelId: "",
  });

  // Use filtered project service for image projects only and model service
  const { projects, isLoading: isLoadingProjects } = useProjectServiceWithFilter('image');
  const { models, isLoading: isLoadingModels, getModelsByFileType } = useModelService();
  
  // Filter models that support VQA/image questioning, fallback to all models if none found
  const vqaModels = getModelsByFileType('image').length > 0 
    ? getModelsByFileType('image') 
    : models;

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.projectCode) {
      toast.error("Please select a project");
      return;
    }

    if (!formData.question.trim()) {
      toast.error("Please enter a question");
      return;
    }

    if (!formData.modelId) {
      toast.error("Please select an AI model");
      return;
    }

    setIsLoading(true);

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("project_code", formData.projectCode!);
      formDataToSend.append("question", formData.question);
      formDataToSend.append("model_name", formData.modelId!);

      const response = await fetch(`${API_BASE_URL}/ai/vqa/batch/project`, {
        method: "POST",
        credentials: "include",
        body: formDataToSend,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to process project");
      }

      const result = await response.json();
      toast.success("Project processed successfully");
      onResultReceived(result);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to process");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900">Batch VQA Processing</h3>
        <p className="text-sm text-gray-600">Ask questions about images from selected projects</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Project Selection */}
        <div>
          <label htmlFor="projectCode" className="block text-sm font-medium text-gray-700 mb-1">
            Select Project
          </label>
          <div className="flex">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                
              </div>
              <select
                id="projectCode"
                name="projectCode"
                value={formData.projectCode || ""}
                onChange={handleFormChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
                disabled={isLoadingProjects}
              >
                <option value="">
                  {isLoadingProjects ? "Loading projects..." : "Select a project"}
                </option>
                {projects.map((project) => (
                  <option key={project.id} value={project.project_code}>
                    {project.project_name} ({project.project_code})
                  </option>
                ))}
              </select>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Select a project to process all images in its folder
          </p>
        </div>

        {/* Model Selection */}
        <div>
          <label htmlFor="modelId" className="block text-sm font-medium text-gray-700 mb-1">
            Select AI Model
          </label>
          <div className="flex">
            <div className="relative flex-grow">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                
              </div>
              <select
                id="modelId"
                name="modelId"
                value={formData.modelId || ""}
                onChange={handleFormChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                required
                disabled={isLoadingModels}
              >
                <option value="">
                  {isLoadingModels ? "Loading models..." : "Select an AI model"}
                </option>
                {vqaModels.map((model) => (
                  <option key={model.id} value={model.model_id}>
                    {model.model_name} ({model.model_id})
                  </option>
                ))}
              </select>
            </div>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Choose an AI model that supports visual question answering
          </p>
        </div>

          <div>
            <label htmlFor="question" className="block text-sm font-medium text-gray-700 mb-1">
              Question
            </label>
            <div className="flex">
              <div className="relative flex-grow">
                <input
                  type="text"
                  id="question"
                  name="question"
                  value={formData.question}
                  onChange={handleFormChange}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="What is the color of the car?"
                  required
                />
              </div>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              Ask the same question about all images in the folder
            </p>
          </div>

        <div className="pt-2">
            <button
              type="submit"
              disabled={isLoading || isLoadingProjects || isLoadingModels}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300"
            >
              {isLoading ? (
                <>
                  <FaSpinner className="animate-spin mr-2" /> Processing...
                </>
              ) : (
                "Answer Questions from Project"
              )}
            </button>
          </div>
        </form>
    </div>
  );
};

export default VQA;
