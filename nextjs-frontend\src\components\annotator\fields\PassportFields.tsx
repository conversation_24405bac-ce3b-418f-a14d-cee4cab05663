
import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface PassportFieldsProps {
  data: Record<string, string>;
  onChange: (name: string, value: string) => void;
}

export default function PassportFields({ data, onChange }: PassportFieldsProps) {
  const [birthDate, setBirthDate] = useState<Date | null>(null);
  const [issueDate, setIssueDate] = useState<Date | null>(null);
  const [expirationDate, setExpirationDate] = useState<Date | null>(null);

  useEffect(() => {
    const year = data['Date of Birth Year'];
    const month = data['Date of Birth Month'];
    const day = data['Date of Birth Day'];
    if (year && month && day) {
      setBirthDate(new Date(`${year}-${month.padStart(2,'0')}-${day.padStart(2,'0')}`));
    } else {
      setBirthDate(null);
    }
  }, [data]);

  useEffect(() => {
    const year = data['Date of Issue Year'];
    const month = data['Date of Issue Month'];
    const day = data['Date of Issue Day'];
    if (year && month && day) {
      setIssueDate(new Date(`${year}-${month.padStart(2,'0')}-${day.padStart(2,'0')}`));
    } else {
      setIssueDate(null);
    }
  }, [data]);

  useEffect(() => {
    const year = data['Date of Expiration Year'];
    const month = data['Date of Expiration Month'];
    const day = data['Date of Expiration Day'];
    if (year && month && day) {
      setExpirationDate(new Date(`${year}-${month.padStart(2,'0')}-${day.padStart(2,'0')}`));
    } else {
      setExpirationDate(null);
    }
  }, [data]);

  return (
    <div className="space-y-6">
      {/* Link to File */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Link to File</label>
        <input
          type="text"
          name="Link to File"
          value={data['Link to File'] || ''}
          onChange={e => onChange('Link to File', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>

      {/* Passport Country Code / Type / Number */}
      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Passport Country Code</label>
          <input
            type="text"
            name="Passport Country Code"
            maxLength={3}
            value={data['Passport Country Code'] || ''}
            onChange={e => onChange('Passport Country Code', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Passport Type</label>
          <input
            type="text"
            name="Passport Type"
            maxLength={1}
            value={data['Passport Type'] || ''}
            onChange={e => onChange('Passport Type', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Passport Number</label>
          <input
            type="text"
            name="Passport Number"
            value={data['Passport Number'] || ''}
            onChange={e => onChange('Passport Number', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
      </div>

      {/* First / Family Name */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">First Name</label>
          <input
            type="text"
            name="First Name"
            value={data['First Name'] || ''}
            onChange={e => onChange('First Name', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Family Name</label>
          <input
            type="text"
            name="Family Name"
            value={data['Family Name'] || ''}
            onChange={e => onChange('Family Name', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
      </div>

      {/* Date of Birth */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
        <DatePicker
          selected={birthDate}
          onChange={date => {
            const d = date as Date;
            setBirthDate(d);
            if (d) {
              const dd = d.getDate().toString().padStart(2, '0');
              const mm = (d.getMonth() + 1).toString().padStart(2, '0');
              const yyyy = d.getFullYear().toString();
              onChange('Date of Birth Day', dd);
              onChange('Date of Birth Month', mm);
              onChange('Date of Birth Year', yyyy);
            }
          }}
          dateFormat="yyyy-MM-dd"
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          placeholderText="Select date"
        />
      </div>

      {/* Place of Birth / Gender */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Place of Birth</label>
          <input
            type="text"
            name="Place of Birth"
            value={data['Place of Birth'] || ''}
            onChange={e => onChange('Place of Birth', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Gender</label>
          <select
            name="Gender"
            value={data['Gender'] || ''}
            onChange={e => onChange('Gender', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          >
            <option value="">Select</option>
            <option value="M">M</option>
            <option value="F">F</option>
          </select>
        </div>
      </div>

      {/* Date of Issue */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Date of Issue</label>
        <DatePicker
          selected={issueDate}
          onChange={date => {
            const d = date as Date;
            setIssueDate(d);
            if (d) {
              const dd = d.getDate().toString().padStart(2, '0');
              const mm = (d.getMonth() + 1).toString().padStart(2, '0');
              const yyyy = d.getFullYear().toString();
              onChange('Date of Issue Day', dd);
              onChange('Date of Issue Month', mm);
              onChange('Date of Issue Year', yyyy);
            }
          }}
          dateFormat="yyyy-MM-dd"
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          placeholderText="Select date"
        />
      </div>

      {/* Date of Expiration */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Date of Expiration</label>
        <DatePicker
          selected={expirationDate}
          onChange={date => {
            const d = date as Date;
            setExpirationDate(d);
            if (d) {
              const dd = d.getDate().toString().padStart(2, '0');
              const mm = (d.getMonth() + 1).toString().padStart(2, '0');
              const yyyy = d.getFullYear().toString();
              onChange('Date of Expiration Day', dd);
              onChange('Date of Expiration Month', mm);
              onChange('Date of Expiration Year', yyyy);
            }
          }}
          dateFormat="yyyy-MM-dd"
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          placeholderText="Select date"
        />
      </div>

      {/* Authority */}
      <div>
        <label className="block text-sm font-medium text-gray-700">Authority</label>
        <input
          type="text"
          name="Authority"
          value={data['Authority'] || ''}
          onChange={e => onChange('Authority', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2"
        />
      </div>

      {/* Signature / Stamp */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">Signature</label>
          <select
            name="Signature"
            value={data['Signature'] || ''}
            onChange={e => onChange('Signature', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          >
            <option value="">Select Option</option>
            <option value="Yes">Yes</option>
            <option value="No">No</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700">Stamp</label>
          <select
            name="Stamp"
            value={data['Stamp'] || ''}
            onChange={e => onChange('Stamp', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2"
          >
            <option value="">Select Option</option>
            <option value="Yes">Yes</option>
            <option value="No">No</option>
          </select>
        </div>
      </div>

      {/* Dates of Issue & Expiration similar pattern could follow */}
      {/* ... you can continue adding remaining passport fields ... */}
    </div>
  );
} 