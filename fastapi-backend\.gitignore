
__pycache__
logs
*.log
*.log.*
*.log.*.*
*.log.*.*.*
*.log.*.*.*.*
*.log.*.*.*.*.*
*.pyc
*.pyo
*.pyd
*.pyw
*.pyz
.venv
.ven

sessions
pytest.ini
.venv/
database.db
*.db
downloaded_images
db/database
*.db
venv/
services/telegram_service/downloaded_images
app/db/
Local_*.html
convert_*.py
# Migration files - database specific, should not be tracked
# These files contain database-specific configurations and migration history
# that can cause conflicts between different environments and developers
/app/Migration System/alembic/versions/*
app/migrations/versions/*


app/migrations/*/versions/*.py
app/sqllite-db/

# Processed AI files - these contain results from AI processing and should not be tracked
*/backups/*
