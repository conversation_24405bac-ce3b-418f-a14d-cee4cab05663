"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaBook, FaSync, FaExclamationTriangle } from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";
import { API_BASE_URL } from "@/lib/api";

interface ModelInfo {
  id: string;
  name: string;
  description: string;
}

interface KnowledgeEntry {
  id: number;
  title: string;
  topic: string;
  contentPreview: string;
  source?: string;
}

interface ReferenceSyntheticDataFormProps {
  userQuery: string;
  setUserQuery: (query: string) => void;
  datasetType: string;
  setDatasetType: (type: string) => void;
  numSamples: number;
  setNumSamples: (num: number) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  availableModels: ModelInfo[];
  datasetTypes: Record<string, string>;
  onGenerate: () => void;
  isGenerating: boolean;
  selectedKnowledgeEntry: number | null;
  setSelectedKnowledgeEntry: (id: number | null) => void;
}

export default function ReferenceSyntheticDataForm({
  userQuery,
  setUserQuery,
  datasetType,
  setDatasetType,
  numSamples,
  setNumSamples,
  selectedModel,
  setSelectedModel,
  availableModels,
  datasetTypes,
  onGenerate,
  isGenerating,
  selectedKnowledgeEntry,
  setSelectedKnowledgeEntry,
}: ReferenceSyntheticDataFormProps) {
  const [knowledgeEntries, setKnowledgeEntries] = useState<KnowledgeEntry[]>(
    []
  );
  const [topicFilter, setTopicFilter] = useState<string>("");
  const [topics, setTopics] = useState<string[]>([]);
  const [isLoadingKnowledge, setIsLoadingKnowledge] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);

  // Fetch topics from knowledge base
  const fetchTopics = useCallback(async () => {
    try {
      setFetchError(null);
      const response = await authFetch(`${API_BASE_URL}/knowledge-base/topics`, {
        credentials: "include",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTopics(data);
      } else {
        console.error("Failed to fetch topics:", response.status);
        setFetchError(
          "Failed to fetch topics. API may be unreachable or CORS issue."
        );
      }
    } catch (error) {
      console.error("Error fetching topics:", error);
      setFetchError(
        "Error fetching topics. Please check network connection and API status."
      );
    }
  }, []);

  // Fetch knowledge entries with optional topic filter
  const fetchKnowledgeEntries = useCallback(
    async (topic?: string) => {
      setIsLoadingKnowledge(true);
      try {
        setFetchError(null);
        const url = topic
          ? `${API_BASE_URL}/synthetic-dataset/knowledge-entries?topic=${encodeURIComponent(
              topic
            )}`
          : `${API_BASE_URL}/synthetic-dataset/knowledge-entries`;

        const response = await authFetch(url, {
          credentials: "include",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          setKnowledgeEntries(data);

          // Select the first entry by default if available and none is currently selected
          if (data.length > 0 && selectedKnowledgeEntry === null) {
            setSelectedKnowledgeEntry(data[0].id);
          }
        } else {
          console.error("Failed to fetch knowledge entries:", response.status);
          setFetchError(
            "Failed to fetch knowledge entries. API may be unreachable or CORS issue."
          );
        }
      } catch (error) {
        console.error("Error fetching knowledge entries:", error);
        setFetchError(
          "Error fetching knowledge entries. Please check network connection and API status."
        );
      } finally {
        setIsLoadingKnowledge(false);
      }
    },
    [selectedKnowledgeEntry, setSelectedKnowledgeEntry]
  );

  // Update knowledge entries when topic filter changes
  useEffect(() => {
    fetchKnowledgeEntries(topicFilter || undefined);
  }, [topicFilter, fetchKnowledgeEntries]);

  // Fetch topics on component mount
  useEffect(() => {
    fetchTopics();
    fetchKnowledgeEntries();
    // Only run on mount, so we don't add fetchTopics/fetchKnowledgeEntries as dependencies here
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

 
  // commented this out as it was not used in the original code
  // If you need to use it later, you can uncomment and modify as needed
  // Uncomment this if you want to use dataset type icons

  // const getDatasetTypeIcon = (type: string) => {
  //   switch (type) {
  //     case "qa":
  //       return <div className="text-blue-500">Q&A</div>;
  //     case "articles":
  //       return <div className="text-green-500">Articles</div>;
  //     case "conversation":
  //       return <div className="text-purple-500">Conversations</div>;
  //     case "code_snippets":
  //       return <div className="text-amber-500">Code Snippets</div>;
  //     default:
  //       return <div className="text-gray-500">Data</div>;
  //   }
  // };

  const handleRefresh = () => {
    fetchTopics();
    fetchKnowledgeEntries(topicFilter || undefined);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="space-y-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">
            What would you like to generate?
          </label>
          <textarea
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            rows={3}
            value={userQuery}
            onChange={(e) => setUserQuery(e.target.value)}
            placeholder="Enter your query or topic here..."
          />
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            Dataset Type
          </label>
          <div className="relative">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
              value={datasetType}
              onChange={(e) => setDatasetType(e.target.value)}
            >
              {Object.entries(datasetTypes).map(([type, description]) => (
                <option key={type} value={type}>
                  {description}
                </option>
              ))}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"></div>
          </div>
        </div>

        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-gray-700 font-medium">
              Knowledge Base Reference
            </label>
            <button
              onClick={handleRefresh}
              className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
              disabled={isLoadingKnowledge}
            >
              <FaSync
                className={`mr-1 ${isLoadingKnowledge ? "animate-spin" : ""}`}
              />
              Refresh
            </button>
          </div>

          {fetchError && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 flex items-start">
              <FaExclamationTriangle className="flex-shrink-0 mr-2 mt-0.5" />
              <div className="text-sm">
                {fetchError}
                <p className="mt-1">
                  We&apos;ll use default entries if available or simulate the
                  experience.
                </p>
              </div>
            </div>
          )}

          <div className="space-y-3">
            <div className="relative">
              <select
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
                value={topicFilter}
                onChange={(e) => setTopicFilter(e.target.value)}
              >
                <option value="">All Topics</option>
                {topics.map((topic) => (
                  <option key={topic} value={topic}>
                    {topic}
                  </option>
                ))}
              </select>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"></div>
            </div>

            <div className="border border-gray-200 rounded-lg max-h-60 overflow-y-auto">
              {isLoadingKnowledge ? (
                <div className="p-4 text-center text-gray-500">
                  <FaSync className="animate-spin inline-block mr-2" />
                  Loading knowledge entries...
                </div>
              ) : knowledgeEntries.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {fetchError
                    ? "Failed to load entries"
                    : "No knowledge entries available"}
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {knowledgeEntries.map((entry) => (
                    <div
                      key={entry.id}
                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
                        selectedKnowledgeEntry === entry.id
                          ? "bg-blue-50 border-l-4 border-blue-500"
                          : ""
                      }`}
                      onClick={() => setSelectedKnowledgeEntry(entry.id)}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mt-1">
                          <FaBook
                            className={`${
                              selectedKnowledgeEntry === entry.id
                                ? "text-blue-500"
                                : "text-gray-400"
                            }`}
                          />
                        </div>
                        <div className="ml-3">
                          <div
                            className={`font-medium ${
                              selectedKnowledgeEntry === entry.id
                                ? "text-blue-700"
                                : ""
                            }`}
                          >
                            {entry.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            Topic: {entry.topic}
                          </div>
                          <div className="text-xs text-gray-400 mt-1 line-clamp-2">
                            {entry.contentPreview}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            {!isLoadingKnowledge &&
              knowledgeEntries.length > 0 &&
              selectedKnowledgeEntry === null && (
                <div className="text-sm text-red-500 mt-1">
                  Please select a knowledge base entry to continue
                </div>
              )}
          </div>
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            Number of Samples
          </label>
          <input
            type="number"
            min={1}
            max={10}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            value={numSamples}
            onChange={(e) => setNumSamples(parseInt(e.target.value) || 3)}
          />
          <p className="text-sm text-gray-500 mt-1">
            Recommended: 1-5 samples. Larger values may take longer to generate.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">
            Select Model
          </label>
          <div className="space-y-3">
            {availableModels.map((model) => (
              <div key={model.id} className="flex items-start">
                <input
                  type="radio"
                  id={`model-${model.id}`}
                  name="model"
                  value={model.id}
                  checked={selectedModel === model.id}
                  onChange={() => setSelectedModel(model.id)}
                  className="mt-1"
                />
                <label htmlFor={`model-${model.id}`} className="ml-2">
                  <div className="font-medium">{model.name}</div>
                  <div className="text-sm text-gray-500">
                    {model.description}
                  </div>
                </label>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-center mt-auto pt-6">
          <button
            className="flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:bg-gray-300 disabled:cursor-not-allowed"
            onClick={onGenerate}
            disabled={isGenerating || !userQuery || !selectedKnowledgeEntry}
          >
            {isGenerating ? (
              <>
                <FaSync className="animate-spin mr-2" />
                Generating...
              </>
            ) : (
              <>
                <FaRobot className="mr-2" />
                Generate Dataset
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

// "use client";

// import { useState, useEffect } from "react";
// import { FaRobot, FaBook, FaSync, FaExclamationTriangle } from "react-icons/fa";
// import { authFetch } from "@/lib/authFetch";

// const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000/api";

// interface ModelInfo {
//   id: string;
//   name: string;
//   description: string;
// }

// interface KnowledgeEntry {
//   id: number;
//   title: string;
//   topic: string;
//   contentPreview: string;
//   source?: string;
// }

// interface ReferenceSyntheticDataFormProps {
//   userQuery: string;
//   setUserQuery: (query: string) => void;
//   datasetType: string;
//   setDatasetType: (type: string) => void;
//   numSamples: number;
//   setNumSamples: (num: number) => void;
//   selectedModel: string;
//   setSelectedModel: (model: string) => void;
//   availableModels: ModelInfo[];
//   datasetTypes: Record<string, string>;
//   onGenerate: () => void;
//   isGenerating: boolean;
//   selectedKnowledgeEntry: number | null;
//   setSelectedKnowledgeEntry: (id: number | null) => void;
// }

// export default function ReferenceSyntheticDataForm({
//   userQuery,
//   setUserQuery,
//   datasetType,
//   setDatasetType,
//   numSamples,
//   setNumSamples,
//   selectedModel,
//   setSelectedModel,
//   availableModels,
//   datasetTypes,
//   onGenerate,
//   isGenerating,
//   selectedKnowledgeEntry,
//   setSelectedKnowledgeEntry,
// }: ReferenceSyntheticDataFormProps) {
//   const [knowledgeEntries, setKnowledgeEntries] = useState<KnowledgeEntry[]>(
//     []
//   );
//   const [topicFilter, setTopicFilter] = useState<string>("");
//   const [topics, setTopics] = useState<string[]>([]);
//   const [isLoadingKnowledge, setIsLoadingKnowledge] = useState(false);
//   const [fetchError, setFetchError] = useState<string | null>(null);

//   // Fetch topics from knowledge base
//   const fetchTopics = async () => {
//     try {
//       setFetchError(null);
//       const response = await authFetch(`${API_URL}/knowledge-base/topics`, {
//         credentials: "include",
//         headers: {
//           Accept: "application/json",
//           "Content-Type": "application/json",
//         },
//       });

//       if (response.ok) {
//         const data = await response.json();
//         setTopics(data);
//       } else {
//         console.error("Failed to fetch topics:", response.status);
//         setFetchError(
//           "Failed to fetch topics. API may be unreachable or CORS issue."
//         );
//       }
//     } catch (error) {
//       console.error("Error fetching topics:", error);
//       setFetchError(
//         "Error fetching topics. Please check network connection and API status."
//       );
//     }
//   };

//   // Fetch knowledge entries with optional topic filter
//   const fetchKnowledgeEntries = async (topic?: string) => {
//     setIsLoadingKnowledge(true);
//     try {
//       setFetchError(null);
//       const url = topic
//         ? `${API_URL}/synthetic-dataset/knowledge-entries?topic=${encodeURIComponent(
//             topic
//           )}`
//         : `${API_URL}/synthetic-dataset/knowledge-entries`;

//       const response = await authFetch(url, {
//         credentials: "include",
//         headers: {
//           Accept: "application/json",
//           "Content-Type": "application/json",
//         },
//       });

//       if (response.ok) {
//         const data = await response.json();
//         setKnowledgeEntries(data);

//         // Select the first entry by default if available and none is currently selected
//         if (data.length > 0 && selectedKnowledgeEntry === null) {
//           setSelectedKnowledgeEntry(data[0].id);
//         }
//       } else {
//         console.error("Failed to fetch knowledge entries:", response.status);
//         setFetchError(
//           "Failed to fetch knowledge entries. API may be unreachable or CORS issue."
//         );
//       }
//     } catch (error) {
//       console.error("Error fetching knowledge entries:", error);
//       setFetchError(
//         "Error fetching knowledge entries. Please check network connection and API status."
//       );
//     } finally {
//       setIsLoadingKnowledge(false);
//     }
//   };

//   // Update knowledge entries when topic filter changes
//   useEffect(() => {
//     fetchKnowledgeEntries(topicFilter || undefined);
//   }, [topicFilter, setSelectedKnowledgeEntry]);

//   // Fetch topics on component mount
//   useEffect(() => {
//     fetchTopics();
//     fetchKnowledgeEntries();
//   }, [setSelectedKnowledgeEntry]);

//   // Get the dataset type icon
//   const getDatasetTypeIcon = (type: string) => {
//     switch (type) {
//       case "qa":
//         return <div className="text-blue-500">Q&A</div>;
//       case "articles":
//         return <div className="text-green-500">Articles</div>;
//       case "conversation":
//         return <div className="text-purple-500">Conversations</div>;
//       case "code_snippets":
//         return <div className="text-amber-500">Code Snippets</div>;
//       default:
//         return <div className="text-gray-500">Data</div>;
//     }
//   };

//   const handleRefresh = () => {
//     fetchTopics();
//     fetchKnowledgeEntries(topicFilter || undefined);
//   };

//   return (
//     <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
//       <div className="space-y-6">
//         <div>
//           <label className="block text-gray-700 font-medium mb-2">
//             What would you like to generate?
//           </label>
//           <textarea
//             className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
//             rows={3}
//             value={userQuery}
//             onChange={(e) => setUserQuery(e.target.value)}
//             placeholder="Enter your query or topic here..."
//           />
//         </div>

//         <div>
//           <label className="block text-gray-700 font-medium mb-2">
//             Dataset Type
//           </label>
//           <div className="relative">
//             <select
//               className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
//               value={datasetType}
//               onChange={(e) => setDatasetType(e.target.value)}
//             >
//               {Object.entries(datasetTypes).map(([type, description]) => (
//                 <option key={type} value={type}>
//                   {description}
//                 </option>
//               ))}
//             </select>
//             <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"></div>
//           </div>
//         </div>

//         <div>
//           <div className="flex justify-between items-center mb-2">
//             <label className="block text-gray-700 font-medium">
//               Knowledge Base Reference
//             </label>
//             <button
//               onClick={handleRefresh}
//               className="text-blue-500 hover:text-blue-700 flex items-center text-sm"
//               disabled={isLoadingKnowledge}
//             >
//               <FaSync
//                 className={`mr-1 ${isLoadingKnowledge ? "animate-spin" : ""}`}
//               />
//               Refresh
//             </button>
//           </div>

//           {fetchError && (
//             <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800 flex items-start">
//               <FaExclamationTriangle className="flex-shrink-0 mr-2 mt-0.5" />
//               <div className="text-sm">
//                 {fetchError}
//                 <p className="mt-1">
//                   We&apos;ll use default entries if available or simulate the
//                   experience.
//                 </p>
//               </div>
//             </div>
//           )}

//           <div className="space-y-3">
//             <div className="relative">
//               <select
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
//                 value={topicFilter}
//                 onChange={(e) => setTopicFilter(e.target.value)}
//               >
//                 <option value="">All Topics</option>
//                 {topics.map((topic) => (
//                   <option key={topic} value={topic}>
//                     {topic}
//                   </option>
//                 ))}
//               </select>
//               <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"></div>
//             </div>

//             <div className="border border-gray-200 rounded-lg max-h-60 overflow-y-auto">
//               {isLoadingKnowledge ? (
//                 <div className="p-4 text-center text-gray-500">
//                   <FaSync className="animate-spin inline-block mr-2" />
//                   Loading knowledge entries...
//                 </div>
//               ) : knowledgeEntries.length === 0 ? (
//                 <div className="p-4 text-center text-gray-500">
//                   {fetchError
//                     ? "Failed to load entries"
//                     : "No knowledge entries available"}
//                 </div>
//               ) : (
//                 <div className="divide-y divide-gray-200">
//                   {knowledgeEntries.map((entry) => (
//                     <div
//                       key={entry.id}
//                       className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
//                         selectedKnowledgeEntry === entry.id
//                           ? "bg-blue-50 border-l-4 border-blue-500"
//                           : ""
//                       }`}
//                       onClick={() => setSelectedKnowledgeEntry(entry.id)}
//                     >
//                       <div className="flex items-start">
//                         <div className="flex-shrink-0 mt-1">
//                           <FaBook
//                             className={`${
//                               selectedKnowledgeEntry === entry.id
//                                 ? "text-blue-500"
//                                 : "text-gray-400"
//                             }`}
//                           />
//                         </div>
//                         <div className="ml-3">
//                           <div
//                             className={`font-medium ${
//                               selectedKnowledgeEntry === entry.id
//                                 ? "text-blue-700"
//                                 : ""
//                             }`}
//                           >
//                             {entry.title}
//                           </div>
//                           <div className="text-sm text-gray-500">
//                             Topic: {entry.topic}
//                           </div>
//                           <div className="text-xs text-gray-400 mt-1 line-clamp-2">
//                             {entry.contentPreview}
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   ))}
//                 </div>
//               )}
//             </div>
//             {!isLoadingKnowledge &&
//               knowledgeEntries.length > 0 &&
//               selectedKnowledgeEntry === null && (
//                 <div className="text-sm text-red-500 mt-1">
//                   Please select a knowledge base entry to continue
//                 </div>
//               )}
//           </div>
//         </div>

//         <div>
//           <label className="block text-gray-700 font-medium mb-2">
//             Number of Samples
//           </label>
//           <input
//             type="number"
//             min={1}
//             max={10}
//             className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
//             value={numSamples}
//             onChange={(e) => setNumSamples(parseInt(e.target.value) || 3)}
//           />
//           <p className="text-sm text-gray-500 mt-1">
//             Recommended: 1-5 samples. Larger values may take longer to generate.
//           </p>
//         </div>
//       </div>

//       <div className="space-y-6">
//         <div>
//           <label className="block text-gray-700 font-medium mb-2">
//             Select Model
//           </label>
//           <div className="space-y-3">
//             {availableModels.map((model) => (
//               <div key={model.id} className="flex items-start">
//                 <input
//                   type="radio"
//                   id={`model-${model.id}`}
//                   name="model"
//                   value={model.id}
//                   checked={selectedModel === model.id}
//                   onChange={() => setSelectedModel(model.id)}
//                   className="mt-1"
//                 />
//                 <label htmlFor={`model-${model.id}`} className="ml-2">
//                   <div className="font-medium">{model.name}</div>
//                   <div className="text-sm text-gray-500">
//                     {model.description}
//                   </div>
//                 </label>
//               </div>
//             ))}
//           </div>
//         </div>

//         <div className="flex justify-center mt-auto pt-6">
//           <button
//             className="flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:bg-gray-300 disabled:cursor-not-allowed"
//             onClick={onGenerate}
//             disabled={isGenerating || !userQuery || !selectedKnowledgeEntry}
//           >
//             {isGenerating ? (
//               <>
//                 <FaSync className="animate-spin mr-2" />
//                 Generating...
//               </>
//             ) : (
//               <>
//                 <FaRobot className="mr-2" />
//                 Generate Dataset
//               </>
//             )}
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// }
