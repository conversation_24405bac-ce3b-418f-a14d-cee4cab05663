from fastapi import APIRouter, Depends, HTTPException, status # type: ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from core.session_manager import get_master_db_context, get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.admin_settings import AdminSettings
from schemas.UserSchemas import SuccessResponse
from schemas.AdminSettingsSchemas import AdminInstruction, InstructionsRequest
from typing import Optional
import logging

logger = logging.getLogger('admin_instructions')

router = APIRouter(prefix="/admin", tags=["Admin Instructions"])

#................................. Edit Instructions .................................
@router.get("/edit-instructions", response_model=AdminInstruction)
async def get_edit_instructions(mode: Optional[str] = None, dataset: Optional[str] = None, db: AsyncSession = Depends(get_master_db_session)):
    """
    Get instructions for supervision mode or project-specific instructions.
    """
    if mode == "supervision":
        result = await db.execute(select(AdminSettings).limit(1))
        settings_obj = result.scalar_one_or_none()  # Use LIMIT 1 to handle multiple records gracefully
        if not settings_obj:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Supervision instructions not found")
        return AdminInstruction(id=settings_obj.id, mode="supervision", instructions=settings_obj.supervision_instructions or "")
    
    if mode and dataset is not None:
        # Get instructions from projects_registry using project_code string
        try:
            async with get_master_db_context() as master_session:
                project_result = await master_session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code == dataset)
                )
                project = project_result.scalar_one_or_none()
                if not project:
                    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Project with code '{dataset}' not found")
                
                return AdminInstruction(
                    id=project.id, 
                    mode=mode, 
                    instructions=project.instructions or ""
                )
        except Exception as e:
            logger.error(f"Error getting instructions from projects_registry: {e}")
            raise HTTPException(status_code=500, detail="Failed to get instructions")
    
    raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Mode and dataset parameters are required")

@router.post("/edit-instructions", response_model=SuccessResponse)
async def edit_instructions(req: InstructionsRequest, db: AsyncSession = Depends(get_master_db_session)):
    """
    Save instructions for supervision mode or dataset-specific instructions.
    """
    if req.mode == "supervision":
        result = await db.execute(select(AdminSettings).limit(1))
        settings_obj = result.scalar_one_or_none()
        if not settings_obj:
            settings_obj = AdminSettings()
            db.add(settings_obj)
            await db.flush()
        settings_obj.supervision_instructions = req.instructions
        await db.commit()
        return SuccessResponse(success=True, message="Supervision instructions saved.")
    if req.dataset is None:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Project code is required for dataset instructions")
    
    # Work directly with projects_registry using project_code string
    try:
        async with get_master_db_context() as master_session:
            # Find project by project_code in projects_registry
            project_result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == req.dataset)
            )
            project = project_result.scalar_one_or_none()
            if not project:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Project with code '{req.dataset}' not found")
            
            project.instructions = req.instructions or ""
            await master_session.commit()
            logger.info(f"Updated instructions in projects_registry for project {project.project_code}")
            
    except Exception as e:
        logger.error(f"Error updating instructions in projects_registry: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update instructions")
    
    return SuccessResponse(success=True, message=f'Instructions for project "{project.project_name} ({project.project_code})" saved.')
