import React from 'react';
import {
  FaTimes,
  FaInfoCircle,
  FaFile,
  FaBook,
  FaUsers,
  FaDatabase,
  FaCalendarAlt,
} from 'react-icons/fa';
import { ProjectRegistryResponse, AnnotationField } from '../types';
import { getStatusColor, formatDate, calculateProgress } from '../utils';

interface ProjectDetailsModalProps {
  project: ProjectRegistryResponse;
  isOpen: boolean;
  onClose: () => void;
}

export const ProjectDetailsModal: React.FC<ProjectDetailsModalProps> = ({
  project,
  isOpen,
  onClose,
}) => {
  if (!isOpen || !project) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {project.project_name}
              </h2>
              <p className="text-gray-600">Client name: {project.client?.name}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <FaTimes className="text-xl" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Project Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaInfoCircle className="mr-2" />
                  Project Overview
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Project Type</label>
                    <p className="text-sm text-gray-900 capitalize">{project.project_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <p className="text-sm">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.project_status)}`}>
                        {project.project_status}
                      </span>
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Priority Level</label>
                    <p className="text-sm text-gray-900">{project.priority_level}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  Progress & Statistics
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Files Progress</label>
                    <p className="text-sm text-gray-900">
                      {project.completed_files} / {project.total_files} completed
                    </p>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div
                        className="bg-[#0052CC] h-2 rounded-full"
                        style={{
                          width: `${calculateProgress(project.completed_files, project.total_files)}%`
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Total Batches</label>
                      <p className="text-sm text-gray-900">{project.total_batches}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Active Annotators</label>
                      <p className="text-sm text-gray-900">{project.active_annotators}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Annotation Requirements */}
            {project.annotation_requirements && (
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaFile className="mr-2" />
                  Annotation Requirements
                </h3>
                {project.annotation_requirements.form_config && project.annotation_requirements.form_config.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {project.annotation_requirements.form_config.map((field: AnnotationField, index: number) => (
                      <div key={index} className="border rounded-lg p-4 bg-gray-50">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm text-gray-900">{field.label}</h4>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            field.required
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-600'
                          }`}>
                            {field.required ? 'Required' : 'Optional'}
                          </span>
                        </div>
                        <div className="space-y-1 text-xs text-gray-600">
                          <div><strong>Field Name:</strong> {field.field_name}</div>
                          <div><strong>Type:</strong> {field.field_type.replace('_', ' ')}</div>
                          {field.max_length && <div><strong>Max Length:</strong> {field.max_length}</div>}
                          {field.options && field.options.length > 0 && (
                            <div>
                              <strong>Options:</strong>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {field.options.map((option, optIndex) => (
                                  <span key={optIndex} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                                    {option}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No annotation requirements configured</p>
                )}
              </div>
            )}

            {/* Instructions */}
            {project.instructions && (
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaBook className="mr-2" />
                  Instructions
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">{project.instructions}</p>
                </div>
              </div>
            )}

            {/* Allocation Strategy */}
            {project.allocation_strategy && (
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaUsers className="mr-2" />
                  Allocation Strategy
                </h3>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="space-y-2">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Strategy Name</label>
                      <p className="text-sm text-gray-900 font-medium">{project.allocation_strategy.strategy_name}</p>
                    </div>
                    {project.allocation_strategy.strategy_type && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Strategy Type</label>
                        <p className="text-sm text-gray-900">{project.allocation_strategy.strategy_type}</p>
                      </div>
                    )}
                    {project.allocation_strategy.description && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Description</label>
                        <p className="text-sm text-gray-900">{project.allocation_strategy.description}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Database Information and Timeline */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaDatabase className="mr-2" />
                  Database Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Database Name</label>
                    <p className="text-sm text-gray-900">{project.database_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Host</label>
                    <p className="text-sm text-gray-900">{project.database_host}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Port</label>
                    <p className="text-sm text-gray-900">{project.database_port}</p>
                  </div>
                  {project.folder_path && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Folder Path</label>
                      <p className="text-sm text-gray-900">{project.folder_path}</p>
                    </div>
                  )}
                  
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <FaCalendarAlt className="mr-2" />
                  Timeline
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Created At</label>
                    <p className="text-sm text-gray-900">{formatDate(project.created_at)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Updated At</label>
                    <p className="text-sm text-gray-900">{formatDate(project.updated_at)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Deadline</label>
                    <p className="text-sm text-gray-900">{formatDate(project.project_deadline)}</p>
                  </div>
                  {project.batch_size && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Batch Size</label>
                      <p className="text-sm text-gray-900">{project.batch_size}</p>
                    </div>
                  )}
                  
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
