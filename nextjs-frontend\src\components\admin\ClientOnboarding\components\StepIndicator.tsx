// components/StepIndicator.tsx
import React from 'react';
import { FaCheckCircle } from 'react-icons/fa';
import { OnboardingStep } from '../types';

interface StepIndicatorProps {
  steps: OnboardingStep[];
  currentStep: number;
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ steps, currentStep }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div
              className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                step.completed
                  ? "bg-green-500 border-green-500 text-white"
                  : currentStep === step.id
                  ? "border-blue-500 text-blue-500 bg-white"
                  : "border-gray-300 text-gray-300 bg-white"
              }`}
            >
              {step.completed ? <FaCheckCircle /> : step.id}
            </div>
            <div className="ml-3 text-sm">
              <div
                className={`font-medium ${
                  step.completed
                    ? "text-green-600"
                    : currentStep === step.id
                    ? "text-blue-600"
                    : "text-gray-400"
                }`}
              >
                {step.title}
              </div>
              <div className="text-gray-500">{step.description}</div>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`flex-1 h-0.5 mx-4 ${
                  step.completed ? "bg-green-500" : "bg-gray-300"
                }`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};