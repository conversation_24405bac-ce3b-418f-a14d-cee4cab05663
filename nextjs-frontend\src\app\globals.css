@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: 'Poppins', Georgia, serif;
  --primary-color: #3566c5;
  --secondary-color: #1a3b5d;
  --text-primary: #1a3b5d;
  --text-secondary: #4a6385;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-text-primary bg-white overflow-x-hidden;
  }
  
  a {
    @apply text-current no-underline transition-all duration-300;
  }
  
  section {
    @apply py-24 relative overflow-hidden;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 md:px-6;
  }
  
  .section-title {
    @apply text-4xl text-center mb-10 text-secondary font-bold relative;
  }
  
  .section-title:after {
    content: '';
    @apply block w-20 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-3 rounded-sm;
  }
  
  .background-gradient {
    @apply fixed inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-50 -z-10;
  }
  
  .btn-primary {
    @apply inline-flex items-center px-6 py-3 rounded-lg bg-blue-700 text-white font-bold hover:bg-blue-800 transition-all duration-300;
  }
  
  .btn-secondary {
    @apply inline-flex items-center px-6 py-3 rounded-lg bg-white text-blue-700 border border-blue-700 font-bold hover:bg-gray-100 transition-all duration-300;
  }
  
  .btn-tertiary {
    @apply inline-flex items-center px-4 py-2 rounded-lg bg-white text-gray-700 border border-gray-300 font-medium text-sm hover:bg-gray-100 transition-all duration-300;
  }
  
  .feature-card {
    @apply bg-white/90 rounded-xl p-6 shadow-md transition-all duration-300 relative overflow-hidden;
  }
  
  .feature-card:hover {
    @apply shadow-lg -translate-y-1;
  }
  
  .feature-icon {
    @apply flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-primary mb-4 relative;
  }
  
  .shape {
    @apply absolute rounded-full opacity-70 animate-float -z-10;
  }
  
  .shape-1 {
    @apply top-[15%] left-[10%] w-64 h-64 bg-blue-100;
  }
  
  .shape-2 {
    @apply top-[25%] right-[15%] w-48 h-48 bg-green-100 opacity-60;
  }
  
  .shape-3 {
    @apply bottom-[20%] left-[20%] w-56 h-56 bg-purple-100 opacity-60;
  }
  
  .shape-4 {
    @apply bottom-[30%] right-[10%] w-40 h-40 bg-yellow-100 opacity-60;
  }
  
  .shape-5 {
    @apply top-[45%] left-[40%] w-32 h-32 bg-red-100 opacity-40;
  }
  
  /* Admin Dashboard Styles */
  .admin-dashboard-title {
    @apply text-3xl font-bold text-center mb-10 relative;
  }
  
  .admin-dashboard-title::after {
    content: '';
    @apply block w-32 h-1 bg-blue-600 mx-auto mt-2;
  }
  
  .admin-card {
    @apply bg-white rounded-lg shadow-md transition-all duration-300 h-full overflow-hidden;
  }
  
  .admin-card:hover {
    @apply shadow-lg;
  }
  
  .admin-card-header {
    @apply p-4 border-b border-gray-100 flex items-center;
  }
  
  .admin-card-body {
    @apply p-5;
  }
  
  .admin-card-footer {
    @apply p-4 bg-gray-50 border-t border-gray-100;
  }
  
  .admin-icon {
    @apply text-4xl mb-3;
  }
  
  .admin-icon-primary {
    @apply text-blue-600;
  }
  
  .admin-icon-success {
    @apply text-green-600;
  }
  
  .admin-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .admin-badge-primary {
    @apply bg-blue-100 text-blue-800;
  }
  
  .admin-badge-success {
    @apply bg-green-100 text-green-800;
  }
  
  .folder-input-container {
    @apply border rounded-lg p-4 bg-gray-50;
  }
  
  /* Background decorations */
  .noise-texture {
    @apply absolute inset-0 pointer-events-none opacity-10;
    /* Removed background-image to avoid missing resource 404 */
  }
  .background-grid {
    @apply absolute inset-0 pointer-events-none;
    background-image: repeating-linear-gradient(90deg, rgba(0,0,0,0.02) 1px, transparent 1px 20px);
  }
  .floating-shapes {
    @apply absolute inset-0 pointer-events-none;
  }
  /* Disabled button style */
  .btn-disabled {
    @apply opacity-50 cursor-not-allowed;
  }
  
  /* Animation override for synthetic home page */
  .animate-fade-in,
  .opacity-0,
  .translate-y-4,
  .transition-transform,
  .duration-700,
  .ease-out,
  [class*="delay-"] {
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
  }
}

