"""
Admin routes for MinIO connection pool monitoring and management
"""
from fastapi import APIRouter, Depends, HTTPException # type: ignore
from typing import Dict, Any
import logging
from dependencies.auth import get_current_active_user

router = APIRouter(prefix="/admin", tags=["admin-minio-pool"])
logger = logging.getLogger(__name__)

@router.get("/minio-pool/stats")
async def get_minio_pool_stats(current_user: dict = Depends(get_current_active_user)) -> Dict[str, Any]:
    """Get MinIO connection pool statistics"""
    try:
        from core.minio_pool import get_connection_pool
        pool = await get_connection_pool()
        stats = await pool.get_pool_stats()
        
        # Calculate totals
        total_connections = sum(project_stats["total"] for project_stats in stats.values())
        total_active = sum(project_stats["active"] for project_stats in stats.values())
        total_idle = sum(project_stats["idle"] for project_stats in stats.values())
        total_expired = sum(project_stats["expired"] for project_stats in stats.values())
        
        return {
            "pool_stats": stats,
            "summary": {
                "total_connections": total_connections,
                "total_active": total_active,
                "total_idle": total_idle,
                "total_expired": total_expired,
                "projects_with_connections": len(stats)
            }
        }
    except Exception as e:
        logger.error(f"Error getting MinIO pool stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get pool stats: {str(e)}")

@router.post("/minio-pool/cleanup")
async def cleanup_minio_pool(current_user: dict = Depends(get_current_active_user)) -> Dict[str, str]:
    """Manually trigger MinIO connection pool cleanup"""
    try:
        from core.minio_pool import get_connection_pool
        pool = await get_connection_pool()
        await pool._cleanup_expired_connections()
        
        return {"message": "MinIO connection pool cleanup completed successfully"}
    except Exception as e:
        logger.error(f"Error cleaning up MinIO pool: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cleanup pool: {str(e)}")
