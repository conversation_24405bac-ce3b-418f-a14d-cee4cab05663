import asyncio
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from telethon import Telegram<PERSON>lient
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError
from telethon.tl.types import Channel, Chat
import base64
import re
from telethon.sessions import StringSession
from fastapi import HTT<PERSON>Exception
from .telegram_image_handler import ImageHandler

telegram_clients: Dict[str, Optional[TelegramClient]] = {}

class TelegramManager:
    _instance = None
    _lock = threading.Lock()
    _loop = None
    _thread = None

    @classmethod
    def get_instance(cls) -> 'TelegramManager':
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    def __init__(self):
        self.start_background_loop()

    def start_background_loop(self) -> None:
        if self._loop is not None:
            return

        def run_loop():
            try:
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)
                self._loop.run_forever()
            except Exception:
                self._loop = None

        self._thread = threading.Thread(target=run_loop, daemon=True)
        self._thread.start()

    def get_event_loop(self) -> asyncio.AbstractEventLoop:
        if self._loop is None:
            self.start_background_loop()
        return self._loop

    def run_coroutine(self, coro: Any) -> Any:
        try:
            if self._loop is None:
                self.start_background_loop()

            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            future = asyncio.run_coroutine_threadsafe(coro, self._loop)
            return future.result()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def get_client(self, session_name: str = 'test_session') -> Optional[TelegramClient]:
        if session_name not in telegram_clients:
            telegram_clients[session_name] = None
        return telegram_clients[session_name]

    def set_client(self, session_name: str, client: TelegramClient) -> None:
        telegram_clients[session_name] = client

    def remove_client(self, session_name: str) -> None:
        if session_name in telegram_clients:
            del telegram_clients[session_name]

# Initialize TelegramManager
telegram_manager = TelegramManager.get_instance()

async def connect_to_telegram(api_id: int, api_hash: str, phone: Optional[str] = None, session_str: Optional[str] = None) -> Dict[str, Any]:
    """Connect to Telegram with the given API credentials"""
    try:
        sess = StringSession(session_str) if session_str else StringSession()
        client = TelegramClient(
            sess,
            api_id,
            api_hash,
            loop=telegram_manager.get_event_loop()
        )
        
        await client.connect()

        # Save session after connection is established
        session_key = sess.save()
        
        if phone and not await client.is_user_authorized():
            await client.send_code_request(phone)
            # Save session again after code request (this populates the session with auth data)
            session_key = sess.save()
            # Store the client with the session key so verify_code can use the same client
            telegram_manager.set_client(session_key, client)
            
            result = {
                'status': 'code_sent',
                'message': 'Verification code sent to your phone',
                'session_str': session_key
            }
            return result

        if await client.is_user_authorized():
            me = await client.get_me()
            username = me.username or me.first_name
            
            # Save session for authorized user
            session_key = sess.save()
            print(f"Session key for authorized user: {session_key[:50]}... (length: {len(session_key)})")
            
            # Store the client
            telegram_manager.set_client(session_key, client)

            result = {
                'status': 'connected',
                'message': f'Connected to Telegram as {username}',
                'username': username,
                'session_str': session_key
            }
            return result
        else:

            return {
                'status': 'phone_required',
                'message': 'Please enter your phone number'
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def verify_code(code: str, session_str: Optional[str] = None) -> Dict[str, Any]:
    """Verify the login code sent to the user's phone"""
    try:
        client = await get_or_create_client(session_str)
        if not client:
            raise HTTPException(status_code=400, detail="No active Telegram connection")

        if not client.is_connected():
            await client.connect()
        
        await client.sign_in(code=code)
        me = await client.get_me()
        username = me.username or me.first_name

        return {
            'status': 'connected',
            'message': f'Successfully connected to Telegram as {username}',
            'username': username,
            'session_str': client.session.save()
        }
    except SessionPasswordNeededError:
        if 'client' in locals() and client:
            session_str = client.session.save()
        return {
            'status': 'password_required',
            'message': 'Please enter your 2FA password',
            'session_str': session_str
        }
    except PhoneCodeInvalidError as e:
        raise HTTPException(status_code=400, detail="Invalid verification code")
    except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

async def verify_password(password: str, session_str: Optional[str] = None) -> Dict[str, Any]:
    """Verify the 2FA password for Telegram login"""
    try:
        client = await get_or_create_client(session_str)
        if not client:
            raise HTTPException(status_code=400, detail="No active Telegram connection")

        # Ensure client is connected before signing in
        if not client.is_connected():
            await client.connect()

        await client.sign_in(password=password)
        me = await client.get_me()
        username = me.username or me.first_name

        return {
            'status': 'connected',
            'message': f'Successfully connected to Telegram as {username}',
            'username': username,
            'session_str': client.session.save()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def get_or_create_client(session_str: str, api_id: int = None, api_hash: str = None) -> Optional[TelegramClient]:
    """Get existing client or create a new one from session string"""
    if not session_str:
        return None
    client = telegram_manager.get_client(session_str)
    if client:
        if not client.is_connected():
            try:
                await client.connect()
            except Exception as e:
                client = None
        
        if client:
            return client
    try:
        sess = StringSession(session_str)

        if not api_id or not api_hash:
            api_id = 94575
            api_hash = "a3406de8d171bb422bb6ddf3bbd800e2"
        
        client = TelegramClient(
            sess,
            api_id,
            api_hash,
            loop=telegram_manager.get_event_loop()
        )
        
        telegram_manager.set_client(session_str, client)
        return client
        
    except Exception as e:
        return None

async def check_auth(session_str: Optional[str] = None) -> Dict[str, Any]:
    """Check if user is authenticated with Telegram"""
    if not session_str:
        return {'authenticated': False, 'message': 'Not connected'}

    try:
        client = await get_or_create_client(session_str)
        
        if not client:
            return {
                'authenticated': False,
                'message': 'Failed to create client from session'
            }

        try:
            if not client.is_connected():
                await client.connect()
        except Exception as e:
            return {
                'authenticated': False,
                'message': f'Connection error: {str(e)}'
            }

        try:
            is_authorized = await client.is_user_authorized()
        except Exception as e:
            return {
                'authenticated': False,
                'message': f'Authorization check error: {str(e)}'
            }

        username = None
        if is_authorized:
            try:
                me = await client.get_me()
                username = me.username or me.first_name
            except Exception:
                return {
                    'authenticated': True,
                    'message': 'Authenticated (could not get username)',
                    'username': 'User'
                }

        return {
            'authenticated': is_authorized,
            'message': 'Authenticated' if is_authorized else 'Not authenticated',
            'username': username
        }
    except Exception as e:
        return {
            'authenticated': False,
            'message': f'Authentication check failed: {str(e)}'
        }

async def get_user_channels(session_name: str = 'test_session') -> List[Dict[str, Any]]:
    """Get all channels and groups the user has access to"""
    try:
        client = await get_or_create_client(session_name)
        if not client:
            raise HTTPException(status_code=400, detail="Not connected to Telegram")

        if not client.is_connected():
            await client.connect()

        if not await client.is_user_authorized():
            raise HTTPException(status_code=401, detail="User is not authorized")

        dialogs = await client.get_dialogs()
        channels = []

        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channel_info = {
                    'id': dialog.entity.id,
                    'title': dialog.entity.title,
                    'username': getattr(dialog.entity, 'username', None),
                    'participants_count': getattr(dialog.entity, 'participants_count', None)
                }
                channels.append(channel_info)

        return channels
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def disconnect(session_name: str = 'test_session') -> Dict[str, str]:
    """Disconnect from Telegram"""
    try:
        client = await get_or_create_client(session_name)
        if client:
            try:
                await client.disconnect()
            except Exception:
                pass
            telegram_manager.remove_client(session_name)

        return {'message': 'Successfully disconnected from Telegram'}
    except Exception:
        return {'message': 'Disconnected from Telegram (with some errors)'}

async def reset_session(session_name: str = 'test_session') -> Dict[str, str]:
    """Reset the Telegram session"""
    try:
        client = await get_or_create_client(session_name)
        if client:
            try:
                await client.disconnect()
            except Exception:
                pass
            telegram_manager.remove_client(session_name)

        return {'message': 'Session reset successfully'}
    except Exception:
        return {'message': 'Session reset (with some errors)'}

async def get_channel_images(
    channel_id: Union[int, str],
    date: Optional[str] = None,
    session_name: str = 'test_session',
    limit: int = 50
) -> List[Dict[str, Any]]:
    """Get images from a specific channel"""
    try:
        client = await get_or_create_client(session_name)
        if not client:
            raise HTTPException(status_code=400, detail="Not connected to Telegram")

        if not client.is_connected():
            await client.connect()

        if not await client.is_user_authorized():
            raise HTTPException(status_code=401, detail="User is not authorized")

        image_handler = ImageHandler(client)

        filter_date = None
        if date:
            if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
                raise HTTPException(status_code=400, detail="Invalid date format. Expected: YYYY-MM-DD")
            try:
                filter_date = datetime.strptime(date, '%Y-%m-%d').date()
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date")

        messages = await image_handler.get_channel_images(channel_id, filter_date, limit=limit)

        image_data = []
        for msg in messages:
            image_bytes = await image_handler.get_image_preview(msg)
            if image_bytes:
                image_data.append({
                    'id': msg.id,
                    'date': msg.date.strftime('%Y-%m-%d %H:%M:%S'),
                    'caption': msg.message if msg.message else '',
                    'image': base64.b64encode(image_bytes).decode('utf-8'),
                    'channel_id': channel_id
                })

        return image_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def get_channel_dates(channel_id: Union[int, str], session_name: str = 'test_session') -> List[str]:
    """Get dates that have images in a channel"""
    try:
        client = await get_or_create_client(session_name)
        if not client:
            raise HTTPException(status_code=400, detail="Not connected to Telegram")

        if not client.is_connected():
            await client.connect()

        if not await client.is_user_authorized():
            raise HTTPException(status_code=401, detail="User is not authorized")

        image_handler = ImageHandler(client)
        dates = await image_handler.get_channel_dates(channel_id)
        return [date.strftime('%Y-%m-%d') for date in dates]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def download_image(
    message_id: int,
    channel_id: Union[int, str],
    filename: Optional[str] = None,
    session_name: str = 'test_session'
) -> Dict[str, str]:
    """Download an image from a message - returns cached data or downloads from Telegram"""
    try:
        from cache.fetch_data_cache import get_cached_image_data
        
        # Check channel cache only - no fallback
        cached_data = await get_cached_image_data(channel_id, message_id)
        if cached_data and 'image_data' in cached_data:
            # Return cached image data from channel cache
            return {
                'status': 'cached',
                'image_data': cached_data['image_data'],
                'cache_source': 'channel_cache',
                'metadata': {
                    'date': cached_data.get('date'),
                    'caption': cached_data.get('caption', ''),
                    'file_size': cached_data.get('file_size'),
                    'file_extension': cached_data.get('file_extension', '.jpg'),
                    'mime_type': cached_data.get('mime_type', 'image/jpeg')
                }
            }
        
        # If not cached, download from Telegram
        client = await get_or_create_client(session_name)
        if not client:
            raise HTTPException(status_code=400, detail="Not connected to Telegram")

        if not client.is_connected():
            await client.connect()

        if not await client.is_user_authorized():
            raise HTTPException(status_code=401, detail="User is not authorized")

        channel = await client.get_entity(channel_id)
        message = await client.get_messages(channel, ids=message_id)

        if not message or not message.media:
            raise HTTPException(status_code=404, detail="No media found in the message")

        image_handler = ImageHandler(client)
        
        # Get image data directly (channel cache only, no fallback)
        image_data = await image_handler.get_image_data(message, channel_id)
        
        if not image_data:
            raise HTTPException(status_code=500, detail="Failed to download image")

        # Convert to base64 for response
        image_b64 = base64.b64encode(image_data).decode('utf-8')
        
        # Get file extension and mime type
        file_ext = image_handler._get_media_extension(message.media)
        mime_type = 'image/jpeg' if file_ext in ['.jpg', '.jpeg'] else 'image/png'
        
        return {
            'status': 'downloaded',
            'image_data': image_b64,
            'cache_source': 'fresh_download',
            'metadata': {
                'date': message.date.isoformat(),
                'caption': getattr(message, 'message', '') or '',
                'file_size': len(image_data),
                'file_extension': file_ext,
                'mime_type': mime_type
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

async def get_channel_analytics(
    channel_id: Union[int, str],
    session_str: Optional[str] = None,
    days: Optional[int] = None
) -> Dict[str, Any]:
    """Get analytics data for a specific channel"""
    try:
        client = await get_or_create_client(session_str)
        if not client:
            raise HTTPException(status_code=400, detail="Not connected to Telegram")

        if not client.is_connected():
            await client.connect()

        if not await client.is_user_authorized():
            raise HTTPException(status_code=401, detail="User is not authorized")

        try:
            channel_entity = await client.get_entity(channel_id)
            channel_info = {
                'id': channel_id,
                'title': getattr(channel_entity, 'title', f"Channel {channel_id}"),
                'username': getattr(channel_entity, 'username', None),
                'participants_count': getattr(channel_entity, 'participants_count', None)
            }
        except Exception:
            channel_info = {
                'id': channel_id,
                'title': f"Channel {channel_id}",
                'username': None,
                'participants_count': None
            }

        image_handler = ImageHandler(client)
        dates = await image_handler.get_channel_dates(channel_id)

        if days is not None and days > 0:
            try:
                from datetime import timedelta
                cutoff_date = datetime.now().date() - timedelta(days=days)
                dates = [date for date in dates if date >= cutoff_date]
            except Exception as e:
                print(f"Error filtering dates by days: {str(e)}")
                return {
                    'channel': channel_info,
                    'error': f"Failed to filter by days parameter: {str(e)}",
                    'total_images': 0,
                    'dates': [],
                    'date_image_counts': {}
                }

        formatted_dates = [date.strftime('%Y-%m-%d') for date in dates]
        date_image_counts: Dict[str, int] = {}
        total_images = 0
        monthly_counts: Dict[str, int] = {}
        weekday_counts = {i: 0 for i in range(7)}
        hour_of_day_counts = {hour: 0 for hour in range(24)}
        images_with_captions = 0
        date_objects = []

        for date in dates:
            formatted_date = date.strftime('%Y-%m-%d')
            images = await image_handler.get_channel_images(channel_id, date=date)
            image_count = len(images)
            date_image_counts[formatted_date] = image_count
            total_images += image_count
            date_objects.append(date)

            for msg in images:
                if msg.message and msg.message.strip():
                    images_with_captions += 1

                msg_hour = msg.date.hour
                hour_of_day_counts[msg_hour] += 1

                msg_weekday = msg.date.weekday()
                weekday_counts[msg_weekday] += 1

                month_key = msg.date.strftime('%Y-%m')
                monthly_counts[month_key] = monthly_counts.get(month_key, 0) + 1

        posting_frequency = calculate_posting_frequency(date_objects) if date_objects else {}
        sorted_monthly_counts = {k: monthly_counts[k] for k in sorted(monthly_counts.keys())}
        caption_percentage = (images_with_captions / total_images * 100) if total_images > 0 else 0

        return {
            'channel': channel_info,
            'total_images': total_images,
            'dates': formatted_dates,
            'date_image_counts': date_image_counts,
            'first_date': formatted_dates[-1] if formatted_dates else None,
            'last_date': formatted_dates[0] if formatted_dates else None,
            'days_analyzed': days,
            'advanced_analytics': {
                'monthly_distribution': sorted_monthly_counts,
                'weekday_distribution': weekday_counts,
                'hour_distribution': hour_of_day_counts,
                'caption_stats': {
                    'with_captions': images_with_captions,
                    'caption_percentage': round(caption_percentage, 1)
                },
                'posting_frequency': posting_frequency,
                'activity_metrics': {
                    'avg_posts_per_active_day': round(total_images / len(dates), 2) if dates else 0,
                    'total_active_days': len(dates),
                    'activity_period_days': (date_objects[-1] - date_objects[0]).days + 1 if len(date_objects) > 1 else 1
                }
            }
        }
    except Exception as e:
        return {
            'channel': {
                'id': channel_id,
                'title': f"Channel {channel_id}",
                'error': str(e)
            },
            'total_images': 0,
            'dates': [],
            'error': f"Failed to get analytics: {str(e)}"
        }

def calculate_posting_frequency(date_objects: List[datetime]) -> Dict[str, float]:
    """Calculate posting frequency metrics"""
    if not date_objects or len(date_objects) < 2:
        return {}

    time_deltas = []
    for i in range(1, len(date_objects)):
        delta = (date_objects[i] - date_objects[i-1]).days
        if delta > 0:
            time_deltas.append(delta)

    if not time_deltas:
        return {}

    avg_delta = sum(time_deltas) / len(time_deltas)
    variance = sum((x - avg_delta) ** 2 for x in time_deltas) / len(time_deltas)
    consistency_score = 100 * (1 / (1 + (variance / 10)))

    return {
        'avg_days_between_posts': avg_delta,
        'max_days_between_posts': max(time_deltas),
        'min_days_between_posts': min(time_deltas),
        'consistency_score': round(consistency_score, 1)
    }
