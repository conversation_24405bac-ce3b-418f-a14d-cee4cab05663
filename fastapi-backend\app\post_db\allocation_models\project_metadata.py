from sqlalchemy import <PERSON>um<PERSON>, Integer, String, TIMESTAMP, <PERSON>olean, func, Text
from sqlalchemy.dialects.postgresql import JSONB
from ..project_base import ProjectBase
from enum import Enum as PyEnum


class ProjectMetadata(ProjectBase):
    """
    Central configuration store for each project database. 
    Contains all project-specific rules, schemas, and processing instructions 
    that govern how annotation work is performed in this project.
    """
    __tablename__ = "project_metadata"

    # Primary Identity & Master Database Link
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique metadata record identifier within this project database")
    project_code = Column(String(50), nullable=False,
                         comment="Human-readable project code matching master database for cross-reference")
    master_db_project_id = Column(Integer, nullable=False,
                                 comment="Reference to projects_registry.id in master database for synchronization")
    
    # Dynamic Annotation Configuration
    annotation_requirements = Column(JSONB, nullable=False,
                              comment="Dynamic annotation requirements needed for this project")
   
    validation_rules = Column(JSONB, nullable=True,
                             comment="Project-specific validation rules for quality control and consistency")
    allocation_strategy = Column(JSONB, nullable=True,
                                   comment="Assignment strategies, review processes, and approval workflows for this project")
    
    # Storage and File Management Configuration
    credentials = Column(JSONB, nullable=True,
                        comment="Storage connection credentials and authentication details for this project (NAS, MinIO, GoogleDrive)")
    connection_type = Column(String(50), nullable=True,
                            comment="Type of storage connection: NAS-FTP, MinIO, GoogleDrive")
    folder_path = Column(String(500), nullable=True,
                        comment="Base folder path for project files on NAS or storage system")
    instructions = Column(Text, nullable=True,
                         comment="Project-specific instructions for annotators and reviewers")
    batch_size = Column(Integer, nullable=True,
                       comment="Default batch size for file processing and allocation")
    
    # File Processing & Media Handling Rules
    supported_file_types = Column(JSONB, nullable=True,
                                 comment="Allowed file extensions and media types (e.g., ['jpg', 'png', 'mp4'])")
    file_processing_pipeline = Column(JSONB, nullable=True,
                                     comment="Custom preprocessing steps, resizing, format conversion requirements")
    quality_requirements = Column(JSONB, nullable=True,
                                 comment="Quality standards, resolution requirements, and acceptance criteria")
    
    # Project Status & Synchronization
    is_active = Column(Boolean, default=True, nullable=False,
                      comment="Whether project is currently accepting new work")
    last_sync_with_master = Column(TIMESTAMP, default=func.now(), nullable=False,
                                  comment="Last successful synchronization with master database")
    
    # Audit Trail
    created_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                       comment="Project database creation timestamp")
    updated_at = Column(TIMESTAMP, default=func.now(), onupdate=func.now(), nullable=False,
                       comment="Last configuration update timestamp")

    def __repr__(self):
        return f"<ProjectMetadata(id={self.id}, project_code={self.project_code}, is_active={self.is_active})>"