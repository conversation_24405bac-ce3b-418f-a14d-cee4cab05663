"""
Comprehensive unit tests for CSVBatchService.
Tests CSV processing, batch creation, and project database operations.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Any
import json
import csv
import io
import tempfile
import os

from app.services.csv_batch_service import CSVBatchService

class TestCSVBatchServiceUnit:
    """Unit tests for CSVBatchService with mocked dependencies."""
    
    @pytest.fixture
    def csv_batch_service(self):
        """CSVBatchService instance for testing."""
        return CSVBatchService()
    
    @pytest.fixture
    def mock_csv_data(self):
        """Mock CSV data for testing."""
        return {
            'valid_csv': [
                ['image_path', 'annotation_data', 'metadata'],
                ['/images/img1.jpg', '{"label": "cat"}', '{"confidence": 0.9}'],
                ['/images/img2.jpg', '{"label": "dog"}', '{"confidence": 0.8}'],
                ['/images/img3.jpg', '{"label": "bird"}', '{"confidence": 0.95}']
            ],
            'large_csv': [['image_path', 'annotation_data']] + [
                [f'/images/img{i}.jpg', f'{{"label": "class_{i % 5}"}}'] 
                for i in range(10000)
            ],
            'malformed_csv': [
                ['image_path', 'annotation_data'],
                ['/images/bad.jpg'],  # Missing column
                ['/images/good.jpg', '{"label": "ok"}', 'extra_column']  # Extra column
            ]
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_csv_batches_success(self, csv_batch_service, mock_db_session, mock_csv_data):
        """Test successful CSV batch creation."""
        project_code = 'TEST_CSV_001'
        
        # Mock project info retrieval
        with patch.object(csv_batch_service, '_get_project_info') as mock_get_info:
            mock_get_info.return_value = {
                'num_annotators': 2,
                'project_id': 1,
                'allocation_strategy_id': 10
            }
            
            # Mock CSV processing
            with patch.object(csv_batch_service.csv_processor, 'process_csv_for_project') as mock_process:
                mock_process.return_value = {
                    'success': True,
                    'batches_created': 5,
                    'total_records': 100,
                    'processing_time': 2.5
                }
                
                # Mock batch creation in database
                with patch.object(csv_batch_service, '_create_batches_in_project_db') as mock_create:
                    mock_create.return_value = {
                        'success': True,
                        'batches_inserted': 5,
                        'files_registered': 100
                    }
                    
                    result = await csv_batch_service.create_csv_batches_in_project_db(
                        project_code, mock_db_session
                    )
                    
                    assert result['success'] is True
                    assert result['batches_created'] == 5
                    assert mock_get_info.called
                    assert mock_process.called
                    assert mock_create.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_csv_batches_project_not_found(self, csv_batch_service, mock_db_session):
        """Test CSV batch creation with non-existent project."""
        project_code = 'NONEXISTENT_001'
        
        with patch.object(csv_batch_service, '_get_project_info') as mock_get_info:
            mock_get_info.return_value = None
            
            result = await csv_batch_service.create_csv_batches_in_project_db(
                project_code, mock_db_session
            )
            
            assert result['success'] is False
            assert 'not found' in result['error'].lower()

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_info_success(self, csv_batch_service, mock_db_session):
        """Test successful project info retrieval."""
        project_code = 'INFO_TEST_001'
        
        # Mock database query results
        mock_project = MagicMock()
        mock_project.id = 1
        mock_project.project_code = project_code
        mock_project.allocation_strategy_id = 10
        
        mock_strategy = MagicMock()
        mock_strategy.num_annotators = 3
        mock_strategy.requires_verification = True
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        
        mock_strategy_result = AsyncMock()
        mock_strategy_result.scalar_one_or_none.return_value = mock_strategy
        
        mock_db_session.execute.side_effect = [mock_result, mock_strategy_result]
        
        result = await csv_batch_service._get_project_info(project_code, mock_db_session)
        
        assert result['project_id'] == 1
        assert result['num_annotators'] == 3
        assert result['requires_verification'] is True
        assert result['allocation_strategy_id'] == 10

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_project_info_no_strategy(self, csv_batch_service, mock_db_session):
        """Test project info retrieval when allocation strategy is missing."""
        project_code = 'NO_STRATEGY_001'
        
        mock_project = MagicMock()
        mock_project.id = 1
        mock_project.allocation_strategy_id = None
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_project
        
        mock_db_session.execute.return_value = mock_result
        
        result = await csv_batch_service._get_project_info(project_code, mock_db_session)
        
        assert result['num_annotators'] == 1  # Default value
        assert result['requires_verification'] is False  # Default value

    @pytest.mark.unit
    def test_validate_csv_structure_valid(self, csv_batch_service, mock_csv_data):
        """Test CSV structure validation with valid data."""
        valid_csv = mock_csv_data['valid_csv']
        
        with patch.object(csv_batch_service, '_validate_csv_structure') as mock_validate:
            mock_validate.return_value = {'valid': True, 'errors': []}
            
            result = csv_batch_service._validate_csv_structure(valid_csv)
            assert result['valid'] is True
            assert len(result['errors']) == 0

    @pytest.mark.unit
    def test_validate_csv_structure_malformed(self, csv_batch_service, mock_csv_data):
        """Test CSV structure validation with malformed data."""
        malformed_csv = mock_csv_data['malformed_csv']
        
        with patch.object(csv_batch_service, '_validate_csv_structure') as mock_validate:
            mock_validate.return_value = {
                'valid': False, 
                'errors': ['Row 2: Missing columns', 'Row 3: Extra columns']
            }
            
            result = csv_batch_service._validate_csv_structure(malformed_csv)
            assert result['valid'] is False
            assert len(result['errors']) == 2

    @pytest.mark.unit
    def test_process_csv_data_valid(self, csv_batch_service, mock_csv_data):
        """Test CSV data processing with valid input."""
        csv_data = mock_csv_data['valid_csv'][1:]  # Exclude header
        
        with patch.object(csv_batch_service, '_process_csv_rows') as mock_process:
            mock_process.return_value = {
                'processed_rows': 3,
                'valid_rows': 3,
                'invalid_rows': 0,
                'batch_data': [
                    {'image_path': '/images/img1.jpg', 'annotation_data': '{"label": "cat"}'},
                    {'image_path': '/images/img2.jpg', 'annotation_data': '{"label": "dog"}'},
                    {'image_path': '/images/img3.jpg', 'annotation_data': '{"label": "bird"}'}
                ]
            }
            
            result = csv_batch_service._process_csv_rows(csv_data)
            assert result['valid_rows'] == 3
            assert result['invalid_rows'] == 0
            assert len(result['batch_data']) == 3

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_batches_in_project_db_success(self, csv_batch_service, mock_db_session):
        """Test batch creation in project database."""
        batch_data = [
            {'image_path': '/img1.jpg', 'annotation_data': '{"label": "cat"}'},
            {'image_path': '/img2.jpg', 'annotation_data': '{"label": "dog"}'}
        ]
        project_code = 'BATCH_CREATE_001'
        annotation_count = 2
        
        with patch.object(csv_batch_service, '_get_project_db_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            with patch.object(csv_batch_service, '_insert_batch_records') as mock_insert:
                mock_insert.return_value = {
                    'batches_created': 1,
                    'files_registered': 2,
                    'batch_identifiers': ['CSV_BATCH_001']
                }
                
                result = await csv_batch_service._create_batches_in_project_db(
                    batch_data, project_code, annotation_count, mock_db_session
                )
                
                assert result['success'] is True
                assert result['batches_created'] == 1
                assert result['files_registered'] == 2

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_performance_large_csv_processing(self, csv_batch_service, mock_csv_data, 
                                                   performance_monitor, service_performance_data):
        """Test performance with large CSV files."""
        large_csv = mock_csv_data['large_csv']
        
        with patch.object(csv_batch_service, '_process_csv_rows') as mock_process:
            performance_monitor.start()
            
            mock_process.return_value = {
                'processed_rows': 10000,
                'valid_rows': 9950,
                'invalid_rows': 50,
                'batch_data': [{'image_path': f'/img{i}.jpg'} for i in range(9950)]
            }
            
            result = csv_batch_service._process_csv_rows(large_csv[1:])  # Exclude header
            
            performance_monitor.stop()
            
            execution_time = performance_monitor.get_execution_time()
            acceptable_time = service_performance_data['acceptable_response_times']['create_csv_batches']
            
            assert execution_time < acceptable_time, f"Processing time {execution_time}s exceeds limit"
            assert result['processed_rows'] == 10000

    @pytest.mark.unit
    def test_memory_usage_large_csv(self, csv_batch_service, service_performance_data):
        """Test memory usage with large CSV processing."""
        import sys
        
        # Create large CSV data in memory
        large_data = [
            {'image_path': f'/large/img{i}.jpg', 'data': f'annotation_data_{i}'} 
            for i in range(50000)
        ]
        
        initial_size = sys.getsizeof(large_data)
        
        # Simulate processing
        processed_data = []
        for item in large_data:
            processed_item = {
                'normalized_path': item['image_path'].replace('\\', '/'),
                'parsed_data': json.loads(f'{{"label": "class_{hash(item["data"]) % 10}"}}')
            }
            processed_data.append(processed_item)
        
        final_size = sys.getsizeof(processed_data)
        memory_increase = (final_size - initial_size) / 1024 / 1024  # MB
        
        max_memory = service_performance_data['memory_limits']['csv_processing']
        assert memory_increase < max_memory, f"Memory usage {memory_increase}MB exceeds limit"

    @pytest.mark.unit
    def test_csv_injection_protection(self, csv_batch_service, security_test_data):
        """Test protection against CSV injection attacks."""
        malicious_csv_data = [
            ['image_path', 'annotation_data'],
            [security_test_data['malicious_inputs'][0], '{"label": "test"}'],  # SQL injection
            ['/normal/path.jpg', security_test_data['malicious_inputs'][1]],   # XSS in data
            ['=cmd|"/c calc"!A1', '{"label": "formula_injection"}'],          # Formula injection
        ]
        
        with patch.object(csv_batch_service, '_sanitize_csv_input') as mock_sanitize:
            mock_sanitize.side_effect = lambda x: x if 'normal' in str(x) else '[SANITIZED]'
            
            result = []
            for row in malicious_csv_data[1:]:  # Skip header
                sanitized_row = [mock_sanitize(cell) for cell in row]
                result.append(sanitized_row)
            
            # Verify malicious inputs were sanitized
            assert '[SANITIZED]' in str(result[0])  # SQL injection sanitized
            assert '[SANITIZED]' in str(result[1])  # XSS sanitized
            assert '[SANITIZED]' in str(result[2])  # Formula injection sanitized

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_database_transaction_rollback(self, csv_batch_service, mock_db_session):
        """Test database transaction rollback on errors."""
        batch_data = [{'image_path': '/test.jpg'}]
        
        with patch.object(csv_batch_service, '_get_project_db_session') as mock_get_session:
            mock_project_session = AsyncMock()
            mock_get_session.return_value = mock_project_session
            
            # Mock database error during batch creation
            mock_project_session.execute.side_effect = Exception("Database constraint violation")
            
            result = await csv_batch_service._create_batches_in_project_db(
                batch_data, 'ERROR_TEST_001', 1, mock_db_session
            )
            
            # Verify rollback was called
            assert mock_project_session.rollback.called
            assert result['success'] is False
            assert 'error' in result

    @pytest.mark.unit
    def test_csv_encoding_handling(self, csv_batch_service):
        """Test handling of different CSV encodings."""
        encodings_to_test = ['utf-8', 'utf-16', 'latin1', 'cp1252']
        
        test_data = [
            ['path', 'label'],
            ['/test/файл.jpg', 'тест'],  # Unicode characters
            ['/test/café.jpg', 'café'],   # Accented characters
        ]
        
        for encoding in encodings_to_test:
            with tempfile.NamedTemporaryFile(mode='w', encoding=encoding, delete=False, suffix='.csv') as f:
                writer = csv.writer(f)
                try:
                    writer.writerows(test_data)
                    csv_file_path = f.name
                except UnicodeEncodeError:
                    # Skip encodings that can't handle the test data
                    continue
            
            try:
                with patch.object(csv_batch_service, '_read_csv_file') as mock_read:
                    mock_read.return_value = test_data
                    
                    result = csv_batch_service._read_csv_file(csv_file_path, encoding)
                    assert len(result) == 3  # Header + 2 data rows
                    
            finally:
                os.unlink(csv_file_path)

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_csv_processing(self, csv_batch_service, mock_db_session):
        """Test concurrent CSV processing for multiple projects."""
        import asyncio
        
        project_codes = ['CONCURRENT_001', 'CONCURRENT_002', 'CONCURRENT_003']
        
        with patch.object(csv_batch_service, '_get_project_info') as mock_get_info:
            mock_get_info.return_value = {'num_annotators': 1, 'project_id': 1}
            
            with patch.object(csv_batch_service.csv_processor, 'process_csv_for_project') as mock_process:
                mock_process.return_value = {
                    'success': True,
                    'batches_created': 1,
                    'total_records': 10
                }
                
                # Process multiple projects concurrently
                tasks = []
                for project_code in project_codes:
                    task = csv_batch_service.create_csv_batches_in_project_db(
                        project_code, mock_db_session
                    )
                    tasks.append(task)
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Verify all completed successfully
                exceptions = [r for r in results if isinstance(r, Exception)]
                assert len(exceptions) == 0
                
                successful_results = [r for r in results if r.get('success', False)]
                assert len(successful_results) == len(project_codes)

    @pytest.mark.unit
    def test_csv_format_detection(self, csv_batch_service):
        """Test automatic CSV format detection."""
        csv_formats = {
            'comma_separated': 'path,label,confidence\n/img1.jpg,cat,0.9\n/img2.jpg,dog,0.8',
            'semicolon_separated': 'path;label;confidence\n/img1.jpg;cat;0.9\n/img2.jpg;dog;0.8',
            'tab_separated': 'path\tlabel\tconfidence\n/img1.jpg\tcat\t0.9\n/img2.jpg\tdog\t0.8',
            'pipe_separated': 'path|label|confidence\n/img1.jpg|cat|0.9\n/img2.jpg|dog|0.8'
        }
        
        for format_name, csv_content in csv_formats.items():
            with patch.object(csv_batch_service, '_detect_csv_delimiter') as mock_detect:
                expected_delimiter = {'comma_separated': ',', 'semicolon_separated': ';', 
                                    'tab_separated': '\t', 'pipe_separated': '|'}[format_name]
                mock_detect.return_value = expected_delimiter
                
                delimiter = csv_batch_service._detect_csv_delimiter(csv_content)
                assert delimiter == expected_delimiter

    @pytest.mark.unit
    @pytest.mark.asyncio 
    async def test_error_recovery_partial_failure(self, csv_batch_service, mock_db_session):
        """Test recovery from partial processing failures."""
        # Simulate scenario where some batches succeed and some fail
        batch_data = [
            {'image_path': '/success1.jpg', 'valid': True},
            {'image_path': '/error.jpg', 'valid': False},  # This will cause error
            {'image_path': '/success2.jpg', 'valid': True}
        ]
        
        with patch.object(csv_batch_service, '_process_batch_item') as mock_process:
            def side_effect(item):
                if not item.get('valid', True):
                    raise Exception(f"Processing failed for {item['image_path']}")
                return {'success': True, 'batch_id': f"BATCH_{hash(item['image_path'])}"}
            
            mock_process.side_effect = side_effect
            
            results = []
            errors = []
            
            for item in batch_data:
                try:
                    result = csv_batch_service._process_batch_item(item)
                    results.append(result)
                except Exception as e:
                    errors.append(str(e))
            
            # Should have 2 successful results and 1 error
            assert len(results) == 2
            assert len(errors) == 1
            assert 'error.jpg' in errors[0]
