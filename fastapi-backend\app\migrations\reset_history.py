#!/usr/bin/env python3
"""
Reset Migration History
Cleans up alembic_version table to fix revision conflicts
"""

import sys
import os
from pathlib import Path

# Setup paths - Updated for new structure  
app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))

def get_database_url(db_name):
    """Get database URL"""
    urls = {
        "master_db": os.getenv("MASTER_DB_DATABASE_URL", 
                             "postgresql+asyncpg://kanwar_raj:dadpdev123@***********:5432/master_db"),
        "project_db": os.getenv("PROJECT_DB_DATABASE_URL", 
                               "postgresql+asyncpg://mansi:pass123@***********:5432/project_db")
    }
    return urls[db_name].replace('postgresql+asyncpg://', 'postgresql+psycopg2://')

def reset_migration_history(db_name):
    """Reset migration history for database"""
    try:
        import psycopg2 # type: ignore
        from urllib.parse import urlparse
        
        url = get_database_url(db_name)
        parsed = urlparse(url)
        
        print(f"Resetting migration history for {db_name}...")
        
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path[1:]
        )
        
        with conn.cursor() as cur:
            # Check if alembic_version table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'alembic_version'
                );
            """)
            
            table_exists = cur.fetchone()[0]
            
            if table_exists:
                # Get current version
                cur.execute("SELECT version_num FROM alembic_version;")
                current_versions = cur.fetchall()
                print(f"Current versions: {current_versions}")
                
                # Clear the alembic_version table
                cur.execute("DELETE FROM alembic_version;")
                print("Cleared migration history")
            else:
                print("No alembic_version table found - migration history is clean")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error resetting {db_name}: {e}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument('--database', choices=['master_db', 'project_db', 'all'], default='all')
    
    args = parser.parse_args()
    
    if args.database == 'all':
        success = reset_migration_history('master_db')
        success &= reset_migration_history('project_db')
    else:
        success = reset_migration_history(args.database)
    
    if success:
        print("\n Migration history reset completed!")
        print("You can now run migrations from scratch.")
    else:
        print("\n Migration history reset failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
