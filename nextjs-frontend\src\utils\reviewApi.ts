/**
 * Utility functions for fetching file review data from the API
 */

import { API_BASE_URL } from '@/lib/api';

const API_BASE = API_BASE_URL;

export interface FileReview {
  annotator_id: number;
  annotator_number: number;
  review_data: any;
}

export interface FileReviewResponse {
  success: boolean;
  file_id: number;
  batch_id: number;
  reviews: FileReview[];
  cached_at?: string;
}

/**
 * Fetch review data for a specific file from the cache
 * @param fileId - ID of the file
 * @param batchId - ID of the batch
 * @returns Promise with file review data
 */
export async function fetchFileReviews(fileId: number, batchId: number): Promise<FileReviewResponse> {
  const response = await fetch(`${API_BASE}/verifier/file-reviews/${fileId}?batch_id=${batchId}`, {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json'
    }
  });

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error(`No review data found for file ${fileId} in batch ${batchId}`);
    } else if (response.status === 401) {
      throw new Error('Authentication required. Please log in again.');
    } else {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Failed to fetch review data (Status: ${response.status})`);
    }
  }

  return response.json();
}

/**
 * Fetch review data for multiple files
 * @param fileIds - Array of file objects with file_id and batch_id
 * @returns Promise with array of file review data
 */
export async function fetchMultipleFileReviews(
  files: { file_id: number; batch_id: number }[]
): Promise<FileReviewResponse[]> {
  const promises = files.map(file => 
    fetchFileReviews(file.file_id, file.batch_id)
  );
  
  return Promise.all(promises);
}
