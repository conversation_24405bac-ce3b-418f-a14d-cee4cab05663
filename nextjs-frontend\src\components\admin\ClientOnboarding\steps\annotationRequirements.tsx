'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FaEye, FaSave, FaPlus, FaTrash, FaEdit, FaProjectDiagram } from 'react-icons/fa';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { DynamicField } from '@/components/shared/dynamic-fields';

import { API_BASE_URL } from "@/lib/api";

// Types for form field configuration
type FieldType = 'short_answer' | 'long_answer' | 'multiple_choice' | 'checkboxes';

interface FormFieldConfig {
  field_name: string;
  field_type: FieldType;
  label: string;
  required: boolean;
  options?: string[];
  placeholder?: string;
  max_length?: number;
}

interface Project {
  id: string;
  name: string;
  project_code: string;
  project_name: string;
  project_type: string;
  batch_size?: number;
  total_files?: number;
}

interface AnnotatorFormConfigResponse {
  id: number;
  mode?: string;
  dataset_id: string;  // Project code string in format PROJ_{project_name}_{client_id}
  dataset_name: string;
  fields: FormFieldConfig[];
}

interface FormConfigData {
  success: boolean;
  data?: AnnotatorFormConfigResponse;
  message: string;
}

// Payload type for backend API
type SavePayload = {
  dataset: string;  // Project code string in format PROJ_{project_name}_{client_id}
  fields: FormFieldConfig[];
};

interface AnnotationRequirementsProps {
  selectedClient: any;
  projectCode: string | undefined;
  onGoToStep: (step: number, projectCode?: string) => void;
  isStepCompleted: boolean;
  markStepCompleted: (step: number) => void;
}

export default function AnnotationRequirements({
  selectedClient,
  projectCode,
  onGoToStep,
  isStepCompleted,
  markStepCompleted
}: AnnotationRequirementsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedProjectCode, setSelectedProjectCode] = useState<string>(projectCode || '');
  const [projectsList, setProjectsList] = useState<Project[]>([]);
  const [currentProjectData, setCurrentProjectData] = useState<Project | null>(null);
  const [formFields, setFormFields] = useState<FormFieldConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<Record<string, any>>({});

  // Field types available for selection (similar to Google Forms)
  const fieldTypes: { value: FieldType; label: string; description: string; icon?: string }[] = [
    { value: 'short_answer', label: 'Short Answer', description: 'Brief text response'},
    { value: 'long_answer', label: 'Long Answer', description: 'Paragraph text response'},
    { value: 'multiple_choice', label: 'Multiple Choice', description: 'Single selection (○ radio buttons)'},
    { value: 'checkboxes', label: 'Checkboxes', description: 'Multiple selections (☐ checkboxes)'}
  ];

  // Project change
  const handleProjectChange = (projectCode: string) => {
    if (!projectCode) {
      setSelectedProjectCode('');
      setCurrentProjectData(null);
      setFormFields([]);
      router.push(`?view=annotatorFormConfig`);
      return;
    }
    setSelectedProjectCode(projectCode);
    setFormFields([]);
    router.push(`?view=annotatorFormConfig&project=${projectCode}`);
    
    // Find and set the current project data
    const project = projectsList.find(p => p.project_code === projectCode);
    if (project) {
      setCurrentProjectData(project);
    } else {
      setCurrentProjectData(null);
    }
  };

  // Add new field
  const addField = () => {
    const newField: FormFieldConfig = {
      field_name: '',
      field_type: 'short_answer',
      label: '',
      required: false,
      placeholder: '',
    };
    setFormFields([...formFields, newField]);
  };

  // Remove field
  const removeField = (index: number) => {
    const newFields = formFields.filter((_, i) => i !== index);
    setFormFields(newFields);
  };

  // Update field
  const updateField = (index: number, field: Partial<FormFieldConfig>) => {
    const newFields = [...formFields];
    newFields[index] = { ...newFields[index], ...field };
    setFormFields(newFields);
  };

  // Preview handlers
  const handlePreviewChange = (fieldName: string, value: any) => {
    setPreviewData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const togglePreview = () => {
    setShowPreview(!showPreview);
    if (!showPreview) {
      // Reset preview data when opening preview
      setPreviewData({});
    }
  };

  // Add dummy data function
  const addDummyData = () => {
    const dummyFields: FormFieldConfig[] = [
      {
        field_name: 'document_quality',
        field_type: 'multiple_choice',
        label: 'What is the overall quality of this document?',
        required: true,
        options: ['Excellent', 'Good', 'Fair', 'Poor'],
        placeholder: ''
      },
      {
        field_name: 'key_information',
        field_type: 'checkboxes',
        label: 'Which key information elements are present? (Select all that apply)',
        required: false,
        options: ['Date', 'Amount', 'Names', 'Addresses', 'Signatures'],
        placeholder: ''
      },
      {
        field_name: 'additional_notes',
        field_type: 'long_answer',
        label: 'Please provide any additional notes or observations about this document',
        required: false,
        placeholder: 'Enter your detailed observations here...',
        max_length: 500
      }
    ];
    
    setFormFields(dummyFields);
    showToast.success('3 sample questions have been added!');
  };

  // Add option to select field
  const addOption = (fieldIndex: number) => {
    const newFields = [...formFields];
    if (!newFields[fieldIndex].options) {
      newFields[fieldIndex].options = [];
    }
    newFields[fieldIndex].options!.push('');
    setFormFields(newFields);
  };

  // Update option
  const updateOption = (fieldIndex: number, optionIndex: number, value: string) => {
    const newFields = [...formFields];
    if (newFields[fieldIndex].options) {
      newFields[fieldIndex].options![optionIndex] = value;
      setFormFields(newFields);
    }
  };

  // Remove option
  const removeOption = (fieldIndex: number, optionIndex: number) => {
    const newFields = [...formFields];
    if (newFields[fieldIndex].options) {
      newFields[fieldIndex].options = newFields[fieldIndex].options!.filter((_, i) => i !== optionIndex);
      setFormFields(newFields);
    }
  };

  // Submit handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProjectCode || formFields.length === 0) {
      showToast.error('Please select a project and add at least one field');
      return;
    }

    // Validate fields
    for (let i = 0; i < formFields.length; i++) {
      const field = formFields[i];
      if (!field.field_name || !field.label) {
        showToast.error(`Field ${i + 1}: Field name and label are required`);
        return;
      }
      if (['multiple_choice', 'checkboxes'].includes(field.field_type) && (!field.options || field.options.length === 0)) {
        showToast.error(`Field ${i + 1}: Choice fields must have at least one option`);
        return;
      }
    }

    // Prepare payload for annotation_requirements column
    const payload: SavePayload = {
      dataset: selectedProjectCode, // Project code string
      fields: formFields.map(field => ({
        ...field,
        options: ['multiple_choice', 'checkboxes'].includes(field.field_type) 
          ? field.options?.filter(opt => opt.trim() !== '') 
          : undefined
      }))
    };

    try {
      setLoading(true);
      
      // Add mode field to payload to match backend schema
      const fullPayload = {
        ...payload,
        mode: "annotation" // Default to annotation mode
      };
      
      const response = await authFetch(`${API_BASE_URL}/admin/annotator-form-config`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(fullPayload),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = 'Failed to save form configuration';
        
        if (errorData.detail) {
          errorMessage = typeof errorData.detail === 'string' 
            ? errorData.detail 
            : JSON.stringify(errorData.detail);
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }
        
        throw new Error(errorMessage);
      }
      
      const data = await response.json();
      showToast.success(data.message || 'Annotator form configuration saved successfully!');
      
      // Mark this step as completed and proceed to the next step
      markStepCompleted(6);
      onGoToStep(7);
    } catch (error) {
      console.error('Error saving form configuration:', error);
      showToast.error(error instanceof Error ? error.message : 'Error saving form configuration');
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects - memoized to avoid infinite loops
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      const response = await authFetch(`${API_BASE_URL}/admin/get-datasets`, { credentials: 'include' });
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const json = await response.json();

      if (json.success && json.data?.datasets) {
        setProjectsList(json.data.datasets);
        return json.data.datasets;
      }
      return [];
    } catch (error) {
      console.error('Error fetching projects:', error);
      showToast.error('Failed to load projects');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Initialize with projectCode prop and fetch data
  useEffect(() => {
    // Use the projectCode prop if available
    if (projectCode) {
      setSelectedProjectCode(projectCode);
    }
    
    // Initial load of projects
    fetchProjects().then(projects => {
      // If we have a selected project, find it in the projects list
      const projectToUse = projectCode || selectedProjectCode;
      if (projectToUse) {
        const currentProject = projects.find((p: Project) => p.project_code === projectToUse);
        if (currentProject) {
          setCurrentProjectData(currentProject);
        }
      }
    });
  }, [projectCode, selectedProjectCode, fetchProjects]);

  // Fetch existing form configuration when project changes
  useEffect(() => {
    if (!selectedProjectCode) return;
    
    setLoading(true);
    authFetch(`${API_BASE_URL}/admin/annotator-form-config?dataset=${selectedProjectCode}`, { credentials: 'include' })
      .then(res => {
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        return res.json();
      })
      .then((json: FormConfigData) => {
        if (json.success && json.data) {
          setFormFields(json.data.fields || []);
        }
      })
      .catch(err => {
        console.error('Error fetching form configuration:', err);
        showToast.error('Failed to load form configuration');
      })
      .finally(() => setLoading(false));
  }, [selectedProjectCode]);

  return (
    <div className="w-full">
      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2 flex items-center">
          <FaEye className="mr-2 text-blue-500" />
          Design Annotator Questions
        </h2>
        <p className="text-gray-600 mb-4">
          Create custom form fields for annotators to fill out during the annotation process.
          {projectCode && <span className="font-medium"> Project: {projectCode}</span>}
        </p>
      </div>

      <div className="card mb-4">
        <div className="card-body">
          <form id="annotatorFormConfigForm" onSubmit={handleSubmit}>
            {/* Project Selection - Only show if projectCode is not provided */}
            {!projectCode && (
              <div className="mb-4" id="projectSelectionContainer">
                <label htmlFor="projectSelect" className="form-label d-flex align-items-center">
                  <FaProjectDiagram className="me-2 text-primary" />
                  Select Project
                </label>
                <select
                  className="form-select"
                  id="projectSelect"
                  name="project_id"
                  value={selectedProjectCode}
                  onChange={(e) => handleProjectChange(e.target.value)}
                  disabled={!projectsList.length}
                >
                  <option value="">-- Select Project --</option>
                  {projectsList.map(project => (
                    <option key={project.project_code} value={project.project_code}>
                      {project.project_name} ({project.project_code})
                    </option>
                  ))}
                </select>
                {!projectsList.length && loading && (
                  <div className="d-flex align-items-center mt-2 text-info">
                    <div className="spinner-border spinner-border-sm me-2" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <span>Loading projects...</span>
                  </div>
                )}
                {!projectsList.length && !loading && (
                  <div className="form-text text-warning">No projects available.</div>
                )}
              </div>
            )}

            {/* Show selected project info when projectCode is provided */}
            {projectCode && (
              <div className="mb-4 p-3 bg-success bg-opacity-10 border border-success rounded">
                <div className="d-flex align-items-center text-success">
                  <i className="fas fa-check-circle me-2"></i>
                  <div>
                    <div className="small text-success mt-1">
                      Using project code: <strong>{projectCode}</strong>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Project Details */}
            {currentProjectData && (
              <div className="mb-4 p-3 bg-light rounded">
                <h6 className="mb-2">Project Details</h6>
                <div className="row">
                  <div className="col-md-6">
                    <div className="mb-1"><strong>Type:</strong> {currentProjectData.project_type}</div>
                    <div><strong>Batch Size:</strong> {currentProjectData.batch_size || "Default"}</div>
                  </div>
                  <div className="col-md-6">
                    <div className="mb-1"><strong>Total Files:</strong> {currentProjectData.total_files || 0}</div>
                    <div><strong>ID:</strong> {currentProjectData.project_code}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Fields Configuration */}
            {selectedProjectCode && (
              <div className="mb-4" id="formFieldsContainer">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <h5>Configure Form Fields</h5>
                  <div className="btn-group">
                    {formFields.length > 0 && (
                      <button
                        type="button"
                        className="btn btn-outline-info"
                        onClick={togglePreview}
                      >
                        <FaEye className="me-2" />
                      </button>
                    )}
                    <button
                      type="button"
                      className="btn btn-outline-secondary"
                      onClick={addDummyData}
                      title="Add 3 sample questions to get started quickly"
                    >
                      <FaPlus className="me-2" />
                      Add Sample Questions
                    </button>
                    <button
                      type="button"
                      className="btn btn-primary"
                      onClick={addField}
                    >
                      <FaPlus className="me-2" />
                      Add Question
                    </button>
                  </div>
                </div>
                
                {formFields.length === 0 && !loading && (
                  <div className="text-center py-5 bg-light rounded">
                    <h5 className="text-muted">No questions added yet</h5>
                    <p className="text-muted mb-3">Create your custom form by adding questions for annotators to fill out.</p>
                    <p className="text-muted small">💡 Tip: Use "Add Sample Questions" to get started with 3 example questions that you can customize.</p>
                  </div>
                )}

                {loading && (
                  <div className="text-center py-3">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                )}

                {formFields.map((field, fieldIndex) => (
                  <div key={fieldIndex} className="card mb-3">
                    <div className="card-body">
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <div>
                          <h6 className="mb-0">Question {fieldIndex + 1}</h6>
                          <small className="text-muted">
                            <span className="me-1">{fieldTypes.find(t => t.value === field.field_type)?.icon}</span>
                            {fieldTypes.find(t => t.value === field.field_type)?.label || field.field_type}
                          </small>
                        </div>
                        <button
                          type="button"
                          className="btn btn-outline-danger btn-sm"
                          onClick={() => removeField(fieldIndex)}
                          title="Delete question"
                        >
                          <FaTrash />
                        </button>
                      </div>

                      <div className="row">
                        <div className="col-md-5">
                          <label className="form-label">Question Text *</label>
                          <input
                            type="text"
                            className="form-control"
                            value={field.label}
                            onChange={(e) => updateField(fieldIndex, { label: e.target.value })}
                            placeholder="Enter your question here..."
                          />
                        </div>
                        <div className="col-md-3">
                          <label className="form-label">Question Type</label>
                          <select
                            className="form-select"
                            value={field.field_type}
                            onChange={(e) => updateField(fieldIndex, { field_type: e.target.value as FieldType })}
                          >
                            {fieldTypes.map(type => (
                              <option key={type.value} value={type.value} title={type.description}>
                                {type.icon} {type.label}
                              </option>
                            ))}
                          </select>
                          <div className="form-text text-muted small">
                            <strong>{fieldTypes.find(t => t.value === field.field_type)?.icon}</strong> {fieldTypes.find(t => t.value === field.field_type)?.description}
                          </div>
                        </div>
                        <div className="col-md-3">
                          <label className="form-label">Field Name (Data Key) *</label>
                          <input
                            type="text"
                            className="form-control"
                            value={field.field_name}
                            onChange={(e) => updateField(fieldIndex, { field_name: e.target.value })}
                            placeholder="field_name"
                          />
                          <div className="form-text text-muted small">
                            Used for storing data (no spaces/special chars)
                          </div>
                        </div>
                      </div>

                      <div className="row mt-3">
                        <div className="col-md-6">
                          <label className="form-label">Placeholder Text</label>
                          <input
                            type="text"
                            className="form-control"
                            value={field.placeholder || ''}
                            onChange={(e) => updateField(fieldIndex, { placeholder: e.target.value })}
                            placeholder="Optional placeholder text"
                          />
                        </div>
                        <div className="col-md-3">
                          <label className="form-label">Max Length</label>
                          <input
                            type="number"
                            className="form-control"
                            value={field.max_length || ''}
                            onChange={(e) => updateField(fieldIndex, { max_length: e.target.value ? parseInt(e.target.value) : undefined })}
                            placeholder="Optional"
                          />
                        </div>
                        <div className="col-md-3">
                          <div className="form-check mt-4">
                            <input
                              type="checkbox"
                              className="form-check-input"
                              style={{ borderColor: '#495057', borderWidth: '2px' }}
                              checked={field.required}
                              onChange={(e) => updateField(fieldIndex, { required: e.target.checked })}
                            />
                            <label className="form-check-label">Required</label>
                          </div>
                        </div>
                      </div>

                      {/* Options for choice-based fields */}
                      {['multiple_choice', 'checkboxes'].includes(field.field_type) && (
                        <div className="mt-3">
                          <div className="d-flex justify-content-between align-items-center mb-2">
                            <div>
                              <label className="form-label mb-0">Options</label>
                              <div className="form-text text-muted small">
                                {field.field_type === 'multiple_choice' 
                                  ? '○ Users can select only ONE option' 
                                  : '☐ Users can select MULTIPLE options'
                                }
                              </div>
                            </div>
                            <button
                              type="button"
                              className="btn btn-outline-secondary btn-sm"
                              onClick={() => addOption(fieldIndex)}
                            >
                              <FaPlus className="me-1" /> Add Option
                            </button>
                          </div>
                          
                          {/* Preview section
                          {field.options && field.options.length > 0 && (
                            <div className="bg-light p-3 rounded mb-3">
                              <small className="text-muted">Preview for annotators:</small>
                              <div className="mt-2">
                                <strong>{field.label || 'Your Question'}</strong>
                                <div className="mt-2">
                                  {field.options.map((option, optionIndex) => (
                                    option.trim() && (
                                      <div key={optionIndex} className="mb-1">
                                        {field.field_type === 'multiple_choice' ? (
                                          <label className="form-check-label d-flex align-items-center">
                                            <input type="radio" className="form-check-input me-2" disabled />
                                            {option}
                                          </label>
                                        ) : (
                                          <label className="form-check-label d-flex align-items-center">
                                            <input type="checkbox" className="form-check-input me-2" disabled />
                                            {option}
                                          </label>
                                        )}
                                      </div>
                                    )
                                  ))}
                                </div>
                              </div>
                            </div>
                          )} */}

                          {field.options?.map((option, optionIndex) => (
                            <div key={optionIndex} className="input-group mb-2">
                              <span className="input-group-text">
                                {field.field_type === 'multiple_choice' ? '○' : '☐'}
                              </span>
                              <input
                                type="text"
                                className="form-control"
                                value={option}
                                onChange={(e) => updateOption(fieldIndex, optionIndex, e.target.value)}
                                placeholder={`Option ${optionIndex + 1}`}
                              />
                              <button
                                type="button"
                                className="btn btn-outline-danger"
                                onClick={() => removeOption(fieldIndex, optionIndex)}
                              >
                                <FaTrash />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {/* Form Preview
                {showPreview && formFields.length > 0 && (
                  <div className="card mt-4 border-info">
                    <div className="card-header bg-info text-white">
                      <h6 className="mb-0">
                        <FaEye className="me-2" />
                        Form Preview - How annotators will see this form
                      </h6>
                    </div>
                    <div className="card-body">
                      <div className="alert alert-info">
                        <strong>Preview Mode:</strong> This is how the form will appear to annotators. 
                        You can test the form by filling it out.
                      </div>
                      {formFields.map((field, index) => (
                        <DynamicField
                          key={`preview_${index}`}
                          config={field}
                          value={previewData[field.field_name]}
                          onChange={handlePreviewChange}
                        />
                      ))}
                      {Object.keys(previewData).length > 0 && (
                        <div className="mt-3">
                          <h6>Preview Data:</h6>
                          <pre className="bg-light p-2 rounded">
                            {JSON.stringify(previewData, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )} */}

              </div>
            )}

            {/* Submit Button */}
            {selectedProjectCode && (
              <div className="flex justify-between mt-4" id="submitContainer">
                <button 
                  type="button" 
                  className="btn btn-outline-secondary" 
                  onClick={() => onGoToStep(5)}
                >
                  Back to Strategy Selection
                </button>
                <button 
                  type="submit" 
                  className="btn btn-primary" 
                  disabled={loading || formFields.length === 0}
                >
                  <FaSave className="me-2" />
                  {loading ? 'Saving...' : formFields.length > 0 ? 'Save & Continue' : 'Add Questions First'}
                </button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}