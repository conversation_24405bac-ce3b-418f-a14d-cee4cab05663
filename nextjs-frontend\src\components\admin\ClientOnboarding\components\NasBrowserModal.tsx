// components/NasBrowserModal.tsx
import React from 'react';
import { FaFolder, FaFile, FaFolderOpen, FaCheckCircle } from 'react-icons/fa';
import { Directory, SelectionTarget } from '../types';
import { generateBreadcrumbs } from '../utils/helpers';

interface NasBrowserModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBrowsePath: string;
  currentSelection: string;
  isSelectingFile: boolean;
  directoryContents: Directory[];
  isLoadingDirectory?: boolean;
  onSelectItem: (item: Directory) => void;
  onBreadcrumbClick: (path: string) => void;
  onSelectPath: () => void;
  currentSelectionTarget: SelectionTarget;
}

export const NasBrowserModal: React.FC<NasBrowserModalProps> = ({
  isOpen,
  onClose,
  currentBrowsePath,
  currentSelection,
  isSelectingFile,
  directoryContents,
  isLoadingDirectory = false,
  onSelectItem,
  onBreadcrumbClick,
  onSelectPath,
  currentSelectionTarget,
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal fade show d-block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">
              <FaFolderOpen className="me-2" />
              NAS File Browser
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            {/* Selection mode indicator */}
            <div className="alert alert-info py-2 mb-3">
              <small className="text-info">
                <strong>Mode:</strong> {isSelectingFile ? 'Select a file' : 'Select a folder'}
              </small>
            </div>
            
            {/* Breadcrumb navigation */}
            <nav aria-label="breadcrumb" className="mb-3">
              <ol className="breadcrumb mb-0">
                {generateBreadcrumbs(currentBrowsePath).map((crumb, index) => (
                  <li key={index} className="breadcrumb-item">
                    <button 
                      className="btn btn-link p-0 text-decoration-none"
                      onClick={() => onBreadcrumbClick(crumb.path)}
                    >
                      {crumb.name}
                    </button>
                  </li>
                ))}
              </ol>
            </nav>
            
            {/* Directory contents */}
            <div className="border rounded p-3 mb-3" style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {isLoadingDirectory ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2 text-muted">Loading directory contents...</p>
                </div>
              ) : directoryContents.length === 0 ? (
                <p className="text-center text-muted py-4">This directory is empty.</p>
              ) : (
                <div className="list-group">
                  {directoryContents.map((item, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`list-group-item list-group-item-action d-flex align-items-center ${
                        (isSelectingFile && item.type === 'file' && currentSelection === item.path) ||
                        (!isSelectingFile && currentBrowsePath === item.path)
                          ? 'active'
                          : ''
                      }`}
                      onClick={() => onSelectItem(item)}
                      disabled={!isSelectingFile && item.type === 'file'}
                    >
                      {item.type === 'directory' ? (
                        <FaFolder className="me-2 text-primary" />
                      ) : (
                        <FaFile className="me-2 text-secondary" />
                      )}
                      {item.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Current selection display */}
            <div className="mb-3">
              <label className="form-label">Selected Path:</label>
              <div className="form-control" style={{ fontSize: '0.875rem' }}>
                {isSelectingFile ? currentSelection || currentBrowsePath : currentBrowsePath}
              </div>
            </div>
          </div>
          
          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={onClose}
            >
              Cancel
            </button>
            <button 
              type="button" 
              className="btn btn-primary"
              onClick={onSelectPath}
              disabled={isSelectingFile && !currentSelection}
            >
              <FaCheckCircle className="me-1" />
              Select {isSelectingFile ? 'File' : 'Folder'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};