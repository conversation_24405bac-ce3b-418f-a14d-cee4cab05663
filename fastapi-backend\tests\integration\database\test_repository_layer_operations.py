"""
Integration tests for Repository Layer Database operations with REAL database operations.
Tests direct repository methods and database access patterns.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual repository classes from repositories/ directory
- Database operations managed by repository layer patterns
- Multi-database coordination through ProjectDBManager
- Complex query logic and transaction management tested
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func
import json
import time
from datetime import datetime

from app.repositories.batch_assignment_repository import BatchAssignmentRepository
from app.repositories.project_db_repository import ProjectDBRepository
from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.file_allocations import FileAllocations, WorkflowPhase
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.allocation_models.project_users import ProjectUsers
from app.post_db.master_models.users import users, UserRole
from app.services.auth_service import AuthService
from app.schemas.UserSchemas import UserRegisterRequest

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def repository_test_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up repository test environment with complete data setup."""
    # Use factory to create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Create additional test users for repository testing
    test_users = []
    for i in range(3):
        user_data = test_factory.users.create_user_register_request(role="annotator")
        success, user = await AuthService.register_user(test_master_db, user_data)
        assert success
        test_users.append(user)
    
    # Create test batches with different configurations
    test_batches = []
    for i in range(4):
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier=f"REPO_TEST_BATCH_{i+1}_{int(time.time())}",
            total_files=5 + i,  # Different sizes
            annotation_count=2,
            assignment_count=i % 2,  # Some assigned, some not
            is_priority=(i == 0),  # First batch is priority
            batch_status=BatchStatus.CREATED if i < 2 else BatchStatus.ALLOCATED
        )
        test_db.add(batch)
        test_batches.append(batch)
    
    await test_db.commit()
    for batch in test_batches:
        await test_db.refresh(batch)
    
    # Create files for each batch
    test_files = []
    for batch in test_batches:
        for j in range(batch.total_files):
            file = test_factory.files.create_files_registry(
                batch.id,
                file_identifier=f"repo_file_{batch.id}_{j+1}.jpg",
                file_type=FileType.IMAGE,
                file_size_bytes=1024 * (j + 1)
            )
            test_db.add(file)
            test_files.append(file)
    
    await test_db.commit()
    for file in test_files:
        await test_db.refresh(file)
    
    # Create project users
    project_users = []
    for user in test_users:
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username,
            current_batch=test_batches[0].id if user == test_users[0] else None
        )
        test_db.add(project_user)
        project_users.append(project_user)
    
    await test_db.commit()
    for project_user in project_users:
        await test_db.refresh(project_user)
    
    environment.update({
        "test_users": test_users,
        "test_batches": test_batches,
        "test_files": test_files,
        "project_users": project_users
    })
    
    return environment


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.repository       # Feature marker
@pytest.mark.smoke            # Suite marker - Core repository functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestBatchAssignmentRepository:
    """SMOKE TEST SUITE: Critical batch assignment repository operations."""
    
    @pytest.mark.asyncio
    async def test_get_user_current_batch_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting user's current batch with REAL database operations."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_users = repository_test_environment["test_users"]
        test_batches = repository_test_environment["test_batches"]
        
        # Test user with current batch
        user_with_batch = test_users[0]
        current_batch = await repository.get_user_current_batch(
            project.project_code, 
            user_with_batch.id
        )
        
        # Should return the assigned batch ID
        assert current_batch == test_batches[0].id
        
        # Test user without current batch
        user_without_batch = test_users[1]
        no_batch = await repository.get_user_current_batch(
            project.project_code,
            user_without_batch.id
        )
        
        # Should return None for user without assigned batch
        assert no_batch is None
    
    @pytest.mark.asyncio
    async def test_get_available_batches_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting available batches with REAL database operations."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get available batches
        available_batches = await repository.get_available_batches(project.project_code)
        
        # Should return batches where assignment_count < annotation_count
        assert len(available_batches) >= 2  # At least some should be available
        
        for batch in available_batches:
            assert batch.assignment_count < batch.annotation_count
            assert isinstance(batch, AllocationBatches)
            assert batch.id in [b.id for b in test_batches]
        
        # Verify ordering (should be ordered by batch_id)
        batch_ids = [b.id for b in available_batches]
        assert batch_ids == sorted(batch_ids)
    
    @pytest.mark.asyncio
    async def test_get_batch_files_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting batch files with REAL database operations."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        test_files = repository_test_environment["test_files"]
        
        # Test getting files for a specific batch
        target_batch = test_batches[0]
        batch_files = await repository.get_batch_files(
            project.project_code,
            target_batch.id
        )
        
        # Should return files for the specified batch
        assert len(batch_files) == target_batch.total_files
        
        for file in batch_files:
            assert file.batch_id == target_batch.id
            assert isinstance(file, FilesRegistry)
            assert file.id in [f.id for f in test_files]
        
        # Verify ordering (should be ordered by file ID)
        file_ids = [f.id for f in batch_files]
        assert file_ids == sorted(file_ids)
    
    @pytest.mark.asyncio
    async def test_find_next_annotator_slot_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test finding next annotator slot with REAL database operations."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get a batch with available slots
        target_batch = test_batches[0]
        
        # Find next available slot
        next_slot = await repository.find_next_annotator_slot(
            project.project_code,
            target_batch.id
        )
        
        # Should return a valid slot number or None if all filled
        if next_slot is not None:
            assert isinstance(next_slot, int)
            assert 1 <= next_slot <= target_batch.annotation_count
    
    @pytest.mark.asyncio
    async def test_assign_user_to_batch_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test assigning user to batch with REAL database operations."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_users = repository_test_environment["test_users"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get an available batch and user
        target_batch = test_batches[1]  # Use second batch to avoid conflicts
        target_user = test_users[1]
        
        # Reset batch assignment count for clean test
        target_batch.assignment_count = 0
        test_db.add(target_batch)
        await test_db.commit()
        
        # Assign user to batch
        assignment_success = await repository.assign_user_to_batch(
            project.project_code,
            target_user.id,
            target_user.username,
            target_batch.id,
            1,  # Annotator slot 1
            target_batch.total_files
        )
        
        # Should succeed
        assert assignment_success is True
        
        # Verify assignment in database
        # Check allocation_batches table
        stmt = select(AllocationBatches).where(AllocationBatches.id == target_batch.id)
        result = await test_db.execute(stmt)
        updated_batch = result.scalar_one_or_none()
        
        assert updated_batch is not None
        assert updated_batch.assignment_count == 1
        
        # Check user_allocations table
        stmt = select(UserAllocations).where(
            UserAllocations.user_id == target_user.id,
            UserAllocations.batch_id == target_batch.id,
            UserAllocations.is_active == True
        )
        result = await test_db.execute(stmt)
        user_allocation = result.scalar_one_or_none()
        
        assert user_allocation is not None
        assert user_allocation.username == target_user.username
        assert user_allocation.allocation_role == AllocationRole.ANNOTATOR
        assert user_allocation.total_files == target_batch.total_files
    
    @pytest.mark.asyncio
    async def test_get_batch_with_files_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting batch with files for frontend display."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get batch with files
        target_batch = test_batches[0]
        batch_with_files = await repository.get_batch_with_files(
            project.project_code,
            target_batch.id
        )
        
        # Should return detailed batch information
        if batch_with_files:
            assert batch_with_files["batch_id"] == target_batch.id
            assert batch_with_files["batch_identifier"] == target_batch.batch_identifier
            assert batch_with_files["total_files"] == target_batch.total_files
            assert "files" in batch_with_files
            assert isinstance(batch_with_files["files"], list)
            assert len(batch_with_files["files"]) == target_batch.total_files
            
            # Verify file structure
            for file_data in batch_with_files["files"]:
                assert "id" in file_data
                assert "filename" in file_data
                assert "file_identifier" in file_data
                assert "file_type" in file_data
                assert "storage_location" in file_data


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.repository       # Feature marker
@pytest.mark.smoke            # Suite marker - Core repository functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestProjectDBRepository:
    """SMOKE TEST SUITE: Critical project database repository operations."""
    
    @pytest.mark.asyncio
    async def test_create_allocation_batch_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test creating allocation batch with REAL database operations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        
        # Prepare batch data
        batch_data = {
            'batch_identifier': f'REPO_CREATE_TEST_{int(time.time())}',
            'total_files': 10,
            'file_list': ['file1.jpg', 'file2.jpg', 'file3.jpg'],
            'is_priority': True,
            'annotation_count': 3,
            'custom_batch_config': {'test_config': True}
        }
        
        # Create batch through repository
        created_batch = await repository.create_allocation_batch(
            project.project_code,
            batch_data
        )
        
        # Verify batch creation
        assert created_batch['id'] is not None
        assert created_batch['batch_identifier'] == batch_data['batch_identifier']
        assert created_batch['total_files'] == batch_data['total_files']
        assert created_batch['batch_status'] == BatchStatus.CREATED
        assert 'created_at' in created_batch
    
    @pytest.mark.asyncio
    async def test_get_allocation_batches_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting all allocation batches with REAL database operations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get all batches
        all_batches = await repository.get_allocation_batches(project.project_code)
        
        # Should return all existing batches
        assert len(all_batches) >= len(test_batches)
        
        # Verify batch structure
        for batch_data in all_batches:
            assert 'id' in batch_data
            assert 'batch_identifier' in batch_data
            assert 'batch_status' in batch_data
            assert 'total_files' in batch_data
            assert 'created_at' in batch_data
            
            # Should match one of our test batches
            matching_batch = next(
                (b for b in test_batches if b.id == batch_data['id']), 
                None
            )
            if matching_batch:
                assert batch_data['batch_identifier'] == matching_batch.batch_identifier
                assert batch_data['total_files'] == matching_batch.total_files
    
    @pytest.mark.asyncio
    async def test_register_files_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test registering files with REAL database operations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Use an existing batch for file registration
        target_batch = test_batches[0]
        
        # Prepare files data
        files_data = []
        for i in range(3):
            file_data = {
                'file_identifier': f'repo_register_file_{i+1}_{int(time.time())}.jpg',
                'original_filename': f'register_test_{i+1}.jpg',
                'file_type': 'image',
                'file_extension': '.jpg',
                'storage_location': {
                    'type': 'ftp',
                    'path': f'/test/register_{i+1}.jpg'
                },
                'file_size_bytes': 1024 * (i + 1),
                'file_hash': f'hash_{i+1}_{int(time.time())}',
                'sequence_order': i + 1
            }
            files_data.append(file_data)
        
        # Register files through repository
        registered_files = await repository.register_files(
            project.project_code,
            target_batch.id,
            files_data
        )
        
        # Verify file registration
        assert len(registered_files) == len(files_data)
        
        for i, file_info in enumerate(registered_files):
            assert file_info['id'] is not None
            assert file_info['batch_id'] == target_batch.id
            assert file_info['file_identifier'] == files_data[i]['file_identifier']
            assert file_info['original_filename'] == files_data[i]['original_filename']
            assert file_info['file_type'] == files_data[i]['file_type']
            assert 'file_allocation_id' in file_info
        
        # Verify files exist in database
        for file_info in registered_files:
            stmt = select(FilesRegistry).where(FilesRegistry.id == file_info['id'])
            result = await test_db.execute(stmt)
            db_file = result.scalar_one_or_none()
            
            assert db_file is not None
            assert db_file.batch_id == target_batch.id
            assert db_file.file_identifier == file_info['file_identifier']
    
    @pytest.mark.asyncio
    async def test_get_files_by_batch_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test getting files by batch with REAL database operations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        test_files = repository_test_environment["test_files"]
        
        # Get files for specific batch
        target_batch = test_batches[0]
        batch_files = await repository.get_files_by_batch(
            project.project_code,
            target_batch.id
        )
        
        # Should return files for the specified batch
        expected_files = [f for f in test_files if f.batch_id == target_batch.id]
        assert len(batch_files) == len(expected_files)
        
        # Verify file data structure
        for file_data in batch_files:
            assert 'id' in file_data
            assert 'batch_id' in file_data
            assert file_data['batch_id'] == target_batch.id
            assert 'file_identifier' in file_data
            assert 'original_filename' in file_data
            assert 'file_type' in file_data
            assert 'storage_location' in file_data
            assert 'uploaded_at' in file_data
    
    @pytest.mark.asyncio
    async def test_update_batch_status_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test updating batch status with REAL database operations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Update batch status
        target_batch = test_batches[0]
        original_status = target_batch.batch_status
        new_status = BatchStatus.COMPLETED
        
        # Update through repository
        updated_batch = await repository.update_batch_status(
            project.project_code,
            target_batch.id,
            new_status
        )
        
        # Verify update response
        assert updated_batch['id'] == target_batch.id
        assert updated_batch['batch_status'] == new_status
        
        # Verify update in database
        stmt = select(AllocationBatches).where(AllocationBatches.id == target_batch.id)
        result = await test_db.execute(stmt)
        db_batch = result.scalar_one_or_none()
        
        assert db_batch is not None
        assert db_batch.batch_status == new_status


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.repository       # Feature marker
@pytest.mark.regression       # Suite marker - Error scenarios
@pytest.mark.high             # Priority marker - P1 (error handling is critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestRepositoryErrorHandling:
    """REGRESSION TEST SUITE: Repository error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_repository_invalid_project_code_real_database(
        self,
        setup_test_database
    ):
        """Test repository operations with invalid project code."""
        repository = BatchAssignmentRepository()
        
        # Test with non-existent project code
        available_batches = await repository.get_available_batches("NONEXISTENT_PROJECT")
        
        # Should return empty list or handle gracefully
        assert available_batches == []
        
        # Test getting user batch with invalid project
        user_batch = await repository.get_user_current_batch("NONEXISTENT_PROJECT", 123)
        
        # Should return None
        assert user_batch is None
    
    @pytest.mark.asyncio
    async def test_repository_database_constraint_violations_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test repository handling of database constraint violations."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Try to create batch with duplicate identifier
        existing_batch = test_batches[0]
        duplicate_batch_data = {
            'batch_identifier': existing_batch.batch_identifier,  # Duplicate
            'total_files': 5,
            'file_list': ['file1.jpg'],
            'annotation_count': 1
        }
        
        # Should raise exception due to unique constraint
        with pytest.raises(Exception):
            await repository.create_allocation_batch(
                project.project_code,
                duplicate_batch_data
            )
    
    @pytest.mark.asyncio
    async def test_repository_transaction_rollback_real_database(
        self,
        repository_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test repository transaction rollback scenarios."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        test_users = repository_test_environment["test_users"]
        test_batches = repository_test_environment["test_batches"]
        
        # Get initial state
        target_batch = test_batches[0]
        target_user = test_users[0]
        
        initial_assignment_count = target_batch.assignment_count
        
        # Try to assign user to batch with invalid slot
        # This should fail and rollback transaction
        assignment_result = await repository.assign_user_to_batch(
            project.project_code,
            target_user.id,
            target_user.username,
            target_batch.id,
            99,  # Invalid high slot number
            target_batch.total_files
        )
        
        # Assignment might fail
        if not assignment_result:
            # Verify no partial state changes occurred
            stmt = select(AllocationBatches).where(AllocationBatches.id == target_batch.id)
            result = await test_db.execute(stmt)
            unchanged_batch = result.scalar_one_or_none()
            
            # Assignment count should be unchanged
            assert unchanged_batch.assignment_count == initial_assignment_count


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.repository       # Feature marker
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Performance tests take time
@pytest.mark.bulk_data        # Environment marker - Large datasets
class TestRepositoryPerformance:
    """PERFORMANCE TEST SUITE: Repository performance with large datasets."""
    
    @pytest.mark.asyncio
    async def test_repository_bulk_operations_performance_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test repository bulk operations performance."""
        repository = ProjectDBRepository()
        project = repository_test_environment["project"]
        test_batches = repository_test_environment["test_batches"]
        
        # Create large batch for bulk file registration
        bulk_batch_data = {
            'batch_identifier': f'BULK_PERF_TEST_{int(time.time())}',
            'total_files': 100,
            'file_list': [f'bulk_file_{i}.jpg' for i in range(100)],
            'annotation_count': 1
        }
        
        # Measure batch creation performance
        start_time = time.time()
        created_batch = await repository.create_allocation_batch(
            project.project_code,
            bulk_batch_data
        )
        batch_creation_time = time.time() - start_time
        
        # Should complete within reasonable time
        assert batch_creation_time < 5.0, f"Batch creation took too long: {batch_creation_time}s"
        assert created_batch['total_files'] == 100
        
        # Prepare bulk files data
        bulk_files_data = []
        for i in range(50):  # Test with 50 files
            file_data = {
                'file_identifier': f'bulk_perf_file_{i}.jpg',
                'original_filename': f'bulk_file_{i}.jpg',
                'file_type': 'image',
                'file_size_bytes': 1024,
                'storage_location': {'type': 'local', 'path': f'/bulk/{i}.jpg'}
            }
            bulk_files_data.append(file_data)
        
        # Measure file registration performance
        start_time = time.time()
        registered_files = await repository.register_files(
            project.project_code,
            created_batch['id'],
            bulk_files_data
        )
        file_registration_time = time.time() - start_time
        
        # Should complete within reasonable time
        assert file_registration_time < 10.0, f"File registration took too long: {file_registration_time}s"
        assert len(registered_files) == 50
    
    @pytest.mark.asyncio
    async def test_repository_query_performance_real_database(
        self,
        repository_test_environment,
        setup_test_database
    ):
        """Test repository query performance with larger datasets."""
        repository = BatchAssignmentRepository()
        project = repository_test_environment["project"]
        
        # Measure available batches query performance
        start_time = time.time()
        available_batches = await repository.get_available_batches(project.project_code)
        query_time = time.time() - start_time
        
        # Should complete quickly
        assert query_time < 2.0, f"Available batches query took too long: {query_time}s"
        assert isinstance(available_batches, list)
        
        # Test multiple concurrent queries
        start_time = time.time()
        tasks = []
        import asyncio
        
        for _ in range(5):
            task = repository.get_available_batches(project.project_code)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        concurrent_query_time = time.time() - start_time
        
        # Concurrent queries should complete reasonably fast
        assert concurrent_query_time < 5.0, f"Concurrent queries took too long: {concurrent_query_time}s"
        assert len(results) == 5
        
        # All results should be consistent
        first_result = results[0]
        for result in results[1:]:
            assert len(result) == len(first_result)
