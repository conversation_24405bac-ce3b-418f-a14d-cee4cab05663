'use client';

import { FaRobot, FaSync } from 'react-icons/fa';

interface ModelInfo {
  id: string;
  name: string;
  description: string;
}

interface NonReferenceSyntheticDataFormProps {
  userQuery: string;
  setUserQuery: (query: string) => void;
  datasetType: string;
  setDatasetType: (type: string) => void;
  numSamples: number;
  setNumSamples: (num: number) => void;
  selectedModel: string;
  setSelectedModel: (model: string) => void;
  availableModels: ModelInfo[];
  datasetTypes: Record<string, string>;
  onGenerate: () => void;
  isGenerating: boolean;
}

export default function NonReferenceSyntheticDataForm({
  userQuery,
  setUserQuery,
  datasetType,
  setDatasetType,
  numSamples,
  setNumSamples,
  selectedModel,
  setSelectedModel,
  availableModels,
  datasetTypes,
  onGenerate,
  isGenerating
}: NonReferenceSyntheticDataFormProps) {

  // Removed getDatasetTypeIcon as it's unused

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div className="space-y-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">What would you like to generate?</label>
          <textarea
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            rows={3}
            value={userQuery}
            onChange={(e) => setUserQuery(e.target.value)}
            placeholder="Enter your query or topic here..."
          />
        </div>
        
        <div>
          <label className="block text-gray-700 font-medium mb-2">Dataset Type</label>
          <div className="relative">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary appearance-none"
              value={datasetType}
              onChange={(e) => setDatasetType(e.target.value)}
            >
              {Object.entries(datasetTypes).map(([type, description]) => (
                <option key={type} value={type}>
                  {description}
                </option>
              ))}
            </select>
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              {/* could add an icon here if needed */}
            </div>
          </div>
        </div>
              
        <div>
          <label className="block text-gray-700 font-medium mb-2">Number of Samples</label>
          <input
            type="number"
            min={1}
            max={10}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            value={numSamples}
            onChange={(e) => setNumSamples(parseInt(e.target.value) || 3)}
          />
          <p className="text-sm text-gray-500 mt-1">Recommended: 1-5 samples. Larger values may take longer to generate.</p>
        </div>
      </div>
          
      <div className="space-y-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">Select Model</label>
          <div className="space-y-3">
            {availableModels.map((model) => (
              <div key={model.id} className="flex items-start">
                <input
                  type="radio"
                  id={`model-${model.id}`}
                  name="model"
                  value={model.id}
                  checked={selectedModel === model.id}
                  onChange={() => setSelectedModel(model.id)}
                  className="mt-1"
                />
                <label htmlFor={`model-${model.id}`} className="ml-2">
                  <div className="font-medium">{model.name}</div>
                  <div className="text-sm text-gray-500">{model.description}</div>
                </label>
              </div>
            ))}
          </div>
        </div>
              
        <div className="flex justify-center mt-auto pt-6">
          <button 
            className="flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:bg-gray-300 disabled:cursor-not-allowed"
            onClick={onGenerate}
            disabled={isGenerating || !userQuery}
          >
            {isGenerating ? (
              <>
                <FaSync className="animate-spin mr-2" />
                Generating...
              </>
            ) : (
              <>
                <FaRobot className="mr-2" />
                Generate Dataset
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
