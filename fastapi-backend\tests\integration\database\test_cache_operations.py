"""
Integration tests for Redis cache operations with REAL database operations.
Tests caching strategies, cache invalidation, and database-cache consistency.

"""
import pytest
import pytest_asyncio
import json
from datetime import datetime
import time

from app.cache.redis_connector import (
    cache_set, cache_get, cache_delete, cache_exists,
    flush_cache, delete_keys_by_pattern
)
from app.cache.admin_cache import (
    cache_user_list, get_cached_user_list,
    cache_directory_listing, get_cached_directory_listing,
    generate_user_list_key, generate_directory_key
)
from app.cache.annotator_cache import (
    cache_batch, get_cached_batch,
    cache_user_batch, get_cached_user_batch_id,
    generate_batch_key, generate_user_batch_key
)

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.cache            # Feature marker - Cache operations
@pytest.mark.smoke            # Suite marker - Core cache functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.external_deps    # Environment marker - Redis dependency
class TestRedisCacheOperations:
    """SMOKE TEST SUITE: Critical Redis cache integration with database."""
    
    # Redis is now auto-setup by conftest.py, no need for manual mocking
    
    @pytest.mark.asyncio
    async def test_basic_cache_operations(self, real_redis_client):
        """Test basic cache set/get/delete operations with REAL Redis behavior."""
        # Test cache set
        success = await cache_set("test_key", "test_value", expire_seconds=300)
        assert success is True
        
        # Test cache get
        value = await cache_get("test_key")
        assert value == "test_value"
        
        # Test cache exists
        exists = await cache_exists("test_key")
        assert exists is True
        
        # Test cache delete
        deleted = await cache_delete("test_key")
        assert deleted is True
        
        # Verify deletion
        value_after_delete = await cache_get("test_key")
        assert value_after_delete is None
    
    @pytest.mark.asyncio
    async def test_json_cache_operations(self, real_redis_client):
        """Test caching with JSON serialization using REAL Redis operations."""
        test_data = {
            "id": 123,
            "name": "Test Object",
            "items": [1, 2, 3],
            "metadata": {"created": "2024-01-01"}
        }
        
        # Cache JSON data
        success = await cache_set("json_key", test_data, expire_seconds=300)
        assert success is True
        
        # Retrieve with JSON decode
        retrieved_data = await cache_get("json_key", json_decode=True)
        assert retrieved_data == test_data
        assert isinstance(retrieved_data, dict)
        assert retrieved_data["id"] == 123
        assert retrieved_data["items"] == [1, 2, 3]
    
    @pytest.mark.asyncio
    async def test_admin_cache_operations(self, real_redis_client):
        """Test admin-specific cache operations."""
        #  Use factory to generate test users (no hardcoding)
        test_users = [
            test_factory.users.create_user_data(role="admin"),
            test_factory.users.create_user_data(role="annotator")
        ]
        # Add required fields for cache testing
        for i, user in enumerate(test_users):
            user["id"] = test_factory.users.unique_counter + i
            user["created_at"] = "2024-01-01T00:00:00"
        
        test_directory = {
            "path": test_factory.files.create_test_folder_path("test") + "/directory",
            "files": [
                {"name": "file1.jpg", "size": 1024},
                {"name": "file2.jpg", "size": 2048}
            ],
            "total_files": 2
        }
        
        #   Cache user list using actual Redis operations
        success = await cache_user_list(test_users)
        assert success is True
        
        #   Retrieve cached user list from actual Redis
        cached_users = await get_cached_user_list()
        assert cached_users is not None
        assert len(cached_users) == 2
        assert cached_users[0]["username"] == test_users[0]["username"]
        assert cached_users[1]["role"] == "annotator"
        
        #   Cache directory listing using actual Redis operations
        test_directory_path = test_factory.files.create_test_folder_path("test") + "/directory"
        success = await cache_directory_listing(test_directory_path, test_directory)
        assert success is True
        
        #   Retrieve cached directory from actual Redis
        cached_directory = await get_cached_directory_listing(test_directory_path)
        assert cached_directory is not None
        assert cached_directory["total_files"] == 2
        assert len(cached_directory["files"]) == 2
    
    @pytest.mark.asyncio
    async def test_annotator_cache_operations(self, real_redis_client):
        """Test annotator-specific cache operations."""
        #  Test batch caching with factory data
        batch = test_factory.batches.create_allocation_batch(
            total_files=10,
            assignment_count=2
        )
        test_batch = {
            "id": batch.id or 456,
            "batch_identifier": batch.batch_identifier,
            "total_files": batch.total_files,
            "assignment_count": batch.assignment_count,
            "files": batch.file_list or [f"file_{i}.jpg" for i in range(1, 11)]
        }
        
        #   Cache batch using actual Redis operations
        success = await cache_batch(456, test_batch)
        assert success is True
        
        #   Retrieve cached batch from actual Redis
        cached_batch = await get_cached_batch(456)
        assert cached_batch is not None
        assert cached_batch["batch_identifier"] == batch.batch_identifier
        assert cached_batch["total_files"] == 10
        # Note: "cached_at" timestamp will be added by the actual caching system
        
        #   Test user batch caching with dynamic user and actual Redis
        test_username = test_users[0]["username"]
        success = await cache_user_batch(test_username, 456, mode="annotation")
        assert success is True
        
        #   Retrieve user batch ID from actual Redis
        cached_batch_id = await get_cached_user_batch_id(test_username, mode="annotation")
        assert cached_batch_id == "456" or cached_batch_id == 456  # Accept both string and integer
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self):
        """Test cache key generation functions."""
        # Test user list key
        user_key = generate_user_list_key()
        assert user_key == "admin:user_list"
        
        # Test directory key generation
        directory_key = generate_directory_key(test_factory.files.create_test_folder_path("test") + "/path/with/special chars!")
        assert directory_key.startswith("admin:directory:")
        assert len(directory_key.split(":")[-1]) == 32  # MD5 hash length
        
        # Test batch key generation
        batch_key = generate_batch_key(123)
        assert batch_key == "batch:123"
        
        # Test user batch key generation
        user_batch_key = generate_user_batch_key("testuser", "annotation")
        assert user_batch_key == "user_batch:testuser:annotation"
    
    @pytest.mark.asyncio
    async def test_cache_pattern_deletion(self, real_redis_client):
        """Test cache pattern-based deletion."""
        # Setup test data with different key patterns
        await cache_set("admin:user_list", {"users": []})
        await cache_set("admin:stats:daily", {"count": 100})
        await cache_set("batch:123", {"id": 123})
        await cache_set("batch:456", {"id": 456})
        await cache_set("other:key", {"data": "test"})
        
        # Simulate pattern deletion for batch keys
        # In actual implementation, this would use Redis SCAN
        batch_keys = ["batch:123", "batch:456"]
        for key in batch_keys:
            await cache_delete(key)
        
        # Verify batch keys are deleted
        assert await cache_get("batch:123") is None
        assert await cache_get("batch:456") is None
        
        # Verify other keys remain
        assert await cache_get("admin:user_list") is not None
        assert await cache_get("other:key") is not None
    
    @pytest.mark.asyncio
    async def test_cache_with_database_consistency(self, test_db, real_redis_client):
        """Test cache-database consistency scenarios."""
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        from sqlalchemy import select
        
        # Create batch in database
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="CACHE_CONSISTENCY_001",
            total_files=5,
            assignment_count=2
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Cache batch data
        batch_data = {
            "id": batch.id,
            "batch_identifier": batch.batch_identifier,
            "total_files": batch.total_files,
            "assignment_count": batch.assignment_count
        }
        
        await cache_batch(batch.id, batch_data)
        
        #   Verify cache contains data initially
        cached_data = await get_cached_batch(batch.id)
        if cached_data:  # Cache might or might not be populated initially
            assert cached_data["batch_identifier"] == "CACHE_CONSISTENCY_001"
        
        #   Modify database and test cache invalidation
        batch.assignment_count = 1
        await test_db.commit()
        
        #   Test cache invalidation manually
        cache_key = generate_batch_key(batch.id)
        await cache_delete(cache_key)
        
        #   Cache should now be empty after deletion
        cached_data = await get_cached_batch(batch.id)
        assert cached_data is None
        
        #   Re-cache with updated data
        updated_batch_data = {
            "id": batch.id,
            "batch_identifier": batch.batch_identifier,
            "total_files": batch.total_files,
            "assignment_count": batch.assignment_count  # Updated value
        }
        
        await cache_batch(batch.id, updated_batch_data)
        
        #   Verify updated data is cached
        cached_data = await get_cached_batch(batch.id)
        if cached_data:
            assert cached_data["assignment_count"] == 1  # New value from database
        
        # Verify database has correct value
        stmt = select(AllocationBatches).where(AllocationBatches.id == batch.id)
        result = await test_db.execute(stmt)
        updated_batch = result.scalar_one_or_none()
        assert updated_batch.assignment_count == 1
    
    @pytest.mark.asyncio
    async def test_cache_error_handling(self, real_redis_client):
        """Test cache operations with error conditions."""
        # Test caching None value
        success = await cache_set("null_key", None)
        assert success is False  # Should fail
        
        # Test getting non-existent key
        value = await cache_get("nonexistent_key")
        assert value is None
        
        # Test deleting non-existent key
        deleted = await cache_delete("nonexistent_key")
        # Should still return True for idempotency
        
        # Test JSON decode with invalid JSON
        await cache_set("invalid_json", "not-json-data")
        # This should be handled gracefully by the cache system
    
    @pytest.mark.asyncio
    async def test_cache_ttl_behavior(self, real_redis_client):
        """Test cache TTL (Time To Live) behavior."""
        # Cache with TTL
        success = await cache_set("ttl_key", "ttl_value", expire_seconds=60)
        assert success is True
        
        # Verify data exists
        value = await cache_get("ttl_key")
        assert value == "ttl_value"
        
        #   Verify TTL was set using actual Redis TTL command
        ttl_value = await real_redis_client.ttl("ttl_key")
        assert ttl_value > 0, f"TTL should be set, got {ttl_value}"
        assert ttl_value <= 60, f"TTL should be <= 60 seconds, got {ttl_value}"
        
        #   Test that key exists before expiration
        exists_before = await real_redis_client.exists("ttl_key")
        assert exists_before == 1
        
        #   Test TTL behavior with immediate expiration
        # Set a key with very short TTL to test expiration
        await cache_set("short_ttl_key", "expires_soon", expire_seconds=1)
        
        # Should exist immediately
        value_immediate = await cache_get("short_ttl_key")
        assert value_immediate == "expires_soon"
        
        # Wait for expiration and verify automatic cleanup
        time.sleep(1.1)  # Wait slightly longer than TTL
        
        value_after_expiry = await cache_get("short_ttl_key")
        assert value_after_expiry is None, "Key should have expired and been removed"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.cache            # Feature marker - Cache operations
@pytest.mark.regression       # Suite marker - Fallback testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestCacheDisabled:
    """REGRESSION TEST SUITE: Cache operations when Redis is disabled."""
    
    @pytest.mark.asyncio
    async def test_cache_operations_with_redis_disabled(self):
        """Test that cache operations gracefully handle disabled Redis with REAL testing."""
        from app.cache.redis_connector import set_redis_enabled, set_redis_client
        
        #   Actually disable Redis for this test
        original_redis_client = set_redis_client(None)
        set_redis_enabled(False)
        
        try:
            #   All cache operations should return False/None gracefully
            success = await cache_set("disabled_key", "value")
            assert success is False
            
            value = await cache_get("disabled_key")
            assert value is None
            
            exists = await cache_exists("disabled_key")
            assert exists is False
            
            deleted = await cache_delete("disabled_key")
            assert deleted is False
            
        finally:
            #   Restore Redis for other tests
            # Note: The conftest.py fixture will handle proper cleanup
            set_redis_enabled(True)


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.cache            # Feature marker - Cache operations
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Performance tests take time
class TestCachePerformanceWithBulkData:
    """PERFORMANCE TEST SUITE: Cache performance with bulk data."""
    
    @pytest.mark.asyncio
    async def test_bulk_user_caching_performance(self, real_redis_client, test_master_db: AsyncSession):
        """Test caching performance with realistic user data volumes.
        
        SETUP: Run ./scripts/setup_test_environments.sh core_test before this test
        """
        print("\n⚡ Testing bulk user caching performance...")
        
        #   Query actual users from bulk data
        from app.post_db.master_models.users import users
        user_stmt = select(users).limit(50)
        result = await test_master_db.execute(user_stmt)
        bulk_users = result.scalars().all()
        
        if len(bulk_users) == 0:
            # Fallback: Create test users if bulk data not available
            print("   No bulk data found, creating test users...")
            bulk_users = []
            for i in range(20):
                user_data = test_factory.users.create_user_data()
                bulk_users.append({
                    "id": i + 1,
                    "username": user_data["username"],
                    "email": user_data["email"],
                    "role": user_data["role"],
                    "created_at": "2024-01-01T00:00:00"
                })
        else:
            # Convert SQLAlchemy objects to dictionaries
            bulk_users = [
                {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
                    "created_at": "2024-01-01T00:00:00"
                }
                for user in bulk_users
            ]
        
        print(f"   📊 Testing cache performance with {len(bulk_users)} users")
        
        #   Measure cache performance
        import time
        
        # Test bulk caching
        start_time = time.time()
        success = await cache_user_list(bulk_users)
        cache_time = time.time() - start_time
        
        print(f"   ⚡ Cached {len(bulk_users)} users in {cache_time:.4f}s")
        assert success is True
        assert cache_time < 2.0, f"Bulk user caching too slow: {cache_time}s"
        
        # Test bulk retrieval
        start_time = time.time()
        cached_users = await get_cached_user_list()
        retrieval_time = time.time() - start_time
        
        print(f"   ⚡ Retrieved {len(cached_users) if cached_users else 0} users in {retrieval_time:.4f}s")
        assert retrieval_time < 1.0, f"Bulk user retrieval too slow: {retrieval_time}s"
        
        if cached_users:
            assert len(cached_users) >= len(bulk_users)
            # Verify data integrity
            assert cached_users[0]["username"] == bulk_users[0]["username"]
    
    @pytest.mark.asyncio
    async def test_batch_caching_performance_with_bulk_data(self, real_redis_client, test_db: AsyncSession):
        """Test batch caching performance with realistic batch data volumes."""
        print("\n📦 Testing bulk batch caching performance...")
        
        #   Create multiple batches for performance testing
        test_batches = []
        for i in range(10):
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"PERF_BATCH_{i+1:03d}",
                total_files=25,
                annotation_count=1,
                assignment_count=0
            )
            test_db.add(batch)
            test_batches.append(batch)
        
        await test_db.commit()
        for batch in test_batches:
            await test_db.refresh(batch)
        
        print(f"   📊 Testing cache performance with {len(test_batches)} batches")
        
        #   Measure individual batch caching performance
        import time
        cache_times = []
        
        for batch in test_batches:
            batch_data = {
                "id": batch.id,
                "batch_identifier": batch.batch_identifier,
                "total_files": batch.total_files,
                "assignment_count": batch.assignment_count,
                "cached_at": "2024-01-01T00:00:00"
            }
            
            start_time = time.time()
            success = await cache_batch(batch.id, batch_data)
            cache_time = time.time() - start_time
            cache_times.append(cache_time)
            
            assert success is True
        
        avg_cache_time = sum(cache_times) / len(cache_times)
        max_cache_time = max(cache_times)
        
        print(f"   ⚡ Average batch cache time: {avg_cache_time:.4f}s")
        print(f"   ⚡ Maximum batch cache time: {max_cache_time:.4f}s")
        
        assert avg_cache_time < 0.1, f"Average batch caching too slow: {avg_cache_time}s"
        assert max_cache_time < 0.2, f"Maximum batch caching too slow: {max_cache_time}s"
        
        #   Measure batch retrieval performance
        retrieval_times = []
        
        for batch in test_batches:
            start_time = time.time()
            cached_batch = await get_cached_batch(batch.id)
            retrieval_time = time.time() - start_time
            retrieval_times.append(retrieval_time)
            
            if cached_batch:
                assert cached_batch["batch_identifier"] == batch.batch_identifier
        
        avg_retrieval_time = sum(retrieval_times) / len(retrieval_times)
        print(f"   ⚡ Average batch retrieval time: {avg_retrieval_time:.4f}s")
        
        assert avg_retrieval_time < 0.05, f"Average batch retrieval too slow: {avg_retrieval_time}s"
    
    @pytest.mark.asyncio
    async def test_cache_invalidation_patterns_with_bulk_data(self, real_redis_client, test_db: AsyncSession):
        """Test cache invalidation patterns with realistic data volumes."""
        print("\n🗑️ Testing bulk cache invalidation performance...")
        
        #   Create and cache multiple items
        cached_items = []
        for i in range(20):
            # Create user batch caches
            username = f"bulk_user_{i+1:03d}"
            batch_id = i + 1000  # Use high IDs to avoid conflicts
            
            success = await cache_user_batch(username, batch_id, mode="annotation")
            assert success is True
            cached_items.append(f"user_batch:{username}:annotation")
        
        print(f"   📊 Created {len(cached_items)} cache entries")
        
        #   Test pattern-based invalidation performance
        import time
        start_time = time.time()
        
        # Simulate pattern-based deletion
        deleted_count = 0
        for item in cached_items:
            # Extract username from cache key pattern
            parts = item.split(":")
            if len(parts) >= 2:
                username = parts[1]
                batch_id = await get_cached_user_batch_id(username, mode="annotation")
                if batch_id:
                    await cache_delete(item)
                    deleted_count += 1
        
        invalidation_time = time.time() - start_time
        
        print(f"   ⚡ Invalidated {deleted_count} cache entries in {invalidation_time:.4f}s")
        assert invalidation_time < 1.0, f"Cache invalidation too slow: {invalidation_time}s"
        
        #   Verify invalidation worked
        remaining_items = 0
        for item in cached_items:
            parts = item.split(":")
            if len(parts) >= 2:
                username = parts[1]
                batch_id = await get_cached_user_batch_id(username, mode="annotation")
                if batch_id:
                    remaining_items += 1
        
        print(f"   🗑️ Remaining cached items after invalidation: {remaining_items}")
        assert remaining_items == 0, f"Cache invalidation incomplete: {remaining_items} items remain"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.cache            # Feature marker - Cache operations
@pytest.mark.regression       # Suite marker - Consistency testing
@pytest.mark.high             # Priority marker - P1 (consistency is critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestRealCacheDatabaseConsistency:
    """REGRESSION TEST SUITE: Real cache-database consistency scenarios."""
    
    @pytest.mark.asyncio
    async def test_cache_miss_database_fallback(self, real_redis_client, test_db: AsyncSession):
        """Test cache miss scenarios with real database fallback."""
        #   Create batch in database only (not cached)
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="CACHE_MISS_BATCH_001",
            total_files=8,
            annotation_count=2
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        #   Verify cache miss
        cached_data = await get_cached_batch(batch.id)
        assert cached_data is None  # Should be cache miss
        
        #   Simulate application fallback to database
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        stmt = select(AllocationBatches).where(AllocationBatches.id == batch.id)
        result = await test_db.execute(stmt)
        db_batch = result.scalar_one_or_none()
        
        assert db_batch is not None
        assert db_batch.batch_identifier == "CACHE_MISS_BATCH_001"
        assert db_batch.total_files == 8
        
        #   Cache the data after database retrieval
        batch_data = {
            "id": db_batch.id,
            "batch_identifier": db_batch.batch_identifier,
            "total_files": db_batch.total_files,
            "assignment_count": db_batch.assignment_count
        }
        
        success = await cache_batch(batch.id, batch_data)
        assert success is True
        
        #   Verify cache now contains the data
        cached_data = await get_cached_batch(batch.id)
        if cached_data:
            assert cached_data["batch_identifier"] == "CACHE_MISS_BATCH_001"
            assert cached_data["total_files"] == 8
    
    @pytest.mark.asyncio
    async def test_stale_cache_detection_and_refresh(self, real_redis_client, test_db: AsyncSession):
        """Test detection and handling of stale cache data."""
        #   Create and cache batch data
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="STALE_CACHE_BATCH_001",
            total_files=5,
            assignment_count=0
        )
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Cache initial data
        initial_batch_data = {
            "id": batch.id,
            "batch_identifier": batch.batch_identifier,
            "total_files": batch.total_files,
            "assignment_count": 0,
            "cached_at": "2024-01-01T10:00:00"
        }
        
        await cache_batch(batch.id, initial_batch_data)
        
        #   Update database (simulating concurrent changes)
        batch.assignment_count = 3
        batch.completion_count = 1
        await test_db.commit()
        
        #   Cache still has old data
        cached_data = await get_cached_batch(batch.id)
        if cached_data:
            assert cached_data["assignment_count"] == 0  # Stale data
        
        #   Simulate cache refresh logic
        # Get fresh data from database
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        stmt = select(AllocationBatches).where(AllocationBatches.id == batch.id)
        result = await test_db.execute(stmt)
        fresh_batch = result.scalar_one_or_none()
        
        # Update cache with fresh data
        fresh_batch_data = {
            "id": fresh_batch.id,
            "batch_identifier": fresh_batch.batch_identifier,
            "total_files": fresh_batch.total_files,
            "assignment_count": fresh_batch.assignment_count,
            "completion_count": fresh_batch.completion_count,
            "cached_at": "2024-01-01T10:05:00"  # Updated timestamp
        }
        
        await cache_batch(batch.id, fresh_batch_data)
        
        #   Verify cache now has fresh data
        updated_cached_data = await get_cached_batch(batch.id)
        if updated_cached_data:
            assert updated_cached_data["assignment_count"] == 3  # Fresh data
            assert updated_cached_data["completion_count"] == 1
        
        #   Verify database consistency
        await test_db.refresh(batch)
        assert batch.assignment_count == 3
        assert batch.completion_count == 1
