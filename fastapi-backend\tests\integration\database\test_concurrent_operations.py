"""
Integration tests for Concurrent Database Operations with REAL database operations.
Tests race conditions, deadlocks, and concurrent access patterns.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual concurrent database operations without mocks
- Validates multi-user scenarios and race condition handling  
- Tests deadlock detection and prevention mechanisms
- Covers concurrent cache operations and invalidation patterns
"""
import pytest
import pytest_asyncio
import asyncio
import time
import random
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func, update
from sqlalchemy.exc import IntegrityError, OperationalError
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.repositories.batch_assignment_repository import BatchAssignmentRepository
from app.repositories.project_db_repository import ProjectDBRepository
from app.services.auth_service import AuthService
from app.services.annotator_batch_assignment_service import AnnotatorBatchAssignmentService
from app.cache.annotator_cache import AnnotatorCache
from app.cache.admin_cache import AdminCache

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.allocation_models.project_users import ProjectUsers
from app.post_db.master_models.users import users, UserRole

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def concurrent_test_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up environment for concurrent operations testing."""
    # Create complete environment with many users for concurrency testing
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Create multiple annotators for concurrent testing
    concurrent_users = []
    for i in range(10):  # 10 concurrent users
        user_data = test_factory.users.create_user_register_request(role="annotator")
        success, user = await AuthService.register_user(test_master_db, user_data)
        assert success
        concurrent_users.append(user)
    
    # Create multiple batches with limited slots to test race conditions
    concurrent_batches = []
    for i in range(5):  # 5 batches
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier=f"CONCURRENT_BATCH_{i+1}_{int(time.time())}",
            total_files=10,
            annotation_count=2,  # Only 2 slots per batch to create competition
            assignment_count=0,
            is_priority=(i == 0),
            batch_status=BatchStatus.CREATED
        )
        test_db.add(batch)
        concurrent_batches.append(batch)
    
    await test_db.commit()
    for batch in concurrent_batches:
        await test_db.refresh(batch)
    
    # Create files for each batch
    concurrent_files = []
    for batch in concurrent_batches:
        for j in range(batch.total_files):
            file = test_factory.files.create_files_registry(
                batch.id,
                file_identifier=f"concurrent_file_{batch.id}_{j+1}.jpg",
                file_type=FileType.IMAGE
            )
            test_db.add(file)
            concurrent_files.append(file)
    
    await test_db.commit()
    for file in concurrent_files:
        await test_db.refresh(file)
    
    # Add all users to project
    project_users = []
    for user in concurrent_users:
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username
        )
        test_db.add(project_user)
        project_users.append(project_user)
    
    await test_db.commit()
    for project_user in project_users:
        await test_db.refresh(project_user)
    
    environment.update({
        "concurrent_users": concurrent_users,
        "concurrent_batches": concurrent_batches,
        "concurrent_files": concurrent_files,
        "project_users": project_users
    })
    
    return environment


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.regression       # Suite marker - Concurrency testing
@pytest.mark.high             # Priority marker - P1 (concurrency issues are critical)
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Concurrent tests take time
class TestConcurrentBatchAssignment:
    """PERFORMANCE TEST SUITE: Concurrent batch assignment operations."""
    
    @pytest.mark.asyncio
    async def test_concurrent_batch_assignment_race_conditions_real_database(
        self,
        concurrent_test_environment,
        setup_test_database
    ):
        """Test race conditions in concurrent batch assignments."""
        repository = BatchAssignmentRepository()
        project = concurrent_test_environment["project"]
        concurrent_users = concurrent_test_environment["concurrent_users"]
        concurrent_batches = concurrent_test_environment["concurrent_batches"]
        
        # Use first batch with limited slots
        target_batch = concurrent_batches[0]
        max_assignments = target_batch.annotation_count  # Only 2 slots
        
        assignment_results = []
        assignment_errors = []
        
        async def attempt_assignment(user, slot_attempt):
            """Attempt to assign user to batch."""
            try:
                # Add random delay to increase race condition probability
                await asyncio.sleep(random.uniform(0.01, 0.05))
                
                success = await repository.assign_user_to_batch(
                    project.project_code,
                    user.id,
                    user.username,
                    target_batch.id,
                    slot_attempt,  # Try different slots
                    target_batch.total_files
                )
                
                return {
                    "user_id": user.id,
                    "username": user.username,
                    "success": success,
                    "slot_attempt": slot_attempt,
                    "timestamp": time.time()
                }
            
            except Exception as e:
                assignment_errors.append({
                    "user_id": user.id,
                    "error": str(e),
                    "slot_attempt": slot_attempt
                })
                return {
                    "user_id": user.id,
                    "success": False,
                    "error": str(e),
                    "slot_attempt": slot_attempt
                }
        
        # Start concurrent assignment attempts
        tasks = []
        for i, user in enumerate(concurrent_users[:6]):  # More users than slots
            slot = (i % max_assignments) + 1  # Alternate between slots 1 and 2
            task = attempt_assignment(user, slot)
            tasks.append(task)
        
        # Execute all assignments concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful assignments
        successful_assignments = [r for r in results if isinstance(r, dict) and r.get("success")]
        failed_assignments = [r for r in results if isinstance(r, dict) and not r.get("success")]
        
        # Verify race condition handling
        assert len(successful_assignments) <= max_assignments, f"Too many assignments: {len(successful_assignments)} > {max_assignments}"
        assert len(successful_assignments) >= 1, "At least one assignment should succeed"
        assert len(failed_assignments) >= 1, "Some assignments should fail due to race conditions"
        
        # Verify database consistency
        final_batch_state = await repository.get_batch_with_files(
            project.project_code,
            target_batch.id
        )
        
        if final_batch_state:
            assert final_batch_state["assignment_count"] == len(successful_assignments)
            assert final_batch_state["assignment_count"] <= max_assignments
    
    @pytest.mark.asyncio
    async def test_concurrent_user_current_batch_updates_real_database(
        self,
        concurrent_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test concurrent updates to user current batch assignments."""
        repository = BatchAssignmentRepository()
        project = concurrent_test_environment["project"]
        concurrent_users = concurrent_test_environment["concurrent_users"]
        concurrent_batches = concurrent_test_environment["concurrent_batches"]
        
        # Test user switching between batches concurrently
        test_user = concurrent_users[0]
        
        async def update_user_batch(batch_index):
            """Update user's current batch."""
            try:
                batch = concurrent_batches[batch_index]
                
                # Reset assignment count for this test
                batch.assignment_count = 0
                test_db.add(batch)
                await test_db.commit()
                
                success = await repository.assign_user_to_batch(
                    project.project_code,
                    test_user.id,
                    test_user.username,
                    batch.id,
                    1,  # Always use slot 1
                    batch.total_files
                )
                
                return {
                    "batch_id": batch.id,
                    "success": success,
                    "timestamp": time.time()
                }
            
            except Exception as e:
                return {
                    "batch_id": batch.id,
                    "success": False,
                    "error": str(e)
                }
        
        # Attempt concurrent batch switches
        tasks = []
        for i in range(3):  # Switch between first 3 batches
            task = update_user_batch(i)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # At least one should succeed
        successful_updates = [r for r in results if isinstance(r, dict) and r.get("success")]
        assert len(successful_updates) >= 1, "At least one batch update should succeed"
        
        # Verify final state consistency
        final_batch = await repository.get_user_current_batch(
            project.project_code,
            test_user.id
        )
        
        # Should be assigned to one of the attempted batches
        attempted_batch_ids = [concurrent_batches[i].id for i in range(3)]
        if final_batch:
            assert final_batch in attempted_batch_ids, f"Final batch {final_batch} not in attempted batches {attempted_batch_ids}"
    
    @pytest.mark.asyncio
    async def test_concurrent_batch_file_operations_real_database(
        self,
        concurrent_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test concurrent file operations on the same batch."""
        repository = ProjectDBRepository()
        project = concurrent_test_environment["project"]
        target_batch = concurrent_test_environment["concurrent_batches"][0]
        
        concurrent_file_results = []
        
        async def register_files_concurrently(file_prefix):
            """Register files for the same batch concurrently."""
            try:
                files_data = []
                for i in range(3):
                    file_data = {
                        'file_identifier': f'{file_prefix}_concurrent_file_{i}_{int(time.time())}.jpg',
                        'original_filename': f'{file_prefix}_test_{i}.jpg',
                        'file_type': 'image',
                        'file_size_bytes': 1024 * (i + 1),
                        'storage_location': {'type': 'test', 'path': f'/{file_prefix}/{i}.jpg'}
                    }
                    files_data.append(file_data)
                
                registered_files = await repository.register_files(
                    project.project_code,
                    target_batch.id,
                    files_data
                )
                
                return {
                    "prefix": file_prefix,
                    "success": True,
                    "file_count": len(registered_files),
                    "files": registered_files
                }
            
            except Exception as e:
                return {
                    "prefix": file_prefix,
                    "success": False,
                    "error": str(e)
                }
        
        # Start multiple concurrent file registration operations
        tasks = []
        prefixes = ["worker1", "worker2", "worker3", "worker4"]
        
        for prefix in prefixes:
            task = register_files_concurrently(prefix)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful operations
        successful_operations = [r for r in results if isinstance(r, dict) and r.get("success")]
        failed_operations = [r for r in results if isinstance(r, dict) and not r.get("success")]
        
        # At least some should succeed
        assert len(successful_operations) >= 1, "At least one file registration should succeed"
        
        # Verify no duplicate file identifiers in database
        all_batch_files = await repository.get_files_by_batch(
            project.project_code,
            target_batch.id
        )
        
        file_identifiers = [f['file_identifier'] for f in all_batch_files]
        unique_identifiers = set(file_identifiers)
        
        # No duplicates should exist
        assert len(file_identifiers) == len(unique_identifiers), "Duplicate file identifiers found"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.regression       # Suite marker - Cache concurrency
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Concurrent tests take time
class TestConcurrentCacheOperations:
    """PERFORMANCE TEST SUITE: Concurrent cache operations and invalidation."""
    
    @pytest.mark.asyncio
    async def test_concurrent_cache_access_and_invalidation_real_database(
        self,
        concurrent_test_environment,
        setup_test_database
    ):
        """Test concurrent cache access with invalidation."""
        cache = AnnotatorCache()
        project = concurrent_test_environment["project"]
        concurrent_users = concurrent_test_environment["concurrent_users"]
        
        cache_results = []
        
        async def access_cache_concurrently(user, operation_type):
            """Perform cache operations concurrently."""
            try:
                if operation_type == "read":
                    # Attempt to read user's batch assignment from cache
                    cache_key = f"user_batch_{user.id}_{project.project_code}"
                    result = await cache.get(cache_key)
                    
                    return {
                        "user_id": user.id,
                        "operation": "read",
                        "success": True,
                        "cache_hit": result is not None,
                        "result": result
                    }
                
                elif operation_type == "write":
                    # Write to cache
                    cache_key = f"user_batch_{user.id}_{project.project_code}"
                    cache_value = f"batch_data_{int(time.time())}"
                    
                    await cache.set(cache_key, cache_value, ttl=60)
                    
                    return {
                        "user_id": user.id,
                        "operation": "write",
                        "success": True,
                        "value": cache_value
                    }
                
                elif operation_type == "invalidate":
                    # Invalidate cache entries
                    pattern = f"user_batch_*_{project.project_code}"
                    await cache.delete_pattern(pattern)
                    
                    return {
                        "user_id": user.id,
                        "operation": "invalidate",
                        "success": True,
                        "pattern": pattern
                    }
            
            except Exception as e:
                return {
                    "user_id": user.id,
                    "operation": operation_type,
                    "success": False,
                    "error": str(e)
                }
        
        # Mix of concurrent operations
        tasks = []
        
        # Start with writes
        for i, user in enumerate(concurrent_users[:3]):
            task = access_cache_concurrently(user, "write")
            tasks.append(task)
        
        # Add concurrent reads
        for user in concurrent_users[3:6]:
            task = access_cache_concurrently(user, "read")
            tasks.append(task)
        
        # Add invalidation operations
        for user in concurrent_users[6:8]:
            task = access_cache_concurrently(user, "invalidate")
            tasks.append(task)
        
        # Execute all operations concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Categorize results
        write_ops = [r for r in results if isinstance(r, dict) and r.get("operation") == "write"]
        read_ops = [r for r in results if isinstance(r, dict) and r.get("operation") == "read"]
        invalidate_ops = [r for r in results if isinstance(r, dict) and r.get("operation") == "invalidate"]
        
        # At least some operations should succeed
        successful_writes = [op for op in write_ops if op.get("success")]
        successful_reads = [op for op in read_ops if op.get("success")]
        successful_invalidations = [op for op in invalidate_ops if op.get("success")]
        
        assert len(successful_writes) >= 1, "At least one cache write should succeed"
        assert len(successful_reads) >= 0, "Cache reads should not fail due to concurrency"
        assert len(successful_invalidations) >= 0, "Cache invalidations should handle concurrency"
    
    @pytest.mark.asyncio
    async def test_concurrent_admin_cache_operations_real_database(
        self,
        concurrent_test_environment,
        setup_test_database
    ):
        """Test concurrent admin cache operations."""
        admin_cache = AdminCache()
        project = concurrent_test_environment["project"]
        
        async def admin_cache_operation(operation_id, operation_type):
            """Perform admin cache operations."""
            try:
                cache_key = f"admin_stats_{project.project_code}_{operation_id}"
                
                if operation_type == "set_stats":
                    stats_data = {
                        "total_batches": random.randint(10, 100),
                        "active_users": random.randint(5, 50),
                        "completion_rate": random.uniform(0.1, 1.0),
                        "timestamp": time.time()
                    }
                    
                    await admin_cache.set(cache_key, stats_data, ttl=300)
                    
                    return {
                        "operation_id": operation_id,
                        "operation": "set_stats",
                        "success": True,
                        "data": stats_data
                    }
                
                elif operation_type == "get_stats":
                    stats = await admin_cache.get(cache_key)
                    
                    return {
                        "operation_id": operation_id,
                        "operation": "get_stats",
                        "success": True,
                        "found": stats is not None,
                        "data": stats
                    }
                
                elif operation_type == "update_stats":
                    # Simulate read-modify-write operation
                    existing_stats = await admin_cache.get(cache_key)
                    
                    if existing_stats:
                        # Update stats
                        existing_stats["completion_rate"] = random.uniform(0.1, 1.0)
                        existing_stats["timestamp"] = time.time()
                        
                        await admin_cache.set(cache_key, existing_stats, ttl=300)
                        
                        return {
                            "operation_id": operation_id,
                            "operation": "update_stats",
                            "success": True,
                            "updated": True,
                            "data": existing_stats
                        }
                    else:
                        return {
                            "operation_id": operation_id,
                            "operation": "update_stats",
                            "success": True,
                            "updated": False,
                            "message": "No existing stats to update"
                        }
            
            except Exception as e:
                return {
                    "operation_id": operation_id,
                    "operation": operation_type,
                    "success": False,
                    "error": str(e)
                }
        
        # Create concurrent admin operations
        tasks = []
        
        # Initial set operations
        for i in range(3):
            task = admin_cache_operation(i, "set_stats")
            tasks.append(task)
        
        # Concurrent get operations
        for i in range(3, 6):
            task = admin_cache_operation(i % 3, "get_stats")  # Get previously set stats
            tasks.append(task)
        
        # Concurrent update operations (potential race conditions)
        for i in range(6, 9):
            task = admin_cache_operation(i % 3, "update_stats")
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze results for race conditions
        set_operations = [r for r in results if isinstance(r, dict) and r.get("operation") == "set_stats"]
        get_operations = [r for r in results if isinstance(r, dict) and r.get("operation") == "get_stats"]
        update_operations = [r for r in results if isinstance(r, dict) and r.get("operation") == "update_stats"]
        
        # Verify operations completed successfully
        successful_sets = [op for op in set_operations if op.get("success")]
        successful_gets = [op for op in get_operations if op.get("success")]
        successful_updates = [op for op in update_operations if op.get("success")]
        
        assert len(successful_sets) >= 2, "Most set operations should succeed"
        assert len(successful_gets) >= 0, "Get operations should handle concurrency"
        assert len(successful_updates) >= 0, "Update operations should handle concurrency gracefully"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.regression       # Suite marker - Deadlock testing
@pytest.mark.critical         # Priority marker - P0 (deadlocks are critical)
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Deadlock tests take time
class TestDeadlockDetection:
    """PERFORMANCE TEST SUITE: Deadlock detection and prevention mechanisms."""
    
    @pytest.mark.asyncio
    async def test_potential_deadlock_scenarios_real_database(
        self,
        concurrent_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test scenarios that could potentially cause deadlocks."""
        repository = BatchAssignmentRepository()
        project = concurrent_test_environment["project"]
        concurrent_users = concurrent_test_environment["concurrent_users"]
        concurrent_batches = concurrent_test_environment["concurrent_batches"]
        
        deadlock_results = []
        
        async def create_potential_deadlock(user_pair, batch_pair):
            """Create scenario that might cause deadlock."""
            try:
                user1, user2 = user_pair
                batch1, batch2 = batch_pair
                
                # Task 1: User1 -> Batch1, then User1 -> Batch2
                async def task1():
                    try:
                        # First assignment
                        success1 = await repository.assign_user_to_batch(
                            project.project_code,
                            user1.id,
                            user1.username,
                            batch1.id,
                            1,
                            batch1.total_files
                        )
                        
                        # Small delay to increase deadlock probability
                        await asyncio.sleep(0.01)
                        
                        # Second assignment (might cause deadlock)
                        success2 = await repository.assign_user_to_batch(
                            project.project_code,
                            user1.id,
                            user1.username,
                            batch2.id,
                            1,
                            batch2.total_files
                        )
                        
                        return {"task": "task1", "success1": success1, "success2": success2}
                    
                    except Exception as e:
                        return {"task": "task1", "error": str(e)}
                
                # Task 2: User2 -> Batch2, then User2 -> Batch1 (reverse order)
                async def task2():
                    try:
                        # First assignment (reverse order)
                        success1 = await repository.assign_user_to_batch(
                            project.project_code,
                            user2.id,
                            user2.username,
                            batch2.id,
                            2,  # Different slot
                            batch2.total_files
                        )
                        
                        # Small delay to increase deadlock probability
                        await asyncio.sleep(0.01)
                        
                        # Second assignment
                        success2 = await repository.assign_user_to_batch(
                            project.project_code,
                            user2.id,
                            user2.username,
                            batch1.id,
                            2,  # Different slot
                            batch1.total_files
                        )
                        
                        return {"task": "task2", "success1": success1, "success2": success2}
                    
                    except Exception as e:
                        return {"task": "task2", "error": str(e)}
                
                # Execute both tasks concurrently
                results = await asyncio.gather(task1(), task2(), return_exceptions=True)
                
                return {
                    "user_pair": [user1.id, user2.id],
                    "batch_pair": [batch1.id, batch2.id],
                    "results": results,
                    "completed": True
                }
            
            except Exception as e:
                return {
                    "user_pair": [user1.id, user2.id],
                    "batch_pair": [batch1.id, batch2.id],
                    "error": str(e),
                    "completed": False
                }
        
        # Test multiple potential deadlock scenarios
        tasks = []
        for i in range(3):
            user_pair = (concurrent_users[i*2], concurrent_users[i*2 + 1])
            batch_pair = (concurrent_batches[i], concurrent_batches[i + 1])
            
            # Reset assignment counts for clean test
            for batch in batch_pair:
                batch.assignment_count = 0
                test_db.add(batch)
            
            await test_db.commit()
            
            task = create_potential_deadlock(user_pair, batch_pair)
            tasks.append(task)
        
        deadlock_test_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze results
        completed_tests = [r for r in deadlock_test_results if isinstance(r, dict) and r.get("completed")]
        failed_tests = [r for r in deadlock_test_results if isinstance(r, dict) and not r.get("completed")]
        
        # At least some tests should complete without deadlocks
        assert len(completed_tests) >= 1, "At least one deadlock test should complete successfully"
        
        # Check for database integrity after potential deadlock scenarios
        for batch in concurrent_batches[:4]:  # Check first 4 batches used in tests
            batch_state = await repository.get_batch_with_files(
                project.project_code,
                batch.id
            )
            
            if batch_state:
                # Assignment count should be consistent
                assert batch_state["assignment_count"] <= batch_state["total_annotation_slots"], \
                    f"Assignment count {batch_state['assignment_count']} exceeds slots"
    
    @pytest.mark.asyncio
    async def test_transaction_timeout_handling_real_database(
        self,
        concurrent_test_environment,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test handling of long-running transactions that might timeout."""
        repository = ProjectDBRepository()
        project = concurrent_test_environment["project"]
        
        async def long_running_operation(operation_id):
            """Simulate long-running database operation."""
            try:
                # Create batch with many files to simulate long operation
                batch_data = {
                    'batch_identifier': f'LONG_RUNNING_BATCH_{operation_id}_{int(time.time())}',
                    'total_files': 50,  # Larger batch
                    'file_list': [f'long_file_{i}.jpg' for i in range(50)],
                    'annotation_count': 1
                }
                
                created_batch = await repository.create_allocation_batch(
                    project.project_code,
                    batch_data
                )
                
                # Register many files (long operation)
                files_data = []
                for i in range(25):  # Many files
                    file_data = {
                        'file_identifier': f'long_op_{operation_id}_file_{i}.jpg',
                        'original_filename': f'long_test_{i}.jpg',
                        'file_type': 'image',
                        'file_size_bytes': 1024 * 1024,  # 1MB files
                        'storage_location': {'type': 'test', 'path': f'/long/{i}.jpg'}
                    }
                    files_data.append(file_data)
                
                registered_files = await repository.register_files(
                    project.project_code,
                    created_batch['id'],
                    files_data
                )
                
                return {
                    "operation_id": operation_id,
                    "success": True,
                    "batch_id": created_batch['id'],
                    "files_registered": len(registered_files)
                }
            
            except Exception as e:
                return {
                    "operation_id": operation_id,
                    "success": False,
                    "error": str(e)
                }
        
        # Start multiple long-running operations concurrently
        tasks = []
        for i in range(3):
            task = long_running_operation(i)
            tasks.append(task)
        
        # Set timeout to test timeout handling
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=30.0  # 30 second timeout
            )
            
            # Analyze results
            successful_operations = [r for r in results if isinstance(r, dict) and r.get("success")]
            failed_operations = [r for r in results if isinstance(r, dict) and not r.get("success")]
            
            # At least some operations should complete within timeout
            assert len(successful_operations) >= 1, "At least one long operation should complete"
            
            # Verify database consistency after long operations
            for result in successful_operations:
                if result.get("batch_id"):
                    batch_files = await repository.get_files_by_batch(
                        project.project_code,
                        result["batch_id"]
                    )
                    
                    # Should have registered files
                    assert len(batch_files) >= result.get("files_registered", 0)
        
        except asyncio.TimeoutError:
            # Timeout occurred - this is acceptable for testing timeout handling
            assert True, "Long operations timed out as expected"
