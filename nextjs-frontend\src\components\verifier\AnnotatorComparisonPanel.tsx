"use client";

import { useState, use<PERSON><PERSON>back, useMemo } from "react";
import {
  <PERSON>a<PERSON>ser,
  FaCheck,
  FaTimes,
  FaEdit,
  FaChevronDown,
  FaChevronUp,
  FaStar
} from "react-icons/fa";
import { DynamicField, type FormFieldConfig } from "../shared/dynamic-fields";

interface AnnotatorReview {
  annotator_id: number;
  annotator_number: number;
  review_data: any;
  submitted_at: string;
}

interface FieldDecision {
  chosen_annotator_id?: number;
  custom_response?: any;
  decision_type: 'annotator_choice' | 'custom_input';
  final_value: any;
}

interface AnnotatorComparisonPanelProps {
  annotatorReviews: AnnotatorReview[];
  formConfig: FormFieldConfig[];
  currentDecisions: Record<string, FieldDecision>;
  onDecisionChange: (fieldName: string, decision: FieldDecision) => void;
}

export default function AnnotatorComparisonPanel({
  annotatorReviews,
  formConfig,
  currentDecisions,
  onDecisionChange
}: AnnotatorComparisonPanelProps) {
  const [expandedFields, setExpandedFields] = useState<Record<string, boolean>>({});
  const [customInputMode, setCustomInputMode] = useState<Record<string, boolean>>({});

  // Toggle field expansion
  const toggleFieldExpansion = useCallback((fieldName: string) => {
    setExpandedFields(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  }, []);

  // Toggle custom input mode for a field
  const toggleCustomInput = useCallback((fieldName: string) => {
    setCustomInputMode(prev => {
      const newMode = !prev[fieldName];
      
      // Defer the decision change to avoid setState during render
      setTimeout(() => {
        if (newMode) {
          // Switching to custom input mode
          const currentDecision = currentDecisions[fieldName];
          onDecisionChange(fieldName, {
            decision_type: 'custom_input',
            custom_response: currentDecision?.final_value || null,
            final_value: currentDecision?.final_value || null
          });
        } else {
          // Switching back to annotator choice mode
          onDecisionChange(fieldName, {
            decision_type: 'annotator_choice',
            chosen_annotator_id: undefined,
            final_value: null
          });
        }
      }, 0);
      
      return {
        ...prev,
        [fieldName]: newMode
      };
    });
  }, [currentDecisions, onDecisionChange]);

  // Handle annotator choice selection
  const handleAnnotatorChoice = useCallback((fieldName: string, annotatorId: number, value: any) => {
    onDecisionChange(fieldName, {
      decision_type: 'annotator_choice',
      chosen_annotator_id: annotatorId,
      final_value: value
    });
  }, [onDecisionChange]);

  // Handle custom input change
  const handleCustomInputChange = useCallback((fieldName: string, value: any) => {
    onDecisionChange(fieldName, {
      decision_type: 'custom_input',
      custom_response: value,
      final_value: value
    });
  }, [onDecisionChange]);

  // Parse annotator review data
  const parseAnnotatorData = useCallback((reviewData: any) => {
    try {
      if (typeof reviewData === 'string') {
        return JSON.parse(reviewData);
      }
      return reviewData;
    } catch (error) {
      return reviewData;
    }
  }, []);

  // Create reverse mapping from field_name to label
  const fieldNameToLabel = useMemo(() => {
    const mapping: Record<string, string> = {};
    formConfig.forEach(field => {
      mapping[field.field_name] = field.label;
    });
    return mapping;
  }, [formConfig]);

  // Get annotator response for a field
  const getAnnotatorResponse = useCallback((review: AnnotatorReview, fieldName: string) => {
    const data = parseAnnotatorData(review.review_data);
    
    if (data && typeof data === 'object') {
      // Check in form_data first, then direct field
      if (data.form_data) {
        // Try field_name first
        if (data.form_data[fieldName] !== undefined) {
          return data.form_data[fieldName];
        }
        // Try label (for data stored with label keys)
        const label = fieldNameToLabel[fieldName];
        if (label && data.form_data[label] !== undefined) {
          return data.form_data[label];
        }
      }
      if (data[fieldName] !== undefined) {
        return data[fieldName];
      }
    }
    
    return null;
  }, [parseAnnotatorData, fieldNameToLabel]);

  // Check if all annotators agree on a field
  const checkAnnotatorAgreement = useCallback((fieldName: string) => {
    const responses = annotatorReviews.map(review => getAnnotatorResponse(review, fieldName));
    const nonNullResponses = responses.filter(r => r !== null && r !== undefined && r !== '');
    
    if (nonNullResponses.length <= 1) return { agree: false, value: null };
    
    const firstResponse = JSON.stringify(nonNullResponses[0]);
    const allSame = nonNullResponses.every(r => JSON.stringify(r) === firstResponse);
    
    return { agree: allSame, value: allSame ? nonNullResponses[0] : null };
  }, [annotatorReviews, getAnnotatorResponse]);

  // Format response value for display
  const formatResponseValue = useCallback((value: any) => {
    if (value === null || value === undefined) return 'No response';
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  }, []);


  return (
    <div className="h-full">
      <div className="p-4">
        {formConfig.map((field, index) => {
          const agreement = checkAnnotatorAgreement(field.field_name);
          const isExpanded = expandedFields[field.field_name] ?? true;
          const isCustomMode = customInputMode[field.field_name] ?? false;
          const currentDecision = currentDecisions[field.field_name];
          
          return (
            <div key={field.field_name} className="border border-gray-200 rounded-xl mb-2 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
              {/* Field Header */}
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-3 py-2 cursor-pointer flex justify-between items-center border-b border-gray-200 select-none hover:from-gray-100 hover:to-gray-200 transition-colors duration-200" onClick={() => toggleFieldExpansion(field.field_name)}>
                <div className="flex items-center gap-3">
                  <h4 className="m-0 text-lg font-bold text-gray-800 tracking-tight">{field.label}</h4>
                  {field.required && <span className="bg-red-500 text-white px-2 py-0.5 rounded-xl text-xs font-semibold uppercase tracking-wide">Required</span>}
                  {agreement.agree && (
                    <div className="bg-gradient-to-r from-green-500 to-green-600 text-white px-3 py-1 rounded-2xl text-xs font-semibold flex items-center gap-1.5 shadow-md">
                      <FaCheck /> All Agree
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-3">
                  <div>
                    {currentDecision?.final_value ? (
                      <span className="text-green-600 flex items-center gap-1 text-xs">
                        <FaCheck /> Decided
                      </span>
                    ) : (
                      <span className="text-gray-500 text-xs">Pending</span>
                    )}
                  </div>
                  
                  <button className="bg-transparent border-none text-gray-500 cursor-pointer p-1 hover:text-gray-700 transition-colors">
                    {isExpanded ? <FaChevronUp /> : <FaChevronDown />}
                  </button>
                </div>
              </div>

              {/* Field Content */}
              {isExpanded && (
                <div className="p-5">
                  {field.description && (
                    <p className="m-0 mb-4 text-sm text-gray-500 italic">{field.description}</p>
                  )}

                  {!isCustomMode ? (
                    <>
                      {/* Annotator Responses */}
                      <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 mb-5">
                        {annotatorReviews.map((review) => {
                          const response = getAnnotatorResponse(review, field.field_name);
                          const isSelected = currentDecision?.chosen_annotator_id === review.annotator_id;
                          
                          return (
                            <div
                              key={review.annotator_id}
                              className={`border-2 rounded-xl cursor-pointer transition-all duration-300 ease-out relative overflow-hidden bg-white ${
                                isSelected 
                                  ? 'border-blue-600 shadow-xl shadow-blue-200/50 -translate-y-0.5 bg-gradient-to-br from-white to-blue-50' 
                                  : response 
                                    ? 'border-gray-200 hover:border-blue-300 hover:shadow-lg' 
                                    : 'border-gray-200 opacity-50 cursor-not-allowed grayscale-[0.3]'
                              }`}
                              onClick={() => response && handleAnnotatorChoice(field.field_name, review.annotator_id, response)}
                            >
                              <div className="px-4 py-3 bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 border-b border-gray-200 flex justify-between items-center">
                                <div className="flex items-center gap-1.5 font-medium">
                                  <FaUser className="text-blue-500 text-xs" />
                                  <span className="text-sm">
                                    Annotator {review.annotator_number}
                                  </span>
                                </div>
                                
                                <div className="flex items-center gap-2 text-xs text-gray-500">
                                  {isSelected && <FaStar className="text-yellow-400" />}
                                </div>
                              </div>
                              
                              <div className="p-4 relative bg-white min-h-[60px]">
                                <div className="font-mono text-sm text-gray-800 leading-relaxed whitespace-pre-wrap break-words min-h-[20px] p-3 bg-gray-50 rounded-lg border border-gray-200">
                                  {formatResponseValue(response)}
                                </div>
                                
                                {response && (
                                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/95 to-blue-600/95 text-white flex items-center justify-center gap-2 font-semibold text-sm opacity-0 hover:opacity-100 transition-all duration-300 ease-out backdrop-blur-sm">
                                    <FaCheck /> Select This Response
                                  </div>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>

                      {/* Quick Agreement Action */}
                      {agreement.agree && (
                        <div className="mb-4">
                          <button
                            className="w-full py-3.5 px-4 bg-gradient-to-r from-green-500 to-green-600 text-white border-none rounded-xl cursor-pointer flex items-center justify-center gap-2.5 font-semibold text-sm transition-all duration-300 ease-out shadow-lg shadow-green-500/30 hover:shadow-xl hover:shadow-green-500/40 hover:from-green-600 hover:to-green-700"
                            onClick={() => handleAnnotatorChoice(field.field_name, annotatorReviews[0].annotator_id, agreement.value)}
                          >
                            <FaCheck /> Accept Consensus: {formatResponseValue(agreement.value)}
                          </button>
                        </div>
                      )}
                    </>
                  ) : (
                    <>
                      {/* Custom Input Mode */}
                      <div className="mb-4">
                        <div className="flex items-center gap-2 mb-3 text-gray-600 font-medium">
                          <FaEdit className="text-blue-500" />
                          <span>Provide your own response</span>
                        </div>
                        
                        <DynamicField
                          config={field}
                          value={currentDecision?.custom_response}
                          onChange={handleCustomInputChange}
                        />
                      </div>
                    </>
                  )}

                  {/* Mode Toggle */}
                  <div className="flex gap-2 border-t border-gray-200 pt-4">
                    <button
                      className={`flex-1 py-2.5 px-4 border-2 rounded-lg cursor-pointer transition-all duration-300 ease-out flex items-center justify-center gap-2 text-xs font-medium ${
                        !isCustomMode 
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600 shadow-lg shadow-blue-500/30' 
                          : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                      }`}
                      onClick={() => {
                        if (isCustomMode) {
                          toggleCustomInput(field.field_name);
                        }
                      }}
                    >
                      <FaUser /> Choose Annotator Response
                    </button>
                    <button
                      className={`flex-1 py-2.5 px-4 border-2 rounded-lg cursor-pointer transition-all duration-300 ease-out flex items-center justify-center gap-2 text-xs font-medium ${
                        isCustomMode 
                          ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600 shadow-lg shadow-blue-500/30' 
                          : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                      }`}
                      onClick={() => {
                        if (!isCustomMode) {
                          toggleCustomInput(field.field_name);
                        }
                      }}
                    >
                      <FaEdit /> Provide Custom Input
                    </button>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

    </div>
  );
}
