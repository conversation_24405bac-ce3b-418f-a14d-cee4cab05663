import React from 'react';
import { FaCog, FaSync } from 'react-icons/fa';
import { 
  BatchAllocationInfo, 
  AssignmentProgress, 
  StrategyDetails 
} from '../types';
import { getBatchStatusColor } from '../utils';

interface BatchAllocationSectionProps {
  strategyDetails: StrategyDetails | null;
  batchAllocations: BatchAllocationInfo[];
  assignmentProgress: AssignmentProgress | null;
  loadingBatchAllocations: boolean;
  syncingBatchAllocations: boolean;
  onSyncBatchAllocations: () => void;
}

export const BatchAllocationSection: React.FC<BatchAllocationSectionProps> = ({
  strategyDetails,
  batchAllocations,
  assignmentProgress,
  loadingBatchAllocations,
  syncingBatchAllocations,
  onSyncBatchAllocations,
}) => {
  if (!strategyDetails) {
    return null;
  }

  return (
    <div className="border rounded-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold flex items-center">
          <FaCog className="mr-2" />
          Batch Allocation 
          <span className="ml-2 text-sm font-normal text-gray-500">
            ({batchAllocations.length} batches)
          </span>
        </h3>
        <button
          onClick={onSyncBatchAllocations}
          disabled={syncingBatchAllocations || loadingBatchAllocations}
          className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          title="Sync batch allocation counts with real completion data"
        >
          <FaSync className={`w-3 h-3 mr-1.5 ${syncingBatchAllocations ? 'animate-spin' : ''}`} />
          {syncingBatchAllocations ? 'Syncing...' : 'Sync Counts'}
        </button>
      </div>
      
      {/* Progress Overview */}
      {assignmentProgress && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium mb-2">Assignment Progress</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="font-medium text-blue-600">Annotator Phase</div>
              <div>Completed: {assignmentProgress.annotator_phase.completed_batches}</div>
              <div>In Progress: {assignmentProgress.annotator_phase.in_progress_batches}</div>
              <div>Pending: {assignmentProgress.annotator_phase.pending_batches}</div>
            </div>
            {strategyDetails?.requires_verification && (
              <div>
                <div className="font-medium text-green-600">Verifier Phase</div>
                <div>Completed: {assignmentProgress.verifier_phase.completed_batches}</div>
                <div>In Progress: {assignmentProgress.verifier_phase.in_progress_batches}</div>
                <div>Pending: {assignmentProgress.verifier_phase.pending_batches}</div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Batch Details */}
      {loadingBatchAllocations ? (
        <div className="p-4 text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading batch allocations...</p>
        </div>
      ) : batchAllocations.length > 0 ? (
        <div className="max-h-64 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {batchAllocations.map(batch => (
              <div key={batch.batch_id} className="p-3 border rounded-md bg-white">
                <div className="text-sm font-medium mb-2">{batch.batch_name}</div>
                <div className="text-xs space-y-1">
                  <div className="flex justify-between">
                    <span>Annotators:</span>
                    <span className={batch.annotator_count >= batch.total_annotators_required ? 'text-green-600' : 'text-orange-600'}>
                      {batch.annotator_count}/{batch.total_annotators_required}
                    </span>
                  </div>
                  {strategyDetails?.requires_verification && (
                    <div className="flex justify-between">
                      <span>Verifiers:</span>
                      <span className={batch.verified_count >= batch.total_verifiers_required ? 'text-green-600' : 'text-orange-600'}>
                        {batch.verified_count}/{batch.total_verifiers_required}
                      </span>
                    </div>
                  )}
                  <div className="mt-2 pt-1 border-t">
                    <span className={`px-2 py-1 rounded-full text-xs ${getBatchStatusColor(batch.status)}`}>
                      {batch.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className="p-4 text-center text-gray-600">
          <p className="text-sm">No batch allocations found.</p>
          <p className="text-xs mt-1 text-gray-500">
            This could mean: 1) No batches have been created for this project, 2) Database connection issues, or 3) Project database doesn't exist.
          </p>
          <p className="text-xs mt-1 text-blue-600">
            Try refreshing the page or contact support if the issue persists.
          </p>
        </div>
      )}
    </div>
  );
};
