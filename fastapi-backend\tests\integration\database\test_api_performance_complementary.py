"""
Integration tests for Performance scenarios with bulk test data.
These tests use test_data_manager.py to pre-populate databases with large datasets.

USAGE:
1. Setup performance environment: ./scripts/setup_test_environments.sh performance  
2. Run tests: pytest tests/integration/database/test_performance_scenarios.py -v
3. Cleanup: ./scripts/setup_test_environments.sh clean
"""
import pytest
import time
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text
from httpx import AsyncClient

from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType  
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType

# Import test factory for creating additional test data if needed
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest.mark.performance
@pytest.mark.integration
@pytest.mark.database
@pytest.mark.api             # Feature marker - API operations
@pytest.mark.performance     # Suite marker - Performance testing
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
@pytest.mark.slow            # Execution marker - Performance tests take time
class TestLargeBatchQueries:
    """Performance tests for batch operations with large datasets."""
    
    @pytest.mark.asyncio
    async def test_batch_aggregation_performance(self, test_db: AsyncSession):
        """Test performance of batch aggregation queries."""
        print("\n Testing batch aggregation performance...")
        
        start_time = time.time()
        
        # Aggregation query
        stmt = select(
            func.count(AllocationBatches.id).label('total_batches'),
            func.sum(AllocationBatches.total_files).label('total_files'),
            func.avg(AllocationBatches.total_files).label('avg_files_per_batch'),
            AllocationBatches.batch_status
        ).group_by(AllocationBatches.batch_status)
        
        result = await test_db.execute(stmt)
        aggregations = result.all()
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"    Aggregation query: {len(aggregations)} groups in {query_time:.4f}s")
        for agg in aggregations:
            print(f"      Status {agg.batch_status}: {agg.total_batches} batches, {agg.total_files} files")
        
        assert query_time < 1.5, f"Aggregation query took too long: {query_time}s"
        assert len(aggregations) > 0
   
@pytest.mark.performance
@pytest.mark.integration
@pytest.mark.database
@pytest.mark.api             # Feature marker - API operations
@pytest.mark.performance     # Suite marker - Performance testing
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
@pytest.mark.slow            # Execution marker - Performance tests take time
class TestCrossTableJoinPerformance:
    """Performance tests for complex joins across multiple tables."""
    
    @pytest.mark.asyncio
    async def test_master_project_join_performance(self, test_master_db: AsyncSession):
        """Test join performance between master database tables."""
        print("\n Testing master database join performance...")
        
        start_time = time.time()
        
        # Complex join across master tables
        stmt = select(
            ProjectsRegistry.project_code,
            ProjectsRegistry.project_name,
            Clients.client_name,
            AllocationStrategies.strategy_name,
            AllocationStrategies.num_annotators
        ).join(
            Clients, ProjectsRegistry.client_id == Clients.id
        ).join(
            AllocationStrategies, ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id
        ).where(
            ProjectsRegistry.is_active == True,
            AllocationStrategies.allocation_status == "active"
        )
        
        result = await test_master_db.execute(stmt)
        project_details = result.all()
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"    Master join query: {len(project_details)} projects in {query_time:.4f}s")
        for detail in project_details[:5]:  # Show first 5
            print(f"      {detail.project_code} ({detail.client_name}): {detail.strategy_name}")
        
        assert query_time < 1.0, f"Master join took too long: {query_time}s"
        assert len(project_details) > 0
    
    @pytest.mark.asyncio 
    async def test_allocation_workflow_join_performance(self, test_db: AsyncSession):
        """Test join performance across allocation workflow tables."""
        print("\n Testing allocation workflow join performance...")
        
        start_time = time.time()
        
        # Complex workflow join
        stmt = select(
            AllocationBatches.batch_identifier,
            func.count(FilesRegistry.id).label('file_count'),
            func.count(UserAllocations.id).label('allocation_count')
        ).outerjoin(
            FilesRegistry, AllocationBatches.id == FilesRegistry.batch_id
        ).outerjoin(
            UserAllocations, AllocationBatches.id == UserAllocations.batch_id
        ).group_by(
            AllocationBatches.id, AllocationBatches.batch_identifier
        ).having(
            func.count(FilesRegistry.id) > 5
        ).limit(20)
        
        result = await test_db.execute(stmt)
        workflow_stats = result.all()
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"    Workflow join: {len(workflow_stats)} batches in {query_time:.4f}s")
        for stat in workflow_stats[:3]:  # Show first 3
            print(f"      {stat.batch_identifier}: {stat.file_count} files, {stat.allocation_count} allocations")
        
        assert query_time < 2.0, f"Workflow join took too long: {query_time}s"


@pytest.mark.performance
@pytest.mark.integration
@pytest.mark.database
class TestBulkDataConsistency:
    """Tests to verify data consistency in bulk-populated databases."""
    
    @pytest.mark.asyncio
    async def test_referential_integrity_check(self, test_db: AsyncSession, test_master_db: AsyncSession):
        """Verify referential integrity across bulk-populated data."""
        print("\n Testing referential integrity in bulk data...")
        
        # Check file -> batch references
        orphaned_files_query = select(func.count()).select_from(
            FilesRegistry
        ).outerjoin(
            AllocationBatches, FilesRegistry.batch_id == AllocationBatches.id
        ).where(AllocationBatches.id.is_(None))
        
        orphaned_files = await test_db.execute(orphaned_files_query)
        orphan_count = orphaned_files.scalar()
        
        print(f"    Orphaned files: {orphan_count}")
        assert orphan_count == 0, f"Found {orphan_count} orphaned files"
        
        # Check project -> client references  
        orphaned_projects_query = select(func.count()).select_from(
            ProjectsRegistry
        ).outerjoin(
            Clients, ProjectsRegistry.client_id == Clients.id
        ).where(Clients.id.is_(None))
        
        orphaned_projects = await test_master_db.execute(orphaned_projects_query)
        orphan_project_count = orphaned_projects.scalar()
        
        print(f"    Orphaned projects: {orphan_project_count}")
        assert orphan_project_count == 0, f"Found {orphan_project_count} orphaned projects"
    
    @pytest.mark.asyncio
    async def test_data_distribution_validation(self, test_db: AsyncSession, test_master_db: AsyncSession):
        """Validate that bulk data has reasonable distribution."""
        print("\n Testing bulk data distribution...")
        
        # Check batch status distribution
        status_dist = await test_db.execute(
            select(
                AllocationBatches.batch_status,
                func.count().label('count')
            ).group_by(AllocationBatches.batch_status)
        )
        
        status_counts = status_dist.all()
        print("   Batch status distribution:")
        total_batches = sum(row.count for row in status_counts)
        
        for row in status_counts:
            percentage = (row.count / total_batches) * 100
            print(f"      {row.batch_status}: {row.count} ({percentage:.1f}%)")
        
        assert total_batches > 0, "No batches found in database"
        assert len(status_counts) >= 2, "Expected variety in batch statuses"
        
        # Check user role distribution
        role_dist = await test_master_db.execute(
            select(
                users.role,
                func.count().label('count')  
            ).group_by(users.role)
        )
        
        role_counts = role_dist.all()
        print("   User role distribution:")
        
        for row in role_counts:
            print(f"      {row.role.value}: {row.count}")
        
        assert len(role_counts) >= 3, "Expected variety in user roles"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.api             # Feature marker - API operations
@pytest.mark.performance     # Suite marker - Performance testing
@pytest.mark.bulk_data       # Environment marker - Large datasets
@pytest.mark.medium          # Priority marker - P2
@pytest.mark.stable          # Stability marker - Reliable
@pytest.mark.slow            # Execution marker - Performance tests take time
class TestAPIPerformanceWithBulkData:
    """API performance tests with bulk-populated data."""
    
    @pytest.mark.asyncio
    async def test_batch_listing_api_performance(self, authenticated_client: AsyncClient):
        """Test batch listing API performance with large datasets."""
        print("\n Testing batch listing API performance...")
        
        start_time = time.time()
        
        response = await authenticated_client.get("/batches?limit=50&offset=0")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"    API response time: {response_time:.4f}s")
        print(f"    Status code: {response.status_code}")
        
        assert response_time < 3.0, f"API response took too long: {response_time}s"
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, list):
                print(f"    Returned {len(data)} batches")
                assert len(data) <= 50
    
    @pytest.mark.asyncio
    async def test_project_listing_api_performance(self, authenticated_client: AsyncClient):
        """Test project listing API performance."""
        print("\n Testing project listing API performance...")
        
        start_time = time.time()
        
        response = await authenticated_client.get("/projects")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"   API response time: {response_time:.4f}s")
        print(f"    Status code: {response.status_code}")
        
        assert response_time < 2.0, f"API response took too long: {response_time}s"
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, list):
                print(f"   Returned {len(data)} projects")


# Benchmark utility for measuring consistent performance
class PerformanceBenchmark:
    """Utility class for performance measurements."""
    
    def __init__(self, name: str):
        self.name = name
        self.start_time = None
        self.measurements = []
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - self.start_time
        self.measurements.append(duration)
        print(f"⏱️  {self.name}: {duration:.4f}s")
    
    def average_time(self) -> float:
        return sum(self.measurements) / len(self.measurements) if self.measurements else 0
