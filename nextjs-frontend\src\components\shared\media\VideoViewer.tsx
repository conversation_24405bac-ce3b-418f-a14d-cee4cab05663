"use client";

import { useRef, useEffect, useState } from "react";
import { MediaViewerProps, getStreamingUrlEndpoint } from "./types";
import { FaCheck, FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaExpand } from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";
import { API_BASE_URL } from "@/lib/api";

interface VideoViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  isLabeled?: boolean;
}

interface StreamingInfo {
  streaming_url: string;
  type: 'presigned' | 'api_endpoint';
  storage_type?: string;
  expires_in?: number;
  supports_range_requests: boolean;
}

export default function VideoViewer({
  mediaUrl,
  zoomLevel = 100,
  onLoad,
  onError,
  isLabeled = false
}: VideoViewerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [optimizedUrl, setOptimizedUrl] = useState<string | null>(null);
  const [isLoadingUrl, setIsLoadingUrl] = useState(true);
  const [streamingError, setStreamingError] = useState<string | null>(null);

  // Get optimized streaming URL for video
  useEffect(() => {
    const getStreamingUrl = async () => {
      if (!mediaUrl) return;
      
      setIsLoadingUrl(true);
      setStreamingError(null);
      
      // Extract video path from the mediaUrl
      const videoPath = mediaUrl.replace(/.*\/media\/video\//, '').replace(/^\//, '');
      
      console.log('VideoViewer: Setting up video for path:', videoPath);
      console.log('VideoViewer: Media URL:', mediaUrl);
      
      try {
        // Try to get MinIO presigned URL if available
        const streamingEndpoint = getStreamingUrlEndpoint('video', videoPath);
        const streamUrlResponse = await authFetch(streamingEndpoint);
        
        if (streamUrlResponse.ok) {
          const streamingInfo: StreamingInfo = await streamUrlResponse.json();
          
          // Only use presigned URLs (MinIO), for everything else use direct media URL
          if (streamingInfo.type === 'presigned' && streamingInfo.storage_type === 'MinIO') {
            console.log('VideoViewer: Using MinIO presigned URL for optimized streaming');
            setOptimizedUrl(streamingInfo.streaming_url);
            setIsLoadingUrl(false);
            return;
          }
        }
      } catch (error) {
        console.log('VideoViewer: Streaming URL not available, using direct serving');
      }
      
      // For all other cases (NAS-FTP, errors, non-MinIO), use direct media URL
      console.log('VideoViewer: Using direct media URL through API');
      setOptimizedUrl(mediaUrl);
      setStreamingError(null);
      setIsLoadingUrl(false);
    };

    getStreamingUrl();
  }, [mediaUrl, onError]);

  // Manual check for video duration after URL is set
  useEffect(() => {
    if (optimizedUrl && videoRef.current) {
      const video = videoRef.current;
      const checkDuration = () => {
        if (video.duration && !isNaN(video.duration)) {
          console.log('Manual duration check:', video.duration);
          setDuration(video.duration);
        } else {
          setTimeout(checkDuration, 100);
        }
      };
      setTimeout(checkDuration, 500);
    }
  }, [optimizedUrl]);

  // Set up video event listeners
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      console.log('Video duration:', video.duration);
      setDuration(video.duration || 0);
      onLoad?.();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime || 0);
    };

    const handleCanPlay = () => {
      console.log('Video can play, duration:', video.duration);
      if (video.duration && video.duration !== duration) {
        setDuration(video.duration);
      }
    };

    const handleLoadedData = () => {
      console.log('Video loaded data, duration:', video.duration);
      if (video.duration) {
        setDuration(video.duration);
      }
    };

    const handleError = () => {
      onError?.("Failed to load video");
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      video.currentTime = 0;
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('error', handleError);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('error', handleError);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
    };
  }, [onLoad, onError, duration]);


  const togglePlay = async () => {
    const video = videoRef.current;
    if (!video) return;

    try {
      if (isPlaying) {
        video.pause();
        setIsPlaying(false);
      } else {
        await video.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.warn('Video play/pause error:', error);
      // Reset state to match actual video state
      setIsPlaying(!video.paused);
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = parseFloat(e.target.value);
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      video.requestFullscreen();
    }
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) {
      return '0:00';
    }
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="h-full flex flex-col justify-center items-center relative">
      {isLoadingUrl ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-gray-400">Loading...</div>
        </div>
      ) : streamingError ? (
        <div className="flex flex-col items-center justify-center h-full text-center p-4">
          <div className="text-red-500 mb-2">Streaming Error</div>
          <div className="text-gray-600 text-sm">{streamingError}</div>
          <div className="text-gray-500 text-xs mt-2">Please check MinIO configuration</div>
        </div>
      ) : optimizedUrl ? (
        <>
          <video
            ref={videoRef}
            src={optimizedUrl || ''}
            className="w-full h-auto max-h-full"
            style={{
              transform: `scale(${zoomLevel / 100})`,
            }}
            controls={false}
            preload="metadata"
          />
          
          {/* Custom Video Controls */}
          <div className="absolute bottom-2.5 left-2.5 right-2.5 bg-black/80 rounded-lg p-2.5 flex items-center gap-2.5 text-white">
            <button 
              className="bg-transparent border-none text-white cursor-pointer p-1 rounded transition-colors duration-200 hover:bg-white/20" 
              onClick={togglePlay}
            >
              {isPlaying ? <FaPause /> : <FaPlay />}
            </button>
            
            <div className="flex-1 flex flex-col gap-1">
              <input
                type="range"
                min="0"
                max={duration || 0}
                value={currentTime}
                onChange={handleSeek}
                className="w-full h-1 bg-white/30 rounded-sm outline-none cursor-pointer appearance-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-3 [&::-webkit-slider-thumb]:h-3 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-500 [&::-webkit-slider-thumb]:cursor-pointer"
              />
              <div className="text-xs text-center">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <button 
                className="bg-transparent border-none text-white cursor-pointer p-1 rounded transition-colors duration-200 hover:bg-white/20" 
                onClick={toggleMute}
              >
                {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={handleVolumeChange}
                className="w-15 h-1 bg-white/30 rounded-sm outline-none appearance-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:w-2.5 [&::-webkit-slider-thumb]:h-2.5 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-blue-500 [&::-webkit-slider-thumb]:cursor-pointer"
              />
            </div>
            
            <button 
              className="bg-transparent border-none text-white cursor-pointer p-1 rounded transition-colors duration-200 hover:bg-white/20" 
              onClick={toggleFullscreen}
            >
              <FaExpand />
            </button>
          </div>
        </>
      ) : (
        <div className="flex items-center justify-center h-full">
          <div className="text-gray-400">No streaming URL available</div>
        </div>
      )}
      
      {isLabeled && (
        <span className="absolute top-2.5 left-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center z-10">
          <FaCheck className="me-1" /> Labeled
        </span>
      )}

    </div>
  );
}
