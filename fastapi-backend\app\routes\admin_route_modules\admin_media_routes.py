from fastapi import APIRouter, HTTPException, Request #type:ignore
from schemas.UserSchemas import SuccessResponse, ErrorResponse
from utils.media_utils import get_media_from_storage

router = APIRouter(
    prefix="/admin",
    tags=["Admin - Media"],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}},
)

@router.get("/image/{image_path:path}")
async def admin_get_image(image_path: str):
    try:
        return await get_media_from_storage(image_path, "image", include_response_time=True)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/video-stream-url/{video_path:path}")
async def admin_get_video_stream_url(video_path: str):
    """Get optimized streaming URL for a video file (admin)"""
    try:
        from utils.media_utils import get_streaming_url_for_media
        streaming_info = await get_streaming_url_for_media(video_path, "video")
        return streaming_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/video/{video_path:path}")
async def admin_get_video(video_path: str, request: Request):
    """Serve video files for admin with optimized streaming"""
    try:
        from utils.media_utils import get_media_from_storage
        return await get_media_from_storage(video_path, "video", include_response_time=True, request=request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/audio/{audio_path:path}")
async def admin_get_audio(audio_path: str, request: Request):
    """Serve audio files for admin with smart Redis caching"""
    try:
        from utils.media_utils import get_media_from_storage
        return await get_media_from_storage(audio_path, "audio", include_response_time=True, request=request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/video-info/{video_path:path}")
async def admin_get_video_info(video_path: str):
    """Get diagnostic information about a video file"""
    try:
        from core.nas_connector import get_ftp_connector
        from cache.redis_connector import cache_get
        import os
        
        connector = await get_ftp_connector()
        if not connector:
            raise HTTPException(status_code=400, detail="Storage connection failed")
        
        # Normalize the path
        normalized_path = video_path.replace('%20', ' ').strip()
        cache_key = f"media_file:video:{normalized_path}"
        
        # Check cache status
        cached_content = await cache_get(cache_key)
        is_cached = cached_content is not None
        
        # Get file info from NAS
        try:
            file_content = await connector.get_file_content(video_path)
            file_exists = file_content is not None
            file_size = len(file_content) if file_content else 0
        except Exception as e:
            file_exists = False
            file_size = 0
            
        # Get file extension and content type
        ext = os.path.splitext(video_path)[1].lower()
        content_type_map = {
            '.mp4': 'video/mp4', '.avi': 'video/x-msvideo', '.mov': 'video/quicktime',
            '.wmv': 'video/x-ms-wmv', '.flv': 'video/x-flv', '.webm': 'video/webm',
            '.mkv': 'video/x-matroska', '.m4v': 'video/mp4', '.3gp': 'video/3gpp', '.ogv': 'video/ogg'
        }
        content_type = content_type_map.get(ext, 'application/octet-stream')
        
        return {
            "video_path": video_path,
            "normalized_path": normalized_path,
            "file_exists": file_exists,
            "file_size": file_size,
            "file_extension": ext,
            "content_type": content_type,
            "is_cached": is_cached,
            "cache_key": cache_key,
            "cached_size": len(cached_content) if cached_content else 0,
            "supports_range_requests": True,
            "is_supported_format": ext in content_type_map
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get video info: {str(e)}")


@router.delete("/clear-video-cache/{video_path:path}")
async def clear_video_cache(video_path: str):
    """Clear cache for a specific video file to force fresh download"""
    try:
        from cache.redis_connector import cache_delete
        
        # Normalize the path
        normalized_path = video_path.replace('%20', ' ').strip()
        cache_key = f"media_file:video:{normalized_path}"
        
        # Delete from cache
        deleted = await cache_delete(cache_key)
        
        return {
            "success": True,
            "message": f"Cache cleared for video: {normalized_path}",
            "cache_key": cache_key,
            "was_cached": deleted
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear video cache: {str(e)}")


@router.get("/text/{text_path:path}")
async def admin_get_text(text_path: str, request: Request):
    """Serve text files for admin with smart Redis caching"""
    try:
        from utils.media_utils import get_media_from_storage
        return await get_media_from_storage(text_path, "text", include_response_time=True, request=request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pdf/{pdf_path:path}")
async def admin_get_pdf(pdf_path: str, request: Request):
    """Serve PDF files for admin with smart Redis caching"""
    try:
        from utils.media_utils import get_media_from_storage
        return await get_media_from_storage(pdf_path, "pdf", include_response_time=True, request=request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/csv/{csv_path:path}")
async def admin_get_csv(csv_path: str, request: Request):
    """Serve CSV files for admin with smart Redis caching"""
    try:
        from utils.media_utils import get_media_from_storage
        return await get_media_from_storage(csv_path, "csv", include_response_time=True, request=request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))