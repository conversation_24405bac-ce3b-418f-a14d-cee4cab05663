"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { API_BASE_URL } from "@/lib/api";

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: string;
  is_active: boolean;
  created_at: string;
  last_login?: string;
  annotator_mode?: "annotation" | "verification" | "supervision";
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  checkAuth: () => Promise<void>;
  logout: () => Promise<void>;
  login: () => Promise<void>;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  checkAuth: async () => {},
  logout: async () => {},
  login: async () => {},
  isLoading: false,
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const checkAuth = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/auth/verify`,
        {
          credentials: "include",
        }
      );

      if (response.ok) {
        const data = await response.json();
        setIsAuthenticated(true);
        setUser(data.user);
      } else {
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async () => {
    // This function is called after successful login to update auth state
    await checkAuth();
  };

  const logout = async () => {
    try {
      await fetch(
        `${API_BASE_URL}/auth/logout`,
        {
          method: "POST",
          credentials: "include",
        }
      );
      setIsAuthenticated(false);
      setUser(null);
      router.push("/");
    } catch (error) {
      console.error("Logout failed:", error);
      // Even if logout fails on server, clear local state
      setIsAuthenticated(false);
      setUser(null);
      router.push("/");
    }
  };

  // Only check auth automatically for protected routes
  useEffect(() => {
    const protectedRoutes = ["/admin", "/annotator", "/auditor", "/client", "/verifier"];
    const isProtectedRoute = protectedRoutes.some((route) =>
      pathname?.startsWith(route)
    );

    // Only check auth if we're on a protected route and not already authenticated
    if (isProtectedRoute && !isAuthenticated) {
      checkAuth();
    }
  }, [pathname, isAuthenticated]); // Add isAuthenticated to the dependency array

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, user, checkAuth, logout, login, isLoading }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
