from fastapi import APIRouter, Depends, HTTPException
from dependencies.auth import get_current_active_user
from .caption_routes import router as caption_router
from .ocr_routes import router as ocr_router
from .transcription_routes import router as transcription_router
from .vqa_routes import router as vqa_router
from .progress_routes import router as progress_router
from .file_allocation_routes import router as file_allocation_router
from .project_folders import router as project_folders_router

ai_processing_router = APIRouter()

# Include all routers under the same AI Processing tag
ai_processing_router.include_router(caption_router, prefix="/caption", tags=["AI Processing"])
ai_processing_router.include_router(ocr_router, prefix="/ocr", tags=["AI Processing"])
ai_processing_router.include_router(transcription_router, prefix="/transcription", tags=["AI Processing"])
ai_processing_router.include_router(vqa_router, prefix="/vqa", tags=["AI Processing"])
ai_processing_router.include_router(progress_router, prefix="/processing", tags=["AI Processing"])
ai_processing_router.include_router(file_allocation_router, tags=["AI Processing"])
ai_processing_router.include_router(project_folders_router, tags=["AI Processing"])