import { useState } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { BatchAllocationInfo, AssignmentProgress } from '../types';
import { API_BASE_URL } from "../../../../lib/api";
export const useBatchAllocations = () => {
  const [batchAllocations, setBatchAllocations] = useState<BatchAllocationInfo[]>([]);
  const [assignmentProgress, setAssignmentProgress] = useState<AssignmentProgress | null>(null);
  const [loadingBatchAllocations, setLoadingBatchAllocations] = useState(false);
  const [syncingBatchAllocations, setSyncingBatchAllocations] = useState(false);

  // Sync batch allocations to get real-time counts
  const syncBatchAllocations = async (projectId: number) => {
    try {
      setSyncingBatchAllocations(true);
      
      // First sync batch allocations
      const response = await authFetch(`${API_BASE_URL}/project-batches/projects/${projectId}/sync-batch-allocations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      
      const syncResult = await response.json();
      
      if (!syncResult.success) {
        throw new Error(syncResult.error || 'Failed to sync batch allocations');
      }
      
      // Then sync project progress to update the main table
      try {
        const progressResponse = await authFetch(`${API_BASE_URL}/project-batches/projects/${projectId}/sync-progress`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (progressResponse.ok) {
          const progressResult = await progressResponse.json();
          console.log('Progress sync result:', progressResult);
        }
      } catch (progressError) {
        console.warn('Progress sync failed:', progressError);
        // Don't fail the whole operation if progress sync fails
      }
      
      showToast.success(`Successfully synced ${syncResult.batches_synced} batches`);
      console.log('Sync result:', syncResult);
      
      return syncResult;
      
    } catch (error) {
      console.error("Error syncing batch allocations:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showToast.error(`Failed to sync batch allocations: ${errorMessage}`);
      throw error;
    } finally {
      setSyncingBatchAllocations(false);
    }
  };

  // Fetch batch allocations for a project with auto-sync
  const fetchBatchAllocations = async (projectId: number, autoSync: boolean = true) => {
    try {
      setLoadingBatchAllocations(true);
      
      // First sync the batch allocations to get real-time data
      if (autoSync) {
        try {
          await syncBatchAllocations(projectId);
        } catch (syncError) {
          console.warn("Sync failed, continuing with fetch:", syncError);
          // Continue with fetch even if sync fails
        }
      }
      
      const response = await authFetch(`${API_BASE_URL}/projects/${projectId}/batch-allocations`);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Check if the response indicates an error
      if (data.success === false) {
        throw new Error(data.error || 'Failed to load batch allocations');
      }
      
      setBatchAllocations(data.batches || []);
      setAssignmentProgress(data.progress || null);
      
      // Log success for debugging
      console.log(`Successfully loaded ${data.batches?.length || 0} batches for project ${projectId}`);
      console.log('Batch data:', data.batches);
      console.log('Assignment progress:', data.progress);
      
      return {
        batches: data.batches || [],
        progress: data.progress || null,
        totalBatches: data.batches?.length || 0
      };
      
    } catch (error) {
      console.error("Error fetching batch allocations:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showToast.error(`Failed to load batch allocations: ${errorMessage}`);
      
      // Set empty state to ensure UI shows no batches
      setBatchAllocations([]);
      setAssignmentProgress(null);
      
      return {
        batches: [],
        progress: null,
        totalBatches: 0
      };
    } finally {
      setLoadingBatchAllocations(false);
    }
  };

  // Clear batch allocations state
  const clearBatchAllocations = () => {
    setBatchAllocations([]);
    setAssignmentProgress(null);
  };

  return {
    batchAllocations,
    assignmentProgress,
    loadingBatchAllocations,
    syncingBatchAllocations,
    fetchBatchAllocations,
    syncBatchAllocations,
    clearBatchAllocations,
  };
};
