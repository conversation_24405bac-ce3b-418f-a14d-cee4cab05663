from fastapi import APIRouter, Depends, HTTPException, status, Query #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_
from sqlalchemy.orm import joinedload
from typing import List, Optional
from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.clients import Clients
from post_db.master_models.allocation_strategies import AllocationStrategies
from dependencies.auth import get_current_active_user, require_admin
from schemas.ProjectSchemas import (
    ProjectRegistryResponse,
    ProjectListResponse,
    ProjectFilterRequest,
    ClientInfo,
    AllocationStrategyInfo
)
from schemas.UserSchemas import SuccessResponse, ErrorResponse
import logging

logger = logging.getLogger('admin_project_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin Projects"],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
)

@router.get("/projects", response_model=ProjectListResponse)
async def list_projects(
    project_type: Optional[str] = Query(None, description="Filter by project type"),
    project_status: Optional[str] = Query(None, description="Filter by project status"),
    search: Optional[str] = Query(None, description="Search in project name or code"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    List all projects with filtering and pagination.
    """
    try:
        # Build the query
        query = select(ProjectsRegistry).options(
            joinedload(ProjectsRegistry.client),
            joinedload(ProjectsRegistry.allocation_strategy)
        )

        # Apply filters
        filters = []
        if project_type:
            filters.append(ProjectsRegistry.project_type == project_type)
        if project_status:
            filters.append(ProjectsRegistry.project_status == project_status)
        if search:
            search_filter = or_(
                ProjectsRegistry.project_name.ilike(f"%{search}%"),
                ProjectsRegistry.project_code.ilike(f"%{search}%")
            )
            filters.append(search_filter)

        if filters:
            query = query.where(and_(*filters))

        # Get total count
        count_query = select(func.count(ProjectsRegistry.id)).select_from(ProjectsRegistry)
        if filters:
            count_query = count_query.where(and_(*filters))
        total_result = await db.execute(count_query)
        total = total_result.scalar_one()

        # Apply pagination and ordering
        query = query.order_by(ProjectsRegistry.created_at.desc())
        query = query.offset((page - 1) * page_size).limit(page_size)

        # Execute query
        result = await db.execute(query)
        projects = result.unique().scalars().all()

        # Convert to response format
        project_responses = []
        for project in projects:
            client_info = None
            if project.client:
                client_info = ClientInfo(
                    id=project.client.id,
                    name=project.client.name,
                    username=project.client.username,
                    email=project.client.email
                )

            allocation_strategy_info = None
            if project.allocation_strategy:
                allocation_strategy_info = AllocationStrategyInfo(
                    id=project.allocation_strategy.id,
                    strategy_name=project.allocation_strategy.strategy_name,
                    strategy_type=getattr(project.allocation_strategy, 'strategy_type', None),
                    description=getattr(project.allocation_strategy, 'description', None)
                )

            project_response = ProjectRegistryResponse(
                id=project.id,
                project_code=project.project_code,
                project_name=project.project_name,
                project_type=project.project_type,
                client_id=project.client_id,
                client=client_info,
                database_name=project.database_name,
                database_host=project.database_host,
                database_port=project.database_port,
                database_connection_params=project.database_connection_params,
                folder_path=project.folder_path,
                batch_size=project.batch_size,
                allocation_strategy_id=project.allocation_strategy_id,
                allocation_strategy=allocation_strategy_info,
                annotation_requirements=project.annotation_requirements,
                instructions=project.instructions,
                project_status=project.project_status,
                priority_level=project.priority_level,
                total_files=project.total_files,
                total_batches=project.total_batches,
                completed_files=project.completed_files,
                active_annotators=project.active_annotators,
                project_deadline=project.project_deadline,
                created_at=project.created_at,
                updated_at=project.updated_at,
                last_sync_at=project.last_sync_at
            )
            project_responses.append(project_response)

        # Calculate total pages
        total_pages = (total + page_size - 1) // page_size

        return ProjectListResponse(
            projects=project_responses,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"Error listing projects: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving projects: {str(e)}"
        )

@router.get("/projects/{project_id:int}", response_model=ProjectRegistryResponse)
async def get_project_details(
    project_id: int,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    Get detailed information about a specific project.
    """
    try:
        query = select(ProjectsRegistry).options(joinedload(ProjectsRegistry.client)).where(ProjectsRegistry.id == project_id)
        result = await db.execute(query)
        project = result.unique().scalar_one_or_none()

        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        client_info = None
        if project.client:
            client_info = ClientInfo(
                id=project.client.id,
                name=project.client.name,
                username=project.client.username,
                email=project.client.email
            )

        return ProjectRegistryResponse(
            id=project.id,
            project_code=project.project_code,
            project_name=project.project_name,
            project_type=project.project_type,
            client_id=project.client_id,
            client=client_info,
            database_name=project.database_name,
            database_host=project.database_host,
            database_port=project.database_port,
            database_connection_params=project.database_connection_params,
            folder_path=project.folder_path,
            batch_size=project.batch_size,
            allocation_strategy_id=project.allocation_strategy_id,
            annotation_requirements=project.annotation_requirements,
            instructions=project.instructions,
            project_status=project.project_status,
            priority_level=project.priority_level,
            total_files=project.total_files,
            total_batches=project.total_batches,
            completed_files=project.completed_files,
            active_annotators=project.active_annotators,
            project_deadline=project.project_deadline,
            created_at=project.created_at,
            updated_at=project.updated_at,
            last_sync_at=project.last_sync_at
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving project details: {str(e)}"
        )

@router.get("/projects/types")
async def get_project_types(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    Get all unique project types.
    """
    try:
        logger.info("Fetching project types...")

        # Check if table exists and has data
        try:
            count_query = select(func.count(ProjectsRegistry.id))
            count_result = await db.execute(count_query)
            total_projects = count_result.scalar_one()
            logger.info(f"Total projects in database: {total_projects}")
        except Exception as table_error:
            logger.error(f"Error checking ProjectsRegistry table: {table_error}")
            return []

        if total_projects == 0:
            logger.info("No projects found in database")
            return []

        query = select(ProjectsRegistry.project_type).distinct().where(ProjectsRegistry.project_type.isnot(None))
        result = await db.execute(query)
        project_types = result.scalars().all()

        # Filter out None values and ensure all are strings
        valid_types = [t for t in project_types if t is not None and isinstance(t, str)]
        logger.info(f"Found {len(valid_types)} project types: {valid_types}")

        return valid_types

    except Exception as e:
        logger.error(f"Error getting project types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving project types: {str(e)}"
        )

@router.get("/projects/statuses")
async def get_project_statuses(
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    Get all unique project statuses.
    """
    try:
        logger.info("Fetching project statuses...")

        # Check if table exists and has data
        try:
            count_query = select(func.count(ProjectsRegistry.id))
            count_result = await db.execute(count_query)
            total_projects = count_result.scalar_one()
            logger.info(f"Total projects in database: {total_projects}")
        except Exception as table_error:
            logger.error(f"Error checking ProjectsRegistry table: {table_error}")
            return []

        if total_projects == 0:
            logger.info("No projects found in database")
            return ["active", "paused", "completed", "archived"]  # Return default statuses

        query = select(ProjectsRegistry.project_status).distinct().where(ProjectsRegistry.project_status.isnot(None))
        result = await db.execute(query)
        project_statuses = result.scalars().all()

        # Filter out None values and ensure all are strings
        valid_statuses = [s for s in project_statuses if s is not None and isinstance(s, str)]

        # Ensure we always have the basic statuses
        default_statuses = ["active", "paused", "completed", "archived"]
        for status in default_statuses:
            if status not in valid_statuses:
                valid_statuses.append(status)

        logger.info(f"Found {len(valid_statuses)} project statuses: {valid_statuses}")

        return valid_statuses

    except Exception as e:
        logger.error(f"Error getting project statuses: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving project statuses: {str(e)}"
        )
