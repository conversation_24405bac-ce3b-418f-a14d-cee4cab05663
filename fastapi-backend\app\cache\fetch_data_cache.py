"""
Fetch data-related caching functionality.
"""

from cache.redis_connector import cache_set, cache_get, cache_delete
from cache.base import serialize_for_cache
import logging
from core.config import settings
from datetime import datetime

logger = logging.getLogger('fetch_data_cache') 

# Cache key prefixes
TELEGRAM_CHANNELS_PREFIX = "telegram:channels"
TELEGRAM_CHANNEL_IMAGES_PREFIX = "telegram:channel_images:"
TELEGRAM_CHANNEL_DATES_PREFIX = "telegram:channel_dates:"
TELEGRAM_CHANNEL_ANALYTICS_PREFIX = "telegram:channel_analytics:"
TELEGRAM_AUTH_STATUS_PREFIX = "telegram:auth_status"
TELEGRAM_INDIVIDUAL_IMAGE_PREFIX = "telegram:image:"  # Individual image with O(1) access
# Cache TTL values (in seconds) - loaded from configuration
CHANNELS_TTL = settings.redis_settings.fetch_channels_ttl
CHANNEL_IMAGES_TTL = settings.redis_settings.fetch_channel_images_ttl
CHANNEL_DATES_TTL = settings.redis_settings.fetch_channel_dates_ttl
CHANNEL_ANALYTICS_TTL = settings.redis_settings.fetch_channel_analytics_ttl
AUTH_STATUS_TTL = settings.redis_settings.fetch_auth_status_ttl

def generate_channels_key():
    """
    Generate a cache key for Telegram channels list

    Returns:
        str: Cache key
    """
    return TELEGRAM_CHANNELS_PREFIX

def generate_channel_images_key(channel_id, date=None):
    """
    Generate a cache key for Telegram channel images

    Args:
        channel_id: Channel ID
        date: Optional date filter

    Returns:
        str: Cache key
    """
    if date:
        return f"{TELEGRAM_CHANNEL_IMAGES_PREFIX}{channel_id}:{date}"
    return f"{TELEGRAM_CHANNEL_IMAGES_PREFIX}{channel_id}"

def generate_channel_dates_key(channel_id):
    """
    Generate a cache key for Telegram channel dates

    Args:
        channel_id: Channel ID

    Returns:
        str: Cache key
    """
    return f"{TELEGRAM_CHANNEL_DATES_PREFIX}{channel_id}"

def generate_channel_analytics_key(channel_id):
    """
    Generate a cache key for Telegram channel analytics

    Args:
        channel_id: Channel ID

    Returns:
        str: Cache key
    """
    return f"{TELEGRAM_CHANNEL_ANALYTICS_PREFIX}{channel_id}"

def generate_auth_status_key():
    """
    Generate a cache key for Telegram authentication status

    Returns:
        str: Cache key
    """
    return TELEGRAM_AUTH_STATUS_PREFIX

def generate_individual_image_key(channel_id, message_id):
    """
    Generate a cache key for individual image with O(1) access

    Args:
        channel_id: Channel ID
        message_id: Message ID

    Returns:
        str: Cache key
    """
    return f"{TELEGRAM_INDIVIDUAL_IMAGE_PREFIX}{channel_id}:{message_id}"

async def cache_channels(channels):
    """
    Cache Telegram channels list

    Args:
        channels: List of channel dictionaries

    Returns:
        bool: Success status
    """
    key = generate_channels_key()
    ttl = CHANNELS_TTL

    # Serialize datetime objects to strings
    serialized_channels = serialize_for_cache(channels)

    result = await cache_set(key, serialized_channels, ttl)
    return result

async def get_cached_channels():
    """
    Get cached Telegram channels list

    Returns:
        list: Channels list or None if not found
    """
    key = generate_channels_key()
    result = await cache_get(key, json_decode=True)
    return result

async def cache_channel_images(channel_id, date, images, channel_title):
    """
    Cache Telegram channel images efficiently - no data duplication
    - Stores full image data individually for O(1) access
    - Stores only metadata in channel list

    Args:
        channel_id: Channel ID
        date: Optional date filter
        images: List of image dictionaries
        channel_title: Channel title

    Returns:
        bool: Success status
    """
    # Store individual images with full data for O(1) access
    individual_ttl = CHANNEL_IMAGES_TTL
    individual_success_count = 0
    
    # Prepare lightweight channel list (metadata only)
    channel_image_refs = []
    
    for image in images:
        if image.get('id') and image.get('image'):  # Ensure we have required data
            # Store full image data individually
            individual_key = generate_individual_image_key(channel_id, image['id'])
            individual_data = {
                'image_data': image.get('image'),  # Full base64 data stored here only
                'date': image.get('date'),
                'caption': image.get('caption', ''),
                'channel_id': channel_id,
                'message_id': image['id'],
                'channel_title': channel_title,
                'cached_at': datetime.now().isoformat(),
                'source': 'channel_cache'
            }
            
            if await cache_set(individual_key, individual_data, individual_ttl):
                individual_success_count += 1
                
                # Add lightweight reference to channel list (no image data)
                channel_image_refs.append({
                    'id': image['id'],
                    'date': image.get('date'),
                    'caption': image.get('caption', ''),
                    # NO 'image' field here - prevents duplication
                })

    # Store lightweight channel data (no duplicate image data)
    channel_key = generate_channel_images_key(channel_id, date)
    channel_data = {
        'image_refs': channel_image_refs,  # Only metadata, no image data
        'channel_title': channel_title,
        'total_images': len(channel_image_refs)
    }
    
    channel_success = await cache_set(channel_key, channel_data, CHANNEL_IMAGES_TTL)
    
    logger.info(f"Cached {len(images)} images for channel {channel_id} (date: {date if date else 'all'}) - "
                f"Channel refs: {channel_success}, Individual images: {individual_success_count}/{len(images)}")
    
    return channel_success and individual_success_count == len(images)

async def get_cached_channel_images(channel_id, date=None):
    """
    Get cached Telegram channel images
    Reconstructs full image data from individual cache entries

    Args:
        channel_id: Channel ID
        date: Optional date filter

    Returns:
        dict: Images data with full image data reconstructed
    """
    key = generate_channel_images_key(channel_id, date)
    logger.debug(f"Getting cached images for channel {channel_id} (date: {date if date else 'all'})")
    
    channel_data = await cache_get(key, json_decode=True)
    if not channel_data or 'image_refs' not in channel_data:
        return None
    
    # Reconstruct full images with data from individual cache
    full_images = []
    for image_ref in channel_data['image_refs']:
        image_id = image_ref.get('id')
        if image_id:
            # Get full image data from individual cache (O(1) access)
            individual_data = await get_individual_image_from_channel_cache(channel_id, image_id)
            if individual_data and 'image_data' in individual_data:
                full_images.append({
                    'id': image_id,
                    'date': image_ref.get('date'),
                    'caption': image_ref.get('caption', ''),
                    'image': individual_data['image_data'],  # Retrieved from individual cache
                    'channel_id': channel_id
                })
    
    return {
        'images': full_images,
        'channel_title': channel_data.get('channel_title', ''),
        'total_images': len(full_images)
    }

async def get_individual_image_from_channel_cache(channel_id, message_id, date=None):
    """
    Get individual image data with O(1) constant time access
    No for loops - direct Redis key lookup

    Args:
        channel_id: Channel ID
        message_id: Message ID
        date: Optional date filter (not used - kept for compatibility)

    Returns:
        dict: Individual image data or None if not found
    """
    # O(1) direct access using unique key
    individual_key = generate_individual_image_key(channel_id, message_id)
    cached_data = await cache_get(individual_key, json_decode=True)
    
    if cached_data:
        logger.debug(f"Found image {message_id} in cache for channel {channel_id} (O(1) access)")
        return cached_data
    
    logger.debug(f"Image {message_id} not found in cache for channel {channel_id}")
    return None

async def cache_channel_dates(channel_id, dates):
    """
    Cache Telegram channel dates

    Args:
        channel_id: Channel ID
        dates: List of date strings

    Returns:
        bool: Success status
    """
    key = generate_channel_dates_key(channel_id)
    ttl = CHANNEL_DATES_TTL

    logger.info(f"Caching {len(dates)} dates for channel {channel_id}")
    return await cache_set(key, {'dates': dates}, ttl)

async def get_cached_channel_dates(channel_id):
    """
    Get cached Telegram channel dates

    Args:
        channel_id: Channel ID

    Returns:
        list: Dates list or None if not found
    """
    key = generate_channel_dates_key(channel_id)
    logger.debug(f"Getting cached dates for channel {channel_id}")
    data = await cache_get(key, json_decode=True)
    return data.get('dates') if data else None

async def cache_channel_analytics(channel_id, analytics):
    """
    Cache Telegram channel analytics

    Args:
        channel_id: Channel ID
        analytics: Analytics data dictionary

    Returns:
        bool: Success status
    """
    key = generate_channel_analytics_key(channel_id)
    ttl = CHANNEL_ANALYTICS_TTL

    # Serialize datetime objects to strings
    serialized_analytics = serialize_for_cache(analytics)

    logger.info(f"Caching analytics for channel {channel_id}")
    return await cache_set(key, serialized_analytics, ttl)

async def get_cached_channel_analytics(channel_id):
    """
    Get cached Telegram channel analytics

    Args:
        channel_id: Channel ID

    Returns:
        dict: Analytics data or None if not found
    """
    key = generate_channel_analytics_key(channel_id)
    logger.debug(f"Getting cached analytics for channel {channel_id}")
    return await cache_get(key, json_decode=True)

async def cache_auth_status(status):
    """
    Cache Telegram authentication status

    Args:
        status: Authentication status dictionary

    Returns:
        bool: Success status
    """
    key = generate_auth_status_key()
    ttl = AUTH_STATUS_TTL

    logger.info(f"Caching Telegram authentication status")
    return await cache_set(key, status, ttl)

async def get_cached_auth_status():
    """
    Get cached Telegram authentication status

    Returns:
        dict: Authentication status or None if not found
    """
    key = generate_auth_status_key()
    logger.debug(f"Getting cached Telegram authentication status")
    return await cache_get(key, json_decode=True)

async def invalidate_channels_cache():
    """
    Invalidate Telegram channels cache

    Returns:
        bool: Success status
    """
    key = generate_channels_key()
    logger.info(f"Invalidating Telegram channels cache")
    return await cache_delete(key)

async def invalidate_channel_images_cache(channel_id, date=None):
    """
    Invalidate Telegram channel images cache

    Args:
        channel_id: Channel ID
        date: Optional date filter

    Returns:
        bool: Success status
    """
    key = generate_channel_images_key(channel_id, date)
    logger.info(f"Invalidating images cache for channel {channel_id} (date: {date if date else 'all'})")
    return await cache_delete(key)

async def invalidate_channel_dates_cache(channel_id):
    """
    Invalidate Telegram channel dates cache

    Args:
        channel_id: Channel ID

    Returns:
        bool: Success status
    """
    key = generate_channel_dates_key(channel_id)
    logger.info(f"Invalidating dates cache for channel {channel_id}")
    return await cache_delete(key)

async def invalidate_channel_analytics_cache(channel_id):
    """
    Invalidate Telegram channel analytics cache

    Args:
        channel_id: Channel ID

    Returns:
        bool: Success status
    """
    key = generate_channel_analytics_key(channel_id)
    logger.info(f"Invalidating analytics cache for channel {channel_id}")
    return await cache_delete(key)

async def invalidate_auth_status_cache():
    """
    Invalidate Telegram authentication status cache

    Returns:
        bool: Success status
    """
    key = generate_auth_status_key()
    logger.info(f"Invalidating Telegram authentication status cache")
    return await cache_delete(key)

async def get_cached_image_data(channel_id, message_id, date=None):
    """
    Get cached individual image data from channel cache ONLY
    No fallback - pure channel cache approach

    Args:
        channel_id: Channel ID
        message_id: Message ID
        date: Optional date filter for channel cache lookup

    Returns:
        dict: Image data from channel cache or None if not found
    """
    return await get_individual_image_from_channel_cache(channel_id, message_id, date)


