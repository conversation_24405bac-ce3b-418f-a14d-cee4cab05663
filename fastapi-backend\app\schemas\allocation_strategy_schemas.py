# app/schemas/allocation_strategies.py
from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from post_db.master_models.allocation_strategies import (
    StrategyType  # enums from your model
)

JsonDict = Dict[str, Any]

class AllocationStrategyBase(BaseModel):
    strategy_name: str = Field(..., max_length=255)
    strategy_type: StrategyType = StrategyType.SEQUENTIAL
    description: Optional[str] = None

    # Strategy Configuration
    num_annotators: int = Field(default=1, ge=1, description="Number of annotators required for this strategy")
    
    # Verification Configuration
    requires_verification: bool = False

    # AI Configuration
    requires_ai_preprocessing: bool = False

    # Quality & Audit Configuration
    requires_audit: bool = False
    quality_requirements: Optional[JsonDict] = None

    configuration: Optional[JsonDict] = None

class AllocationStrategyCreate(AllocationStrategyBase):
    pass  # all fields above are acceptable on create

class AllocationStrategyUpdate(BaseModel):
    # all optional for PATCH semantics
    strategy_name: Optional[str] = Field(None, max_length=255)
    strategy_type: Optional[StrategyType] = None
    description: Optional[str] = None

    # Strategy Configuration
    num_annotators: Optional[int] = Field(None, ge=1, description="Number of annotators required for this strategy")
    
    # Verification Configuration
    requires_verification: Optional[bool] = None

    # AI Configuration
    requires_ai_preprocessing: Optional[bool] = None

    # Quality & Audit Configuration
    requires_audit: Optional[bool] = None
    quality_requirements: Optional[JsonDict] = None

    configuration: Optional[JsonDict] = None

class AllocationStrategyOut(AllocationStrategyBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True  