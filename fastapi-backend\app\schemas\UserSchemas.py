from pydantic import BaseModel, EmailStr, field_validator, Field, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime
from post_db.master_models.users import UserRole

# Request Schemas
class UserRegisterRequest(BaseModel):
    """Schema for user registration request"""
    username: str
    password: str = Field(..., min_length=8, max_length=32)
    confirm_password: str = Field(..., min_length=8, max_length=32)
    role: str
    full_name: str
    email: EmailStr

    @field_validator('confirm_password')
    def check_passwords_match(cls, v, info):
        if info.data.get('password') is not None and v != info.data.get('password'):
            raise ValueError('Passwords do not match')
        return v

    model_config = ConfigDict(from_attributes=True)

class UserCreate(BaseModel):
    """Schema for creating a user in the database"""
    username: str
    password_hash: str
    email: EmailStr
    full_name: str
    role: UserRole
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

class AddUserRequest(BaseModel):
    """Schema for adding a user via admin, with plaintext password"""
    username: str
    password: str = Field(..., min_length=8, max_length=64)
    full_name: str
    email: EmailStr
    role: UserRole
    is_active: bool = True
    annotation_mode: Optional[str] = None
    project_code: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class UserUpdate(BaseModel):
    """Schema for updating a user in the database"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    password_hash: Optional[str] = None
    annotator_mode: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class LoginRequest(BaseModel):
    """Schema for login request"""
    username: str
    password: str

class ChangePasswordRequest(BaseModel):
    """Schema for password change request"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=32)
    confirm_password: str = Field(..., min_length=8, max_length=32)

    @field_validator('confirm_password')
    def check_passwords_match(cls, v, info):
        if info.data.get('new_password') is not None and v != info.data.get('new_password'):
            raise ValueError('New password and confirmation do not match')
        return v


class RefreshTokenRequest(BaseModel):
    """Schema for refresh token request"""
    refresh_token: str

# Response Schemas
class UserResponse(BaseModel):
    """Schema for user response"""
    id: int
    username: str
    email: EmailStr
    full_name: str
    role: str
    is_active: bool
    created_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    annotator_mode: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class TokenResponse(BaseModel):
    """Schema for token response"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user: Optional[UserResponse] = None

class AccessTokenResponse(BaseModel):
    """Schema for access token response"""
    access_token: str
    token_type: str = "bearer"

class SuccessResponse(BaseModel):
    """Schema for success response"""
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseModel):
    """Schema for error response"""
    success: bool = False
    error: str
    details: Optional[List[Dict[str, Any]]] = None