"""Base NAS connector class with async support."""

from pydantic import BaseModel
from pathlib import Path
import logging
from typing import Optional, List, Dict, Any, Union

logger = logging.getLogger("StorageConnector")

class Credentials(BaseModel):
    url: str
    username: str
    password: str

class StorageConnector:
    def __init__(self, url: str, username: str, password: str):
        self.url = url
        self.username = username
        self.password = password
        self.authenticated = False
        self.logger = logger

    def normalize_path(self, path: Union[str, Path]) -> str:
        if isinstance(path, Path):
            path = str(path)
        normalized = path.replace('\\', '/')
        while '//' in normalized:
            normalized = normalized.replace('//', '/')
        if normalized and not normalized.startswith('/'):
            normalized = '/' + normalized
        return normalized

    def split_path(self, path: Union[str, Path]) -> tuple[str, str]:
        path = self.normalize_path(path)
        if '/' not in path or path == '/':
            return '/', path
        if path.endswith('/'):
            return path, ''
        directory = '/'.join(path.split('/')[:-1]) or '/'
        filename = path.split('/')[-1]
        return directory, filename

    async def authenticate(self) -> bool:
        """Authenticate with the NAS server."""
        self.authenticated = False
        return False

    async def list_directory(self, path: str = "/") -> List[Dict[str, str]]:
        """List contents of a directory."""
        raise NotImplementedError("Subclasses must implement list_directory()")
    
    async def directory_exists(self, directory_path: str) -> bool:
        """Check if a directory exists."""
        raise NotImplementedError("Subclasses must implement directory_exists()")

    async def create_directory(self, directory_path: str) -> bool:
        """Create a directory."""
        raise NotImplementedError("Subclasses must implement create_directory()")

    async def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about a file."""
        raise NotImplementedError("Subclasses must implement get_file_info()")

    async def get_file_content(self, file_path: str) -> Optional[bytes]:
        """Get the content of a file."""
        raise NotImplementedError("Subclasses must implement get_file_content()")

    async def file_exists(self, file_path: str) -> bool:
        """Check if a file exists."""
        raise NotImplementedError("Subclasses must implement file_exists()")

    async def save_file(self, file_path: str, content: Union[str, bytes]) -> bool:
        """Save content to a file."""
        raise NotImplementedError("Subclasses must implement save_file()")
        
