import logging
from typing import Dict, Any, Optional
import httpx
from core.config import settings
from .model_utils import async_exception_handler

# Configure logging
logger = logging.getLogger('image_ocr_service')

class ImageOCRService:
    def __init__(self):
        self.wrapper_url = settings.api_settings.wrapper_url
    def create_client(self) -> httpx.AsyncClient:
        return httpx.AsyncClient(
            base_url=self.wrapper_url.rstrip('/'),
            timeout=None,
            follow_redirects=True
        )

    @async_exception_handler
    async def extract_text(
        self,
        image_data,
        model_name: Optional[str] = None,
        custom_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        original_filename = getattr(image_data, 'filename', 'image.jpg')
        
        try:
            async with self.create_client() as client:
                # Reset file position to beginning if available
                if hasattr(image_data, 'seek'):
                    await image_data.seek(0)
                
                # Handle FileWrapper file property
                if hasattr(image_data, 'file') and hasattr(image_data.file, '__await__'):
                    file_stream = await image_data.file
                else:
                    file_stream = image_data.file if hasattr(image_data, 'file') else image_data
                
                files = {
                    "image": (original_filename, file_stream,
                            getattr(image_data, 'content_type', 'image/jpeg'))
                }
                
                if model_name is not None and str(model_name).lower() == "string":
                    model_name = None
                if custom_prompt is not None and str(custom_prompt).lower() == "string":
                    custom_prompt = None
                params: Dict[str, Any] = {}
                if model_name:
                    params["model_name"] = model_name
                if custom_prompt:
                    params["custom_prompt"] = custom_prompt

                response = await client.post(
                    "/wrapper/extract",
                    files=files,
                    params=params
                )
                response.raise_for_status()
                result = response.json()
            
            return {
                "file_name": original_filename,
                "extracted_text": result.get("extracted_text", ""),
                "status": "success"
            }
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error forwarding to wrapper: {error_msg}", exc_info=True)
            return {
                "file_name": original_filename,
                "error": error_msg,
                "status": "failed"
            }

def get_image_ocr_service() -> ImageOCRService:
    """Get the image OCR service instance"""
    return ImageOCRService()