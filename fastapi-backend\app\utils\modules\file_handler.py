import os
import logging

logger = logging.getLogger('file_handler')

def delete_file(file_path):
    """Deletes a file at the given path and returns success status"""
    if not file_path:
        return False
        
    if os.path.exists(file_path):
        os.remove(file_path)
        logger.debug(f"Deleted temporary file: {file_path}")
        return True
    else:
        logger.debug(f"File does not exist, cannot delete: {file_path}")
        return False

def get_file_extension(path):
    """Extract file extension from path"""
    if not path:
        return '.png', 'image/png'
        
    path_lower = path.lower()
    if path_lower.endswith('.jpg') or path_lower.endswith('.jpeg'):
        return '.jpg', 'image/jpeg'
    elif path_lower.endswith('.png'):
        return '.png', 'image/png'
    elif path_lower.endswith('.pdf'):
        return '.pdf', 'application/pdf'
    elif path_lower.endswith('.gif'):
        return '.gif', 'image/gif'
    elif path_lower.endswith('.bmp'):
        return '.bmp', 'image/bmp'
    elif path_lower.endswith('.tiff') or path_lower.endswith('.tif'):
        return '.tiff', 'image/tiff'
    else:
        return '.png', 'image/png'

def ensure_directory_exists(directory_path):
    """Ensure a directory exists, create if it doesn't"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path, exist_ok=True)
        logger.debug(f"Created directory: {directory_path}")
        return True
    return True

def get_file_size(file_path):
    """Get file size in bytes"""
    if os.path.exists(file_path):
        return os.path.getsize(file_path)
    return 0
