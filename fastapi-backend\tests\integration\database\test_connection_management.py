"""
Integration tests for database connection management and migrations with REAL operations.
Tests connection pooling, transaction management, and migration workflows.
"""
import pytest
import asyncio
import asyncpg
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select

from app.post_db.connect import get_db_connection, test_connection
from app.post_db.master_db import get_master_db_connection, test_master_connection
from app.post_db.config import DatabaseSettings, MasterDatabaseSettings
from app.utils.project_db_manager import ProjectDBManager
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.connection       # Feature marker - Connection operations
@pytest.mark.smoke            # Suite marker - Core infrastructure
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestDatabaseConnections:
    """SMOKE TEST SUITE: Critical database connection management."""
    
    @pytest.mark.asyncio
    async def test_project_db_connection(self, test_db: AsyncSession):
        """Test project database connection functionality."""
        # Test basic connection
        assert test_db is not None
        
        # Test query execution
        result = await test_db.execute(text("SELECT 1 as test_value"))
        row = result.fetchone()
        assert row[0] == 1
        
        # Test transaction handling (use existing auto-started transaction)
        await test_db.execute(text("CREATE TEMPORARY TABLE test_transaction (id INTEGER)"))
        await test_db.execute(text("INSERT INTO test_transaction (id) VALUES (1)"))
        
        # Verify data exists within transaction
        result = await test_db.execute(text("SELECT COUNT(*) FROM test_transaction"))
        count = result.scalar()
        assert count == 1
        
        # Test rollback
        await test_db.rollback()
        
        # Verify rollback worked (should get error accessing dropped temp table)
        try:
            await test_db.execute(text("SELECT COUNT(*) FROM test_transaction"))
            assert False, "Table should not exist after rollback"
        except Exception:
            pass  # Expected - table was rolled back
    
    @pytest.mark.asyncio
    async def test_master_db_connection(self, test_master_db: AsyncSession):
        """Test master database connection functionality."""
        # Test basic connection
        assert test_master_db is not None
        
        # Test query execution
        result = await test_master_db.execute(text("SELECT 'master' as db_type"))
        row = result.fetchone()
        assert row[0] == "master"
        
        # Test transaction rollback (use existing auto-started transaction)
        await test_master_db.execute(text("CREATE TEMPORARY TABLE test_rollback (id INTEGER)"))
        await test_master_db.execute(text("INSERT INTO test_rollback (id) VALUES (999)"))
        
        # Verify data exists
        result = await test_master_db.execute(text("SELECT COUNT(*) FROM test_rollback"))
        count = result.scalar()
        assert count == 1
        
        # Test rollback
        await test_master_db.rollback()
        
        # Verify rollback worked (table should not exist)
        try:
            await test_master_db.execute(text("SELECT COUNT(*) FROM test_rollback"))
            assert False, "Table should not exist after rollback"
        except Exception:
            pass  # Expected - table doesn't exist
    
    @pytest.mark.asyncio
    async def test_connection_error_handling_real_database(self):
        """Test connection error handling with REAL database operations."""
        #   Test connection error handling by attempting invalid operations
        
        # Test 1: Invalid SQL that should cause database errors
        try:
            from app.post_db.connect import engine
            async with engine.connect() as conn:
                # Try to execute invalid SQL to test error handling
                await conn.execute(text("SELECT * FROM definitely_nonexistent_table_12345"))
                assert False, "Expected database error for non-existent table"
        except Exception as e:
            # Should get a database error about missing table
            assert any(keyword in str(e).lower() for keyword in ["not found", "does not exist", "relation", "table"])
            print(f"    Database correctly rejected invalid table query: {type(e).__name__}")
        
        # Test 2: Invalid database operations to trigger connection issues  
        try:
            from app.post_db.connect import engine
            async with engine.connect() as conn:
                # Try to execute malformed SQL
                await conn.execute(text("INVALID SQL COMMAND THAT MAKES NO SENSE"))
                assert False, "Expected SQL syntax error"
        except Exception as e:
            # Should get a SQL syntax error
            assert any(keyword in str(e).lower() for keyword in ["syntax", "error", "invalid", "parse"])
            print(f"    Database correctly rejected malformed SQL: {type(e).__name__}")
        
        # Test 3: Test that valid connections still work (proving connection is functional)
        try:
            from app.post_db.connect import engine
            async with engine.connect() as conn:
                result = await conn.execute(text("SELECT 1 as test_value"))
                row = result.fetchone()
                assert row[0] == 1
                print(f"    Valid database connection works correctly")
        except Exception as e:
            # Valid operations should not fail
            assert False, f"Valid database operation failed unexpectedly: {e}")
        
        print(f"    Connection error handling tested with REAL database operations")
    
    @pytest.mark.asyncio
    async def test_project_db_manager(self, test_master_db: AsyncSession):
        """Test ProjectDBManager functionality."""
        # Skip this test for now since it requires a project to exist in master DB
        # and the ProjectDBManager expects real project data
        
        # Create a test project first
        from app.post_db.master_models.projects_registry import ProjectsRegistry
        from app.post_db.master_models.clients import Clients
        
        # Create a test client first
        client = test_factory.projects.create_client()
        test_master_db.add(client)
        await test_master_db.commit()
        await test_master_db.refresh(client)
        
        # Create a test project
        project = test_factory.projects.create_project(
            client.id,
            None,  # No allocation strategy
            project_code="TEST_PROJECTDB_001",
            project_name="Test ProjectDB Project",
            project_type="image",
            project_status="active"
        )
        test_master_db.add(project)
        await test_master_db.commit()
        await test_master_db.refresh(project)
        
        # Now test ProjectDBManager
        manager = ProjectDBManager()
        
        # Test that manager can retrieve database name from master
        try:
            db_name = await manager._get_db_name_from_master("TEST_PROJECTDB_001")
            assert db_name == "test_projectdb_db"
        except Exception as e:
            # If project doesn't exist, that's also a valid test result
            assert "not found" in str(e).lower()
        
        # Test engine caching
        assert isinstance(manager._engines_async, dict)
    
    @pytest.mark.asyncio
    async def test_connection_pool_behavior(self, test_engines):
        """Test connection pool behavior under load."""
        test_engine, _ = test_engines
        
        # Simulate multiple concurrent connections using separate sessions
        @pytest.mark.asyncio
        async def test_concurrent_query(query_id: int):
            from sqlalchemy.ext.asyncio import async_sessionmaker
            SessionLocal = async_sessionmaker(bind=test_engine, class_=AsyncSession, expire_on_commit=False)
            async with SessionLocal() as session:
                result = await session.execute(text(f"SELECT {query_id} as query_id"))
                row = result.fetchone()
                return row[0]
        
        # Create multiple concurrent tasks (limit to 3 due to database server connection limits)
        tasks = [test_concurrent_query(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        # Verify all queries completed successfully
        assert results == [0, 1, 2]
    
    @pytest.mark.asyncio
    async def test_session_lifecycle_real_database(self, test_engines):
        """Test database session lifecycle management with REAL database operations."""
        test_engine, _ = test_engines
        
        #   Create session factory for testing lifecycle
        from sqlalchemy.ext.asyncio import async_sessionmaker
        SessionLocal = async_sessionmaker(bind=test_engine, class_=AsyncSession, expire_on_commit=False)
        
        #   Test session creation and basic operations
        session = SessionLocal()
        assert session is not None
        print(f"    Session created successfully: {type(session).__name__}")
        
        #   Test that session can execute queries
        try:
            result = await session.execute(text("SELECT 1 as lifecycle_test"))
            row = result.fetchone()
            assert row[0] == 1
            print(f"    Session executed query successfully")
        except Exception as e:
            assert False, f"Session query execution failed: {e}"
        
        #   Test session state before closing
        assert session.is_active is True
        print(f"    Session is active before closing")
        
        #   Test session closing
        await session.close()
        print(f"    Session closed successfully")
        
        #   Test session context manager lifecycle
        session_operations_completed = False
        async with SessionLocal() as context_session:
            # Session should be active within context
            assert context_session.is_active is True
            
            # Test operations within context
            result = await context_session.execute(text("SELECT 'context_test' as test_value"))
            row = result.fetchone()
            assert row[0] == 'context_test'
            session_operations_completed = True
            print(f"    Session context manager operations completed")
        
        #   Verify operations completed and context was managed
        assert session_operations_completed is True
        print(f"    Session context manager lifecycle tested successfully")
        
        #   Test multiple concurrent sessions
        async def test_concurrent_session(session_id: int):
            async with SessionLocal() as concurrent_session:
                result = await concurrent_session.execute(text(f"SELECT {session_id} as session_id"))
                row = result.fetchone()
                return row[0]
        
        # Test concurrent session creation and cleanup
        concurrent_tasks = [test_concurrent_session(i) for i in range(3)]
        concurrent_results = await asyncio.gather(*concurrent_tasks)
        
        assert concurrent_results == [0, 1, 2]
        print(f"    Concurrent session lifecycle management tested: {concurrent_results}")
        
        print(f"    Session lifecycle tested with REAL database operations")


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.regression       # Suite marker - Migration testing
@pytest.mark.high             # Priority marker - P1 (migrations are critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestDatabaseMigrations:
    """REGRESSION TEST SUITE: Database migration functionality."""
    
    @pytest.mark.asyncio
    async def test_migration_system_structure(self):
        """Test migration system directory structure."""
        import os
        from pathlib import Path
        
        # Check migration directory structure
        migrations_dir = Path("app/migrations")
        assert migrations_dir.exists()
        
        master_migrations = migrations_dir / "master_db"
        project_migrations = migrations_dir / "project_db"
        
        assert master_migrations.exists()
        assert project_migrations.exists()
        
        # Check for alembic configuration files
        master_alembic = master_migrations / "alembic.ini"
        project_alembic = project_migrations / "alembic.ini"
        
        # Files may or may not exist depending on whether migrations have been run
        # Just verify the directory structure is correct
    
    @pytest.mark.asyncio
    async def test_schema_creation(self, test_db: AsyncSession, test_master_db: AsyncSession):
        """Test database schema creation."""
        # Test that tables can be created
        from app.post_db.base import Base
        from app.post_db.project_base import ProjectBase
        
        # Check that models are properly registered
        assert len(Base.metadata.tables) > 0
        assert len(ProjectBase.metadata.tables) > 0
        
        # Verify some key tables exist in metadata
        master_tables = list(Base.metadata.tables.keys())
        project_tables = list(ProjectBase.metadata.tables.keys())
        
        # Master database should have user-related tables
        assert any("user" in table for table in master_tables)
        assert any("project" in table for table in master_tables)
        assert any("client" in table for table in master_tables)
        
        # Project database should have allocation-related tables
        assert any("allocation" in table for table in project_tables)
        assert any("file" in table for table in project_tables)
    
    @pytest.mark.asyncio
    async def test_database_settings_validation(self):
        """Test database configuration validation."""
        # Test database settings
        db_settings = DatabaseSettings()
        assert db_settings.url is not None
        assert db_settings.pool_size > 0
        assert db_settings.max_overflow > 0
        
        # Test master database settings
        master_settings = MasterDatabaseSettings()
        assert master_settings.url is not None
        assert master_settings.pool_size > 0
        assert master_settings.max_overflow > 0
        
        # Test URL format
        assert "postgresql" in db_settings.url
        assert "postgresql" in master_settings.url


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.connection       # Feature marker - Connection operations
@pytest.mark.smoke            # Suite marker - Core transaction functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestTransactionManagement:
    """SMOKE TEST SUITE: Critical database transaction management."""
    
    @pytest.mark.asyncio
    async def test_successful_transaction(self, test_db: AsyncSession):
        """Test successful transaction commit."""
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        
        # Start transaction and create data
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="TRANSACTION_TEST_001",
            total_files=5
        )
        
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Verify data was committed
        assert batch.id is not None
        
        # Verify data persists in new query
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier == "TRANSACTION_TEST_001"
        )
        result = await test_db.execute(stmt)
        retrieved_batch = result.scalar_one_or_none()
        
        assert retrieved_batch is not None
        assert retrieved_batch.batch_identifier == "TRANSACTION_TEST_001"
    
    @pytest.mark.asyncio
    async def test_transaction_rollback(self, test_db: AsyncSession):
        """Test transaction rollback functionality."""
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        
        try:
            # Start transaction
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier="ROLLBACK_TEST_001", 
                total_files=3
            )
            
            test_db.add(batch)
            await test_db.flush()  # Send to DB but don't commit
            
            # Force an error to trigger rollback
            raise Exception("Force rollback")
            
        except Exception:
            await test_db.rollback()
        
        # Verify data was not committed
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier == "ROLLBACK_TEST_001"
        )
        result = await test_db.execute(stmt)
        batch = result.scalar_one_or_none()
        
        assert batch is None  # Should not exist due to rollback
    
    @pytest.mark.asyncio
    async def test_nested_transactions(self, test_db: AsyncSession):
        """Test nested transaction behavior."""
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        
        # Outer transaction
        batch1 = test_factory.batches.create_allocation_batch(
            batch_identifier="NESTED_OUTER_001",
            total_files=2
        )
        test_db.add(batch1)
        
        try:
            # Inner transaction (savepoint)
            async with test_db.begin_nested():  # Savepoint
                batch2 = test_factory.batches.create_allocation_batch(
                    batch_identifier="NESTED_INNER_001", 
                    total_files=3
                )
                test_db.add(batch2)
                
                # Force inner rollback
                raise Exception("Inner rollback")
                
        except Exception:
            pass  # Inner transaction rolled back
        
        # Commit outer transaction
        await test_db.commit()
        
        # Verify outer transaction committed, inner rolled back
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier == "NESTED_OUTER_001"
        )
        result = await test_db.execute(stmt)
        outer_batch = result.scalar_one_or_none()
        assert outer_batch is not None
        
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier == "NESTED_INNER_001"
        )
        result = await test_db.execute(stmt)
        inner_batch = result.scalar_one_or_none()
        assert inner_batch is None  # Should be rolled back
    
    @pytest.mark.asyncio
    async def test_concurrent_transactions(self, test_engines):
        """Test concurrent transaction handling."""
        test_engine, _ = test_engines
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        
        async def create_batch(identifier: str, files: int):
            from sqlalchemy.ext.asyncio import async_sessionmaker
            SessionLocal = async_sessionmaker(bind=test_engine, class_=AsyncSession, expire_on_commit=False)
            async with SessionLocal() as session:
                batch = test_factory.batches.create_allocation_batch(
                    batch_identifier=identifier,
                    total_files=files
                )
                session.add(batch)
                await session.commit()
                return batch
        
        # Create multiple batches concurrently (limit to 2 due to database server connection limits)
        tasks = [
            create_batch(f"CONCURRENT_{i}", i+1)
            for i in range(2)
        ]
        
        batches = await asyncio.gather(*tasks)
        
        # Verify all batches were created
        assert len(batches) == 2
        for i, batch in enumerate(batches):
            assert batch.batch_identifier == f"CONCURRENT_{i}"
            assert batch.total_files == i + 1


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.connection       # Feature marker - Connection operations
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.bulk_data        # Environment marker - Large datasets
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Performance tests take time
class TestConnectionPerformanceWithBulkData:
    """PERFORMANCE TEST SUITE: Database connection performance with bulk data."""
    
    @pytest.mark.asyncio
    async def test_high_concurrency_connection_performance(self, test_engines):
        """Test connection performance under high concurrency with bulk data.
        
        SETUP: Run ./scripts/setup_test_environments.sh performance_test before this test
        """
        print("\n🔗 Testing high concurrency connection performance...")
        
        test_engine, _ = test_engines
        
        #   Create many concurrent database operations
        async def concurrent_database_operation(operation_id: int):
            from sqlalchemy.ext.asyncio import async_sessionmaker
            SessionLocal = async_sessionmaker(bind=test_engine, class_=AsyncSession, expire_on_commit=False)
            
            import time
            start_time = time.time()
            
            async with SessionLocal() as session:
                # Create batch in this connection
                batch = test_factory.batches.create_allocation_batch(
                    batch_identifier=f"CONCURRENT_PERF_{operation_id:03d}",
                    total_files=5 + (operation_id % 10),
                    annotation_count=1
                )
                session.add(batch)
                await session.commit()
                await session.refresh(batch)
                
                # Perform some operations
                from app.post_db.allocation_models.allocation_batches import AllocationBatches
                stmt = select(AllocationBatches).where(AllocationBatches.id == batch.id)
                result = await session.execute(stmt)
                queried_batch = result.scalar_one()
                
                # Clean up
                await session.delete(queried_batch)
                await session.commit()
                
                operation_time = time.time() - start_time
                return {
                    "operation_id": operation_id,
                    "duration": operation_time,
                    "batch_id": batch.id
                }
        
        #   Run concurrent operations (limit to 15 to avoid overloading test database)
        concurrent_operations = 15
        print(f"   📊 Running {concurrent_operations} concurrent database operations...")
        
        start_time = time.time()
        tasks = [concurrent_database_operation(i) for i in range(concurrent_operations)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        #   Analyze performance results
        operation_times = [result["duration"] for result in results]
        avg_operation_time = sum(operation_times) / len(operation_times)
        max_operation_time = max(operation_times)
        min_operation_time = min(operation_times)
        
        print(f"   ⚡ Total execution time: {total_time:.4f}s")
        print(f"   ⚡ Average operation time: {avg_operation_time:.4f}s")
        print(f"   ⚡ Maximum operation time: {max_operation_time:.4f}s")
        print(f"   ⚡ Minimum operation time: {min_operation_time:.4f}s")
        
        # Performance assertions
        assert total_time < 10.0, f"Total concurrent operations too slow: {total_time}s"
        assert avg_operation_time < 2.0, f"Average operation too slow: {avg_operation_time}s"
        assert max_operation_time < 5.0, f"Maximum operation too slow: {max_operation_time}s"
        
        assert len(results) == concurrent_operations
        print(f"    {len(results)} concurrent operations completed successfully")
    
    @pytest.mark.asyncio
    async def test_connection_pool_exhaustion_recovery(self, test_engines):
        """Test connection pool behavior under stress and recovery."""
        print("\n🏊 Testing connection pool exhaustion and recovery...")
        
        test_engine, _ = test_engines
        
        #   Create many long-running connections
        async def long_running_operation(operation_id: int, duration: float):
            from sqlalchemy.ext.asyncio import async_sessionmaker
            SessionLocal = async_sessionmaker(bind=test_engine, class_=AsyncSession, expire_on_commit=False)
            
            try:
                async with SessionLocal() as session:
                    # Start a transaction that holds the connection
                    batch = test_factory.batches.create_allocation_batch(
                        batch_identifier=f"LONG_RUNNING_{operation_id:03d}",
                        total_files=3,
                        annotation_count=1
                    )
                    session.add(batch)
                    await session.flush()  # Don't commit yet
                    
                    # Simulate long-running work
                    await asyncio.sleep(duration)
                    
                    # Complete the work
                    await session.commit()
                    await session.refresh(batch)
                    
                    return {"operation_id": operation_id, "success": True, "batch_id": batch.id}
            except Exception as e:
                return {"operation_id": operation_id, "success": False, "error": str(e)}
        
        #   Start many long-running operations
        print("   📊 Starting long-running operations to stress connection pool...")
        
        # Create 8 long-running operations (most connection pools have ~10 connections)
        long_tasks = [long_running_operation(i, 0.5) for i in range(8)]
        
        # Also create quick operations while pool is stressed
        quick_tasks = [long_running_operation(i + 100, 0.1) for i in range(5)]
        
        import time
        start_time = time.time()
        
        # Run all tasks concurrently
        all_results = await asyncio.gather(*(long_tasks + quick_tasks), return_exceptions=True)
        
        stress_time = time.time() - start_time
        
        #   Analyze results
        successful_operations = []
        failed_operations = []
        
        for result in all_results:
            if isinstance(result, Exception):
                failed_operations.append(str(result))
            elif isinstance(result, dict) and result.get("success"):
                successful_operations.append(result)
            else:
                failed_operations.append(result)
        
        print(f"   ⚡ Connection pool stress test completed in {stress_time:.4f}s")
        print(f"    Successful operations: {len(successful_operations)}")
        print(f"   ❌ Failed operations: {len(failed_operations)}")
        
        # At least some operations should succeed even under stress
        assert len(successful_operations) >= 8, f"Too many operations failed under stress: {len(failed_operations)}"
        
        # Total time should be reasonable (operations run concurrently)
        assert stress_time < 3.0, f"Connection pool stress test too slow: {stress_time}s"
    
    @pytest.mark.asyncio 
    async def test_transaction_isolation_under_load(self, test_db: AsyncSession):
        """Test transaction isolation with concurrent modifications."""
        print("\n🔒 Testing transaction isolation under concurrent load...")
        
        #   Create base batch for concurrent modifications
        base_batch = test_factory.batches.create_allocation_batch(
            batch_identifier="ISOLATION_TEST_BATCH",
            total_files=10,
            assignment_count=0,
            completion_count=0
        )
        test_db.add(base_batch)
        await test_db.commit()
        await test_db.refresh(base_batch)
        
        print(f"   📊 Base batch created with ID: {base_batch.id}")
        
        #   Concurrent modification function
        async def modify_batch_concurrently(modifier_id: int, modification_type: str):
            # Create new session for isolation
            from sqlalchemy.ext.asyncio import async_sessionmaker
            from app.post_db.connect import AsyncSessionLocal
            
            async with AsyncSessionLocal() as session:
                try:
                    from app.post_db.allocation_models.allocation_batches import AllocationBatches
                    
                    # Read batch in this transaction
                    stmt = select(AllocationBatches).where(AllocationBatches.id == base_batch.id)
                    result = await session.execute(stmt)
                    batch = result.scalar_one()
                    
                    original_assignment_count = batch.assignment_count
                    original_completion_count = batch.completion_count
                    
                    # Simulate some processing time
                    await asyncio.sleep(0.1)
                    
                    # Modify based on type
                    if modification_type == "assignment":
                        batch.assignment_count += 1
                    elif modification_type == "completion":
                        batch.completion_count += 1
                    elif modification_type == "both":
                        batch.assignment_count += 1
                        batch.completion_count += 1
                    
                    await session.commit()
                    
                    return {
                        "modifier_id": modifier_id,
                        "type": modification_type,
                        "success": True,
                        "original_assignment": original_assignment_count,
                        "original_completion": original_completion_count,
                        "final_assignment": batch.assignment_count,
                        "final_completion": batch.completion_count
                    }
                    
                except Exception as e:
                    await session.rollback()
                    return {
                        "modifier_id": modifier_id,
                        "type": modification_type,
                        "success": False,
                        "error": str(e)
                    }
        
        #   Run concurrent modifications
        modification_tasks = [
            modify_batch_concurrently(1, "assignment"),
            modify_batch_concurrently(2, "assignment"),
            modify_batch_concurrently(3, "completion"),
            modify_batch_concurrently(4, "completion"),
            modify_batch_concurrently(5, "both"),
        ]
        
        import time
        start_time = time.time()
        results = await asyncio.gather(*modification_tasks)
        isolation_time = time.time() - start_time
        
        #   Analyze isolation behavior
        successful_mods = [r for r in results if r["success"]]
        failed_mods = [r for r in results if not r["success"]]
        
        print(f"   ⚡ Concurrent modifications completed in {isolation_time:.4f}s")
        print(f"    Successful modifications: {len(successful_mods)}")
        print(f"   ❌ Failed modifications: {len(failed_mods)}")
        
        # Check final state
        await test_db.refresh(base_batch)
        print(f"   📊 Final batch state - assignment_count: {base_batch.assignment_count}, completion_count: {base_batch.completion_count}")
        
        # All operations should succeed with proper isolation
        assert len(successful_mods) == 5, f"Some modifications failed: {failed_mods}"
        assert isolation_time < 2.0, f"Transaction isolation test too slow: {isolation_time}s"
        
        # Final counts should reflect all successful modifications
        expected_assignment_increment = sum(1 for r in successful_mods if r["type"] in ["assignment", "both"])
        expected_completion_increment = sum(1 for r in successful_mods if r["type"] in ["completion", "both"])
        
        assert base_batch.assignment_count == expected_assignment_increment
        assert base_batch.completion_count == expected_completion_increment
    
    @pytest.mark.asyncio
    async def test_connection_recovery_after_failure(self, test_db: AsyncSession):
        """Test database connection recovery after simulated failures."""
        print("\n🛡️ Testing connection recovery after failures...")
        
        #   Perform successful operations first
        successful_batch = test_factory.batches.create_allocation_batch(
            batch_identifier="RECOVERY_SUCCESS_BATCH",
            total_files=5
        )
        test_db.add(successful_batch)
        await test_db.commit()
        await test_db.refresh(successful_batch)
        
        print(f"    Baseline successful operation: Created batch {successful_batch.id}")
        
        #   Simulate operation that might cause issues
        try:
            # Create batch with potential constraint violation
            problematic_batch = test_factory.batches.create_allocation_batch(
                batch_identifier="RECOVERY_SUCCESS_BATCH",  # Same identifier (might cause issues)
                total_files=3
            )
            test_db.add(problematic_batch)
            await test_db.commit()
            
            print(f"   ⚠️ Potentially problematic operation succeeded: {problematic_batch.id}")
            
        except Exception as e:
            # Expected - constraint violation or other database error
            await test_db.rollback()
            print(f"   ⚠️ Expected constraint violation: {str(e)[:100]}")
        
        #   Verify connection is still functional after error
        recovery_batch = test_factory.batches.create_allocation_batch(
            batch_identifier="RECOVERY_AFTER_ERROR_BATCH",
            total_files=7
        )
        test_db.add(recovery_batch)
        await test_db.commit()
        await test_db.refresh(recovery_batch)
        
        print(f"    Recovery successful: Created batch {recovery_batch.id} after error")
        
        #   Verify data consistency
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        
        final_count = await test_db.execute(
            select(func.count()).select_from(AllocationBatches)
        )
        total_batches = final_count.scalar()
        
        print(f"   📊 Total batches in database after recovery test: {total_batches}")
        assert total_batches >= 2  # At least successful_batch and recovery_batch
        
        # Verify specific batches exist
        verification_stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.in_(["RECOVERY_SUCCESS_BATCH", "RECOVERY_AFTER_ERROR_BATCH"])
        )
        result = await test_db.execute(verification_stmt)
        verified_batches = result.scalars().all()
        
        print(f"    Verified {len(verified_batches)} batches exist after recovery test")
        assert len(verified_batches) >= 2, "Expected batches not found after recovery test"


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.schema           # Feature marker - Schema operations
@pytest.mark.connection       # Feature marker - Connection operations
@pytest.mark.regression       # Suite marker - Migration testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestRealDatabaseMigrations:
    """REGRESSION TEST SUITE: Real database migration and schema operations."""
    
    @pytest.mark.asyncio
    async def test_dynamic_column_addition_simulation(self, test_db: AsyncSession):
        """Test schema flexibility for dynamic column scenarios."""
        print("\n📋 Testing dynamic schema flexibility...")
        
        #   Create batch with base columns
        flexible_batch = test_factory.batches.create_allocation_batch(
            batch_identifier="DYNAMIC_SCHEMA_TEST",
            total_files=10,
            annotation_count=3  # This should support 3 annotators
        )
        test_db.add(flexible_batch)
        await test_db.commit()
        await test_db.refresh(flexible_batch)
        
        print(f"   📊 Created batch with annotation_count: {flexible_batch.annotation_count}")
        
        #   Test that the batch can handle multiple assignments
        # (In production, dynamic columns annotator_1, annotator_2, etc. would be added)
        flexible_batch.assignment_count = 3  # All annotators assigned
        await test_db.commit()
        
        #   Verify batch state
        from app.post_db.allocation_models.allocation_batches import AllocationBatches
        stmt = select(AllocationBatches).where(AllocationBatches.id == flexible_batch.id)
        result = await test_db.execute(stmt)
        updated_batch = result.scalar_one()
        
        assert updated_batch.assignment_count == 3
        assert updated_batch.annotation_count == 3
        print(f"    Batch supports {updated_batch.assignment_count}/{updated_batch.annotation_count} annotators")
        
        #   Simulate different annotation strategies
        strategies_tested = []
        for strategy_type in ["single", "dual", "triple"]:
            test_batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"STRATEGY_{strategy_type.upper()}_TEST",
                total_files=5,
                annotation_count={"single": 1, "dual": 2, "triple": 3}[strategy_type]
            )
            test_db.add(test_batch)
            strategies_tested.append(test_batch)
        
        await test_db.commit()
        for batch in strategies_tested:
            await test_db.refresh(batch)
        
        print(f"   📊 Successfully tested {len(strategies_tested)} annotation strategies")
        
        # Verify all strategies work
        for batch in strategies_tested:
            assert batch.id is not None
            assert batch.annotation_count in [1, 2, 3]
            print(f"      Strategy {batch.batch_identifier}: {batch.annotation_count} annotators")
