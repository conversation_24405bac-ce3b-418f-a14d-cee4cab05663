// hooks/useDatasetCreation.tsx
import { useState } from 'react';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "../../../../lib/api";

export const useDatasetCreation = () => {
  const [manualFolderPath, setManualFolderPath] = useState<string>("");
  const [verificationImageFolderPath, setVerificationImageFolderPath] = useState<string>("");
  const [verificationLabelFilePath, setVerificationLabelFilePath] = useState<string>("");
  const [filesPerBatch, setFilesPerBatch] = useState<number | null>(null);
  const [creatingAnnotationBatch, setCreatingAnnotationBatch] = useState<boolean>(false);
  const [creatingVerificationBatch, setCreatingVerificationBatch] = useState<boolean>(false);

  // Handle setting manual folder
  const handleSetManualFolder = async (projectCode: string, clientId?: number, onRefreshDatasets?: () => Promise<void>) => {
    if (!manualFolderPath) {
      showToast.warning("Please select a folder first.");
      return { success: false };
    }

    if (!projectCode) {
      showToast.warning("Please select a project first.");
      return { success: false };
    }

        setCreatingAnnotationBatch(true);
    try {
      const requestBody: any = {
        folder_path: manualFolderPath,
        project_code: projectCode
      };

      // Add files_per_batch if specified
      if (filesPerBatch && filesPerBatch > 0) {
        requestBody.files_per_batch = filesPerBatch;
      }

      // Add client_id if provided
      if (clientId) {
        requestBody.client_id = clientId;
      }

      const res = await authFetch(`${API_BASE_URL}/admin/select-annotation-folder`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });
      const data = await res.json();

      if (!res.ok) {
        showToast.error(
          data.error || data.detail || data.message || "Failed to set manual folder"
        );
        return { success: false };
      } else {
        showToast.success(data.message || "Manual folder set successfully");

        // Refresh datasets after successful batch creation
        if (onRefreshDatasets) {
          await onRefreshDatasets();
        }

        // Get project_code from the response for next steps
        // The response structure is: { success, message, data: { folder_path, project_code, ... } }
        const project_code = data.data?.project_code;

        // Clear the folder path and batch count after successful creation
        setManualFolderPath("");
        setFilesPerBatch(null);

        return {
          success: true,
          project_code: project_code,
          data: data.data // Include the full response data with total_files and total_batches
        };
      }
    } catch (err) {
      console.error(err);
      showToast.error("Failed to set manual folder");
      return { success: false };
    } finally {
      setCreatingAnnotationBatch(false);
    }
  };

  // Handle setting verification folders
  const handleSetVerificationFolders = async (onRefreshDatasets?: () => Promise<void>) => {
    if (!verificationImageFolderPath || !verificationLabelFilePath) {
      showToast.warning("Please select both image folder and label file.");
      return { success: false };
    }
    setCreatingVerificationBatch(true);
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/select-verification-folders`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            image_folder: verificationImageFolderPath,
            label_file: verificationLabelFilePath,
          }),
        }
      );
      const data = await res.json();
      if (!res.ok) {
        showToast.error(
          data.error || data.detail || "Failed to set verification folders"
        );
        return { success: false };
      } else {
        showToast.success(
          data.message || "Verification folders set successfully"
        );
        
        // Refresh datasets after successful batch creation
        if (onRefreshDatasets) {
          await onRefreshDatasets();
        }
        
        // Clear the folder paths after successful creation
        setVerificationImageFolderPath("");
        setVerificationLabelFilePath("");
        return { success: true };
      }
    } catch (err) {
      console.error(err);
      showToast.error("Failed to set verification folders");
      return { success: false };
    } finally {
      setCreatingVerificationBatch(false);
    }
  };

  // Handle selecting the current path from NAS browser
  const handleSelectPath = (
    currentSelectionTarget: string | null,
    currentBrowsePath: string,
    currentSelection: string
  ) => {
    if (currentSelectionTarget === 'manual-folder') {
      setManualFolderPath(currentBrowsePath);
    } else if (currentSelectionTarget === 'verification-image-folder') {
      setVerificationImageFolderPath(currentBrowsePath);
    } else if (currentSelectionTarget === 'verification-label-file') {
      setVerificationLabelFilePath(currentSelection);
    }
  };

  return {
    manualFolderPath,
    setManualFolderPath,
    verificationImageFolderPath,
    setVerificationImageFolderPath,
    verificationLabelFilePath,
    setVerificationLabelFilePath,
    filesPerBatch,
    setFilesPerBatch,
    creatingAnnotationBatch,
    creatingVerificationBatch,
    handleSetManualFolder,
    handleSetVerificationFolders,
    handleSelectPath,
  };
};