// Export all AI processing components
export { default as AIProcessing } from './AIProcessing';
export { default as OCR } from './ocr/ocr.tsx';
export { default as VQA } from './vqa/vqa.tsx';
export { default as Transcription } from './transcription/transcription.tsx';
export { useProjectService, useProjectServiceWithFilter, projectService } from './projectService';
export { useFileAllocationService, fileAllocationService } from './fileAllocationService';
export type { FileAllocationResult } from './fileAllocationService';

// Project interface for project-based processing
export interface Project {
  id: number;
  project_code: string;
  project_name: string;
  project_type: string;
  folder_path: string;
  resolved_path: string;
  client_id: number;
  ai_processing: boolean;
  has_storage_credentials: boolean;
}

export interface CaptionResponse {
  metadata?: {
    folder_path?: string;
    total_images?: number;
    successful?: number;
    failed?: number;
    success_rate?: number;
    timestamp?: string;
  };
  results?: Array<{
    file_name?: string;
    file_path?: string;
    caption?: string;
    status?: string;
    error?: string;
  }>;
  project_code?: string;
  project_name?: string;
  folder_path?: string;
}

export interface OCRResponse {
  metadata?: {
    folder_path?: string;
    total_images?: number;
    successful?: number;
    failed?: number;
    success_rate?: number;
    timestamp?: string;
  };
  results?: Array<{
    file_name?: string;
    file_path?: string;
    extracted_text?: string;
    status?: string;
    error?: string;
  }>;
  // Project information when processing via project
  project_code?: string;
  project_name?: string;
  folder_path?: string;
}

export interface VQAResponse {
  metadata?: {
    folder_path?: string;
    total_images?: number;
    successful?: number;
    failed?: number;
    success_rate?: number;
    timestamp?: string;
  };
  results?: Array<{
    file_name?: string;
    file_path?: string;
    question?: string;
    answer?: string;
    status?: string;
    error?: string;
  }>;
  // Project information when processing via project
  project_code?: string;
  project_name?: string;
  folder_path?: string;
}

export interface TranscriptionResponse {
  metadata?: {
    folder_path?: string;
    total_audio_files?: number;
    successful?: number;
    failed?: number;
    success_rate?: number;
    timestamp?: string;
  };
  results?: Array<{
    file_name?: string;
    file_path?: string;
    transcription?: string;
    status?: string;
    error?: string;
  }>;
  // Project information when processing via project
  project_code?: string;
  project_name?: string;
  folder_path?: string;
}




