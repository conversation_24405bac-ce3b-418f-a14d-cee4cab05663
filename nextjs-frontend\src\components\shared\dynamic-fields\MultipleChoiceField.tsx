import React from 'react';
import { FormFieldConfig } from './DynamicField';

interface MultipleChoiceFieldProps {
  config: FormFieldConfig;
  value: string;
  onChange: (fieldName: string, value: any) => void;
  error?: string;
}

export default function MultipleChoiceField({ 
  config, 
  value, 
  onChange, 
  error 
}: MultipleChoiceFieldProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(config.field_name, e.target.value);
  };

  if (!config.options || config.options.length === 0) {
    return (
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {config.label}
          {config.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <div className="text-sm text-gray-500">No options configured for this field</div>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {config.label}
        {config.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {config.description && (
        <p className="text-sm text-gray-500 mb-2">{config.description}</p>
      )}
      <div className="space-y-2">
        {config.options.map((option, index) => (
          <label key={index} className="flex items-center">
            <input
              type="radio"
              id={`${config.field_name}_${index}`}
              name={config.field_name}
              value={option}
              checked={value === option}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              required={config.required}
            />
            <span className="ml-2 text-sm text-gray-700">{option}</span>
          </label>
        ))}
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
}
