import io
from datetime import datetime, timedelta
from fastapi import HTT<PERSON>Ex<PERSON>, status
from typing import Dict, Any, Optional, Union, BinaryIO, Callable, Coroutine, TypeVar, cast
import logging
import httpx
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger('model_utils')

# Generic type for decorator
T = TypeVar('T', bound=Callable[..., Coroutine[Any, Any, Any]])

def async_exception_handler(func: T) -> T:
    """
    Decorator to catch exceptions in async functions, log them and raise HTTPException.
    """
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Internal error in {func.__name__}: {str(e)}"
            )
    return cast(T, wrapper)


def prepare_files(image_data: Union[bytes, BinaryIO], **kwargs) -> Dict[str, Any]:
    """
    Prepare files dictionary for API request with images.
    No local storage - direct bytes streaming.

    Args:
        image_data: Image data (file object or bytes)
        **kwargs: Additional fields to include in files dict

    Returns:
        Dict: Files dictionary for API request
    """
    if isinstance(image_data, bytes):
        # Use direct bytes without intermediate BytesIO storage
        files = {'file': ('image.jpg', image_data)}
    else:
        # Assume image_data has a filename attribute (like UploadFile)
        filename = getattr(image_data, 'filename', 'image.jpg')
        files = {'file': (filename, image_data)}

    # Add additional fields as (None, value) tuples for multipart/form-data
    for key, value in kwargs.items():
        files[key] = (None, value)

    return files


def prepare_json_data(text_content: str = None, **kwargs) -> Dict[str, Any]:
    """
    Prepare JSON data dictionary for API request with text.

    Args:
        text_content: Text content to process (optional)
        **kwargs: Additional fields to include in data dict

    Returns:
        Dict: Data dictionary for API request
    """
    data = {}
    
    if text_content is not None:
        data['text'] = text_content

    # Add additional fields
    for key, value in kwargs.items():
        data[key] = value

    return data


async def make_api_request(
    api_url: str,
    api_headers: Optional[Dict[str, str]],
    endpoint: str,
    operation_name: str,
    files: Optional[Dict[str, Any]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    timeout: float = 30.0
) -> Dict[str, Any]:
    """
    Make async API request with standardized error handling.
    
    Args:
        api_url: Base API URL
        api_headers: Headers to include in request
        endpoint: API endpoint to call
        operation_name: Name of operation for logging
        files: Files dictionary for multipart upload (optional)
        json_data: JSON data for request body (optional)
        timeout: Request timeout in seconds
        
    Returns:
        Dict: Parsed JSON response
    """
    url = f"{api_url}{endpoint}"
    headers = api_headers.copy() if api_headers else {}
    
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            if files is not None:
                response = await client.post(url, headers=headers, files=files)
            elif json_data is not None:
                response = await client.post(url, headers=headers, json=json_data)
            else:
                raise ValueError("Either files or json_data must be provided")
                
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            error_msg = f"{operation_name} API Error {e.response.status_code}: {e.response.text}"
            logger.error(error_msg)
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail=error_msg
            )
        except Exception as e:
            error_msg = f"Error in {operation_name}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_msg
            )


def validate_id(id_str: str, field_name: str = "ID") -> int:
    """
    Validate and convert string ID to integer
    
    Args:
        id_str: String ID to validate
        field_name: Name of field for error messages
        
    Returns:
        int: Validated integer ID
        
    Raises:
        HTTPException: If ID is invalid
    """
    try:
        return int(id_str)
    except ValueError:
        error_msg = f"Invalid {field_name}: {id_str}"
        logger.error(error_msg)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )



def parse_nested_json_response(response_content: str, *field_names: str) -> str:
    """
    Parse response content that might contain nested JSON
    
    Args:
        response_content: Response content (string or dict)
        *field_names: Field names to try extracting (in order of preference)
        
    Returns:
        str: Extracted content as string
    """
    if isinstance(response_content, str):
        try:
            import json
            parsed_content = json.loads(response_content)
            if isinstance(parsed_content, dict):
                # Try each field name in order
                for field_name in field_names:
                    if field_name in parsed_content:
                        return str(parsed_content[field_name]).strip()
                # Fallback to string representation
                return str(parsed_content).strip()
        except json.JSONDecodeError:
            pass  # Keep as plain text
    
    return str(response_content).strip()