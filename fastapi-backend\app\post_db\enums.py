"""
Shared enum utilities for the database models.
"""

from sqlalchemy.types import TypeDecorator, String
from enum import Enum as PyEnum


class CaseInsensitiveEnum(TypeDecorator):
    """
    A custom SQLAlchemy type that stores enum values as lowercase strings
    and handles case-insensitive comparison.
    """
    impl = String
    cache_ok = True

    def __init__(self, enum_class, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.enum_class = enum_class

    def process_bind_param(self, value, dialect):
        if value is None:
            return None
        if isinstance(value, self.enum_class):
            return value.value
        if isinstance(value, str):
            return value.lower()
        raise ValueError(f"Invalid value for enum {self.enum_class}: {value}")

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        if isinstance(value, self.enum_class):
            return value
        if isinstance(value, str):
            try:
                return self.enum_class(value)
            except ValueError:
                # Try case-insensitive lookup
                for enum_value in self.enum_class:
                    if enum_value.value.lower() == value.lower():
                        return enum_value
                raise ValueError(f"Invalid enum value: {value}")
        raise ValueError(f"Invalid type for enum: {type(value)}")
    
    def __repr__(self):
        """Custom representation for Alembic serialization"""
        if hasattr(self, 'enum_class') and self.enum_class:
            return f"CaseInsensitiveEnum({self.enum_class.__name__})"
        return "CaseInsensitiveEnum()"