"""
FastAPI route for project database provisioning.
"""

from fastapi import APIRouter, HTTPException, Depends, status # type: ignore
from fastapi.responses import JSONResponse # type: ignore
from pydantic import BaseModel
from typing import Optional
import logging
from sqlalchemy import select, func
from core.session_manager import get_project_db_session

# Adjust import path based on your project structure
from utils.project_provisioning import provision_project_database
from core.session_manager import get_master_db_context
from post_db.master_models.projects_registry import ProjectsRegistry
# Database session and models are imported above
from post_db.allocation_models.project_metadata import ProjectMetadata
from datetime import datetime
        
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/projects", tags=["project-provisioning"])

class ProvisionResponse(BaseModel):
    success: bool
    database_name: str
    table_count: int = None
    connection_info: dict = None
    pgadmin_info: dict = None
    message: str
    error: str = None

class ProvisionRequest(BaseModel):
    database_name: Optional[str] = None  # Optional, if not provided will use project_code
    strategy_id: Optional[int] = None  # Optional, allocation strategy ID to use for schema generation

@router.post("/{project_id}/provision-database", response_model=ProvisionResponse)
async def provision_project_database_route(project_id: str, request: ProvisionRequest = None):
    """
    Create a new project database with all necessary tables.
    
    Args:
        project_id: The ID of the project from projects_registry
        
    Returns:
        ProvisionResponse: Result of the database provisioning
    """
    try:
        logger.info(f"Starting database provisioning for project_id: {project_id}")
        
        # TODO: Uncomment and modify this section when you're ready to integrate with master DB
        """
        # Step 1: Get project details from master database
        async with get_master_db_session() as session:
            project = await session.get(ProjectsRegistry, project_id)
            if not project:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Project with ID {project_id} not found"
                )
            
            project_code = project.project_code
        """
        
        # Use project_id as project_code if no database name is provided
        project_code = project_id
        
        # Use the database name and strategy ID from the request if provided
        database_name = None
        strategy_id = None
        
        if request:
            if request.database_name:
                database_name = request.database_name
            if request.strategy_id:
                strategy_id = request.strategy_id
        
        # Step 2: Provision the database with optional strategy
        result = await provision_project_database(project_code, database_name, strategy_id)
        
        if result["success"]:
            logger.info(f"Successfully provisioned database for project {project_id}")
            return ProvisionResponse(
                success=True,
                database_name=result["database_name"],
                table_count=result["table_count"],
                connection_info=result.get("connection_info"),
                pgadmin_info=result.get("pgadmin_info"),
                message=result["message"]
            )
        else:
            logger.error(f"Failed to provision database for project {project_id}: {result['error']}")
            raise HTTPException(
                status_code=500,
                detail=result["message"]
            )
    
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    
    except Exception as e:
        logger.error(f"Unexpected error provisioning database for project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error during database provisioning: {str(e)}"
        )

@router.post("/fix-database-permissions/{database_name}", response_model=ProvisionResponse)
async def fix_database_permissions_route(database_name: str):
    """
    Fix permissions for an existing database by granting access to the mansi user.
    Useful for databases created before the permission fix.
    
    Args:
        database_name: Name of the database to fix permissions for
        
    Returns:
        ProvisionResponse: Result of the permission fix
    """
    try:
        logger.info(f"Fixing permissions for database: {database_name}")
        
        from utils.project_provisioning import fix_database_permissions
        result = await fix_database_permissions(database_name)
        
        if result["success"]:
            return ProvisionResponse(
                success=True,
                message=result["message"],
                database_name=database_name,
                connection_info={}
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=result.get("error", "Unknown error during permission fix")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fixing database permissions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fixing database permissions: {str(e)}")

@router.get("/{project_id}/database-status")
async def check_project_database_status(project_id: str):
    """
    Check if a project database exists and get basic info.
    
    Args:
        project_id: The ID of the project
        
    Returns:
        dict: Database status information
    """
    try:
        # For now, using project_id as project_code
        project_code = project_id
        database_name = f"project_{project_code}"
        
        # TODO: Add actual database existence check here
        # This is a placeholder response
        return {
            "project_id": project_id,
            "database_name": database_name,
            "exists": False,  # You can implement actual check later
            "message": "Database status check - implementation pending"
        }
        
    except Exception as e:
        logger.error(f"Error checking database status for project {project_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error checking database status: {str(e)}"
        )

# Alternative simpler route if you prefer
@router.post("/{project_id}/create-db")
async def create_project_db_simple(project_id: str, request: ProvisionRequest = None):
    """
    Simple version - just create the database and return basic response.
    """
    try:
        database_name = None
        strategy_id = None
        
        if request:
            if request.database_name:
                database_name = request.database_name
            if request.strategy_id:
                strategy_id = request.strategy_id
            
        result = await provision_project_database(project_id, database_name, strategy_id)
        return JSONResponse(
            status_code=200 if result["success"] else 500,
            content=result
        )
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "message": f"Failed to create database for project {project_id}"
            }
        )

@router.get("/{project_code}/get-database-name")
async def get_project_database_name(project_code: str):
    """
    Get the database name for a project from the project registry.
    
    Args:
        project_code: The project code to look up
        
    Returns:
        dict: Database name information
    """
    try:
        async with get_master_db_context() as session:
            result = await session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project = result.scalar_one_or_none()
            
            if not project:
                return {
                    "success": False,
                    "error": f"Project with code {project_code} not found"
                }
            
            # Use the actual database_name from the projects_registry table
            database_name = project.database_name
            
            return {
                "success": True,
                "project_code": project_code,
                "database_name": database_name,
                "project_name": project.project_name
            }
            
    except Exception as e:
        logger.error(f"Error getting database name for project {project_code}: {str(e)}")
        return {
            "success": False,
            "error": f"Error retrieving database name: {str(e)}"
        }

@router.get("/allocation-strategies")
async def get_allocation_strategies():
    """
    Get all available allocation strategies.
    
    Returns:
        List[dict]: List of allocation strategies
    """
    try:
        from post_db.master_models.allocation_strategies import AllocationStrategies
        
        async with get_master_db_context() as session:
            result = await session.execute(
                select(AllocationStrategies).where(AllocationStrategies.allocation_status != 'deprecated')
            )
            strategies = result.scalars().all()
            
            return {
                "success": True,
                "strategies": [
                    {
                        "id": strategy.id,
                        "name": strategy.strategy_name,
                        "type": strategy.strategy_type,
                        "description": strategy.description,
                        "num_annotators": strategy.num_annotators,
                        "requires_verification": strategy.requires_verification,
                        "requires_ai_preprocessing": strategy.requires_ai_preprocessing,
                        "requires_audit": strategy.requires_audit,
                    }
                    for strategy in strategies
                ]
            }
            
    except Exception as e:
        logger.error(f"Error getting allocation strategies: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving allocation strategies: {str(e)}"
        )

@router.get("/pgadmin/servers-config")
async def get_pgadmin_config():
    """
    Generate pgAdmin servers.json configuration for all project databases.
    This can be imported into pgAdmin for bulk server setup.
    """
    try:
        # TODO: Get list of all project databases from your system
        # For now, returning example structure
        from utils.project_provisioning import ProjectDatabaseProvisioner
        
        provisioner = ProjectDatabaseProvisioner()
        
        # Example project databases - replace with actual query to get all project DBs
        project_databases = ["project_test1", "project_test2"]  # Replace this
        
        config = await provisioner.get_pgadmin_servers_config(project_databases)
        
        return {
            "servers_config": config,
            "import_instructions": [
                "1. Download this JSON configuration",
                "2. Open pgAdmin",
                "3. File → Preferences → Paths → Binary paths → Set paths if needed",
                "4. Tools → Import/Export Servers",
                "5. Import the downloaded JSON file",
                "6. Enter passwords when prompted"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error generating pgAdmin config: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error generating pgAdmin configuration: {str(e)}"
        )

@router.post("/{project_code}/sync-metadata")
async def sync_project_metadata(project_code: str):
    """
    Sync project registry data to the project_metadata table in the project database.
    This route fetches the complete project registry record and allocation strategy details,
    then mirrors that data into the project_metadata table of the project's database.
    
    Args:
        project_code: The project code to sync metadata for
        
    Returns:
        dict: Result of the metadata sync operation
    """
    try:
        logger.info(f"Starting metadata sync for project_code: {project_code}")
        
        # Step 1: Fetch project registry record from master database
        async with get_master_db_context() as master_session:
            logger.info(f"Looking for project_code: '{project_code}' in master database")
            
            # First, let's see all projects to understand what's in the database
            all_projects_result = await master_session.execute(
                select(ProjectsRegistry.project_code, ProjectsRegistry.project_status, ProjectsRegistry.id).limit(20)
            )
            all_projects = all_projects_result.fetchall()
            logger.info(f"Found {len(all_projects)} projects in registry:")
            for proj in all_projects:
                logger.info(f"  - Project: {proj.project_code} (status: {proj.project_status}, id: {proj.id})")
            
            # Get the specific project registry record
            result = await master_session.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
            )
            project = result.scalar_one_or_none()
            
            if not project:
                logger.error(f"Project with code '{project_code}' not found in registry")
                # Let's also try a case-insensitive search to see if it's a case issue
                case_insensitive_result = await master_session.execute(
                    select(ProjectsRegistry).where(ProjectsRegistry.project_code.ilike(f"%{project_code}%"))
                )
                similar_projects = case_insensitive_result.fetchall()
                if similar_projects:
                    logger.info(f"Found similar projects (case-insensitive): {[p.project_code for p in similar_projects]}")
                
                raise HTTPException(
                    status_code=404,
                    detail=f"Project with code {project_code} not found in registry"
                )
            
            logger.info(f"Found project: {project.project_code} (status: {project.project_status}, id: {project.id})")
            
            # Get the allocation strategy details if assigned
            allocation_strategy_data = None
            if project.allocation_strategy_id:
                from post_db.master_models.allocation_strategies import AllocationStrategies
                strategy_result = await master_session.execute(
                    select(AllocationStrategies).where(AllocationStrategies.id == project.allocation_strategy_id)
                )
                strategy = strategy_result.scalar_one_or_none()
                
                if strategy:
                    # Convert strategy object to dictionary for JSONB storage
                    allocation_strategy_data = {
                        "id": strategy.id,
                        "strategy_name": strategy.strategy_name,
                        "strategy_type": strategy.strategy_type,
                        "description": strategy.description,
                        "allocation_status": strategy.allocation_status,
                        "num_annotators": strategy.num_annotators,
                        "requires_verification": strategy.requires_verification,
                        "requires_ai_preprocessing": strategy.requires_ai_preprocessing,
                        "requires_audit": strategy.requires_audit,
                        "quality_requirements": strategy.quality_requirements,
                        "configuration": strategy.configuration,
                        "created_at": strategy.created_at.isoformat() if strategy.created_at else None,
                        "updated_at": strategy.updated_at.isoformat() if strategy.updated_at else None
                    }
        
        # Step 2: Connect to the project database and insert/update metadata
        
        try:
            async with get_project_db_session(project_code) as project_db_session:
                # Check if metadata already exists
                existing_result = await project_db_session.execute(
                    select(ProjectMetadata).where(ProjectMetadata.project_code == project_code)
                )
                existing_metadata = existing_result.scalar_one_or_none()
                
                # Prepare metadata dictionary
                current_time = datetime.utcnow()
                
                # For CSV projects, get the correct batch size from Redis configuration
                batch_size = project.batch_size
                if project.project_type == 'csv':
                    try:
                        from cache.redis_connector import cache_get
                        redis_key = f"csv_config:{project_code}"
                        csv_config = await cache_get(redis_key, json_decode=True)
                        if csv_config and 'records_per_batch' in csv_config:
                            batch_size = csv_config['records_per_batch']
                            logger.info(f"Using CSV batch size from Redis for project {project_code}: {batch_size}")
                        else:
                            logger.warning(f"No CSV configuration found in Redis for project {project_code}, using project.batch_size: {batch_size}")
                    except Exception as e:
                        logger.error(f"Error retrieving CSV configuration from Redis: {str(e)}, using project.batch_size: {batch_size}")
                
                metadata_dict = {
                    "project_code": project.project_code,
                    "master_db_project_id": project.id,
                    "annotation_requirements": project.annotation_requirements,
                    "validation_rules": None,  # This might need to be derived from project data
                    "allocation_strategy": allocation_strategy_data,  # Store complete strategy as JSONB
                    "credentials": project.credentials,
                    "connection_type": project.connection_type,
                    "folder_path": project.folder_path,
                    "instructions": project.instructions,
                    "batch_size": batch_size,  # Use corrected batch size for CSV projects
                    "supported_file_types": None,  # This might need to be derived from project_type
                    "file_processing_pipeline": None,  # This might need to be configured separately
                    "quality_requirements": None,  # This might need to be derived from strategy
                    "is_active": project.project_status == 'active',
                    "last_sync_with_master": current_time
                }
                
                if existing_metadata:
                    # Update existing record
                    for key, value in metadata_dict.items():
                        if key != "created_at":  # Don't update created_at
                            setattr(existing_metadata, key, value)
                    existing_metadata.updated_at = current_time
                    logger.info(f"Updated existing metadata for project {project_code}")
                else:
                    # Create new record
                    new_metadata = ProjectMetadata(**metadata_dict)
                    project_db_session.add(new_metadata)
                    logger.info(f"Created new metadata record for project {project_code}")
                
                await project_db_session.commit()
                
                return {
                    "success": True,
                    "project_code": project_code,
                    "database_name": project.database_name,
                    "message": f"Successfully synced metadata for project {project_code}",
                    "synced_data": {
                        "project_registry_id": project.id,
                        "allocation_strategy_synced": allocation_strategy_data is not None,
                        "allocation_strategy_id": project.allocation_strategy_id,
                        "has_storage_credentials": project.credentials is not None,
                        "has_folder_path": project.folder_path is not None,
                        "has_instructions": project.instructions is not None,
                        "batch_size": project.batch_size
                    }
                }
        
        except Exception as e:
            logger.error(f"Error in database transaction: {str(e)}")
            raise e
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error syncing metadata for project {project_code}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error syncing project metadata: {str(e)}"
        )