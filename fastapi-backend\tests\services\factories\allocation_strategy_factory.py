"""
Allocation strategy factory for creating consistent test allocation strategies.
"""

from typing import Dict, Any
try:
    from app.post_db.master_models.allocation_strategies import StrategyType
except ImportError:
    # Mock StrategyType for testing
    class StrategyType:
        SINGLE_ANNOTATOR_NO_VERIFICATION = "single_annotator_no_verification"
        DUAL_ANNOTATOR_VERIFICATION = "dual_annotator_verification"
        THREE_ANNOTATOR_VERIFICATION = "three_annotator_verification"


class AllocationStrategyFactory:
    """Factory for creating test allocation strategy data."""
    
    @staticmethod
    def create_single_annotator_strategy() -> Dict[str, Any]:
        """Create single annotator, no verification strategy."""
        return {
            'strategy_type': 'single_annotator_no_verification',
            'strategy_enum': StrategyType.SINGLE_ANNOTATOR_NO_VERIFICATION,
            'annotators_per_batch': 1,
            'verifiers_per_batch': 0,
            'verification_required': False,
            'cross_verification': False,
            'completion_threshold': 1,
            'verification_threshold': 0,
            'quality_threshold': 0.7,
            'description': 'Single annotator without verification'
        }
    
    @staticmethod
    def create_dual_annotator_strategy() -> Dict[str, Any]:
        """Create dual annotator with verification strategy."""
        return {
            'strategy_type': 'dual_annotator_verification',
            'strategy_enum': StrategyType.DUAL_ANNOTATOR_VERIFICATION,
            'annotators_per_batch': 2,
            'verifiers_per_batch': 1,
            'verification_required': True,
            'cross_verification': True,
            'completion_threshold': 2,
            'verification_threshold': 1,
            'quality_threshold': 0.8,
            'description': 'Dual annotator with verification'
        }
    
    @staticmethod
    def create_three_annotator_strategy() -> Dict[str, Any]:
        """Create three annotator with verification strategy."""
        return {
            'strategy_type': 'three_annotator_verification',
            'strategy_enum': StrategyType.THREE_ANNOTATOR_VERIFICATION,
            'annotators_per_batch': 3,
            'verifiers_per_batch': 1,
            'verification_required': True,
            'cross_verification': True,
            'completion_threshold': 3,
            'verification_threshold': 1,
            'quality_threshold': 0.85,
            'description': 'Three annotator with verification'
        }
    
    @staticmethod
    def create_custom_strategy(
        annotators_per_batch: int,
        verifiers_per_batch: int,
        verification_required: bool = True
    ) -> Dict[str, Any]:
        """Create custom allocation strategy."""
        return {
            'strategy_type': f'custom_{annotators_per_batch}_annotator',
            'annotators_per_batch': annotators_per_batch,
            'verifiers_per_batch': verifiers_per_batch,
            'verification_required': verification_required,
            'cross_verification': verification_required,
            'completion_threshold': annotators_per_batch,
            'verification_threshold': verifiers_per_batch,
            'quality_threshold': 0.8,
            'description': f'Custom strategy: {annotators_per_batch} annotators, {verifiers_per_batch} verifiers'
        }
    
    @staticmethod
    def get_all_strategies() -> Dict[str, Dict[str, Any]]:
        """Get all available strategies."""
        return {
            'single_annotator': AllocationStrategyFactory.create_single_annotator_strategy(),
            'dual_annotator': AllocationStrategyFactory.create_dual_annotator_strategy(),
            'three_annotator': AllocationStrategyFactory.create_three_annotator_strategy()
        }
