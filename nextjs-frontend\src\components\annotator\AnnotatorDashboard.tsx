"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { FaPencilAlt, FaProjectDiagram, FaCode } from "react-icons/fa";

import { API_BASE_URL } from "@/lib/api";

const API_BASE = API_BASE_URL;
 
interface DashboardData {
  annotation_mode: string;
  username: string;
  full_name: string;
  project_code: string | null;
  project_name: string | null;
  instructions: string;
}

interface AnnotatorDashboardProps {
  username?: string;
  fullName?: string;
  annotationMode?: "annotation" | "verification" | "supervision";
  adminInstructions?: string;
  projectCode?: string | null;
  onActionClick?: (action: "annotation" | "verification" | "supervision") => void;
}
 
export default function AnnotatorDashboard({
  username = "User",
  fullName = "",
  annotationMode = "annotation",
  adminInstructions = "",
  projectCode = null,
  onActionClick,
}: AnnotatorDashboardProps) {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [batchStatus, setBatchStatus] = useState<{
    hasActiveBatch: boolean;
    canStartAnnotating: boolean;
    currentBatch?: any;
  } | null>(null);
 
  
  const [typedText, setTypedText] = useState("");
  const [phraseIdx, setPhraseIdx] = useState(0);
  const [activeTab, setActiveTab] = useState<"task" | "keyboard">("task");

  // Set initial dashboard data from props and fetch batch status
  useEffect(() => {
    console.log('AnnotatorDashboard - projectCode received:', projectCode);
    setDashboardData({
      annotation_mode: annotationMode,
      username: username,
      full_name: fullName,
      project_code: projectCode,
      project_name: null,
      instructions: adminInstructions
    });
    
    // Fetch batch status
    fetchBatchStatus();
    
    setLoading(false);
  }, [annotationMode, username, fullName, adminInstructions, projectCode]);

  const fetchBatchStatus = async () => {
    try {
      // Try the new assignment system first
      const response = await fetch(`${API_BASE}/annotator/batch-status`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('New batch status:', result);
        setBatchStatus({
          hasActiveBatch: result.has_active_batch,
          canStartAnnotating: result.can_start_annotating,
          currentBatch: result.current_batch
        });
        return;
      } else if (response.status === 404) {
        console.log('No batch status available from new system');
      } else {
        console.error('Failed to fetch batch status from new system:', response.status);
      }

      // Fallback to old system
      console.log('Falling back to old batch status endpoint');
      const fallbackResponse = await fetch(`${API_BASE}/annotator/batch-status`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (fallbackResponse.ok) {
        const result = await fallbackResponse.json();
        console.log('Fallback batch status:', result);
        setBatchStatus({
          hasActiveBatch: result.has_active_batch,
          canStartAnnotating: result.can_start_annotating,
          currentBatch: result.current_batch
        });
      } else {
        console.error('Failed to fetch batch status from fallback:', fallbackResponse.status);
        // Set default values if both fail
        setBatchStatus({
          hasActiveBatch: false,
          canStartAnnotating: true
        });
      }
    } catch (error) {
      console.error('Network error fetching batch status:', error);
      // Set default values if fetch fails
      setBatchStatus({
        hasActiveBatch: false,
        canStartAnnotating: true
      });
    }
  };
 
  
 
  const handleActionClick = async (action: "annotation") => {
    console.log('Start Annotating button clicked!');
    
    try {
      // If user doesn't have an active batch, try to assign one using the new system
      if (!batchStatus?.hasActiveBatch) {
        console.log('No active batch, attempting to assign one...');
        
        const startResponse = await fetch(`${API_BASE}/annotator/start-annotation`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (startResponse.ok) {
          const result = await startResponse.json();
          console.log('Batch assignment result:', result);
          
          if (result.success) {
            // Successfully assigned a batch, update status and proceed
            setBatchStatus({
              hasActiveBatch: true,
              canStartAnnotating: true,
              currentBatch: result.batch
            });
            console.log('Successfully assigned new batch');
          } else {
            console.error('Failed to assign batch:', result.error);
            // Still proceed to annotation view in case there's a fallback
          }
        } else if (startResponse.status === 404) {
          console.log('No batches available for assignment');
          // Still proceed to annotation view to show "no tasks" message
        } else {
          console.error('Failed to start annotation:', startResponse.status);
          // Still proceed to annotation view for fallback handling
        }
      }
    } catch (error) {
      console.error('Error starting annotation:', error);
      // Continue to annotation view for fallback handling
    }

    // Proceed to annotation view
    if (onActionClick) {
      console.log('Using onActionClick callback');
      onActionClick(action);
    } else {
      // Fallback: navigate directly if no callback provided
      console.log('No callback provided, navigating directly');
      router.push("/annotator?view=annotation");
    }
  };
 
  // Use data from API or fallback to props
  const actualUsername = dashboardData?.username || username;
  const actualFullName = dashboardData?.full_name || fullName;
  const displayName = actualFullName || actualUsername;
  const firstInitial = displayName.charAt(0).toUpperCase();
  const currentAnnotationMode = dashboardData?.annotation_mode || annotationMode;
  const projectInstructions = dashboardData?.instructions || adminInstructions;
 
  if (loading) {
    return (
      <div className="px-8 py-8 max-w-screen-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="px-8 py-8 max-w-screen-2xl mx-auto min-h-screen">
 
      {/* Welcome Section */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl py-6 px-8 mb-8 shadow-lg border border-blue-200">
        <div className="flex items-center">
          <div className="flex-shrink-0 mr-6">
            <div className="w-[80px] h-[80px] bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl font-bold text-white">
                {firstInitial}
              </span>
            </div>
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <h2 className="text-2xl font-bold text-gray-800">
                  Welcome back, {displayName}{" "}
                  <span className="text-2xl">👋</span>
                </h2>
                <div className="mt-2 min-h-[48px] min-w-[240px] overflow-hidden">
                  <span className="text-lg text-blue-600 font-medium whitespace-nowrap">
                    {typedText}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <span className="bg-gradient-to-br from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium shadow-md">
                  {currentAnnotationMode.charAt(0).toUpperCase() + currentAnnotationMode.slice(1)} Mode
                </span>
                {dashboardData?.project_code && (
                  <span className="bg-white text-blue-600 px-3 py-1 rounded-full text-xs font-medium border border-blue-200">
                    {dashboardData.project_code}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
 
      {/* Main Content */}
      <div className="flex flex-nowrap items-start gap-8">
        {/* Guidelines and Shortcuts Panel */}
        <div className="w-full md:w-5/12">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                <FaProjectDiagram className="mr-2 text-blue-500" />
                Project Information
              </h3>
            </div>
            <div className="p-0">
              <ul className="flex border-b border-gray-200">
                <li role="presentation" className="flex-1">
                  <button
                    type="button"
                    onClick={() => setActiveTab("task")}
                    className={`w-full px-6 py-3 font-medium transition-all duration-200 ${
                      activeTab === "task"
                        ? "text-blue-600 border-b-3 border-blue-600 bg-blue-50"
                        : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    Task Guidelines
                  </button>
                </li>
                <li role="presentation" className="flex-1">
                  <button
                    type="button"
                    onClick={() => setActiveTab("keyboard")}
                    className={`w-full px-6 py-3 font-medium transition-all duration-200 ${
                      activeTab === "keyboard"
                        ? "text-blue-600 border-b-3 border-blue-600 bg-blue-50"
                        : "text-gray-600 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    Keyboard Shortcuts
                  </button>
                </li>
              </ul>
              <div className="min-h-[320px]">
                {activeTab === "task" && (
                  <div className="p-6">
                    {projectInstructions ? (
                      <div className="prose max-w-none">
                        <div
                          className="text-gray-700 leading-relaxed"
                          dangerouslySetInnerHTML={{ __html: projectInstructions }}
                        />
                      </div>
                    ) : (
                      <div className="text-gray-700">
                        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                          <div className="flex">
                            <div className="flex-shrink-0">
                              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div className="ml-3">
                              <p className="text-sm text-yellow-700">
                                No specific project instructions available.
                              </p>
                            </div>
                          </div>
                        </div>
                        <h4 className="font-semibold text-lg mb-3">General Guidelines:</h4>
                        <div className="space-y-3">
                          <div className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">1</span>
                            <p>Label all items according to the provided taxonomy and project requirements</p>
                          </div>
                          <div className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">2</span>
                            <p>Maintain consistency in your labeling approach throughout the session</p>
                          </div>
                          <div className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">3</span>
                            <p>If uncertain about a label, flag the item for review rather than guessing</p>
                          </div>
                          <div className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">4</span>
                            <p>Use keyboard shortcuts for faster and more efficient annotation</p>
                          </div>
                          <div className="flex items-start">
                            <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">5</span>
                            <p>Take regular breaks to avoid fatigue and maintain annotation quality</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                {activeTab === "keyboard" && (
                  <div className="p-6">
                    <h4 className="font-semibold text-lg mb-4 text-gray-800">Essential Shortcuts</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <span className="font-medium text-gray-700">Next Image</span>
                        <div className="flex space-x-2">
                          <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">→</span>
                          <span className="text-gray-400">or</span>
                          <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">N</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <span className="font-medium text-gray-700">Previous Image</span>
                        <div className="flex space-x-2">
                          <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">←</span>
                          <span className="text-gray-400">or</span>
                          <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">P</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <span className="font-medium text-gray-700">Save Label</span>
                        <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">Enter</span>
                      </div>
                      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <span className="font-medium text-gray-700">Save All Labels</span>
                        <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">Ctrl+S</span>
                      </div>
                      <div className="flex items-center justify-between py-3 px-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <span className="font-medium text-gray-700">Zoom Controls</span>
                        <span className="bg-white text-gray-800 text-sm px-3 py-1 rounded-md border shadow-sm font-mono">Mouse Wheel</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Action Panel */}
        <div className="w-full md:w-7/12">
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-2">Ready to Start Annotating?</h3>
              <p className="text-gray-600">Begin your annotation work with the tools below</p>
            </div>
            
            <div className="max-w-md mx-auto">
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-8 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div className="text-center">
                  <div className="mb-4">
                    <FaPencilAlt className="text-4xl mx-auto" />
                  </div>
                  <h3 className="text-2xl font-bold mb-3">
                    {batchStatus?.hasActiveBatch ? "Continue Annotation" : "Start Annotation"}
                  </h3>
                  <p className="text-blue-100 mb-6 leading-relaxed">
                    {batchStatus?.hasActiveBatch 
                      ? "Continue working on your current batch of assigned data."
                      : "Begin labeling and annotating your assigned data with our intuitive annotation tools."
                    }
                  </p>
                  <button
                    onClick={() => handleActionClick("annotation")}
                    disabled={!batchStatus?.canStartAnnotating}
                    className={`w-full font-bold py-4 px-6 rounded-lg transition-all duration-200 transform shadow-lg ${
                      batchStatus?.canStartAnnotating
                        ? "bg-white text-blue-600 hover:bg-blue-50 hover:scale-105"
                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                    }`}
                  >
                    {batchStatus?.hasActiveBatch ? "Continue Annotating →" : "Start Annotating →"}
                  </button>
                </div>
              </div>
              
              {/* Project Status */}
              {dashboardData?.project_code ? (
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center text-green-800">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Project Active: {dashboardData.project_code}</span>
                  </div>
                </div>
              ) : (
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center text-yellow-800">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">No active project assigned</span>
                  </div>
                  <p className="text-yellow-700 text-sm mt-1">Please contact your administrator to get assigned to a project.</p>
                </div>
              )}

              {/* Batch Status */}
              {batchStatus?.hasActiveBatch && batchStatus.currentBatch && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center text-blue-800 mb-2">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Current Batch: {batchStatus.currentBatch.batch_identifier}</span>
                  </div>
                  <p className="text-blue-700 text-sm">
                    {batchStatus.currentBatch.total_files} files • Status: {batchStatus.currentBatch.batch_status}
                  </p>
                </div>
              )}

              {/* No batches available */}
              {!batchStatus?.canStartAnnotating && !batchStatus?.hasActiveBatch && (
                <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="flex items-center text-gray-600">
                    <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">No batches available</span>
                  </div>
                  <p className="text-gray-500 text-sm mt-1">All batches are currently assigned or completed. Please check back later.</p>
                </div>
              )}
              
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
 