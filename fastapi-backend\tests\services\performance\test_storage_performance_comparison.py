"""
Performance comparison tests between MinIO and NAS storage across all services.
Benchmarks throughput, latency, memory usage, and scalability for both storage types.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import time
import asyncio
import psutil
import statistics
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor

from app.services.media_streaming_service import MediaStreamingService
from app.services.project_batch_service import ProjectBatchService
from app.services.ai_processing_service import AIProcessingService

class TestStoragePerformanceComparison:
    """Comprehensive performance comparison between storage types."""
    
    @pytest.fixture
    def performance_test_configs(self):
        """Performance test configurations for different scenarios."""
        return {
            'small_files': {
                'file_count': 1000,
                'file_size': 1024 * 1024,  # 1MB each
                'total_size': 1024 * 1024 * 1000,  # ~1GB total
                'description': 'Many small files'
            },
            'medium_files': {
                'file_count': 100,
                'file_size': 10 * 1024 * 1024,  # 10MB each
                'total_size': 10 * 1024 * 1024 * 100,  # ~1GB total
                'description': 'Medium sized files'
            },
            'large_files': {
                'file_count': 10,
                'file_size': 100 * 1024 * 1024,  # 100MB each
                'total_size': 100 * 1024 * 1024 * 10,  # ~1GB total
                'description': 'Large files'
            },
            'very_large_files': {
                'file_count': 2,
                'file_size': 500 * 1024 * 1024,  # 500MB each
                'total_size': 500 * 1024 * 1024 * 2,  # ~1GB total
                'description': 'Very large files'
            }
        }
    
    @pytest.fixture
    def mock_storage_performance_profiles(self):
        """Mock performance profiles for different storage types."""
        return {
            'MinIO': {
                'throughput_mbps': {
                    'upload': 150,
                    'download': 180,
                    'streaming': 200
                },
                'latency_ms': {
                    'first_byte': 25,
                    'avg_response': 50,
                    'connection_setup': 10
                },
                'concurrent_connections': 100,
                'memory_per_connection_kb': 512
            },
            'NAS-FTP': {
                'throughput_mbps': {
                    'upload': 80,
                    'download': 100,
                    'streaming': 90
                },
                'latency_ms': {
                    'first_byte': 150,
                    'avg_response': 200,
                    'connection_setup': 500
                },
                'concurrent_connections': 50,
                'memory_per_connection_kb': 1024
            }
        }

    @pytest.mark.performance
    @pytest.mark.storage
    def test_throughput_comparison_batch_operations(
        self, 
        performance_test_configs, 
        mock_storage_performance_profiles,
        performance_monitor
    ):
        """Compare throughput between storage types for batch operations."""
        service = ProjectBatchService()
        
        throughput_results = {}
        
        for config_name, config in performance_test_configs.items():
            throughput_results[config_name] = {}
            
            for storage_type, profile in mock_storage_performance_profiles.items():
                performance_monitor.start()
                
                # Simulate batch operation with specific performance profile
                with patch.object(service, '_execute_batch_operation') as mock_batch:
                    # Calculate expected time based on throughput and file size
                    total_size_mb = config['total_size'] / (1024 * 1024)
                    upload_throughput = profile['throughput_mbps']['upload']
                    expected_time = total_size_mb / upload_throughput
                    
                    # Simulate operation time
                    time.sleep(min(expected_time / 100, 0.5))  # Scale down for testing
                    
                    mock_batch.return_value = {
                        'success': True,
                        'files_processed': config['file_count'],
                        'total_size_mb': total_size_mb,
                        'actual_throughput_mbps': upload_throughput
                    }
                    
                    result = service._execute_batch_operation(
                        config['file_count'], config['file_size'], storage_type
                    )
                    
                    performance_monitor.stop()
                    execution_time = performance_monitor.get_execution_time()
                    
                    # Calculate effective throughput
                    effective_throughput = total_size_mb / execution_time if execution_time > 0 else 0
                    
                    throughput_results[config_name][storage_type] = {
                        'execution_time': execution_time,
                        'effective_throughput_mbps': effective_throughput,
                        'expected_throughput_mbps': upload_throughput,
                        'files_processed': config['file_count'],
                        'total_size_mb': total_size_mb
                    }
        
        # Analyze and compare results
        self._analyze_throughput_results(throughput_results, mock_storage_performance_profiles)
    
    def _analyze_throughput_results(self, results, profiles):
        """Analyze throughput comparison results."""
        print("\n📊 THROUGHPUT COMPARISON RESULTS:")
        print("=" * 50)
        
        for config_name, config_results in results.items():
            print(f"\n{config_name.upper().replace('_', ' ')}:")
            
            minio_result = config_results.get('MinIO', {})
            nas_result = config_results.get('NAS-FTP', {})
            
            if minio_result and nas_result:
                minio_throughput = minio_result['effective_throughput_mbps']
                nas_throughput = nas_result['effective_throughput_mbps']
                
                print(f"  MinIO:    {minio_throughput:.1f} MB/s")
                print(f"  NAS-FTP:  {nas_throughput:.1f} MB/s")
                
                if minio_throughput > 0 and nas_throughput > 0:
                    improvement = ((minio_throughput - nas_throughput) / nas_throughput) * 100
                    print(f"  MinIO is {improvement:+.1f}% vs NAS-FTP")
                    
                    # MinIO should generally be faster
                    assert minio_throughput >= nas_throughput * 0.8, f"MinIO performance unexpectedly low for {config_name}"

    @pytest.mark.performance
    @pytest.mark.storage
    @pytest.mark.asyncio
    async def test_latency_comparison_streaming_operations(
        self, 
        mock_storage_performance_profiles,
        performance_monitor
    ):
        """Compare latency between storage types for streaming operations."""
        service = MediaStreamingService()
        
        latency_test_scenarios = [
            {'operation': 'get_streaming_url', 'file_size': '10MB', 'concurrent_users': 1},
            {'operation': 'get_streaming_url', 'file_size': '100MB', 'concurrent_users': 5},
            {'operation': 'stream_media', 'file_size': '500MB', 'concurrent_users': 10}
        ]
        
        latency_results = {}
        
        for scenario in latency_test_scenarios:
            latency_results[f"{scenario['operation']}_{scenario['concurrent_users']}users"] = {}
            
            for storage_type, profile in mock_storage_performance_profiles.items():
                latency_measurements = []
                
                # Run multiple measurements for statistical accuracy
                for _ in range(10):
                    performance_monitor.start()
                    
                    with patch.object(service, '_execute_streaming_operation') as mock_stream:
                        # Simulate latency based on storage profile
                        base_latency = profile['latency_ms']['avg_response'] / 1000.0  # Convert to seconds
                        concurrent_penalty = scenario['concurrent_users'] * 0.1  # Additional latency per concurrent user
                        simulated_latency = base_latency + concurrent_penalty
                        
                        await asyncio.sleep(min(simulated_latency / 10, 0.1))  # Scale down for testing
                        
                        mock_stream.return_value = {
                            'success': True,
                            'latency_ms': profile['latency_ms']['avg_response'],
                            'storage_type': storage_type
                        }
                        
                        result = await service._execute_streaming_operation(
                            scenario, storage_type
                        )
                        
                        performance_monitor.stop()
                        execution_time = performance_monitor.get_execution_time()
                        latency_measurements.append(execution_time * 1000)  # Convert to ms
                
                # Calculate statistics
                avg_latency = statistics.mean(latency_measurements)
                median_latency = statistics.median(latency_measurements)
                p95_latency = statistics.quantiles(latency_measurements, n=20)[18] if len(latency_measurements) >= 20 else max(latency_measurements)
                
                latency_results[f"{scenario['operation']}_{scenario['concurrent_users']}users"][storage_type] = {
                    'avg_latency_ms': avg_latency,
                    'median_latency_ms': median_latency,
                    'p95_latency_ms': p95_latency,
                    'measurements': latency_measurements
                }
        
        # Analyze latency results
        self._analyze_latency_results(latency_results, mock_storage_performance_profiles)
    
    def _analyze_latency_results(self, results, profiles):
        """Analyze latency comparison results."""
        print("\n⚡ LATENCY COMPARISON RESULTS:")
        print("=" * 40)
        
        for scenario_name, scenario_results in results.items():
            print(f"\n{scenario_name.upper().replace('_', ' ')}:")
            
            minio_result = scenario_results.get('MinIO', {})
            nas_result = scenario_results.get('NAS-FTP', {})
            
            if minio_result and nas_result:
                print(f"  MinIO Average:    {minio_result['avg_latency_ms']:.1f}ms")
                print(f"  NAS-FTP Average:  {nas_result['avg_latency_ms']:.1f}ms")
                print(f"  MinIO P95:        {minio_result['p95_latency_ms']:.1f}ms")
                print(f"  NAS-FTP P95:      {nas_result['p95_latency_ms']:.1f}ms")
                
                # MinIO should have lower latency
                assert minio_result['avg_latency_ms'] <= nas_result['avg_latency_ms'] * 1.2, f"MinIO latency too high for {scenario_name}"

    @pytest.mark.performance
    @pytest.mark.storage
    @pytest.mark.memory_intensive
    def test_memory_usage_comparison(
        self, 
        performance_test_configs,
        mock_storage_performance_profiles
    ):
        """Compare memory usage between storage types under load."""
        process = psutil.Process()
        memory_results = {}
        
        for storage_type, profile in mock_storage_performance_profiles.items():
            memory_results[storage_type] = {}
            
            for config_name, config in performance_test_configs.items():
                initial_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # Simulate concurrent connections based on storage profile
                max_connections = profile['concurrent_connections']
                memory_per_conn = profile['memory_per_connection_kb'] / 1024  # MB
                
                # Simulate memory usage for concurrent file operations
                simulated_connections = min(config['file_count'], max_connections)
                
                # Mock memory-intensive operations
                memory_intensive_data = []
                for i in range(simulated_connections):
                    # Simulate connection memory usage
                    connection_data = {
                        'connection_id': i,
                        'buffer': b'x' * int(memory_per_conn * 1024 * 1024),  # Convert back to bytes
                        'file_handle': f'file_{i}',
                        'metadata': {'size': config['file_size'], 'type': 'test'}
                    }
                    memory_intensive_data.append(connection_data)
                
                peak_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_used = peak_memory - initial_memory
                
                # Cleanup simulated data
                del memory_intensive_data
                
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_freed = peak_memory - final_memory
                
                memory_results[storage_type][config_name] = {
                    'initial_memory_mb': initial_memory,
                    'peak_memory_mb': peak_memory,
                    'memory_used_mb': memory_used,
                    'memory_freed_mb': memory_freed,
                    'memory_efficiency': memory_freed / memory_used if memory_used > 0 else 0,
                    'concurrent_connections': simulated_connections
                }
        
        # Analyze memory usage results
        self._analyze_memory_results(memory_results, mock_storage_performance_profiles)
    
    def _analyze_memory_results(self, results, profiles):
        """Analyze memory usage comparison results."""
        print("\n💾 MEMORY USAGE COMPARISON:")
        print("=" * 35)
        
        for storage_type, storage_results in results.items():
            print(f"\n{storage_type}:")
            total_memory_used = sum(r['memory_used_mb'] for r in storage_results.values())
            avg_efficiency = statistics.mean([r['memory_efficiency'] for r in storage_results.values()])
            
            print(f"  Total Memory Used: {total_memory_used:.1f} MB")
            print(f"  Average Efficiency: {avg_efficiency:.2f}")
            print(f"  Max Connections: {profiles[storage_type]['concurrent_connections']}")
            
            for config_name, config_result in storage_results.items():
                print(f"    {config_name}: {config_result['memory_used_mb']:.1f} MB")
        
        # Memory usage should be reasonable for both storage types
        for storage_type, storage_results in results.items():
            for config_name, config_result in storage_results.items():
                assert config_result['memory_used_mb'] < 500, f"{storage_type} {config_name} uses too much memory"
                assert config_result['memory_efficiency'] > 0.7, f"{storage_type} {config_name} poor memory efficiency"

    @pytest.mark.performance
    @pytest.mark.storage
    @pytest.mark.concurrent
    @pytest.mark.asyncio
    async def test_concurrent_scalability_comparison(
        self,
        mock_storage_performance_profiles,
        performance_monitor
    ):
        """Compare scalability under concurrent load between storage types."""
        service = ProjectBatchService()
        
        concurrency_levels = [1, 5, 10, 25, 50, 100]
        scalability_results = {}
        
        for storage_type, profile in mock_storage_performance_profiles.items():
            scalability_results[storage_type] = {}
            max_connections = profile['concurrent_connections']
            
            for concurrency in concurrency_levels:
                if concurrency > max_connections:
                    # Skip if exceeding storage capacity
                    scalability_results[storage_type][concurrency] = {
                        'skipped': True,
                        'reason': 'Exceeds max connections'
                    }
                    continue
                
                performance_monitor.start()
                
                # Create concurrent tasks
                async def concurrent_operation(task_id):
                    # Simulate degradation with increased concurrency
                    base_time = 0.1
                    degradation_factor = 1 + (concurrency / max_connections) * 0.5
                    processing_time = base_time * degradation_factor
                    
                    await asyncio.sleep(processing_time)
                    
                    return {
                        'task_id': task_id,
                        'success': True,
                        'processing_time': processing_time,
                        'storage_type': storage_type
                    }
                
                # Execute concurrent tasks
                tasks = [concurrent_operation(i) for i in range(concurrency)]
                task_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                performance_monitor.stop()
                total_execution_time = performance_monitor.get_execution_time()
                
                # Analyze concurrent execution
                successful_tasks = [r for r in task_results if not isinstance(r, Exception) and r.get('success')]
                failed_tasks = [r for r in task_results if isinstance(r, Exception)]
                
                scalability_results[storage_type][concurrency] = {
                    'total_execution_time': total_execution_time,
                    'successful_tasks': len(successful_tasks),
                    'failed_tasks': len(failed_tasks),
                    'success_rate': len(successful_tasks) / len(task_results),
                    'tasks_per_second': len(successful_tasks) / total_execution_time if total_execution_time > 0 else 0,
                    'avg_task_time': statistics.mean([r['processing_time'] for r in successful_tasks]) if successful_tasks else 0
                }
        
        # Analyze scalability results
        self._analyze_scalability_results(scalability_results, mock_storage_performance_profiles)
    
    def _analyze_scalability_results(self, results, profiles):
        """Analyze scalability comparison results."""
        print("\n📈 SCALABILITY COMPARISON:")
        print("=" * 30)
        
        for storage_type, storage_results in results.items():
            print(f"\n{storage_type} Scalability:")
            print(f"  Max Connections: {profiles[storage_type]['concurrent_connections']}")
            
            for concurrency, result in storage_results.items():
                if result.get('skipped'):
                    print(f"    {concurrency:3d} concurrent: SKIPPED ({result['reason']})")
                    continue
                
                success_rate = result['success_rate'] * 100
                tasks_per_sec = result['tasks_per_second']
                
                print(f"    {concurrency:3d} concurrent: {success_rate:5.1f}% success, {tasks_per_sec:5.1f} tasks/sec")
                
                # Ensure reasonable performance degradation
                if concurrency <= 10:  # Low concurrency should have high success rate
                    assert success_rate >= 95, f"{storage_type} poor performance at {concurrency} concurrent operations"

    @pytest.mark.performance
    @pytest.mark.storage
    @pytest.mark.stress
    def test_stress_test_comparison(
        self,
        mock_storage_performance_profiles,
        performance_monitor
    ):
        """Stress test comparison between storage types."""
        ai_service = AIProcessingService()
        
        stress_scenarios = [
            {'name': 'high_throughput', 'files_per_second': 100, 'duration_seconds': 30},
            {'name': 'large_files', 'file_size_mb': 1000, 'concurrent_files': 10},
            {'name': 'mixed_load', 'small_files': 1000, 'large_files': 10, 'duration_seconds': 60}
        ]
        
        stress_results = {}
        
        for scenario in stress_scenarios:
            stress_results[scenario['name']] = {}
            
            for storage_type, profile in mock_storage_performance_profiles.items():
                performance_monitor.start()
                
                with patch.object(ai_service, '_execute_stress_scenario') as mock_stress:
                    # Simulate stress test results based on storage characteristics
                    base_performance = profile['throughput_mbps']['download']
                    stress_degradation = 0.8  # 20% degradation under stress
                    
                    if scenario['name'] == 'high_throughput':
                        files_processed = int(scenario['files_per_second'] * scenario['duration_seconds'] * stress_degradation)
                        errors_encountered = max(0, scenario['files_per_second'] * scenario['duration_seconds'] - files_processed)
                    elif scenario['name'] == 'large_files':
                        files_processed = int(scenario['concurrent_files'] * stress_degradation)
                        errors_encountered = scenario['concurrent_files'] - files_processed
                    else:  # mixed_load
                        total_files = scenario['small_files'] + scenario['large_files']
                        files_processed = int(total_files * stress_degradation)
                        errors_encountered = total_files - files_processed
                    
                    mock_stress.return_value = {
                        'files_processed': files_processed,
                        'errors_encountered': errors_encountered,
                        'peak_memory_mb': profile['memory_per_connection_kb'] * files_processed / 1024,
                        'avg_response_time_ms': profile['latency_ms']['avg_response'] * (1 + (1 - stress_degradation)),
                        'throughput_degradation': 1 - stress_degradation
                    }
                    
                    result = ai_service._execute_stress_scenario(scenario, storage_type)
                    
                    performance_monitor.stop()
                    execution_time = performance_monitor.get_execution_time()
                    
                    stress_results[scenario['name']][storage_type] = {
                        'execution_time': execution_time,
                        'files_processed': result['files_processed'],
                        'errors_encountered': result['errors_encountered'],
                        'error_rate': result['errors_encountered'] / (result['files_processed'] + result['errors_encountered']) if (result['files_processed'] + result['errors_encountered']) > 0 else 0,
                        'peak_memory_mb': result['peak_memory_mb'],
                        'avg_response_time_ms': result['avg_response_time_ms'],
                        'throughput_degradation': result['throughput_degradation']
                    }
        
        # Analyze stress test results
        self._analyze_stress_results(stress_results)
    
    def _analyze_stress_results(self, results):
        """Analyze stress test comparison results."""
        print("\n🔥 STRESS TEST COMPARISON:")
        print("=" * 30)
        
        for scenario_name, scenario_results in results.items():
            print(f"\n{scenario_name.upper().replace('_', ' ')} Scenario:")
            
            for storage_type, result in scenario_results.items():
                error_rate = result['error_rate'] * 100
                throughput_loss = result['throughput_degradation'] * 100
                
                print(f"  {storage_type}:")
                print(f"    Files Processed: {result['files_processed']}")
                print(f"    Error Rate: {error_rate:.1f}%")
                print(f"    Throughput Loss: {throughput_loss:.1f}%")
                print(f"    Peak Memory: {result['peak_memory_mb']:.1f}MB")
                print(f"    Avg Response: {result['avg_response_time_ms']:.1f}ms")
                
                # Stress test acceptance criteria
                assert error_rate < 10, f"{storage_type} {scenario_name} error rate too high: {error_rate:.1f}%"
                assert throughput_loss < 30, f"{storage_type} {scenario_name} throughput loss too high: {throughput_loss:.1f}%"

    @pytest.mark.performance
    @pytest.mark.storage
    def test_cost_performance_analysis(
        self,
        performance_test_configs,
        mock_storage_performance_profiles
    ):
        """Analyze cost vs performance trade-offs between storage types."""
        # Mock cost structures
        cost_models = {
            'MinIO': {
                'storage_cost_per_gb_month': 0.023,
                'transfer_cost_per_gb': 0.09,
                'operation_cost_per_1000_requests': 0.0004,
                'setup_cost': 100,  # One-time setup cost
            },
            'NAS-FTP': {
                'storage_cost_per_gb_month': 0.015,
                'transfer_cost_per_gb': 0.12,
                'operation_cost_per_1000_requests': 0.0006,
                'setup_cost': 200,  # Higher setup cost
            }
        }
        
        cost_performance_analysis = {}
        
        for storage_type in mock_storage_performance_profiles.keys():
            cost_performance_analysis[storage_type] = {}
            
            for config_name, config in performance_test_configs.items():
                # Calculate costs
                data_size_gb = config['total_size'] / (1024 ** 3)
                monthly_storage_cost = data_size_gb * cost_models[storage_type]['storage_cost_per_gb_month']
                transfer_cost = data_size_gb * cost_models[storage_type]['transfer_cost_per_gb']
                operation_cost = (config['file_count'] / 1000) * cost_models[storage_type]['operation_cost_per_1000_requests']
                total_monthly_cost = monthly_storage_cost + transfer_cost + operation_cost
                
                # Get performance metrics
                performance_profile = mock_storage_performance_profiles[storage_type]
                avg_throughput = statistics.mean(performance_profile['throughput_mbps'].values())
                avg_latency = statistics.mean(performance_profile['latency_ms'].values())
                
                # Calculate cost-performance ratios
                cost_per_mbps = total_monthly_cost / avg_throughput if avg_throughput > 0 else float('inf')
                performance_per_dollar = avg_throughput / total_monthly_cost if total_monthly_cost > 0 else 0
                
                cost_performance_analysis[storage_type][config_name] = {
                    'monthly_storage_cost': monthly_storage_cost,
                    'transfer_cost': transfer_cost,
                    'operation_cost': operation_cost,
                    'total_monthly_cost': total_monthly_cost,
                    'avg_throughput_mbps': avg_throughput,
                    'avg_latency_ms': avg_latency,
                    'cost_per_mbps': cost_per_mbps,
                    'performance_per_dollar': performance_per_dollar,
                    'data_size_gb': data_size_gb
                }
        
        # Analyze cost-performance trade-offs
        self._analyze_cost_performance(cost_performance_analysis, cost_models)
    
    def _analyze_cost_performance(self, analysis, cost_models):
        """Analyze cost vs performance trade-offs."""
        print("\n💰 COST-PERFORMANCE ANALYSIS:")
        print("=" * 35)
        
        for storage_type, storage_analysis in analysis.items():
            total_cost = sum(result['total_monthly_cost'] for result in storage_analysis.values())
            avg_performance_per_dollar = statistics.mean([result['performance_per_dollar'] for result in storage_analysis.values()])
            
            print(f"\n{storage_type}:")
            print(f"  Setup Cost: ${cost_models[storage_type]['setup_cost']}")
            print(f"  Monthly Operational Cost: ${total_cost:.2f}")
            print(f"  Performance per Dollar: {avg_performance_per_dollar:.2f} Mbps/$")
            
            for config_name, result in storage_analysis.items():
                print(f"    {config_name}: ${result['total_monthly_cost']:.2f}/month, {result['performance_per_dollar']:.2f} Mbps/$")
        
        # Compare overall cost-effectiveness
        minio_analysis = analysis.get('MinIO', {})
        nas_analysis = analysis.get('NAS-FTP', {})
        
        if minio_analysis and nas_analysis:
            minio_avg_perf_per_dollar = statistics.mean([r['performance_per_dollar'] for r in minio_analysis.values()])
            nas_avg_perf_per_dollar = statistics.mean([r['performance_per_dollar'] for r in nas_analysis.values()])
            
            print(f"\nCOST-EFFECTIVENESS COMPARISON:")
            print(f"  MinIO: {minio_avg_perf_per_dollar:.2f} Mbps/$ average")
            print(f"  NAS-FTP: {nas_avg_perf_per_dollar:.2f} Mbps/$ average")
            
            if minio_avg_perf_per_dollar > nas_avg_perf_per_dollar:
                improvement = ((minio_avg_perf_per_dollar - nas_avg_perf_per_dollar) / nas_avg_perf_per_dollar) * 100
                print(f"  MinIO is {improvement:.1f}% more cost-effective")
            else:
                improvement = ((nas_avg_perf_per_dollar - minio_avg_perf_per_dollar) / minio_avg_perf_per_dollar) * 100
                print(f"  NAS-FTP is {improvement:.1f}% more cost-effective")
