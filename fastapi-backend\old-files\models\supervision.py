from sqlalchemy import Column, Integer, String, Text, TIMESTAMP
from . import Base

class Supervision(Base):
    __tablename__ = "supervision"

    id = Column("Id", Integer, primary_key=True, autoincrement=True)  # Added PK
    processed_by = Column("Processed_By", String)
    processed_at = Column("Processed_at", TIMESTAMP)
    source = Column("Source", String, default="Local")
    folder_id = Column("Folder_id", String)
    power = Column("Power", String)
    sheet_id = Column("Sheet_id", String)
    document_type = Column("Document_type", String)
