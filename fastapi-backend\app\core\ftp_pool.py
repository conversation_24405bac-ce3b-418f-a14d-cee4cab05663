# ftp_pool.py
from typing import  List, Dict
import logging
import asyncio
from contextlib import asynccontextmanager
from ftplib import FTP, FTP_TLS

log = logging.getLogger("ftp_pool")

class FTPConnectionError(Exception):
    """Custom exception for FTP connection errors"""
    pass

class FTPPool:
    """
    Thread-safe pool of pre-authenticated ftplib.FTP objects.
    Each checkout gives exclusive use of one socket.
    """

    def __init__(self, host: str, port: int, username: str, password: str, 
                 size: int = 10, tls: bool = False, timeout: int = 15):
        """Initialize FTP connection pool.
        
        Args:
            host: FTP server hostname
            port: FTP server port
            username: FTP username
            password: FTP password
            size: Maximum pool size
            tls: Whether to use FTPS/TLS
            timeout: Connection timeout in seconds
        """
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.size = size
        self.tls = tls
        self.timeout = timeout
        
        self._pool: List[FTP] = []
        self._in_use: Dict[FTP, bool] = {}
        self._lock = asyncio.Lock()
        self._semaphore = asyncio.Semaphore(size)

    async def _create_connection(self) -> FTP:
        """Create a new FTP connection."""
        try:
            ftp_class = FTP_TLS if self.tls else FTP
            ftp = ftp_class()
            ftp.connect(self.host, self.port, timeout=self.timeout)
            ftp.login(self.username, self.password)
            if self.tls:
                ftp.prot_p()
            return ftp
        except Exception as e:
            log.error(f"Error creating FTP connection: {str(e)}")
            raise

    async def _get_connection(self) -> FTP:
        """Get an available connection from the pool or create a new one."""
        async with self._lock:
            # Try to get an existing unused connection
            for ftp in self._pool:
                if not self._in_use.get(ftp, False):
                    try:
                        # Test if connection is still alive
                        await asyncio.to_thread(ftp.voidcmd, "NOOP")
                        self._in_use[ftp] = True
                        return ftp
                    except:
                        # Remove dead connection
                        self._pool.remove(ftp)
                        if ftp in self._in_use:
                            del self._in_use[ftp]
            
            # Create new connection if pool not full
            if len(self._pool) < self.size:
                ftp = await self._create_connection()
                self._pool.append(ftp)
                self._in_use[ftp] = True
                return ftp
                
            raise RuntimeError("No available connections in pool")

    async def _release_connection(self, ftp: FTP):
        """Release a connection back to the pool."""
        async with self._lock:
            if ftp in self._in_use:
                self._in_use[ftp] = False

    @asynccontextmanager
    async def acquire(self):
        """Acquire a connection from the pool.
        
        Usage:
            async with pool.acquire() as ftp:
                await asyncio.to_thread(ftp.cwd, "/some/path")
        """
        async with self._semaphore:
            ftp = await self._get_connection()
            try:
                yield ftp
            finally:
                await self._release_connection(ftp)

    async def close(self):
        """Close all connections in the pool."""
        async with self._lock:
            for ftp in self._pool:
                try:
                    ftp.quit()
                except:
                    try:
                        ftp.close()
                    except:
                        pass
            self._pool.clear()
            self._in_use.clear()