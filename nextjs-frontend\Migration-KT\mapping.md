
# Revised Next.js Architecture with Migration Mapping

Based on your requirements, here's the updated architecture with specific mapping from the existing codebase:

```
dadp-frontend/
├── src/
│   ├── app/                         # App Router pages
│   │   ├── page.tsx                 # Landing page (Home)
│   │   ├── admin/                   # Admin dashboard page
│   │   │   └── page.tsx             # Admin main page
│   │   ├── annotator/               # Annotator interface page
│   │   │   └── page.tsx             # Annotator main page
│   │   ├── auditor/                 # Auditor interface page
│   │   │   └── page.tsx             # Auditor main page
│   │   ├── documind/                # Documind service page
│   │   │   └── page.tsx             # Documind main page
│   │   ├── synthetic/               # Synthetic data service page
│   │   │   └── page.tsx             # SynGround main page
│   │   ├── note-ocr/                # Note OCR service page
│   │   │   └── page.tsx             # NoteOCR main page
│   │   └── layout.tsx               # Root layout with header/footer
│   ├── components/                  # All components organized by function
│   │   ├── landing/                 # Landing page components
│   │   │   ├── Hero.tsx             # Hero section
│   │   │   ├── About.tsx            # About section
│   │   │   ├── Features.tsx         # Features section
│   │   │   └── Contact.tsx          # Contact section
│   │   ├── auth/                    # Auth modal components
│   │   │   ├── LoginModal.tsx       # Login modal
│   │   │   ├── RegisterModal.tsx    # Registration modal
│   │   │   └── PasswordModal.tsx    # Change password modal
│   │   ├── layout/                  # Layout components
│   │   │   ├── Header.tsx           # Header with navigation
│   │   │   └── Footer.tsx           # Footer component
│   │   ├── admin/                   # Admin components
│   │   │   ├── UserManagement.tsx   # User management
│   │   │   ├── DataConnectors.tsx   # Data source connections
│   │   │   └── ...                  # Other admin components
│   │   ├── annotator/               # Annotator components
│   │   │   ├── ImageAnnotation.tsx  # Image annotation
│   │   │   ├── VerificationMode.tsx # Verification interface
│   │   │   └── ...                  # Other annotator components
│   │   ├── auditor/                 # Auditor components
│   │   │   ├── TaskList.tsx         # Available tasks
│   │   │   ├── History.tsx          # Audit history
│   │   │   └── ...                  # Other auditor components
│   │   ├── synthetic/               # SynGround components
│   │   ├── documind/                # Documind components
│   │   └── note-ocr/                # NoteOCR components
│   ├── contexts/                    # State management
│   │   ├── AuthContext.tsx          # Authentication context
│   │   ├── ModalContext.tsx         # Modal control context
│   │   └── ...                      # Other contexts
│   └── lib/                         # Utilities and services
```

## Migration Mapping - Files to Move

### 1. Landing Page (`/src/app/page.tsx` + components)

**Source Files:**
- `templates/landing.html` → Map to multiple components:
  - `src/components/landing/Hero.tsx`
  - `src/components/landing/Features.tsx`
  - `src/components/landing/About.tsx`
  - `src/components/landing/Contact.tsx`
- `static/js/common/landing.js` → Logic to incorporate
- `static/css/common/landing_page.css` → Convert to Tailwind/CSS modules

### 2. Authentication Modals

**Source Files:**
- `templates/login.html` → `src/components/auth/LoginModal.tsx`
- `templates/user_register.html` → `src/components/auth/RegisterModal.tsx`
- `templates/change_password.html` → `src/components/auth/PasswordModal.tsx`
- `static/js/common/login.js` → Logic to incorporate
- `static/css/common/login.css` → Convert to Tailwind/CSS modules

### 3. Layout Components

**Source Files:**
- `templates/base.html` (navigation section) → `src/components/layout/Header.tsx`
- `templates/base.html` (footer section) → `src/components/layout/Footer.tsx`
- `static/js/main.js` → Common functionality to incorporate

### 4. Admin Page & Components

**Source Files:**
- `templates/admin/dashboard.html` → `src/app/admin/page.tsx`
- `templates/admin/manage_users.html` → `src/components/admin/UserManagement.tsx`
- `templates/admin/admin_ocr_directory.html` → `src/components/admin/OcrDirectory.tsx`
- `templates/admin/telegram_channels_list.html` → `src/components/admin/TelegramChannels.tsx`
- `templates/admin/data_sources.html` → `src/components/admin/DataSources.tsx`
- `templates/admin/auditor_tracking.html` → `src/components/admin/AuditorTracking.tsx`
- `static/js/admin/*.js` files → Convert functionality to TypeScript

### 5. Annotator Page & Components

**Source Files:**
- `templates/annotator/annotator_dashboard.html` → `src/app/annotator/page.tsx`
- `templates/annotator/annotate.html` → `src/components/annotator/ImageAnnotation.tsx`
- `templates/annotator/review.html` → `src/components/annotator/ReviewInterface.tsx`
- `templates/annotator/supervision.html` → `src/components/annotator/Supervision.tsx`
- `static/js/annotator/annotate.js` → `src/lib/hooks/useAnnotation.ts`
- `static/js/annotator/review.js` → Logic to incorporate
- `static/js/annotator/supervision.js` → Logic to incorporate
- `static/css/annotator/*.css` → Convert to Tailwind/CSS modules

### 6. Auditor Page & Components

**Source Files:**
- `templates/auditor/dashboard.html` → `src/app/auditor/page.tsx`
- `templates/auditor/available_tasks.html` → `src/components/auditor/TaskList.tsx`
- `templates/auditor/history.html` → `src/components/auditor/History.tsx`
- `static/js/auditor/*.js` files → Convert functionality to TypeScript
- `static/css/auditor/*.css` → Convert to Tailwind/CSS modules

### 7. Service Pages

**Documind:**
- `templates/documind/*.html` → `src/app/documind/page.tsx` + components
- `static/js/documind/*.js` → Convert functionality to TypeScript

**SynGround:**
- `templates/synthetic/synthetic_landing.html` → `src/app/synthetic/page.tsx`
- `templates/synthetic/home.html` → `src/components/synthetic/Home.tsx`
- `templates/synthetic/dataset.html` → `src/components/synthetic/Dataset.tsx`
- `templates/synthetic/upload_for_conversation.html` → `src/components/synthetic/UploadConversation.tsx`
- `static/js/synthetic/*.js` → Convert functionality to TypeScript

**NoteOCR:**
- `templates/note_ocr/*.html` → `src/app/note-ocr/page.tsx` + components
- `static/js/noteocr/*.js` → Convert functionality to TypeScript

### 8. Common JavaScript and Utils

- `static/js/main.js` → `src/lib/utils.ts`
- Any utility functions across files → Consolidate in `src/lib/`

## Implementation Plan

1. **Set up Next.js project** with the command provided earlier
2. **Create base layout** with header, footer, and modal context
3. **Implement landing page** with all sections
4. **Create authentication modals** with context for showing/hiding
5. **Build service pages** one by one
6. **Implement role-based pages** (admin, annotator, auditor)
7. **Migrate specialized components** like image annotation tools

## Special Considerations

1. **Image Annotation Tools**: The current implementation uses Cropper.js. You'll need React equivalents like `react-image-crop` or `react-easy-crop`.

2. **Authentication Flow**: Instead of page navigation, implement a modal system with React context:
   ```typescript
   // src/contexts/ModalContext.tsx
   export const ModalContext = createContext({
     isLoginOpen: false,
     isRegisterOpen: false,
     isPasswordOpen: false,
     openLogin: () => {},
     openRegister: () => {},
     openPassword: () => {},
     closeAll: () => {},
   });
   ```

3. **API Integration**: You'll need to ensure the backend APIs support the new frontend:
   - Check for CORS configuration
   - Update authentication mechanisms (JWT recommended)
   - Ensure all current functionality has API endpoints

4. **Component State Management**: For complex components like the image annotator, consider using Zustand or React Context:
   ```typescript
   // Example for annotation state
   export const useAnnotationStore = create((set) => ({
     currentImage: null,
     labels: {},
     setCurrentImage: (image) => set({ currentImage: image }),
     setLabel: (imageName, label) => set((state) => ({
       labels: { ...state.labels, [imageName]: label }
     })),
   }));
   ```

Would you like me to elaborate on any specific part of this migration plan?
