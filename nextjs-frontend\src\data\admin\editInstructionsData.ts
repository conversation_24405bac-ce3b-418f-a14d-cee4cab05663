// Mock dataset types for demonstration
export interface Dataset {
  id: number;
  dataset_name: string;
}

export const manualDatasets: Dataset[] = [
  { id: 1, dataset_name: 'Medical Records Dataset' },
  { id: 2, dataset_name: 'Financial Documents Dataset' },
  { id: 3, dataset_name: 'Legal Contracts Dataset' },
];

export const verificationDatasets: Dataset[] = [
  { id: 4, dataset_name: 'Verified Medical Records' },
  { id: 5, dataset_name: 'Verified Financial Documents' },
];

export const defaultDatasetInstructions = (datasetId: string): string => {
  return `These are the default instructions for dataset ${datasetId}.\n\n1. Look for the relevant information in the image.\n2. Label according to the provided guidelines.\n3. Be consistent with your labeling approach.\n4. If unsure, mark the field as "uncertain".`;
};

export const defaultSupervisionInstructions = `General instructions for supervision mode:\n\n1. Review the work of annotators for accuracy.\n2. Provide feedback on mistakes.\n3. Ensure consistency across annotations.\n4. Report any systematic issues to administrators.`; 