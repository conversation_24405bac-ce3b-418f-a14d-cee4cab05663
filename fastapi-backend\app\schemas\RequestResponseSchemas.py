from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Union

class SuccessResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None

class ErrorResponse(BaseModel):
    success: bool = False
    error: str

class GenerateQARequest(BaseModel):
    sources: List[str]
    qaCount: int = 10
    difficulty: str = "intermediate"
    model: str
    api_key: Optional[str] = None
    use_default_key: bool = False
    customQueries: Optional[List[str]] = []
    includeAutoQueries: bool = True

class GenerateConceptsRequest(BaseModel):
    sources: List[str]
    sampleCount: int = 10
    difficulty: str = "intermediate"
    model: str
    api_key: Optional[str] = None
    use_default_key: bool = False

class JsonContentRequest(BaseModel):
    json_path: str
    image_folder: Optional[str] = None

class TaskResponse(BaseModel):
    task_id: str
    image_path: str
    labels: Dict[str, Any]
    status: str = "pending"

class TasksResponse(BaseModel):
    success: bool
    tasks: List[TaskResponse]