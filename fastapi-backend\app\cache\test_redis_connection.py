import redis
import os

# --- Configuration ---
# Get Redis connection details from environment variables or use defaults.
# To set environment variables, you can use:
# export REDIS_HOST='***********'
# export REDIS_PORT='6379'
# export REDIS_PASSWORD='your_password' # (if you have one)

REDIS_HOST = os.getenv("REDIS_HOST", "***********")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None) # Set to your password if you have one, otherwise None

def test_redis_connection():
    """
    Connects to a Redis server and tests the connection.
    """
    try:
        print(f"Attempting to connect to Redis at {REDIS_HOST}:{REDIS_PORT}...")

        # Create a Redis client.
        # decode_responses=True makes the client return strings instead of bytes.
        r = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            password=REDIS_PASSWORD,
            decode_responses=True,
            socket_connect_timeout=5  # Timeout for connection in seconds
        )

        # Ping the Redis server to check the connection.
        pong = r.ping()

        if pong:
            print("Successfully connected to Redis! Server responded with: PONG")
            # You can perform other operations here, like setting and getting a key.
            print("Performing a simple SET and GET operation...")
            r.set("test_key", "hello_redis")
            value = r.get("test_key")
            print(f"  SET 'test_key' to 'hello_redis'")
            print(f"  GET 'test_key' returned: '{value}'")
            # Clean up the test key
            r.delete("test_key")
            print("  Deleted 'test_key'.")

        else:
            print("Connected to Redis, but the server did not respond to PING as expected.")

    except redis.exceptions.ConnectionError as e:
        print(f"Error: Could not connect to Redis at {REDIS_HOST}:{REDIS_PORT}.")
        print("Please check the following:")
        print("1. Is the Redis server running on the VM?")
        print("2. Is the `bind` directive in redis.conf set to `0.0.0.0` or the VM's IP address?")
        print("3. Is `protected-mode` set to `no` if you are not using a password?")
        print("4. Is the firewall on the VM or your network blocking port 6379?")
        print(f"   (You can check with `sudo ufw status` or `sudo iptables -L` on the VM)")
        print(f"Details: {e}")

    except redis.exceptions.AuthenticationError as e:
        print(f"Error: Authentication failed.")
        print("Please check if the `REDIS_PASSWORD` is correct.")
        print(f"Details: {e}")

    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    test_redis_connection() 