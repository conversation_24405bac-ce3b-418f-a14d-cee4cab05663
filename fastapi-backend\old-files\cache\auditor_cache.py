"""
Auditor-related caching functionality.
Provides caching for auditor module data to improve performance.
"""

from cache.redis_connector import cache_set, cache_get, cache_delete
from cache.base import serialize_for_cache
import logging
from core.config import settings

logger = logging.getLogger('auditor_cache')

# Cache key prefixes
AUDITOR_REVIEW_LIST_PREFIX = "auditor:review_list:"
AUDITOR_BATCH_DETAILS_PREFIX = "auditor:batch_details:"
AUDITOR_STATS_PREFIX = "auditor:stats:"
AUDITOR_AVAILABLE_TASKS_PREFIX = "auditor:available_tasks"
AUDITOR_TASK_DETAILS_PREFIX = "auditor:task_details:"
AUDITOR_VERIFICATION_MODES_PREFIX = "auditor:verification_modes"
AUDITOR_DATASETS_PREFIX = "auditor:datasets"
AUDITOR_VERIFIERS_PREFIX = "auditor:verifiers:"
AUDITOR_VERIFICATION_FILES_PREFIX = "auditor:verification_files:"
AUDITOR_SELECTION_STATE_PREFIX = "auditor:selection_state:"

# Cache TTL values (in seconds) - loaded from configuration
REVIEW_LIST_TTL = settings.redis_settings.auditor_review_list_ttl
BATCH_DETAILS_TTL = settings.redis_settings.auditor_batch_details_ttl
STATS_TTL = settings.redis_settings.auditor_stats_ttl
AVAILABLE_TASKS_TTL = settings.redis_settings.auditor_available_tasks_ttl
TASK_DETAILS_TTL = settings.redis_settings.auditor_task_details_ttl
VERIFICATION_MODES_TTL = settings.redis_settings.auditor_verification_modes_ttl
DATASETS_TTL = settings.redis_settings.auditor_datasets_ttl
VERIFIERS_TTL = settings.redis_settings.auditor_verifiers_ttl
VERIFICATION_FILES_TTL = settings.redis_settings.auditor_verification_files_ttl
SELECTION_STATE_TTL = settings.redis_settings.auditor_selection_state_ttl

def generate_review_list_key(username, mode="manual"):
    """
    Generate a cache key for an auditor's review list

    Args:
        username: Auditor's username
        mode: 'manual' or 'verification'

    Returns:
        str: Cache key
    """
    return f"{AUDITOR_REVIEW_LIST_PREFIX}{username}:{mode}"

def generate_batch_details_key(batch_id):
    """
    Generate a cache key for batch details

    Args:
        batch_id: Batch ID

    Returns:
        str: Cache key
    """
    return f"{AUDITOR_BATCH_DETAILS_PREFIX}{batch_id}"

def generate_stats_key(username, stats_type="performance"):
    """
    Generate a cache key for auditor statistics

    Args:
        username: Auditor's username
        stats_type: Type of statistics

    Returns:
        str: Cache key
    """
    return f"{AUDITOR_STATS_PREFIX}{username}:{stats_type}"

def generate_available_tasks_key(username=None, verification_mode=None):
    """
    Generate a cache key for available tasks

    Args:
        username: Auditor's username (if None, all users)
        verification_mode: Verification mode (if None, all modes)

    Returns:
        str: Cache key
    """
    if username and verification_mode:
        return f"{AUDITOR_AVAILABLE_TASKS_PREFIX}:{username}:{verification_mode}"
    elif username:
        return f"{AUDITOR_AVAILABLE_TASKS_PREFIX}:{username}"
    else:
        return AUDITOR_AVAILABLE_TASKS_PREFIX

def generate_task_details_key(task_id):
    """
    Generate a cache key for task details

    Args:
        task_id: Task ID

    Returns:
        str: Cache key
    """
    return f"{AUDITOR_TASK_DETAILS_PREFIX}{task_id}"

def generate_verification_modes_key():
    """
    Generate a cache key for verification modes

    Returns:
        str: Cache key
    """
    return AUDITOR_VERIFICATION_MODES_PREFIX

def generate_datasets_key():
    """
    Generate a cache key for datasets

    Returns:
        str: Cache key
    """
    return AUDITOR_DATASETS_PREFIX

def generate_verifiers_key(dataset_id=None, verification_mode=None):
    """
    Generate a cache key for verifiers

    Args:
        dataset_id: Dataset ID (if None, all verifiers)
        verification_mode: Verification mode (if None, all modes)

    Returns:
        str: Cache key
    """
    if dataset_id and verification_mode:
        return f"{AUDITOR_VERIFIERS_PREFIX}{dataset_id}:{verification_mode}"
    elif dataset_id:
        return f"{AUDITOR_VERIFIERS_PREFIX}{dataset_id}"
    return AUDITOR_VERIFIERS_PREFIX


def generate_verification_files_key(dataset_id=None, verifier_id=None):
    """
    Generate a cache key for verification files

    Args:
        dataset_id: Dataset ID
        verifier_id: Verifier ID

    Returns:
        str: Cache key
    """
    if dataset_id and verifier_id:
        return f"{AUDITOR_VERIFICATION_FILES_PREFIX}{dataset_id}:{verifier_id}"
    return AUDITOR_VERIFICATION_FILES_PREFIX

def generate_selection_state_key(username):
    """
    Generate a cache key for selection state

    Args:
        username: Username

    Returns:
        str: Cache key
    """
    return f"{AUDITOR_SELECTION_STATE_PREFIX}{username}"


def cache_batch_details(batch_id, batch_details):
    """
    Cache batch details

    Args:
        batch_id: Batch ID
        batch_details: Batch details dictionary

    Returns:
        bool: Success status
    """
    key = generate_batch_details_key(batch_id)
    ttl = BATCH_DETAILS_TTL

    # Serialize datetime objects to strings
    serialized_batch_details = serialize_for_cache(batch_details)

    logger.info(f"Caching batch details for batch {batch_id}")
    return cache_set(key, serialized_batch_details, ttl)

def get_cached_batch_details(batch_id):
    """
    Get cached batch details

    Args:
        batch_id: Batch ID

    Returns:
        dict: Batch details or None if not found
    """
    key = generate_batch_details_key(batch_id)
    logger.debug(f"Getting cached batch details for batch {batch_id}")
    return cache_get(key, json_decode=True)

def cache_auditor_stats(username, stats_data, stats_type="performance"):
    """
    Cache auditor statistics

    Args:
        username: Auditor's username
        stats_data: Statistics data
        stats_type: Type of statistics

    Returns:
        bool: Success status
    """
    key = generate_stats_key(username, stats_type)
    ttl = STATS_TTL

    # Serialize datetime objects to strings
    serialized_stats_data = serialize_for_cache(stats_data)

    logger.info(f"Caching {stats_type} statistics for {username}")
    return cache_set(key, serialized_stats_data, ttl)

def get_cached_auditor_stats(username, stats_type="performance"):
    """
    Get cached auditor statistics

    Args:
        username: Auditor's username
        stats_type: Type of statistics

    Returns:
        dict: Statistics data or None if not found
    """
    key = generate_stats_key(username, stats_type)
    logger.debug(f"Getting cached {stats_type} statistics for {username}")
    return cache_get(key, json_decode=True)

def cache_available_tasks(username, verification_mode, tasks):
    """
    Cache available tasks

    Args:
        username: Auditor's username
        verification_mode: Verification mode
        tasks: List of available tasks or dashboard data

    Returns:
        bool: Success status
    """
    # Check for None values before proceeding
    if tasks is None:
        logger.warning(f"Attempted to cache None tasks for {username} (mode: {verification_mode}). Skipping cache operation.")
        return False

    key = generate_available_tasks_key(username, verification_mode)
    ttl = AVAILABLE_TASKS_TTL

    # Serialize datetime objects to strings
    serialized_tasks = serialize_for_cache(tasks)

    logger.info(f"Caching available tasks for {username} (mode: {verification_mode})")
    return cache_set(key, serialized_tasks, ttl)

def get_cached_available_tasks(username, verification_mode=None):
    """
    Get cached available tasks

    Args:
        username: Auditor's username
        verification_mode: Verification mode (if None, get all modes)

    Returns:
        list: Available tasks or None if not found
    """
    key = generate_available_tasks_key(username, verification_mode)
    logger.debug(f"Getting cached available tasks for {username} (mode: {verification_mode})")
    return cache_get(key, json_decode=True)

def cache_task_details(task_id, task_details):
    """
    Cache task details

    Args:
        task_id: Task ID
        task_details: Task details dictionary

    Returns:
        bool: Success status
    """
    key = generate_task_details_key(task_id)
    ttl = TASK_DETAILS_TTL

    # Serialize datetime objects to strings
    serialized_task_details = serialize_for_cache(task_details)

    logger.info(f"Caching task details for task {task_id}")
    return cache_set(key, serialized_task_details, ttl)

def get_cached_task_details(task_id):
    """
    Get cached task details

    Args:
        task_id: Task ID

    Returns:
        dict: Task details or None if not found
    """
    key = generate_task_details_key(task_id)
    logger.debug(f"Getting cached task details for task {task_id}")
    return cache_get(key, json_decode=True)

def cache_verification_modes(modes):
    """
    Cache verification modes

    Args:
        modes: List of verification modes

    Returns:
        bool: Success status
    """
    key = generate_verification_modes_key()
    ttl = VERIFICATION_MODES_TTL

    logger.info(f"Caching {len(modes)} verification modes")
    return cache_set(key, modes, ttl)

def get_cached_verification_modes():
    """
    Get cached verification modes

    Returns:
        list: Verification modes or None if not found
    """
    key = generate_verification_modes_key()
    logger.debug("Getting cached verification modes")
    return cache_get(key, json_decode=True)

def cache_datasets(datasets):
    """
    Cache datasets

    Args:
        datasets: List of datasets

    Returns:
        bool: Success status
    """
    key = generate_datasets_key()
    ttl = DATASETS_TTL

    # Serialize datetime objects to strings
    serialized_datasets = serialize_for_cache(datasets)

    logger.info(f"Caching {len(datasets)} datasets")
    return cache_set(key, serialized_datasets, ttl)

def get_cached_datasets():
    """
    Get cached datasets

    Returns:
        list: Datasets or None if not found
    """
    key = generate_datasets_key()
    logger.debug("Getting cached datasets")
    return cache_get(key, json_decode=True)

def cache_verifiers(verifiers, dataset_id=None, verification_mode=None):
    """
    Cache verifiers

    Args:
        verifiers: List of verifiers
        dataset_id: Dataset ID (if None, all verifiers)
        verification_mode: Verification mode (if None, all modes)

    Returns:
        bool: Success status
    """
    key = generate_verifiers_key(dataset_id, verification_mode)
    ttl = VERIFIERS_TTL

    # Serialize datetime objects to strings
    serialized_verifiers = serialize_for_cache(verifiers)

    logger.info(f"Caching {len(verifiers)} verifiers for dataset {dataset_id if dataset_id else 'all'} and mode {verification_mode if verification_mode else 'all'}")
    return cache_set(key, serialized_verifiers, ttl)

def get_cached_verifiers(dataset_id=None, verification_mode=None):
    """
    Get cached verifiers

    Args:
        dataset_id: Dataset ID (if None, all verifiers)
        verification_mode: Verification mode (if None, all modes)

    Returns:
        list: Verifiers or None if not found
    """
    key = generate_verifiers_key(dataset_id, verification_mode)
    logger.debug(f"Getting cached verifiers for dataset {dataset_id if dataset_id else 'all'} and mode {verification_mode if verification_mode else 'all'}")
    return cache_get(key, json_decode=True)

def cache_verification_files(files, dataset_id, verifier_id):
    """
    Cache verification files

    Args:
        files: List of verification files
        dataset_id: Dataset ID
        verifier_id: Verifier ID

    Returns:
        bool: Success status
    """
    key = generate_verification_files_key(dataset_id, verifier_id)
    ttl = VERIFICATION_FILES_TTL

    # Serialize datetime objects to strings
    serialized_files = serialize_for_cache(files)

    logger.info(f"Caching {len(files)} verification files for dataset {dataset_id} and verifier {verifier_id}")
    return cache_set(key, serialized_files, ttl)

def get_cached_verification_files(dataset_id, verifier_id):
    """
    Get cached verification files

    Args:
        dataset_id: Dataset ID
        verifier_id: Verifier ID

    Returns:
        list: Verification files or None if not found
    """
    key = generate_verification_files_key(dataset_id, verifier_id)
    logger.debug(f"Getting cached verification files for dataset {dataset_id} and verifier {verifier_id}")
    return cache_get(key, json_decode=True)

def cache_selection_state(username, state):
    """
    Cache selection state

    Args:
        username: Username
        state: Selection state dictionary

    Returns:
        bool: Success status
    """
    key = generate_selection_state_key(username)
    ttl = SELECTION_STATE_TTL

    logger.info(f"Caching selection state for {username}")
    return cache_set(key, state, ttl)

def get_cached_selection_state(username):
    """
    Get cached selection state

    Args:
        username: Username

    Returns:
        dict: Selection state or None if not found
    """
    key = generate_selection_state_key(username)
    logger.debug(f"Getting cached selection state for {username}")
    return cache_get(key, json_decode=True)

def invalidate_review_list_cache(username=None, mode=None):
    """
    Invalidate review list cache

    Args:
        username: Auditor's username (if None, invalidate all review list caches)
        mode: 'manual' or 'verification' (if None, invalidate both modes)

    Returns:
        bool: Success status
    """
    if username:
        if mode:
            # Invalidate specific user and mode cache
            key = generate_review_list_key(username, mode)
            logger.info(f"Invalidating review list cache for {username} (mode: {mode})")
            return cache_delete(key)
        else:
            # Invalidate both manual and verification caches for this user
            manual_key = generate_review_list_key(username, 'manual')
            verification_key = generate_review_list_key(username, 'verification')

            logger.info(f"Invalidating all review list caches for {username}")
            manual_deleted = cache_delete(manual_key)
            verification_deleted = cache_delete(verification_key)

            return manual_deleted and verification_deleted
    else:
        # Invalidate the base key
        key = AUDITOR_REVIEW_LIST_PREFIX
        logger.info("Invalidating all review list caches")
        return cache_delete(key)

def invalidate_batch_details_cache(batch_id):
    """
    Invalidate batch details cache

    Args:
        batch_id: Batch ID

    Returns:
        bool: Success status
    """
    key = generate_batch_details_key(batch_id)
    logger.info(f"Invalidating batch details cache for batch {batch_id}")
    return cache_delete(key)

def invalidate_available_tasks_cache(username=None, verification_mode=None):
    """
    Invalidate available tasks cache

    Args:
        username: Auditor's username (if None, invalidate all user caches)
        verification_mode: Verification mode (if None, invalidate all mode caches)

    Returns:
        bool: Success status
    """
    if username:
        if verification_mode:
            # Invalidate specific user and mode cache
            key = generate_available_tasks_key(username, verification_mode)
            logger.info(f"Invalidating available tasks cache for {username} (mode: {verification_mode})")
            return cache_delete(key)
        else:
            # Invalidate all mode caches for this user
            # In a production system, you would use Redis pattern matching
            # For now, we'll just invalidate the user-level cache
            key = generate_available_tasks_key(username)
            logger.info(f"Invalidating all available tasks caches for {username}")
            return cache_delete(key)
    else:
        # Invalidate all user caches
        # In a production system, you would use Redis pattern matching
        key = generate_available_tasks_key()
        logger.info("Invalidating all available tasks caches")
        return cache_delete(key)

def invalidate_task_details_cache(task_id):
    """
    Invalidate task details cache

    Args:
        task_id: Task ID

    Returns:
        bool: Success status
    """
    key = generate_task_details_key(task_id)
    logger.info(f"Invalidating task details cache for task {task_id}")
    return cache_delete(key)

def invalidate_verification_modes_cache():
    """
    Invalidate verification modes cache

    Returns:
        bool: Success status
    """
    key = generate_verification_modes_key()
    logger.info("Invalidating verification modes cache")
    return cache_delete(key)

def invalidate_datasets_cache():
    """
    Invalidate datasets cache

    Returns:
        bool: Success status
    """
    key = generate_datasets_key()
    logger.info("Invalidating datasets cache")
    return cache_delete(key)

def invalidate_verifiers_cache(dataset_id=None, verification_mode=None):
    """
    Invalidate verifiers cache

    Args:
        dataset_id: Dataset ID (if None, invalidate all verifiers caches)
        verification_mode: Verification mode (if None, invalidate all mode caches)

    Returns:
        bool: Success status
    """
    if dataset_id:
        if verification_mode:
            key = generate_verifiers_key(dataset_id, verification_mode)
            logger.info(f"Invalidating verifiers cache for dataset {dataset_id} and mode {verification_mode}")
        else:
            key = generate_verifiers_key(dataset_id)
            logger.info(f"Invalidating verifiers cache for dataset {dataset_id}")
        return cache_delete(key)
    else:
        # Invalidate the base key
        key = AUDITOR_VERIFIERS_PREFIX
        logger.info("Invalidating all verifiers caches")
        return cache_delete(key)

def invalidate_verification_files_cache(dataset_id=None, verifier_id=None):
    """
    Invalidate verification files cache

    Args:
        dataset_id: Dataset ID (if None, invalidate all dataset caches)
        verifier_id: Verifier ID (if None, invalidate all verifier caches)

    Returns:
        bool: Success status
    """
    if dataset_id and verifier_id:
        key = generate_verification_files_key(dataset_id, verifier_id)
        logger.info(f"Invalidating verification files cache for dataset {dataset_id} and verifier {verifier_id}")
        return cache_delete(key)
    else:
        # For now, just invalidate the base key
        key = AUDITOR_VERIFICATION_FILES_PREFIX
        logger.info("Invalidating all verification files caches")
        return cache_delete(key)

def invalidate_selection_state_cache(username):
    """
    Invalidate selection state cache

    Args:
        username: Username

    Returns:
        bool: Success status
    """
    key = generate_selection_state_key(username)
    logger.info(f"Invalidating selection state cache for {username}")
    return cache_delete(key)

def invalidate_auditor_stats_cache(username=None, stats_type=None):
    """
    Invalidate auditor statistics cache

    Args:
        username: Auditor's username (if None, invalidate all auditor statistics caches)
        stats_type: Type of statistics (if None, invalidate all statistics types)

    Returns:
        bool: Success status
    """
    if username:
        if stats_type:
            # Invalidate specific statistics cache
            key = generate_stats_key(username, stats_type)
            logger.info(f"Invalidating {stats_type} statistics cache for {username}")
            return cache_delete(key)
        else:
            # Invalidate performance stats by default
            key = generate_stats_key(username, "performance")
            logger.info(f"Invalidating performance statistics cache for {username}")
            return cache_delete(key)
    else:
        # Invalidate the base key
        key = AUDITOR_STATS_PREFIX
        logger.info("Invalidating all auditor statistics caches")
        return cache_delete(key)
