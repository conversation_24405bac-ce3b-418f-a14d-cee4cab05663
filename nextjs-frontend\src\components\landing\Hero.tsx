'use client';

import React, { FC, useEffect, useState } from 'react';

import { motion, AnimatePresence } from 'framer-motion';
import {
  Rocket,
  BookOpen,
  Database,
  Layers,
  FileText,
  Shield,
  CheckCircle,
  Link as LucideLink,
} from 'lucide-react';
import Link from 'next/link';

interface HeroProps {
  onOpenLogin: () => void;
}

const taglineAnim = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -30 },
  transition: { duration: 0.5, ease: [0.4, 0, 0.2, 1] as [number, number, number, number] },
};


const Hero: FC<HeroProps> = ({ onOpenLogin }) => {
  const leftMessages = ['Your Data', 'Your Database', 'Your Privacy'];
  const rightMessages = ['Our AI Processing', 'Our Human Validation', 'Our Accountability'];
  const [leftTaglineIndex, setLeftTaglineIndex] = useState(0);
  const [rightTaglineIndex, setRightTaglineIndex] = useState(0);

  useEffect(() => {
    const leftTimer = setInterval(() => {
      setLeftTaglineIndex(i => (i + 1) % leftMessages.length);
    }, 3800);
    const rightTimer = setInterval(() => {
      setRightTaglineIndex(i => (i + 1) % rightMessages.length);
    }, 3800);

    return () => {
      clearInterval(leftTimer);
      clearInterval(rightTimer);
    };
  }, [leftMessages.length, rightMessages.length]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const parallax = document.querySelector('.parallax') as HTMLElement;
      if (parallax) parallax.style.backgroundPositionY = `${scrollY * 0.5}px`;
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section
      className="bg-gradient-to-br from-blue-50 to-blue-100 min-h-screen flex items-center justify-center px-6"
      aria-label="Hero Section"
      style={{ backgroundAttachment: 'fixed' }}
    >
      <div className="text-center space-y-8">
        <h1 className="text-6xl md:text-7xl font-extrabold text-blue-900 select-none rounded-3xl px-8 py-4 inline-block">
          DATA ANALYTICS & DELIVERY PLATFORM
        </h1>

        <p className="text-lg md:text-xl text-gray-700 max-w-2xl mx-auto">
          HAI-Agent platform with HITL for precise AI training Datasets & Enterprise Data Management.
        </p>

        <div className="flex flex-col md:flex-row items-center justify-center gap-8">
          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={leftTaglineIndex}
              {...taglineAnim}
              className="flex items-center gap-3 bg-[#e0e5ec] rounded-3xl px-6 py-4 shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] max-w-xs"
            >
              <Shield className="w-6 h-6 text-blue-600" />
              <span className="font-semibold text-lg text-blue-900">
                {leftMessages[leftTaglineIndex]}
              </span>
            </motion.div>
          </AnimatePresence>

          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={rightTaglineIndex}
              {...taglineAnim}
              className="flex items-center gap-3 bg-[#e0e5ec] rounded-3xl px-6 py-4 shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] max-w-xs"
            >
              <CheckCircle className="w-6 h-6 text-green-600" />
              <span className="font-semibold text-lg text-blue-900">
                {rightMessages[rightTaglineIndex]}
              </span>
            </motion.div>
          </AnimatePresence>
        </div>

        <div className="flex flex-wrap justify-center gap-6">
          {/* Button: open login */}
          <button
            onClick={onOpenLogin}
            className="flex items-center gap-2 bg-blue-200 text-blue-800 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
          >
            <Rocket className="w-5 h-5" />
            Get Started
          </button>

          {/* Same-page anchor */}
          <Link
            href="#features"
            className="flex items-center gap-2 bg-blue-50 text-blue-700 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
          >
            <BookOpen className="w-5 h-5" />
            Learn More
          </Link>

          {/* External link */}
          <a
            href="https://huggingface.co/Process-Venue"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 bg-gray-200 text-gray-800 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
          >
            <Database className="w-5 h-5" />
            See Sample Dataset
          </a>

          {/* Internal navigation */}
          <Link
            href="/synthetic"
            className="flex items-center gap-2 bg-gray-200 text-gray-800 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
          >
            <Layers className="w-5 h-5" />
            SynGround
          </Link>

       <Link
  href="/documind"
  className="flex items-center gap-2 bg-gray-200 text-gray-800 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
>
  <LucideLink className="w-5 h-5" />
  Documind-o
</Link>


          <Link
            href="/note-ocr"
            className="flex items-center gap-2 bg-gray-200 text-gray-800 rounded-3xl px-8 py-3 font-semibold shadow-[6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)] transition-transform hover:scale-105 active:shadow-[inset_6px_6px_16px_rgba(0,0,0,0.1),_-6px_-6px_16px_rgba(255,255,255,0.7)]"
          >
            <FileText className="w-5 h-5" />
            NoteOCR
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Hero;
