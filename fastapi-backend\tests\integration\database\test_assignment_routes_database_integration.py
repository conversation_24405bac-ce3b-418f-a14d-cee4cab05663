"""
Integration tests for Assignment Routes Database operations with REAL database operations.
Tests end-to-end assignment workflows through API endpoints with database persistence.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE:
- Tests actual assignment route endpoints from routes/assignment_routes/
- Database operations managed by service layer through API calls
- Dynamic schema generation tested with real allocation strategies
- Multi-database coordination (master + project databases) tested
"""
import pytest
import pytest_asyncio
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text
import json
import time
from datetime import datetime

from app.post_db.master_models.users import users, UserRole
from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.files_registry import FilesRegistry, FileType
from app.post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
from app.post_db.allocation_models.project_users import ProjectUsers
from app.services.auth_service import AuthService
from app.schemas.UserSchemas import UserRegisterRequest

# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory


@pytest_asyncio.fixture
async def assignment_test_user(test_master_db: AsyncSession):
    """Create test user for assignment operations."""
    # Create annotator user using factory
    user_data = test_factory.users.create_user_register_request(role="annotator")
    
    success, user = await AuthService.register_user(test_master_db, user_data)
    assert success
    
    # Generate JWT token
    from app.core.security import create_access_token
    token_data = {
        "sub": user.username,
        "user_id": user.id,
        "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
        "email": user.email
    }
    access_token = create_access_token(data=token_data)
    
    return {
        "user": user,
        "token": access_token,
        "password": "testpass123"
    }


@pytest_asyncio.fixture
async def authenticated_assignment_client(client: AsyncClient, assignment_test_user):
    """Create authenticated client for assignment operations."""
    token = assignment_test_user["token"]
    client.headers.update({"Authorization": f"Bearer {token}"})
    return client


@pytest_asyncio.fixture
async def assignment_test_environment(test_master_db: AsyncSession, test_db: AsyncSession):
    """Set up complete assignment test environment with batches and files."""
    # Use factory to create complete environment
    environment = await test_factory.create_complete_test_environment(test_db, test_master_db)
    
    # Create additional batches for assignment testing
    batches = []
    for i in range(3):
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier=f"ASSIGN_BATCH_{i+1}_{int(time.time())}",
            total_files=5,
            annotation_count=2,
            assignment_count=0,
            is_priority=(i == 0)  # First batch is priority
        )
        test_db.add(batch)
        batches.append(batch)
    
    await test_db.commit()
    for batch in batches:
        await test_db.refresh(batch)
    
    # Create files for each batch
    files = []
    for batch in batches:
        for j in range(batch.total_files):
            file = test_factory.files.create_files_registry(
                batch.id,
                file_identifier=f"assign_file_{batch.id}_{j+1}.jpg",
                file_type=FileType.IMAGE
            )
            test_db.add(file)
            files.append(file)
    
    await test_db.commit()
    for file in files:
        await test_db.refresh(file)
    
    environment["assignment_batches"] = batches
    environment["assignment_files"] = files
    return environment


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.smoke            # Suite marker - Critical path
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestBatchAllocationOperations:
    """SMOKE TEST SUITE: Critical batch allocation operations."""
    
    @pytest.mark.asyncio
    async def test_get_batch_allocations_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        setup_test_database
    ):
        """Test getting batch allocations with REAL database operations."""
        project = assignment_test_environment["project"]
        
        # Test batch allocations endpoint
        response = await authenticated_assignment_client.get(
            f"/api/projects/{project.id}/batch-allocations"
        )
        
        # Should succeed or fail gracefully based on project database setup
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Verify response structure
            assert "batches" in result or "success" in result
            assert "progress" in result or "error" in result
            
            if "batches" in result:
                batches = result["batches"]
                assert isinstance(batches, list)
                
                # Verify batch structure
                for batch in batches:
                    assert "batch_id" in batch
                    assert "batch_name" in batch
                    assert "status" in batch
        
        elif response.status_code == 404:
            result = response.json()
            assert "not found" in result["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_batch_allocation_progress_tracking_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        setup_test_database
    ):
        """Test batch allocation progress tracking with REAL database operations."""
        project = assignment_test_environment["project"]
        
        # Test allocation progress endpoint
        response = await authenticated_assignment_client.get(
            f"/api/projects/{project.id}/allocation-progress"
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Verify progress structure
            assert "total_batches" in result or "success" in result
            
            if "total_batches" in result:
                assert isinstance(result["total_batches"], int)
                assert result["total_batches"] >= 0


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.regression       # Suite marker - Comprehensive testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestUserAssignmentOperations:
    """REGRESSION TEST SUITE: Comprehensive user assignment operations."""
    
    @pytest.mark.asyncio
    async def test_user_batch_assignment_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test user batch assignment with REAL database operations."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # Add user to project database first
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username
        )
        test_db.add(project_user)
        await test_db.commit()
        await test_db.refresh(project_user)
        
        # Test batch assignment request
        assignment_data = {
            "user_id": user.id,
            "username": user.username,
            "project_code": project.project_code,
            "role": "annotator"
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/assign-batch",
            json=assignment_data
        )
        
        # Expected outcomes: success, no available batches, or configuration error
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code == 200:
            result = response.json()
            assert result["success"] is True
            assert "assigned" in result["message"].lower() or "batch" in result["message"].lower()
            
            # Verify assignment in database
            if "data" in result and "batch_id" in result["data"]:
                batch_id = result["data"]["batch_id"]
                
                # Check user_allocations table
                stmt = select(UserAllocations).where(
                    UserAllocations.user_id == user.id,
                    UserAllocations.batch_id == batch_id
                )
                allocation_result = await test_db.execute(stmt)
                user_allocation = allocation_result.scalar_one_or_none()
                
                if user_allocation:
                    assert user_allocation.user_id == user.id
                    assert user_allocation.batch_id == batch_id
                    assert user_allocation.allocation_role == AllocationRole.ANNOTATOR
                    assert user_allocation.is_active is True
    
    @pytest.mark.asyncio
    async def test_user_batch_removal_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test user batch removal with REAL database operations."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # Set up user with existing assignment
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username,
            current_batch=batches[0].id
        )
        test_db.add(project_user)
        
        # Create user allocation
        user_allocation = UserAllocations(
            user_id=user.id,
            batch_id=batches[0].id,
            username=user.username,
            total_files=5,
            allocation_role=AllocationRole.ANNOTATOR,
            is_active=True
        )
        test_db.add(user_allocation)
        await test_db.commit()
        
        # Test batch removal
        removal_data = {
            "user_id": user.id,
            "project_code": project.project_code,
            "batch_id": batches[0].id
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/remove-user",
            json=removal_data
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code == 200:
            result = response.json()
            assert result["success"] is True
            assert "removed" in result["message"].lower()
            
            # Verify removal in database
            stmt = select(UserAllocations).where(
                UserAllocations.user_id == user.id,
                UserAllocations.batch_id == batches[0].id,
                UserAllocations.is_active == True
            )
            allocation_result = await test_db.execute(stmt)
            active_allocation = allocation_result.scalar_one_or_none()
            
            # Should be no active allocation or allocation should be marked inactive
            if active_allocation:
                assert active_allocation.is_active is False


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.regression       # Suite marker - Comprehensive testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestAnnotatorAssignmentOperations:
    """REGRESSION TEST SUITE: Comprehensive annotator assignment operations."""
    
    @pytest.mark.asyncio
    async def test_annotator_batch_assignment_service_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test annotator batch assignment service with REAL database operations."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        
        # Add user to project
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username
        )
        test_db.add(project_user)
        await test_db.commit()
        
        # Test automatic batch assignment for annotator
        response = await authenticated_assignment_client.post(
            f"/api/annotator/assign-to-project/{project.project_code}"
        )
        
        # Should succeed or fail gracefully based on available batches
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code == 200:
            result = response.json()
            assert result["success"] is True
            
            # Should contain batch assignment information
            if "data" in result:
                data = result["data"]
                assert "batch_id" in data or "project_code" in data
    
    @pytest.mark.asyncio
    async def test_annotator_current_assignment_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test getting annotator's current assignment with REAL database operations."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # Set up user with current assignment
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username,
            current_batch=batches[0].id
        )
        test_db.add(project_user)
        
        user_allocation = UserAllocations(
            user_id=user.id,
            batch_id=batches[0].id,
            username=user.username,
            total_files=5,
            allocation_role=AllocationRole.ANNOTATOR,
            is_active=True
        )
        test_db.add(user_allocation)
        await test_db.commit()
        
        # Test getting current assignment
        response = await authenticated_assignment_client.get(
            f"/api/annotator/current-assignment/{project.project_code}"
        )
        
        # Should succeed or return no assignment
        assert response.status_code in [200, 404, 422]
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success"):
                # Should contain assignment information
                assert "data" in result
                data = result["data"]
                
                if "batch_id" in data:
                    assert data["batch_id"] == batches[0].id
                if "user_id" in data:
                    assert data["user_id"] == user.id


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.regression       # Suite marker - Advanced testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestAdvancedAssignmentOperations:
    """REGRESSION TEST SUITE: Advanced assignment logic and algorithms."""
    
    @pytest.mark.asyncio
    async def test_priority_batch_assignment_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test priority batch assignment with REAL database operations."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # Add user to project
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username
        )
        test_db.add(project_user)
        await test_db.commit()
        
        # Ensure one batch is marked as priority
        priority_batch = batches[0]
        priority_batch.is_priority = True
        test_db.add(priority_batch)
        await test_db.commit()
        
        # Test assignment with priority consideration
        assignment_data = {
            "user_id": user.id,
            "username": user.username,
            "project_code": project.project_code,
            "priority_first": True
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/assign-priority-batch",
            json=assignment_data
        )
        
        # Should succeed or fail gracefully
        assert response.status_code in [200, 404, 422, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("success") and "data" in result:
                assigned_batch_id = result["data"].get("batch_id")
                
                # Should prioritize the priority batch
                if assigned_batch_id:
                    assert assigned_batch_id == priority_batch.id
    
    @pytest.mark.asyncio
    async def test_batch_capacity_management_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        test_master_db: AsyncSession,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test batch capacity management with REAL database operations."""
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # Create multiple users for capacity testing
        users = []
        for i in range(3):
            user_data = test_factory.users.create_user_register_request(role="annotator")
            success, user = await AuthService.register_user(test_master_db, user_data)
            assert success
            
            # Add to project
            project_user = test_factory.users.create_project_user(
                role="annotator",
                user_id=user.id,
                username=user.username
            )
            test_db.add(project_user)
            users.append(user)
        
        await test_db.commit()
        
        # Fill batch to capacity (annotation_count = 2)
        target_batch = batches[0]
        target_batch.assignment_count = 0  # Reset
        test_db.add(target_batch)
        await test_db.commit()
        
        assigned_users = 0
        for user in users[:target_batch.annotation_count]:  # Only assign up to annotation_count
            # Generate token for each user
            from app.core.security import create_access_token
            token_data = {"sub": user.username, "user_id": user.id}
            access_token = create_access_token(data=token_data)
            
            # Create authenticated client
            temp_client = authenticated_assignment_client
            temp_client.headers.update({"Authorization": f"Bearer {access_token}"})
            
            assignment_data = {
                "user_id": user.id,
                "username": user.username,
                "project_code": project.project_code,
                "batch_id": target_batch.id
            }
            
            response = await temp_client.post(
                "/api/assignments/assign-specific-batch",
                json=assignment_data
            )
            
            if response.status_code == 200:
                assigned_users += 1
        
        # Verify capacity management
        # The batch should not accept more users than annotation_count allows
        stmt = select(AllocationBatches).where(AllocationBatches.id == target_batch.id)
        result = await test_db.execute(stmt)
        updated_batch = result.scalar_one_or_none()
        
        if updated_batch:
            assert updated_batch.assignment_count <= updated_batch.annotation_count


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.regression       # Suite marker - Error scenarios
@pytest.mark.high             # Priority marker - P1 (error handling is critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestAssignmentErrorHandling:
    """REGRESSION TEST SUITE: Assignment error handling and edge cases."""
    
    @pytest.mark.asyncio
    async def test_assignment_without_authentication(
        self,
        client: AsyncClient,
        setup_test_database
    ):
        """Test assignment routes without authentication should fail."""
        assignment_endpoints = [
            ("/api/assignments/assign-batch", "POST"),
            ("/api/assignments/remove-user", "POST"),
            ("/api/annotator/current-assignment/TEST_PROJECT", "GET"),
        ]
        
        for endpoint, method in assignment_endpoints:
            if method == "POST":
                response = await client.post(endpoint, json={})
            else:
                response = await client.get(endpoint)
            
            assert response.status_code == 401, f"Endpoint {endpoint} should require authentication"
    
    @pytest.mark.asyncio
    async def test_assignment_invalid_project_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_user,
        setup_test_database
    ):
        """Test assignment operations with invalid project."""
        user = assignment_test_user["user"]
        
        # Test assignment to non-existent project
        assignment_data = {
            "user_id": user.id,
            "username": user.username,
            "project_code": "NONEXISTENT_PROJECT",
            "role": "annotator"
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/assign-batch",
            json=assignment_data
        )
        
        # Should fail with appropriate error
        assert response.status_code in [404, 422, 500]
        
        if response.status_code != 500:  # 500 might be internal error
            result = response.json()
            assert "not found" in result.get("detail", "").lower() or \
                   "invalid" in result.get("detail", "").lower() or \
                   result.get("success") is False
    
    @pytest.mark.asyncio
    async def test_assignment_invalid_user_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        setup_test_database
    ):
        """Test assignment operations with invalid user."""
        project = assignment_test_environment["project"]
        
        # Test assignment with non-existent user
        assignment_data = {
            "user_id": 999999,  # Non-existent user ID
            "username": "nonexistent_user",
            "project_code": project.project_code,
            "role": "annotator"
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/assign-batch",
            json=assignment_data
        )
        
        # Should fail with appropriate error
        assert response.status_code in [404, 422, 500]
        
        if response.status_code != 500:  # 500 might be internal error
            result = response.json()
            assert "user" in result.get("detail", "").lower() or \
                   result.get("success") is False


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.assignment       # Feature marker
@pytest.mark.regression       # Suite marker - Complex workflows
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Complex workflows take time
class TestComplexAssignmentWorkflows:
    """REGRESSION TEST SUITE: Complex multi-step assignment workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_assignment_workflow_real_database(
        self,
        authenticated_assignment_client: AsyncClient,
        assignment_test_environment,
        assignment_test_user,
        test_db: AsyncSession,
        setup_test_database
    ):
        """Test complete assignment workflow from user addition to batch completion."""
        user = assignment_test_user["user"]
        project = assignment_test_environment["project"]
        batches = assignment_test_environment["assignment_batches"]
        
        # 1. Add user to project
        project_user = test_factory.users.create_project_user(
            role="annotator",
            user_id=user.id,
            username=user.username
        )
        test_db.add(project_user)
        await test_db.commit()
        
        # 2. Assign user to batch
        assignment_data = {
            "user_id": user.id,
            "username": user.username,
            "project_code": project.project_code,
            "role": "annotator"
        }
        
        response = await authenticated_assignment_client.post(
            "/api/assignments/assign-batch",
            json=assignment_data
        )
        
        assigned_batch_id = None
        if response.status_code == 200:
            result = response.json()
            if result.get("success") and "data" in result:
                assigned_batch_id = result["data"].get("batch_id")
        
        # 3. Verify assignment in database
        if assigned_batch_id:
            stmt = select(UserAllocations).where(
                UserAllocations.user_id == user.id,
                UserAllocations.batch_id == assigned_batch_id,
                UserAllocations.is_active == True
            )
            allocation_result = await test_db.execute(stmt)
            user_allocation = allocation_result.scalar_one_or_none()
            
            assert user_allocation is not None
            assert user_allocation.allocation_role == AllocationRole.ANNOTATOR
            
            # 4. Simulate work completion
            user_allocation.files_completed = user_allocation.total_files
            test_db.add(user_allocation)
            await test_db.commit()
            
            # 5. Test assignment completion
            completion_data = {
                "user_id": user.id,
                "batch_id": assigned_batch_id,
                "project_code": project.project_code
            }
            
            response = await authenticated_assignment_client.post(
                "/api/assignments/complete-batch",
                json=completion_data
            )
            
            # Should succeed or fail gracefully
            assert response.status_code in [200, 404, 422, 500]
            
            if response.status_code == 200:
                result = response.json()
                assert result.get("success") is True or "completed" in result.get("message", "").lower()
