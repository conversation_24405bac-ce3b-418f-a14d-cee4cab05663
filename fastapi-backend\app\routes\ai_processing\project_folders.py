from fastapi import APIRouter, Depends, HTTP<PERSON>x<PERSON>, Request, Cookie #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from core.session_manager import get_master_db_session
from dependencies.auth import get_current_active_user, get_user_service, UserService
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

async def get_optional_user(
    request: Request,
    access_token: Optional[str] = Cookie(None),
    user_service: UserService = Depends(get_user_service)
) -> Optional[dict]:
    """
    Optional authentication dependency that doesn't raise 401 errors.
    Returns user dict if authenticated, None if not.
    """
    from dependencies.auth import verify_token

    if not access_token:
        return None  # No token provided, return None (not authenticated)

    try:
        payload = verify_token(access_token)
        if payload is None:
            return None  # Invalid token, return None

        username = payload.get("sub")
        token_type = payload.get("token_type")

        if token_type != "access":
            return None  # Wrong token type, return None

        user = await user_service.get_user_by_username(username)
        if user is None or not user.is_active:
            return None  # User not found or inactive, return None

        request.state.user = username
        return payload  # Valid user, return user info

    except Exception:
        return None  # Any error, return None (not authenticated)

@router.get("/projects")
async def get_projects_with_folders(
    file_type: Optional[str] = None,
    current_user: Optional[dict] = Depends(get_optional_user),
    db: AsyncSession = Depends(get_master_db_session)
):
    try:
        # Get all projects that have folder_path, are active, and have allocation strategy with requires_ai_preprocessing=true
        stmt = select(ProjectsRegistry).join(
            AllocationStrategies, 
            ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id
        ).where(
            ProjectsRegistry.folder_path.isnot(None),
            ProjectsRegistry.project_status == 'active',
            AllocationStrategies.requires_ai_preprocessing == True
        )
        
        # Add file_type filtering if specified
        if file_type:
            if file_type.lower() == 'image':
                stmt = stmt.where(ProjectsRegistry.project_type == 'image')
            elif file_type.lower() == 'audio':
                stmt = stmt.where(ProjectsRegistry.project_type == 'audio')
        result = await db.execute(stmt)
        projects = result.scalars().all()

        project_data = []
        for project in projects:
            resolved_path = project.folder_path

            # Base project info available to everyone
            project_info = {
                "id": project.id,
                "project_code": project.project_code,
                "project_name": project.project_name,
                "project_type": project.project_type,
                "ai_processing": project.ai_processing,
                "has_storage_credentials": bool(project.credentials),
                "connection_type": project.connection_type
            }

            # Add sensitive info only for authenticated users
            if current_user:
                project_info.update({
                    "folder_path": project.folder_path,
                    "resolved_path": resolved_path,
                    "client_id": project.client_id,
                })

            project_data.append(project_info)

        response = {
            "projects": project_data,
            "authenticated": bool(current_user)
        }

        if current_user:
            response["user"] = {
                "username": current_user.get("sub"),
                "role": current_user.get("role")
            }

        return response

    except Exception as e:
        logger.error(f"Error fetching projects: {e}")
        raise HTTPException(status_code=500, detail="Error fetching projects")

@router.get("/projects/{project_code}")
async def get_project_folder(
    project_code: str,
    current_user: Optional[dict] = Depends(get_optional_user),
    db: AsyncSession = Depends(get_master_db_session)
):
    """Get specific project folder path for AI processing"""
    try:
        stmt = select(ProjectsRegistry).join(
            AllocationStrategies, 
            ProjectsRegistry.allocation_strategy_id == AllocationStrategies.id
        ).where(
            ProjectsRegistry.project_code == project_code,
            ProjectsRegistry.folder_path.isnot(None),
            ProjectsRegistry.project_status == 'active',
            AllocationStrategies.requires_ai_preprocessing == True
        )
        result = await db.execute(stmt)
        project = result.scalar_one_or_none()

        if not project:
            raise HTTPException(status_code=404, detail="Project not found or no folder path available")

        resolved_path = project.folder_path

        # Base project info available to everyone
        project_info = {
            "id": project.id,
            "project_code": project.project_code,
            "project_name": project.project_name,
            "project_type": project.project_type,
            "ai_processing": project.ai_processing,
            "has_storage_credentials": bool(project.credentials),
            "connection_type": project.connection_type
        }

        # Add sensitive info only for authenticated users
        if current_user:
            project_info.update({
                "folder_path": project.folder_path,
                "resolved_path": resolved_path,
                "client_id": project.client_id,
            })

        response = {
            "project": project_info,
            "authenticated": bool(current_user)
        }

        if current_user:
            response["user"] = {
                "username": current_user.get("sub"),
                "role": current_user.get("role")
            }

        return response

    except Exception as e:
        logger.error(f"Error fetching project: {e}")
        raise HTTPException(status_code=500, detail="Error fetching project")
