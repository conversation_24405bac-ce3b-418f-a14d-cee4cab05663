import React, { useState, useEffect } from "react";
import { MediaViewerProps } from "./types";
import { FaCheck, FaTable, FaSpinner } from "react-icons/fa";

interface CSVViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  batchId?: number;
  projectCode?: string;
  isLabeled?: boolean;
}

export default function CSVViewer({
  mediaUrl,
  onLoad,
  onError,
  isLabeled = false
}: CSVViewerProps) {
  const [textContent, setTextContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTextContent = async () => {
      try {
        setIsLoading(true);
        
        // For CSV projects, the content is already in mediaUrl (which is the file_identifier)
        if (mediaUrl) {
          setTextContent(mediaUrl);
          onLoad?.();
        } else {
          throw new Error('No content available');
        }
      } catch (error) {
        onError?.(`Failed to load content: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadTextContent();
  }, [mediaUrl, onLoad, onError]);

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center gap-3">
          <FaSpinner className="animate-spin text-2xl text-blue-500" />
          <span className="text-gray-600">Loading content...</span>
        </div>
      </div>
    );
  }

  if (!textContent) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        No content available
      </div>
    );
  }

  return (
    <div className="h-full w-full flex flex-col bg-white border border-gray-200 rounded-lg overflow-hidden relative">
         
            <div className="flex-1 p-4 w-full">
              <textarea
                value={textContent}
                readOnly
                className="w-full h-full min-w-[600px] p-3 border-2 border-blue-500 rounded resize-none focus:outline-none text-sm overflow-hidden"
                placeholder="Content will appear here..."
                style={{ minWidth: '600px', width: '100%', overflow: 'hidden' }}
              />
            </div>
      
      {isLabeled && (
        <div className="absolute top-2 right-2">
          <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs flex items-center gap-1">
            <FaCheck className="text-xs" />
            Labeled
          </div>
        </div>
      )}
    </div>
  );
}