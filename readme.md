# DATP (Data Annotation and Delivery Platform) - FastAPI Backend Documentation

## Project Overview

**DATP (Data Annotation and Delivery Platform)** is a comprehensive FastAPI-based backend system designed for managing data annotation workflows, document processing, and synthetic dataset generation. The platform provides a complete solution for handling image annotation, document OCR processing, user management, and dataset supervision with role-based access control.

### Key Features
- **Multi-role Authentication System** (Admin, Annotator, Auditor, Client)
- **Image Annotation/Verification/Auditing Workflow Management**
- **Document OCR Processing** (PDF/Image extraction)
- **Synthetic Dataset Generation** using LLMs
- **Batch Processing and Supervision**
- **FTP/NAS & Google Drive Integration** for Data storage and retrieval
- **Redis Caching** for performance optimization


### Core Components

1. **FastAPI Application** (`main.py`) - Application entry point and configuration
2. **Route Handlers** (`routes/`) - HTTP endpoint definitions
3. **Service Layer** (`services/`) - Business logic implementation
4. **Data Models** (`db/models/`) - SQLAlchemy database models
5. **Configuration** (`core/`) - Application settings and security
6. **Utilities** (`utils/`) - Helper functions and external integrations

## Project Structure

```
fastapi-backend/
├── app/
│   ├── main.py                     # Application entry point
│   ├── requirements.txt            # Python dependencies
│   ├── database.db                 # SQLite database
│   ├── init_db.py                  # Database initialization
│   │
│   ├── core/                       # Core application components
│   │   ├── config.py               # Application configuration
│   │   ├── security.py             # Authentication & JWT handling
│   │   ├── logging.py              # Logging configuration
│   │   ├── nas_connector.py        # NAS file system integration
│   │   ├── nas_base.py             # NAS base connection class
│   │   ├── ftp_connector.py        # FTP connection handling
│   │   └── ftp_pool.py             # FTP connection pooling
│   │
│   ├── db/                         # Database layer
│   │   ├── config.py               # Database configuration
│   │   ├── db_connector.py         # Database connection handling
│   │   └── models/                 # SQLAlchemy models
│   │       ├── __init__.py         # Models registration
│   │       ├── user.py             # User model
│   │       ├── image_annotation.py # Image annotation model
│   │       ├── image_verification.py # Image verification model
│   │       ├── datasets.py         # Dataset model
│   │       ├── pdf_extractor.py    # PDF document models
│   │       ├── knowledge_base.py   # Knowledge base model
│   │       ├── supervision.py      # Supervision model
│   │       └── admin_settings.py   # Admin settings model
│   │
│   ├── routes/                     # API route handlers
│   │   ├── __init__.py             # Routes registration
│   │   ├── auth_routes.py          # Authentication endpoints
│   │   ├── admin_routes.py         # Admin management endpoints
│   │   ├── annotator_routes.py     # Annotator workflow endpoints
│   │   ├── auditor_routes.py       # Auditor verification endpoints
│   │   ├── client_routes.py        # Client access endpoints
│   │   ├── NoteOCR_routes.py       # OCR processing endpoints
│   │   ├── synthetic_dataset_routes.py # Dataset generation endpoints
│   │   ├── knowledge_base_routes.py # Knowledge management endpoints
│   │   ├── annotator_supervision_routes.py # Supervision endpoints
│   │   └── telegram_fetch_data_routes.py # Telegram integration
│   │
│   ├── services/                   # Business logic layer
│   │   ├── __init__.py
│   │   ├── auth_service.py         # Authentication business logic
│   │   ├── batch_service.py        # Batch processing logic
│   │   ├── annotator_service.py    # Annotation workflow logic
│   │   └── auditor_service.py      # Audit workflow logic
│   │
│   ├── schemas/                    # Pydantic data models
│   │   ├── __init__.py
│   │   ├── UserSchemas.py          # User-related schemas
│   │   ├── annotation_schemas.py   # Annotation schemas
│   │   ├── ImageProcessingSchemas.py # Image processing schemas
│   │   ├── PDF_ExtractorSchemas.py # PDF extraction schemas
│   │   ├── RequestResponseSchemas.py # Generic request/response schemas
│   │   ├── AdminSettingsSchemas.py # Admin settings schemas
│   │   └── FTPConfig.py            # FTP configuration schemas
│   │
│   ├── dependencies/               # Dependency injection
│   │   └── auth.py                 # Authentication dependencies
│   │
│   ├── NoteOCR/                    # OCR processing modules
│   │   ├── pdf_service.py          # PDF processing service
│   │   └── image_extractor_service.py # Image extraction service
│   │
│   ├── synthetic_dataset/          # AI dataset generation
│   │   ├── __init__.py
│   │   ├── reference_agent.py      # Reference-based AI agent
│   │   └── nonref_agents.py        # Non-reference AI agents
│   │
│   ├── utils/                      # Utility functions
│   │   ├── image_processing.py     # Image manipulation utilities
│   │   ├── google_drive/           # Google Drive integration
│   │   └── modules/                # Additional utility modules
│   │
│   ├── cache/                      # Caching layer
│   │   └── redis_connector.py      # Redis caching implementation
│   │
│   └── sessions/                   # Session storage
└── venv/                           # Virtual environment
```

## Codebase Walkthrough

### 1. Application Entry Point (`main.py`)

**Purpose**: Main FastAPI application initialization and configuration

**Key Classes/Functions**:
- `lifespan()`: Manages application startup/shutdown lifecycle
- `app`: FastAPI application instance with middleware configuration
- `custom_http_exception_handler()`: Global exception handling
- `google_drive_callback()`: OAuth callback for Google Drive integration

**Functionality**:
- Initializes Redis caching
- Configures CORS middleware
- Sets up session middleware
- Registers all API routes
- Handles Google Drive OAuth flow

### 2. Core Configuration (`core/config.py`)

**Purpose**: Centralized configuration management using Pydantic settings

**Key Classes**:
- `UserRole(Enum)`: User role definitions (ADMIN, ANNOTATOR, AUDITOR)
- `AnnotationMode(Enum)`: Annotation modes (ANNOTATION, VERIFICATION, SUPERVISION)
- `PathSettings`: File path configurations
- `NASSettings`: Network storage settings
- `RedisSettings`: Caching configuration with TTL settings
- `JWTSettings`: Authentication token settings
- `Settings`: Main application settings container

**Functionality**:
- Environment variable loading
- Configuration validation
- Settings inheritance and composition
- Connection settings for external services

### 3. Security Module (`core/security.py`)

**Purpose**: Authentication and authorization utilities

**Key Functions**:
- `hash_password(password: str) -> str`: Hash passwords using bcrypt
- `verify_password(plain_password: str, hashed_password: str) -> bool`: Verify password hashes
- `create_access_token(data: dict) -> str`: Generate JWT access tokens
- `create_refresh_token(data: dict) -> str`: Generate JWT refresh tokens
- `verify_token(token: str) -> dict`: Validate and decode JWT tokens

### 4. Database Models (`db/models/`)

#### User Model (`user.py`)
**Purpose**: User authentication and role management

**Class**: `User`
**Fields**:
- `id`: Primary key
- `username`: Unique username
- `full_name`: User's full name
- `email`: Unique email address
- `role`: User role (UserRole enum)
- `password_hash`: Hashed password
- `created_at`: Registration timestamp
- `last_login`: Last login timestamp
- `is_active`: Account status
- `annotator_mode`: Annotation mode for annotators

#### Image Annotation Model (`image_annotation.py`)
**Purpose**: Track image annotation assignments and progress

**Class**: `ImageAnnotation`
**Fields**:
- `id`: Primary key
- `annotator_username`: Assigned annotator
- `auditor_username`: Assigned auditor
- `dataset_name`: Dataset identifier
- `dataset_batch_name`: Batch identifier
- `images`: List of images (JSON)
- `label_file_path`: Path to label file
- `image_count`: Number of images in batch
- `assigned_at`: Assignment timestamp
- `processed_at`: Completion timestamp
- `annotation_status`: Current status
- `audit_status`: Audit completion flag
- `audited_at`: Audit timestamp
- `audit_comments`: Auditor feedback

#### Image Verification Model (`image_verification.py`)
**Purpose**: Track image verification workflow

**Class**: `ImageVerification`
**Fields**: Similar to ImageAnnotation but for verification tasks
- `verification_status`: Verification progress status

#### Dataset Model (`datasets.py`)
**Purpose**: Dataset configuration and progress tracking

**Class**: `Datasets`
**Fields**:
- `id`: Primary key
- `dataset_name`: Dataset identifier
- `dataset_image_path`: Path to source images
- `label_folder_path`: Path to labels
- `annotator_mode`: Annotation mode
- `instructions`: Task instructions
- `audited_batch`: Number of audited batches
- `completed_batch`: Number of completed batches
- `total_batch`: Total number of batches
- `dataset_status`: Overall dataset status
- `client_id`: Associated client

#### PDF Extractor Models (`pdf_extractor.py`)
**Purpose**: Document processing and OCR results storage

**Classes**:
- `Document`: Stores document metadata
  - `id`: Primary key
  - `filename`: Original filename
  - `upload_time`: Upload timestamp
  - `pages`: Relationship to Page objects

- `Page`: Stores individual page content
  - `id`: Primary key
  - `document_id`: Foreign key to Document
  - `page_number`: Page sequence number
  - `text_content`: Extracted text
  - `metadata_json`: Page metadata
  - `images`: Relationship to Image objects

- `Image`: Stores extracted images from pages
  - `id`: Primary key
  - `page_id`: Foreign key to Page
  - `image_data`: Base64 encoded image
  - `image_type`: Image format (png, jpg, etc.)

#### Knowledge Base Model (`knowledge_base.py`)
**Purpose**: Store reference data for synthetic dataset generation

**Class**: `KnowledgeEntry`
**Fields**:
- `id`: Primary key
- `title`: Entry title
- `topic`: Subject category
- `content`: Main content text
- `source`: Content source
- `created_at`: Creation timestamp
- `updated_at`: Last modification timestamp

**Methods**:
- `to_dict()`: Convert model to dictionary format

### 5. Service Layer (`services/`)

#### Authentication Service (`auth_service.py`)
**Purpose**: Business logic for user authentication and management

**Class**: `AuthService`
**Methods**:
- `register_user(db, user_data) -> Tuple[bool, Union[UserResponse, str]]`: Register new users
- `authenticate_user(db, login_data) -> Optional[UserResponse]`: Authenticate user credentials
- `generate_tokens(username) -> TokenResponse`: Create JWT tokens
- `change_password(db, username, password_data) -> Tuple[bool, str]`: Update user password
- `get_user_profile(db, username) -> Optional[UserResponse]`: Retrieve user profile

#### Batch Service (`batch_service.py`)
**Purpose**: Manage batch processing workflows

**Key Functions**:
- Batch creation and assignment
- Progress tracking
- Status updates
- Completion handling

#### Annotator Service (`annotator_service.py`)
**Purpose**: Handle annotation workflow logic

**Key Functions**:
- Task assignment
- Submission handling

#### Auditor Service (`auditor_service.py`)
**Purpose**: Manage audit and verification processes

**Key Functions**:
- Quality review
- Feedback management

### 6. API Routes (`routes/`)

#### Authentication Routes (`auth_routes.py`)
**Purpose**: User authentication endpoints

**Endpoints**:
- `POST /auth/register`: User registration
- `POST /auth/login`: User login
- `POST /auth/refresh-token`: Token refresh
- `POST /auth/change-password`: Password change
- `GET /auth/me`: Current user profile

#### Admin Routes (`admin_routes.py`)
**Purpose**: Administrative functions

**Key Endpoints**:
- User management
- Dataset configuration
- System settings
- Analytics and reporting
- FTP/NAS configuration
- Batch management

#### Annotator Routes (`annotator_routes.py`)
**Purpose**: Annotation workflow endpoints

**Key Endpoints**:
- Available tasks
- Task assignment
- Batch completion

#### Auditor Routes (`auditor_routes.py`)
**Purpose**: Audit and verification endpoints

**Key Endpoints**:
- Quality assessment
- Feedback submission

#### NoteOCR Routes (`NoteOCR_routes.py`)
**Purpose**: Document processing endpoints

**Key Endpoints**:
- PDF upload and processing
- Text extraction
- Image extraction

#### Synthetic Dataset Routes (`synthetic_dataset_routes.py`)
**Purpose**: Synthetic dataset generation

**Key Endpoints**:
- Dataset generation
- Reference data management

#### Knowledge Base Routes (`knowledge_base_routes.py`)
**Purpose**: Knowledge management endpoints

**Key Endpoints**:
- Knowledge entry CRUD
- Search and filtering
- Content retrieval

### 7. Synthetic Dataset Generation (`synthetic_dataset/`)

#### Reference Agent (`reference_agent.py`)
**Purpose**: Generate synthetic datasets using Google's Gemini model with Agno Agent based on knowledge base references

**Key Classes/Functions**:
- `DatasetType(Enum)`: Defines dataset types (QA, ARTICLES, CONVERSATION, CODE_SNIPPETS)
- `ReferenceDataRequest(BaseModel)`: Request model with knowledge_entry_id for reference-based generation
- `ReferenceDataResponse(BaseModel)`: Response model with generated data
- `ReferenceBasedGenerator`: Main generator class with methods:
  - `__init__()`: Initialize with GEMINI_API_KEY
  - `_get_agent(model_id)`: Create Agno agent with specified Gemini model
  - `_build_qa_prompt()`: Generate Q&A prompts using reference text context
  - `_build_articles_prompt()`: Generate article prompts using reference text context
  - `_build_conversation_prompt()`: Generate conversation prompts using reference text context
  - `_build_code_snippets_prompt()`: Generate code snippet prompts using reference text context
  - `_get_prompt_for_dataset_type()`: Route to appropriate prompt builder with reference context
  - `generate_dataset()`: Main async function to generate datasets using AI with reference text
- Output formatting

#### Non-Reference Agents (`nonref_agents.py`)
**Purpose**: Generate synthetic datasets using Google's Gemini model with Agno Agent

**Key Classes/Functions**:
- `DatasetType(Enum)`: Defines dataset types (QA, ARTICLES, CONVERSATION, CODE_SNIPPETS)
- `SyntheticDataRequest(BaseModel)`: Request model for dataset generation
- `SyntheticDataResponse(BaseModel)`: Response model with generated data
- `GeminiGenerator`: Main generator class with methods:
  - `__init__()`: Initialize with GEMINI_API_KEY
  - `_get_agent(model_id)`: Create Agno agent with specified Gemini model
  - `_build_qa_prompt()`: Generate prompts for Q&A dataset creation
  - `_build_articles_prompt()`: Generate prompts for article creation
  - `_build_conversation_prompt()`: Generate prompts for conversation datasets
  - `_build_code_snippets_prompt()`: Generate prompts for code snippet datasets
  - `_get_prompt_for_dataset_type()`: Route to appropriate prompt builder
  - `generate_dataset()`: Main async function to generate datasets using AI

### 8. OCR Processing (`NoteOCR/`)

#### PDF Service (`pdf_service.py`)
**Purpose**: PDF document processing

**Key Functions**:
- PDF parsing
- Text extraction
- Image extraction

#### Image Extractor Service (`image_extractor_service.py`)
**Purpose**: Extract images from documents

**Key Functions**:
- Image extraction
- Format conversion

### 9. Utilities (`utils/`)

#### Image Processing (`image_processing.py`)
**Purpose**: Image manipulation utilities

**Key Functions**:
- Image resizing
- Format conversion

#### Google Drive Integration (`utils/google_drive/`)
**Purpose**: Google Drive API integration

**Key Functions**:
- OAuth authentication
- File upload/download
- Folder management
- Permission handling

### 10. Dependency Injection (`dependencies/`)

#### Auth Dependencies (`auth.py`)
**Purpose**: Authentication dependency injection

**Key Classes/Functions**:
- `UserService`: User data access layer
- `get_current_user()`: Extract user from JWT token
- `get_current_active_user()`: Verify user is active
- `require_role()`: Role-based access control
- `create_user_response()`: Format user response

## Database Schemas

### Entity Relationship Overview

```
Users (1) ─── (N) ImageAnnotation
Users (1) ─── (N) ImageVerification
Datasets (1) ── (N) ImageAnnotation
Datasets (1) ── (N) ImageVerification
Documents (1) ── (N) Pages
Pages (1) ─── (N) Images
```

### Table Definitions

#### users
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| id | INTEGER | PRIMARY KEY, AUTOINCREMENT | Unique user identifier |
| username | STRING | UNIQUE, NOT NULL, INDEX | Login username |
| full_name | STRING | NOT NULL | User's full name |
| email | STRING | UNIQUE, NOT NULL, INDEX | Email address |
| role | STRING | NOT NULL, DEFAULT 'annotator' | User role |
| password_hash | STRING | NOT NULL | Hashed password |
| created_at | TIMESTAMP | DEFAULT now() | Registration time |
| last_login | TIMESTAMP | NULLABLE | Last login time |
| is_active | BOOLEAN | DEFAULT TRUE | Account status |
| annotator_mode | STRING | DEFAULT 'annotation' | Annotation mode |

#### image_annotation
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| Id | INTEGER | PRIMARY KEY | Unique annotation task ID |
| Annotator_Username | STRING | | Assigned annotator |
| Auditor_Username | STRING | | Assigned auditor |
| Dataset_Name | STRING | NOT NULL | Dataset identifier |
| Dataset_Batch_Name | STRING | NOT NULL | Batch identifier |
| Images | TEXT | NOT NULL | JSON list of images |
| Label_File_Path | STRING | NOT NULL | Path to label file |
| Image_Count | INTEGER | NOT NULL | Number of images |
| Assigned_At | TIMESTAMP | | Assignment time |
| Processed_At | TIMESTAMP | | Completion time |
| Annotation_Status | STRING | DEFAULT 'available' | Current status |
| Audit_Status | BOOLEAN | DEFAULT FALSE | Audit completion |
| Audited_At | TIMESTAMP | | Audit time |
| Audit_Comments | TEXT | | Auditor feedback |

#### image_verification
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| Id | INTEGER | PRIMARY KEY | Unique verification task ID |
| Annotator_Username | STRING | | Assigned annotator |
| Auditor_Username | STRING | | Assigned auditor |
| Dataset_Name | STRING | NOT NULL | Dataset identifier |
| Dataset_Batch_Name | STRING | NOT NULL | Batch identifier |
| Images | TEXT | NOT NULL | JSON list of images |
| Label_File_Path | STRING | NOT NULL | Path to label file |
| Image_Count | INTEGER | NOT NULL | Number of images |
| Assigned_At | TIMESTAMP | | Assignment time |
| Processed_At | TIMESTAMP | | Completion time |
| Verification_Status | STRING | DEFAULT 'available' | Current status |
| Audit_Status | BOOLEAN | DEFAULT FALSE | Audit completion |
| Audited_At | TIMESTAMP | | Audit time |
| Audit_Comments | TEXT | | Auditor feedback |

#### datasets
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| Id | INTEGER | PRIMARY KEY | Unique dataset ID |
| Dataset_Name | STRING | | Dataset identifier |
| Dataset_Image_Path | STRING | | Path to source images |
| Label_Folder_Path | STRING | | Path to labels |
| Annotator_Mode | STRING | | Annotation mode |
| Instructions | TEXT | | Task instructions |
| Audited_Batch | INTEGER | | Audited batch count |
| Completed_Batch | INTEGER | | Completed batch count |
| Total_Batch | INTEGER | | Total batch count |
| Dataset_Status | STRING | | Overall status |
| Client_Id | STRING | | Associated client |

#### documents
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| id | INTEGER | PRIMARY KEY, INDEX | Unique document ID |
| filename | STRING | INDEX | Original filename |
| upload_time | DATETIME | DEFAULT now() | Upload timestamp |

#### pages
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| id | INTEGER | PRIMARY KEY, INDEX | Unique page ID |
| document_id | INTEGER | FOREIGN KEY(documents.id) | Parent document |
| page_number | INTEGER | | Page sequence |
| text_content | TEXT | | Extracted text |
| metadata_json | TEXT | NULLABLE | Page metadata |

#### images
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| id | INTEGER | PRIMARY KEY, INDEX | Unique image ID |
| page_id | INTEGER | FOREIGN KEY(pages.id) | Parent page |
| image_data | TEXT | | Base64 image data |
| image_type | STRING | | Image format |

#### knowledge_base
| Column | Type | Constraints | Description |
|--------|------| ----------- | ----------- |
| id | INTEGER | PRIMARY KEY, INDEX | Unique entry ID |
| title | STRING(255) | NOT NULL, INDEX | Entry title |
| topic | STRING(100) | NOT NULL, INDEX | Subject category |
| content | TEXT | NOT NULL | Main content |
| source | STRING(255) | NULLABLE | Content source |
| created_at | DATETIME | DEFAULT now() | Creation time |
| updated_at | DATETIME | DEFAULT now() | Last update |

## API Endpoints

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh-token` - Token refresh
- `POST /api/auth/change-password` - Password change
- `GET /api/auth/me` - Current user info

### Admin Endpoints
- `GET /api/admin/users` - List all users
- `POST /api/admin/users` - Create user
- `PUT /api/admin/users/{user_id}` - Update user
- `DELETE /api/admin/users/{user_id}` - Delete user
- `GET /api/admin/datasets` - List datasets
- `POST /api/admin/datasets` - Create dataset
- `GET /api/admin/statistics` - System statistics

### Annotator Endpoints
- `GET /api/annotator/tasks` - Available tasks
- `POST /api/annotator/tasks/{task_id}/assign` - Assign task
- `POST /api/annotator/tasks/{task_id}/submit` - Submit work
- `GET /api/annotator/progress` - Work progress

### Auditor Endpoints
- `GET /api/auditor/review-queue` - Pending reviews
- `POST /api/auditor/review/{task_id}` - Submit review
- `GET /api/auditor/statistics` - Audit statistics

### OCR Endpoints
- `POST /api/noteocr/upload-pdf` - Upload PDF for processing
- `GET /api/noteocr/documents` - List processed documents
- `GET /api/noteocr/documents/{doc_id}` - Get document details
- `GET /api/noteocr/documents/{doc_id}/pages` - Get document pages

### Synthetic Dataset Endpoints
- `POST /api/synthetic-dataset/generate` - Generate dataset
- `GET /api/synthetic-dataset/jobs` - List generation jobs
- `GET /api/synthetic-dataset/jobs/{job_id}` - Get job status

### Knowledge Base Endpoints
- `GET /api/knowledge-base/entries` - List entries
- `POST /api/knowledge-base/entries` - Create entry
- `PUT /api/knowledge-base/entries/{entry_id}` - Update entry
- `DELETE /api/knowledge-base/entries/{entry_id}` - Delete entry

## Configuration

### Environment Variables

```bash
# Application Settings
HOST=0.0.0.0
PORT=5000
DEBUG=true

#Google Drive frontend redirect 
FRONTEND_URL=http://localhost:3000
# Database
DATABASE_URL=sqlite:///./database.db

# Authentication
JWT_SECRET_KEY=datp-sas-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7


# NAS/FTP Settings - will after entering 
NAS_URL='ftp://************:21'
NAS_USERNAME='Kanwar.rajsingh'
NAS_PASSWORD='SeK(6u2f'
NAS_TYPE=
CLIENT_ID=


# OCR API
OCR_API_KEY='pdf039490nvoinioe093ujnv39898'
OCR_API_URL='http://*************:8002'

# Synthetic Dataset Settings
GEMINI_API_KEY=
```

### Configuration Classes

The application uses Pydantic Settings for configuration management:

- **Settings**: Main configuration container
- **PathSettings**: File path configurations
- **NASSettings**: Network storage settings
- **RedisSettings**: Caching configuration
- **JWTSettings**: Authentication settings
- **CORSSettings**: Cross-origin settings
- **APISettings**: External API settings

## Setup and Installation

### Prerequisites
- Python 3.8+
- Node.js 18+ (for frontend)
- Redis (optional, for caching)
- FTP/NAS server (optional, for file storage)

### Quick Start Guide

Follow this sequence to set up the complete DATP application:

#### 1. Backend Setup (FastAPI)

**Step 1: Clone and Navigate**
```bash
cd fastapi-backend
```

**Step 2: Create Virtual Environment**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

**Step 3: Install Dependencies**
```bash
pip install -r app/requirements.txt
```

**Step 4: Environment Configuration**
Create `.env` file in `fastapi-backend/` directory:
```env
# Application Settings
HOST=0.0.0.0
PORT=5000
DEBUG=true

# Frontend Connection
FRONTEND_URL=http://localhost:3000

# Database
DATABASE_URL=sqlite:///./database.db

# Authentication
JWT_SECRET_KEY=datp-sas-secret-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# Redis (Optional)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# NAS/FTP Settings (Optional)
NAS_URL=ftp://your-nas-server:21
NAS_USERNAME=your-username
NAS_PASSWORD=your-password
NAS_TYPE=ftp
CLIENT_ID=your-client-id

# OCR API (Optional)
OCR_API_KEY=your-ocr-api-key
OCR_API_URL=http://your-ocr-server:8002

# AI/Synthetic Dataset (Optional)
GEMINI_API_KEY=your-gemini-api-key
```

**Step 5: Initialize Database**
```bash
cd app
python init_db.py
```
**Step 6: Initialize Documind Service**

The Documind Service is a separate FastAPI microservice that handles Telegram-related operations and data fetching. It runs independently from the main backend service and provides specialized endpoints for Telegram integration.

**Service Overview:**
- **Purpose**: Dedicated service for Telegram channel data fetching and processing
- **Port**: `8002` (default, configurable via `TELEGRAM_SERVICE_PORT`)
- **Architecture**: Microservice pattern for better separation of concerns
- **Features**: 
  - Telegram channel data extraction
  - Image processing and Google Drive integration
  - Independent caching and session management
  - Separate API documentation at `/docs`

**Configuration:**
The service uses its own configuration settings defined in `documind/telegram_service/config.py`:
- Default host: `0.0.0.0`
- Default port: `8002`
- CORS settings for frontend integration
- Independent Redis caching support

**Initialize and Start Documind Service:**
```bash
cd app
python documind_service.py
```

**Service Endpoints:**
- Root: `http://localhost:8002/` - Service information
- Telegram Routes: `http://localhost:8002/api/telegram/*` - Telegram operations

**Environment Variables (Optional):**
Add to your `.env` file for custom configuration:
```env
# Documind/Telegram Service Settings
TELEGRAM_SERVICE_HOST=0.0.0.0
TELEGRAM_SERVICE_PORT=8002
TELEGRAM_SERVICE_VERSION=1.0.0
TELEGRAM_SERVICE_DOCS_URL=/docs
TELEGRAM_SERVICE_REDOC_URL=/redoc
```

**Important Notes:**
- The Documind service should be started **before** the main backend service
- Both services can run simultaneously on different ports
- The service has its own Redis caching and session management
- Telegram functionality requires this service to be running

**Step 7: Start Main Backend Server**
```bash
python main.py
```
Backend will be available at: `http://localhost:5000`

#### 2. Frontend Setup (Next.js)

**Step 1: Navigate to Frontend**
```bash
cd ../nextjs-frontend
```

**Step 2: Install Dependencies**
```bash
npm install
```

**Step 3: Environment Configuration**
Create `.env.local` file in `nextjs-frontend/` directory:
```env
# Backend API Connection
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_API_BASE_URL=http://localhost:5000/api

# Optional: If using different ports
NEXT_PUBLIC_BACKEND_HOST=localhost
NEXT_PUBLIC_BACKEND_PORT=5000
```

**Step 4: Start Frontend Server**
```bash
npm run dev
```
Frontend will be available at: `http://localhost:3000`

### Application Initialization Sequence

**Critical Order for Startup:**

1. **Database First** → Run `python init_db.py` (creates tables and initial data)
2. **Documind Service Second** → Start Telegram service with `python documind_service.py` (port 8002)
3. **Main Backend Third** → Start FastAPI server with `python main.py` (port 5000)
4. **Frontend Last** → Start Next.js with `npm run dev` (port 3000)

### Port Configuration

#### Default Ports
- **Main Backend (FastAPI)**: `5000`
- **Documind Service (FastAPI)**: `8002`
- **Frontend (Next.js)**: `3000`
- **Redis** (if used): `6379`

#### Service Architecture
The DATP application follows a microservice architecture:
- **Main Backend Service**: Core application logic, authentication, annotation workflows
- **Documind Service**: Specialized Telegram integration and document processing
- **Frontend Service**: Next.js React application for user interface

#### Google Drive Integration
1. Create Google Cloud Project
2. Enable Google Drive API
3. Create OAuth 2.0 credentials
4. Add credentials to backend `.env`

This comprehensive setup guide ensures proper connection between backend and frontend components of the DATP application.
