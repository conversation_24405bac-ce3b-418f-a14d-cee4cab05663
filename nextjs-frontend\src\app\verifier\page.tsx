'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import VerifierDashboard from '@/components/verifier/VerifierDashboard';

import { API_BASE_URL } from "@/lib/api";


export default function VerifierPage() {
  const { user } = useAuth();
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboard = async () => {
      try {
        const resp = await fetch(`${API_BASE_URL}/verifier/dashboard`, { credentials: 'include' });
        if (resp.ok) {
          const json = await resp.json();
          setData(json);
        } else {
          console.error('Failed to fetch verifier dashboard', resp.status);
        }
      } catch (e) {
        console.error('Error fetching verifier dashboard', e);
      } finally {
        setLoading(false);
      }
    };
    fetchDashboard();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const username = data?.username || user?.username;
  const fullName = data?.full_name || user?.full_name;
  const projectCode = data?.project_code || null;
  const projectName = data?.project_name || null;

  return (
    <VerifierDashboard
      username={username}
      fullName={fullName}
      projectCode={projectCode}
      projectName={projectName}
    />
  );
}
