"""
Routes for annotator batch assignment operations.
Handles annotator-specific assignment requests like "Start Annotating".
"""

from fastapi import APIRouter, Depends, HTTPException, status # type: ignore
from typing import Dict, Any
import logging

from dependencies.auth import get_current_active_user, get_user_service, UserService
from services.annotator_batch_assignment_service import AnnotatorBatchAssignmentService

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/annotator",
    tags=["Annotator Assignment"],
    dependencies=[Depends(get_current_active_user)]
)

@router.post("/start-annotation", response_model=Dict[str, Any])
async def start_annotation(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
):
    """
    Assign the current annotator to the next available batch.
    
    This endpoint is called when an annotator clicks "Start Annotating" button.
    It will:
    1. Check if user already has an active batch
    2. If not, find the next available batch and assign the user
    3. Return batch details and files for the frontend
    
    Returns:
        Dict: Assignment result with batch details and files
    """
    try:
        logger.info(f"START ANNOTATION endpoint called for user: {current_user['sub']}")
        service = AnnotatorBatchAssignmentService()
        
        # Get user details from the database
        user = await user_service.get_user_by_username(current_user["sub"])
        if not user:
            logger.error(f"User not found: {current_user['sub']}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        logger.info(f"Found user: {user.username} (ID: {user.id})")
        
        # Assign annotator to next available batch
        logger.info(f"Calling assign_annotator_to_next_batch for user {user.id}")
        result = await service.assign_annotator_to_next_batch(user.id)
        logger.info(f"Assignment result: {result}")
        
        if not result["success"]:
            # Handle different error cases with appropriate HTTP status codes
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            
            if error_code == "NO_ACTIVE_PROJECT":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="You are not assigned to any active project. Please contact your administrator."
                )
            elif error_code == "NO_ALLOCATION_STRATEGY":
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Project configuration error. Please contact your administrator."
                )
            elif error_code == "USER_NOT_IN_PROJECT":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You are not authorized to work on this project."
                )
            elif error_code == "NO_AVAILABLE_BATCHES":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No available batches found. All work has been completed or allocated."
                )
            elif error_code == "ALL_BATCHES_FULL":
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="All batches are currently full. Please try again later."
                )
            elif error_code == "ALL_BATCHES_COMPLETED":
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="You have completed all available batches. No new work is currently available."
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("error", "An unexpected error occurred.")
                )
        
        return {
            "success": True,
            "message": result["message"],
            "batch": result["batch"],
            "assigned_slot": result.get("assigned_slot"),
            "is_existing_batch": result.get("is_existing_batch", False),
            "project_code": result.get("project_code"),
            "strategy": result.get("strategy"),
            "user_id": user.id,
            "username": user.username
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in start_annotation endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error occurred while assigning the batch."
        )

@router.get("/batch-status", response_model=Dict[str, Any])
async def get_batch_status(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
):
    """
    Get the current batch status for the annotator.
    
    This endpoint is used to determine:
    - Whether the "Start Annotating" button should be enabled/disabled
    - If the user has an active batch
    - Current batch details if any
    
    Returns:
        Dict: User batch status information
    """
    try:
        service = AnnotatorBatchAssignmentService()
        
        # Get user details from the database
        user = await user_service.get_user_by_username(current_user["sub"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get user batch status
        result = await service.get_user_batch_status(user.id)
        
        return {
            "success": result["success"],
            "has_active_batch": result.get("has_active_batch", False),
            "can_start_annotating": result.get("can_start_annotating", False),
            "current_batch": result.get("current_batch"),
            "available_batches_count": result.get("available_batches_count", 0),
            "project_code": result.get("project_code"),
            "error": result.get("error"),
            "user_id": user.id,
            "username": user.username
        }
        
    except Exception as e:
        logger.error(f"Error in get_batch_status endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error occurred while fetching batch status."
        )

@router.get("/current-batch", response_model=Dict[str, Any])
async def get_current_batch(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
):
    """
    Get details of the user's current active batch.
    
    Returns:
        Dict: Current batch details with files
    """
    try:
        service = AnnotatorBatchAssignmentService()
        
        # Get user details from the database
        user = await user_service.get_user_by_username(current_user["sub"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get user's active project
        project_code = await service.get_user_active_project(user.id)
        if not project_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You are not assigned to any active project."
            )
        
        # Get current batch
        current_batch_id = await service.repository.get_user_current_batch(project_code, user.id)
        if current_batch_id is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="You have no active batch assigned."
            )
        
        # Get batch details
        batch_details = await service.repository.get_batch_with_files(project_code, current_batch_id)
        if not batch_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Batch details not found."
            )
        
        return {
            "success": True,
            "batch": batch_details,
            "project_code": project_code,
            "user_id": user.id,
            "username": user.username
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_current_batch endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error occurred while fetching current batch."
        )

@router.get("/ai-suggestions/{file_id}", response_model=Dict[str, Any])
async def get_ai_suggestions(
    file_id: int,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
):
    """
    Get AI suggestions for a specific file.
    
    This endpoint fetches the AI preprocessing results from the file_allocations table
    for projects where AI preprocessing is enabled.
    
    Args:
        file_id: ID of the file to get AI suggestions for
        
    Returns:
        Dict: AI suggestions and processing status
    """
    try:
        service = AnnotatorBatchAssignmentService()
        
        # Get user details from the database
        user = await user_service.get_user_by_username(current_user["sub"])
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get user's active project
        project_code = await service.get_user_active_project(user.id)
        if not project_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You are not assigned to any active project."
            )
        
        # Get AI suggestions for the specific file
        suggestions = await service.repository.get_file_ai_suggestions(project_code, file_id)
        
        if suggestions is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or no AI suggestions available"
            )
        
        return {
            "success": True,
            "file_id": file_id,
            "ai_suggestions": suggestions.get("ai_suggestions"),
            "processing_status": suggestions.get("processing_status", "pending"),
            "project_code": project_code
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching AI suggestions for file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal server error occurred while fetching AI suggestions."
        )
