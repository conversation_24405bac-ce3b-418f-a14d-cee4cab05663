"""
Base cache functionality shared across all cache modules.
"""
from datetime import datetime

def serialize_for_cache(data):
    """
    Serialize data for caching, handling datetime objects

    Args:
        data: Data to serialize

    Returns:
        dict or list: Serialized data
    """
    # Handle None values explicitly
    if data is None:
        return ""  # Convert None to empty string for Redis compatibility

    if isinstance(data, dict):
        return {k: serialize_for_cache(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [serialize_for_cache(item) for item in data]
    elif isinstance(data, datetime):
        return data.isoformat()
    else:
        return data
