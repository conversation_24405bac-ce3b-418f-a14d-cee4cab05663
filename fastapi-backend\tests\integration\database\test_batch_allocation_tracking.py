"""
Integration tests for Advanced Batch Allocation Tracking operations.
Tests complex batch allocation tracking, progress calculation, and assignment status aggregation.

IMPORTANT: These tests align with PRODUCTION ARCHITECTURE (@utils/dynamic_schema_generator.py):
- NO foreign key constraints in allocation tracking tables
- user_id references managed by business logic, not database constraints
- Focus on allocation tracking logic, not constraint enforcement
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, func, and_
from httpx import AsyncClient

from app.post_db.master_models.projects_registry import ProjectsRegistry
from app.post_db.master_models.clients import Clients
from app.post_db.master_models.allocation_strategies import AllocationStrategies, StrategyType
from app.post_db.master_models.user_project_access import UserProjectAccess
from app.post_db.allocation_models.allocation_batches import AllocationBatches, BatchStatus
from app.post_db.allocation_models.project_users import ProjectUsers
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest_asyncio.fixture
async def batch_tracking_setup(test_master_db: AsyncSession):
    """Set up complex project with multiple batches for tracking tests."""
    #  Create client with factory (no hardcoding)
    client = test_factory.projects.create_client()
    test_master_db.add(client)
    await test_master_db.commit()
    await test_master_db.refresh(client)
    
    #  Create allocation strategy with factory
    strategy = test_factory.projects.create_allocation_strategy(
        strategy_type=StrategyType.PARALLEL,
        num_annotators=3,
        requires_verification=True,
        requires_ai_preprocessing=True,
        requires_audit=False,
        allocation_status="active",
        quality_requirements={"min_accuracy": 0.9},
        configuration={"tracking_enabled": True}
    )
    test_master_db.add(strategy)
    await test_master_db.commit()
    await test_master_db.refresh(strategy)
    
    #  Create project with factory
    project = test_factory.projects.create_project(
        client.id, 
        strategy.id,
        project_type="image",
        batch_size=10,
        project_status="active",
        priority_level=1
    )
    test_master_db.add(project)
    await test_master_db.commit()
    await test_master_db.refresh(project)
    
    #  Create user project access entries with dynamic IDs
    base_user_id = test_factory.users.unique_counter
    user_accesses = [
        UserProjectAccess(project_id=project.id, user_id=base_user_id + 1, database_name=project.database_name, project_role="annotator"),
        UserProjectAccess(project_id=project.id, user_id=base_user_id + 2, database_name=project.database_name, project_role="annotator"),
        UserProjectAccess(project_id=project.id, user_id=base_user_id + 3, database_name=project.database_name, project_role="annotator"),
        UserProjectAccess(project_id=project.id, user_id=base_user_id + 4, database_name=project.database_name, project_role="verifier"),
        UserProjectAccess(project_id=project.id, user_id=base_user_id + 5, database_name=project.database_name, project_role="admin")
    ]
    
    for access in user_accesses:
        test_master_db.add(access)
    
    await test_master_db.commit()
    
    return {
        "client": client,
        "strategy": strategy,
        "project": project,
        "user_accesses": user_accesses
    }


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.batch_allocation # Feature marker - Batch allocation
@pytest.mark.smoke            # Suite marker - Core functionality
@pytest.mark.critical         # Priority marker - P0
@pytest.mark.stable           # Stability marker - Reliable
class TestBatchProgressCalculation:
    """SMOKE TEST SUITE: Critical batch progress calculation operations."""
    
    @pytest.mark.asyncio
    async def test_batch_progress_single_annotator(self, test_db: AsyncSession):
        """Test progress calculation for single annotator batches."""
        # Create batches with different progress states
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="PROGRESS_001",
                total_files=10,
                file_list=[f"file{i}.jpg" for i in range(1, 11)],
                annotation_count=1,
                assignment_count=1,
                completion_count=10,  # Completed
                batch_status=BatchStatus.COMPLETED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="PROGRESS_002",
                total_files=15,
                file_list=[f"file{i}.jpg" for i in range(11, 26)],
                annotation_count=1,
                assignment_count=1,
                completion_count=7,  # Partially completed
                batch_status=BatchStatus.ALLOCATED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="PROGRESS_003",
                total_files=8,
                file_list=[f"file{i}.jpg" for i in range(26, 34)],
                annotation_count=1,
                assignment_count=1,
                completion_count=0,  # Not started
                batch_status=BatchStatus.ALLOCATED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="PROGRESS_004",
                total_files=12,
                file_list=[f"file{i}.jpg" for i in range(34, 46)],
                annotation_count=1,
                assignment_count=0,
                completion_count=0,  # Not assigned
                batch_status=BatchStatus.CREATED
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Calculate overall progress
        stmt = select(
            func.sum(AllocationBatches.total_files).label('total_files'),
            func.sum(AllocationBatches.completion_count).label('completed_files'),
            func.count(AllocationBatches.id).label('total_batches')
        )
        result = await test_db.execute(stmt)
        progress_data = result.one()
        
        total_files = progress_data.total_files
        completed_files = progress_data.completed_files
        total_batches = progress_data.total_batches
        
        assert total_files == 45  # 10 + 15 + 8 + 12
        assert completed_files == 17  # 10 + 7 + 0 + 0
        assert total_batches == 4
        
        completion_percentage = (completed_files / total_files) * 100
        assert abs(completion_percentage - 37.78) < 0.1  # ~37.78%
    
    @pytest.mark.asyncio
    async def test_batch_progress_multi_annotator(self, test_db: AsyncSession):
        """Test progress calculation for multi-annotator batches."""
        # Create multi-annotator batches
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="MULTI_PROGRESS_001",
                total_files=5,
                file_list=[f"multi{i}.jpg" for i in range(1, 6)],
                annotation_count=3,  # Requires 3 annotations per file
                assignment_count=3,  # All 3 annotators assigned
                completion_count=2,  # 2 files fully completed (all 3 annotations)
                batch_status=BatchStatus.ALLOCATED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="MULTI_PROGRESS_002",
                total_files=8,
                file_list=[f"multi{i}.jpg" for i in range(6, 14)],
                annotation_count=3,
                assignment_count=2,  # Only 2 annotators assigned
                completion_count=0,  # No files completed yet
                batch_status=BatchStatus.ALLOCATED
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Calculate multi-annotator progress - filter to only our test batches
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.like("MULTI_PROGRESS_%")
        )
        result = await test_db.execute(stmt)
        all_batches = result.scalars().all()
        
        total_annotations_required = 0
        completed_annotations = 0
        
        for batch in all_batches:
            total_annotations_required += batch.total_files * batch.annotation_count
            completed_annotations += batch.completion_count * batch.annotation_count
        
        assert total_annotations_required == 39  # (5 * 3) + (8 * 3)
        assert completed_annotations == 6  # (2 * 3) + (0 * 3)
        
        annotation_completion_percentage = (completed_annotations / total_annotations_required) * 100
        assert abs(annotation_completion_percentage - 15.38) < 0.1  # ~15.38%
    
    @pytest.mark.asyncio
    async def test_batch_status_distribution(self, test_db: AsyncSession):
        """Test calculation of batch status distribution."""
        # Create batches with different statuses
        batches = [
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_001", total_files=5, batch_status=BatchStatus.CREATED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_002", total_files=8, batch_status=BatchStatus.CREATED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_003", total_files=6, batch_status=BatchStatus.ALLOCATED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_004", total_files=10, batch_status=BatchStatus.ALLOCATED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_005", total_files=7, batch_status=BatchStatus.ALLOCATED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_006", total_files=9, batch_status=BatchStatus.COMPLETED),
            test_factory.batches.create_allocation_batch(batch_identifier="STATUS_007", total_files=4, batch_status=BatchStatus.COMPLETED)
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Query status distribution - filter to only our test batches
        stmt = select(
            AllocationBatches.batch_status,
            func.count(AllocationBatches.id).label('count'),
            func.sum(AllocationBatches.total_files).label('files')
        ).where(
            AllocationBatches.batch_identifier.like("STATUS_%")
        ).group_by(AllocationBatches.batch_status)
        
        result = await test_db.execute(stmt)
        status_distribution = result.all()
        
        status_dict = {row.batch_status: {"count": row.count, "files": row.files} for row in status_distribution}
        
        assert status_dict[BatchStatus.CREATED]["count"] == 2
        assert status_dict[BatchStatus.CREATED]["files"] == 13  # 5 + 8
        
        assert status_dict[BatchStatus.ALLOCATED]["count"] == 3
        assert status_dict[BatchStatus.ALLOCATED]["files"] == 23  # 6 + 10 + 7
        
        assert status_dict[BatchStatus.COMPLETED]["count"] == 2
        assert status_dict[BatchStatus.COMPLETED]["files"] == 13  # 9 + 4


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.batch_allocation # Feature marker - Batch allocation
@pytest.mark.assignment       # Feature marker - Assignment operations
@pytest.mark.regression       # Suite marker - Aggregation testing
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestAssignmentStatusAggregation:
    """REGRESSION TEST SUITE: Assignment status aggregation across batches."""
    
    @pytest.mark.asyncio
    async def test_annotator_assignment_aggregation(self, test_db: AsyncSession):
        """Test aggregation of annotator assignments."""
        # Create batches with different assignment patterns
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="ASSIGN_AGG_001",
                total_files=10,
                annotation_count=1,
                assignment_count=1
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="ASSIGN_AGG_002",
                total_files=15,
                annotation_count=3,
                assignment_count=3
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="ASSIGN_AGG_003",
                total_files=8,
                annotation_count=2,
                assignment_count=2
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="ASSIGN_AGG_004",
                total_files=12,
                annotation_count=1,
                assignment_count=0  # No assignments yet
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Test assignment capacity analysis
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.like("ASSIGN_AGG_%")
        )
        result = await test_db.execute(stmt)
        test_batches = result.scalars().all()
        
        assignment_stats = {
            'total_batches': len(test_batches),
            'assigned_batches': len([b for b in test_batches if b.assignment_count > 0]),
            'unassigned_batches': len([b for b in test_batches if b.assignment_count == 0]),
            'total_assignments': sum(b.assignment_count for b in test_batches),
            'total_files': sum(b.total_files for b in test_batches),
            'avg_assignment_per_batch': sum(b.assignment_count for b in test_batches) / len(test_batches)
        }
        
        # Verify aggregation metrics
        assert assignment_stats['total_batches'] == 4
        assert assignment_stats['assigned_batches'] == 3  # First 3 batches have assignments
        assert assignment_stats['unassigned_batches'] == 1  # Last batch has no assignments
        assert assignment_stats['total_assignments'] == 6  # 1 + 3 + 2 + 0
        assert assignment_stats['total_files'] == 45  # 10 + 15 + 8 + 12
        assert assignment_stats['avg_assignment_per_batch'] == 1.5  # 6 / 4
    
    @pytest.mark.asyncio
    async def test_verifier_assignment_tracking(self, test_db: AsyncSession):
        """Test tracking of verifier assignments."""
        # Create batches ready for verification
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="VERIF_TRACK_001",
                total_files=10,
                annotation_count=1,
                assignment_count=1,
                completion_count=10,  # Completed, ready for verification
                batch_status=BatchStatus.COMPLETED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="VERIF_TRACK_002",
                total_files=8,
                annotation_count=1,
                assignment_count=1,
                completion_count=8,  # Completed, ready for verification
                batch_status=BatchStatus.COMPLETED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="VERIF_TRACK_003",
                total_files=12,
                annotation_count=1,
                assignment_count=1,
                completion_count=5,  # Not completed yet
                batch_status=BatchStatus.ALLOCATED
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="VERIF_TRACK_004",
                total_files=6,
                annotation_count=1,
                assignment_count=1,
                completion_count=6,  # Completed
                batch_status=BatchStatus.COMPLETED
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Aggregate verification status - test completion tracking
        stmt = select(AllocationBatches).where(
            and_(
                AllocationBatches.completion_count == AllocationBatches.total_files,
                AllocationBatches.batch_identifier.like("VERIF_TRACK_%")
            )
        )
        result = await test_db.execute(stmt)
        completed_batches = result.scalars().all()
        
        verification_status = {
            'completed_batches': len(completed_batches),
            'ready_for_verification': len([b for b in completed_batches if b.batch_status == BatchStatus.COMPLETED])
        }
        
        assert verification_status['completed_batches'] == 3  # 3 fully completed batches
        assert verification_status['ready_for_verification'] == 3  # All completed batches are ready for verification
        
        # Test batch completion metrics
        total_files_completed = sum(batch.total_files for batch in completed_batches)
        assert total_files_completed == 24  # 10 + 8 + 6
    
    @pytest.mark.asyncio
    async def test_assignment_capacity_analysis(self, test_db: AsyncSession):
        """Test analysis of assignment capacity and availability."""
        # Create batches with capacity constraints
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="CAPACITY_001",
                total_files=10,
                annotation_count=2,  # Requires 2 annotators
                assignment_count=2  # Fully assigned
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="CAPACITY_002", 
                total_files=8,
                annotation_count=3,  # Requires 3 annotators
                assignment_count=1  # Under-assigned
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="CAPACITY_003",
                total_files=15,
                annotation_count=1,  # Requires 1 annotator
                assignment_count=1  # Fully assigned
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="CAPACITY_004",
                total_files=12,
                annotation_count=2,  # Requires 2 annotators
                assignment_count=0  # Not assigned
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Analyze capacity - filter to only our test batches
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.like("CAPACITY_%")
        )
        result = await test_db.execute(stmt)
        all_batches = result.scalars().all()
        
        capacity_analysis = {
            'fully_assigned': 0,
            'under_assigned': 0,
            'not_assigned': 0,
            'over_capacity': 0
        }
        
        for batch in all_batches:
            required = batch.annotation_count
            assigned = batch.assignment_count
            
            if assigned == 0:
                capacity_analysis['not_assigned'] += 1
            elif assigned < required:
                capacity_analysis['under_assigned'] += 1
            elif assigned == required:
                capacity_analysis['fully_assigned'] += 1
            else:
                capacity_analysis['over_capacity'] += 1
        
        assert capacity_analysis['fully_assigned'] == 2  # CAPACITY_001, CAPACITY_003
        assert capacity_analysis['under_assigned'] == 1   # CAPACITY_002
        assert capacity_analysis['not_assigned'] == 1     # CAPACITY_004
        assert capacity_analysis['over_capacity'] == 0


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.batch_allocation # Feature marker - Batch allocation
@pytest.mark.regression       # Suite marker - Complex scenarios
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestMultiBatchAllocationScenarios:
    """REGRESSION TEST SUITE: Complex multi-batch allocation scenarios."""
    
    @pytest.mark.asyncio
    async def test_project_wide_allocation_summary(self, test_db: AsyncSession):
        """Test project-wide allocation summary across all batches."""
        # Create comprehensive batch set
        batches = [
            # Completed batches
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_001", total_files=10, annotation_count=1, 
                            assignment_count=1, completion_count=10, batch_status=BatchStatus.COMPLETED),
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_002", total_files=8, annotation_count=1,
                            assignment_count=1, completion_count=8, batch_status=BatchStatus.COMPLETED),
            
            # In progress batches
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_003", total_files=15, annotation_count=2,
                            assignment_count=2, completion_count=7, batch_status=BatchStatus.ALLOCATED),
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_004", total_files=12, annotation_count=1,
                            assignment_count=1, completion_count=3, batch_status=BatchStatus.ALLOCATED),
            
            # Assigned but not started
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_005", total_files=6, annotation_count=1,
                            assignment_count=1, completion_count=0, batch_status=BatchStatus.ALLOCATED),
            
            # Created but not assigned
            test_factory.batches.create_allocation_batch(batch_identifier="SUMMARY_006", total_files=9, annotation_count=3,
                            assignment_count=0, completion_count=0, batch_status=BatchStatus.CREATED)
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Calculate project-wide summary - filter to only our test batches
        stmt = select(
            func.count(AllocationBatches.id).label('total_batches'),
            func.sum(AllocationBatches.total_files).label('total_files'),
            func.sum(AllocationBatches.completion_count).label('completed_files'),
            func.sum(AllocationBatches.assignment_count).label('total_assignments')
        ).where(
            AllocationBatches.batch_identifier.like("SUMMARY_%")
        )
        result = await test_db.execute(stmt)
        summary = result.one()
        
        # Status-specific counts - filter to only our test batches
        status_counts = {}
        for status in [BatchStatus.CREATED, BatchStatus.ALLOCATED, BatchStatus.COMPLETED]:
            stmt = select(func.count(AllocationBatches.id)).where(
                and_(
                    AllocationBatches.batch_status == status,
                    AllocationBatches.batch_identifier.like("SUMMARY_%")
                )
            )
            result = await test_db.execute(stmt)
            status_counts[status] = result.scalar()
        
        # Assertions
        assert summary.total_batches == 6
        assert summary.total_files == 60  # 10+8+15+12+6+9
        assert summary.completed_files == 28  # 10+8+7+3+0+0
        assert summary.total_assignments == 6  # 1+1+2+1+1+0
        
        assert status_counts[BatchStatus.COMPLETED] == 2
        assert status_counts[BatchStatus.ALLOCATED] == 3  # SUMMARY_003, SUMMARY_004, SUMMARY_005
        assert status_counts[BatchStatus.CREATED] == 1
        
        # Calculate percentages
        completion_percentage = (summary.completed_files / summary.total_files) * 100
        assert abs(completion_percentage - 46.67) < 0.1  # ~46.67%
    
    @pytest.mark.asyncio
    async def test_temporal_allocation_patterns(self, test_db: AsyncSession):
        """Test temporal allocation patterns and trends."""
        from datetime import datetime, timedelta
        
        # Create batches with different creation times
        base_time = datetime.utcnow()
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="TEMPORAL_001",
                total_files=10,
                annotation_count=1,
                assignment_count=1,
                completion_count=10,
                created_at=base_time - timedelta(days=5)
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="TEMPORAL_002",
                total_files=8,
                annotation_count=1,
                assignment_count=1,
                completion_count=8,
                created_at=base_time - timedelta(days=4)
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="TEMPORAL_003",
                total_files=12,
                annotation_count=1,
                assignment_count=1,
                completion_count=6,
                created_at=base_time - timedelta(days=3)
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="TEMPORAL_004",
                total_files=15,
                annotation_count=1,
                assignment_count=1,
                completion_count=0,
                created_at=base_time - timedelta(days=1)
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="TEMPORAL_005",
                total_files=6,
                annotation_count=1,
                assignment_count=0,
                completion_count=0,
                created_at=base_time
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Analyze temporal patterns - filter to only our test batches
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.like("TEMPORAL_%")
        ).order_by(AllocationBatches.created_at)
        result = await test_db.execute(stmt)
        time_ordered_batches = result.scalars().all()
        
        # Calculate completion rates over time
        cumulative_files = 0
        cumulative_completed = 0
        completion_rates = []
        
        for batch in time_ordered_batches:
            cumulative_files += batch.total_files
            cumulative_completed += batch.completion_count
            
            if cumulative_files > 0:
                rate = (cumulative_completed / cumulative_files) * 100
                completion_rates.append(rate)
        
        # Verify temporal progression
        assert len(completion_rates) == 5
        assert completion_rates[0] == 100.0  # First batch fully completed
        assert completion_rates[1] == 100.0  # First two batches completed
        assert completion_rates[2] == 80.0   # (18/30) * 100 = 60%
        # Later rates should show declining completion percentage as new batches are added
    
    @pytest.mark.asyncio
    async def test_cross_batch_dependency_tracking(self, test_db: AsyncSession):
        """Test tracking dependencies between batches."""
        # Create batches with dependencies (sequential processing)
        batches = [
            test_factory.batches.create_allocation_batch(
                batch_identifier="DEP_001",
                total_files=5,
                annotation_count=1,
                assignment_count=1,
                completion_count=5,
                batch_status=BatchStatus.COMPLETED,
                custom_batch_config={"dependency": None, "sequence": 1}  # First in sequence
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="DEP_002",
                total_files=8,
                annotation_count=1,
                assignment_count=1,
                completion_count=8,
                batch_status=BatchStatus.COMPLETED,
                custom_batch_config={"dependency": "DEP_001", "sequence": 2}  # Depends on DEP_001
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="DEP_003",
                total_files=10,
                annotation_count=1,
                assignment_count=1,
                completion_count=4,
                batch_status=BatchStatus.ALLOCATED,
                custom_batch_config={"dependency": "DEP_002", "sequence": 3}  # Depends on DEP_002
            ),
            test_factory.batches.create_allocation_batch(
                batch_identifier="DEP_004",
                total_files=6,
                annotation_count=1,
                assignment_count=0,
                completion_count=0,
                batch_status=BatchStatus.CREATED,
                custom_batch_config={"dependency": "DEP_003", "sequence": 4}  # Depends on DEP_003
            )
        ]
        
        for batch in batches:
            test_db.add(batch)
        
        await test_db.commit()
        
        # Analyze dependency chain - use batch_identifier ordering instead
        stmt = select(AllocationBatches).where(
            AllocationBatches.batch_identifier.like("DEP_%")
        ).order_by(AllocationBatches.batch_identifier)
        result = await test_db.execute(stmt)
        sequence_ordered_batches = result.scalars().all()
        
        # Verify dependency progression
        dependency_status = []
        for batch in sequence_ordered_batches:
            is_ready = True
            dependency = batch.custom_batch_config.get("dependency") if batch.custom_batch_config else None
            
            if dependency:
                # Check if dependency is completed
                dep_stmt = select(AllocationBatches).where(AllocationBatches.batch_identifier == dependency)
                dep_result = await test_db.execute(dep_stmt)
                dep_batch = dep_result.scalar_one_or_none()
                
                if dep_batch:
                    is_ready = (dep_batch.completion_count == dep_batch.total_files)
            
            dependency_status.append({
                "batch": batch.batch_identifier,
                "ready": is_ready,
                "dependency": dependency
            })
        
        # Verify dependency chain logic
        assert dependency_status[0]["ready"] is True   # DEP_001 has no dependency
        assert dependency_status[1]["ready"] is True   # DEP_002 dependency (DEP_001) is complete
        assert dependency_status[2]["ready"] is True   # DEP_003 dependency (DEP_002) is complete
        assert dependency_status[3]["ready"] is False  # DEP_004 dependency (DEP_003) is not complete


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.batch_allocation # Feature marker - Batch allocation
@pytest.mark.api              # Feature marker - API operations
@pytest.mark.regression       # Suite marker - API testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
class TestBatchAllocationAPI:
    """REGRESSION TEST SUITE: Batch allocation tracking via API endpoints."""
    
    @pytest.mark.asyncio
    async def test_get_batch_allocations_api(self, authenticated_client: AsyncClient, test_master_db: AsyncSession):
        """Test getting batch allocation information via API."""
        response = await authenticated_client.get(test_factory.config.get_endpoint("/projects/1/batch-allocations"))
        
        # Handle different possible response codes based on implementation
        assert response.status_code in [200, 404, 403, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Expected structure based on batch_allocation_routes.py
            assert "batches" in result or "progress" in result
            
            if "progress" in result:
                progress = result["progress"]
                assert "total_batches" in progress
                assert "annotator_phase" in progress
                assert "verifier_phase" in progress
                
                # Verify phase structure
                if "annotator_phase" in progress:
                    annotator_phase = progress["annotator_phase"]
                    assert "completed_batches" in annotator_phase
                    assert "in_progress_batches" in annotator_phase
                    assert "pending_batches" in annotator_phase
        
        elif response.status_code == 404:
            result = response.json()
            assert "not found" in result.get("detail", "").lower()
    
    @pytest.mark.asyncio
    async def test_project_allocation_summary_api(self, authenticated_client: AsyncClient):
        """Test project allocation summary API endpoint structure."""
        # Test with a dummy project ID to verify the endpoint exists and has correct structure
        # We expect either:
        # - 200: Success with proper project data
        # - 404: Project not found (expected for non-existent ID)  
        # - 403: Forbidden (permissions issue)
        # - 500: Server error
        
        response = await authenticated_client.get(test_factory.config.get_endpoint("/projects/999/summary") ) # Non-existent project ID
        
        # Should NOT return 500 (which would indicate a broken endpoint)
        # Should return 404 (project not found) or 403 (forbidden) - both indicate endpoint exists
        assert response.status_code in [200, 403, 404]
        
        if response.status_code == 200:
            result = response.json()
            
            # Should be a single project summary object (not a list)
            assert isinstance(result, dict)
            
            # Verify expected fields from ProjectAssignmentSummary schema  
            assert "project_id" in result
            assert "project_code" in result  
            assert "project_name" in result
            assert "project_status" in result
            assert "allocation_strategy" in result
            assert "annotators" in result
            assert "verifiers" in result
            assert "batch_info" in result
            
        elif response.status_code == 404:
            result = response.json()
            assert "detail" in result
            # This is expected for a non-existent project ID
    
    @pytest.mark.asyncio
    async def test_batch_progress_tracking_api(self, authenticated_client: AsyncClient):
        """Test batch progress tracking API endpoint."""
        response = await authenticated_client.get(test_factory.config.get_endpoint("/projects/PROJECT_CODE_001/progress"))
        
        assert response.status_code in [200, 404, 403, 500]
        
        if response.status_code == 200:
            result = response.json()
            
            # Should contain progress metrics
            assert "completion_percentage" in result or "progress" in result
            assert "total_files" in result or "files" in result
            assert "completed_files" in result or "completed" in result
        
        elif response.status_code == 404:
            result = response.json()
            assert "not found" in result.get("detail", "").lower()


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.batch_allocation # Feature marker - Batch allocation
@pytest.mark.performance      # Suite marker - Performance testing
@pytest.mark.medium           # Priority marker - P2
@pytest.mark.stable           # Stability marker - Reliable
@pytest.mark.slow             # Execution marker - Performance tests take time
class TestBatchAllocationPerformance:
    """PERFORMANCE TEST SUITE: Batch allocation tracking performance."""
    
    @pytest.mark.asyncio
    async def test_large_scale_batch_aggregation(self, test_db: AsyncSession):
        """Test aggregation performance with large number of batches."""
        # Create a large number of batches for performance testing
        batches = []
        for i in range(100):  # 100 batches
            batch = test_factory.batches.create_allocation_batch(
                batch_identifier=f"PERF_BATCH_{i:03d}",
                total_files=10 + (i % 20),  # Varying file counts
                annotation_count=1 + (i % 3),  # Varying annotation requirements
                assignment_count=(i % 4),  # Varying assignment states
                completion_count=(i % 15),  # Varying completion states
                batch_status=list(BatchStatus)[i % len(list(BatchStatus))]  # Rotating statuses
            )
            batches.append(batch)
        
        # Batch insert for performance
        test_db.add_all(batches)
        await test_db.commit()
        
        # Test aggregation queries
        # 1. Overall summary - count only our test batches
        stmt = select(
            func.count(AllocationBatches.id),
            func.sum(AllocationBatches.total_files),
            func.sum(AllocationBatches.completion_count),
            func.avg(AllocationBatches.assignment_count)
        ).where(AllocationBatches.batch_identifier.like("PERF_BATCH_%"))
        result = await test_db.execute(stmt)
        summary = result.one()
        
        assert summary[0] == 100  # batch count (our test batches only)
        assert summary[1] > 0     # total files
        assert summary[2] >= 0    # completed files
        assert summary[3] >= 0    # avg assignments
        
        # 2. Status-based aggregation
        stmt = select(
            AllocationBatches.batch_status,
            func.count(AllocationBatches.id),
            func.sum(AllocationBatches.total_files)
        ).group_by(AllocationBatches.batch_status)
        
        result = await test_db.execute(stmt)
        status_aggregation = result.all()
        
        assert len(status_aggregation) > 0
        
        # 3. Completion rate calculation
        stmt = select(AllocationBatches).where(AllocationBatches.total_files > 0)
        result = await test_db.execute(stmt)
        all_batches = result.scalars().all()
        
        total_completion_rate = sum(
            (batch.completion_count / batch.total_files) * 100 
            for batch in all_batches 
            if batch.total_files > 0
        ) / len(all_batches)
        
        assert 0 <= total_completion_rate <= 100
    
    @pytest.mark.asyncio
    async def test_concurrent_batch_updates(self, test_db: AsyncSession):
        """Test handling of concurrent batch status updates."""
        # Create test batch
        batch = test_factory.batches.create_allocation_batch(
            batch_identifier="CONCURRENT_001",
            total_files=10,
            annotation_count=1,
            assignment_count=0,
            completion_count=0,
            batch_status=BatchStatus.CREATED
        )
        
        test_db.add(batch)
        await test_db.commit()
        await test_db.refresh(batch)
        
        # Simulate concurrent updates (in real scenario, this would be multiple transactions)
        # Update 1: Assignment
        batch.assignment_count = 1
        batch.batch_status = BatchStatus.ALLOCATED
        await test_db.commit()
        
        # Update 2: Progress
        batch.completion_count = 5
        batch.batch_status = BatchStatus.ALLOCATED
        await test_db.commit()
        
        # Update 3: Completion
        batch.completion_count = 10
        batch.batch_status = BatchStatus.COMPLETED
        await test_db.commit()
        
        # Verify final state
        await test_db.refresh(batch)
        assert batch.assignment_count == 1
        assert batch.completion_count == 10
        assert batch.batch_status == BatchStatus.COMPLETED
        
        # Verify consistency
        assert batch.completion_count <= batch.total_files
        assert batch.assignment_count <= batch.annotation_count
