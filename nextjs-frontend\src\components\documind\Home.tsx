"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import LoginModal from "@/components/auth/LoginModal";
import { FaCloudDownloadAlt, FaChartLine, FaFileAlt } from "react-icons/fa";

export default function Home() {
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const handleOpenLogin = () => setIsLoginOpen(true);

  useEffect(() => {
    const onScroll = () => setScrolled(window.scrollY > 50);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  // Smooth scrolling for anchor links
  useEffect(() => {
    const anchors = Array.from(
      document.querySelectorAll<HTMLAnchorElement>('a[href^="#"]')
    );
    const handleClick = (e: Event) => {
      e.preventDefault();
      const targetId = (e.currentTarget as HTMLAnchorElement).getAttribute(
        "href"
      )!;
      if (targetId === "#") return;
      const el = document.querySelector<HTMLElement>(targetId);
      if (el) {
        window.scrollTo({ top: el.offsetTop - 100, behavior: "smooth" });
        setMenuOpen(false);
      }
    };
    anchors.forEach((a) => a.addEventListener("click", handleClick));
    return () =>
      anchors.forEach((a) => a.removeEventListener("click", handleClick));
  }, []);

  // Tagline typing animation
  useEffect(() => {
    const tagline = document.querySelector<HTMLElement>(".tagline-typing");
    if (!tagline) return;
    const options = ["AI PROCESSING", "HUMAN SUPERVISION"];
    let idx = 0,
      ch = 0,
      deleting = false;
    let text = "";
    const type = () => {
      const full = options[idx];
      if (!deleting) {
        if (ch < full.length) {
          text += full.charAt(ch++);
          tagline.textContent = text;
          setTimeout(type, 100);
        } else {
          deleting = true;
          setTimeout(type, 2000);
        }
      } else {
        if (ch > 0) {
          text = full.substring(0, --ch);
          tagline.textContent = text;
          setTimeout(type, 50);
        } else {
          deleting = false;
          idx = (idx + 1) % options.length;
          setTimeout(type, 500);
        }
      }
    };
    setTimeout(type, 500);
  }, []);

  // Rotating messages under hero
  useEffect(() => {
    const elem = document.querySelector<HTMLElement>(".typing-message");
    if (!elem) return;
    const msgs = [
      "Seamlessly fetch data from anywhere, store everywhere.",
      "Gather every source's data, store in any database.",
      "Acquire data from any source, serve any destination.",
      "Connect any source and power every database.",
    ];
    let mid = 0;
    const rotate = () => {
      elem.style.opacity = "0";
      setTimeout(() => {
        mid = (mid + 1) % msgs.length;
        elem.textContent = msgs[mid];
        elem.style.opacity = "1";
        setTimeout(rotate, 5000);
      }, 100);
    };
    setTimeout(rotate, 4000);
  }, []);

  return (
    <>
      <header
        className={`fixed top-0 left-0 w-full z-50 transition-colors duration-300 ${
          scrolled ? "bg-white/80 backdrop-blur-md shadow-md" : "bg-white/90"
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <Link
            href="/"
            className="flex items-center no-underline hover:no-underline"
          >
            <Image
              src="/img/PVlogo-1024x780.png"
              alt="Documind-o Logo"
              width={60}
              height={60}
              className="object-contain"
            />
            <div className="ml-3">
              <span className="block text-lg font-semibold text-gray-800">
                End to End Data Solutions
              </span>
              <span className="block text-sm text-secondary">
                Human-AI Collaboration
              </span>
              <span className="inline-block text-xs font-semibold bg-gradient-to-r from-teal-300 to-blue-600 text-black px-2 py-0.5 rounded-full">
                Beta Version
              </span>
            </div>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <div className="flex items-center space-x-5 bg-white/80 backdrop-blur-md px-4 py-2 rounded-full shadow">
              <a
                href="#features"
                className="text-gray-700 hover:text-[#0D47A1] no-underline hover:no-underline"
              >
                Features
              </a>
              <a
                href="#how-it-works"
                className="text-gray-700 hover:text-[#0D47A1] no-underline hover:no-underline"
              >
                How It Works
              </a>
            </div>
            <Link
              href="/"
              className="bg-[#0D47A1] text-white px-4 py-2 rounded-full hover:bg-[#1159B8] no-underline hover:no-underline"
            >
              Back to Home
            </Link>
          </nav>
          <button
            className="md:hidden text-gray-700"
            onClick={() => setMenuOpen(!menuOpen)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </button>
        </div>
        {menuOpen && (
          <div className="md:hidden bg-white/90 backdrop-blur-md p-4 space-y-4">
            <a
              href="#features"
              className="block text-gray-700 no-underline hover:no-underline"
            >
              Features
            </a>
            <a
              href="#how-it-works"
              className="block text-gray-700 no-underline hover:no-underline"
            >
              How It Works
            </a>
            <Link
              href="/"
              className="block bg-[#0D47A1] text-white px-4 py-2 rounded no-underline hover:no-underline"
            >
              Back to Home
            </Link>
          </div>
        )}
      </header>

      <main>
        <section className="relative bg-gradient-to-br from-blue-50 to-blue-100 pt-40 pb-24">
          {/* Floating shapes */}
          <div className="absolute -top-10 -left-10 w-48 h-48 bg-primary rounded-full opacity-20 animate-float" />
          <div className="absolute top-20 right-10 w-32 h-32 bg-secondary rounded-full opacity-20 animate-float" />

          <div className="relative z-10 max-w-4xl mx-auto text-center px-4">
            <h1 className="text-4xl md:text-6xl font-bold text-secondary leading-tight">
              Documind-o
              <br />
              <span className="text-[#0D47A1]">HAI</span> Agent
            </h1>
            <div className="mt-4 text-2xl md:text-3xl font-medium text-secondary">
              <span className="tagline-typing"></span>
              <span className="text-[#0D47A1]">|</span>
            </div>
            <p className="mt-4 text-lg md:text-xl text-secondary">
              From Source to Solution → Smart Data Validation and Delivery
            </p>
            <div className="mt-8 flex justify-center space-x-4">
              <button
                onClick={handleOpenLogin}
                className="bg-[#0D47A1] text-white px-6 py-3 rounded-full hover:bg-[#1159B8] transition"
              >
                Data Fetching
              </button>
              <button
                onClick={handleOpenLogin}
                className="bg-[#0D47A1] text-white px-6 py-3 rounded-full hover:bg-[#1159B8] transition"
              >
                Data Processing
              </button>
            </div>
          </div>
        </section>

        <section id="features" className="py-20 bg-white">
          <div className="max-w-6xl mx-auto px-4 text-center">
            <h2 className="text-3xl font-semibold text-secondary mb-8 relative inline-block after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition">
                <div className="mb-4 flex justify-center">
                  <FaCloudDownloadAlt className="text-[#0D47A1] text-4xl" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Multiple Data Sources
                </h3>
                <p className="text-text-secondary">
                  Seamless integration of platforms like Telegram, Google Drive,
                  and more.
                </p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition">
                <div className="mb-4 flex justify-center">
                  <FaChartLine className="text-[#0D47A1] text-4xl" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Detailed Analytics
                </h3>
                <p className="text-text-secondary">
                  Gain insights from your documents with comprehensive analytics
                  and reporting tools.
                </p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow hover:shadow-lg transition">
                <div className="mb-4 flex justify-center">
                  <FaFileAlt className="text-[#0D47A1] text-4xl" />
                </div>
                <h3 className="text-xl font-semibold mb-2">
                  Multi-format Support
                </h3>
                <p className="text-text-secondary">
                  Process diverse documents with intelligent format detection.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section
          id="how-it-works"
          className="relative bg-gradient-to-br from-blue-50 to-blue-100 py-20"
        >
          {/* Decorative circles */}
          <div className="absolute -top-12 -right-12 w-52 h-52 bg-green-100 rounded-full opacity-30"></div>
          <div className="absolute -bottom-16 right-1/4 w-72 h-72 bg-yellow-100 rounded-full opacity-30"></div>

          <div className="relative z-10 max-w-4xl mx-auto px-4 text-center">
            <h2 className="text-3xl font-semibold text-secondary mb-8 relative inline-block after:content-[''] after:absolute after:left-1/2 after:-translate-x-1/2 after:transform after:-bottom-2 after:w-16 after:h-1 after:bg-gradient-to-r after:from-primary after:to-secondary after:rounded">
              How It Works
            </h2>

            <div className="mt-12 space-y-12 text-left">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 text-[#0D47A1] text-2xl font-bold">
                  1
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary mb-2">
                    Fetch Documents
                  </h3>
                  <p className="text-text-secondary">
                    Connect and extract data from various platforms including
                    Telegram, Google Drive, and more with seamless integration.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 text-[#0D47A1] text-2xl font-bold">
                  2
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary mb-2">
                    Process in Secure Environment
                  </h3>
                  <p className="text-text-secondary">
                    Select processing power (Standard, Enhanced, or Premium)
                    based on document quality and complexity.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 text-[#0D47A1] text-2xl font-bold">
                  3
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-secondary mb-2">
                    Review & Export
                  </h3>
                  <p className="text-text-secondary">
                    Inspect processed documents, review extracted data, make
                    corrections, and export it to your desired database.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <section
          id="footer"
          className="relative overflow-hidden bg-gradient-to-br from-[#0D47A1] to-[#1159B8] py-12"
        >
          <div className="relative z-10 max-w-7xl mx-auto px-4 text-center text-sm text-white">
            Documind-o • HAI Agent • All Rights Reserved • © 2025
          </div>
        </section>
      </main>

      {/* Login Modal trigger */}
      <LoginModal
        isOpen={isLoginOpen}
        onClose={() => setIsLoginOpen(false)}
        onRegisterClick={() => setIsLoginOpen(false)}
      />
    </>
  );
}
