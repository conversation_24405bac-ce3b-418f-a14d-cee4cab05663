"use client";

import { useRef } from "react";
import Image from "next/image";
import { MediaViewerProps } from "./types";
import { FaCheck } from "react-icons/fa";

interface ImageViewerProps extends Omit<MediaViewerProps, 'mediaType'> {
  isLabeled?: boolean;
}

export default function ImageViewer({
  mediaUrl,
  zoomLevel = 100,
  onLoad,
  onError,
  isLabeled = false
}: ImageViewerProps) {
  const imageRef = useRef<HTMLImageElement>(null);

  const handleLoad = () => {
    onLoad?.();
  };

  const handleError = () => {
    onError?.("Failed to load image");
  };

  return (
    <div className="h-full flex justify-center items-center overflow-hidden relative">
      {mediaUrl ? (
        <Image
          ref={imageRef}
          id="currentImage"
          src={mediaUrl}
          alt="Image to annotate"
          className="max-h-full max-w-full transition-transform duration-200 ease-in-out"
          style={{
            transform: `scale(${zoomLevel / 100})`,
            width: "auto",
            height: "auto",
          }}
          width={600}
          height={400}
          unoptimized
          onLoad={handleLoad}
          onError={handleError}
        />
      ) : (
        <div className="flex items-center justify-center h-full">
          <div className="text-gray-400">Loading image...</div>
        </div>
      )}
      
      {isLabeled && (
        <span className="absolute top-2.5 left-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center">
          <FaCheck className="me-1" /> Labeled
        </span>
      )}
    </div>
  );
}
