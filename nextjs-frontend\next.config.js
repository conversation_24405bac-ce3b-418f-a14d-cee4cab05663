/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Disable CSS source maps to prevent 404 errors for .map files
  productionBrowserSourceMaps: false,
  // Optimize CSS handling
  experimental: {
    optimizeCss: true
  },
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5000',
        pathname: '/api/**',
      },
      {
        protocol: 'http',
        hostname: '***********',
        port: '5000',
        pathname: '/api/**',
      },
    ],
  },
};

module.exports = nextConfig; 