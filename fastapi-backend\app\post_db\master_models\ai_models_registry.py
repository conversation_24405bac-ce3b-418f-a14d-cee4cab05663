"""
AI Models Registry - Master Database Model
This module defines the SQLAlchemy model for the AI Models Registry,
following the master_models pattern for centralized model management.
"""

from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from ..base import Base

class DeploymentStatus(str, PyEnum):
    """Model deployment status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"


class AIModelsRegistry(Base):
    """
    Centralized AI/ML models registry for the platform.
    Manages model metadata, capabilities, and deployment configurations
    for all AI models available across the annotation system.
    """
    __tablename__ = 'ai_models_registry'

    # Primary Identity
    id = Column(Integer, primary_key=True, autoincrement=True,
                comment='Unique model identifier for system references')
    model_name = Column(String(255), nullable=False, unique=True,
                       comment='Human-readable model name for display and identification')
    model_id = Column(String(100), nullable=False, unique=True,
                      comment='Technical model identifier for programmatic reference')
    supported_file_types = Column(JSONB, nullable=False,
                                 comment='Array of supported file formats (e.g., ["image", "video", "pdf"])')
    input_requirements = Column(JSONB,
                               comment='Model input specifications (size, format, preprocessing requirements)')
    output_format = Column(JSONB, nullable=False,
                          comment='Model output structure and format specifications')
    
    # Deployment & Status Management
    deployment_status = Column(String(50), default=DeploymentStatus.INACTIVE.value, nullable=False,
                              comment='Current deployment status for availability control')
    
    # Lifecycle & Audit Information
    created_at = Column(DateTime, default=func.now(), nullable=False,
                       comment='Model registration timestamp for tracking and audit')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False,
                       comment='Last modification timestamp for change tracking')
    created_by = Column(String(255),
                       comment='Username of person who registered the model')
    
    # Documentation & Description
    description = Column(String,
                        comment='Detailed model description, capabilities, and use cases')

    def __repr__(self):
        return f"<AIModelsRegistry(id={self.id}, model_name={self.model_name}, model_id={self.model_id}, status={self.deployment_status})>"

