// hooks/useNasBrowser.tsx
import { useState } from 'react';
import { Directory, SelectionTarget } from '../types';
import { authFetch } from '@/lib/authFetch';
import { showToast } from '@/lib/toast';
import { API_BASE_URL } from "../../../../lib/api";
export const useNasBrowser = () => {
  const [isNasBrowserOpen, setIsNasBrowserOpen] = useState(false);
  const [currentSelectionTarget, setCurrentSelectionTarget] = useState<SelectionTarget>(null);
  const [currentBrowsePath, setCurrentBrowsePath] = useState<string>("/");
  const [currentSelection, setCurrentSelection] = useState<string>("");
  const [isSelectingFile, setIsSelectingFile] = useState(false);
  const [directoryContents, setDirectoryContents] = useState<Directory[]>([]);
  const [isLoadingDirectory, setIsLoadingDirectory] = useState(false);

  // Load directory contents via API
  const loadDirectoryContents = async (path: string) => {
    setCurrentBrowsePath(path);
    setIsLoadingDirectory(true);
    try {
      const res = await authFetch(
        `${API_BASE_URL}/admin/browse-nas-directory?path=${encodeURIComponent(path)}`,
        { credentials: "include" }
      );
      if (!res.ok) {
        const errorData = await res.json().catch(() => ({ detail: "Failed to load directory" }));
        throw new Error(errorData.detail || "Failed to load directory");
      }
      const json = await res.json();
      const items = json.data?.items || [];
      // Map API items to Directory type
      const dirs: Directory[] = items.map((item: any) => ({
        name: item.name,
        path: item.path || `${path}/${item.name}`,
        type: item.type === "file" ? "file" : "directory",
      }));
      setDirectoryContents(dirs);
    } catch (err) {
      console.error("NAS Browser Error:", err);
      let errorMessage = "Failed to load directory";

      if (err instanceof Error) {
        // Provide more specific error messages based on the error
        if (err.message.includes("No active project")) {
          errorMessage = "Please create or select a project first";
        } else if (err.message.includes("No NAS credentials")) {
          errorMessage = "Please connect to NAS first before browsing directories";
        } else if (err.message.includes("Failed to connect")) {
          errorMessage = "Unable to connect to NAS. Please check your connection";
        } else if (err.message.includes("No project with NAS credentials")) {
          errorMessage = "No project with NAS credentials found. Please connect to NAS first";
        }
      }

      showToast.error(`NAS Browser: ${errorMessage}`);
      setDirectoryContents([]); // Clear directory contents on error
    } finally {
      setIsLoadingDirectory(false);
    }
  };

  // Handle opening NAS browser
  const handleOpenNasBrowser = (target: SelectionTarget) => {
    if (!target) return;
    
    setCurrentSelectionTarget(target);
    setCurrentBrowsePath("/");
    setCurrentSelection("");
    setIsSelectingFile(target === "verification-label-file");
    setIsNasBrowserOpen(true);

    // Load directory contents for path '/'
    loadDirectoryContents("/");
  };

  // Handle directory or file selection in NAS browser
  const handleSelectItem = (item: Directory) => {
    if (item.type === 'directory') {
      // Navigate to directory
      loadDirectoryContents(item.path);
    } else if (isSelectingFile && item.type === 'file') {
      // Select file
      setCurrentSelection(item.path);
    }
  };

  // Handle breadcrumb navigation
  const handleBreadcrumbClick = (path: string) => {
    loadDirectoryContents(path);
  };

  return {
    isNasBrowserOpen,
    setIsNasBrowserOpen,
    currentSelectionTarget,
    currentBrowsePath,
    currentSelection,
    isSelectingFile,
    directoryContents,
    isLoadingDirectory,
    handleOpenNasBrowser,
    handleSelectItem,
    handleBreadcrumbClick,
    loadDirectoryContents,
  };
};