"use client";

import { useState, useEffect, ReactNode, Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import RoleGuard from "@/components/auth/RoleGuard";
import PasswordModal from "@/components/auth/PasswordModal";
import {
  FaBars,
  FaHome,
  FaClipboardCheck,
  FaHistory,
  FaSignOutAlt,
  FaUser,
  FaKey,
  FaChevronDown,
} from "react-icons/fa";
import {
  getSidebarClasses,
  getHeaderClasses,
  getNavItemClasses,
  pageWrapperClasses,
  mainContentClasses,
  brandLinkClasses,
  userDropdownContainerClasses,
  changePasswordButtonClasses,
} from "@/utils/AuditorclassNames";

interface Props {
  children: ReactNode;
}

type SidebarItemProps = {
  href?: string;
  onClick?: () => void;
  icon: ReactNode;
  label: string;
  isActive?: boolean;
  fullWidth?: boolean;
  children?: ReactNode;
};

export default function AuditorLayout({ children }: Props) {
  const { user, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);

  // Format username to display as Title Case with spaces
  const formattedUsername = user?.username
    ? user.username
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    : "User";

  // Initialize sidebar state from localStorage (this is UI preference, not auth-related)
  useEffect(() => {
    const stored = localStorage.getItem("sidebarCollapsed");
    setCollapsed(stored === "true");
  }, []);

  // Persist sidebar state
  useEffect(() => {
    localStorage.setItem("sidebarCollapsed", collapsed.toString());
  }, [collapsed]);

  const toggleSidebar = () => setCollapsed(!collapsed);
  const toggleUserDropdown = () => setShowUserDropdown(!showUserDropdown);
  const handleLogout = async () => {
    await logout();
  };

  function SidebarItem({
    href,
    onClick,
    icon,
    label,
    isActive = false,
    fullWidth = true,
    children,
  }: SidebarItemProps) {
    const classes = getNavItemClasses(collapsed, isActive, fullWidth);
    const content = (
      <>
        {icon}
        {!collapsed && <span className="ml-2">{label}</span>}
        {children}
      </>
    );
    return href ? (
      <Link href={href} className={classes}>
        {content}
      </Link>
    ) : (
      <button onClick={onClick} type="button" className={classes}>
        {content}
      </button>
    );
  }

  // --- Inline component for sidebar nav using useSearchParams ---
  function SidebarNav() {
    const searchParams = useSearchParams();
    const view = searchParams.get("view") || "dashboard";
    return (
      <>
        <SidebarItem
          href="/auditor"
          icon={<FaHome />}
          label="Dashboard"
          isActive={view === "dashboard"}
          fullWidth={false}
        />
        <SidebarItem
          href="/auditor?view=tasks"
          icon={<FaClipboardCheck />}
          label="Available Tasks"
          isActive={view === "tasks"}
          fullWidth={false}
        />
        <SidebarItem
          href="/auditor?view=history"
          icon={<FaHistory />}
          label="History"
          isActive={view === "history"}
          fullWidth={false}
        />
      </>
    );
  }
  // --- end SidebarNav ---

  return (
    <RoleGuard allowedRoles={["auditor"]}>
      <div className={pageWrapperClasses}>
        {/* Sidebar */}
        <aside className={getSidebarClasses(collapsed)}>
          {/* Sidebar Header */}
          <div className={getHeaderClasses(collapsed)}>
            <button onClick={toggleSidebar} className="focus:outline-none">
              <FaBars className="text-xl" />
            </button>
            {!collapsed && (
              <Link href="/auditor" className={brandLinkClasses}>
                DADP
              </Link>
            )}
          </div>

          {/* --- FIX: Only SidebarNav uses useSearchParams, and it's Suspense-wrapped --- */}
          <nav className="flex-1 mt-2 space-y-2">
            <Suspense fallback={<div>Loading menu...</div>}>
              <SidebarNav />
            </Suspense>
          </nav>
          {/* --- END FIX --- */}

          {/* Footer */}
          <div className="border-t border-gray-700 pt-2">
            <nav className="space-y-1">
              <SidebarItem
                onClick={handleLogout}
                icon={<FaSignOutAlt />}
                label="Logout"
              />
              <div className="relative">
                <SidebarItem
                  onClick={toggleUserDropdown}
                  icon={<FaUser />}
                  label={formattedUsername}
                >
                  {!collapsed && <FaChevronDown className="ml-auto" />}
                </SidebarItem>
                {showUserDropdown && (
                  <div className={userDropdownContainerClasses}>
                    <div className="px-4 py-1">{user?.role || "Auditor"}</div>
                    <hr className="border-gray-600 my-1" />
                    <button
                      onClick={() => {
                        setIsPasswordModalOpen(true);
                        setShowUserDropdown(false);
                      }}
                      className={changePasswordButtonClasses}
                    >
                      <FaKey />
                      <span className="ml-2">Change Password</span>
                    </button>
                  </div>
                )}
              </div>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className={mainContentClasses}>{children}</main>

        {/* Password Change Modal */}
        <PasswordModal
          isOpen={isPasswordModalOpen}
          onClose={() => setIsPasswordModalOpen(false)}
        />
      </div>
    </RoleGuard>
  );
}

