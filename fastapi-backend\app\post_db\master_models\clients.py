from sqlalchemy import Column, Integer, String, DateTime, Sequence, JSON
from sqlalchemy.sql import func
from ..base import Base

class Clients(Base):
    """Master table storing client organisation metadata."""
    __tablename__ = 'clients'

    id = Column(Integer, Sequence('clients_id_seq'), primary_key=True)
    name = Column(String(255), nullable=False)
    username = Column(String(100), nullable=False, unique=True)
    email = Column(String(255), nullable=False, unique=True)
    created_at = Column(DateTime, server_default=func.current_timestamp())
    updated_at = Column(DateTime, server_default=func.current_timestamp(), onupdate=func.current_timestamp())
    contact_info = Column(JSON, nullable=True)

    def __repr__(self):
        return f"<Clients(id={self.id}, name={self.name}, username={self.username}, email={self.email})>"