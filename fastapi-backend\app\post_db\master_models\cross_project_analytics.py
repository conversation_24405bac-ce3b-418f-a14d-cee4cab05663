from sqlalchemy import Column, Integer, String, DateTime, Numeric, Date, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from ..base import Base


class CrossProjectAnalytics(Base):
    """
    Time-series analytics data aggregated across all projects for executive reporting, 
    performance monitoring, and business intelligence. 
    Provides platform-wide insights for management decisions.
    """
    __tablename__ = 'cross_project_analytics'

    # Primary Key
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Analytics Time Scope & Granularity
    analytics_date = Column(Date, default=func.current_date(), 
                          comment='Date of the analytics snapshot for time-series analysis')
    analytics_type = Column(String(50), nullable=False, 
                          comment='Time granularity (daily, weekly, monthly) for different reporting levels')
    
    # Project Portfolio Metrics
    total_active_projects = Column(Integer, default=0, 
                                 comment='Number of projects with active annotation work (for portfolio management)')
    total_files_processed = Column(Integer, default=0, 
                                 comment='Total files completed across all projects (for throughput measurement)')
    total_annotations_completed = Column(Integer, default=0, 
                                       comment='Total annotation tasks completed platform-wide (for productivity tracking)')
    
    # Human Resource Metrics
    total_active_users = Column(Integer, default=0, 
                              comment='Number of users who performed annotation work during this period')
    avg_user_utilization = Column(Numeric(5, 2), 
                                comment='Average user capacity utilization percentage across platform (for resource planning)')
    
    # Quality & Performance Metrics
    overall_quality_score = Column(Numeric(5, 2), 
                                 comment='Platform-wide quality score aggregated from all projects (for quality monitoring)')
    avg_annotation_time_seconds = Column(Integer, 
                                       comment='Average time to complete annotation across all media types (for efficiency analysis)')
    
    # Business Performance & Trend Analysis
    daily_throughput = Column(Integer, 
                            comment='Number of files completed per day (for capacity planning and client estimates)')
    weekly_trend = Column(JSONB, 
                         comment='Trend analysis data (e.g., {"direction": "up", "percentage": 15}) for executive dashboards')

    # Constraints
    __table_args__ = (
        UniqueConstraint('analytics_date', 'analytics_type', name='unique_analytics_date_type'),
    )

    def __repr__(self):
        return f"<CrossProjectAnalytics(date={self.analytics_date}, type='{self.analytics_type}', active_projects={self.total_active_projects})>" 