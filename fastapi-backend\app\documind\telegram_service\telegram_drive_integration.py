# from typing import Dict, List, Optional, Any, Union
# from pathlib import Path
# from datetime import datetime
# from googleapiclient.http import MediaFileUpload, MediaIoBaseUpload
# # from utils.google_drive.google_auth import build_service
# import io
# import base64


# class DriveUploader:
#     def __init__(self):
#         self.drive_service = None
#         self.root_folder_name = 'Telegram Images'
#         self.root_folder_id: Optional[str] = None
#         self.current_date_folder_id: Optional[str] = None
#         self._initialize_service()

#     def _initialize_service(self, force_refresh: bool = False) -> bool:
#         """Initialize or reinitialize the Drive service"""
#         try:
#             self.drive_service = build_service('drive', 'v3', force_refresh=force_refresh)
#             if not self.drive_service:
#                 raise Exception("Failed to initialize Drive service")
    
#             self._ensure_root_folder()
#             return True
#         except Exception:
#             return False

#     def _ensure_service(self):
#         """Ensure we have a working Drive service, attempting to fix if not"""
#         if not self.drive_service:
#             if not self._initialize_service():
#                 if not self._initialize_service(force_refresh=True):
#                     raise Exception("Could not establish Drive service connection")
#         return self.drive_service

#     def _ensure_root_folder(self) -> None:
#         """Ensure the root Telegram Images folder exists"""
#         service = self.drive_service
#         query = f"name='{self.root_folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
#         results = service.files().list(q=query, fields="files(id, name)").execute()

#         if results.get('files'):
#             self.root_folder_id = results.get('files')[0].get('id')
#         else:
#             folder_metadata = {
#                 'name': self.root_folder_name,
#                 'mimeType': 'application/vnd.google-apps.folder'
#             }
#             folder = service.files().create(body=folder_metadata, fields='id').execute()
#             self.root_folder_id = folder.get('id')

#         self._ensure_date_folder()

#     def _ensure_date_folder(self) -> None:
#         """Ensure the date-based folder exists (YYYY-MM-DD format)"""
#         if not self.root_folder_id:
#             raise Exception("Root folder ID not set")

#         service = self.drive_service
#         today_date = datetime.now().strftime("%Y-%m-%d")

#         query = f"name='{today_date}' and mimeType='application/vnd.google-apps.folder' and '{self.root_folder_id}' in parents and trashed=false"
#         results = service.files().list(q=query, fields="files(id, name)").execute()

#         if results.get('files'):
#             self.current_date_folder_id = results.get('files')[0].get('id')
#         else:
#             folder_metadata = {
#                 'name': today_date,
#                 'mimeType': 'application/vnd.google-apps.folder',
#                 'parents': [self.root_folder_id]
#             }
#             folder = service.files().create(body=folder_metadata, fields='id').execute()
#             self.current_date_folder_id = folder.get('id')

#     def create_channel_folder(self, channel_name: str) -> str:
#         """Create a folder for a channel in Google Drive inside the date folder."""
#         service = self._ensure_service()

#         if not self.root_folder_id:
#             self._ensure_root_folder()

#         if not self.current_date_folder_id:
#             self._ensure_date_folder()

#         folder_metadata = {
#             'name': channel_name,
#             'mimeType': 'application/vnd.google-apps.folder',
#             'parents': [self.current_date_folder_id]
#         }

#         query = f"name='{channel_name}' and mimeType='application/vnd.google-apps.folder' and '{self.current_date_folder_id}' in parents and trashed=false"
#         results = service.files().list(q=query, fields="files(id, name)").execute()

#         if results.get('files'):
#             return results.get('files')[0].get('id')

#         try:
#             folder = service.files().create(
#                 body=folder_metadata,
#                 fields='id'
#             ).execute()
#             return folder.get('id')
#         except Exception:
#             if self._initialize_service(force_refresh=True):
#                 return self.create_channel_folder(channel_name)
#             raise

#     def upload_file(self, file_path: Union[str, Path], filename: str, channel_folder_id: str) -> Dict[str, str]:
#         """Upload a file to the specified Google Drive folder."""
#         service = self._ensure_service()

#         file_metadata = {
#             'name': filename,
#             'parents': [channel_folder_id]
#         }

#         mime_type = self._get_mime_type(filename)

#         try:
#             media = MediaFileUpload(
#                 str(file_path),
#                 mimetype=mime_type,
#                 resumable=True
#             )

#             file = service.files().create(
#                 body=file_metadata,
#                 media_body=media,
#                 fields='id, webViewLink'
#             ).execute()

#             return {
#                 'id': file.get('id'),
#                 'link': file.get('webViewLink'),
#                 'name': filename
#             }
#         except Exception:
#             if self._initialize_service(force_refresh=True):
#                 return self.upload_file(file_path, filename, channel_folder_id)
#             raise

#     def upload_telegram_images(self, image_paths: List[Union[str, Path]], channel_name: str) -> Dict[str, Any]:
#         """Upload multiple Telegram images to Google Drive with date-based organization."""
#         folder_id = self.create_channel_folder(channel_name)
#         today_date = datetime.now().strftime("%Y-%m-%d")
#         upload_results = []
#         crop_counter = 1

#         for image_path_str in image_paths:
#             image_path = Path(image_path_str)
#             if image_path.exists():
#                 try:
#                     if '_' in image_path.stem and image_path.stem.split('_')[-1].isdigit():
#                         # Extract original filename from temp file name
#                         parts = image_path.stem.split('_')
#                         # format: tmp_originalname_cropnumber
#                         original_name = '_'.join(parts[1:-1])  
#                         crop_num = parts[-1]
#                         filename = f"{original_name}_{crop_num}.jpg"
#                     else:
#                         # Use original filename for regular images
#                         filename = image_path.name
                    
#                     result = self.upload_file(str(image_path), filename, folder_id)
#                     upload_results.append(result)
#                 except Exception:
#                     continue

#         return {
#             'root_folder_id': self.root_folder_id,
#             'root_folder_name': self.root_folder_name,
#             'date_folder_id': self.current_date_folder_id,
#             'date_folder_name': today_date,
#             'channel_folder_id': folder_id,
#             'channel_folder_name': channel_name,
#             'folder_name': f"{today_date}/{channel_name}",  # Full folder path
#             'uploaded_files': upload_results,
#             'folder_link': f"https://drive.google.com/drive/folders/{folder_id}",
#             'date_folder_link': f"https://drive.google.com/drive/folders/{self.current_date_folder_id}",
#             'root_folder_link': f"https://drive.google.com/drive/folders/{self.root_folder_id}"
#         }

#     def upload_binary_data(self, binary_data: bytes, filename: str, channel_folder_id: str, mime_type: str = None) -> Dict[str, str]:
#         """Upload binary data directly to Google Drive without creating temporary files."""
#         service = self._ensure_service()

#         file_metadata = {
#             'name': filename,
#             'parents': [channel_folder_id]
#         }

#         if not mime_type:
#             mime_type = self._get_mime_type(filename)

#         try:
#             # Create MediaIoBaseUpload from binary data
#             media = MediaIoBaseUpload(
#                 io.BytesIO(binary_data),
#                 mimetype=mime_type,
#                 resumable=True
#             )

#             file = service.files().create(
#                 body=file_metadata,
#                 media_body=media,
#                 fields='id, webViewLink'
#             ).execute()

#             return {
#                 'id': file.get('id'),
#                 'link': file.get('webViewLink'),
#                 'name': filename
#             }
#         except Exception:
#             if self._initialize_service(force_refresh=True):
#                 return self.upload_binary_data(binary_data, filename, channel_folder_id, mime_type)
#             raise

#     def upload_telegram_images_from_cache(self, image_data_list: List[Dict[str, Any]], channel_name: str) -> Dict[str, Any]:
#         """Upload multiple Telegram images to Google Drive directly from cache/binary data."""
#         folder_id = self.create_channel_folder(channel_name)
#         today_date = datetime.now().strftime("%Y-%m-%d")
#         upload_results = []

#         for i, img_data in enumerate(image_data_list):
#             try:
#                 # Handle different types of image data
#                 if 'image_data' in img_data:
#                     # Base64 encoded image data
#                     image_bytes = base64.b64decode(img_data['image_data'])
                    
#                     # Generate filename
#                     if 'original_filename' in img_data:
#                         filename = img_data['original_filename']
#                     elif 'message_id' in img_data:
#                         ext = img_data.get('file_extension', '.jpg')
#                         filename = f"msg_{img_data['message_id']}{ext}"
#                     else:
#                         filename = f"image_{i+1}.jpg"
                    
#                     # Get mime type
#                     mime_type = img_data.get('mime_type') or self._get_mime_type(filename)
                    
#                     result = self.upload_binary_data(image_bytes, filename, folder_id, mime_type)
#                     upload_results.append(result)
                    
#             except Exception as e:
#                 # Continue with other images if one fails
#                 upload_results.append({
#                     'error': str(e),
#                     'filename': img_data.get('original_filename', f'image_{i+1}')
#                 })
#                 continue

#         return {
#             'root_folder_id': self.root_folder_id,
#             'root_folder_name': self.root_folder_name,
#             'date_folder_id': self.current_date_folder_id,
#             'date_folder_name': today_date,
#             'channel_folder_id': folder_id,
#             'channel_folder_name': channel_name,
#             'folder_name': f"{today_date}/{channel_name}",
#             'uploaded_files': upload_results,
#             'folder_link': f"https://drive.google.com/drive/folders/{folder_id}",
#             'date_folder_link': f"https://drive.google.com/drive/folders/{self.current_date_folder_id}",
#             'root_folder_link': f"https://drive.google.com/drive/folders/{self.root_folder_id}"
#         }

#     @staticmethod
#     def _get_mime_type(filename: str) -> str:
#         """Get MIME type based on file extension."""
#         ext = filename.lower().split('.')[-1] if '.' in filename else ''
#         mime_types = {
#             'jpg': 'image/jpeg',
#             'jpeg': 'image/jpeg',
#             'png': 'image/png',
#             'gif': 'image/gif',
#             'webp': 'image/webp',
#             'bmp': 'image/bmp',
#             'pdf': 'application/pdf'
#         }
#         return mime_types.get(ext, 'application/octet-stream')