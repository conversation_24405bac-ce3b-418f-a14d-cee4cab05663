#!/usr/bin/env python3
"""
Migration Status Checker
Shows current migration status of both databases
"""

import sys
import os
from pathlib import Path
import psycopg2 # type: ignore
from urllib.parse import urlparse

app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))

def get_database_url(db_name):
    """Get database URL"""
    urls = {
        "master_db": os.getenv("MASTER_DB_DATABASE_URL", 
                             "postgresql+asyncpg://kanwar_raj:dadpdev123@***********:5432/master_db"),
        "project_db": os.getenv("PROJECT_DB_DATABASE_URL", 
                               "postgresql+asyncpg://mansi:pass123@***********:5432/project_db")
    }
    return urls[db_name].replace('postgresql+asyncpg://', 'postgresql+psycopg2://')

def check_database_status(db_name):
    """Check migration status and table count"""
    try:
        url = get_database_url(db_name)
        parsed = urlparse(url)
        
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port or 5432,
            user=parsed.username,
            password=parsed.password,
            database=parsed.path[1:]
        )
        
        with conn.cursor() as cur:
            # Check if alembic_version table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'alembic_version'
                );
            """)
            
            has_alembic = cur.fetchone()[0]
            
            current_revision = None
            if has_alembic:
                cur.execute("SELECT version_num FROM alembic_version;")
                result = cur.fetchone()
                current_revision = result[0] if result else None
            
            # Count tables
            cur.execute("""
                SELECT count(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name != 'alembic_version'
            """)
            
            table_count = cur.fetchone()[0]
            
            # List tables
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name != 'alembic_version'
                ORDER BY table_name
            """)
            
            tables = [row[0] for row in cur.fetchall()]
        
        conn.close()
        
        return {
            'status': 'connected',
            'has_alembic': has_alembic,
            'current_revision': current_revision,
            'table_count': table_count,
            'tables': tables
        }
        
    except Exception as e:
        return {
            'status': 'error',
            'error': str(e)
        }

def main():
    print("MIGRATION STATUS CHECKER")
    print("=" * 50)
    
    for db_name in ['master_db', 'project_db']:
        print(f"\n {db_name.upper()}")
        print("-" * 30)
        
        status = check_database_status(db_name)
        
        if status['status'] == 'error':
            print(f"Connection failed: {status['error']}")
            continue
        
        print(f"Connected successfully")
        print(f"Tables: {status['table_count']}")
        
        if status['has_alembic']:
            rev = status['current_revision'] or 'None'
            print(f"Migration Status: {rev}")
        else:
            print(f"Migration Status: No migrations applied")
        
        if status['tables']:
            print("Table List:")
            for table in status['tables']:
                print(f"   • {table}")
        else:
            print("No tables found")
    
    print("\n" + "=" * 50)
    print("Status check complete!")

if __name__ == "__main__":
    main()
