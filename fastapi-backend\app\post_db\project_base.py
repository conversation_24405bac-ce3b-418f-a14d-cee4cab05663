"""
Project database base for project-specific models only.
This module contains the declarative base that only project models inherit from.
Master models are NOT imported here to prevent them from being registered 
with the Base metadata for project databases.
"""

from sqlalchemy.orm import declarative_base

# Project-specific Base for project models only
ProjectBase = declarative_base()

# Import project models to register them with the ProjectBase metadata
from .allocation_models.project_metadata import ProjectMetadata
from .allocation_models.files_registry import FilesRegistry
from .allocation_models.allocation_batches import AllocationBatches
from .allocation_models.user_allocations import UserAllocations
from .allocation_models.file_allocations import FileAllocations
from .allocation_models.model_execution_logs import ModelExecutionLogs, ExecutionStatus
from .allocation_models.project_users import ProjectUsers
