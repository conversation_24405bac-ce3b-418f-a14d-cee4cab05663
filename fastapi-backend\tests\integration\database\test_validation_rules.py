"""
Integration tests for validation rules and schema requirements.
Tests password validation, field validation, and schema compliance.
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.schemas.UserSchemas import UserRegisterRequest, AddUserRequest
from app.services.auth_service import AuthService
# Import test factory for dynamic data generation
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
from tests.utilities.test_factories import test_factory



@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker - Authentication
@pytest.mark.security         # Suite marker - Security validation
@pytest.mark.regression       # Suite marker - Validation testing
@pytest.mark.high             # Priority marker - P1 (security is critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestPasswordValidation:
    """SECURITY REGRESSION TEST SUITE: Password validation rules."""
    
    @pytest.mark.asyncio
    async def test_password_length_validation(self, test_master_db: AsyncSession):
        """Test password length requirements (8-32 characters)."""
        # Test password too short (< 8 characters)
        with pytest.raises(Exception):  # Should raise validation error
            user_data = test_factory.users.create_user_register_request(role="annotator")
        
        # Test password too long (> 32 characters)  
        with pytest.raises(Exception):  # Should raise validation error
            user_data = test_factory.users.create_user_register_request(role="annotator")
        
        # Test valid password length (8-32 characters)
        user_data = test_factory.users.create_user_register_request(role="annotator")
        
        success, result = await AuthService.register_user(test_master_db, user_data)
        assert success
    
    @pytest.mark.asyncio
    async def test_password_confirmation_validation(self, test_master_db: AsyncSession):
        """Test password and confirm_password must match."""
        # Test mismatched passwords
        with pytest.raises(Exception):  # Should raise validation error
            user_data = test_factory.users.create_user_register_request(role="annotator")
        
        # Test matching passwords (should succeed)
        user_data = test_factory.users.create_user_register_request(role="annotator")
        
        success, result = await AuthService.register_user(test_master_db, user_data)
        assert success


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker - Authentication
@pytest.mark.security         # Suite marker - Security validation
@pytest.mark.regression       # Suite marker - API validation
@pytest.mark.high             # Priority marker - P1
@pytest.mark.stable           # Stability marker - Reliable
class TestAPIPasswordValidation:
    """SECURITY REGRESSION TEST SUITE: API password validation."""
    
    @pytest.mark.asyncio
    async def test_registration_password_validation_api(self, client: AsyncClient, setup_test_database):
        """Test password validation through registration API."""
        # Test short password
        short_password_data = test_factory.users.create_user_data(role="annotator")
        short_password_data.update({
            "password": "short",  # Too short
            "confirm_password": "short",
            "full_name": "API Short User"
        })
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=short_password_data)
        assert response.status_code == 422  # Validation error
        
        error_detail = response.json()
        assert "detail" in error_detail
        # Should contain password length validation error
        
        # Test mismatched passwords
        mismatch_data = test_factory.users.create_user_data(role="annotator")
        mismatch_data.update({
            "password": "valid_password123",
            "confirm_password": "different_password123",
            "full_name": "API Mismatch User"
        })
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=mismatch_data)
        assert response.status_code == 422  # Validation error
        
        error_detail = response.json() 
        assert "detail" in error_detail
        # Should contain password mismatch validation error
    
    @pytest.mark.asyncio
    async def test_valid_registration_api(self, client: AsyncClient, setup_test_database):
        """Test valid registration through API."""
        valid_data = test_factory.users.create_user_data(role="annotator")
        valid_data.update({
            "password": "valid_password123",
            "confirm_password": "valid_password123",
            "full_name": "API Valid User"
        })
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=valid_data)
        
        # Debug: Print response details if test fails  
        if response.status_code != 200:
            print(f"❌ Registration failed with status {response.status_code}")
            print(f"Response: {response.text}")
            try:
                error_detail = response.json()
                print(f"Error detail: {error_detail}")
            except:
                pass
        
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        assert result["message"] == "User registered successfully"
        assert result["data"]["user_id"] is not None


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.core_features     # Feature marker - Core validation
@pytest.mark.regression        # Suite marker - Schema validation
@pytest.mark.medium            # Priority marker - P2
@pytest.mark.stable            # Stability marker - Reliable
class TestSchemaFieldValidation:
    """REGRESSION TEST SUITE: Schema field validation rules."""
    
    @pytest.mark.asyncio
    async def test_user_register_required_fields(self, client: AsyncClient, setup_test_database):
        """Test that all required fields are validated."""
        # Test missing username
        incomplete_data = {
            "email": "<EMAIL>",
            "password": "test_password123", 
            "confirm_password": "test_password123",
            "full_name": "Incomplete User",
            "role": "annotator"
            # Missing username
        }
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=incomplete_data)
        assert response.status_code == 422  # Validation error
        
        # Test missing email
        import time
        import uuid
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        incomplete_data = {
            "username": f"incomplete_user_{unique_id}",
            "password": "test_password123",
            "confirm_password": "test_password123", 
            "full_name": "Incomplete User",
            "role": "annotator"
            # Missing email
        }
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=incomplete_data)
        assert response.status_code == 422  # Validation error
    
    @pytest.mark.asyncio
    async def test_admin_user_creation_fields(self, test_master_db: AsyncSession, setup_test_database):
        """Test AddUserRequest schema validation directly without API calls."""
        import time
        import uuid
        
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        # Test valid AddUserRequest schema creation
        try:
            admin_user_data = AddUserRequest(
                username=f"admin_test_user_{unique_id}",
                email=f"admin_test_{unique_id}@test.com",
                password="admin_password123",  # No confirm_password needed
                full_name="Admin Test User",
                role="annotator",
                is_active=True,
                annotation_mode="annotation",  # Optional field
                project_code="TEST_PROJECT_001"  # Optional field
            )
            
            # If we get here, validation passed
            assert admin_user_data.username == f"admin_test_user_{unique_id}"
            assert "@" in admin_user_data.email
            assert admin_user_data.role == "annotator"
            assert admin_user_data.is_active is True
            
        except Exception as e:
            pytest.fail(f"Valid AddUserRequest should not raise validation error: {e}")
        
        # Test that AddUserRequest allows missing optional fields
        try:
            minimal_admin_data = AddUserRequest(
                username=f"minimal_admin_{unique_id}",
                email=f"minimal_{unique_id}@test.com",
                password="minimal_password123",
                full_name="Minimal Admin",
                role="admin"
            )
            
            assert minimal_admin_data.username == f"minimal_admin_{unique_id}"
            assert minimal_admin_data.role == "admin"
            
        except Exception as e:
            pytest.fail(f"Minimal AddUserRequest should not raise validation error: {e}")
    
    @pytest.mark.asyncio
    async def test_email_format_validation(self, client: AsyncClient, setup_test_database):
        """Test email format validation."""
        import time
        import uuid
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        invalid_email_data = {
            "username": f"invalid_email_user_{unique_id}",
            "email": "not-an-email",  # Invalid email format
            "password": "test_password123",
            "confirm_password": "test_password123",
            "full_name": "Invalid Email User",
            "role": "annotator"
        }
        
        response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=invalid_email_data)
        assert response.status_code == 422  # Validation error
        
        error_detail = response.json()
        assert "detail" in error_detail
        # Should contain email format validation error


@pytest.mark.integration
@pytest.mark.database
@pytest.mark.auth             # Feature marker - Authentication
@pytest.mark.security         # Suite marker - Role security
@pytest.mark.regression       # Suite marker - Role validation
@pytest.mark.high             # Priority marker - P1 (roles are critical)
@pytest.mark.stable           # Stability marker - Reliable
class TestRoleValidation:
    """SECURITY REGRESSION TEST SUITE: User role validation."""
    
    @pytest.mark.asyncio
    async def test_valid_roles(self, client: AsyncClient, setup_test_database):
        """Test that valid roles are accepted."""
        import time
        import uuid
        import asyncio
        
        base_unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        valid_roles = ["admin", "annotator", "auditor", "verifier", "client", "manager"]
        
        async def test_single_role(role, i):
            """Test a single role registration."""
            unique_id = f"{base_unique_id}_{i}"
            user_data = {
                "username": f"role_test_user_{unique_id}",
                "email": f"role_test_{unique_id}@test.com",
                "password": "test_password123",
                "confirm_password": "test_password123",
                "full_name": f"Role Test User {i}",
                "role": role
            }
            
            try:
                response = await client.post(test_factory.config.get_endpoint("/auth/register"), json=user_data)
                
                # Debug: Print response details if test fails
                if response.status_code != 200:
                    print(f"❌ Registration failed for role {role} with status {response.status_code}")
                    print(f"Response: {response.text}")
                    try:
                        error_detail = response.json()
                        print(f"Error detail: {error_detail}")
                    except:
                        pass
                        
                assert response.status_code == 200, f"Role {role} should be valid"
                return True
            except Exception as e:
                print(f"❌ Exception testing role {role}: {e}")
                raise
        
        # Process roles sequentially to avoid overwhelming the database
        for i, role in enumerate(valid_roles):
            await test_single_role(role, i)
            # Small delay to ensure proper cleanup between requests
            await asyncio.sleep(0.1)
    
    @pytest.mark.asyncio
    async def test_invalid_role(self, test_master_db: AsyncSession, setup_test_database):
        """Test that invalid roles are rejected by service layer validation."""
        import time
        import uuid
        from app.services.auth_service import AuthService
        
        unique_id = f"{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
        
        # Test that AuthService rejects invalid roles
        invalid_role_data = test_factory.users.create_user_register_request(role="invalid_role")
        
        # Service layer should reject invalid role
        success, result = await AuthService.register_user(test_master_db, invalid_role_data)
        assert success is False
        assert "Invalid role" in result
        
        # Test that valid roles are accepted by service layer
        valid_roles = ["admin", "annotator", "verifier", "client"]
        
        for i, valid_role in enumerate(valid_roles):
            valid_data = test_factory.users.create_user_register_request(
                role=valid_role
            )
            
            success, result = await AuthService.register_user(test_master_db, valid_data)
            assert success is True, f"Valid role '{valid_role}' should be accepted by service layer"
            assert hasattr(result, 'username')
            assert result.role == valid_role  # Role should match the input
