"""
Storage-related fixtures for testing across all service tests.
"""

import pytest
import tempfile
import os
import shutil
from typing import Dict, Any


@pytest.fixture
def mock_storage_connectors():
    """Mock storage connectors (FTP, MinIO, etc.) - centralized version."""
    from unittest.mock import MagicMock
    
    mocks = {}
    
    # Mock FTP connector
    mocks['ftp'] = MagicMock()
    mocks['ftp'].connect.return_value = True
    mocks['ftp'].upload_file.return_value = True
    mocks['ftp'].download_file.return_value = True
    mocks['ftp'].list_files.return_value = ['file1.jpg', 'file2.jpg']
    mocks['ftp'].get_file_size.return_value = 2 * 1024 * 1024  # 2MB
    mocks['ftp'].is_available.return_value = True
    mocks['ftp'].connection_type = 'NAS-FTP'
    
    # Mock MinIO connector  
    mocks['minio'] = MagicMock()
    mocks['minio'].upload_file.return_value = True
    mocks['minio'].get_presigned_url.return_value = "https://test.com/presigned"
    mocks['minio'].download_file.return_value = True
    mocks['minio'].list_files.return_value = ['video1.mp4', 'video2.mp4']
    mocks['minio'].get_file_size.return_value = 100 * 1024 * 1024  # 100MB
    mocks['minio'].is_available.return_value = True
    mocks['minio'].connection_type = 'MinIO'
    
    return mocks


@pytest.fixture
def temp_file_setup():
    """Create temporary files for testing."""
    temp_dir = tempfile.mkdtemp(prefix='test_files_')
    
    test_files = {
        'small_image': {
            'path': os.path.join(temp_dir, 'test_image.jpg'),
            'size': 1024 * 1024,  # 1MB
            'type': 'image'
        },
        'medium_video': {
            'path': os.path.join(temp_dir, 'test_video.mp4'),
            'size': 10 * 1024 * 1024,  # 10MB
            'type': 'video'
        },
        'large_dataset': {
            'path': os.path.join(temp_dir, 'dataset.zip'),
            'size': 50 * 1024 * 1024,  # 50MB
            'type': 'archive'
        }
    }
    
    # Create actual test files
    for file_info in test_files.values():
        with open(file_info['path'], 'wb') as f:
            # Write test data
            f.write(b'test_data' * (file_info['size'] // 9))
    
    yield test_files
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def integration_storage_config():
    """Integration test storage configuration."""
    return {
        'minio_test_config': {
            'endpoint': '***********:9000',
            'access_key': 'test_access_key',
            'secret_key': 'test_secret_key',
            'bucket': 'integration-test-bucket',
            'use_ssl': False
        },
        'nas_test_config': {
            'ftp_host': '************',
            'ftp_port': 21,
            'ftp_username': 'test_ftp_user',
            'ftp_password': 'test_ftp_pass',
            'base_path': '/integration/test'
        }
    }
