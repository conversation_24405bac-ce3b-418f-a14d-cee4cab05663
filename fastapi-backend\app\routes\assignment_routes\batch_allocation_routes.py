from fastapi import APIRouter, Depends, HTTPException, status #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List, Dict, Any
import logging
from datetime import datetime

from dependencies.auth import require_admin
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.allocation_strategies import AllocationStrategies
from post_db.master_models.user_project_access import UserProjectAccess
from schemas.ProjectAssignmentSchemas import BatchAllocationInfo, AssignmentProgress
from core.session_manager import get_project_db_session, get_master_db_session
from post_db.allocation_models.allocation_batches import AllocationBatches
from post_db.allocation_models.file_allocations import FileAllocations
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/projects", tags=["batch-allocation"])

@router.get("/{project_id}/batch-allocations", response_model=Dict[str, Any])
async def get_batch_allocations(
    project_id: int,
    db: AsyncSession = Depends(get_master_db_session),
    current_user = Depends(require_admin)
):
    """
    Get batch allocation information for a project including progress tracking.
    """
    try:
        # Get project details
        result = await db.execute(select(ProjectsRegistry).where(ProjectsRegistry.id == project_id))
        project = result.scalar_one_or_none()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get strategy details
        result = await db.execute(select(AllocationStrategies).where(
            AllocationStrategies.id == project.allocation_strategy_id
        ))
        strategy = result.scalar_one_or_none()
        if not strategy:
            raise HTTPException(status_code=404, detail="Allocation strategy not found")
        
        # Get assigned users
        result = await db.execute(select(UserProjectAccess).where(
            UserProjectAccess.project_id == project_id
        ))
        assigned_users = result.scalars().all()
        
        # Group users by role
        annotators = [u for u in assigned_users if u.project_role == 'annotator']
        verifiers = [u for u in assigned_users if u.project_role == 'verifier']
        
        # Get actual batch count from allocation_batches table in project database
        
        try:
            async with get_project_db_session(project.project_code) as project_db:
                result = await project_db.execute(select(AllocationBatches))
                batches = result.scalars().all()
                total_batches = len(batches)
                logger.info(f"Found {total_batches} batches in project {project.project_code}")
        except Exception as e:
            error_msg = f"Could not fetch batches from project database {project.project_code}: {e}"
            logger.warning(error_msg)
            # Return more specific error information
            return {
                "success": False,
                "error": error_msg,
                "batches": [],
                "progress": None,
                "assigned_users": {
                    "annotators": len(annotators),
                    "verifiers": len(verifiers)
                }
            }
        batch_allocations = []
        
        for i in range(1, total_batches + 1):
            batch_info = BatchAllocationInfo(
                batch_id=i,
                batch_name=f"Batch {i}",
                annotator_count=0,  # This should come from your allocation_batches table
                verifier_count=0,   # This should come from your allocation_batches table
                total_annotators_required=strategy.num_annotators,
                total_verifiers_required=1 if strategy.requires_verification else 0,
                status='pending'
            )
            batch_allocations.append(batch_info)
        
        # Calculate progress
        progress = AssignmentProgress(
            total_batches=total_batches,
            annotator_phase={
                "completed_batches": 0,
                "in_progress_batches": 0,
                "pending_batches": total_batches
            },
            verifier_phase={
                "completed_batches": 0,
                "in_progress_batches": 0,
                "pending_batches": total_batches if strategy.requires_verification else 0
            }
        )
        
        return {
            "success": True,
            "batches": [batch.dict() for batch in batch_allocations],
            "progress": progress.dict(),
            "assigned_users": {
                "annotators": len(annotators),
                "verifiers": len(verifiers)
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching batch allocations for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch batch allocations: {str(e)}")

@router.post("/{project_id}/auto-assign/{role}")
async def auto_assign_users_to_batches(
    project_id: int,
    role: str,
    request_data: Dict[str, Any],
    db: AsyncSession = Depends(get_master_db_session),
    current_user = Depends(require_admin)
):
    """
    Automatically assign users to batches based on strategy requirements.
    Supports both sequential and parallel allocation strategies.
    """
    try:
        if role not in ['annotators', 'verifiers']:
            raise HTTPException(status_code=400, detail="Invalid role. Must be annotators or verifiers")
        
        # Get project details
        result = await db.execute(select(ProjectsRegistry).where(ProjectsRegistry.id == project_id))
        project = result.scalar_one_or_none()
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get strategy details
        result = await db.execute(select(AllocationStrategies).where(
            AllocationStrategies.id == project.allocation_strategy_id
        ))
        strategy = result.scalar_one_or_none()
        if not strategy:
            raise HTTPException(status_code=404, detail="Allocation strategy not found")
        
        # Validate strategy for annotators
        if role == 'annotators' and strategy.strategy_type == 'sequential' and strategy.num_annotators != 1:
            raise HTTPException(
                status_code=400, 
                detail=f"Sequential strategy with num_annotators={strategy.num_annotators} is not currently supported. Only num_annotators=1 is supported for sequential strategy."
            )
        
        # Get assigned users for the role
        role_singular = role.rstrip('s')  # Convert 'annotators' to 'annotator'
        result = await db.execute(select(UserProjectAccess).where(
            UserProjectAccess.project_id == project_id,
            UserProjectAccess.project_role == role_singular
        ))
        assigned_users = result.scalars().all()
        
        if not assigned_users:
            raise HTTPException(status_code=400, detail=f"No {role} assigned to this project")
        
        # Get actual batch count from allocation_batches table in project database

        
        try:
            async with get_project_db_session(project.project_code) as project_db:
                result = await project_db.execute(select(AllocationBatches))
                batches = result.scalars().all()
                total_batches = len(batches)
                logger.info(f"Found {total_batches} batches in project {project.project_code}")
        except Exception as e:
            logger.warning(f"Could not fetch batches from project database {project.project_code}: {e}")
            total_batches = 0
        if total_batches == 0:
            raise HTTPException(status_code=400, detail="No batches found for this project")
        
        # Determine assignment strategy based on role and strategy type
        from post_db.allocation_models.user_allocations import UserAllocations, AllocationRole
        from post_db.allocation_models.file_allocations import WorkflowPhase, AllocationStatus
        from post_db.allocation_models.project_users import ProjectUsers
        from post_db.allocation_models.files_registry import FilesRegistry
        from post_db.master_models.allocation_strategies import StrategyType
        
        try:
            async with get_project_db_session(project.project_code) as project_db:
                # Get all batches
                result = await project_db.execute(select(AllocationBatches))
                batches = result.scalars().all()
                
                assigned_count = 0
                
                if role == 'annotators':
                    # Handle annotator assignments based on strategy type
                    if strategy.strategy_type == StrategyType.SEQUENTIAL:
                        # Sequential strategy - distribute annotators across batches
                        logger.info(f"Using SEQUENTIAL strategy for {len(assigned_users)} annotators across {len(batches)} batches")
                        
                        # For sequential, we assign one annotator per batch
                        user_index = 0
                        for batch in batches:
                            if user_index < len(assigned_users):
                                user = assigned_users[user_index]
                                
                                # Get username from project_users table
                                project_user_result = await project_db.execute(
                                    select(ProjectUsers).where(ProjectUsers.user_id == user.user_id)
                                )
                                project_user = project_user_result.scalar_one_or_none()
                                username = project_user.username if project_user else f"user_{user.user_id}"
                                
                                # Get all files in this batch to calculate total_files
                                files_result = await project_db.execute(
                                    select(FilesRegistry).where(FilesRegistry.batch_id == batch.id)
                                )
                                batch_files = files_result.scalars().all()
                                total_files_in_batch = len(batch_files)
                                
                                # Create user allocation record
                                user_allocation = UserAllocations(
                                    batch_id=batch.id,
                                    user_id=user.user_id,
                                    username=username,
                                    total_files=total_files_in_batch,
                                    allocation_role=AllocationRole.ANNOTATOR,
                                    allocated_at=datetime.utcnow()
                                )
                                project_db.add(user_allocation)
                                
                                # Update the allocation_batches table
                                # For sequential strategy, we use annotator_1 column
                                setattr(batch, f"annotator_1", user.user_id)
                                
                                # Update file allocations for this batch
                                for file in batch_files:
                                    file_allocation = FileAllocations(
                                        file_id=file.id,
                                        batch_id=batch.id,
                                        allocation_sequence=1,  # First annotator
                                        workflow_phase=WorkflowPhase.ANNOTATION,
                                        processing_status="pending",
                                        allocated_at=datetime.utcnow()
                                    )
                                    
                                    # Set the annotator_1 column using setattr (dynamic column)
                                    try:
                                        setattr(file_allocation, "annotator_1", user.user_id)
                                    except Exception as e:
                                        logger.warning(f"Could not set annotator_1 column: {e}")
                                    
                                    project_db.add(file_allocation)
                                
                                assigned_count += 1
                                user_index += 1
                                
                                # Wrap around to the beginning of the users list if we have more batches than users
                                if user_index >= len(assigned_users):
                                    user_index = 0
                    
                    elif strategy.strategy_type == StrategyType.PARALLEL:
                        # Parallel strategy - assign multiple annotators to each batch
                        logger.info(f"Using PARALLEL strategy for {len(assigned_users)} annotators with {strategy.num_annotators} required per batch")
                        
                        # Validate if we have enough annotators
                        if len(assigned_users) < strategy.num_annotators:
                            raise HTTPException(
                                status_code=400, 
                                detail=f"Not enough annotators assigned. Strategy requires {strategy.num_annotators}, but only {len(assigned_users)} are assigned."
                            )
                        
                        batch_index = 0
                        user_index = 0
                        
                        # Assign users to batches in parallel fashion
                        while batch_index < len(batches) and user_index < len(assigned_users):
                            batch = batches[batch_index]
                            
                            # Count how many annotators are already assigned to this batch
                            annotators_assigned = 0
                            for i in range(1, strategy.num_annotators + 1):
                                if getattr(batch, f"annotator_{i}", None) is not None:
                                    annotators_assigned += 1
                            
                            # If this batch already has all required annotators, move to next batch
                            if annotators_assigned >= strategy.num_annotators:
                                batch_index += 1
                                continue
                            
                            # Assign the next user to the next available annotator slot
                            for i in range(1, strategy.num_annotators + 1):
                                annotator_field = f"annotator_{i}"
                                
                                # If this slot is empty and we have users left to assign
                                if getattr(batch, annotator_field, None) is None and user_index < len(assigned_users):
                                    user = assigned_users[user_index]
                                    
                                    # Get username from project_users table
                                    project_user_result = await project_db.execute(
                                        select(ProjectUsers).where(ProjectUsers.user_id == user.user_id)
                                    )
                                    project_user = project_user_result.scalar_one_or_none()
                                    username = project_user.username if project_user else f"user_{user.user_id}"
                                    
                                    # Get all files in this batch to calculate total_files
                                    files_result = await project_db.execute(
                                        select(FilesRegistry).where(FilesRegistry.batch_id == batch.id)
                                    )
                                    batch_files = files_result.scalars().all()
                                    total_files_in_batch = len(batch_files)
                                    
                                    # Create user allocation record
                                    user_allocation = UserAllocations(
                                        batch_id=batch.id,
                                        user_id=user.user_id,
                                        username=username,
                                        total_files=total_files_in_batch,
                                        allocation_role=AllocationRole.ANNOTATOR,
                                        allocated_at=datetime.utcnow()
                                    )
                                    project_db.add(user_allocation)
                                    
                                    # Update the allocation_batches table
                                    setattr(batch, annotator_field, user.user_id)
                                    
                                    # Update file allocations for this batch
                                    for file in batch_files:
                                        # Create a file allocation with the dynamic annotator column
                                        file_allocation = FileAllocations(
                                            file_id=file.id,
                                            batch_id=batch.id,
                                            allocation_sequence=i,  # Sequence based on annotator number
                                            workflow_phase=WorkflowPhase.ANNOTATION,
                                            processing_status="pending",
                                            allocated_at=datetime.utcnow()
                                        )
                                        
                                        # Set the appropriate annotator_N column using setattr (dynamic column)
                                        try:
                                            setattr(file_allocation, f"annotator_{i}", user.user_id)
                                        except Exception as e:
                                            logger.warning(f"Could not set annotator_{i} column: {e}")
                                        
                                        project_db.add(file_allocation)
                                    
                                    assigned_count += 1
                                    user_index += 1
                                    
                                    # If we've assigned all required annotators to this batch, move to next batch
                                    if i >= strategy.num_annotators:
                                        batch_index += 1
                                        break
                    else:
                        raise HTTPException(
                            status_code=400, 
                            detail=f"Unsupported strategy type: {strategy.strategy_type}"
                        )
                        
                elif role == 'verifiers':
                    # Handle verifier assignments
                    if not strategy.requires_verification:
                        raise HTTPException(
                            status_code=400, 
                            detail="This strategy does not require verification"
                        )
                    
                    logger.info(f"Assigning {len(assigned_users)} verifiers across {len(batches)} batches")
                    
                    # For verifiers, we assign one verifier per batch, cycling through the list
                    user_index = 0
                    for batch in batches:
                        if user_index < len(assigned_users):
                            user = assigned_users[user_index]
                            
                            # Get username from project_users table
                            project_user_result = await project_db.execute(
                                select(ProjectUsers).where(ProjectUsers.user_id == user.user_id)
                            )
                            project_user = project_user_result.scalar_one_or_none()
                            username = project_user.username if project_user else f"user_{user.user_id}"
                            
                            # Get all files in this batch to calculate total_files
                            files_result = await project_db.execute(
                                select(FilesRegistry).where(FilesRegistry.batch_id == batch.id)
                            )
                            batch_files = files_result.scalars().all()
                            total_files_in_batch = len(batch_files)
                            
                            # Create user allocation record
                            user_allocation = UserAllocations(
                                batch_id=batch.id,
                                user_id=user.user_id,
                                username=username,
                                total_files=total_files_in_batch,
                                allocation_role=AllocationRole.VERIFIER,
                                allocated_at=datetime.utcnow()
                            )
                            project_db.add(user_allocation)
                            
                            # Update the allocation_batches table
                            setattr(batch, "verifier", user.user_id)
                            
                            # Update file allocations for this batch
                            for file in batch_files:
                                file_allocation = FileAllocations(
                                    file_id=file.id,
                                    batch_id=batch.id,
                                    allocation_sequence=1,  # Only one verifier per file
                                    workflow_phase=WorkflowPhase.VERIFICATION,
                                    processing_status="pending",
                                    allocated_at=datetime.utcnow()
                                )
                                
                                # Set the verifier column using setattr (dynamic column)
                                try:
                                    setattr(file_allocation, "verifier", user.user_id)
                                except Exception as e:
                                    logger.warning(f"Could not set verifier column: {e}")
                                
                                project_db.add(file_allocation)
                            
                            assigned_count += 1
                            user_index += 1
                            
                            # Wrap around to the beginning of the users list if we have more batches than users
                            if user_index >= len(assigned_users):
                                user_index = 0
                
                # Commit all changes
                await project_db.commit()
                
                logger.info(f"Successfully assigned {assigned_count} {role} to batches for project {project_id}")
            
            return {
                "success": True,
                "assigned_batches": total_batches,
                "total_users": len(assigned_users),
                "assigned_users": assigned_count,
                    "strategy_type": strategy.strategy_type,
                    "message": f"Successfully assigned {assigned_count} {role} to batches using {strategy.strategy_type} strategy"
            }
            
        except Exception as e:
            logger.error(f"Error assigning users to batches: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to assign users to batches: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error auto-assigning {role} for project {project_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to auto-assign {role}: {str(e)}")
