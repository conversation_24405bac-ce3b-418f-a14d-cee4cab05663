import requests
from PIL import Image
from core.config import settings

# API configuration from settings
API_URL = settings.api_settings.ocr_url
API_KEY = settings.api_settings.ocr_key
API_HEADERS = settings.api_settings.ocr_headers

# System instructions for different document types
passport_instruction = (
    "Extract ONLY the following specific data from this passport image. Look carefully for each field. If any field is not visible or cannot be found, leave it blank in the output:"
    "\n\n1. PASSPORT COUNTRY CODE:"
    "\n   - Find the 3-letter country code in the passport"
    "\n   - Must be EXACTLY 3 UPPERCASE letters"
    "\n   - Cross-verify with MRZ line"
    
    "\n\n2. PASSPORT TYPE:"
    "\n   - First character in the MRZ line (usually 'P')"
    "\n   - Must be a SINGLE UPPERCASE letter"
    
    "\n\n3. PASSPORT NUMBER:"
    "\n   - Look for 'Passport No.' or 'No. du Passeport'"
    "\n   - Extract EXACTLY as shown"
    "\n   - Cross-verify with MRZ line"
    
    "\n\n4. FIRST NAME:"
    "\n   - Look under 'Given names/Prénoms'"
    "\n   - Include ALL given names and middle names"
    "\n   - Extract EXACTLY as shown"
    
    "\n\n5. FAMILY NAME:"
    "\n   - Look under 'Surname/Nom'"
    "\n   - Extract ONLY the family name"
    "\n   - Usually in UPPERCASE letters"
    
    "\n\n6. DATE OF BIRTH:"
    "\n   - IMPORTANT: Only extract the date of birth from the image"
    "\n   - Look for 'Date of Birth/Date de naissance'"
    "\n   - Extract day, month, and year separately"
    "\n   - Day: Two digits (01-31)"
    "\n   - Month: Two digits (01-12)"
    "\n   - Year: Four digits (YYYY)"
    "\n   - Cross-verify with MRZ line"
    "\n   - Example format:"
    "\n     Date of Birth Day: 15"
    "\n     Date of Birth Month: 03"
    "\n     Date of Birth Year: 1990"
    
    "\n\n7. PLACE OF BIRTH:"
    "\n   - Look for 'Place of birth/Lieu de naissance'"
    "\n   - Include city and/or country as written"
    
    "\n\n8. GENDER:"
    "\n   - Look for 'Sex/Sexe'"
    "\n   - Must be ONLY 'M' or 'F'"
    
    "\n\n9. AUTHORITY:"
    "\n   - Look for 'Authority/Autorité'"
    "\n   - Extract EXACTLY as shown"
    
    "\n\nOutput exactly in this format (leave field blank if information is not visible):"
    "\n----------------------------"
    "\nPassport Country Code: [EXACTLY 3 letters, UPPERCASE]"
    "\nPassport Type: [Single letter, UPPERCASE]"
    "\nPassport Number: [Exactly as shown]"
    "\nFirst Name: [All given names]"
    "\nFamily Name: [Surname only]"
    "\nDate of Birth Day: [2 digits]"
    "\nDate of Birth Month: [2 digits]"
    "\nDate of Birth Year: [4 digits]"
    "\nPlace of Birth: [As written]"
    "\nGender: [M or F only]"
    "\nAuthority: [Exactly as shown]"
)

check_instruction = (
   "Extract text exactly as it appears in this check/cheque image. Look carefully for ONLY these specific fields:"
"\n\n1. BANK NAME:"
"   - Look at the top center/header of check"
"   - Usually includes words like 'Bank', 'Trust', 'Financial' etc."
"\n\n2. PAYOR NAME:"
"   - Look for the pre-printed name at top-left of check"
"   - This is the person/entity WRITING the check"
"   - Extract only the full name"
"   - If business name, include full name"
"\n\n3. PAYOR ADDRESS:"
"   - Look for the pre-printed address under the payor name"
"   - Include complete street address"
"\n\n4. CHECK NUMBER:"
"   - Look for number in top-right corner or bottom MICR line"
"\n\n5. PAYEE NAME:"
"   - Look for name after 'Pay to the order of' or 'Pay'"
"   - Extract only the full name"
"   - If business name, include full name"
"\n\n6. PAYEE ADDRESS:"
"   - Look for address associated with payee if present"
"\n\n7. AMOUNT:"
"   - Look for amount in numbers (in box on right side)"
"   - Format as dollars and cents (e.g., 1,123.56)"
"\n\nOutput exactly in this format :"
"\n----------------------------"
"\nBank Name: [name of bank]"
"\n1st Payor First Name: [ name of payor]"
"\nPayor Street Address: [complete street address]"
"\nCheck Amount: [amount in numbers]"
"\n1st Payee First Name: [ name or business name]"
"\nCheck Number: [number]"
"\nPayee Street Address: [complete street address]"
)

invoice_instruction = (
   "Extract text exactly as it appears in this invoice image. For each field below:"
"\n\n1. INVOICE NUMBER: Look for 'Invoice #', 'Invoice Number', etc."
"\n\n2. INVOICE DATE: Look for 'Date', 'Invoice Date', etc."
"\n\n3. DUE DATE: Look for 'Due Date', 'Payment Due', etc."
"\n\n4. VENDOR/SELLER: Company name, address, contact info (who issued the invoice)"
"\n\n5. CUSTOMER/BILL TO: Name and address of the customer"
"\n\n6. PAYMENT TERMS: Look for 'Terms', 'Payment Terms', etc. (e.g., Net 30)"
"\n\n7. ITEMS/SERVICES: List all line items with descriptions, quantities, unit prices"
"\n\n8. SUBTOTAL: Amount before tax/shipping"
"\n\n9. TAX: Tax amount and rate (if specified)"
"\n\n10. SHIPPING/HANDLING: Shipping or handling charges (if any)"
"\n\n11. TOTAL AMOUNT: Final amount due"
"\n\n12. PAYMENT INSTRUCTIONS: Bank details, payment methods, etc."
"\n\nOutput exactly in this format (write 'Not visible' only if you cannot find the information):"
"\n----------------------------"
"\nInvoice Number: [number]"
"\nInvoice Date: [date]"
"\nDue Date: [date]"
"\nVendor/Seller: [company name & address]"
"\nCustomer: [name & address]"
"\nPayment Terms: [terms]"
"\nItems/Services: [description of items with prices]"
"\nSubtotal: [amount]"
"\nTax: [amount and rate]"
"\nShipping/Handling: [amount if applicable]"
"\nTotal Amount: [final amount]"
"\nPayment Instructions: [payment details]"
)

system_instructions = {
    "passport": passport_instruction,
    "check": check_instruction,
    "invoice": invoice_instruction
}

def preprocess_image(image_path, input_size=448, min_size=14):
    """Preprocess image"""
    image = Image.open(image_path)

    if image.mode != "RGB":
        image = image.convert("RGB")

    w, h = image.size
    
    if w < min_size or h < min_size:
        scale = max(min_size/w, min_size/h)
        new_w = max(min_size, int(w * scale))
        new_h = max(min_size, int(h * scale))
        image = image.resize((new_w, new_h), Image.Resampling.LANCZOS)
    
    max_dim = max(image.size)
    square_size = max(max_dim, input_size)
    
    new_image = Image.new('RGB', (square_size, square_size), (255, 255, 255))
    
    paste_x = (square_size - w) // 2
    paste_y = (square_size - h) // 2
    new_image.paste(image, (paste_x, paste_y))
    
    if square_size > input_size:
        new_image = new_image.resize((input_size, input_size), Image.Resampling.LANCZOS)
    
    return new_image

def process_document_image(image_path, document_type, model_type=None, tokenizer=None, model=None):
    system_instruction = system_instructions.get(document_type, passport_instruction)
    prompt = f"<image>\n{system_instruction}\n\n"
    
    url = f"{API_URL}/infer/image"
    
    with open(image_path, "rb") as image_file:
        files = {"file": image_file}
        data = {
            "question": prompt,
            "model_type": "standard"  
        }
        
        response = requests.post(
            url, 
            headers=API_HEADERS, 
            data=data, 
            files=files,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            return result["response"]
        else:
            raise Exception(f"API Error: {response.status_code} - {response.text}")

def check_server_status():
    """Check server status"""
    return {"status": "available"}

def load_model(model_type: str = "standard"):
    """Load model configuration"""
    tokenizer = {
        "model_type": model_type,
        "api_url": API_URL,
        "headers": API_HEADERS
    }
    
    model = {}
    
    return tokenizer, model