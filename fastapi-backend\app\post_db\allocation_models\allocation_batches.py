from sqlalchemy import <PERSON>umn, Integer, String, TIMESTAMP, Boolean, func, UniqueConstraint, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from ..project_base import ProjectBase
from ..enums import CaseInsensitiveEnum
from enum import Enum as PyEnum

class BatchStatus(str, PyEnum):
    """Batch status enumeration."""
    CREATED = "created"
    ALLOCATING = "allocating"
    ALLOCATED = "allocated"
    COMPLETED = "completed"


class AllocationBatches(ProjectBase):
    """
    Groups files into batches for efficient allocation management. 
    Focuses purely on batch-level allocation logistics.
    """
    __tablename__ = "allocation_batches"
    
    __table_args__ = (
        UniqueConstraint('batch_identifier', name='_batch_identifier_uc'),
    )

    # Primary Identity & Batch Reference
    id = Column(Integer, primary_key=True, autoincrement=True, index=True,
                comment="Unique batch identifier within this project database")
    batch_identifier = Column(String(100), nullable=False, unique=True, index=True,
                             comment="Human-readable batch reference (e.g., 'BATCH_001_IMAGES') for tracking and communication")
    
    # Batch Status
    batch_status = Column(CaseInsensitiveEnum(BatchStatus), default=BatchStatus.CREATED, nullable=False,
                         comment="Current batch state")
    
    # Batch Content Management
    total_files = Column(Integer, nullable=False,
                        comment="Total number of files included in this batch")
    file_list = Column(JSONB, nullable=True,
                      comment="Array of file IDs included in this batch")
    
    # Allocation Requirements & Criteria
    skill_requirements = Column(JSONB, nullable=True,
                               comment="Required skills for users who can be allocated to this batch")
    allocation_criteria = Column(JSONB, nullable=True,
                                comment="Custom criteria for user selection (experience, certification, etc.)")
    
    # Priority & Timeline Management
    is_priority = Column(Boolean, default=False, nullable=False,
                        comment="Whether this batch has priority for allocation")
    created_at = Column(TIMESTAMP, default=func.now(), nullable=False,
                       comment="When batch was created")
    deadline = Column(TIMESTAMP, nullable=True,
                     comment="Target completion deadline")
                     
    # Dynamic Allocation Columns
    annotation_count = Column(Integer, default=0, nullable=False,
                           comment="Number of annotators for this batch")
    assignment_count = Column(Integer, default=0, nullable=False,
                           comment="Number of annotators assignments in this batch")
    completion_count = Column(Integer, default=0, nullable=False,
                           comment="Number of annotators completions in this batch")
                           
    # Dynamic columns for annotators will be added at runtime
    # Example: annotator_1 = Column(Integer, nullable=True, comment='References user_id in project_users table')

    # Dynamic columns for verifier will be added at runtime for strategies that require verification
    # Example: verifier = Column(Integer, nullable=True, comment='References user_id in project_users table')

    # Project-Specific Extensibility
    custom_batch_config = Column(JSONB, nullable=True,
                                comment="Project-specific batch allocation configuration")

    # Relationships
    files = relationship("FilesRegistry", back_populates="batch", cascade="all, delete-orphan")
    user_allocations = relationship("UserAllocations", back_populates="batch", cascade="all, delete-orphan")
    file_allocations = relationship("FileAllocations", back_populates="batch", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<AllocationBatches(id={self.id}, batch_identifier={self.batch_identifier}, batch_status={self.batch_status})>"