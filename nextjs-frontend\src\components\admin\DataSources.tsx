"use client";

import React, { useState, useEffect } from "react";
import {
  FaGoogle,
  FaDatabase,
  FaServer,
  FaTelegram,
  FaTwitter,
  FaYoutube,
  FaLinkedin,
  FaInstagram,
  FaArrowRight,
  FaArrowLeft,
  FaInfoCircle,
  FaShieldAlt,
  FaCheck,
} from "react-icons/fa";
import { TelegramChannels, TelegramImages, TelegramConnect } from "./telegram";
// import { authFetch } from '@/lib/authFetch';
import { showToast } from "@/lib/toast";

import { checkTelegramAuth, fetchTelegramChannels } from "./telegram/types";

export default function DataSources() {
  const [activeTab, setActiveTab] = useState<"overview" | "telegram">(
    "overview"
  );
  const [tgStatus, setTgStatus] = useState<
    "connected" | "disconnected" | "error" | "loading"
  >("loading");
  const [tgChannelsCount, setTgChannelsCount] = useState<number>(0);
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(
    null
  );
  // viewType: 'channels' shows the channels list and analysis panel; 'images' shows images view
  const [viewType, setViewType] = useState<"channels" | "images">("channels");

  // Flash and Coming Soon modal state
  const [showTelegramInfo, setShowTelegramInfo] = useState(false);
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [comingSoonDetails] = useState<{
    icon: JSX.Element;
    title: string;
    text: string;
  } | null>(null);
  const closeFlash = () => setShowTelegramInfo(false);
  const closeComingSoon = () => setShowComingSoon(false);
  const handleShowTelegramInfo = () => setShowTelegramInfo(true);

  // On clicking analysis
  const handleSelectAnalysis = (id: number) => {
    setSelectedChannelId(id);
    setViewType("channels");
  };
  // On clicking view images
  const handleSelectImages = (id: number) => {
    setSelectedChannelId(id);
    setViewType("images");
  };

  // Fetch and update Telegram connection status and channel count
  const fetchTelegramStatus = async () => {
    setTgStatus("loading");
    try {
      const authData = await checkTelegramAuth();
      if (authData.authenticated) {
        setTgStatus("connected");
        const channels = await fetchTelegramChannels();
        setTgChannelsCount(channels.length);
      } else {
        setTgStatus("disconnected");
      }
    } catch (err) {
      console.error(err);
      setTgStatus("error");
    }
  };

  // On mount, check Telegram status
  useEffect(() => {
    fetchTelegramStatus();
  }, []);

  // Attach click handler for any .btn-disabled to show disabled toast
  useEffect(() => {
    document.querySelectorAll(".btn-disabled").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        e.preventDefault();
        showToast.info("This feature is currently disabled");
      });
    });
  }, []);

  return (
    <div className="bg-gray-50 min-h-screen py-16">
      {/* Background decorations */}
      <div className="noise-texture pointer-events-none absolute inset-0"></div>
      <div className="background-grid pointer-events-none absolute inset-0"></div>
      <div className="floating-shapes pointer-events-none absolute inset-0">
        <div className="shape shape-1"></div>
        <div className="shape shape-2"></div>
        <div className="shape shape-3"></div>
      </div>

      <div className="container mx-auto px-4">
        {/* Flash Modal */}
        {showTelegramInfo && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
            <div className="bg-white p-6 rounded shadow-lg max-w-md w-full">
              <h3 className="text-xl font-semibold mb-4">Telegram Info</h3>
              {/* TODO: Insert flash message content here */}
              <button
                onClick={closeFlash}
                className="mt-4 px-4 py-2 bg-gray-200 rounded"
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Coming Soon Modal */}
        {showComingSoon && comingSoonDetails && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
            <div className="bg-white p-6 rounded shadow-lg max-w-md w-full text-center">
              <div className="text-4xl mb-2">{comingSoonDetails.icon}</div>
              <h3 className="text-xl font-semibold mb-2">
                {comingSoonDetails.title}
              </h3>
              <p className="mb-4">{comingSoonDetails.text}</p>
              <button
                onClick={closeComingSoon}
                className="mt-2 px-4 py-2 bg-gray-200 rounded"
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary">Data Sources</h1>
          <div className="w-16 h-1 bg-primary mx-auto mt-2 rounded"></div>
        </div>

        {/* Tabs Content */}
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Telegram Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaTelegram className="text-blue-500 mr-2" /> Telegram
                </div>
                <FaInfoCircle
                  onClick={handleShowTelegramInfo}
                  className="text-gray-400 hover:text-gray-600 cursor-pointer"
                />
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Connect to Telegram channels and download media content for
                  processing.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Access channel messages
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Download media files
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Filter by date and type
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Batch processing
                  </li>
                </ul>
              </div>
              <div className="mt-6 flex items-center justify-between">
                {tgStatus === "connected" ? (
                  <span className="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-700">
                    {tgChannelsCount} Channels
                  </span>
                ) : (
                  <span
                    className={`text-xs font-medium px-2 py-1 rounded-full ${
                      tgStatus === "error"
                        ? "bg-red-100 text-red-700"
                        : "bg-gray-200 text-gray-700"
                    }`}
                  >
                    {tgStatus === "error" ? "Error" : "Loading..."}
                  </span>
                )}
                <button
                  onClick={() => setActiveTab("telegram")}
                  className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition"
                >
                  {tgStatus === "connected" ? "Manage" : "Connect"}{" "}
                  <FaArrowRight className="ml-2" />
                </button>
              </div>
            </div>
            {/* Twitter Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaTwitter className="text-blue-400 mr-2" /> Twitter
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Track hashtags and download media from Twitter for analysis.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Track hashtags
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Monitor accounts
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Download media
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Sentiment analysis
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* Database Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaDatabase className="text-gray-700 mr-2" /> Database
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Connect to external databases and import data for processing.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    SQL databases
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    NoSQL databases
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Custom Database
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Scheduled imports
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* YouTube Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaYoutube className="text-red-600 mr-2" /> YouTube
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>Access YouTube videos and channels for content analysis.</p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Download videos
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Channel analysis
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Comment extraction
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Metadata processing
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* LinkedIn Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaLinkedin className="text-blue-700 mr-2" /> LinkedIn
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Monitor LinkedIn posts and profiles for professional insights.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Profile tracking
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Post analysis
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Media collection
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Engagement metrics
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* NAS Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaServer className="text-gray-700 mr-2" /> NAS
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Connect to network storage devices for secure data import and
                  processing.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Bulk file import
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Scheduled syncing
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Secure connections
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Automated backup
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* Google Drive Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaGoogle className="text-green-600 mr-2" /> Google Drive
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Import files and documents from Google Drive for analysis.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Document import
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Media collection
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Folder monitoring
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Auto-synchronization
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
            {/* Instagram Card */}
            <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center text-lg font-semibold text-gray-900">
                  <FaInstagram className="text-pink-500 mr-2" /> Instagram
                </div>
              </div>
              <div className="text-sm text-gray-600 space-y-4">
                <p>
                  Track Instagram posts and stories for visual content analysis.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Post tracking
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Story collection
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Hashtag monitoring
                  </li>
                  <li className="flex items-start">
                    <FaCheck className="mt-1 mr-2 text-green-500" />
                    Image analysis
                  </li>
                </ul>
              </div>
              <div className="mt-6">
                <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">
                  Coming Soon
                </span>
              </div>
            </div>
          </div>
        )}

        {activeTab === "telegram" && (
          <>
            {/* Telegram Connection header */}
            <div className="bg-primary text-white rounded-top p-3 mb-3 d-flex justify-content-between align-items-center">
              <h2 className="mb-0">
                <FaTelegram className="me-2" />
                Telegram Connection
              </h2>
              <button
                className="btn-secondary px-3 py-1 text-sm"
                onClick={() => setActiveTab("overview")}
              >
                &larr; Back to Data Sources
              </button>
            </div>
            {/* Conditional render: show connect form until connected, then channels/images */}
            {tgStatus !== "connected" && (
              <div className="bg-white shadow rounded-lg p-6 mb-4">
                <TelegramConnect
                  onConnected={() => {
                    fetchTelegramStatus();
                    setActiveTab("telegram");
                  }}
                />
              </div>
            )}
            {tgStatus === "connected" && (
              <>
                {viewType === "channels" ? (
                  <div className="bg-white shadow rounded-lg p-6 mb-4">
                    <TelegramChannels
                      selectedChannelId={selectedChannelId}
                      onSelectChannel={handleSelectAnalysis}
                      onShowImages={handleSelectImages}
                      onDisconnect={() => fetchTelegramStatus()}
                    />
                  </div>
                ) : (
                  <div className="bg-white shadow rounded-lg p-6 mb-4">
                    <button
                      onClick={() => setViewType("channels")}
                      className="px-3 py-1 mb-3 bg-gray-200 hover:bg-gray-300 rounded-md text-gray-700 flex items-center"
                    >
                      <FaArrowLeft className="mr-2" /> Back to Channels
                    </button>
                    {selectedChannelId !== null && (
                      <TelegramImages channelId={selectedChannelId} />
                    )}
                  </div>
                )}
              </>
            )}
          </>
        )}

        {/* Compliance Section */}
        <div className="mt-16 p-8 bg-secondary text-white rounded-xl flex items-start space-x-4">
          <FaShieldAlt className="text-2xl mt-1" />
          <div className="text-sm leading-relaxed">
            <h4 className="font-semibold mb-2">Our Commitment to Ethics:</h4>
            <p>
              ProcessVenue adheres to the highest standards of data privacy and
              ethical practices. We&apos;re committed to compliant and
              transparent data handling, respecting all privacy regulations, and
              never engaging in unauthorized data collection. Your trust is our
              priority—we safeguard it through ethical operations and strict
              adherence to our{" "}
              <a href="#" className="underline font-medium">
                Terms &amp; Conditions
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// 'use client';

// import React, { useState, useEffect } from 'react';
// import { FaGoogle, FaDatabase, FaServer,  FaTelegram, FaTwitter, FaYoutube, FaLinkedin, FaInstagram, FaArrowRight, FaArrowLeft, FaInfoCircle, FaShieldAlt, FaCheck } from 'react-icons/fa';
// import { TelegramChannels, TelegramImages, TelegramConnect } from './telegram';
// // import { authFetch } from '@/lib/authFetch';
// import { showToast } from '@/lib/toast';

// type SourceType = 'google_drive' | 'nas' | 'local' | 'api';

// // interface DataSource {
// //   id: number;
// //   name: string;
// //   type: SourceType;
// //   connectionString: string;
// //   status: 'connected' | 'disconnected' | 'error';
// //   lastConnected: string;
// // }

// import { BASE_URL, checkTelegramAuth, fetchTelegramChannels } from './telegram/types';

// export default function DataSources() {
//   const [activeTab, setActiveTab] = useState<'overview' | 'telegram'>('overview');
//   const [tgStatus, setTgStatus] = useState<'connected' | 'disconnected' | 'error' | 'loading'>('loading');
//   const [tgChannelsCount, setTgChannelsCount] = useState<number>(0);
//   const [selectedChannelId, setSelectedChannelId] = useState<number | null>(null);
//   // viewType: 'channels' shows the channels list and analysis panel; 'images' shows images view
//   const [viewType, setViewType] = useState<'channels' | 'images'>('channels');

//   // NEW: Flash and Coming Soon modal state
//   const [showTelegramInfo, setShowTelegramInfo] = useState(false);
//   const [showComingSoon, setShowComingSoon] = useState(false);
//   const [comingSoonDetails, setComingSoonDetails] = useState<{icon: JSX.Element; title: string; text: string} | null>(null);
//   const closeFlash = () => setShowTelegramInfo(false);
//   const closeComingSoon = () => setShowComingSoon(false);
//   const handleShowTelegramInfo = () => setShowTelegramInfo(true);
//   const handleShowComingSoon = (icon: JSX.Element, title: string, text: string) => {
//     setComingSoonDetails({ icon, title, text });
//     setShowComingSoon(true);
//   };

//   // On clicking analysis
//   const handleSelectAnalysis = (id: number) => {
//     setSelectedChannelId(id);
//     setViewType('channels');
//   };
//   // On clicking view images
//   const handleSelectImages = (id: number) => {
//     setSelectedChannelId(id);
//     setViewType('images');
//   };

//   // Fetch and update Telegram connection status and channel count
//   const fetchTelegramStatus = async () => {
//     setTgStatus('loading');
//     try {
//       const authData = await checkTelegramAuth();
//       if (authData.authenticated) {
//         setTgStatus('connected');
//         const channels = await fetchTelegramChannels();
//         setTgChannelsCount(channels.length);
//       } else {
//         setTgStatus('disconnected');
//       }
//     } catch (err) {
//       console.error(err);
//       setTgStatus('error');
//     }
//   };

//   // On mount, check Telegram status
//   useEffect(() => {
//     fetchTelegramStatus();
//   }, []);

//   // Attach click handler for any .btn-disabled to show disabled toast
//   useEffect(() => {
//     document.querySelectorAll('.btn-disabled').forEach(btn => {
//       btn.addEventListener('click', e => {
//         e.preventDefault();
//         showToast.info('This feature is currently disabled');
//       });
//     });
//   }, []);

//   return (
//     <div className="bg-gray-50 min-h-screen py-16">
//       {/* Background decorations */}
//       <div className="noise-texture pointer-events-none absolute inset-0"></div>
//       <div className="background-grid pointer-events-none absolute inset-0"></div>
//       <div className="floating-shapes pointer-events-none absolute inset-0">
//         <div className="shape shape-1"></div>
//         <div className="shape shape-2"></div>
//         <div className="shape shape-3"></div>
//       </div>

//       <div className="container mx-auto px-4">
//         {/* Flash Modal */}
//         {showTelegramInfo && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
//             <div className="bg-white p-6 rounded shadow-lg max-w-md w-full">
//               <h3 className="text-xl font-semibold mb-4">Telegram Info</h3>
//               {/* TODO: Insert flash message content here */}
//               <button onClick={closeFlash} className="mt-4 px-4 py-2 bg-gray-200 rounded">Close</button>
//             </div>
//           </div>
//         )}

//         {/* Coming Soon Modal */}
//         {showComingSoon && comingSoonDetails && (
//           <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
//             <div className="bg-white p-6 rounded shadow-lg max-w-md w-full text-center">
//               <div className="text-4xl mb-2">{comingSoonDetails.icon}</div>
//               <h3 className="text-xl font-semibold mb-2">{comingSoonDetails.title}</h3>
//               <p className="mb-4">{comingSoonDetails.text}</p>
//               <button onClick={closeComingSoon} className="mt-2 px-4 py-2 bg-gray-200 rounded">Close</button>
//             </div>
//           </div>
//         )}

//         {/* Page Header */}
//         <div className="text-center mb-12">
//           <h1 className="text-4xl font-bold text-primary">Data Sources</h1>
//           <div className="w-16 h-1 bg-primary mx-auto mt-2 rounded"></div>
//         </div>

//         {/* Tabs Content */}
//         {activeTab === 'overview' && (
//           <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
//             {/* Telegram Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaTelegram className="text-blue-500 mr-2" /> Telegram
//                 </div>
//                 <FaInfoCircle onClick={handleShowTelegramInfo} className="text-gray-400 hover:text-gray-600 cursor-pointer" />
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Connect to Telegram channels and download media content for processing.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Access channel messages</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Download media files</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Filter by date and type</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Batch processing</li>
//                 </ul>
//               </div>
//               <div className="mt-6 flex items-center justify-between">
//                 {tgStatus === 'connected' ? (
//                   <span className="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-700">{tgChannelsCount} Channels</span>
//                 ) : (
//                   <span className={`text-xs font-medium px-2 py-1 rounded-full ${tgStatus === 'error' ? 'bg-red-100 text-red-700' : 'bg-gray-200 text-gray-700'}`}>{tgStatus === 'error' ? 'Error' : 'Loading...'}</span>
//                 )}
//                 <button onClick={() => setActiveTab('telegram')} className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition">
//                   {tgStatus === 'connected' ? 'Manage' : 'Connect'} <FaArrowRight className="ml-2" />
//                 </button>
//               </div>
//             </div>
//             {/* Twitter Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaTwitter className="text-blue-400 mr-2" /> Twitter
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Track hashtags and download media from Twitter for analysis.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Track hashtags</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Monitor accounts</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Download media</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Sentiment analysis</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* Database Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaDatabase className="text-gray-700 mr-2" /> Database
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Connect to external databases and import data for processing.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />SQL databases</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />NoSQL databases</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Custom Database</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Scheduled imports</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* YouTube Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaYoutube className="text-red-600 mr-2" /> YouTube
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Access YouTube videos and channels for content analysis.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Download videos</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Channel analysis</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Comment extraction</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Metadata processing</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* LinkedIn Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaLinkedin className="text-blue-700 mr-2" /> LinkedIn
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Monitor LinkedIn posts and profiles for professional insights.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Profile tracking</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Post analysis</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Media collection</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Engagement metrics</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* NAS Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaServer className="text-gray-700 mr-2" /> NAS
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Connect to network storage devices for secure data import and processing.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Bulk file import</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Scheduled syncing</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Secure connections</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Automated backup</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* Google Drive Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaGoogle className="text-green-600 mr-2" /> Google Drive
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Import files and documents from Google Drive for analysis.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Document import</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Media collection</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Folder monitoring</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Auto-synchronization</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//             {/* Instagram Card */}
//             <div className="bg-white rounded-xl p-6 shadow hover:shadow-lg transition">
//               <div className="flex items-center justify-between mb-4">
//                 <div className="flex items-center text-lg font-semibold text-gray-900">
//                   <FaInstagram className="text-pink-500 mr-2" /> Instagram
//                 </div>
//               </div>
//               <div className="text-sm text-gray-600 space-y-4">
//                 <p>Track Instagram posts and stories for visual content analysis.</p>
//                 <ul className="space-y-2">
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Post tracking</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Story collection</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Hashtag monitoring</li>
//                   <li className="flex items-start"><FaCheck className="mt-1 mr-2 text-green-500" />Image analysis</li>
//                 </ul>
//               </div>
//               <div className="mt-6">
//                 <span className="inline-flex items-center px-2 py-1 bg-gray-200 text-gray-700 rounded-full text-xs font-medium cursor-not-allowed">Coming Soon</span>
//               </div>
//             </div>
//           </div>
//         )}

//         {activeTab === 'telegram' && (
//           <>
//             {/* Telegram Connection header */}
//             <div className="bg-primary text-white rounded-top p-3 mb-3 d-flex justify-content-between align-items-center">
//               <h2 className="mb-0"><FaTelegram className="me-2" />Telegram Connection</h2>
//               <button className="btn-secondary px-3 py-1 text-sm" onClick={() => setActiveTab('overview')}>
//                 &larr; Back to Data Sources
//               </button>
//             </div>
//             {/* Conditional render: show connect form until connected, then channels/images */}
//             {tgStatus !== 'connected' && (
//               <div className="bg-white shadow rounded-lg p-6 mb-4">
//                 <TelegramConnect onConnected={() => { fetchTelegramStatus(); setActiveTab('telegram'); }} />
//               </div>
//             )}
//             {tgStatus === 'connected' && (
//               <>
//                 {viewType === 'channels' ? (
//                   <div className="bg-white shadow rounded-lg p-6 mb-4">
//                     <TelegramChannels
//                       selectedChannelId={selectedChannelId}
//                       onSelectChannel={handleSelectAnalysis}
//                       onShowImages={handleSelectImages}
//                       onDisconnect={() => fetchTelegramStatus()}
//                     />
//                   </div>
//                 ) : (
//                   <div className="bg-white shadow rounded-lg p-6 mb-4">
//                     <button onClick={() => setViewType('channels')} className="px-3 py-1 mb-3 bg-gray-200 hover:bg-gray-300 rounded-md text-gray-700 flex items-center">
//                       <FaArrowLeft className="mr-2" /> Back to Channels
//                     </button>
//                     {selectedChannelId !== null && <TelegramImages channelId={selectedChannelId} />}
//                   </div>
//                 )}
//               </>
//             )}
//           </>
//         )}

//         {/* Compliance Section */}
//         <div className="mt-16 p-8 bg-secondary text-white rounded-xl flex items-start space-x-4">
//           <FaShieldAlt className="text-2xl mt-1" />
//           <div className="text-sm leading-relaxed">
//             <h4 className="font-semibold mb-2">Our Commitment to Ethics:</h4>
//             <p>
//               ProcessVenue adheres to the highest standards of data privacy and ethical practices. We're committed to compliant and transparent data handling, respecting all privacy regulations, and never engaging in unauthorized data collection. Your trust is our priority—we safeguard it through ethical operations and strict adherence to our <a href="#" className="underline font-medium">Terms & Conditions</a>.
//             </p>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
