# AI Models Schemas Package - Simplified for Actual Use Case
# Only includes schemas that match the actual database tables and annotation workflow

# Import all schemas for easy access
from .shared_schemas import *
from .model_execution_schemas import *

# Re-export only relevant schema classes
__all__ = [
    # Shared schemas
    "PaginationInfo",
    "SuccessResponse",
    "ErrorResponse",
    "JsonDict",
    
    # Enums from database models
    "DeploymentStatus",
    "ExecutionStatus",
    
    # AI Models Registry schemas
    "AIModelCreate",
    "AIModelUpdate", 
    "AIModelSummary",
    "AIModelResponse",
    "AIModelListResponse",
    
    # Model Execution schemas
    "ModelExecutionCreate",
    "ModelExecutionUpdate",
    "HumanVerificationUpdate",
    "ModelExecutionSummary",
    "ModelExecutionResponse",
    "ExecutionListResponse"
]