from typing import List, Dict, Optional, Literal
from pydantic_settings import BaseSettings
from pydantic import Field, EmailStr, field_validator, ConfigDict
from datetime import datetime
import os
from functools import lru_cache
from dotenv import load_dotenv
from enum import Enum

# Load environment variables from .env file
load_dotenv()

class UserRole(str, Enum):
    ADMIN = "admin"
    ANNOTATOR = "annotator"
    AUDITOR = "auditor"

class AnnotationMode(str, Enum):
    ANNOTATION = "annotation"
    VERIFICATION = "verification"
    SUPERVISION = "supervision"

class UserConfig(BaseSettings):
    """User configuration model"""
    username: str
    password: str
    role: UserRole
    full_name: str
    email: Optional[EmailStr] = None
    is_active: bool = True
    annotation_mode: AnnotationMode = AnnotationMode.ANNOTATION

class PathSettings(BaseSettings):
    """Path configuration settings"""
    image_folders: List[str] = Field(default=[])
    labels_root: str = Field(default="")
    labels_folder_template: str = Field(default="labels_%Y%m%d_%H%M%S")
    base_directory: str = Field(default="")



    @field_validator('image_folders', 'labels_root', 'base_directory')
    def validate_paths(cls, v):
        if isinstance(v, str) and v:
            return os.path.normpath(v)
        elif isinstance(v, list):
            return [os.path.normpath(p) for p in v if p]
        return v

class NASSettings(BaseSettings):
    """NAS configuration settings"""
    url: str = Field(default=os.environ.get('NAS_URL', ''))
    type: str = Field(default=os.environ.get('NAS_TYPE', 'ftp'))
    nas_username: str = Field(default=os.environ.get('NAS_USERNAME', ''))
    nas_password: str = Field(default=os.environ.get('NAS_PASSWORD', ''))
    
    @property
    def is_connected(self) -> bool:
        return bool(self.url and self.nas_username and self.nas_password)
    
    def get_connection_settings(self) -> Dict:
        host = ''
        port = 21

        if self.url:
            if self.url.startswith(('http://', 'https://', 'ftp://')):
                host_part = self.url.split('://', 1)[1]
                host = host_part.split('/', 1)[0]
                host = host.split(':', 1)[0]
            else:
                host = self.url.split(':', 1)[0]

        return {
            'type': 'ftp',
            'host': host,
            'port': port,
            'use_https': False,
            'admin_username': self.nas_username,
            'admin_password': self.nas_password,
            'base_url': self.url
        }

class MinIOSettings(BaseSettings):
    """MinIO configuration settings"""
    endpoint: str = Field(default=os.environ.get('MINIO_ENDPOINT', ''))
    access_key: str = Field(default=os.environ.get('MINIO_ACCESS_KEY', ''))
    secret_key: str = Field(default=os.environ.get('MINIO_SECRET_KEY', ''))
    bucket_name: str = Field(default=os.environ.get('MINIO_BUCKET_NAME', 'default-bucket'))
    csv_bucket_name: str = Field(default=os.environ.get('MINIO_CSV_BUCKET_NAME', 'csv-uploads'))
    secure: bool = Field(default=os.environ.get('MINIO_SECURE', 'false').lower())
    region: Optional[str] = Field(default=os.environ.get('MINIO_REGION', 'us-east-1'))
    timeout: int = Field(default=int(os.environ.get('MINIO_TIMEOUT', '30')))
    max_retries: int = Field(default=int(os.environ.get('MINIO_MAX_RETRIES', '3')))
    retry_delay: int = Field(default=int(os.environ.get('MINIO_RETRY_DELAY', '1')))
    
    @property
    def is_connected(self) -> bool:
        return bool(self.endpoint and self.access_key and self.secret_key)
    
    def get_connection_settings(self) -> Dict:
        return {
            'endpoint': self.endpoint,
            'access_key': self.access_key,
            'secret_key': self.secret_key,
            'bucket_name': self.bucket_name,
            'csv_bucket_name': self.csv_bucket_name,
            'secure': self.secure,
            'region': self.region,
            'timeout': self.timeout,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay
        }

class RedisSettings(BaseSettings):
    """Redis configuration settings"""
    enabled: bool = Field(default=True)
    host: str = Field(default="***********")
    port: int = Field(default=6379)
    db: int = Field(default=0)
    password: Optional[str] = Field(default=None)
    image_cache_ttl: int = Field(default=300)
    batch_cache_ttl: int = Field(default=300)
    # Admin cache TTL settings (in seconds)
    admin_batch_status_ttl: int = Field(default=300, env="REDIS_ADMIN_BATCH_STATUS_TTL")
    admin_user_list_ttl: int = Field(default=300, env="REDIS_ADMIN_USER_LIST_TTL")
    admin_user_details_ttl: int = Field(default=300, env="REDIS_ADMIN_USER_DETAILS_TTL")
    admin_directory_ttl: int = Field(default=120, env="REDIS_ADMIN_DIRECTORY_TTL")
    admin_stats_ttl: int = Field(default=300, env="REDIS_ADMIN_STATS_TTL")
    # Auth cache TTL settings
    auth_user_role_ttl: int = Field(default=3600, env="REDIS_AUTH_USER_ROLE_TTL")
    auth_user_data_ttl: int = Field(default=1800, env="REDIS_AUTH_USER_DATA_TTL")
    # Fetch data cache TTL settings
    fetch_channels_ttl: int = Field(default=300, env="REDIS_FETCH_CHANNELS_TTL")
    fetch_channel_images_ttl: int = Field(default=300, env="REDIS_FETCH_CHANNEL_IMAGES_TTL")
    fetch_channel_dates_ttl: int = Field(default=600, env="REDIS_FETCH_CHANNEL_DATES_TTL")
    fetch_channel_analytics_ttl: int = Field(default=1800, env="REDIS_FETCH_CHANNEL_ANALYTICS_TTL")
    fetch_auth_status_ttl: int = Field(default=300, env="REDIS_FETCH_AUTH_STATUS_TTL")
    # Supervision cache TTL settings
    supervision_document_status_ttl: int = Field(default=60, env="REDIS_SUPERVISION_DOCUMENT_STATUS_TTL")
    supervision_drive_files_ttl: int = Field(default=300, env="REDIS_SUPERVISION_DRIVE_FILES_TTL")
    supervision_drive_folders_ttl: int = Field(default=600, env="REDIS_SUPERVISION_DRIVE_FOLDERS_TTL")
    supervision_folder_contents_ttl: int = Field(default=300, env="REDIS_SUPERVISION_FOLDER_CONTENTS_TTL")
    # Auditor cache TTL settings
    auditor_review_list_ttl: int = Field(default=600, env="REDIS_AUDITOR_REVIEW_LIST_TTL")
    auditor_batch_details_ttl: int = Field(default=600, env="REDIS_AUDITOR_BATCH_DETAILS_TTL")
    auditor_stats_ttl: int = Field(default=600, env="REDIS_AUDITOR_STATS_TTL")
    auditor_available_tasks_ttl: int = Field(default=600, env="REDIS_AUDITOR_AVAILABLE_TASKS_TTL")
    auditor_task_details_ttl: int = Field(default=600, env="REDIS_AUDITOR_TASK_DETAILS_TTL")
    auditor_verification_modes_ttl: int = Field(default=600, env="REDIS_AUDITOR_VERIFICATION_MODES_TTL")
    auditor_datasets_ttl: int = Field(default=600, env="REDIS_AUDITOR_DATASETS_TTL")
    auditor_verifiers_ttl: int = Field(default=600, env="REDIS_AUDITOR_VERIFIERS_TTL")
    auditor_verification_files_ttl: int = Field(default=600, env="REDIS_AUDITOR_VERIFICATION_FILES_TTL")
    auditor_selection_state_ttl: int = Field(default=600, env="REDIS_AUDITOR_SELECTION_STATE_TTL")

class JWTSettings(BaseSettings):
    """JWT configuration settings"""
    secret_key: str = Field(default="datp-sas-secret-key-change-in-production")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=60)  # 1 hour
    refresh_token_expire_days: int = Field(default=7)     # 7 days

class AnnotationSettings(BaseSettings):
    """Annotation configuration settings"""
    supervision_instructions: Optional[str] = Field(default=None)
    max_images_per_batch: int = Field(default=50)
    allowed_image_extensions: List[str] = Field(default=['.png', '.jpg', '.jpeg', '.gif', '.bmp'])

class CORSSettings(BaseSettings):
    """CORS configuration settings"""
    # Allow only the frontend origin when using credentials
    allow_origins: List[str] = Field(default=["http://localhost:3000", "http://127.0.0.1:3000"])
    allow_credentials: bool = Field(default=True)
    allow_methods: List[str] = Field(default=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"])
    allow_headers: List[str] = Field(default=["Authorization", "Content-Type", "Accept", "Origin", "User-Agent", "DNT", "Cache-Control", "X-Mx-ReqToken", "Keep-Alive", "X-Requested-With", "If-Modified-Since"])

class APISettings(BaseSettings):
    """API configuration settings"""
    url: str = Field(default=os.environ.get('API_URL', ''))
    key: str = Field(default=os.environ.get('API_KEY', ''))
    ocr_url: str = Field(default=os.environ.get('OCR_API_URL', ''))
    ocr_key: str = Field(default=os.environ.get('OCR_API_KEY', ''))
    audio_url: str = Field(default=os.environ.get('AUDIO_API_URL', '').rstrip('/'))
    audio_key: str = Field(default=os.environ.get('AUDIO_API_KEY', ''))
    wrapper_url: str = Field(default=os.environ.get('WRAPPER_URL', ''))

    @property
    def headers(self) -> Dict[str, str]:
        return {"X-API-Key": self.key}
    
    @property
    def ocr_headers(self) -> Dict[str, str]:
        return {"X-API-Key": self.ocr_key}
        
    # @property
    # def model_headers(self) -> Dict[str, str]:
    #     return {"Authorization": f"Bearer {self.model_key}"}
        
    @property
    def audio_headers(self) -> Dict[str, str]:
        return {"Authorization": f"Bearer {self.audio_key}"}

class Settings(BaseSettings):
    """Main application settings"""
    # ignore extra env vars (e.g. INTEG_FTP_*) to avoid validation errors
    model_config = ConfigDict(env_file=".env", case_sensitive=False, extra="ignore")

    # App settings
    app_name: str = Field(default="DATP Data Annotation Platform")
    debug: bool = Field(default=True)
    api_version: str = Field(default="v1")
    host: str = Field(default=os.environ.get('HOST', '0.0.0.0'))
    port: int = Field(default=os.environ.get('PORT', 5000))
    api_prefix: str = Field(default="/api")
    version: str = Field(default="1.0.0")
    


    # API settings
    api_settings: APISettings = Field(default_factory=APISettings)
    
    # Common settings
    items_per_page: int = Field(default=50)
    upload_methods: List[str] = Field(default=['ftp', 'webdav', 'api'])
    upload_timeout: int = Field(default=10)
    max_upload_attempts: int = Field(default=3)
    
    # File settings
    upload_folder: str = Field(default=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads'))
    max_content_length: int = Field(default=16 * 1024 * 1024)
    cleanup_after_processing: bool = Field(default=True)
    cleanup_interval: int = Field(default=3600)
    
    # Subsettings
    path_settings: PathSettings = Field(default_factory=PathSettings)
    nas_settings: NASSettings = Field(default_factory=NASSettings)
    minio_settings: MinIOSettings = Field(default_factory=MinIOSettings)
    redis_settings: RedisSettings = Field(default_factory=RedisSettings)
    jwt_settings: JWTSettings = Field(default_factory=JWTSettings)
    annotation_settings: AnnotationSettings = Field(default_factory=AnnotationSettings)
    cors_settings: CORSSettings = Field(default_factory=CORSSettings)

    def get_session_folder(self) -> str:
        """Generate a unique folder name for the current session"""
        return datetime.now().strftime(self.path_settings.labels_folder_template)

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()

# Create a global settings instance
settings = get_settings()

# For backward compatibility
get_session_folder = settings.get_session_folder
get_nas_settings = settings.nas_settings.get_connection_settings
PATH_SETTINGS = settings.path_settings.model_dump()
ITEMS_PER_PAGE = settings.items_per_page
MAX_IMAGES_TO_ANNOTATE = settings.annotation_settings.max_images_per_batch
ALLOWED_IMAGE_EXTENSIONS = settings.annotation_settings.allowed_image_extensions
UPLOAD_METHODS = settings.upload_methods
UPLOAD_TIMEOUT = settings.upload_timeout
MAX_UPLOAD_ATTEMPTS = settings.max_upload_attempts
NAS_SETTINGS = settings.nas_settings.get_connection_settings()
REDIS_SETTINGS = settings.redis_settings.model_dump()
