"use client";

import React, { useState, useEffect, FormEvent } from "react";
import { FaT<PERSON>gram, FaKey } from "react-icons/fa";
import { BASE_URL, TelegramConnectProps, checkTelegramAuth } from "./types";

type SessionMsgType = "info" | "success" | "warning" | "danger";
type StatusMsgType = "info" | "success" | "warning" | "error";

interface SessionMsg {
  message: string;
  type: SessionMsgType;
}
interface StatusMsg {
  message: string;
  type: StatusMsgType;
}

export default function TelegramConnect({ onConnected }: TelegramConnectProps) {
  const [apiId, setApiId] = useState("");
  const [apiHash, setApiHash] = useState("");
  const [phone, setPhone] = useState("");
  const [stage, setStage] = useState<"connect" | "code" | "password">(
    "connect"
  );
  const [sessionMsg, setSessionMsg] = useState<SessionMsg>({
    message: "Checking session...",
    type: "info",
  });
  const [statusMsg, setStatusMsg] = useState<StatusMsg | null>(null);
  const [loading, setLoading] = useState(false);

  // Helper for extracting error messages safely
  const getErrorMessage = (err: unknown) => {
    if (
      err &&
      typeof err === "object" &&
      "message" in err &&
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      typeof (err as any).message === "string"
    ) {
      return (err as { message: string }).message;
    }
    return "Unknown error";
  };

  useEffect(() => {
    (async function checkSession() {
      setLoading(true);
      try {
        const data = await checkTelegramAuth();
        if (data.authenticated) {
          setSessionMsg({
            message: `Connected as ${data.username || "User"}`,
            type: "success",
          });
          onConnected?.();
        } else {
          setSessionMsg({
            message: "No active session. Please connect.",
            type: "info",
          });
        }
      } catch (err: unknown) {
        console.error(err);
        setSessionMsg({ message: "Error checking session", type: "danger" });
        setStatusMsg({
          message: `Error: ${getErrorMessage(err)}`,
          type: "error",
        });
      } finally {
        setLoading(false);
      }
    })();
  }, [onConnected]);

  const showStatus = (message: string, type: StatusMsgType) => {
    setStatusMsg({ message, type });
    setTimeout(() => setStatusMsg(null), 5000);
  };

  const handleConnect = async (e: FormEvent) => {
    e.preventDefault();
    if (!apiId || !apiHash || !phone) {
      showStatus("Please fill all fields", "warning");
      return;
    }
    setLoading(true);
    setSessionMsg({ message: "Connecting to Telegram...", type: "info" });
    try {
      const res = await fetch(`${BASE_URL}/telegram/connect`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({
          api_id: Number(apiId),
          api_hash: apiHash,
          phone,
        }),
      });
      const data = await res.json();
      switch (data.status) {
        case "code_sent":
          setStage("code");
          showStatus(
            "Enter the verification code sent to your Telegram",
            "info"
          );
          break;
        case "password_required":
          setStage("password");
          showStatus("Enter your 2FA password", "info");
          break;
        case "connected":
          setSessionMsg({
            message: `Connected as ${data.username || "User"}`,
            type: "success",
          });
          showStatus("Connected!", "success");
          onConnected?.();
          break;
        default:
          setSessionMsg({ message: "Connection failed", type: "danger" });
          showStatus(`Failed to connect: ${data.message}`, "error");
      }
    } catch (err: unknown) {
      console.error(err);
      setSessionMsg({ message: "Connection error", type: "danger" });
      showStatus(`Error: ${getErrorMessage(err)}`, "error");
    } finally {
      setLoading(false);
    }
  };

  // const handleVerifyCode = async (e: FormEvent) => {
  //   e.preventDefault();
  //   const codeInput = document.getElementById(
  //     "code"
  //   ) as HTMLInputElement | null;
  //   const code = codeInput?.value;
  //   if (!code) {
  //     showStatus("Please enter the verification code", "warning");
  //     return;
  //   }
  //   setLoading(true);
  //   try {
  //     const res = await fetch(`${}/telegram/verify-code`, {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //       credentials: "include",
  //       body: JSON.stringify({ code }),
  //     });

  //     const data = await res.json();
  //     if (!res.ok) {
  //       showStatus(
  //         data.detail || data.message || "Verification failed",
  //         "error"
  //       );
  //       return;
  //     }

  //     if (data.status === "password_required") {
  //       setStage("password");
  //       showStatus("Enter your 2FA password", "info");
  //     } else if (data.status === "connected") {
  //       setSessionMsg({
  //         message: `Connected as ${data.username || "User"}`,
  //         type: "success",
  //       });
  //       showStatus("Connected!", "success");
  //       onConnected?.();
  //     } else {
  //       showStatus(`Verification failed: ${data.message}`, "error");
  //     }
  //   } catch (err: unknown) {
  //     console.error(err);
  //     showStatus(`Network error: ${getErrorMessage(err)}`, "error");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleVerifyPassword = async (e: FormEvent) => {
    e.preventDefault();
    const pwdInput = document.getElementById(
      "password"
    ) as HTMLInputElement | null;
    const password = pwdInput?.value;
    if (!password) {
      showStatus("Please enter the 2FA password", "warning");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch(`${BASE_URL}/telegram/verify-password`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify({ password }),
      });
      const data = await res.json();

      if (!res.ok) {
        showStatus(
          data.detail || data.message || "Password verification failed",
          "error"
        );
        return;
      }

      if (data.status === "connected") {
        setSessionMsg({
          message: `Connected as ${data.username || "User"}`,
          type: "success",
        });
        showStatus("Connected!", "success");
        onConnected?.();
      } else {
        showStatus(`Password verification failed: ${data.message}`, "error");
      }
    } catch (err: unknown) {
      console.error(err);
      showStatus(`Network error: ${getErrorMessage(err)}`, "error");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (stage === "code") {
      setTimeout(() => {
        const codeInput = document.getElementById("code");
        codeInput?.focus();
      }, 300);
    } else if (stage === "password") {
      setTimeout(() => {
        const pwdInput = document.getElementById("password");
        pwdInput?.focus();
      }, 300);
    }
  }, [stage]);

  const renderLoadingOverlay = () =>
    loading ? (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500"></div>
      </div>
    ) : null;

  return (
    <>
      {renderLoadingOverlay()}
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="bg-primary px-6 py-3 flex items-center">
            <FaTelegram className="text-white mr-2" />
            <h2 className="text-lg font-semibold text-white">
              Connect to Telegram
            </h2>
          </div>
          <div className="p-6">
            <div
              className={`mb-4 flex items-center px-4 py-2 rounded ${
                {
                  info: "bg-blue-100 text-blue-800",
                  success: "bg-green-100 text-green-800",
                  warning: "bg-yellow-100 text-yellow-800",
                  danger: "bg-red-100 text-red-800",
                }[sessionMsg.type]
              }`}
            >
              <i
                className={`mr-2 ${
                  sessionMsg.type === "success"
                    ? "fas fa-check-circle"
                    : sessionMsg.type === "warning"
                    ? "fas fa-exclamation-triangle"
                    : "fas fa-exclamation-circle"
                }`}
              ></i>
              <span>{sessionMsg.message}</span>
            </div>
            {statusMsg && (
              <div
                className={`mb-4 px-4 py-2 rounded ${
                  {
                    info: "bg-blue-100 text-blue-800",
                    success: "bg-green-100 text-green-800",
                    warning: "bg-yellow-100 text-yellow-800",
                    error: "bg-red-100 text-red-800",
                  }[statusMsg.type]
                }`}
              >
                {statusMsg.message}
              </div>
            )}
            {stage === "connect" && (
              <form onSubmit={handleConnect} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API ID
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={apiId}
                    onChange={(e) => setApiId(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API Hash
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={apiHash}
                    onChange={(e) => setApiHash(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="text"
                    className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                </div>
                <div className="flex items-center justify-between mt-6">
                  <button
                    type="submit"
                    className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2"
                    disabled={loading}
                  >
                    {loading ? (
                      "Connecting..."
                    ) : (
                      <>
                        <FaTelegram className="mr-2" /> Connect
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
            {/* {stage === "code" && (
              <form onSubmit={handleVerifyCode} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Verification Code
                  </label>
                  <input
                    id="code"
                    type="text"
                    className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2"
                  disabled={loading}
                >
                  {loading ? (
                    "Verifying..."
                  ) : (
                    <>
                      <FaKey className="mr-2" /> Verify Code
                    </>
                  )}
                </button>
              </form>
            )}
            {stage === "password" && (
              <form onSubmit={handleVerifyPassword} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    2FA Password
                  </label>
                  <input
                    id="password"
                    type="password"
                    className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white rounded px-4 py-2"
                  disabled={loading}
                >
                  {loading ? (
                    "Verifying..."
                  ) : (
                    <>
                      <FaKey className="mr-2" /> Verify Password
                    </>
                  )}
                </button>
              </form>
            )} */}
          </div>
        </div>
      </div>
    </>
  );
}
