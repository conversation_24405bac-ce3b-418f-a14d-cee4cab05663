  // components/About.tsx
  'use client';

  import { motion } from 'framer-motion';

  export default function About() {
    return (
      <section id="about" className="py-24 bg-gradient-to-b from-blue-50 to-white">
        <div className="max-w-6xl mx-auto px-6">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-center mb-12"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            About ProcessVenue
          </motion.h2>

          <motion.p
            className="text-lg text-gray-700 leading-relaxed text-center max-w-3xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            ProcessVenue is a global business-process outsourcing partner specialising in data annotation, document processing, and intelligent automation. Combining deep domain expertise with cutting-edge AI, we help companies unlock the full potential of their data and accelerate time-to-insight.
          </motion.p>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.1 } },
            }}
          >
            {[
              {
                title: 'Domain Expertise',
                description: 'A decade of experience across finance, healthcare, legal and e-commerce means we speak your language and understand your challenges.',
              },
              {
                title: 'Scalable Workforce',
                description: 'A vetted pool of annotators and reviewers ready to ramp up or down based on project needs, ensuring consistent quality and quick turnaround.',
              },
              {
                title: 'Secure Infrastructure',
                description: 'ISO-certified facilities, rigorous data-privacy controls and full audit trails keep your sensitive information protected.',
              },
            ].map(({ title, description }, i) => (
              <motion.div
                key={i}
                className="bg-gray-100 p-8 rounded-xl shadow-[8px_8px_16px_#c1c8e4,-8px_-8px_16px_#ffffff]"
                whileHover={{ y: -5, boxShadow: '8px 8px 16px #c1c8e4, -8px -8px 16px #ffffff' }}
              >
                <h3 className="text-xl font-semibold mb-4 text-gray-800 text-center md:text-left">
                  {title}
                </h3>
                <p className="text-gray-700 leading-relaxed text-center md:text-left">
                  {description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>
    );
  }
