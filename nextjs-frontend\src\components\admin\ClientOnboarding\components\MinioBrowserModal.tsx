// components/MinioBrowserModal.tsx
import React from 'react';
import { FaDatabase, FaFolder, FaFile, FaCheckCircle } from 'react-icons/fa';
import { Directory, SelectionTarget } from '../types';
import { generateBreadcrumbs } from '../utils/helpers';

interface MinioBrowserModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentBrowsePath: string;
  currentSelection: string;
  isSelectingFile: boolean;
  directoryContents: Directory[];
  isLoadingDirectory?: boolean;
  onSelectItem: (item: Directory) => void;
  onBreadcrumbClick: (path: string) => void;
  onSelectPath: () => void;
  currentSelectionTarget: SelectionTarget;
  bucketName?: string;
}

export const MinioBrowserModal: React.FC<MinioBrowserModalProps> = ({
  isOpen,
  onClose,
  currentBrowsePath,
  currentSelection,
  isSelectingFile,
  directoryContents,
  isLoadingDirectory = false,
  onSelectItem,
  onBreadcrumbClick,
  onSelectPath,
  currentSelectionTarget,
  bucketName = "bucket",
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal fade show d-block" tabIndex={-1} style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog modal-lg modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header bg-pink-50 border-b border-pink-200">
            <h5 className="modal-title flex items-center text-pink-700">
              MinIO Object Browser
              {bucketName && (
                <span className="ml-2 px-2 py-1 bg-pink-100 text-pink-600 text-xs rounded-full">
                  {bucketName}
                </span>
              )}
            </h5>
            <button type="button" className="btn-close" onClick={onClose}></button>
          </div>
          <div className="modal-body">
            {/* Selection mode indicator */}
            <div className="alert alert-info py-2 mb-3 bg-pink-50 border border-pink-200">
              <small className="text-pink-700">
                <strong>Mode:</strong> {isSelectingFile ? 'Select an object' : 'Select a prefix/folder'}
              </small>
            </div>
            
            {/* Breadcrumb navigation */}
            <nav aria-label="breadcrumb" className="mb-3">
              <ol className="breadcrumb mb-0">
                <li className="breadcrumb-item">
                  <button 
                    className="btn btn-link p-0 text-decoration-none text-pink-600"
                    onClick={() => onBreadcrumbClick('/')}
                  >
                    <FaDatabase className="me-1" />
                    {bucketName}
                  </button>
                </li>
                {generateBreadcrumbs(currentBrowsePath).slice(1).map((crumb, index) => (
                  <li key={index + 1} className="breadcrumb-item">
                    <button 
                      className="btn btn-link p-0 text-decoration-none text-pink-600"
                      onClick={() => onBreadcrumbClick(crumb.path)}
                    >
                      {crumb.name}
                    </button>
                  </li>
                ))}
              </ol>
            </nav>
            
            {/* Directory contents */}
            <div className="border rounded p-3 mb-3 border-pink-200" style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {isLoadingDirectory ? (
                <div className="text-center py-4">
                  <div className="spinner-border text-pink-600" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <p className="mt-2 text-muted">Loading objects...</p>
                </div>
              ) : directoryContents.length === 0 ? (
                <p className="text-center text-muted py-4">This location is empty.</p>
              ) : (
                <div className="list-group">
                  {directoryContents.map((item, index) => (
                    <button
                      key={index}
                      type="button"
                      className={`list-group-item list-group-item-action d-flex align-items-center ${
                        (isSelectingFile && item.type === 'file' && currentSelection === item.path) ||
                        (!isSelectingFile && currentBrowsePath === item.path)
                          ? 'active bg-pink-100 border-pink-300'
                          : 'hover:bg-pink-50'
                      }`}
                      onClick={() => onSelectItem(item)}
                      disabled={!isSelectingFile && item.type === 'file'}
                    >
                      {item.type === 'directory' ? (
                        <FaFolder className="me-2 text-pink-600" />
                      ) : (
                        <FaFile className="me-2 text-pink-400" />
                      )}
                      <span className="flex-grow-1 text-left">{item.name}</span>
                      {item.type === 'directory' && (
                        <small className="text-muted">Prefix</small>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
            
            {/* Current selection display */}
            <div className="mb-3">
              <label className="form-label text-pink-700">Selected Path:</label>
              <div className="form-control border-pink-200" style={{ fontSize: '0.875rem' }}>
                {isSelectingFile ? currentSelection || currentBrowsePath : currentBrowsePath}
              </div>
            </div>
          </div>
          
          <div className="modal-footer">
            <button 
              type="button" 
              className="btn btn-secondary"
              onClick={onClose}
            >
              Cancel
            </button>
            <button 
              type="button" 
              className="btn btn-primary bg-pink-600 border-pink-600 hover:bg-pink-700"
              onClick={onSelectPath}
              disabled={isSelectingFile && !currentSelection}
            >
              <FaCheckCircle className="me-1" />
              Select {isSelectingFile ? 'Object' : 'Prefix'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
