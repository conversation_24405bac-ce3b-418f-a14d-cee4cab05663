"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaArrowLeft,
  FaArrowRight,
  FaSave,
  FaPencilAlt,
  FaTimes,
  FaSearchMinus,
  FaSearchPlus,
  FaUndo,
} from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";
import { useRouter } from "next/navigation";
import { showToast } from "@/lib/toast";
import Image from "next/image";
import { API_BASE_URL } from "@/lib/api";

interface VerificationModeProps {
  adminInstructions?: string;
  folder?: string;
  isAdmin?: boolean;
}

interface ImageApiResponse {
  url: string;
  name: string;
  label?: string;
}
interface ImageData {
  url: string;
  name: string;
  label: string;
  verified: boolean;
  edited: boolean;
}

const API_BASE = API_BASE_URL;

export default function VerificationMode({
  adminInstructions = "",
  folder = "",
}: VerificationModeProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imagesData, setImagesData] = useState<ImageData[]>([]);
  const [fetchedInstructions, setFetchedInstructions] =
    useState<string>(adminInstructions);
  const [batchName, setBatchName] = useState<string>(folder);
  const [editMode, setEditMode] = useState(false);
  const [editedLabel, setEditedLabel] = useState("");
  const [zoomLevel, setZoomLevel] = useState(100);
  const [completedCount, setCompletedCount] = useState(0);
  const [noTasks, setNoTasks] = useState(false);

  const imageRef = useRef<HTMLImageElement>(null);
  const router = useRouter();

  // Memoize loadBatch so it's stable for useEffect deps
  const loadBatch = useCallback(async () => {
    setNoTasks(false);
    try {
      const annotateUrl = `${API_BASE}/annotator/annotate`;
      let resp = await authFetch(annotateUrl, { credentials: "include" });

      if (resp.status === 204) {
        setNoTasks(true);
        return;
      }
      if (!resp.ok) {
        const err = await resp.json().catch(() => ({}));
        throw new Error(
          err.detail || err.message || `Server error: ${resp.status}`
        );
      }
      let data = await resp.json();
      if (!data.images || data.images.length === 0) {
        const nextResp = await authFetch(`${API_BASE}/annotator/next-set`, {
          credentials: "include",
        });
        if (!nextResp.ok) {
          const err2 = await nextResp.json().catch(() => ({}));
          showToast.warning(
            err2.message || err2.detail || "No new batch available"
          );
          return;
        }
        resp = await authFetch(`${API_BASE}/annotator/annotate`, {
          credentials: "include",
        });
        if (resp.status === 204) {
          setNoTasks(true);
          return;
        }
        if (!resp.ok) {
          const err3 = await resp.json().catch(() => ({}));
          throw new Error(
            err3.detail || err3.message || `Server error: ${resp.status}`
          );
        }
        data = await resp.json();
      }
      const mappedImages = (data.images as ImageApiResponse[]).map((img) => {
        let path = img.url;
        if (!path.startsWith("http")) {
          if (path.startsWith("/api")) {
            path = path.replace(/^\/api/, "");
          }
          path = `${API_BASE}${path}`;
        }
        return {
          url: path,
          name: img.name,
          label: img.label || "",
          verified: false,
          edited: false,
        };
      });
      setImagesData(mappedImages);
      setFetchedInstructions(data.instructions || "");
      setBatchName(data.batch_name || folder);
      setCurrentImageIndex(0);
      setCompletedCount(0);
      setEditMode(false);
    } catch (err) {
      console.error("Error loading verification batch:", err);
      showToast.error("Failed to load batch");
    }
  }, [folder]);

  useEffect(() => {
    loadBatch();
  }, [loadBatch]);

  const handlePrevious = useCallback(() => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex((prev) => prev - 1);
      setEditMode(false);
    }
  }, [currentImageIndex]);

  const handleNext = useCallback(() => {
    if (currentImageIndex < imagesData.length - 1) {
      setCurrentImageIndex((prev) => prev + 1);
      setEditMode(false);
    }
  }, [currentImageIndex, imagesData.length]);

  const handleVerify = useCallback(() => {
    setImagesData((prev) => {
      const updatedData = [...prev];
      if (!updatedData[currentImageIndex].verified) {
        updatedData[currentImageIndex].verified = true;
        setCompletedCount((c) => c + 1);
      }
      return updatedData;
    });
    if (currentImageIndex < imagesData.length - 1) {
      handleNext();
    }
  }, [currentImageIndex, imagesData.length, handleNext]);

  const handleEdit = useCallback(() => {
    setEditMode(true);
    setEditedLabel(imagesData[currentImageIndex].label);
  }, [currentImageIndex, imagesData]);

  const handleCancelEdit = useCallback(() => setEditMode(false), []);

  const handleSaveEdit = useCallback(() => {
    if (!editedLabel.trim()) return;
    setImagesData((prev) => {
      const updatedData = [...prev];
      updatedData[currentImageIndex].label = editedLabel;
      updatedData[currentImageIndex].edited = true;
      return updatedData;
    });
    setEditMode(false);
  }, [currentImageIndex, editedLabel]);

  const handleSaveAll = useCallback(async () => {
    const labelsMapping: Record<string, string> = {};
    imagesData.forEach((item) => {
      labelsMapping[item.name] = item.label;
    });
    try {
      const response = await authFetch(`${API_BASE}/annotator/save-labels`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          labels: labelsMapping,
          verification_mode: true,
          batch_name: batchName,
        }),
      });
      const result = await response.json();
      if (result.success) {
        showToast.success("All verifications saved successfully!");
      } else {
        showToast.error("Error saving verifications");
      }
    } catch (error) {
      console.error("Error saving verifications:", error);
      showToast.error("Error saving verifications");
    }
  }, [imagesData, batchName]);

  const handleGetNextBatch = useCallback(() => {
    loadBatch();
  }, [loadBatch]);

  const handleZoomIn = () => setZoomLevel((z) => Math.min(z + 10, 200));
  const handleZoomOut = () => setZoomLevel((z) => Math.max(z - 10, 50));
  const handleResetZoom = () => setZoomLevel(100);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (editMode) return;
      if (e.key === "ArrowLeft") handlePrevious();
      else if (e.key === "ArrowRight") handleNext();
      else if (e.key === "Enter") handleVerify();
      else if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        handleSaveAll();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    currentImageIndex,
    editMode,
    imagesData,
    handleNext,
    handlePrevious,
    handleSaveAll,
    handleVerify,
  ]);

  const currentImageData = imagesData[currentImageIndex] || {
    url: "",
    name: "",
    label: "",
    verified: false,
    edited: false,
  };
  const progress =
    imagesData.length > 0
      ? Math.round((completedCount / imagesData.length) * 100)
      : 0;
  const isCompleted =
    completedCount === imagesData.length && imagesData.length > 0;

  if (noTasks) {
    return (
      <div className="card mt-4">
        <div className="card-header bg-info text-white">No Tasks Available</div>
        <div className="card-body">
          <div className="alert alert-info">
            There are currently no verification tasks available for you.
          </div>
          <p>You are currently in verification mode.</p>
          <p>You can:</p>
          <ul>
            <li>Wait for an administrator to assign new tasks</li>
            <li>Contact your administrator for assistance</li>
          </ul>
          <button
            className="btn btn-primary"
            onClick={() => router.push("/annotator?view=dashboard")}
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  if (imagesData.length === 0) {
    return (
      <div
        className="d-flex justify-content-center align-items-center h-96"
      >
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <div>Loading verification batch...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-[280px_1fr_240px] gap-5 mt-5">
      {/* Left sidebar: Task Guidelines */}
      <div className="guidelines-sidebar">
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h5 className="mb-0">Task Guidelines</h5>
          </div>
          <div className="card-body p-3">
            {fetchedInstructions ? (
              <div
                className="instructions-content"
                dangerouslySetInnerHTML={{ __html: fetchedInstructions }}
              />
            ) : (
              <p className="text-muted fst-italic">
                No specific instructions have been provided for this
                verification task.
              </p>
            )}
            <div className="best-practices mt-4">
              <h6 className="fw-bold text-primary mb-2">Best Practices</h6>
              <ul className="list-unstyled">
                <li>
                  <FaCheck className="text-success me-2" />
                  Verify label accuracy against the image
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Edit labels only when necessary
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Be consistent with your approach
                </li>
                <li>
                  <FaCheck className="text-success me-2" />
                  Use keyboard shortcuts for efficiency
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Main verification area */}
      <div className="verification-container">
        <div className="verification-main">
          <div className="bg-blue-50 text-blue-600 px-4 py-2 rounded flex items-center mb-4 font-medium">
            <FaCheck className="me-1" /> Label Verification Mode
          </div>
          <div id="imageDisplay" className="relative bg-gray-50 rounded-lg shadow-md h-96 flex justify-center items-center overflow-hidden">
            <div className="h-full flex justify-center items-center overflow-hidden">
              {currentImageData.url ? (
                <Image
                  ref={imageRef}
                  id="currentImage"
                  src={currentImageData.url}
                  alt="Loading image..."
                  width={600}
                  height={400}
                  style={{
                    objectFit: "contain",
                    transform: `scale(${zoomLevel / 100})`,
                  }}
                  unoptimized
                  priority
                />
              ) : (
                <div className="d-flex align-items-center justify-content-center h-100">
                  <div className="text-muted">Loading image...</div>
                </div>
              )}
            </div>
            {currentImageData.verified && (
              <span className="absolute top-2.5 left-2.5 bg-green-600/90 text-white px-2.5 py-1.5 rounded text-sm flex items-center">
                <FaCheck className="me-1" /> Verified
              </span>
            )}
            <div className="absolute bottom-4 right-4 bg-white/80 rounded-full px-2.5 py-1.5 flex items-center z-10">
              <div
                className="cursor-pointer p-1.5"
                onClick={handleZoomOut}
                title="Zoom Out (-)"
              >
                <FaSearchMinus />
              </div>
              <div className="mx-2 text-sm">{zoomLevel}%</div>
              <div
                className="cursor-pointer p-1.5"
                onClick={handleZoomIn}
                title="Zoom In (+)"
              >
                <FaSearchPlus />
              </div>
              <div
                className="cursor-pointer p-1.5"
                onClick={handleResetZoom}
                title="Reset Zoom"
              >
                <FaUndo />
              </div>
            </div>
            <div
              className="progress absolute bottom-0 left-0 right-0 m-0 rounded-none h-1.5"
            >
              <div
                className="progress-bar"
                role="progressbar"
                style={{ width: `${progress}%` }}
                aria-valuenow={progress}
                aria-valuemin={0}
                aria-valuemax={100}
              ></div>
            </div>
          </div>

          <div className="text-center mt-2.5 text-sm text-gray-500 mb-2">
            <span>{completedCount}</span> of <span>{imagesData.length}</span>{" "}
            images verified (<span>{progress}%</span>)
          </div>

          <div className="mt-5">
            {!editMode ? (
              <div className="bg-gray-50 border border-gray-300 rounded p-4 mb-5 flex justify-between items-center">
                <div className="text-base font-medium">
                  {currentImageData.label || (
                    <span className="text-muted">No label available</span>
                  )}
                </div>
                <div className="flex gap-2">
                  <button
                    className="btn btn-sm bg-green-600 text-white"
                    onClick={handleVerify}
                    title="Verify Label (Enter)"
                  >
                    <FaCheck className="me-1" /> Verify
                  </button>
                  <button
                    className="btn btn-sm bg-gray-500 text-white"
                    onClick={handleEdit}
                    title="Edit Label"
                  >
                    <FaPencilAlt className="me-1" /> Edit
                  </button>
                </div>
              </div>
            ) : (
              <div className="mb-5">
                <label htmlFor="editLabelInput" className="form-label">
                  Edit Label
                </label>
                <div className="input-group">
                  <input
                    type="text"
                    className="form-control"
                    id="editLabelInput"
                    placeholder="Enter corrected label"
                    value={editedLabel}
                    onChange={(e) => setEditedLabel(e.target.value)}
                  />
                  <button
                    className="btn btn-primary"
                    onClick={handleSaveEdit}
                    disabled={!editedLabel.trim()}
                  >
                    <FaCheck className="me-1" /> Save
                  </button>
                  <button
                    className="btn btn-secondary"
                    onClick={handleCancelEdit}
                  >
                    <FaTimes className="me-1" /> Cancel
                  </button>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center my-5">
              <button
                className="btn btn-outline-primary min-w-[100px]"
                onClick={handlePrevious}
                disabled={currentImageIndex === 0}
              >
                <FaArrowLeft /> Previous
              </button>

              <span>
                Image {currentImageIndex + 1} of {imagesData.length}
              </span>

              <button
                className="btn btn-outline-primary min-w-[100px]"
                onClick={handleNext}
                disabled={currentImageIndex === imagesData.length - 1}
              >
                Next <FaArrowRight />
              </button>
            </div>

            {/* Save All Verifications and Next Batch section */}
            <div className="mt-8">
              {isCompleted && (
                <div className="alert alert-success mb-2 py-1 px-2">
                  <FaCheck className="me-1" /> All images verified! Ready to
                  save.
                </div>
              )}
              <div className="d-flex justify-content-center items-center space-x-4 mb-4">
                <button
                  className="btn btn-success min-w-[200px]"
                  onClick={handleSaveAll}
                  disabled={!isCompleted}
                  title="Complete verifying all images to enable this button"
                >
                  <FaSave /> Save All Verifications
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleGetNextBatch}
                  disabled={!isCompleted}
                  title={
                    isCompleted
                      ? "Get next batch of images"
                      : "Complete verifying all images to enable this button"
                  }
                >
                  Get Next Set of Images
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right side: Keyboard Shortcuts */}
      <div className="shortcuts-sidebar">
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h5 className="mb-0">Keyboard Shortcuts</h5>
          </div>
          <div className="card-body p-0">
            <div className="p-0">
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Verify label</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">↵ Enter</span>
              </div>
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Previous image</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">← Left</span>
              </div>
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Next image</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">→ Right</span>
              </div>
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Save all verifications</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">Ctrl+S</span>
              </div>
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Zoom in</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">+</span>
              </div>
              <div className="flex justify-between px-4 py-2.5 border-b border-black/5">
                <span className="shortcut-text">Zoom out</span>
                <span className="bg-gray-100 border border-gray-300 rounded px-2 py-0.5 text-xs font-mono">-</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
}
