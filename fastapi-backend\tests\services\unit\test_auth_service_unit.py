"""
Unit tests for AuthService.
Tests security-critical authentication operations including registration, login, and password management.

SECURITY FOCUS:
- User registration validation
- Login authentication flow
- Password hashing and verification  
- Token creation and management
- Input sanitization
- SQL injection prevention
- Rate limiting compliance
- Session management
- Error handling without information leakage
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from app.services.auth_service import AuthService
from app.schemas.UserSchemas import (
    UserRegisterRequest,
    LoginRequest,
    ChangePasswordRequest,
    UserResponse,
    TokenResponse,
    UserCreate
)
from app.post_db.master_models.users import UserRole

class TestAuthService:
    """Test suite for AuthService with focus on security."""

    # ==================================================================
    # USER REGISTRATION TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_user_success(self, mock_db_session, auth_factory, user_factory):
        """Test successful user registration."""
        
        # Use centralized factories
        register_request = auth_factory.create_register_request()
        sample_user_response = user_factory.create_user_response_mock()
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = None  # Username not taken
            
            with patch('app.dependencies.auth.UserService.get_user_by_email') as mock_get_email:
                mock_get_email.return_value = None  # Email not taken
                
                with patch('app.dependencies.auth.UserService.create_user') as mock_create:
                    mock_create.return_value = sample_user_response
                    
                    with patch('app.dependencies.auth.create_user_response') as mock_response:
                        mock_response.return_value = sample_user_response
                        
                        success, result = await AuthService.register_user(
                            mock_db_session,
                            register_request
                        )
                        
                        assert success is True
                        assert isinstance(result, (dict, UserResponse))
                        assert result.get('username') == register_request.username or result.username == register_request.username

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_user_username_exists(self, mock_db_session, auth_factory):
        """Test registration failure when username already exists."""
        
        register_request = auth_factory.create_register_request()
        existing_user = MagicMock()
        existing_user.username = register_request.username
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = existing_user  # Username taken
            
            success, result = await AuthService.register_user(
                mock_db_session,
                register_request
            )
            
            assert success is False
            assert "Username already exists" in result

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_user_email_exists(self, mock_db_session, auth_factory):
        """Test registration failure when email already exists."""

        register_request = auth_factory.create_register_request()
        existing_user = MagicMock()
        existing_user.email = auth_factory.create_register_request().email
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = None  # Username available
            
            with patch('app.dependencies.auth.UserService.get_user_by_email') as mock_get_email:
                mock_get_email.return_value = existing_user  # Email taken
                
                success, result = await AuthService.register_user(
                    mock_db_session,
                    auth_factory.create_register_request()
                )
                
                assert success is False
                assert "Email already registered" in result

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_user_weak_password(self, mock_db_session, auth_factory):
        """Test registration failure with weak password."""

        weak_password_request = auth_factory.create_weak_password_register()
        weak_password_request = UserRegisterRequest(
            username="testuser",
            email="<EMAIL>",
            password="123",  # Weak password
            first_name="Test",
            last_name="User",
            role=UserRole.ANNOTATOR
        )
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = None
            
            with patch('app.dependencies.auth.UserService.get_user_by_email') as mock_get_email:
                mock_get_email.return_value = None
                
                # Mock password validation failure
                with patch('app.core.security.hash_password') as mock_hash:
                    mock_hash.side_effect = ValueError("Password too weak")
                    
                    success, result = await AuthService.register_user(
                        mock_db_session,
                        weak_password_request
                    )
                    
                    assert success is False
                    assert "Password" in result

    # ==================================================================
    # USER LOGIN TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_login_user_success(self, mock_db_session, auth_factory):
        """Test successful user login."""
        
        login_request = auth_factory.create_login_request()
        mock_user = MagicMock()
        mock_user.id = 1
        mock_user.username = login_request.username
        mock_user.password_hash = "hashed_password"
        mock_user.is_active = True
        mock_user.role = UserRole.ANNOTATOR
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = True
                
                with patch('app.core.security.create_access_token') as mock_access_token:
                    mock_access_token.return_value = "mock_access_token"
                    
                    with patch('app.core.security.create_refresh_token') as mock_refresh_token:
                        mock_refresh_token.return_value = "mock_refresh_token"
                        
                        success, result = await AuthService.login_user(
                            mock_db_session,
                            auth_factory.create_login_request()
                        )
                        
                        assert success is True
                        assert isinstance(result, TokenResponse)
                        assert result.access_token == "mock_access_token"
                        assert result.refresh_token == "mock_refresh_token"

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_login_user_not_found(self, mock_db_session, auth_factory):
        """Test login failure when user doesn't exist."""

        login_request = auth_factory.create_login_request()
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = None  # User not found
            
            success, result = await AuthService.login_user(
                mock_db_session,
                auth_factory.create_login_request()
            )
            
            assert success is False
            assert "Invalid credentials" in result  # Generic message for security

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_login_user_wrong_password(self, mock_db_session, auth_factory):
        """Test login failure with wrong password."""

        login_request = auth_factory.create_login_request()
        mock_user = MagicMock()
        mock_user.username = auth_factory.create_login_request().username
        mock_user.password_hash = "correct_hashed_password"
        mock_user.is_active = True
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = False  # Wrong password
                
                success, result = await AuthService.login_user(
                    mock_db_session,
                    auth_factory.create_login_request()
                )
                
                assert success is False
                assert "Invalid credentials" in result  # Generic message for security

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_login_user_inactive_account(self, mock_db_session, auth_factory):
        """Test login failure for inactive user account."""

        login_request = auth_factory.create_login_request()
        mock_user = MagicMock()
        mock_user.username = auth_factory.create_login_request().username
        mock_user.password_hash = "hashed_password"
        mock_user.is_active = False  # Account disabled
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = True  # Correct password
                
                success, result = await AuthService.login_user(
                    mock_db_session,
                    auth_factory.create_login_request()
                )
                
                assert success is False
                assert "Account disabled" in result or "inactive" in result.lower()

    # ==================================================================
    # PASSWORD CHANGE TESTS  
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_change_password_success(self, mock_db_session, auth_factory):
        """Test successful password change."""

        password_change_request = auth_factory.create_password_change_request()
        user_id = 1
        mock_user = MagicMock()
        mock_user.id = user_id
        mock_user.password_hash = "old_hashed_password"
        
        with patch('app.dependencies.auth.UserService.get_user_by_id') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = True  # Old password correct
                
                with patch('app.core.security.hash_password') as mock_hash:
                    mock_hash.return_value = "new_hashed_password"
                    
                    with patch('app.dependencies.auth.UserService.update_user_password') as mock_update:
                        mock_update.return_value = True
                        
                        success, result = await AuthService.change_password(
                            mock_db_session,
                            user_id,
                            auth_factory.create_password_change_request()
                        )
                        
                        assert success is True
                        assert "Password updated successfully" in result

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_change_password_wrong_old_password(self, mock_db_session, auth_factory):
        """Test password change failure with wrong old password."""

        password_change_request = auth_factory.create_password_change_request()
        user_id = 1
        mock_user = MagicMock()
        mock_user.password_hash = "old_hashed_password"
        
        with patch('app.dependencies.auth.UserService.get_user_by_id') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = False  # Old password incorrect
                
                success, result = await AuthService.change_password(
                    mock_db_session,
                    user_id,
                    auth_factory.create_password_change_request()
                )
                
                assert success is False
                assert "Current password is incorrect" in result

    @pytest.mark.unit
    @pytest.mark.asyncio  
    async def test_change_password_same_as_old(self, mock_db_session, auth_factory):
        """Test password change failure when new password is same as old."""

        same_password_request = auth_factory.create_password_change_request(
            old_password="SamePassword123!",
            new_password="SamePassword123!"
        )
        same_password_request = ChangePasswordRequest(
            old_password="SamePassword123!",
            new_password="SamePassword123!"  # Same password
        )
        
        user_id = 1
        mock_user = MagicMock()
        mock_user.password_hash = "hashed_same_password"
        
        with patch('app.dependencies.auth.UserService.get_user_by_id') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                # Both old and new password match existing hash
                mock_verify.return_value = True
                
                success, result = await AuthService.change_password(
                    mock_db_session,
                    user_id,
                    same_password_request
                )
                
                assert success is False
                assert "New password must be different" in result

    # ==================================================================
    # SECURITY TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_sql_injection_prevention_login(self, mock_db_session):
        """Test that SQL injection attempts in login are prevented."""
        
        malicious_login = LoginRequest(
            username="'; DROP TABLE users; --",
            password="password' OR '1'='1"
        )
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            # Should safely handle malicious input and return None
            mock_get_user.return_value = None
            
            success, result = await AuthService.login_user(
                mock_db_session,
                malicious_login
            )
            
            assert success is False
            # Verify that the malicious username was passed to the query safely
            mock_get_user.assert_called_once_with(mock_db_session, malicious_login.username)

    @pytest.mark.unit
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_password_hash_never_returned(self, mock_db_session, auth_factory):
        """Test that password hashes are never included in responses."""
        
        mock_user = MagicMock()
        mock_user.password_hash = "secret_hash"
        mock_user.is_active = True
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = True
                
                with patch('app.core.security.create_access_token') as mock_access_token:
                    mock_access_token.return_value = "token"
                    
                    with patch('app.core.security.create_refresh_token') as mock_refresh_token:
                        mock_refresh_token.return_value = "refresh"
                        
                        success, result = await AuthService.login_user(
                            mock_db_session,
                            auth_factory.create_login_request()
                        )
                        
                        # Convert result to dict for inspection
                        result_dict = result.dict() if hasattr(result, 'dict') else vars(result)
                        
                        # Ensure no password hash in response
                        assert 'password_hash' not in str(result_dict)
                        assert 'password' not in str(result_dict)
                        assert mock_user.password_hash not in str(result_dict)

    @pytest.mark.unit
    @pytest.mark.security
    @pytest.mark.asyncio
    async def test_timing_attack_resistance(self, mock_db_session):
        """Test that login timing is consistent regardless of user existence."""
        
        import time
        
        # Test with existing user (wrong password)
        existing_user_login = LoginRequest(username="existing_user", password="wrong_pass")
        mock_user = MagicMock()
        mock_user.password_hash = "real_hash"
        mock_user.is_active = True
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = False
                
                start_time = time.time()
                success1, result1 = await AuthService.login_user(
                    mock_db_session,
                    existing_user_login
                )
                time1 = time.time() - start_time
        
        # Test with non-existing user
        nonexistent_user_login = LoginRequest(username="fake_user", password="any_pass")
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = None
            
            start_time = time.time()
            success2, result2 = await AuthService.login_user(
                mock_db_session,
                nonexistent_user_login
            )
            time2 = time.time() - start_time
        
        # Both should fail
        assert success1 is False
        assert success2 is False
        
        # Times should be reasonably similar (within 100ms for mocked operations)
        time_diff = abs(time1 - time2)
        assert time_diff < 0.1  # Less than 100ms difference

    # ==================================================================
    # INPUT VALIDATION TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_invalid_email_format(self, mock_db_session):
        """Test registration failure with invalid email format."""
        
        invalid_email_request = UserRegisterRequest(
            username="testuser",
            email="invalid-email-format",
            password="SecurePassword123!",
            first_name="Test",
            last_name="User",
            role=UserRole.ANNOTATOR
        )
        
        # The pydantic model should validate email format
        # This test verifies that invalid emails are rejected at the model level
        try:
            success, result = await AuthService.register_user(
                mock_db_session,
                invalid_email_request
            )
            # If we reach here, the email validation passed when it shouldn't have
            assert False, "Invalid email format should be rejected"
        except ValueError:
            # Expected: pydantic validation should raise ValueError for invalid email
            pass

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_register_special_characters_in_username(self, mock_db_session):
        """Test registration with special characters in username."""
        
        special_char_usernames = [
            "user@name",
            "user#name", 
            "user$name",
            "user!name",
            "user name",  # Space
            "",  # Empty
            "a" * 101  # Too long
        ]
        
        for username in special_char_usernames:
            special_request = UserRegisterRequest(
                username=username,
                email="<EMAIL>",
                password="SecurePassword123!",
                first_name="Test",
                last_name="User",
                role=UserRole.ANNOTATOR
            )
            
            with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
                mock_get_user.return_value = None
                
                with patch('app.dependencies.auth.UserService.get_user_by_email') as mock_get_email:
                    mock_get_email.return_value = None
                    
                    # Should validate username format and reject if invalid
                    success, result = await AuthService.register_user(
                        mock_db_session,
                        special_request
                    )
                    
                    if username == "" or len(username) > 100 or " " in username:
                        assert success is False

    # ==================================================================
    # ERROR HANDLING TESTS
    # ==================================================================

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_handle_database_error_gracefully(self, mock_db_session, auth_factory):
        """Test graceful handling of database errors."""
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.side_effect = Exception("Database connection failed")
            
            success, result = await AuthService.login_user(
                mock_db_session,
                auth_factory.create_login_request()
            )
            
            assert success is False
            # Should not expose internal error details
            assert "Database connection failed" not in result
            assert "Internal server error" in result or "Service temporarily unavailable" in result

    @pytest.mark.unit  
    @pytest.mark.asyncio
    async def test_token_generation_failure_handling(self, mock_db_session, auth_factory):
        """Test handling when token generation fails."""
        
        mock_user = MagicMock()
        mock_user.is_active = True
        
        with patch('app.dependencies.auth.UserService.get_user_by_username') as mock_get_user:
            mock_get_user.return_value = mock_user
            
            with patch('app.core.security.verify_password') as mock_verify:
                mock_verify.return_value = True
                
                with patch('app.core.security.create_access_token') as mock_access_token:
                    mock_access_token.side_effect = Exception("Token generation failed")
                    
                    success, result = await AuthService.login_user(
                        mock_db_session,
                        auth_factory.create_login_request()
                    )
                    
                    assert success is False
                    assert "Unable to generate authentication token" in result
