'use client';

import React, { useState, useEffect, useRef } from 'react';
// import { useRouter } from 'next/navigation';
import { FaDatabase, FaUser, FaKey, FaSignOutAlt, FaChevronDown } from 'react-icons/fa';
import { useAuth } from '@/contexts/AuthContext';
import RoleGuard from '@/components/auth/RoleGuard';
import PasswordModal from '@/components/auth/PasswordModal';

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  // const router = useRouter();
  const { user, logout } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const displayName = user?.full_name || user?.username || '';

  const handleLogout = async () => {
    await logout();
  };

  const openPasswordModal = () => setIsPasswordModalOpen(true);
  const closePasswordModal = () => setIsPasswordModalOpen(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <RoleGuard allowedRoles={['client']}>
      <div className="flex flex-col min-h-screen">
        {/* Custom Navbar */}
        <nav className="bg-blue-600 text-white px-6 py-3 flex justify-between items-center font-serif">
          <div className="flex items-center space-x-3">
            <FaDatabase size={28} />
            <span className="text-2xl font-bold">DADP</span>
          </div>
          <div ref={dropdownRef} className="relative">
            <button 
              onClick={() => setIsDropdownOpen(!isDropdownOpen)} 
              className="flex items-center space-x-2 focus:outline-none"
            >
              <FaUser size={20} />
              <span className="font-medium text-white">{displayName}</span>
              <FaChevronDown className={`transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white text-black rounded-lg shadow-lg z-50">
                <div className="px-4 py-2 border-b text-gray-700">{displayName}</div>
                <button
                  onClick={() => { openPasswordModal(); setIsDropdownOpen(false); }}
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <FaKey />
                  <span>Change Password</span>
                </button>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2"
                >
                  <FaSignOutAlt />
                  <span>Logout</span>
                </button>
              </div>
            )}
          </div>
        </nav>

        {/* Page Content */}
        <main className="flex-grow bg-blue-50 py-8">
          <div className="container mx-auto px-4">
            {children}
          </div>
        </main>

        {/* Change Password Modal */}
        <PasswordModal isOpen={isPasswordModalOpen} onClose={closePasswordModal} />
      </div>
    </RoleGuard>
  );
}
