from fastapi import APIRouter, Depends, HTTPException, status, Query #type:ignore
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, func, text
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from core.session_manager import get_master_db_session
from post_db.master_models.projects_registry import ProjectsRegistry
from post_db.master_models.clients import Clients
from dependencies.auth import get_current_active_user, require_admin
from schemas.ClientSchemas import ClientRegistrationRequest, ClientResponse, ProjectRegistryResponse, ClientWithProjectsResponse
from schemas.UserSchemas import SuccessResponse, ErrorResponse
import logging
import random
import os

logger = logging.getLogger('admin_client_routes')

router = APIRouter(
    prefix="/admin",
    tags=["Admin Clients"],
    dependencies=[Depends(get_current_active_user), Depends(require_admin)],
    responses={401: {"model": ErrorResponse}, 403: {"model": ErrorResponse}}
)

# Removed generate_unique_client_id function - now using auto-generated integer id

@router.post("/register-client", response_model=SuccessResponse)
async def register_client(
    client_data: ClientRegistrationRequest,
    db: AsyncSession = Depends(get_master_db_session)
):
    """
    Register a new client with project details.
    Creates both client and project entries in one operation.
    """
    try:
        # Extract project details from either nested or flat structure
        project_name = None
        project_type = None
        project_description = None
        
        if client_data.project:
            # Use nested project structure
            project_name = client_data.project.project_name
            project_type = client_data.project.project_type
            project_description = client_data.project.project_description
        else:
            # Use flat structure
            project_name = client_data.project_name
            project_type = client_data.project_type
            project_description = client_data.project_description
        
        # Validate required project fields
        if not project_name or not project_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Project name and type are required"
            )
        
        # Check for existing client using multiple criteria for uniqueness
        existing_client = None
        
        # Extract unique identifiers from request data
        client_email = getattr(client_data, 'email', None)
        client_username = getattr(client_data, 'username', None)
        
        # Fallback to contact_info for backward compatibility (if direct fields not provided)
        if not client_email or not client_username:
            if client_data.contact_info and isinstance(client_data.contact_info, dict):
                client_email = client_email or client_data.contact_info.get('email')
                client_username = client_username or client_data.contact_info.get('username')
        
        logger.info(f"Looking for existing client with name='{client_data.client_name}', email='{client_email}', username='{client_username}'")
        
        # Method 1: Check by email if available
        if client_email:
            try:
                existing_client_result = await db.execute(
                    select(Clients).where(Clients.email == client_email)
                )
                existing_client = existing_client_result.scalars().first()
                if existing_client:
                    logger.info(f"Found existing client by email: id={existing_client.id}, name='{existing_client.name}'")
            except Exception as e:
                logger.warning(f"Error searching by email: {e}")
        
        # Method 2: Check by username if no client found by email
        if not existing_client and client_username:
            try:
                existing_client_result = await db.execute(
                    select(Clients).where(Clients.username == client_username)
                )
                existing_client = existing_client_result.scalars().first()
                if existing_client:
                    logger.info(f"Found existing client by username: id={existing_client.id}, name='{existing_client.name}'")
            except Exception as e:
                logger.warning(f"Error searching by username: {e}")
        
        # No name-based fallback - names are not unique identifiers
        # If we can't find by email or username, we'll create a new client

        if existing_client:
            logger.info(f"Using existing client: id={existing_client.id}, name='{existing_client.name}', email='{client_email}', username='{client_username}'")
            new_client = existing_client
        else:
            # Create new client if it doesn't exist
            logger.info(f"No existing client found by email='{client_email}' or username='{client_username}'. Creating new client with name: '{client_data.client_name}'")

            # Create new client
            try:
                # Check if the sequence exists and create it if needed
                check_seq_result = await db.execute(
                    text("SELECT EXISTS(SELECT 1 FROM pg_sequences WHERE schemaname = 'public' AND sequencename = 'clients_id_seq')")
                )
                sequence_exists = check_seq_result.scalar()

                if not sequence_exists:
                    logger.info("Creating clients_id_seq sequence")
                    await db.execute(text("CREATE SEQUENCE IF NOT EXISTS clients_id_seq"))

                    # Get the current max id
                    max_id_result = await db.execute(
                        text("SELECT COALESCE(MAX(id), 0) + 1 FROM clients")
                    )
                    next_id = max_id_result.scalar()

                    # Set the sequence to start from the next available id
                    logger.info(f"Setting clients_id_seq to start from {next_id}")
                    await db.execute(
                        text(f"ALTER SEQUENCE clients_id_seq RESTART WITH {next_id}")
                    )

                    # Alter the id column to use the sequence
                    logger.info("Setting clients.id to use clients_id_seq as default")
                    await db.execute(
                        text("ALTER TABLE clients ALTER COLUMN id SET DEFAULT nextval('clients_id_seq')")
                    )

                    await db.commit()
            except Exception as seq_error:
                logger.warning(f"Error setting up sequence: {seq_error}")
                # Continue without sequence setup - let PostgreSQL handle ID generation

            # Create new client
            if not client_username or not client_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username and email are required for client registration"
                )
            
            new_client = Clients(
                name=client_data.client_name,
                username=client_username,
                email=client_email
            )
            db.add(new_client)
        
        try:
            await db.flush()  # Flush to get the client.id
        except IntegrityError as e:
            if "duplicate key value violates unique constraint" in str(e):
                # If there's a duplicate key error, try to get the existing client using same logic
                await db.rollback()
                
                # Start a new transaction after rollback
                existing_client = None
                
                # Try the same approach
                if client_email:
                    try:
                        client_result = await db.execute(
                            select(Clients).where(Clients.email == client_email)
                        )
                        existing_client = client_result.scalars().first()
                    except Exception:
                        pass
                
                if not existing_client and client_username:
                    try:
                        client_result = await db.execute(
                            select(Clients).where(Clients.username == client_username)
                        )
                        existing_client = client_result.scalars().first()
                    except Exception:
                        pass
                

                if existing_client:
                    new_client = existing_client
                else:
                    # If we can't find the client, re-raise the error
                    raise
            else:
                raise
        
        # Generate project_code if not provided
        project_code = client_data.project_code
        logger.info(f"Project code from request: '{project_code}', Client ID: {new_client.id}")
        
        if not project_code:
            sanitized_project_name = project_name.replace(' ', '_').replace('-', '_').upper()
            client_id_formatted = f"{new_client.id:04d}"  # Format as 4-digit number
            project_code = f"PROJ_{sanitized_project_name}_{client_id_formatted}"
            logger.info(f"Generated new project code: '{project_code}'")
        else:
            logger.warning(f"Using provided project code: '{project_code}' (may not match client ID {new_client.id})")
        
        # Check if project_code already exists
        project_result = await db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.project_code == project_code)
        )
        existing_project = project_result.scalar_one_or_none()
        
        if existing_project:
            # If project exists but with different client_id, update it
            if existing_project.client_id != new_client.id:
                logger.info(f"Updating existing project {project_code} from client_id {existing_project.client_id} to {new_client.id}")
                existing_project.client_id = new_client.id
                # Also update database name to match new client
                new_database_name = f"proj_{project_name.lower().replace(' ', '_')}_db"
                existing_project.database_name = new_database_name
                logger.info(f"Updated project database name to: {new_database_name}")
                await db.commit()
                new_project = existing_project
            else:
                # Project exists with same client_id, just use it
                logger.info(f"Using existing project {project_code} with client_id {new_client.id}")
                new_project = existing_project
        else:
            # Create new project
            new_project = None
        
        # Create project entry with NAS credentials (only if new project)
        if not new_project:
            nas_credentials = {
                "nas_url": client_data.nas_url,
                "nas_username": client_data.nas_username,
                "nas_password": client_data.nas_password,
                "nas_type": client_data.nas_type
            } if all([client_data.nas_url, client_data.nas_username, client_data.nas_password]) else None

            new_project = ProjectsRegistry(
                project_code=project_code,
                project_name=project_name,
                project_type=project_type,
                client_id=new_client.id,
                credentials=nas_credentials,
                database_name=f"proj_{project_name.lower().replace(' ', '_')}_db",
                instructions=project_description or "",
                priority_level=1
            )
            db.add(new_project)
            await db.commit()
            logger.info(f"Created new project: {project_code}")
        else:
            logger.info(f"Using existing project: {project_code}")

        # Client created successfully
        logger.info(f"Client created successfully: id={new_client.id}, name='{new_client.name}'")

        # Return success response with client and project details
        return SuccessResponse(
            success=True,
            message="Client and project registered successfully",
            data={
                "client": {
                    "id": new_client.id,
                    "name": new_client.name
                },
                "project": {
                    "id": new_project.id,
                    "project_code": new_project.project_code,
                    "project_name": new_project.project_name,
                    "project_type": new_project.project_type
                }
            }
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except SQLAlchemyError as e:
        # Handle database errors
        await db.rollback()
        logger.error(f"Database error during client registration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error during client registration: {str(e)}"
        )
    except Exception as e:
        # Handle other errors
        await db.rollback()
        logger.error(f"Error during client registration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during client registration: {str(e)}"
        )

@router.get("/debug-clients", response_model=SuccessResponse)
async def debug_clients(db: AsyncSession = Depends(get_master_db_session)):
    """
    Debug endpoint to see all clients and their contact_info structure.
    """
    try:
        clients_result = await db.execute(select(Clients))
        clients = clients_result.scalars().all()
        
        debug_info = []
        for client in clients:
            debug_info.append({
                "id": client.id,
                "name": client.name,
                "username": client.username,
                "email": client.email,
                "created_at": client.created_at.isoformat() if client.created_at else None
            })
        
        return SuccessResponse(
            success=True,
            message=f"Debug info for {len(debug_info)} clients",
            data={"clients": debug_info}
        )
        
    except Exception as e:
        logger.error(f"Error in debug_clients: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving debug info: {str(e)}"
        )

@router.get("/clients", response_model=SuccessResponse)
async def get_clients(db: AsyncSession = Depends(get_master_db_session)):
    """
    Get all clients with their projects.
    """
    try:
        # Get all clients
        clients_result = await db.execute(select(Clients))
        clients = clients_result.scalars().all()
        
        client_list = []
        for client in clients:
            # Get projects for this client
            projects_result = await db.execute(
                select(ProjectsRegistry).where(ProjectsRegistry.client_id == client.id)
            )
            projects = projects_result.scalars().all()
            
            # Create client response with projects
            client_data = {
                "id": client.id,
                "name": client.name,
                "username": client.username,
                "email": client.email,
                "created_at": client.created_at,
                "projects": [
                    {
                        "id": project.id,
                        "project_code": project.project_code,
                        "project_name": project.project_name,
                        "project_type": project.project_type,
                        "folder_path": project.folder_path,
                        "batch_size": project.batch_size,
                        "total_files": project.total_files,
                        "total_batches": project.total_batches,
                        "completed_files": project.completed_files,
                        "credentials": project.credentials,
                        "connection_type": project.connection_type
                    }
                    for project in projects
                ]
            }
            client_list.append(client_data)
        
        return SuccessResponse(
            success=True,
            message=f"Retrieved {len(client_list)} clients",
            data={"clients": client_list}
        )
        
    except Exception as e:
        logger.error(f"Error retrieving clients: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving clients"
        )

@router.get("/clients/{client_id}", response_model=SuccessResponse)
async def get_client(client_id: int, db: AsyncSession = Depends(get_master_db_session)):
    """
    Get a specific client with its projects.
    """
    try:
        # Get client
        client_result = await db.execute(
            select(Clients).where(Clients.id == client_id)
        )
        client = client_result.scalar_one_or_none()

        if not client:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Client with ID {client_id} not found"
            )

        # Get projects for this client
        projects_result = await db.execute(
            select(ProjectsRegistry).where(ProjectsRegistry.client_id == client.id)
        )
        projects = projects_result.scalars().all()

        # Create client response with projects
        client_data = {
            "id": client.id,
            "name": client.name,
            "username": client.username,
            "email": client.email,
            "created_at": client.created_at,
            "projects": [
                {
                    "id": project.id,
                    "project_code": project.project_code,
                    "project_name": project.project_name,
                    "project_type": project.project_type,
                    "folder_path": project.folder_path,
                    "batch_size": project.batch_size,
                    "total_files": project.total_files,
                    "total_batches": project.total_batches,
                    "completed_files": project.completed_files,
                    "credentials": project.credentials,
                    "connection_type": project.connection_type
                }
                for project in projects
            ]
        }
        
        return SuccessResponse(
            success=True,
            message=f"Retrieved client {client_id}",
            data={"client": client_data}
        )
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving client {client_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving client {client_id}"
        )


@router.get("/client-by-user/{user_id}")
async def get_client_by_user_id(
    user_id: int,
    db: AsyncSession = Depends(get_master_db_session),
    current_user: dict = Depends(get_current_active_user),
    _: dict = Depends(require_admin)
):
    """
    Get client information for a given user ID.
    This is used during onboarding to set CLIENT_ID when an existing client is selected.
    """
    try:
        # First, get the user to find their username/client_id
        from post_db.master_models.users import User
        user_result = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = user_result.scalar_one_or_none()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # For clients, the username might be the client_id or we need to find the client entry
        # Let's try to find the client by various methods

        # Method 1: Try to find client by email if available
        client = None
        if hasattr(user, 'email') and user.email:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.email == user.email)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by email: {e}")

        if not client:
            # Method 2: Try to find client by username
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.username == user.username)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by username: {e}")
        
        # Method 3: Fallback to name matching for backward compatibility
        if not client:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.name == user.full_name)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by full_name: {e}")
        
        if not client:
            try:
                client_result = await db.execute(
                    select(Clients).where(Clients.name == user.username)
                )
                client = client_result.scalar_one_or_none()
            except Exception as e:
                logger.warning(f"Error searching by username as name: {e}")

        if not client:
            # Method 3: If no client found, this might be a user-only client
            logger.warning(f"No client entry found for user {user.username}, treating as user-only client")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No client entry found for this user"
            )

        # Client information retrieved successfully
        logger.info(f"Client information retrieved: id={client.id}, name='{client.name}'")

        return SuccessResponse(
            success=True,
            message="Client information retrieved successfully",
            data={
                "client": {
                    "id": client.id,
                    "name": client.name,
                    "user_id": user.id,
                    "username": user.username
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting client by user ID: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving client information: {str(e)}"
        )