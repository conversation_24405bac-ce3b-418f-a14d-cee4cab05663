export interface Dataset {
  id: string;
  name: string;
  status: 'merged' | 'pending';
}

export interface DatasetStats {
  totalBatches: number;
  totalCompleted: number;
}

export const datasets: Dataset[] = [
  { id: 'dataset1', name: 'Medical Records', status: 'merged' },
  { id: 'dataset2', name: 'Financial Documents', status: 'merged' },
  { id: 'dataset3', name: 'Legal Contracts', status: 'pending' },
  { id: 'dataset4', name: 'Customer Support Tickets', status: 'pending' },
];

export const datasetStats: Record<string, DatasetStats> = {
  'dataset1': { totalBatches: 10, totalCompleted: 10 },
  'dataset2': { totalBatches: 8, totalCompleted: 8 },
  'dataset3': { totalBatches: 12, totalCompleted: 7 },
  'dataset4': { totalBatches: 5, totalCompleted: 2 },
};

export const storageOptions = [
  { value: 'current', label: 'Current Storage' },
  { value: 'google-drive', label: 'Google Drive' },
  { value: 'dropbox', label: 'Dropbox (Coming Soon)', disabled: true },
  { value: 'database', label: 'Database (Coming Soon)', disabled: true },
];

export const calculateCompletionPercentage = (stats: DatasetStats): number => {
  return stats.totalBatches > 0 
    ? Math.round((stats.totalCompleted / stats.totalBatches) * 100) 
    : 0;
}; 