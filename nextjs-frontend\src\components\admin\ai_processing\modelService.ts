import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { API_BASE_URL } from "@/lib/api";

export interface AIModel {
  id: number;
  model_name: string;
  model_id: string;
  supported_file_types: string[];
  output_format: Record<string, unknown>;
  deployment_status: string;
  description?: string;
  input_requirements?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  created_by?: string;
}



export const useModelService = () => {
  const [models, setModels] = useState<AIModel[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);



  // Fetch all models (no caching)
  const fetchModels = useCallback(async (): Promise<AIModel[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_BASE_URL}/ai-models`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const activeModels = await response.json();
      setModels(activeModels);
      return activeModels;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(' Error fetching models:', errorMessage);
      setError(errorMessage);
      toast.error(`Failed to load models: ${errorMessage}`);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [API_BASE_URL]);

  const getModelById = useCallback(async (modelId: string): Promise<AIModel | null> => {
    try {
      const response = await fetch(`${API_BASE_URL}/ai-models/${modelId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch model: ${response.status}`);
      }

      const model = await response.json();
      return model;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error(`Error fetching model ${modelId}:`, errorMessage);
      toast.error(`Failed to load model: ${errorMessage}`);
      return null;
    }
  }, [API_BASE_URL]);

  // Get models by supported file type
  const getModelsByFileType = useCallback((fileType: string): AIModel[] => {
    return models.filter(model => 
      model.supported_file_types.some(type => 
        type.toLowerCase().includes(fileType.toLowerCase())
      )
    );
  }, [models]);

  // Refresh models data
  const refreshModels = useCallback(async () => {
    return await fetchModels();
  }, [fetchModels]);

  // Load models on mount
  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  return {
    models,
    isLoading,
    error,
    fetchModels,
    getModelById,
    getModelsByFileType,
    refreshModels,
  };
};

// Export individual functions for non-hook usage
export const modelService = {
  async fetchModels(): Promise<AIModel[]> {


    try {
      const response = await fetch(`${API_BASE_URL}/ai-models`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.status}`);
      }

      const activeModels = await response.json();
      return activeModels;

    } catch (err) {
      console.error('Error in modelService.fetchModels:', err);
      throw err;
    }
  },

  async getModelById(modelId: string): Promise<AIModel | null> {

    try {
      const response = await fetch(`${API_BASE_URL}/ai-models/${modelId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch model: ${response.status}`);
      }

      return await response.json();

    } catch (err) {
      console.error(`Error in modelService.getModelById for ${modelId}:`, err);
      throw err;
    }
  },
};