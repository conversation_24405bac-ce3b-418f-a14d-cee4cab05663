"use client";

import React, { useState, useEffect } from "react";
import {
  FaPlus,
  FaEdit,
  FaTrash,
  <PERSON>aE<PERSON>,
  <PERSON>a<PERSON><PERSON>ch,
  <PERSON>a<PERSON><PERSON>er,
  FaSort,
  FaCheck,
  FaTimes,
  FaBalanceScale,
} from "react-icons/fa";

interface AllocationStrategy {
  id: number;
  strategy_name: string;
  strategy_type: string;
  description: string | null;
  allocation_status: string;
  num_annotators: number;
  requires_verification: boolean;
  requires_ai_preprocessing: boolean;
  quality_requirements: any;
  configuration: any;
  created_at: string;
  updated_at: string;
}

interface AllocationStrategyForm {
  strategy_name: string;
  strategy_type: string;
  description: string;
  allocation_status: string;
  num_annotators: number;
  requires_verification: boolean;
  requires_ai_preprocessing: boolean;
  quality_requirements: any;
  configuration: any;
}

const STRATEGY_TYPES = [
  "sequential",
  "parallel",
];

const ALLOCATION_STATUSES = ["active", "inactive", "deprecated"];

export default function AllocationStrategy() {
  const [strategies, setStrategies] = useState<AllocationStrategy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState<AllocationStrategy | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const [formData, setFormData] = useState<AllocationStrategyForm>({
    strategy_name: "",
    strategy_type: "sequential",
    description: "",
    allocation_status: "active",
    num_annotators: 1,
    requires_verification: false,
    requires_ai_preprocessing: false,
    quality_requirements: null,
    configuration: null,
  });

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/allocation-strategies");
      if (response.ok) {
        const data = await response.json();
        console.log("Fetched strategies data:", data);
        setStrategies(data);
      } else {
        console.error("Failed to fetch strategies");
      }
    } catch (error) {
      console.error("Error fetching strategies:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const url = editingStrategy
        ? `/api/allocation-strategies/${editingStrategy.id}`
        : "/api/allocation-strategies";
      
      const method = editingStrategy ? "PATCH" : "POST";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        await fetchStrategies();
        resetForm();
        setShowForm(false);
      } else {
        console.error("Failed to save strategy");
      }
    } catch (error) {
      console.error("Error saving strategy:", error);
    }
  };

  const handleEdit = (strategy: AllocationStrategy) => {
    setEditingStrategy(strategy);
    setFormData({
      strategy_name: strategy.strategy_name,
      strategy_type: strategy.strategy_type,
      description: strategy.description || "",
      allocation_status: strategy.allocation_status,
      num_annotators: strategy.num_annotators,
      requires_verification: strategy.requires_verification,
      requires_ai_preprocessing: strategy.requires_ai_preprocessing,
      quality_requirements: strategy.quality_requirements,
      configuration: strategy.configuration,
    });
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this strategy?")) {
      try {
        const response = await fetch(`/api/allocation-strategies/${id}`, {
          method: "DELETE",
        });
        if (response.ok) {
          await fetchStrategies();
        } else {
          console.error("Failed to delete strategy");
        }
      } catch (error) {
        console.error("Error deleting strategy:", error);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      strategy_name: "",
      strategy_type: "sequential",
      description: "",
      allocation_status: "active",
      num_annotators: 1,
      requires_verification: false,
      requires_ai_preprocessing: false,
      quality_requirements: null,
      configuration: null,
    });
    setEditingStrategy(null);
  };

  const handleStrategyTypeChange = (newType: string) => {
    const updatedFormData = { ...formData, strategy_type: newType };
    
    // Apply validation rules for parallel strategy
    if (newType === "parallel") {
      // Ensure minimum 2 annotators for parallel
      if (updatedFormData.num_annotators < 2) {
        updatedFormData.num_annotators = 2;
      }
      // Note: Verification can be enabled for both sequential and parallel strategies
      // The user can choose whether to enable verification based on their needs
    }
    
    setFormData(updatedFormData);
  };

  const filteredStrategies = strategies
    .filter((strategy) => {
      const matchesSearch = strategy.strategy_name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
        (strategy.description &&
          strategy.description.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesType = !filterType || strategy.strategy_type === filterType;
      
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      const aValue = a[sortBy as keyof AllocationStrategy];
      const bValue = b[sortBy as keyof AllocationStrategy];
      
      // Handle null values for comparison
      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return 1;
      if (bValue === null) return -1;
      
      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Add fallback for empty data
  if (!strategies || strategies.length === 0) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Allocation Strategies</h1>
            <p className="mt-2 text-gray-600">
              Manage allocation strategy templates for annotation workflows
            </p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <FaPlus className="mr-2" />
            New Strategy
          </button>
        </div>

        {/* Empty State */}
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <div className="text-gray-500 mb-4">
            <FaBalanceScale className="mx-auto h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No allocation strategies found</h3>
          <p className="text-gray-500 mb-6">
            Get started by creating your first allocation strategy template.
          </p>
          <div className="flex justify-center gap-3">
            <button
              onClick={() => setShowForm(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FaPlus className="mr-2" />
              Create First Strategy
            </button>
            <button
              onClick={() => {
                // Create a test strategy for debugging
                const testStrategy = {
                  id: 1,
                  strategy_name: "Test Strategy",
                  strategy_type: "sequential",
                  description: "A test strategy for debugging",
                  allocation_status: "active",
                  num_annotators: 2,
                  requires_verification: true,
                  requires_ai_preprocessing: false,
                  quality_requirements: null,
                  configuration: null,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString(),
                };
                setStrategies([testStrategy]);
              }}
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Load Test Data
            </button>
          </div>
        </div>

        {/* Form Modal */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-semibold text-gray-900">New Strategy</h2>
              </div>
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                {/* Form fields - same as below */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Strategy Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.strategy_name}
                      onChange={(e) =>
                        setFormData({ ...formData, strategy_name: e.target.value })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Strategy Type *
                    </label>
                    <select
                      required
                      value={formData.strategy_type}
                      onChange={(e) => handleStrategyTypeChange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {STRATEGY_TYPES.map((type) => (
                        <option key={type} value={type}>
                          {type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Allocation Status
                    </label>
                    <select
                      value={formData.allocation_status}
                      onChange={(e) =>
                        setFormData({ ...formData, allocation_status: e.target.value })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {ALLOCATION_STATUSES.map((status) => (
                        <option key={status} value={status}>
                          {status.replace(/\b\w/g, (l) => l.toUpperCase())}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Annotators *
                    </label>
                    <input
                      type="number"
                      min={formData.strategy_type === "parallel" ? "2" : "1"}
                      max="10"
                      required
                      value={formData.num_annotators}
                      onChange={(e) =>
                        setFormData({ ...formData, num_annotators: parseInt(e.target.value) })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="verification"
                      checked={formData.requires_verification}
                      disabled={formData.strategy_type === "parallel"}
                      onChange={(e) =>
                        setFormData({ ...formData, requires_verification: e.target.checked })
                      }
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                    <label htmlFor="verification" className={`ml-2 text-sm ${formData.strategy_type === "parallel" ? "text-gray-500" : "text-gray-700"}`}>
                      Requires Verification {formData.strategy_type === "parallel" && "(Required for Parallel)"}
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="ai_preprocessing"
                      checked={formData.requires_ai_preprocessing}
                      onChange={(e) =>
                        setFormData({ ...formData, requires_ai_preprocessing: e.target.checked })
                      }
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="ai_preprocessing" className="ml-2 text-sm text-gray-700">
                      Requires AI Preprocessing
                    </label>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      resetForm();
                    }}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Strategy
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Allocation Strategies</h1>
          <p className="mt-2 text-gray-600">
            Manage allocation strategy templates for annotation workflows
          </p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <FaPlus className="mr-2" />
          New Strategy
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search strategies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              {STRATEGY_TYPES.map((type) => (
                <option key={type} value={type}>
                  {type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                </option>
              ))}
            </select>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split("-");
                setSortBy(field);
                setSortOrder(order as "asc" | "desc");
              }}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="created_at-desc">Newest First</option>
              <option value="created_at-asc">Oldest First</option>
              <option value="strategy_name-asc">Name A-Z</option>
              <option value="strategy_name-desc">Name Z-A</option>
            </select>
          </div>
        </div>
      </div>

      {/* Strategies Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Strategy
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Annotators
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Requirements
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStrategies.map((strategy) => (
                <tr key={strategy.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {strategy.strategy_name}
                      </div>
                      {strategy.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {strategy.description}
                        </div>
                      )}
                    </div>
                  </td>
                                     <td className="px-6 py-4 whitespace-nowrap">
                                           <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        strategy.strategy_type === 'sequential' ? 'bg-yellow-100 text-yellow-800' :
                        strategy.strategy_type === 'parallel' ? 'bg-purple-100 text-purple-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {(strategy.strategy_type || 'sequential').replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                   </td>
                                       <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        (strategy.allocation_status || 'active') === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : (strategy.allocation_status || 'active') === 'inactive'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {(strategy.allocation_status || 'active').replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </td>
                   <td className="px-6 py-4 whitespace-nowrap">
                     <div className="space-y-1">
                       <div className="text-sm text-gray-500">
                         Annotators: {strategy.num_annotators || 1}
                       </div>
                     </div>
                   </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <div className="flex items-center text-sm">
                        <span className="mr-2">Verification:</span>
                        {(strategy.requires_verification || false) ? (
                          <FaCheck className="text-green-500" />
                        ) : (
                          <FaTimes className="text-red-500" />
                        )}
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="mr-2">AI Preprocessing:</span>
                        {(strategy.requires_ai_preprocessing || false) ? (
                          <FaCheck className="text-green-500" />
                        ) : (
                          <FaTimes className="text-red-500" />
                        )}
                      </div>
                    </div>
                  </td>
                                     <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                     {strategy.created_at ? formatDate(strategy.created_at) : 'N/A'}
                   </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(strategy)}
                        className="text-blue-600 hover:text-blue-900 p-1"
                        title="Edit"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDelete(strategy.id)}
                        className="text-red-600 hover:text-red-900 p-1"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {filteredStrategies.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No strategies found</p>
          </div>
        )}
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {editingStrategy ? "Edit Strategy" : "New Strategy"}
              </h2>
            </div>
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Strategy Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.strategy_name}
                    onChange={(e) =>
                      setFormData({ ...formData, strategy_name: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Strategy Type *
                  </label>
                  <select
                    required
                    value={formData.strategy_type}
                    onChange={(e) => handleStrategyTypeChange(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {STRATEGY_TYPES.map((type) => (
                      <option key={type} value={type}>
                        {type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Allocation Status
                  </label>
                  <select
                    value={formData.allocation_status}
                    onChange={(e) =>
                      setFormData({ ...formData, allocation_status: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {ALLOCATION_STATUSES.map((status) => (
                      <option key={status} value={status}>
                        {status.replace(/\b\w/g, (l) => l.toUpperCase())}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Number of Annotators *
                  </label>
                  <input
                    type="number"
                    min={formData.strategy_type === "parallel" ? "2" : "1"}
                    max="10"
                    required
                    value={formData.num_annotators}
                    onChange={(e) =>
                      setFormData({ ...formData, num_annotators: parseInt(e.target.value) })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="verification"
                    checked={formData.requires_verification}
                    disabled={formData.strategy_type === "parallel"}
                    onChange={(e) =>
                      setFormData({ ...formData, requires_verification: e.target.checked })
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  />
                  <label htmlFor="verification" className={`ml-2 text-sm ${formData.strategy_type === "parallel" ? "text-gray-500" : "text-gray-700"}`}>
                    Requires Verification {formData.strategy_type === "parallel" && "(Required for Parallel)"}
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="ai_preprocessing"
                    checked={formData.requires_ai_preprocessing}
                    onChange={(e) =>
                      setFormData({ ...formData, requires_ai_preprocessing: e.target.checked })
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="ai_preprocessing" className="ml-2 text-sm text-gray-700">
                    Requires AI Preprocessing
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingStrategy ? "Update" : "Create"} Strategy
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
