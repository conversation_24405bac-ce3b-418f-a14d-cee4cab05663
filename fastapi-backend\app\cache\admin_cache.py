"""
Admin-related caching functionality.
"""
import hashlib
from cache.redis_connector import cache_set, cache_get, cache_delete, cache_exists
from cache.base import serialize_for_cache
import logging
from core.config import settings

logger = logging.getLogger('admin_cache')


# Cache key prefixes
ADMIN_USER_LIST_PREFIX = "admin:user_list"
ADMIN_USER_DETAILS_PREFIX = "admin:user_details:"
ADMIN_DIRECTORY_PREFIX = "admin:directory:"
ADMIN_STATS_PREFIX = "admin:stats:"

# Cache TTL values (in seconds) - loaded from configuration
USER_LIST_TTL = settings.redis_settings.admin_user_list_ttl
USER_DETAILS_TTL = settings.redis_settings.admin_user_details_ttl
DIRECTORY_TTL = settings.redis_settings.admin_directory_ttl
STATS_TTL = settings.redis_settings.admin_stats_ttl


def generate_user_list_key():
    """
    Generate a cache key for user list

    Returns:
        str: Cache key
    """
    return ADMIN_USER_LIST_PREFIX

def generate_user_details_key(username):
    """
    Generate a cache key for user details

    Args:
        username: Username

    Returns:
        str: Cache key
    """
    return f"{ADMIN_USER_DETAILS_PREFIX}{username}"

def generate_directory_key(path):
    """
    Generate a cache key for directory listing

    Args:
        path: Directory path or cache key

    Returns:
        str: Cache key
    """
    # Normalize path for consistent keys
    normalized_path = str(path).replace('\\', '/').strip()
    if normalized_path.startswith('/'):
        normalized_path = normalized_path[1:]

    # Create a hash of the path to avoid special characters in Redis keys
    path_hash = hashlib.md5(normalized_path.encode()).hexdigest()
    return f"{ADMIN_DIRECTORY_PREFIX}{path_hash}"

def generate_stats_key(stats_type):
    """
    Generate a cache key for statistics

    Args:
        stats_type: Type of statistics

    Returns:
        str: Cache key
    """
    return f"{ADMIN_STATS_PREFIX}{stats_type}"

def cache_user_list(user_list):
    """
    Cache user list

    Args:
        user_list: List of users

    Returns:
        bool: Success status
    """
    key = generate_user_list_key()
    ttl = USER_LIST_TTL

    # Serialize datetime objects to strings
    serialized_user_list = serialize_for_cache(user_list)

    logger.info(f"Caching user list with {len(user_list)} users")
    return cache_set(key, serialized_user_list, ttl)

def get_cached_user_list():
    """
    Get cached user list

    Returns:
        list: User list or None if not found
    """
    key = generate_user_list_key()
    logger.debug("Getting cached user list")

    # Get the cached data
    cached_data = cache_get(key, json_decode=True)

    # If we have data, ensure it's safe to use
    if cached_data is not None:
        # Process the cached data to ensure datetime fields are properly handled
        for user in cached_data:
            # Ensure created_at and last_login are not treated as datetime objects
            # by the template or other code
            if 'created_at' in user and user['created_at'] is not None:
                # If it's already a string, leave it as is
                pass

            if 'last_login' in user and user['last_login'] is not None:
                # If it's already a string, leave it as is
                pass

    return cached_data

def cache_directory_listing(path, directory_listing):
    """
    Cache directory listing

    Args:
        path: Directory path or cache key
        directory_listing: Directory listing data

    Returns:
        bool: Success status
    """
    key = generate_directory_key(path)
    ttl = DIRECTORY_TTL

    # Serialize datetime objects to strings
    serialized_directory_listing = serialize_for_cache(directory_listing)

    logger.info(f"Caching directory listing for {path}")
    return cache_set(key, serialized_directory_listing, ttl)

def get_cached_directory_listing(path):
    """
    Get cached directory listing

    Args:
        path: Directory path or cache key

    Returns:
        dict: Directory listing data or None if not found
    """
    key = generate_directory_key(path)
    logger.debug(f"Getting cached directory listing for {path}")
    return cache_get(key, json_decode=True)

def cache_stats(stats_type, stats_data):
    """
    Cache statistics

    Args:
        stats_type: Type of statistics
        stats_data: Statistics data

    Returns:
        bool: Success status
    """
    key = generate_stats_key(stats_type)
    ttl = STATS_TTL

    # Serialize datetime objects to strings
    serialized_stats_data = serialize_for_cache(stats_data)

    logger.info(f"Caching statistics for {stats_type}")
    return cache_set(key, serialized_stats_data, ttl)

def get_cached_stats(stats_type):
    """
    Get cached statistics

    Args:
        stats_type: Type of statistics

    Returns:
        dict: Statistics data or None if not found
    """
    key = generate_stats_key(stats_type)
    logger.debug(f"Getting cached statistics for {stats_type}")
    return cache_get(key, json_decode=True)

def invalidate_directory_cache(path=None):
    """
    Invalidate directory cache

    Args:
        path: Directory path or cache key (if None, invalidate all directory caches)

    Returns:
        bool: Success status
    """
    if path:
        # Invalidate specific directory cache
        key = generate_directory_key(path)
        logger.info(f"Invalidating directory cache for {path}")
        return cache_delete(key)
    else:
        # In a production system, you would use Redis pattern matching
        # to delete all keys that match a pattern
        logger.info("Invalidating all directory caches is not implemented")
        return True  # Pretend success

def invalidate_stats_cache(stats_type=None):
    """
    Invalidate statistics cache

    Args:
        stats_type: Type of statistics (if None, invalidate all statistics caches)

    Returns:
        bool: Success status
    """
    if stats_type:
        # Invalidate specific statistics cache
        key = generate_stats_key(stats_type)
        logger.info(f"Invalidating statistics cache for {stats_type}")
        return cache_delete(key)
    else:
        # In a production system, you would use Redis pattern matching
        # to delete all keys that match a pattern
        logger.info("Invalidating all statistics caches is not implemented")
        return True  # Pretend success