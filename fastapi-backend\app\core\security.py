"""
Security module for the application.
Handles password hashing, JWT token generation and verification.
"""
import bcrypt
import types
# Monkey-patch bcrypt to include __about__ for passlib version detection
bcrypt.__about__ = types.SimpleNamespace(__version__=bcrypt.__version__)

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional, Any
from passlib.context import <PERSON>pt<PERSON><PERSON>xt
from jose import JWTError, jwt
from core.config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt algorithm
    
    Args:
        password: Plain text password
        
    Returns:
        Hashed password string
    """
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against a hash
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        True if password matches hash, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def create_token(data: Dict[str, Any], expires_delta: timedelta, token_type: str = "access") -> str:
    """
    Create a JWT token
    
    Args:
        data: Token payload data
        expires_delta: Token expiration time delta
        token_type: Type of token ("access" or "refresh")
        
    Returns:
        JWT token string
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + expires_delta
    
    # Add token metadata
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "token_type": token_type
    })
    
    # Create token
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.jwt_settings.secret_key, 
        algorithm=settings.jwt_settings.algorithm
    )
    
    return encoded_jwt

def create_access_token(data: Dict[str, Any]) -> str:
    """
    Create an access token
    
    Args:
        data: Token payload data
        
    Returns:
        JWT access token string
    """
    return create_token(
        data=data,
        expires_delta=timedelta(minutes=settings.jwt_settings.access_token_expire_minutes),
        token_type="access"
    )

def create_refresh_token(data: Dict[str, Any]) -> str:
    """
    Create a refresh token
    
    Args:
        data: Token payload data
        
    Returns:
        JWT refresh token string
    """
    return create_token(
        data=data,
        expires_delta=timedelta(days=settings.jwt_settings.refresh_token_expire_days),
        token_type="refresh"
    )

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify and decode a JWT token
    
    Args:
        token: JWT token string
        
    Returns:
        Token payload if valid, None otherwise
    """
    try:
        # Decode token
        payload = jwt.decode(
            token, 
            settings.jwt_settings.secret_key, 
            algorithms=[settings.jwt_settings.algorithm]
        )
        
        # Check if token has expired
        if datetime.fromtimestamp(payload.get("exp")) < datetime.utcnow():
            return None
            
        return payload
    except JWTError:
        return None
