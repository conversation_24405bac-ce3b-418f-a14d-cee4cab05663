// Main export
export { default } from './ProjectsManagement';
export { default as ProjectsManagement } from './ProjectsManagement';

// Export types for external use
export type {
  ProjectRegistryResponse,
  ProjectFilters as ProjectFiltersType,
  UserInfo,
  AssignedUserInfo,
  StrategyDetails,
  BatchAllocationInfo,
  AssignmentProgress,
} from './types';

// Export hooks for potential reuse
export {
  useProjects,
  useProjectActions,
  useUserAssignment,
  useStrategy,
  useBatchAllocations,
} from './hooks';

// Export components for potential reuse
export {
  ProjectFilters,
  ProjectsTable,
  ProjectActions,
  UserAssignmentSection,
  BatchAllocationSection,
} from './components';

// Export modals for potential reuse
export {
  ProjectDetailsModal,
  ProjectActionsModal,
  DeadlineModal,
} from './modals';

// Export utilities
export {
  getStatusColor,
  formatDate,
  calculateProgress,
  getBatchStatusColor,
} from './utils';
