# import json
# import re
# from typing import List, Dict, Any
# import pymupdf4llm

# class PDFService:
#     """
#     Simple PDF service that extracts page-wise text using pymupdf4llm.
#     """
#     def extract_pages(self, file_path: str) -> List[Dict[str, Any]]:
#         """
#         Extract pages from a PDF and return structured page data.
#         """
#         try:
#             # Extract data using pymupdf4llm
#             raw_pages = pymupdf4llm.to_markdown(
#                 file_path,
#                 write_images=True,
#                 image_path="images",
#                 embed_images=True,
#                 dpi=300,
#                 page_chunks=True,
#                 extract_words=False,
#                 table_strategy='lines_strict',
#                 image_size_limit=0.05,
#                 show_progress=True
#             )
            
#             # Process the raw pages into our expected format
#             processed_pages = []
            
#             if isinstance(raw_pages, list):
#                 for i, page_data in enumerate(raw_pages):
#                     # Extract page number from metadata if available, otherwise use index + 1
#                     page_number = i + 1
#                     if isinstance(page_data, dict):
#                         metadata = page_data.get('metadata', {})
#                         if 'page' in metadata:
#                             page_number = metadata['page']
                        
#                         # Extract text content
#                         text_content = page_data.get('text', '')
                        
#                         # Store metadata as JSON string
#                         metadata_json = json.dumps(metadata) if metadata else None
                        
#                         # Extract images from text content using regex
#                         images = []
#                         if text_content:
#                             # Look for embedded base64 images in markdown
#                             base64_pattern = r'!\[.*?\]\(data:image/([^;]+);base64,([^)]+)\)'
#                             matches = re.findall(base64_pattern, text_content)
#                             for match in matches:
#                                 image_type, image_data = match
#                                 images.append({
#                                     'image_data': image_data,
#                                     'image_type': image_type
#                                 })
                            
#                             # Clean text content by removing base64 image URLs
#                             text_content = re.sub(base64_pattern, '', text_content)
#                             # Clean up extra whitespace and empty lines
#                             text_content = re.sub(r'\n\s*\n\s*\n', '\n\n', text_content)
#                             text_content = text_content.strip()
                        
#                         processed_pages.append({
#                             'page_number': page_number,
#                             'text_content': text_content,
#                             'metadata_json': metadata_json,
#                             'images': images
#                         })
#                     else:
#                         # If page_data is just a string
#                         processed_pages.append({
#                             'page_number': page_number,
#                             'text_content': str(page_data),
#                             'metadata_json': None,
#                             'images': []
#                         })
#             else:
#                 # If single string, treat as one page
#                 processed_pages.append({
#                     'page_number': 1,
#                     'text_content': str(raw_pages),
#                     'metadata_json': None,
#                     'images': []
#                 })
            
#             return processed_pages
            
#         except Exception as e:
#             raise Exception(f"Failed to extract PDF content: {str(e)}") 