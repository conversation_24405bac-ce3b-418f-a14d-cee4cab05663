"use client";

import { useState, useEffect, useCallback } from "react";
import {
  FaCheck,
  FaTimes,
  FaArrowLeft,
  FaArrowRight,
  FaSave,
  FaUser,
  FaEye,
  FaExclamationTriangle,
  FaEdit
} from "react-icons/fa";
import { useRouter } from "next/navigation";
import { showToast } from "@/lib/toast";
import { MediaViewer } from "../shared/media";
import { detectMediaType } from "../shared/media/types";
import { DynamicField, type FormFieldConfig } from "../shared/dynamic-fields";
import { API_BASE_URL } from "@/lib/api";
import AnnotatorComparisonPanel from "./AnnotatorComparisonPanel";
// import VerificationProgress from "./VerificationProgress";

interface ReviewData {
  annotator_id: number;
  annotator_number: number;
  review_data: any;
}

interface VerificationFile {
  file_id: number;
  filename: string;
  file_path: string;
  file_type?: string;
  file_extension?: string;
  processing_status: string;
  completion_count: number;
  reviews: ReviewData[];
  batch_id?: number;
  url: string;
  is_csv?: boolean;
}

interface VerificationInterfaceProps {
  files: VerificationFile[];
  batchName: string;
  batchInfo: any;
  totalFiles: number;
  formConfig: any[];
}

interface VerificationDecision {
  file_id: number;
  field_decisions: Record<string, {
    chosen_annotator_id?: number;
    custom_response?: any;
    decision_type: 'annotator_choice' | 'custom_input';
    final_value: any;
  }>;
  completed: boolean;
}

export default function VerificationInterface({
  files = [],
  batchName,
  batchInfo,
  totalFiles,
  formConfig = []
}: VerificationInterfaceProps) {
  const router = useRouter();
  
  // State management
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [verificationDecisions, setVerificationDecisions] = useState<Record<number, VerificationDecision>>({});
  const [selectedReview, setSelectedReview] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Get current file
  const currentFile = files.length > 0 ? files[currentFileIndex] : null;
  const currentDecision = currentFile ? verificationDecisions[currentFile.file_id] : null;

  // Initialize verification decisions
  useEffect(() => {
    const decisions: Record<number, VerificationDecision> = {};
    files.forEach(file => {
      const fieldDecisions: Record<string, any> = {};
      
      // Initialize decisions for each field in the form config
      formConfig.forEach(field => {
        fieldDecisions[field.field_name] = {
          decision_type: 'annotator_choice',
          chosen_annotator_id: null,
          custom_response: null,
          final_value: null
        };
      });
      
      decisions[file.file_id] = {
        file_id: file.file_id,
        field_decisions: fieldDecisions,
        completed: false
      };
    });
    
    setVerificationDecisions(decisions);
  }, [files, formConfig]);

  // Check if all files are completed
  const allCompleted = Object.values(verificationDecisions).every(decision => decision.completed);
  const completedFiles = Object.values(verificationDecisions).filter(d => d.completed).length;

  // Navigation functions
  const handlePrevious = useCallback(() => {
    if (currentFileIndex > 0) {
      setCurrentFileIndex(currentFileIndex - 1);
      setSelectedReview(0);
    }
  }, [currentFileIndex]);

  const handleNext = useCallback(() => {
    if (currentFileIndex < files.length - 1) {
      setCurrentFileIndex(currentFileIndex + 1);
      setSelectedReview(0);
    }
  }, [currentFileIndex, files.length]);

  // Zoom functions
  const handleZoomIn = useCallback(() => setZoomLevel(prev => Math.min(prev + 10, 200)), []);
  const handleZoomOut = useCallback(() => setZoomLevel(prev => Math.max(prev - 10, 50)), []);
  const handleResetZoom = useCallback(() => setZoomLevel(100), []);

  // Handle field decision change
  const handleFieldDecisionChange = useCallback((fieldName: string, decision: any) => {
    if (!currentFile) return;
    
    setVerificationDecisions(prev => ({
      ...prev,
      [currentFile.file_id]: {
        ...prev[currentFile.file_id],
        field_decisions: {
          ...prev[currentFile.file_id].field_decisions,
          [fieldName]: decision
        }
      }
    }));
  }, [currentFile]);

  // Mark current file as completed
  const markFileCompleted = useCallback(() => {
    if (!currentFile) return;
    
    const fileDecision = verificationDecisions[currentFile.file_id];
    const allFieldsCompleted = Object.values(fileDecision.field_decisions).every(
      decision => decision.final_value !== null && decision.final_value !== undefined
    );
    
    if (allFieldsCompleted) {
      setVerificationDecisions(prev => ({
        ...prev,
        [currentFile.file_id]: {
          ...prev[currentFile.file_id],
          completed: true
        }
      }));
      
      showToast.success(`File ${currentFileIndex + 1} completed!`);
      
      // Auto-advance to next file if not last
      if (currentFileIndex < files.length - 1) {
        setTimeout(() => handleNext(), 500);
      }
    } else {
      showToast.warning("Please make decisions for all fields before marking as complete.");
    }
  }, [currentFile, verificationDecisions, currentFileIndex, files.length, handleNext]);

  // Parse annotator review data
  const parseAnnotatorData = useCallback((reviewData: any) => {
    try {
      if (typeof reviewData === 'string') {
        return JSON.parse(reviewData);
      }
      return reviewData;
    } catch (error) {
      return reviewData;
    }
  }, []);

  // Get annotator response for a field
  const getAnnotatorResponse = useCallback((review: any, fieldName: string) => {
    const data = parseAnnotatorData(review.review_data);
    
    if (data && typeof data === 'object') {
      // Check in form_data first, then direct field
      if (data.form_data && data.form_data[fieldName] !== undefined) {
        return data.form_data[fieldName];
      }
      if (data[fieldName] !== undefined) {
        return data[fieldName];
      }
      // Try with label as key
      const field = formConfig.find(f => f.field_name === fieldName);
      if (field && data.form_data && data.form_data[field.label] !== undefined) {
        return data.form_data[field.label];
      }
    }
    
    return null;
  }, [parseAnnotatorData, formConfig]);

  // Format response value for display
  const formatResponseValue = useCallback((value: any) => {
    if (value === null || value === undefined) return 'No response';
    if (Array.isArray(value)) return value.join(', ');
    if (typeof value === 'object') return JSON.stringify(value, null, 2);
    return String(value);
  }, []);

  const handleSaveAll = async () => {
    setIsSaving(true);
    
    try {
      console.log('Saving sequential verification results:', verificationDecisions);
      
      const API_BASE = API_BASE_URL;
      
      // Prepare decisions for API - convert to the expected format
      const decisionsToSave = Object.values(verificationDecisions).map(decision => {
        // Convert field decisions to question-answer format
        const form_data: Record<string, any> = {};
        
        // Map field decisions to question-answer format
        Object.entries(decision.field_decisions).forEach(([fieldName, fieldDecision]) => {
          const field = formConfig.find(f => f.field_name === fieldName);
          if (field && fieldDecision.final_value !== null && fieldDecision.final_value !== undefined) {
            form_data[field.label] = fieldDecision.final_value;
          }
        });
        
        return {
          file_id: decision.file_id,
          decision: 'approved', // Sequential verification always results in approval with refined data
          review_data: {
            label: "completed",
            form_data: form_data
          }
        };
      }).filter(decision => Object.keys(decision.review_data.form_data).length > 0);
      
      // Save each verification decision
      const savePromises = decisionsToSave.map(async (decision) => {
        const response = await fetch(`${API_BASE}/verifier/save-review`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(decision),
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API Error for file ${decision.file_id}:`, {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });
          throw new Error(`Failed to save review for file ${decision.file_id}: ${response.status} ${response.statusText}`);
        }
        
        return response.json();
      });
      
      await Promise.all(savePromises);
      
      // Call completion endpoint
      try {
        const completeResponse = await fetch(`${API_BASE}/verifier/complete-verification`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (completeResponse.ok) {
          const completeResult = await completeResponse.json();
          console.log('Batch completion successful:', completeResult);
        } else {
          const errorText = await completeResponse.text();
          console.error('Failed to complete batch:', completeResponse.status, errorText);
        }
      } catch (error) {
        console.error('Error calling completion endpoint:', error);
      }
      
      showToast.success("All verification results saved successfully!");
      
      setTimeout(() => {
        router.push('/verifier');
      }, 2000);
      
    } catch (error) {
      console.error('Error saving verification results:', error);
      showToast.error("Failed to save verification results");
    } finally {
      setIsSaving(false);
    }
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") handlePrevious();
      else if (e.key === "ArrowRight") {
        // Only allow navigation if current file is completed
        if (currentDecision?.completed) {
          handleNext();
        }
      }
      else if (e.ctrlKey && e.key === "s") {
        e.preventDefault();
        if (allCompleted) handleSaveAll();
      }
    };
    
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handlePrevious, handleNext, allCompleted, handleSaveAll, currentDecision]);

  // Calculate verification progress
  const progress = files.length > 0 ? Math.round((completedFiles / files.length) * 100) : 0;

  if (files.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">No files to verify</h2>
          <button
            onClick={() => router.push('/verifier')}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed top-16 left-0 right-0 bottom-0 bg-gray-50 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4 flex justify-between items-center flex-shrink-0 h-20 overflow-hidden">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Verification: {batchName}</h1>
          <p className="text-gray-600 m-0">
            File {currentFileIndex + 1} of {files.length} • 
            {completedFiles} completed • 
            Progress: {progress}%
          </p>
        </div>
        
        <div className="flex gap-4">
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg font-medium bg-green-100 text-green-800">
            <FaCheck className="text-sm" />
            <span>{completedFiles} Completed</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-2 rounded-lg font-medium bg-yellow-100 text-yellow-800">
            <FaExclamationTriangle className="text-sm" />
            <span>{files.length - completedFiles} Pending</span>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 grid grid-cols-5 gap-0 h-[calc(100vh-144px)] overflow-hidden">
        {/* Left side: Media viewer */}
        <div className="col-span-3 bg-white border-r border-gray-200 flex flex-col h-full overflow-hidden relative">
          <div className="flex-1 relative overflow-hidden">
            {currentFile && (
              <MediaViewer
                key={`${currentFile.file_id}-${currentFile.file_path}`}
                mediaPath={currentFile.file_type === 'csv' || currentFile.is_csv ? currentFile.url : currentFile.file_path}
                mediaType={currentFile.file_type === 'csv' || currentFile.is_csv ? 'csv' : detectMediaType(currentFile.file_path)}
                zoomLevel={zoomLevel}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetZoom={handleResetZoom}
                showZoomControls={true}
              />
            )}
          </div>

          {/* File info */}
          <div className="px-4 py-4 border-t border-gray-200 bg-gray-50 relative flex-shrink-0">
            <h4 className="m-0 mb-1 text-base font-semibold">{currentFile?.filename}</h4>
            <p className="m-0 text-sm text-gray-500">
              Type: {currentFile?.file_type || 'Unknown'} • 
              Status: {currentFile?.processing_status} • 
              Reviews: {currentFile?.completion_count}/{batchInfo?.annotation_count || 0}
            </p>
            
            {currentDecision?.completed && (
              <div className="absolute top-3 right-3 bg-green-500 text-white px-2.5 py-1.5 rounded-xl text-xs font-semibold flex items-center gap-1 shadow-md">
                <FaCheck /> Completed
              </div>
            )}
          </div>

          {/* Progress Section */}
          {/* <div className="px-4 py-4 bg-white border-t border-gray-200 flex-shrink-0">
            <VerificationProgress
              totalFiles={files.length}
              verifierDecisions={verificationDecisions}
              formConfig={formConfig}
              className="verification-progress-media"
            />
          </div> */}
        </div>

        {/* Right side: Annotator responses and verification controls */}
        <div className="col-span-2 bg-white flex flex-col min-h-0">
          <div className="px-4 py-4 border-b border-gray-200 bg-gray-50 flex-shrink-0">
            <h3 className="text-lg font-semibold text-gray-800">Sequential Verification</h3>
            <p className="text-sm text-gray-600">
              Review and refine the annotator response for each field
            </p>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {currentFile && currentFile.reviews && formConfig.length > 0 && (
              <div className="p-3">
                {formConfig.map((field, index) => {
                  const currentFieldDecision = currentDecision?.field_decisions?.[field.field_name];
                  const annotatorResponse = getAnnotatorResponse(currentFile.reviews[0], field.field_name);
                  const isCustomMode = currentFieldDecision?.decision_type === 'custom_input';
                  
                  
                  return (
                    <div key={field.field_name} className="border border-gray-200 rounded-lg mb-4 overflow-hidden bg-white shadow-sm">
                      {/* Field Header */}
                      <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                        <h4 className="m-0 text-sm font-semibold text-gray-800 flex-1">
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1 font-bold text-base">*</span>}
                        </h4>
                        <div className="text-xs">
                          {currentFieldDecision?.final_value ? (
                            <span className="text-green-600 flex items-center gap-1 font-medium">
                              <FaCheck /> Decided
                            </span>
                          ) : (
                            <span className="text-gray-500 font-medium">Pending</span>
                          )}
                        </div>
                      </div>

                      {field.description && (
                        <p className="m-0 px-4 py-2 text-xs text-gray-500 italic bg-gray-50 border-b border-gray-200">{field.description}</p>
                      )}

                      {!isCustomMode ? (
                        <>
                          {/* Current Response Display */}
                          <div className="px-4 py-3">
                            <div className="flex items-center gap-1.5 mb-2 text-xs font-medium text-gray-600">
                              <span className="text-sm">📝</span>
                              <span>Current Annotator Response:</span>
                            </div>
                            <div className="bg-gray-50 border border-gray-200 rounded-md px-3 py-2.5 text-xs text-gray-800 leading-relaxed whitespace-pre-wrap break-words min-h-5">
                              {annotatorResponse ? formatResponseValue(annotatorResponse) : 'No response provided'}
                            </div>
                          </div>
                        </>
                      ) : (
                        <>
                          {/* Custom Input Mode */}
                          <div className="px-4 py-3">
                            <div className="flex items-center gap-1.5 mb-2.5 text-gray-600 font-medium text-xs">
                              <FaEdit className="text-blue-600 text-xs" />
                              <span>Provide your own response</span>
                            </div>
                            
                            <DynamicField
                              config={field}
                              value={currentFieldDecision?.custom_response}
                              onChange={(fieldName, value) => handleFieldDecisionChange(fieldName, {
                                decision_type: 'custom_input',
                                custom_response: value,
                                final_value: value
                              })}
                            />
                          </div>
                        </>
                      )}

                      {/* Compact Mode Toggle */}
                      <div className="flex gap-1.5 border-t border-gray-200 px-4 py-2.5 bg-gray-50">
                        <button
                          className={`flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer transition-all duration-200 flex items-center justify-center gap-1.5 text-xs font-medium min-h-8 ${!isCustomMode && currentFieldDecision?.final_value ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600 shadow-md' : 'hover:bg-gray-50 hover:border-gray-400'}`}
                          onClick={() => {
                            if (annotatorResponse !== null) {
                              handleFieldDecisionChange(field.field_name, {
                                decision_type: 'annotator_choice',
                                chosen_annotator_id: currentFile.reviews[0].annotator_id,
                                final_value: annotatorResponse
                              });
                            }
                          }}
                        >
                          <FaUser /> Use Response
                        </button>
                        <button
                          className={`flex-1 px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer transition-all duration-200 flex items-center justify-center gap-1.5 text-xs font-medium min-h-8 ${isCustomMode ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white border-blue-600 shadow-md' : 'hover:bg-gray-50 hover:border-gray-400'}`}
                          onClick={() => {
                            if (!isCustomMode) {
                              handleFieldDecisionChange(field.field_name, {
                                decision_type: 'custom_input',
                                custom_response: currentFieldDecision?.final_value || null,
                                final_value: currentFieldDecision?.final_value || null
                              });
                            }
                          }}
                        >
                          <FaEdit /> Edit
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
              
          {/* Fixed Controls */}
          <div className="border-t border-gray-200 px-4 py-4 bg-gray-50 flex flex-col gap-3 flex-shrink-0">
            <div className="flex justify-between items-center">
              <button
                className="px-2.5 py-1.5 bg-gray-500 text-white border border-gray-600 rounded cursor-pointer transition-all duration-200 flex items-center gap-1 text-xs min-w-20 max-w-25 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-400 hover:bg-gray-600 hover:border-gray-700"
                onClick={handlePrevious}
                disabled={currentFileIndex === 0}
              >
                <FaArrowLeft />
                Previous
              </button>

              <span className="font-medium text-gray-700">
                {currentFileIndex + 1} / {files.length}
              </span>

              <button
                className="px-2.5 py-1.5 bg-gray-500 text-white border border-gray-600 rounded cursor-pointer transition-all duration-200 flex items-center gap-1 text-xs min-w-20 max-w-25 font-medium disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-400 hover:bg-gray-600 hover:border-gray-700"
                onClick={handleNext}
                disabled={currentFileIndex === files.length - 1 || !currentDecision?.completed}
                title={!currentDecision?.completed ? "Please mark current file as complete first" : "Next file"}
              >
                Next
                <FaArrowRight />
              </button>
            </div>
            
            <button
              className="w-full px-3 py-3 bg-green-600 text-white border-none rounded-lg font-medium cursor-pointer transition-colors duration-200 flex items-center justify-center gap-2 disabled:bg-gray-500 disabled:cursor-not-allowed hover:bg-green-700"
              onClick={markFileCompleted}
              disabled={currentDecision?.completed}
            >
              {currentDecision?.completed ? (
                <>
                  <FaCheck /> Completed
                </>
              ) : (
                <>
                  <FaCheck /> Mark Complete
                </>
              )}
            </button>
            
            {allCompleted && (
              <button
                className="w-full px-3 py-3 bg-blue-600 text-white border-none rounded-lg font-medium cursor-pointer transition-colors duration-200 flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed hover:bg-blue-700"
                onClick={handleSaveAll}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <FaExclamationTriangle className="animate-spin" /> Saving...
                  </>
                ) : (
                  <>
                    <FaSave /> Save All Results
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>

    </div>
  );
}
