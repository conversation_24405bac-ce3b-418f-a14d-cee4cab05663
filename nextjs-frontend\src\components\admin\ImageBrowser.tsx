import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import {
  FaChevronLeft,
  FaChevronRight,
  FaSyncAlt,
  FaFolder,
  FaPlay,
  FaVolumeUp,
  FaFileAlt,
  FaFilePdf,
} from "react-icons/fa";
import { authFetch } from "@/lib/authFetch";
import { API_BASE_URL } from "@/lib/api";

type MediaType = { path: string; name: string; type?: string };

interface MediaBrowserProps {
  images: MediaType[]; // Keep as 'images' for backward compatibility
  folder: string;
  onBack: () => void;
  onRefresh: () => void;
  page?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  loading?: boolean;
}

const MediaBrowser: React.FC<MediaBrowserProps> = ({
  images: media,
  folder,
  onBack,
  onRefresh,
  page = 1,
  totalPages = 1,
  onPageChange,
  loading = false,
}) => {
  // Modal state and navigation
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0);
  const [zoom, setZoom] = useState(1);
  // Loading state for refresh and page changes
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isChangingPage, setIsChangingPage] = useState(false);
  // Video blob URLs for authenticated access
  const [videoBlobUrls, setVideoBlobUrls] = useState<{[key: string]: string}>({});
  const [videoLoadingStates, setVideoLoadingStates] = useState<{[key: string]: boolean}>({});

  const zoomStep = 0.15;
  const minZoom = 0.5;
  const maxZoom = 5;
  const modalImageRef = useRef<HTMLImageElement>(null);

  // Function to get optimized streaming URL for videos
  const getStreamingVideoUrl = async (videoPath: string): Promise<string> => {
    // Check if we already have a streaming URL for this video
    if (videoBlobUrls[videoPath]) {
      return videoBlobUrls[videoPath];
    }

    // Check if we're already processing this video
    if (videoLoadingStates[videoPath]) {
      // Wait for the loading to complete
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (!videoLoadingStates[videoPath]) {
            clearInterval(checkInterval);
            if (videoBlobUrls[videoPath]) {
              resolve(videoBlobUrls[videoPath]);
            } else {
              reject(new Error('Failed to get streaming URL'));
            }
          }
        }, 100);
      });
    }

    // Mark as loading
    setVideoLoadingStates(prev => ({ ...prev, [videoPath]: true }));

    try {
      console.log('Fetching streaming URL for video:', videoPath);
      
      // Get optimized streaming URL - no fallbacks for MinIO projects
      const streamUrlResponse = await authFetch(`${API_BASE_URL}/admin/video-stream-url/${videoPath}`);
      
      if (!streamUrlResponse.ok) {
        const errorText = await streamUrlResponse.text().catch(() => 'Unknown error');
        throw new Error(`Failed to get streaming URL (${streamUrlResponse.status}): ${errorText}`);
      }
      
      const streamingInfo = await streamUrlResponse.json();
      console.log('Got streaming info:', streamingInfo);
      
      const streamingUrl = streamingInfo.streaming_url;
      
      // Store the streaming URL
      setVideoBlobUrls(prev => ({ ...prev, [videoPath]: streamingUrl }));
      
      console.log('Using optimized streaming URL for video:', videoPath, '→', streamingUrl);
      return streamingUrl;
      
    } catch (error) {
      console.error('Error getting video URL:', error);
      throw error;
    } finally {
      // Mark as not loading
      setVideoLoadingStates(prev => ({ ...prev, [videoPath]: false }));
    }
  };

  // Cleanup streaming URLs when component unmounts
  useEffect(() => {
    return () => {
      Object.values(videoBlobUrls).forEach(url => {
        // Only revoke blob URLs, not regular URLs
        if (url.startsWith('blob:')) {
          URL.revokeObjectURL(url);
        }
      });
    };
  }, [videoBlobUrls]);

  const prevMedia = () =>
    currentMediaIndex > 0 && setCurrentMediaIndex((i) => i - 1);
  const nextMedia = () =>
    currentMediaIndex < media.length - 1 && setCurrentMediaIndex((i) => i + 1);
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    if (e.deltaY < 0 && zoom < maxZoom)
      setZoom((z) => Math.min(z + zoomStep, maxZoom));
    if (e.deltaY > 0 && zoom > minZoom)
      setZoom((z) => Math.max(z - zoomStep, minZoom));
  };
  const resetZoom = () => setZoom(1);
  const closeModal = () => {
    setIsModalOpen(false);
    setZoom(1);
  };

  // Handle refresh with loading state
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle page change with loading state
  const handlePageChange = async (newPage: number) => {
    if (!onPageChange) return;
    setIsChangingPage(true);
    try {
      await onPageChange(newPage);
    } finally {
      setIsChangingPage(false);
    }
  };

  const folderName = folder.split("/").filter(Boolean).pop() || "Root";
  const isLoading = loading || isRefreshing || isChangingPage;
  const mediaType = media.length > 0 ? media[0].type || 'image' : 'image';
  const mediaTypeName = mediaType === 'video' ? 'Videos' : 'Images';

  // Component for authenticated video loading
  const AuthenticatedVideo: React.FC<{ 
    videoPath: string; 
    onError?: (error: any) => void;
    onCanPlay?: () => void;
    style?: React.CSSProperties;
    [key: string]: any;
  }> = ({ videoPath, onError, onCanPlay, style, ...props }) => {
    const [blobUrl, setBlobUrl] = useState<string>('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string>('');
    const [videoInfo, setVideoInfo] = useState<any>(null);

    useEffect(() => {
      const loadVideo = async () => {
        try {
          setLoading(true);
          setError('');
          console.log('Loading authenticated video for:', videoPath);
          
          // First, get video diagnostic information
          const infoUrl = `${API_BASE_URL}/admin/video-info/${videoPath}`;
          
          try {
            console.log('Fetching video info from:', infoUrl);
            const infoResponse = await authFetch(infoUrl);
            if (infoResponse.ok) {
              const info = await infoResponse.json();
              setVideoInfo(info);
              console.log('Video diagnostic info:', info);
              
              // Check if video exists and is supported
              if (!info.file_exists) {
                throw new Error(`Video file not found: ${videoPath}`);
              }
              
              if (!info.is_supported_format) {
                throw new Error(`Unsupported video format: ${info.file_extension}. Supported formats: MP4, WebM, OGV`);
              }
              
              if (info.file_size === 0) {
                throw new Error('Video file is empty (0 bytes)');
              }
              
              console.log(`Video file OK: ${info.file_extension} format, ${info.file_size} bytes, ${info.content_type}`);
            }
          } catch (infoError) {
            console.warn('Could not fetch video info (endpoint may not be available):', infoError);
          }
          
          const url = await getStreamingVideoUrl(videoPath);
          setBlobUrl(url);
          console.log('Authenticated video loaded successfully:', videoPath);
        } catch (err) {
          console.error('Failed to load authenticated video:', err);
          const errorMsg = err instanceof Error ? err.message : 'Failed to load video';
          setError(errorMsg);
          onError?.(err);
        } finally {
          setLoading(false);
        }
      };

      if (videoPath) {
        loadVideo();
      }
    }, [videoPath]);

    if (loading) {
      return (
        <div 
          className="flex items-center justify-center bg-gray-900 text-white h-96"
          style={style}
        >
          <div className="text-center">
            <div className="spinner-border mb-2" role="status">
              <span className="visually-hidden">Loading video...</span>
            </div>
            <div>Loading video...</div>
            {videoInfo && (
              <small className="mt-2 d-block">
                {videoInfo.file_extension?.toUpperCase()} • {Math.round(videoInfo.file_size / 1024 / 1024 * 100) / 100} MB
              </small>
            )}
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div 
          className="flex items-center justify-center bg-red-600 text-white p-3 h-96"
          style={style}
        >
          <div className="text-center">
            <div className="mb-2">Video Error</div>
            <div className="mb-3">{error}</div>
            {videoInfo && (
              <div className="text-start">
                <small><strong>File Info:</strong></small><br/>
                <small>Path: {videoInfo.video_path}</small><br/>
                <small>Exists: {videoInfo.file_exists ? '✅' : '❌'}</small><br/>
                <small>Size: {videoInfo.file_size} bytes</small><br/>
                <small>Format: {videoInfo.file_extension} ({videoInfo.content_type})</small><br/>
                <small>Browser Support: {videoInfo.is_supported_format ? '✅' : '❌'}</small><br/>
                <small>Cached: {videoInfo.is_cached ? '✅' : '❌'}</small>
              </div>
            )}
            <button 
              className="btn btn-sm btn-outline-light mt-2"
              onClick={() => {
                setError('');
                setBlobUrl('');
                setLoading(true);
              }}
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return (
      <video
        src={blobUrl}
        controls
        preload="metadata"
        className="max-w-full max-h-[80vh] w-full"
        style={style}
        onCanPlay={() => {
          console.log('Authenticated video can play:', videoPath);
          onCanPlay?.();
        }}
        onError={(e) => {
          const target = e.target as HTMLVideoElement;
          let errorMsg = 'Video playback failed';
          
          if (target.error) {
            const errorCodes = {
              1: 'MEDIA_ERR_ABORTED - Video loading was aborted',
              2: 'MEDIA_ERR_NETWORK - Network error while loading video',
              3: 'MEDIA_ERR_DECODE - Video decoding failed (codec/format issue)',
              4: 'MEDIA_ERR_SRC_NOT_SUPPORTED - Video format not supported by browser'
            };
            
            errorMsg = errorCodes[target.error.code as keyof typeof errorCodes] || `Unknown error (code: ${target.error.code})`;
            
            if (target.error.code === 3) {
              errorMsg += '\n\nThis usually means:\n• Video codec is not browser-compatible\n• Video file is corrupted\n• Video encoding is invalid';
            }
          }
          
          console.error('Video playback error:', {
            videoPath,
            errorCode: target.error?.code,
            errorMessage: target.error?.message,
            networkState: target.networkState,
            readyState: target.readyState,
            videoInfo
          });
          
          setError(errorMsg);
          onError?.(e);
        }}
        onLoadedData={() => {
          console.log('Video data loaded successfully:', videoPath);
        }}
        onLoadedMetadata={() => {
          console.log('Video metadata loaded:', videoPath);
        }}
        {...props}
      >
        Your browser does not support the video tag.
      </video>
    );
  };

  return (
    <div>
      {/* Full Page Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 w-full h-full flex items-center justify-center bg-black/70 z-[9999]">
          <div className="text-center text-white">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-3"
              role="status"
            >
              <span className="visually-hidden">Loading...</span>
            </div>
            <h4 className="mb-2">
              {isRefreshing
                ? `Refreshing ${mediaTypeName}`
                : isChangingPage
                ? "Loading Page"
                : `Loading ${mediaTypeName}`}
            </h4>
            <p className="mb-0">
              {isRefreshing
                ? `Updating ${mediaTypeName.toLowerCase()} list...`
                : isChangingPage
                ? `Loading ${mediaTypeName.toLowerCase()} for the selected page...`
                : `Please wait while ${mediaTypeName.toLowerCase()} are being loaded...`}
            </p>
          </div>
        </div>
      )}

      <div className="d-flex mb-3">
        <button
          className="btn btn-outline-primary me-2"
          onClick={onBack}
          disabled={isLoading}
        >
          <FaChevronLeft className="me-1" /> Back
        </button>
        <button
          className="btn btn-outline-secondary"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          {isRefreshing ? (
            <span
              className="spinner-border spinner-border-sm me-1"
              role="status"
            ></span>
          ) : (
            <FaSyncAlt className="me-1" />
          )}
          Refresh
        </button>
      </div>
      <h5 className="mb-3">
        <FaFolder className="me-1" /> {folderName} ({mediaTypeName})
      </h5>
      <div className="row row-cols-4 g-4">
        {media.map((item, idx) => (
          <div key={idx} className="col">
            <div
              role="button"
              className={`card h-100 ${isLoading ? "cursor-not-allowed opacity-60" : "cursor-pointer opacity-100"}`}
              onClick={() => {
                if (!isLoading) {
                  setCurrentMediaIndex(idx);
                  setIsModalOpen(true);
                }
              }}
            >
              {item.type === 'video' ? (
                <div 
                  className="h-48 flex items-center justify-center bg-gray-900 text-white relative"
                >
                  <div className="d-flex align-items-center justify-content-center h-100">
                    <FaPlay size={50} className="text-white opacity-75" />
                  </div>
                  <div className="position-absolute bottom-0 start-0 end-0 bg-dark bg-opacity-75 text-center p-1">
                    <small className="text-white">📹 Video</small>
                  </div>
                </div>
              ) : item.type === 'audio' ? (
                <div 
                  className="h-48 flex items-center justify-center bg-blue-600 text-white"
                >
                  <FaVolumeUp size={40} />
                </div>
              ) : item.type === 'text' ? (
                <div 
                  className="h-48 flex items-center justify-center bg-green-600 text-white"
                >
                  <FaFileAlt size={40} />
                </div>
              ) : item.type === 'pdf' ? (
                <div 
                  className="h-48 flex items-center justify-center bg-red-600 text-white"
                >
                  <FaFilePdf size={40} />
                </div>
              ) : (
                <Image
                  src={item.path}
                  alt={item.name}
                  className="card-img-top h-48 object-cover"
                  width={320}
                  height={200}
                  unoptimized
                />
              )}
              <div className="card-body p-2 text-center">
                <small className="text-muted d-block" title={item.name}>
                  {item.name}
                </small>
              </div>
            </div>
          </div>
        ))}
      </div>
      {/* Pagination Controls */}
      <div className="d-flex justify-content-center align-items-center mt-3">
        <button
          className="btn btn-outline-secondary me-2"
          onClick={() => handlePageChange(page - 1)}
          disabled={page <= 1 || isLoading}
        >
          {isChangingPage && page > 1 ? (
            <span
              className="spinner-border spinner-border-sm me-1"
              role="status"
            ></span>
          ) : null}
          Previous
        </button>
        <span>
          Page {page} of {totalPages}
        </span>
        <button
          className="btn btn-outline-secondary ms-2"
          onClick={() => handlePageChange(page + 1)}
          disabled={page >= totalPages || isLoading}
        >
          {isChangingPage && page < totalPages ? (
            <span
              className="spinner-border spinner-border-sm me-1"
              role="status"
            ></span>
          ) : null}
          Next
        </button>
      </div>
      {/* Media Modal */}
      {isModalOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          tabIndex={-1}
        >
          <div className="modal-dialog modal-lg modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  {media[currentMediaIndex]?.name}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={closeModal}
                ></button>
              </div>
              <div className="modal-body p-0">
                <div className="media-viewer d-flex align-items-center justify-content-center position-relative">
                  {currentMediaIndex > 0 && (
                    <button
                      className="nav-btn prev-btn absolute left-0 btn btn-link z-10"
                      onClick={prevMedia}
                    >
                      <FaChevronLeft />
                    </button>
                  )}
                  <div className="media-container text-center">
                    {media[currentMediaIndex]?.type === 'video' ? (
                      <div>
                        <AuthenticatedVideo
                          videoPath={media[currentMediaIndex]?.path}
                          onError={(e) => {
                            console.error('Video playback error in modal:', {
                              error: e,
                              videoSrc: media[currentMediaIndex]?.path,
                              networkState: (e.target as HTMLVideoElement).networkState,
                              readyState: (e.target as HTMLVideoElement).readyState,
                              errorCode: (e.target as HTMLVideoElement).error?.code,
                              errorMessage: (e.target as HTMLVideoElement).error?.message,
                              currentTime: (e.target as HTMLVideoElement).currentTime,
                              duration: (e.target as HTMLVideoElement).duration
                            });
                          }}
                          onCanPlay={() => {
                            console.log('Authenticated video can play in modal:', media[currentMediaIndex]?.path);
                          }}
                        />
                        <div className="mt-3 d-flex justify-content-center gap-2">
                          <button 
                            className="btn btn-sm btn-outline-primary"
                            onClick={async () => {
                              const currentPath = media[currentMediaIndex]?.path;
                              if (currentPath) {
                                // Clear cache and reload
                                try {
                                  const clearUrl = `${API_BASE_URL}/admin/clear-video-cache/${currentPath}`;
                                  await authFetch(clearUrl, { method: 'DELETE' });
                                  console.log('Video cache cleared for:', currentPath);
                                  
                                  // Clear local blob URL
                                  if (videoBlobUrls[currentPath]) {
                                    URL.revokeObjectURL(videoBlobUrls[currentPath]);
                                    setVideoBlobUrls(prev => {
                                      const newUrls = { ...prev };
                                      delete newUrls[currentPath];
                                      return newUrls;
                                    });
                                  }
                                  
                                  // Force reload modal
                                  setIsModalOpen(false);
                                  setTimeout(() => setIsModalOpen(true), 100);
                                } catch (error) {
                                  console.error('Failed to clear video cache:', error);
                                }
                              }
                            }}
                            title="Clear cache and retry"
                          >
                            🗑️ Clear Cache
                          </button>
                          <button 
                            className="btn btn-sm btn-outline-info"
                            onClick={async () => {
                              const currentPath = media[currentMediaIndex]?.path;
                              if (currentPath) {
                                const infoUrl = `${API_BASE_URL}/admin/video-info/${currentPath}`;
                                try {
                                  const response = await authFetch(infoUrl);
                                  const info = await response.json();
                                  console.log('Video diagnostic info:', info);
                                  alert(`Video Info:\nFile: ${info.video_path}\nExists: ${info.file_exists}\nSize: ${info.file_size} bytes\nFormat: ${info.file_extension}\nType: ${info.content_type}\nCached: ${info.is_cached}\nSupported: ${info.is_supported_format}`);
                                } catch (error) {
                                  console.error('Failed to get video info:', error);
                                  alert('Failed to get video information');
                                }
                              }
                            }}
                            title="Show video diagnostic information"
                          >
                            ℹ️ Info
                          </button>
                        </div>
                      </div>
                    ) : media[currentMediaIndex]?.type === 'audio' ? (
                      <div className="text-center p-4">
                        <FaVolumeUp size={80} className="text-primary mb-3" />
                        <audio
                          src={media[currentMediaIndex]?.path}
                          controls
                          autoPlay
                          className="w-full"
                        >
                          Your browser does not support the audio tag.
                        </audio>
                      </div>
                    ) : media[currentMediaIndex]?.type === 'text' ? (
                      <iframe
                        src={media[currentMediaIndex]?.path}
                        className="w-full h-[80vh] border-none"
                        title={media[currentMediaIndex]?.name}
                      />
                    ) : media[currentMediaIndex]?.type === 'pdf' ? (
                      <iframe
                        src={media[currentMediaIndex]?.path}
                        className="w-full h-[80vh] border-none"
                        title={media[currentMediaIndex]?.name}
                      />
                    ) : (
                      <Image
                        ref={modalImageRef}
                        src={media[currentMediaIndex]?.path}
                        alt={media[currentMediaIndex]?.name}
                        style={{
                          transform: `scale(${zoom})`,
                          maxWidth: "100%",
                          maxHeight: "80vh",
                          objectFit: "contain",
                        }}
                        width={1000}
                        height={800}
                        unoptimized
                        onWheel={handleWheel}
                        onDoubleClick={resetZoom}
                      />
                    )}
                  </div>
                  {currentMediaIndex < media.length - 1 && (
                    <button
                      className="nav-btn next-btn absolute right-0 btn btn-link z-10"
                      onClick={nextMedia}
                    >
                      <FaChevronRight />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaBrowser;
