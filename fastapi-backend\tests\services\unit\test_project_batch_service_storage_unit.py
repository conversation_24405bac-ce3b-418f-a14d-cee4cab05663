"""
Comprehensive storage tests for ProjectBatchService.
Tests batch operations with MinIO, NAS-FTP, and hybrid storage configurations.
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, List, Optional
import asyncio
import time
from datetime import datetime

from app.services.project_batch_service import ProjectBatchService

class TestProjectBatchServiceStorage:
    """Storage-focused tests for ProjectBatchService."""
    
    @pytest.fixture
    def batch_service(self):
        """ProjectBatchService instance for testing."""
        return ProjectBatchService()
    
    @pytest.fixture
    def storage_test_projects(self):
        """Mock projects with different storage configurations."""
        return {
            'minio_only': {
                'project_code': 'MINIO_BATCH_001',
                'connection_type': 'MinIO',
                'credentials': {
                    'endpoint': 'https://minio.test.com',
                    'access_key': 'test_access',
                    'secret_key': 'test_secret',
                    'bucket': 'test-bucket'
                },
                'folder_path': '/minio/batches'
            },
            'nas_only': {
                'project_code': 'NAS_BATCH_001',
                'connection_type': 'NAS-FTP',
                'credentials': {
                    'ftp_host': 'nas.test.com',
                    'ftp_username': 'nas_user',
                    'ftp_password': 'nas_pass',
                    'base_path': '/nas/storage'
                },
                'folder_path': '/nas/batches'
            },
            'hybrid': {
                'project_code': 'HYBRID_BATCH_001',
                'connection_type': 'Hybrid',
                'primary_storage': 'MinIO',
                'fallback_storage': 'NAS-FTP',
                'credentials': {
                    'minio': {'endpoint': 'https://minio.test.com'},
                    'nas': {'ftp_host': 'nas.test.com'}
                }
            }
        }
    
    @pytest.fixture
    def mock_file_listings(self):
        """Mock file listings for different storage types."""
        return {
            'minio_files': [
                {'name': 'video_001.mp4', 'size': 50 * 1024 * 1024, 'type': 'video'},
                {'name': 'video_002.mp4', 'size': 75 * 1024 * 1024, 'type': 'video'},
                {'name': 'audio_001.wav', 'size': 30 * 1024 * 1024, 'type': 'audio'}
            ],
            'nas_files': [
                {'name': 'image_001.jpg', 'size': 5 * 1024 * 1024, 'type': 'image'},
                {'name': 'image_002.jpg', 'size': 8 * 1024 * 1024, 'type': 'image'},
                {'name': 'doc_001.pdf', 'size': 2 * 1024 * 1024, 'type': 'document'}
            ],
            'mixed_files': [
                {'name': 'large_video.mp4', 'size': 200 * 1024 * 1024, 'type': 'video'},
                {'name': 'small_image.jpg', 'size': 1 * 1024 * 1024, 'type': 'image'}
            ]
        }

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_batches_minio_storage(self, batch_service, storage_test_projects, mock_file_listings):
        """Test batch creation with MinIO storage backend."""
        project_config = storage_test_projects['minio_only']
        minio_files = mock_file_listings['minio_files']
        
        with patch.object(batch_service, 'get_project_info') as mock_get_info:
            mock_get_info.return_value = project_config
            
            with patch('app.core.minio_utils.get_minio_connector_from_credentials') as mock_minio_connector:
                mock_connector = MagicMock()
                mock_connector.list_files.return_value = minio_files
                mock_connector.connection_type = 'MinIO'
                mock_minio_connector.return_value = mock_connector
                
                with patch.object(batch_service, '_create_database_batches') as mock_create_batches:
                    mock_create_batches.return_value = {
                        'batches_created': 2,
                        'total_files': len(minio_files),
                        'batch_identifiers': ['MINIO_BATCH_001', 'MINIO_BATCH_002']
                    }
                    
                    result = await batch_service.create_batches_from_folder(
                        project_config['project_code'],
                        project_config['folder_path'],
                        files_per_batch=2,
                        content_type='mixed'
                    )
                    
                    success, message, num_batches = result
                    
                    assert success is True
                    assert num_batches == 2
                    assert 'MinIO' in message
                    assert mock_minio_connector.called
                    assert mock_connector.list_files.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_create_batches_nas_storage(self, batch_service, storage_test_projects, mock_file_listings):
        """Test batch creation with NAS-FTP storage backend."""
        project_config = storage_test_projects['nas_only']
        nas_files = mock_file_listings['nas_files']
        
        with patch.object(batch_service, 'get_project_info') as mock_get_info:
            mock_get_info.return_value = project_config
            
            with patch('app.core.nas_connector.get_ftp_connector_from_credentials') as mock_nas_connector:
                mock_connector = MagicMock()
                mock_connector.list_files.return_value = nas_files
                mock_connector.connection_type = 'NAS-FTP'
                mock_nas_connector.return_value = mock_connector
                
                with patch.object(batch_service, '_create_database_batches') as mock_create_batches:
                    mock_create_batches.return_value = {
                        'batches_created': 1,
                        'total_files': len(nas_files),
                        'batch_identifiers': ['NAS_BATCH_001']
                    }
                    
                    result = await batch_service.create_batches_from_folder(
                        project_config['project_code'],
                        project_config['folder_path'],
                        files_per_batch=3,
                        content_type='image'
                    )
                    
                    success, message, num_batches = result
                    
                    assert success is True
                    assert num_batches == 1
                    assert 'NAS' in message or 'FTP' in message
                    assert mock_nas_connector.called

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_connector_pooling(self, batch_service, storage_test_projects):
        """Test storage connector pooling and reuse."""
        project_config = storage_test_projects['minio_only']
        
        with patch.object(batch_service, '_get_or_create_connector_pool') as mock_pool:
            # Mock connector pool behavior
            mock_pool.return_value = {
                'pool_size': 5,
                'active_connections': 2,
                'available_connections': 3,
                'connector_reused': True
            }
            
            # Simulate multiple concurrent batch operations
            batch_operations = [
                {'batch_id': f'BATCH_{i:03d}', 'files': 10}
                for i in range(10)
            ]
            
            with patch.object(batch_service, '_execute_batch_operation_with_pooled_connector') as mock_execute:
                mock_execute.return_value = {'success': True, 'connector_reused': True}
                
                results = []
                for operation in batch_operations:
                    result = await batch_service._execute_batch_operation_with_pooled_connector(
                        operation, project_config
                    )
                    results.append(result)
                
                # Verify all operations succeeded with connection reuse
                assert all(r['success'] for r in results)
                assert all(r['connector_reused'] for r in results)
                assert mock_pool.call_count <= len(batch_operations)  # Pool reuse should reduce calls

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_large_file_batch_operations(self, batch_service, storage_test_projects, performance_monitor):
        """Test batch operations with large files across different storage types."""
        large_files = [
            {'name': f'large_video_{i}.mp4', 'size': 500 * 1024 * 1024}  # 500MB each
            for i in range(20)  # 10GB total
        ]
        
        storage_performance = {}
        
        for storage_name, project_config in storage_test_projects.items():
            if storage_name == 'hybrid':  # Skip hybrid for this test
                continue
                
            performance_monitor.start()
            
            with patch.object(batch_service, 'get_project_info') as mock_get_info:
                mock_get_info.return_value = project_config
                
                with patch.object(batch_service, '_get_storage_connector') as mock_connector:
                    mock_conn = MagicMock()
                    mock_conn.list_files.return_value = large_files
                    mock_conn.connection_type = project_config['connection_type']
                    
                    # Simulate different performance for different storage types
                    if project_config['connection_type'] == 'MinIO':
                        mock_conn.batch_processing_speed = 50  # MB/s
                    else:  # NAS-FTP
                        mock_conn.batch_processing_speed = 20  # MB/s
                    
                    mock_connector.return_value = mock_conn
                    
                    with patch.object(batch_service, '_create_database_batches') as mock_create:
                        mock_create.return_value = {
                            'batches_created': 4,
                            'processing_time': 120.5 if project_config['connection_type'] == 'NAS-FTP' else 45.2
                        }
                        
                        result = await batch_service.create_batches_from_folder(
                            project_config['project_code'],
                            '/large_files',
                            files_per_batch=5
                        )
                        
                        performance_monitor.stop()
                        execution_time = performance_monitor.get_execution_time()
                        
                        storage_performance[storage_name] = {
                            'execution_time': execution_time,
                            'batches_created': result[2],
                            'files_processed': len(large_files)
                        }
        
        # MinIO should generally be faster for large files
        if 'minio_only' in storage_performance and 'nas_only' in storage_performance:
            minio_time = storage_performance['minio_only']['execution_time']
            nas_time = storage_performance['nas_only']['execution_time']
            
            # Allow some variance, but MinIO should typically be faster
            assert minio_time <= nas_time * 1.2  # Within 20% tolerance

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_failure_and_recovery(self, batch_service, storage_test_projects):
        """Test batch service recovery from storage failures."""
        project_config = storage_test_projects['minio_only']
        
        failure_scenarios = [
            {'type': 'connection_timeout', 'retry_count': 3, 'recoverable': True},
            {'type': 'authentication_failure', 'retry_count': 1, 'recoverable': False},
            {'type': 'storage_full', 'retry_count': 0, 'recoverable': False},
            {'type': 'network_partition', 'retry_count': 5, 'recoverable': True}
        ]
        
        for scenario in failure_scenarios:
            with patch.object(batch_service, '_attempt_storage_operation') as mock_attempt:
                if scenario['recoverable']:
                    # Fail first few attempts, then succeed
                    side_effects = [Exception(scenario['type'])] * scenario['retry_count']
                    side_effects.append({'success': True, 'recovered': True})
                    mock_attempt.side_effect = side_effects
                else:
                    # Always fail
                    mock_attempt.side_effect = Exception(scenario['type'])
                
                with patch.object(batch_service, '_handle_storage_failure') as mock_handle:
                    mock_handle.return_value = {
                        'failure_handled': True,
                        'recovery_attempted': scenario['recoverable'],
                        'final_success': scenario['recoverable']
                    }
                    
                    try:
                        result = await batch_service.create_batches_from_folder(
                            project_config['project_code'],
                            '/test/folder',
                            files_per_batch=5
                        )
                        
                        if scenario['recoverable']:
                            assert result[0] is True  # Should succeed after recovery
                        
                    except Exception as e:
                        if not scenario['recoverable']:
                            assert scenario['type'] in str(e)
                        else:
                            pytest.fail(f"Recoverable scenario {scenario['type']} should not raise exception")

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_concurrent_batch_processing_different_storages(self, batch_service, storage_test_projects):
        """Test concurrent batch processing across different storage types."""
        # Create concurrent operations on different storage projects
        concurrent_operations = [
            {'project': storage_test_projects['minio_only'], 'files': 15},
            {'project': storage_test_projects['nas_only'], 'files': 12},
            {'project': storage_test_projects['minio_only'], 'files': 8},  # Another MinIO project
        ]
        
        async def process_batch_operation(operation):
            """Simulate batch processing operation."""
            project_config = operation['project']
            
            with patch.object(batch_service, 'get_project_info') as mock_get_info:
                mock_get_info.return_value = project_config
                
                with patch.object(batch_service, '_get_storage_connector') as mock_connector:
                    mock_conn = MagicMock()
                    mock_conn.list_files.return_value = [
                        {'name': f'file_{i}.jpg', 'size': 1024 * 1024}
                        for i in range(operation['files'])
                    ]
                    mock_connector.return_value = mock_conn
                    
                    with patch.object(batch_service, '_create_database_batches') as mock_create:
                        mock_create.return_value = {
                            'batches_created': operation['files'] // 5,
                            'total_files': operation['files']
                        }
                        
                        # Simulate processing time
                        await asyncio.sleep(0.1)  # Brief processing time
                        
                        result = await batch_service.create_batches_from_folder(
                            project_config['project_code'],
                            '/concurrent/test',
                            files_per_batch=5
                        )
                        
                        return {
                            'project_code': project_config['project_code'],
                            'storage_type': project_config['connection_type'],
                            'success': result[0],
                            'batches_created': result[2]
                        }
        
        # Execute all operations concurrently
        results = await asyncio.gather(*[
            process_batch_operation(op) for op in concurrent_operations
        ], return_exceptions=True)
        
        # Verify no exceptions and all succeeded
        exceptions = [r for r in results if isinstance(r, Exception)]
        assert len(exceptions) == 0, f"Found exceptions: {exceptions}"
        
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(concurrent_operations)
        
        # Verify each project processed correctly
        for result in successful_results:
            assert result['success'] is True
            assert result['batches_created'] > 0

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_optimization_based_on_file_types(self, batch_service, storage_test_projects):
        """Test automatic storage optimization based on file types and sizes."""
        mixed_files = [
            {'name': 'large_video.mp4', 'size': 500 * 1024 * 1024, 'type': 'video'},
            {'name': 'medium_audio.wav', 'size': 50 * 1024 * 1024, 'type': 'audio'},
            {'name': 'small_image.jpg', 'size': 2 * 1024 * 1024, 'type': 'image'},
            {'name': 'tiny_doc.pdf', 'size': 100 * 1024, 'type': 'document'}
        ]
        
        optimization_rules = {
            'large_files_threshold': 100 * 1024 * 1024,  # 100MB
            'video_audio_preferred': 'MinIO',
            'image_document_preferred': 'NAS-FTP',
            'cost_optimization_enabled': True
        }
        
        with patch.object(batch_service, '_apply_storage_optimization') as mock_optimize:
            mock_optimize.return_value = {
                'optimized_distribution': {
                    'MinIO': ['large_video.mp4', 'medium_audio.wav'],  # Large files
                    'NAS-FTP': ['small_image.jpg', 'tiny_doc.pdf']     # Small files
                },
                'cost_savings_estimated': 25.50,
                'performance_improvement': '15%'
            }
            
            optimization_result = batch_service._apply_storage_optimization(
                mixed_files, optimization_rules, storage_test_projects['hybrid']
            )
            
            assert len(optimization_result['optimized_distribution']['MinIO']) == 2
            assert len(optimization_result['optimized_distribution']['NAS-FTP']) == 2
            assert optimization_result['cost_savings_estimated'] > 20
            
            # Verify large files go to MinIO
            minio_files = optimization_result['optimized_distribution']['MinIO']
            assert 'large_video.mp4' in minio_files
            assert 'medium_audio.wav' in minio_files

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_batch_metadata_storage_consistency(self, batch_service, storage_test_projects):
        """Test metadata consistency between database and storage backends."""
        project_config = storage_test_projects['minio_only']
        test_files = [
            {'name': 'test_001.jpg', 'size': 1024 * 1024, 'checksum': 'abc123'},
            {'name': 'test_002.jpg', 'size': 2048 * 1024, 'checksum': 'def456'}
        ]
        
        with patch.object(batch_service, '_verify_storage_metadata_consistency') as mock_verify:
            mock_verify.return_value = {
                'consistent_files': len(test_files),
                'inconsistent_files': 0,
                'metadata_matches': True,
                'checksum_verified': True,
                'size_verified': True
            }
            
            with patch.object(batch_service, '_create_database_batches') as mock_create_batches:
                mock_create_batches.return_value = {
                    'batches_created': 1,
                    'metadata_stored': True,
                    'consistency_verified': True
                }
                
                # Test batch creation with metadata verification
                consistency_result = batch_service._verify_storage_metadata_consistency(
                    test_files, project_config
                )
                
                assert consistency_result['consistent_files'] == len(test_files)
                assert consistency_result['metadata_matches'] is True
                assert consistency_result['checksum_verified'] is True
                
                # Test database batch creation
                batch_result = await batch_service._create_database_batches(
                    test_files, project_config, verify_metadata=True
                )
                
                assert batch_result['metadata_stored'] is True
                assert batch_result['consistency_verified'] is True

    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_storage_capacity_monitoring(self, batch_service, storage_test_projects):
        """Test storage capacity monitoring and alerting."""
        capacity_scenarios = [
            {'storage': 'MinIO', 'used_percentage': 85, 'warning_expected': True},
            {'storage': 'NAS-FTP', 'used_percentage': 95, 'critical_expected': True},
            {'storage': 'MinIO', 'used_percentage': 50, 'ok_expected': True}
        ]
        
        for scenario in capacity_scenarios:
            with patch.object(batch_service, '_check_storage_capacity') as mock_check:
                mock_check.return_value = {
                    'storage_type': scenario['storage'],
                    'used_percentage': scenario['used_percentage'],
                    'available_space_gb': 1000 - (scenario['used_percentage'] * 10),
                    'warning_triggered': scenario.get('warning_expected', False),
                    'critical_triggered': scenario.get('critical_expected', False),
                    'can_accept_new_batches': scenario['used_percentage'] < 90
                }
                
                capacity_result = batch_service._check_storage_capacity(
                    storage_test_projects['minio_only' if scenario['storage'] == 'MinIO' else 'nas_only']
                )
                
                if scenario.get('warning_expected'):
                    assert capacity_result['warning_triggered'] is True
                elif scenario.get('critical_expected'):
                    assert capacity_result['critical_triggered'] is True
                else:
                    assert capacity_result['warning_triggered'] is False
                    assert capacity_result['critical_triggered'] is False
                
                # High usage should prevent new batches
                if scenario['used_percentage'] >= 90:
                    assert capacity_result['can_accept_new_batches'] is False
                else:
                    assert capacity_result['can_accept_new_batches'] is True

    @pytest.mark.unit
    def test_storage_connector_configuration_validation(self, batch_service, storage_test_projects):
        """Test validation of storage connector configurations."""
        valid_configs = [storage_test_projects['minio_only'], storage_test_projects['nas_only']]
        invalid_configs = [
            # Missing credentials
            {'project_code': 'INVALID_001', 'connection_type': 'MinIO', 'credentials': None},
            # Invalid connection type
            {'project_code': 'INVALID_002', 'connection_type': 'UNKNOWN', 'credentials': {}},
            # Malformed credentials
            {'project_code': 'INVALID_003', 'connection_type': 'MinIO', 'credentials': {'invalid': 'data'}}
        ]
        
        # Test valid configurations
        for config in valid_configs:
            with patch.object(batch_service, '_validate_storage_config') as mock_validate:
                mock_validate.return_value = {
                    'valid': True,
                    'connection_type_supported': True,
                    'credentials_complete': True,
                    'connectivity_verified': True
                }
                
                validation_result = batch_service._validate_storage_config(config)
                assert validation_result['valid'] is True
                assert validation_result['credentials_complete'] is True
        
        # Test invalid configurations
        for config in invalid_configs:
            with patch.object(batch_service, '_validate_storage_config') as mock_validate:
                mock_validate.return_value = {
                    'valid': False,
                    'errors': ['Invalid configuration detected'],
                    'connection_type_supported': 'connection_type' in config and config['connection_type'] in ['MinIO', 'NAS-FTP'],
                    'credentials_complete': bool(config.get('credentials'))
                }
                
                validation_result = batch_service._validate_storage_config(config)
                assert validation_result['valid'] is False
                assert len(validation_result['errors']) > 0
