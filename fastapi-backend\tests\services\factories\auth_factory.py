"""
Authentication data factory for creating consistent test auth data across all service tests.
"""

import time
from typing import Dict, Any
try:
    from app.schemas.UserSchemas import UserRegisterRequest, LoginRequest, ChangePasswordRequest
    from app.post_db.master_models.users import UserRole
except ImportError:
    # Mock classes for testing when app is not available
    class UserRole:
        ANNOTATOR = "annotator"
        VERIFIER = "verifier"
        ADMIN = "admin"
    
    class UserRegisterRequest:
        def __init__(self, username, email, password, first_name, last_name, role):
            self.username = username
            self.email = email
            self.password = password
            self.first_name = first_name
            self.last_name = last_name
            self.role = role
    
    class LoginRequest:
        def __init__(self, username, password):
            self.username = username
            self.password = password
    
    class ChangePasswordRequest:
        def __init__(self, old_password, new_password):
            self.old_password = old_password
            self.new_password = new_password


class AuthFactory:
    """Factory for creating test authentication data."""
    
    @staticmethod
    def create_register_request(
        username: str = None,
        email: str = None,
        password: str = "SecureTestPassword123!",
        role: UserRole = UserRole.ANNOTATOR
    ) -> UserRegisterRequest:
        """Create valid user registration request."""
        timestamp = int(time.time())
        
        return UserRegisterRequest(
            username=username or f"testuser_{timestamp % 10000}",
            email=email or f"test_{timestamp % 10000}@example.com",
            password=password,
            first_name="Test",
            last_name="User",
            role=role
        )
    
    @staticmethod
    def create_login_request(
        username: str = "testuser",
        password: str = "SecureTestPassword123!"
    ) -> LoginRequest:
        """Create valid login request."""
        return LoginRequest(
            username=username,
            password=password
        )
    
    @staticmethod
    def create_password_change_request(
        old_password: str = "OldPassword123!",
        new_password: str = "NewSecurePassword456!"
    ) -> ChangePasswordRequest:
        """Create valid password change request."""
        return ChangePasswordRequest(
            old_password=old_password,
            new_password=new_password
        )
    
    @staticmethod
    def create_weak_password_register() -> UserRegisterRequest:
        """Create registration request with weak password for testing."""
        timestamp = int(time.time())
        
        return UserRegisterRequest(
            username=f"weakuser_{timestamp % 10000}",
            email=f"weak_{timestamp % 10000}@example.com",
            password="123",  # Weak password
            first_name="Weak",
            last_name="User",
            role=UserRole.ANNOTATOR
        )
    
    @staticmethod
    def create_invalid_email_register() -> UserRegisterRequest:
        """Create registration request with invalid email for testing."""
        timestamp = int(time.time())
        
        return UserRegisterRequest(
            username=f"invaliduser_{timestamp % 10000}",
            email="invalid-email-format",  # Invalid email
            password="SecureTestPassword123!",
            first_name="Invalid",
            last_name="User",
            role=UserRole.ANNOTATOR
        )
    
    @staticmethod
    def create_special_char_username_register() -> UserRegisterRequest:
        """Create registration request with special characters in username."""
        timestamp = int(time.time())
        
        return UserRegisterRequest(
            username=f"user@name#{timestamp % 1000}",  # Special characters
            email=f"special_{timestamp % 10000}@example.com",
            password="SecureTestPassword123!",
            first_name="Special",
            last_name="User",
            role=UserRole.ANNOTATOR
        )
    
    @staticmethod
    def create_token_response_mock() -> Dict[str, Any]:
        """Create mock token response data."""
        return {
            'access_token': 'mock_access_token_jwt_string',
            'refresh_token': 'mock_refresh_token_jwt_string',
            'token_type': 'bearer',
            'expires_in': 3600
        }
    
    @staticmethod
    def create_user_response_mock(
        user_id: int = None,
        username: str = None,
        role: UserRole = UserRole.ANNOTATOR
    ) -> Dict[str, Any]:
        """Create mock user response data."""
        timestamp = int(time.time())
        
        return {
            'id': user_id or (1000 + timestamp % 1000),
            'username': username or f'testuser_{timestamp % 10000}',
            'email': f'test_{timestamp % 10000}@example.com',
            'first_name': 'Test',
            'last_name': 'User',
            'role': role,
            'is_active': True,
            'created_at': f'2024-01-01T00:00:00Z'
        }
    
    @staticmethod
    def create_malicious_inputs() -> Dict[str, Dict[str, Any]]:
        """Create malicious inputs for security testing."""
        return {
            'sql_injection_login': {
                'username': "'; DROP TABLE users; --",
                'password': "password' OR '1'='1"
            },
            'xss_registration': {
                'username': "<script>alert('xss')</script>",
                'email': "<EMAIL>",
                'password': "SecurePassword123!",
                'first_name': "<img src=x onerror=alert('xss')>",
                'last_name': "User"
            },
            'oversized_inputs': {
                'username': "a" * 1000,  # Very long username
                'email': "b" * 500 + "@example.com",  # Very long email
                'password': "c" * 2000,  # Very long password
                'first_name': "d" * 500,
                'last_name': "e" * 500
            }
        }
