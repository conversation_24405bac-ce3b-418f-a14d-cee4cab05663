#!/usr/bin/env python3
"""
Comprehensive database initialization module.
Handles both master and project database initialization using the unified session manager.
"""

import argparse
import asyncio
import asyncpg  # type: ignore
import logging
import os
import subprocess
import sys

# SQLAlchemy imports
from sqlalchemy import select
from sqlalchemy.ext.asyncio import create_async_engine

# Project imports
from core.session_manager import get_session_manager, get_master_db_session, get_master_db_context
from post_db.config import DatabaseSettings, MasterDatabaseSettings, MasterUserSettings
from post_db.project_base import ProjectBase


logger = logging.getLogger("init_db")


# ==== NO MORE LOCAL CONNECTION POOLS ====
# All database connections now use the unified session manager
# This eliminates the multiple connection pool issue

def _sync_url(url: str) -> str:
    """Convert async database URL to sync for Alembic."""
    return url.replace("postgresql+asyncpg", "postgresql") if url else url


# ==== PROJECT DATABASE FUNCTIONS ====

async def test_connection() -> bool:
    """
    Test connection to project PostgreSQL database using session manager.
    """
    try:
        db_settings = DatabaseSettings()
        
        # Test using the session manager instead of direct asyncpg
        async with get_master_db_context() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT version();"))
            version = result.scalar()
            logger.info(f"Project PostgreSQL connection successful: {version}")
            return True
    except Exception as e:
        logger.error(f"Project PostgreSQL connection test failed: {e}")
        return False


async def initialize_database() -> bool:
    """
    Initialize the project PostgreSQL database by creating all tables.
    Uses unified session manager to avoid creating separate connection pools.
    """
    try:
        logger.info("Testing project PostgreSQL connection")
        if not await test_connection():
            logger.error("Failed to connect to project PostgreSQL database")
            return False
        
        logger.info("Creating project database tables from SQLAlchemy models")
        # Use unified session manager instead of creating separate engine
        session_manager = get_session_manager()
        # For project database initialization, we need to create a temporary engine
        # since project sessions require project codes, but this is for initial setup
        
        db_settings = DatabaseSettings()
        temp_engine = create_async_engine(
            db_settings.url,
            pool_size=5,  # Minimal pool for initialization
            max_overflow=0,
            pool_pre_ping=True
        )
        
        try:
            async with temp_engine.begin() as conn:
                await conn.run_sync(ProjectBase.metadata.create_all)
        finally:
            await temp_engine.dispose()  # Clean up temporary engine
            
        logger.info("Successfully initialized project PostgreSQL database")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing project PostgreSQL database: {str(e)}")
        return False


# ==== MASTER DATABASE FUNCTIONS ====

async def test_master_connection() -> bool:
    """
    Test connection to master PostgreSQL database using unified session manager.
    """
    from utils.connection_utils import test_master_connection
    return await test_master_connection()


async def initialize_master_database() -> bool:
    """
    Initialize the master PostgreSQL database by creating all required tables.
    """
    try:
        logger.info("Testing master PostgreSQL connection")
        if not await test_master_connection():
            logger.error("Failed to connect to master PostgreSQL database")
            return False
        
        logger.info("Creating master database tables")
        
        # Import master models here to avoid circular imports
        from post_db.base import Base
        from post_db.master_models.projects_registry import ProjectsRegistry
        from post_db.master_models.users import users
        from post_db.master_models.user_project_access import UserProjectAccess
        from post_db.master_models.global_workload_summary import GlobalWorkloadSummary
        from post_db.master_models.cross_project_analytics import CrossProjectAnalytics
        from post_db.master_models.allocation_strategies import AllocationStrategies
        from post_db.master_models.clients import Clients
        from post_db.master_models.user_metadata import UserMetadata
        from post_db.master_models.admin_settings import AdminSettings
        from post_db.master_models.ai_models_registry import AIModelsRegistry
        
        # Define master tables to create
        master_tables = [
            ProjectsRegistry.__table__, users.__table__, UserProjectAccess.__table__,
            GlobalWorkloadSummary.__table__, CrossProjectAnalytics.__table__,
            AllocationStrategies.__table__, Clients.__table__, UserMetadata.__table__,
            AdminSettings.__table__, AIModelsRegistry.__table__
        ]
        
        # Use unified session manager for master engine
        session_manager = get_session_manager()
        master_engine = await session_manager._get_master_engine()
        
        async with master_engine.begin() as conn:
            await conn.run_sync(lambda conn: Base.metadata.create_all(conn, tables=master_tables))
        
        logger.info("Successfully initialized master PostgreSQL database")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing master PostgreSQL database: {str(e)}")
        return False


async def seed_master_admin_user() -> bool:
    """
    Seed the master database with default users from MasterUserSettings.
    """
    try:
        # Import required modules here to avoid circular imports
        from post_db.master_models.users import users
        from core.security import hash_password
        
        master_user_settings = MasterUserSettings()
        created_users = []
        
        async with get_master_db_session() as session:
            for user_key, user_config in master_user_settings.users.items():
                # Check if user already exists
                result = await session.execute(
                    select(users).where(users.username == user_config['username'])
                )
                
                if not result.scalar_one_or_none():
                    new_user = users(
                        username=user_config['username'],
                        email=user_config['email'],
                        full_name=user_config['full_name'],
                        role=user_config['role'],
                        password_hash=hash_password(user_config['password']),
                        is_active=user_config.get('is_active', True),
                        annotation_skills=user_config.get('annotation_skills', {}),
                        max_concurrent_projects=user_config.get('max_concurrent_projects', 1),
                        max_concurrent_batches=user_config.get('max_concurrent_batches', 1)
                    )
                    session.add(new_user)
                    created_users.append(user_config['username'])
            
            if created_users:
                await session.commit()
                logger.info(f"Created {len(created_users)} users: {', '.join(created_users)}")
            else:
                logger.info("All users already exist, no seeding required")
                
        return True
                
    except Exception as e:
        logger.error(f"Error seeding users in master database: {e}")
        return False


async def init_postgresql_async() -> bool:
    """
    Initialize both PostgreSQL databases asynchronously with concurrent operations.
    """
    try:
        logger.info("Testing database connections...")
        
        # Test both connections concurrently
        project_test, master_test = await asyncio.gather(
            test_connection(),
            test_master_connection(),
            return_exceptions=True
        )
        
        # Validate connection results
        if isinstance(project_test, Exception) or not project_test:
            logger.error("Failed to connect to project PostgreSQL database")
            return False
            
        if isinstance(master_test, Exception) or not master_test:
            logger.error("Failed to connect to master PostgreSQL database")
            return False
        
        logger.info("Both database connections successful")
        
        # Initialize databases concurrently
        logger.info("Initializing databases...")
        master_init, project_init = await asyncio.gather(
            initialize_master_database(),
            initialize_database(),
            return_exceptions=True
        )
        
        # Validate initialization results
        if isinstance(master_init, Exception) or not master_init:
            logger.error("Master database initialization failed")
            return False
            
        if isinstance(project_init, Exception) or not project_init:
            logger.error("Project database initialization failed")
            return False
        
        logger.info("All databases initialized successfully")
        return True
            
    except Exception as e:
        logger.error(f"Error during PostgreSQL initialization: {e}")
        return False


def init_postgresql() -> bool:
    """Initialize PostgreSQL database (sync wrapper)."""
    return asyncio.run(init_postgresql_async())


async def seed_master_users_async() -> bool:
    """Seed the master database with default users (async optimized)."""
    try:
        logger.info("Testing Master PostgreSQL connection...")
        if not await test_master_connection():
            logger.error("Failed to connect to Master PostgreSQL database. Please check your connection settings.")
            return False
        
        logger.info("Seeding master database with default users...")
        success = await seed_master_admin_user()
        
        if success:
            logger.info("Master database users seeded successfully")
            return True
        else:
            logger.error("Failed to seed master database users")
            return False
            
    except ImportError as e:
        logger.error(f"Failed to import PostgreSQL modules: {e}")
        logger.error("Make sure you have psycopg2 installed: pip install psycopg2-binary")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during user seeding: {e}")
        return False


def seed_master_users() -> bool:
    """Seed the master database with default users (sync wrapper)."""
    return asyncio.run(seed_master_users_async())


def main():
    """
    Main function to handle command line arguments and initialize PostgreSQL database.
    Optimized CLI interface with clear command structure.
    """
    parser = argparse.ArgumentParser(
        description="Database operations: initialize, migrate, or seed databases",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s init                    # Initialize both databases
  %(prog)s init --test-only        # Test connections only
  %(prog)s migrate --db master     # Run migrations for master DB only
  %(prog)s seed-users -v           # Seed users with verbose logging
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", required=True, help="Available commands")

    # Init command
    init_parser = subparsers.add_parser("init", help="Initialize database tables")
    init_parser.add_argument("--db", choices=["project", "master", "both"], default="both",
                           help="Which database to initialize")
    init_parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")
    init_parser.add_argument("--test-only", action="store_true", 
                           help="Only test database connections without initializing")

    # Migrate command
    migrate_parser = subparsers.add_parser("migrate", help="Run Alembic database migrations")
    migrate_parser.add_argument("--db", choices=["project", "master", "both"], default="both",
                               help="Which database to migrate")
    migrate_parser.add_argument("-m", "--message", default="auto", help="Migration message")
    migrate_parser.add_argument("--dry", action="store_true", 
                               help="Generate migrations only, don't apply")

    # Seed users command
    seed_parser = subparsers.add_parser("seed-users", help="Seed master database with default users")
    seed_parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if getattr(args, "verbose", False) else logging.INFO
    logging.basicConfig(
        level=log_level, 
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    logger.info(f"Starting database operation: {args.command}")

    try:
        if args.command == "init":
            if args.test_only:
                logger.info("Testing database connections only...")
                success = asyncio.run(_test_connections_only())
                sys.exit(0 if success else 1)
            else:
                success = init_postgresql()
                sys.exit(0 if success else 1)

        elif args.command == "migrate":
            _run_migrations(args.db, args.message, args.dry)
            logger.info("Migrations completed successfully")
            sys.exit(0)

        elif args.command == "seed-users":
            success = seed_master_users()
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True if args.verbose else False)
        sys.exit(1)


# ==== HELPER FUNCTIONS ====

async def _test_connections_only() -> bool:
    """Test database connections without initialization."""
    try:
        
        logger.info("Testing database connections...")
        
        # Test both connections concurrently
        project_test, master_test = await asyncio.gather(
            test_connection(),
            test_master_connection(),
            return_exceptions=True
        )
        
        project_ok = not isinstance(project_test, Exception) and project_test
        master_ok = not isinstance(master_test, Exception) and master_test
        
        logger.info(f"Project database: {'✓ Connected' if project_ok else '✗ Failed'}")
        logger.info(f"Master database: {'✓ Connected' if master_ok else '✗ Failed'}")
        
        return project_ok and master_ok
        
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return False


def _run_alembic(cmd: list, ini_file: str) -> None:
    """Run Alembic command with proper environment setup."""
    env = os.environ.copy()
    # Ensure Alembic sees sync database URLs
    if "PROJECT_DB_DATABASE_URL" in env:
        env["PROJECT_DB_DATABASE_URL"] = _sync_url(env["PROJECT_DB_DATABASE_URL"])
    if "MASTER_DB_DATABASE_URL" in env:
        env["MASTER_DB_DATABASE_URL"] = _sync_url(env["MASTER_DB_DATABASE_URL"])
    
    try:
        subprocess.check_call(["alembic", "-c", ini_file] + cmd, env=env)
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Alembic command failed: {' '.join(cmd)}") from e


def _run_migrations(db: str, message: str, dry: bool) -> None:
    """
    Run database migrations using Alembic.
    Optimized to handle both databases efficiently.
    """
    databases = []
    if db in ("project", "both"):
        databases.append(("project", "alembic_project.ini"))
    if db in ("master", "both"):
        databases.append(("master", "alembic_master.ini"))
    
    for db_name, ini_file in databases:
        logger.info(f"Processing {db_name} database migrations...")
        
        # Generate migration
        _run_alembic(["revision", "--autogenerate", "-m", message], ini_file)
        logger.info(f"Generated migration for {db_name} database")
        
        # Apply migration if not dry run
        if not dry:
            _run_alembic(["upgrade", "head"], ini_file)
            logger.info(f"Applied migration for {db_name} database")

if __name__ == "__main__":
    main() 