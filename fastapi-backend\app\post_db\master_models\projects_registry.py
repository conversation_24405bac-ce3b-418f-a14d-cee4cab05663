from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, <PERSON>olean, DateTime, Numeric, CheckConstraint, UniqueConstraint, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from ..base import Base


class ProjectsRegistry(Base):
    """
    Central registry of all annotation projects across the platform.
    Acts as the central directory for project discovery, access control, and high-level project management.
    """
    __tablename__ = 'projects_registry'

    # Primary Key & Identification
    id = Column(Integer, primary_key=True, autoincrement=True)
    project_code = Column(String(255), nullable=False, unique=True,
                         comment='Project code in format PROJ_{project_name}_{client_id} for unique identification')
    project_name = Column(String(255), nullable=False, 
                         comment='Descriptive project name displayed in UI and reports')
    project_type = Column(String(50), nullable=False, 
                         comment='Media type being annotated (image, pdf, video, audio, text, csv) - determines annotation interface')
    
    # Client Information & Ownership
    client_id = Column(Integer, ForeignKey('clients.id'), nullable=False,
                      comment='Client identifier as foreign key to clients table')
    
    # Database Connection & Isolation
    database_name = Column(String(100), nullable=False, unique=True, 
                          comment='Name of dedicated project database (e.g., "proj_client_001_db")')
    database_host = Column(String(255), default='localhost', 
                          comment='Database server hostname for distributed database deployment')
    database_port = Column(Integer, default=5432, 
                          comment='Database port for connection routing')
    database_connection_params = Column(JSONB, 
                                      comment='Additional connection parameters (SSL, timeout, pool size) as JSON')
    # Source Data Location
    folder_path = Column(String(500), nullable=True,
                        comment='Original folder path where the project data is stored (for media browsing)')
    credentials = Column(JSONB,
                       comment='Storage credentials specific to this project (NAS, MinIO, Google Drive)')
    connection_type = Column(String(50), nullable=True,
                           comment='Type of storage connection: NAS-FTP, MinIO, GoogleDrive')
    batch_size = Column(Integer, default=10, nullable=True,
                       comment='Number of files to include in each batch when creating allocations')
    ai_processing = Column(Boolean, default=False, 
                           comment='Whether this project uses AI processing for annotation')
    allocation_strategy_id = Column(Integer, ForeignKey('allocation_strategies.id'), nullable=True,
                                   comment='Reference to allocation strategy ID')
    # Project Configuration & Schema Definition
    annotation_requirements = Column(JSONB, 
                                   comment='Client-specific annotation requirements and business rules')
    instructions = Column(String, 
                         comment='Project-specific instructions for annotators and verifiers')
    # Project Status & Management
    project_status = Column(String(50), default='inactive', 
                          comment='Current operational status (inactive, active, paused, completed, archived)')
    priority_level = Column(Integer, default=1, 
                           comment='Project priority for resource allocation (1=highest, 10=lowest)')
    
    # Performance Statistics (cached from project database for dashboard)
    total_files = Column(Integer, default=0, 
                        comment='Total number of files in project (cached for quick dashboard display)')
    total_batches = Column(Integer, default=0, 
                          comment='Total number of annotation batches created')
    completed_files = Column(Integer, default=0, 
                            comment='Number of fully annotated and approved files')
    active_annotators = Column(Integer, default=0, 
                              comment='Current number of users actively working on this project')
    
    # Timeline & Deadlines
    project_deadline = Column(DateTime, 
                            comment='Client deadline for project completion (for scheduling and prioritization)')
    created_at = Column(DateTime, default=func.current_timestamp(), 
                       comment='Project creation timestamp for audit trail')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), 
                       comment='Last modification timestamp for change tracking')
    last_sync_at = Column(DateTime, 
                         comment='Last synchronization with project database for data consistency monitoring')

    # Constraints
    __table_args__ = (
        CheckConstraint(
            project_type.in_(['image', 'pdf', 'video', 'audio', 'text', 'csv']),
            name='valid_project_type'
        ),
        CheckConstraint(
            project_status.in_(['inactive', 'active', 'paused', 'completed', 'archived']),
            name='valid_project_status'
        ),
        CheckConstraint(
            'priority_level >= 1 AND priority_level <= 10',
            name='valid_priority_level'
        ),
        CheckConstraint(
            connection_type.in_(['NAS-FTP', 'MinIO', 'GoogleDrive']),
            name='valid_connection_type'
        )
    )

    # Relationships
    allocation_strategy = relationship("AllocationStrategies", back_populates="projects")
    client = relationship("Clients", foreign_keys=[client_id], backref="projects")
    
    def __repr__(self):
        return f"<ProjectsRegistry(id={self.id}, project_code='{self.project_code}', project_name='{self.project_name}')>" 