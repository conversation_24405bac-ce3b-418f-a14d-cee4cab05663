// Utility functions for project management

// Get status color classes based on project status
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'inactive':
      return 'bg-red-100 text-red-800';
    case 'paused':
      return 'bg-yellow-100 text-yellow-800';
    case 'completed':
      return 'bg-blue-100 text-blue-800';
    case 'archived':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Format date string to readable format
export const formatDate = (dateString?: string): string => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString();
};

// Get batch status color
export const getBatchStatusColor = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'verifying':
      return 'bg-green-100 text-green-800';
    case 'annotating':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Build URL search params from filters
export const buildSearchParams = (page: number, pageSize: number, filters: Record<string, any>): URLSearchParams => {
  const params = new URLSearchParams({
    page: page.toString(),
    page_size: pageSize.toString(),
  });

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value.toString());
    }
  });

  return params;
};

// Calculate progress percentage
export const calculateProgress = (completed: number, total: number): number => {
  return total > 0 ? (completed / total) * 100 : 0;
};

// Validate annotator assignment against strategy requirements
export const validateAnnotatorAssignment = (
  currentAssigned: number,
  newlySelected: number,
  requiredAnnotators: number
): { isValid: boolean; message?: string } => {
  const totalAnnotators = currentAssigned + newlySelected;
  
  if (totalAnnotators < requiredAnnotators) {
    const needed = requiredAnnotators - totalAnnotators;
    return {
      isValid: false,
      message: `This strategy requires at least ${requiredAnnotators} annotator${requiredAnnotators > 1 ? 's' : ''}. You currently have ${currentAssigned} assigned and are selecting ${newlySelected} more. Please select ${needed} more annotator${needed > 1 ? 's' : ''}.`
    };
  }
  
  return { isValid: true };
};

// Constants


// Default pagination settings
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_PAGE = 1;
