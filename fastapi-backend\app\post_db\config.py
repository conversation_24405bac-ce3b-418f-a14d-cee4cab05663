from pydantic_settings import BaseSettings
from pydantic import Field
from typing import Dict, Any
from core.config import UserConfig, UserRole


# ==========================================
# DATABASE CONFIGURATION SETTINGS
# ==========================================

class DatabaseSettings(BaseSettings):
    """Database configuration settings for PostgreSQL (async)"""
    url: str = Field(
        default="postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/project_db", 
        json_schema_extra={"env": "DATABASE_URL"}
    )
    echo: bool = Field(default=False)
    pool_size: int = Field(default=20)
    max_overflow: int = Field(default=30)
    pool_timeout: int = Field(default=30)
    pool_recycle: int = Field(default=1800) 


class MasterDatabaseSettings(BaseSettings):
    """Database configuration settings for Master PostgreSQL database (async)"""
    url: str = Field(
        default="postgresql+asyncpg://kanwar_raj:dadpdev123@10.10.10.30:5432/master_db",
        json_schema_extra={"env": "MASTER_DATABASE_URL"}
    )
    echo: bool = Field(default=False)
    pool_size: int = Field(default=50)
    max_overflow: int = Field(default=100)
    pool_timeout: int = Field(default=30)
    pool_recycle: int = Field(default=1800)
    
    
class MasterUserSettings(BaseSettings):
    """User configuration settings for master database"""
    users: Dict[str, dict] = Field(
        default={
            'admin': {
                'username': 'admin',
                'password': 'admin123',
                'role': 'admin',
                'full_name': 'Admin User',
                'email': '<EMAIL>',
                'is_active': True
            },
            'annotator1': {
                'username': 'annotator1',
                'password': '12345678',
                'role': 'annotator',
                'full_name': 'Annotator 1',
                'email': '<EMAIL>',
                'is_active': True
            },
            'annotator2': {
                'username': 'annotator2',
                'password': '12345678',
                'role': 'annotator',
                'full_name': 'Annotator 2',
                'email': '<EMAIL>',
                'is_active': True
            },
            'annotator3': {
                'username': 'annotator3',
                'password': '12345678',
                'role': 'annotator',
                'full_name': 'Annotator 3',
                'email': '<EMAIL>',
                'is_active': True
            },
            'auditor': {
                'username': 'auditor',
                'password': '12345678',
                'role': 'auditor',
                'full_name': 'Auditor',
                'email': '<EMAIL>',
                'is_active': True
            }
        }
    )


class AllocationDatabaseSettings(BaseSettings):
    """Database configuration settings for allocation system PostgreSQL (async)"""
    # Base allocation database URL pattern
    base_url_pattern: str = Field(
        default="postgresql+asyncpg://mansi:pass123@10.10.10.30:5432/project_db",
        json_schema_extra={"env": "ALLOCATION_DATABASE_URL_PATTERN"}
    )
    
    # Connection pool settings
    echo: bool = Field(default=False)
    pool_size: int = Field(default=15)
    max_overflow: int = Field(default=25)
    pool_timeout: int = Field(default=30)
    pool_recycle: int = Field(default=1800)
    
    def get_database_url(self, database_name: str) -> str:
        """Get the database URL for a specific project database."""
        return self.base_url_pattern.format(database_name=database_name)


# ==========================================
# USER CONFIGURATION SETTINGS
# ==========================================

class UserSettings(BaseSettings):
    """User configuration settings"""
    users: Dict[str, UserConfig] = Field(
        default={
            'admin': {
                'username': 'admin',
                'password': 'admin123',
                'role': UserRole.ADMIN,
                'full_name': 'Admin User',
                'email': '<EMAIL>',
                'is_active': True
            },
            'annotator1': {
                'username': 'annotator1',
                'password': '12345678',
                'role': UserRole.ANNOTATOR,
                'full_name': 'Annotator 1',
                'is_active': True
            },
            'annotator2': {
                'username': 'annotator2',
                'password': '12345678',
                'role': UserRole.ANNOTATOR,
                'full_name': 'Annotator 2',
                'is_active': True
            },
            'annotator3': {
                'username': 'annotator3',
                'password': '12345678',
                'role': UserRole.ANNOTATOR,
                'full_name': 'Annotator 3',
                'is_active': True
            },
            'auditor': {
                'username': 'auditor',
                'password': '12345678',
                'role': UserRole.AUDITOR,
                'full_name': 'Auditor',
                'is_active': True
            }
        }
    )


# ==========================================
# ALLOCATION SYSTEM CONFIGURATION SETTINGS
# ==========================================

class AllocationWorkflowSettings(BaseSettings):
    """Configuration settings for allocation workflows"""
    
    # Default allocation settings
    default_strategy_type: str = Field(default="single")
    default_annotation_mode: str = Field(default="human_only")
    default_max_concurrent_files: int = Field(default=5)
    default_max_files_per_day: int = Field(default=20)
    default_batch_size: int = Field(default=50)
    
    # Parallel workflow settings
    max_parallel_annotators: int = Field(default=5)
    min_consensus_score: float = Field(default=80.0)
    default_isolation_level: str = Field(default="none")
    
    # Audit settings
    max_audit_levels: int = Field(default=3)
    default_max_revision_rounds: int = Field(default=3)
    
    # Priority settings
    priority_levels: Dict[str, int] = Field(
        default={
            "critical": 1,
            "high": 2,
            "normal": 3,
            "low": 4
        }
    )
    
    # Batch processing settings
    max_concurrent_batches_per_user: int = Field(default=3)
    max_files_per_batch: int = Field(default=1000)
    batch_timeout_hours: int = Field(default=168)  # 1 week
    
    # AI integration settings
    ai_confidence_threshold: float = Field(default=0.8)
    human_verification_sampling_rate: float = Field(default=100.0)
    
    # File processing settings
    supported_file_types: Dict[str, list] = Field(
        default={
            "image": ["jpg", "jpeg", "png", "gif", "bmp", "tiff"],
            "video": ["mp4", "avi", "mov", "wmv", "flv"],
            "audio": ["mp3", "wav", "flac", "aac", "ogg"],
            "pdf": ["pdf"],
            "text": ["txt", "doc", "docx", "rtf"]
        }
    )
    
    # Quality control settings
    quality_score_thresholds: Dict[str, float] = Field(
        default={
            "excellent": 95.0,
            "good": 85.0,
            "acceptable": 75.0,
            "needs_improvement": 60.0
        }
    )


class AllocationNotificationSettings(BaseSettings):
    """Notification settings for allocation events"""
    
    # Email notifications
    enable_email_notifications: bool = Field(default=True)
    allocation_notification_template: str = Field(default="allocation_assigned")
    deadline_reminder_hours: int = Field(default=24)
    
    # Webhook notifications
    enable_webhook_notifications: bool = Field(default=False)
    webhook_url: str = Field(default="")
    webhook_events: list = Field(
        default=["allocation_created", "allocation_completed", "deadline_approaching"]
    )
    
    # Internal notifications
    enable_internal_notifications: bool = Field(default=True)
    notification_channels: list = Field(
        default=["dashboard", "email"]
    )


# ==========================================
# GLOBAL CONFIGURATION INSTANCES
# ==========================================

# Database configurations
db_settings = DatabaseSettings()
master_db_settings = MasterDatabaseSettings()
allocation_db_settings = AllocationDatabaseSettings()

# User configurations  
user_settings = UserSettings()

# Allocation system configurations
allocation_workflow_settings = AllocationWorkflowSettings()
allocation_notification_settings = AllocationNotificationSettings()